import React, { useState } from 'react';
import { useMindMap } from '../../../context/MindMapContext';
import './StartupDialog.css';

interface StartupDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const StartupDialog: React.FC<StartupDialogProps> = ({ isOpen, onClose }) => {
  const {
    projects,
    loadProject,
    createNewProject,
    setShowNodeDialog,
    setNodeDescription,
    setNodeText
  } = useMindMap();

  const [userInput, setUserInput] = useState('');
  const [showProjects, setShowProjects] = useState(false);

  if (!isOpen) return null;

  const handleNewProject = (e: React.FormEvent) => {
    e.preventDefault();
    if (!userInput.trim()) return;

    // Create new project with user input as the description
    createNewProject();
    setNodeDescription(userInput);
    setNodeText(''); // Clear the caption for user to enter
    setShowNodeDialog(true); // Open node dialog for editing
    onClose();
  };

  const handleLoadProject = (projectId: string) => {
    loadProject(projectId);
    onClose();
  };

  return (
    <div className="startup-dialog-overlay">
      <div className="startup-dialog-container">
        {!showProjects ? (
          <>
            <div className="startup-dialog-header">
              <h2>Welcome to MindBack.ai</h2>
            </div>
            <div className="startup-dialog-content">
              <div className="startup-options">
                <div className="new-project-section">
                  <h3>Start a New Project</h3>
                  <div className="governance-message">
                    <p>Hello! I'm your Governance Agent. I can help you organize your thoughts and ideas. What would you like to work on today?</p>
                  </div>
                  <form onSubmit={handleNewProject}>
                    <textarea
                      value={userInput}
                      onChange={(e) => setUserInput(e.target.value)}
                      placeholder="Describe your project or idea..."
                      rows={4}
                    />
                    <button type="submit" className="primary-button">
                      Create Project
                    </button>
                  </form>
                </div>
                
                <div className="project-divider">
                  <span>OR</span>
                </div>

                <div className="load-project-section">
                  <h3>Open Existing Project</h3>
                  <button 
                    className="secondary-button"
                    onClick={() => setShowProjects(true)}
                  >
                    Browse Projects
                  </button>
                </div>
              </div>
            </div>
          </>
        ) : (
          <>
            <div className="startup-dialog-header">
              <button 
                className="back-button"
                onClick={() => setShowProjects(false)}
              >
                ← Back
              </button>
              <h2>Your Projects</h2>
            </div>
            <div className="startup-dialog-content">
              <div className="projects-list">
                {projects.length === 0 ? (
                  <p className="no-projects">No saved projects found.</p>
                ) : (
                  projects
                    .sort((a, b) => new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime())
                    .map(project => (
                      <div
                        key={project.id}
                        className="project-item"
                        onClick={() => handleLoadProject(project.id)}
                      >
                        <div className="project-name">{project.name}</div>
                        <div className="project-date">
                          Last modified: {new Date(project.lastModified).toLocaleDateString()}
                        </div>
                      </div>
                    ))
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default StartupDialog; 