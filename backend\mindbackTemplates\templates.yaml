# MindBack Template Library
# This file defines all available templates in the MindBack system

templates:
  - id: response
    name: Simple Response
    description: "Provides direct factual answers without special visualization"
    use_cases:
      - "Simple factual queries"
      - "Specific information requests"
      - "Questions with definitive answers"
    output_format: "simple JSON with direct answer"
    template_file: "responseTemplate.yaml"

  - id: mindmap
    name: MindMap
    description: "Creates a hierarchical breakdown of concepts, goals, or tasks"
    use_cases:
      - "Strategic planning and goal setting"
      - "Project breakdown and task organization"
      - "Concept exploration and organization"
    output_format: "hierarchical JSON with nodes and connections"
    template_file: "mindmapTemplate.yaml"
    
  - id: chatfork
    name: ChatFork
    description: "Provides verbose explanations with interactive forking capabilities"
    use_cases:
      - "Educational content and explanations"
      - "Complex topic exploration"
      - "Interactive learning sessions"
    output_format: "structured JSON with sections and subsections"
    template_file: "chatForkTemplate.yaml"
    
  - id: swat
    name: SWAT Analysis
    description: "Performs Strengths, Weaknesses, Opportunities, and Threats analysis"
    use_cases:
      - "Business strategy evaluation"
      - "Project risk assessment"
      - "Competitive analysis"
    output_format: "quadrant-based JSON with categorized items"
    template_file: "SWATtemplate.yaml"
    
  - id: 5m
    name: 5M Analysis
    description: "Analyzes Man, Machine, Method, Material, and Measurement factors"
    use_cases:
      - "Manufacturing process optimization"
      - "Quality control analysis"
      - "Root cause investigation"
    output_format: "categorized JSON with 5 main sections"
    template_file: "5Mtemplate.yaml"

# Selection criteria for the intent classifier
selection_criteria:
  - "User intent and goals"
  - "Content structure requirements"
  - "Interaction model needed"
  - "Domain appropriateness"
  - "Complexity of response required" 