# Asset Path Utilities

## Overview

This directory contains utility functions for handling asset paths in a standardized way across the application. The primary challenge being addressed is ensuring consistent and correct paths for assets like images and other static files, both in development and production environments.

## Key Files

- `assetPaths.ts`: Contains utilities for standardizing asset paths and providing fallback mechanisms

## Asset Path Strategy

### The Problem

In a Vite-based application:
- Assets in the public directory are served at the root path in development
- Assets are copied to the root of the dist folder in production
- Direct references to assets using relative paths can be problematic when:
  - Components are located at different directory depths
  - The deployment environment has a different base URL
  - The development environment's asset serving behavior differs from production

### Our Solution

1. **Centralized Path Management**: The `getAssetPath` function ensures consistent path resolution
2. **Fallback Mechanism**: The `LogoAssets` object provides fallback options when a primary asset fails to load
3. **Reusable Components**: The `MindBackLogo` component provides a standardized way to include logos with proper error handling

## Usage Examples

### Basic Asset Path Resolution

```typescript
import { getAssetPath } from '../utils/assetPaths';

const imagePath = getAssetPath('images/example.jpg');
// Results in "/images/example.jpg"
```

### Using Logo Assets with Fallbacks

```typescript
import { LogoAssets } from '../utils/assetPaths';

function MyComponent() {
  const logoConfig = LogoAssets.getPrimaryLogo();
  
  return (
    <img 
      src={logoConfig.src} 
      alt="Logo" 
      onError={logoConfig.onError} 
    />
  );
}
```

### Using the MindBackLogo Component

```typescript
import MindBackLogo from '../components/common/MindBackLogo';

function Header() {
  return (
    <header>
      <MindBackLogo 
        size="medium"
        className="header-logo"
        onClick={() => console.log('Logo clicked')}
      />
      <h1>My Application</h1>
    </header>
  );
}
```

## Best Practices

1. **Always use the utility**: Never hardcode asset paths
2. **Provide fallbacks**: Especially for critical assets like logos
3. **Use relative paths**: When calling `getAssetPath`, use paths relative to the public directory
4. **Ensure assets exist**: Verify that referenced assets actually exist in the public directory 