# MindBack Content Protocol (MBCP) Specification V2.0

## Overview
The **MindBack Content Protocol (MBCP)** defines the standard data structure for nodes within the MindBack system. It ensures consistency across LLM outputs, agent workflows, RAG memory, and integration with external systems like HubSpot or Notion.

MBCP is designed to be:
- **Structured** for memory and action extraction
- **Semantic** to capture user intention and agent contribution
- **Composable** for rendering as mindmaps, chatforks, or tables
- **Translatable** to other protocols like MCP (Modular Content Protocol)
- **Verifiable** with traceable references for factual claims

---

## Node Schema
Each node in MBCP is a JSON object that may contain required and optional fields.

### Required Fields
| Field         | Type   | Description                              |
|---------------|--------|------------------------------------------|
| `id`          | string | Unique identifier for the node           |
| `text`        | string | Headline or label (≤50 characters)       |
| `description` | string | Extended explanation or reasoning        |

### Optional Fields
| Field         | Type      | Description                                                        |
|---------------|-----------|--------------------------------------------------------------------|
| `intent`      | enum      | One of: `factual`, `teleological`, `exploratory`                  |
| `agent`       | enum      | One of: `blue_hat`, `white_hat`, `red_hat`, `black_hat`, `yellow_hat`, `green_hat` |   
| `tags`        | array     | List of strings (for topic indexing and filtering)                |
| `parent_id`   | string    | ID of the parent node                                              |
| `created_by`  | enum      | One of: `user`, `llm`, `agent`                                     |
| `timestamp`   | ISO string| Node creation timestamp                                            |
| `embedding_ref` | string  | Reference to stored vector embedding                               |
| `children`    | array     | List of child nodes (recursive MBCP nodes)                        |
| `references`  | array     | List of reference objects (for factual claims)                    |
| `confidence`  | number    | Confidence score (0.0-1.0) for factual claims                     |
| `data_type`   | enum      | One of: `qualitative`, `quantitative`, `mixed`                    |

### Action Sub-object (Optional)
| Field       | Type   | Description                                     |
|-------------|--------|-------------------------------------------------|
| `title`     | string | Name of the action                             |
| `owner`     | string | Responsible party                              |
| `due_date`  | date   | Deadline in YYYY-MM-DD                         |
| `system`    | string | Target system (e.g., `hubspot`, `notion`)      |
| `status`    | enum   | One of: `pending`, `in_progress`, `done`       |

### Reference Sub-object (Optional)
| Field       | Type   | Description                                     |
|-------------|--------|-------------------------------------------------|
| `id`        | string | Unique identifier for the reference             |
| `url`       | string | Source URL                                      |
| `title`     | string | Title of the source                             |
| `publisher` | string | Publisher or domain name                        |
| `date`      | string | Publication date (YYYY-MM-DD)                   |
| `quote`     | string | Direct quote from the source (if applicable)    |
| `page`      | string | Page number or section (if applicable)          |
| `accessed`  | string | Date when the source was accessed               |
| `type`      | enum   | One of: `web`, `academic`, `report`, `news`, `data`, `other` |

---

## Example MBCP Node with References
```json
{
  "id": "node_1_1",
  "text": "French battery storage market size",
  "description": "The French battery storage market was valued at €1.5 billion in 2022 and is projected to grow at a CAGR of 30% to reach €3.8 billion by 2025.",
  "intent": "factual",
  "agent": "white_hat",
  "tags": ["market", "energy", "battery", "france", "statistics"],
  "parent_id": "node_1",
  "created_by": "llm",
  "timestamp": "2025-04-19T18:30:00Z",
  "data_type": "quantitative",
  "confidence": 0.85,
  "references": [
    {
      "id": "ref_1",
      "url": "https://www.researchandmarkets.com/reports/4983308/battery-energy-storage-system-market-in-france",
      "title": "Battery Energy Storage System Market in France - Industry Outlook and Forecast 2022-2027",
      "publisher": "Research and Markets",
      "date": "2022-03-15",
      "accessed": "2025-04-19",
      "type": "report"
    },
    {
      "id": "ref_2",
      "url": "https://www.mordorintelligence.com/industry-reports/battery-energy-storage-systems-market",
      "title": "Battery Energy Storage Systems Market - Growth, Trends, COVID-19 Impact, and Forecasts (2021-2026)",
      "publisher": "Mordor Intelligence",
      "date": "2021-09-01",
      "accessed": "2025-04-19",
      "type": "report"
    }
  ],
  "children": []
}
```

---

## MBCP to MCP Translation Map
For interoperability, MBCP can be translated into a simplified Modular Content Protocol (MCP) format.

| MBCP Field     | MCP Field          |
|----------------|--------------------|
| `id`           | `id`               |
| `text`         | `title` or `text`  |
| `description`  | `content`          |
| `intent`       | `type` or `tags`   |
| `agent`        | `agent` or `source`|
| `tags`         | `tags`             |
| `action`       | `action` (custom)  |
| `children`     | `children`         |
| `references`   | `sources` or `metadata` |

If MCP compliance is required, you can flatten or omit MBCP-specific fields while preserving semantics via tags or metadata.

---

## Markdown Rendering Format
For human-readable output, MBCP nodes can be rendered in Markdown format with the following structure:

### Factual Nodes
```markdown
## [Text]

[Description]

**Data Type:** [data_type]
**Confidence:** [confidence]

**References:**
1. [Title] ([Publisher], [Date]) - [URL]
2. [Title] ([Publisher], [Date]) - [URL]
```

### Action Nodes
```markdown
## [Text] 📋

[Description]

**Action:** [action.title]
**Owner:** [action.owner]
**Due:** [action.due_date]
**Status:** [action.status]
```

---

## Usage
- **LLMs** should be instructed to output nodes in MBCP format.
- **Frontends** should render based on `type`, `intent`, and `agent`.
- **Memory layers (RAG)** can embed and index nodes using `tags`, `intent`, `text`, and `embedding_ref`.
- **Export pipelines** can extract `action` sub-objects and sync them to external systems.
- **Verification systems** can validate factual claims against `references`.

---

## Versioning
This is **MBCP v2.0**, designed for MindBack's structured interaction workflows with enhanced support for factual references and data verification. Future versions may include:
- Support for link-type edges (non-hierarchical relationships)
- Traceability fields (e.g., `justified_by`, `contradicted_by`)
- Localization (e.g., `language`, `translation_of`)
- Enhanced multimedia support (e.g., `images`, `charts`, `audio`)

---

## Changes from V1.0
- Added `references` array for tracking sources of factual claims
- Added `confidence` score for indicating reliability of factual information
- Added `data_type` field to distinguish between qualitative and quantitative data
- Added Markdown rendering format guidelines
- Enhanced example with references

---

For questions, updates, or proposing changes to the MBCP spec, contact the system architecture team.
