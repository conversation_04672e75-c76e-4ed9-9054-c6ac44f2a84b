/**
 * Test script for manual mindmap creation
 * This can be run to debug and verify the manual method functionality
 */

import { createMindMapFromJsonManual, buildMindMapTreeDirectly } from './ManualJsonProcessor';

/**
 * Creates a mock store implementation for testing
 */
const createMockStore = () => {
  // Track created nodes and connections
  const nodes: Record<string, any> = {};
  let nodeIdCounter = 0;
  const connections: any[] = [];
  
  // Create mock store
  return {
    // Store data
    nodes,
    connections,
    
    // Method to add a node
    addNode: (parentId: string, direction: number) => {
      const nodeId = `test-node-${nodeIdCounter++}`;
      nodes[nodeId] = {
        id: nodeId,
        title: 'New Node',
        content: '',
        x: 0,
        y: 0,
        metadata: {}
      };
      console.log(`[MockStore] Added node ${nodeId} with parent ${parentId}`);
      return nodeId;
    },
    
    // Method to update a node
    updateNode: (id: string, updates: any) => {
      if (!nodes[id]) {
        console.error(`[MockStore] Cannot update node ${id} - not found`);
        return;
      }
      nodes[id] = { ...nodes[id], ...updates };
      console.log(`[MockStore] Updated node ${id}:`, updates);
    },
    
    // Method to add a connection
    addConnection: (input: any) => {
      const { source, target } = input;
      connections.push({ source, target, id: `conn-${source}-${target}` });
      console.log(`[MockStore] Added connection from ${source} to ${target}`);
    },
    
    // Method for auto layout - just a mock implementation
    autoLayout: () => {
      console.log(`[MockStore] Running auto layout on ${Object.keys(nodes).length} nodes`);
    },
    
    // For debugging
    printState: () => {
      console.log('=========== MOCK STORE STATE ===========');
      console.log(`Nodes (${Object.keys(nodes).length}):`);
      Object.values(nodes).forEach(node => {
        console.log(`  - ${node.id}: "${node.title}" (${node.metadata?.nodePath || 'no path'})`);
      });
      
      console.log(`Connections (${connections.length}):`);
      connections.forEach(conn => {
        const sourceNode = nodes[conn.source];
        const targetNode = nodes[conn.target];
        console.log(`  - ${sourceNode?.title || conn.source} → ${targetNode?.title || conn.target}`);
      });
    }
  };
};

/**
 * Simple test data with different structures
 */
const testData = [
  // Test 1: Simple hierarchy
  {
    title: "Simple hierarchy",
    content: "A basic test with root and children",
    children: [
      { title: "Child 1", content: "First child node" },
      { title: "Child 2", content: "Second child node" }
    ]
  },
  
  // Test 2: Two-level hierarchy
  {
    title: "Two-level hierarchy",
    content: "Test with grandchildren",
    children: [
      { 
        title: "Child 1", 
        content: "First child node",
        children: [
          { title: "Grandchild 1.1", content: "First grandchild" },
          { title: "Grandchild 1.2", content: "Second grandchild" }
        ]
      },
      { title: "Child 2", content: "Second child node" }
    ]
  },
  
  // Test 3: Backend format structure
  {
    mindmap: {
      root: {
        text: "Backend format",
        description: "Test of backend format structure",
        children: [
          { 
            text: "Backend Child 1",
            description: "First backend child", 
            children: [
              { text: "Backend Grandchild", description: "A grandchild node" }
            ]
          },
          { text: "Backend Child 2", description: "Second backend child" }
        ]
      }
    }
  }
];

/**
 * Run tests for both methods
 */
export const runManualMethodTests = () => {
  console.log('=========== RUNNING MANUAL METHOD TESTS ===========');
  
  testData.forEach((data, index) => {
    console.log(`\n\n===== TEST ${index + 1}: ${data.title || 'Unnamed test'} =====\n`);
    
    // Test createMindMapFromJsonManual
    console.log('Testing createMindMapFromJsonManual:');
    const store1 = createMockStore();
    const rootId1 = createMindMapFromJsonManual(data, store1);
    console.log(`Root node created: ${rootId1}`);
    store1.printState();
    
    // Test buildMindMapTreeDirectly
    console.log('\nTesting buildMindMapTreeDirectly:');
    const store2 = createMockStore();
    const rootId2 = buildMindMapTreeDirectly(store2, data);
    console.log(`Root node created: ${rootId2}`);
    store2.printState();
  });
  
  console.log('\n=========== MANUAL METHOD TESTS COMPLETE ===========');
};

// For manual execution in browser console
(window as any).testManualMethod = runManualMethodTests; 