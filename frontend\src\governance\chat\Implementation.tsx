import React, { useState, useRef, useEffect, MutableRefObject } from 'react';
import { Rnd } from 'react-rnd';
import './styles.css';
import { IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

import { DialogHeader } from './components/DialogHeader';
import MessageList from './MessageList';
import MessageInput from './components/MessageInput';
import { useChat } from './hooks/useChat';
import { ModelSelector } from './components/ModelSelector';
import useChatStore from './state/ChatStore';
import { v4 as uuidv4 } from 'uuid';
import { useContextStateMachine } from './hooks/useContextStateMachine';
import { ContextState } from './state/ContextStateMachine';

// Custom types for our new selection box
type ContextOption = {
  value: string;
  label: string;
};

// Constants for dialog positioning and sizing - centered by default
const DEFAULT_POSITION = {
  x: Math.max(100, window.innerWidth / 2 - 450), // Adjusted for wider box (900/2 = 450)
  y: Math.max(50, window.innerHeight / 2 - 300)
};
const DEFAULT_SIZE = { width: 900, height: 600 };
const COLLAPSED_SIZE = { width: 900, height: 48 }; // Just enough for the header
const MIN_SIZE = { width: 600, height: 400 };

// Main implementation of the GovernanceChatDialog - completely rewritten to avoid Dialog component
const Implementation: React.FC<{
  onClose: () => void;
  isOpen: boolean;
  isCollapsed?: boolean;
  onCollapse?: () => void;
  handleDrag?: boolean;
  mindMapState?: any;
  onAction?: (action: any) => void;
  isContextPanelOpen?: boolean; // New prop to indicate if context panel is open
}> = ({
  onClose,
  isOpen,
  isCollapsed = false,
  onCollapse,
  handleDrag = true,
  mindMapState,
  onAction,
  isContextPanelOpen = false // Default to false
}) => {
  // Debug log for context panel state
  console.log('Implementation - isContextPanelOpen:', isContextPanelOpen);

  // Chat functionality hooks
  const [isLiveLLMEnabled, setIsLiveLLMEnabled] = useState(true);
  const [selectedModel, setSelectedModel] = useState('gpt-4o-mini');

  // Use our new context state machine hook
  const {
    currentState,
    templateValue,
    isTransitioning,
    isCircuitOpen,
    error,
    selectContext
  } = useContextStateMachine();

  // Map the state machine state to the UI context
  const [selectedContext, setSelectedContext] = useState('agentic');

  // Update the UI context when the state machine state changes
  useEffect(() => {
    let newContext = 'agentic'; // Default

    switch (currentState) {
      case ContextState.TELEOLOGICAL:
        newContext = 'teleological';
        break;
      case ContextState.MINDMAP:
        newContext = 'mindmap';
        break;
      case ContextState.CHATFORK:
        newContext = 'chatfork';
        break;
      case ContextState.INSTANTANIOUS:
        newContext = 'instantanious';
        break;
      case ContextState.TEMPLATE:
        newContext = templateValue || 'agentic';
        break;
      case ContextState.ERROR:
        // Keep the current context
        return;
      default:
        // For IDLE and TRANSITIONING, don't change the UI
        return;
    }

    // Only update if different to avoid loops
    if (newContext !== selectedContext) {
      setSelectedContext(newContext);
    }
  }, [currentState, templateValue, selectedContext]);

  // Show error messages from the state machine
  useEffect(() => {
    if (error) {
      console.error('Context state machine error:', error);

      // Add an error message to the chat
      const errorMessage = {
        id: Date.now().toString(),
        text: `Error: ${error.message || 'Unknown error'}`,
        sender: 'system',
        timestamp: new Date()
      };
      useChatStore.getState().addMessage(errorMessage);
    }
  }, [error]);

  // Get showLogging from the store
  const showLogging = useChatStore(state => state.showLogging);
  const [useCrewAI, setUseCrewAI] = useState(false);
  const [crewSettings, setCrewSettings] = useState({
    maxIterations: 3,
    temperature: 0.7,
    verbose: true
  });

  // Use the chat hook directly - the useChat hook maintains its own state
  const {
    messages,
    isSubmitting,
    handleMessageSubmit,
    handleAction: chatHandleAction,
    handleBuildMindmap
  } = useChat({
    selectedModel,
    useLiveLLM: isLiveLLMEnabled
  }, onAction);

  // State management
  const [inputText, setInputText] = useState('');

  // Log input text changes
  useEffect(() => {
    console.log('Input text changed:', inputText);
  }, [inputText]);
  const [position, setPosition] = useState(DEFAULT_POSITION);
  const [size, setSize] = useState(DEFAULT_SIZE);

  // Refs for tracking the dialog
  const dialogRef = useRef<HTMLDivElement>(null);

  // Handle external control of collapsed state
  useEffect(() => {
    console.log('Implementation - isCollapsed changed:', isCollapsed);
  }, [isCollapsed]);

  // Track if the governance box has been moved to the right side
  const [hasMovedToRight, setHasMovedToRight] = useState(false);

  // Calculate the right position consistently
  const calculateRightPosition = () => {
    return window.innerWidth - size.width - 40;
  };

  // Set position to center of screen on first open
  useEffect(() => {
    // Only set position when first opened, not on subsequent opens
    if (isOpen && position.x === 0 && position.y === 0) {
      console.log('Setting initial position to center of screen');
      // Calculate center position
      const centeredX = Math.max(100, window.innerWidth / 2 - size.width / 2);
      const centeredY = Math.max(50, window.innerHeight / 2 - size.height / 2);
      setPosition({ x: centeredX, y: centeredY });
    }
  }, [isOpen, position.x, position.y, size.width, size.height]);

  // Log context panel state changes but don't automatically reposition
  useEffect(() => {
    console.log('Context panel state changed:', isContextPanelOpen);
    // No automatic repositioning
  }, [isContextPanelOpen]);

  // Handle window resizing
  useEffect(() => {
    // Handle window resize to keep the governance box visible
    const handleResize = () => {
      // Only adjust position if the box would go off-screen
      const rightEdge = position.x + size.width;
      const bottomEdge = position.y + size.height;
      let newX = position.x;
      let newY = position.y;
      let needsAdjustment = false;

      if (rightEdge > window.innerWidth) {
        newX = Math.max(0, window.innerWidth - size.width - 20);
        needsAdjustment = true;
      }

      if (bottomEdge > window.innerHeight) {
        newY = Math.max(0, window.innerHeight - size.height - 20);
        needsAdjustment = true;
      }

      if (needsAdjustment) {
        console.log('Window resized, adjusting position to stay on screen:', newX, newY);
        setPosition({ x: newX, y: newY });
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [size.width, size.height, position.x, position.y]);

  // Handle reset size
  const handleResetSize = () => {
    setSize(DEFAULT_SIZE);
  };

  // Handle reset position - center in the viewport
  const handleResetPosition = () => {
    // Center the box in the viewport
    const centeredX = Math.max(100, window.innerWidth / 2 - size.width / 2);
    const centeredY = Math.max(50, window.innerHeight / 2 - size.height / 2);
    setPosition({ x: centeredX, y: centeredY });
    // Reset the hasMovedToRight flag
    setHasMovedToRight(false);
  };

  // Handle minimize (collapse/expand)
  const handleMinimize = () => {
    if (onCollapse) {
      // Use the provided collapse handler
      onCollapse();
    }
  };

  // Handle toggle live LLM
  const handleLiveLLMToggle = () => {
    setIsLiveLLMEnabled(!isLiveLLMEnabled);
  };

  // Handle model change
  const handleModelChange = (model: string) => {
    console.log('Model changed to:', model);
    setSelectedModel(model);
  };

  // Handle context change - this is now a simple wrapper around the state machine
  const handleContextChange = (context: string) => {
    console.log('Context change requested to:', context);

    // Use the state machine to handle the context change
    selectContext(context);
  };

  // We're using the handleBuildMindmap function from useChat.ts
  // No need for a separate implementation here

  // Instantanious template options (for checking if a context is a sub-template)
  const instantaniousTemplates = [
    { value: 'bmc', label: 'Business Model Canvas' },
    { value: 'swot', label: 'SWOT Analysis' },
    { value: 'triz', label: 'TRIZ Method' },
    { value: 'five_forces', label: 'Five Forces (Porter\'s Model)' },
    { value: 'pestel', label: 'PESTEL Analysis' },
    { value: 'ansoff', label: 'Ansoff Matrix' },
    { value: 'value_prop', label: 'Value Proposition Canvas' },
    { value: 'jtbd', label: 'Jobs to be Done (JTBD)' },
    { value: 'business_case', label: 'Business Case Template' },
    { value: 'raci', label: 'RACI Matrix' },
    { value: 'moscow', label: 'MoSCoW Prioritization' },
    { value: 'okr', label: 'OKRs (Objectives and Key Results)' },
    { value: 'persona', label: 'User Persona Canvas' }
  ];

  // Handle logging toggle using the store
  const handleLoggingToggle = () => {
    console.log('Implementation - handleLoggingToggle called, current state:', showLogging);
    // Use the store's toggleLogging function
    useChatStore.getState().toggleLogging();
  };

  // Add effect to log state changes
  useEffect(() => {
    console.log('Implementation - showLogging state changed to:', showLogging);
  }, [showLogging]);

  // Handle CrewAI toggle (keeping for backward compatibility)
  const handleCrewAIToggle = () => {
    setUseCrewAI(!useCrewAI);
  };

  // Handle CrewAI settings change (keeping for backward compatibility)
  const handleCrewSettingsChange = (settings: any) => {
    setCrewSettings(settings);
  };

  // If dialog is not open, don't render anything
  if (!isOpen) return null;

  // Handle action clicks by passing them up to parent
  const handleActionClick = (action) => {
    console.log('Implementation - Action clicked:', action);

    if (onAction) {
      // Check if we're in mindmap mode and this is a user message
      if (selectedContext === 'mindmap' && action.type === 'user_message') {
        console.log('Implementation - User message in mindmap mode:', action.data);

        // Get the active sheet ID
        const mindBookStore = useMindBookStore.getState();
        const activeSheetId = mindBookStore.activeSheetId;

        if (!activeSheetId) {
          console.error('No active sheet found');
          return;
        }

        // Get the sheet-specific store
        const sheetStore = getMindMapStore(activeSheetId);
        const storeState = sheetStore.getState();

        // Get the root node ID
        const rootNodeId = storeState.rootNodeId;

        if (!rootNodeId) {
          console.error('No root node found');
          return;
        }

        // Find all nodes in the mindmap
        const nodes = storeState.nodes;
        const nodeIds = Object.keys(nodes);

        // Find the main topic node (the first child of the root node)
        let mainTopicNodeId = null;
        for (const nodeId of nodeIds) {
          const node = nodes[nodeId];
          if (node.parentId === rootNodeId) {
            mainTopicNodeId = nodeId;
            break;
          }
        }

        if (mainTopicNodeId) {
          // Update the main topic node with the user's topic
          // Get the current node to ensure we have the latest data
          const currentNode = storeState.nodes[mainTopicNodeId];

          // Get the original text with any prefix
          const originalText = currentNode?.text || '';
          // Extract any existing prefix
          const prefixMatch = originalText.match(/^(\d+(\.\d+)*\.?\s+)/);
          const prefix = prefixMatch ? prefixMatch[1] : '1. ';

          storeState.updateNode(mainTopicNodeId, {
            text: prefix + action.data.text,
            title: action.data.text,
            description: action.data.text
          });

          // Also update the root node
          // Get the current node to ensure we have the latest data
          const rootNode = storeState.nodes[rootNodeId];

          // Get the original text with any prefix
          const rootOriginalText = rootNode?.text || '';
          // Extract any existing prefix
          const rootPrefixMatch = rootOriginalText.match(/^(\d+(\.\d+)*\.?\s+)/);
          const rootPrefix = rootPrefixMatch ? rootPrefixMatch[1] : '';

          storeState.updateNode(rootNodeId, {
            text: rootPrefix + action.data.text,
            title: action.data.text
          });

          // Add a system message indicating the node was updated
          const systemMessage = {
            id: Date.now().toString(),
            text: `Mindmap node updated with topic: "${action.data.text}"`,
            sender: 'system',
            timestamp: new Date()
          };

          // Add the system message to the chat
          useChatStore.getState().addMessage(systemMessage);

          // Add a message from the assistant acknowledging the topic
          const assistantMessage = {
            id: (Date.now() + 100).toString(),
            text: `Great! I've created a mindmap with the topic "${action.data.text}". Please describe this topic in more detail, and I'll suggest nodes to add to your mindmap.`,
            sender: 'assistant',
            timestamp: new Date()
          };

          // Add the message to the chat
          useChatStore.getState().addMessage(assistantMessage);
        } else {
          // If we couldn't find the main topic node, just update the root node
          // Get the current node to ensure we have the latest data
          const rootNode = mindMapStore.nodes[rootNodeId];

          // Get the original text with any prefix
          const rootOriginalText = rootNode?.text || '';
          // Extract any existing prefix
          const rootPrefixMatch = rootOriginalText.match(/^(\d+(\.\d+)*\.?\s+)/);
          const rootPrefix = rootPrefixMatch ? rootPrefixMatch[1] : '';

          mindMapStore.updateNode(rootNodeId, {
            text: rootPrefix + action.data.text,
            title: action.data.text
          });

          // Add a system message indicating the node was updated
          const systemMessage = {
            id: Date.now().toString(),
            text: `Mindmap node updated with topic: "${action.data.text}"`,
            sender: 'system',
            timestamp: new Date()
          };

          // Add the system message to the chat
          useChatStore.getState().addMessage(systemMessage);

          // Add a message from the assistant acknowledging the topic
          const assistantMessage = {
            id: (Date.now() + 100).toString(),
            text: `Great! I've created a mindmap with the topic "${action.data.text}". Please describe this topic in more detail, and I'll suggest nodes to add to your mindmap.`,
            sender: 'assistant',
            timestamp: new Date()
          };

          // Add the message to the chat
          useChatStore.getState().addMessage(assistantMessage);
        }

        return;
      }

      if (action.type === 'show_chatfork') {
        // Get the latest message from the messages array
        const latestMessage = messages.length > 0 ? messages[messages.length - 1] : null;
        console.log('Implementation - Latest message:', latestMessage);

        // Log intent detection
        if (latestMessage?.responseType?.type) {
          console.log('⭐ Implementation - Intent detected:', latestMessage.responseType.type);
        }

        // Ensure response data is included in the action without hardcoding intent
        if (latestMessage && latestMessage.responseType) {
          console.log('Implementation - Processing message with responseType:', latestMessage.responseType);

          // Extract the key content data
          const description = latestMessage.description || latestMessage.text;
          const text = latestMessage.text || 'Topic';
          const responseType = latestMessage.responseType;
          const intent = responseType.type;

          // Create a properly structured action with all needed data
          // without hardcoding specific intent handling
          action.data = {
            text,
            description,
            responseType: responseType,
            intent: intent,
            title: text,
            // Include full_text if available or use description as fallback
            full_text: latestMessage.full_text || description,
            // Include root_topic if available or use text as fallback
            root_topic: latestMessage.root_topic || text,
            // Include any UI labels if available
            ui_labels: latestMessage.ui_labels || {
              chatfork_button: `Explore ${text}`,
              chatfork_tooltip: `Click to explore ${text}`
            },
            // Include content from templateOutput or create a basic structure
            content: latestMessage.templateOutput || {
              text,
              description,
              intent: intent,
              full_text: latestMessage.full_text || description
            }
            // Description is already included above
          };

          console.log('Implementation - Enhanced action with response data:', action);
        } else {
          console.warn('Implementation - No responseType found in latest message');
        }
      }

      // Call the parent handler with the enhanced action
      console.log('Implementation - Calling parent action handler with:', action);
      onAction(action);
    }
  };

  return (
    <>
      {/* Backdrop div - only show when expanded */}
      {!isCollapsed && (
        <div
          className="governance-dialog-backdrop"
          /* Removed onClick handler to prevent closing when clicking outside */
        />
      )}

      {/* Custom dialog container without Material-UI Dialog */}
      <Rnd
        default={{
          ...DEFAULT_POSITION,
          ...(isCollapsed ? COLLAPSED_SIZE : DEFAULT_SIZE)
        }}
        className={`governance-chat-dialog-container ${isCollapsed ? 'collapsed' : ''}`}
        size={{
          width: isCollapsed ? COLLAPSED_SIZE.width : size.width,
          height: isCollapsed ? COLLAPSED_SIZE.height : size.height
        }}
        position={{ x: position.x, y: position.y }}
        minWidth={MIN_SIZE.width}
        minHeight={isCollapsed ? COLLAPSED_SIZE.height : MIN_SIZE.height}
        bounds="window"
        dragHandleClassName="dialog-header"
        cancel=".dialog-header-buttons"
        enableResizing={{
          bottom: !isCollapsed,
          bottomRight: !isCollapsed,
          bottomLeft: false,
          right: !isCollapsed,
          left: false,
          top: false,
          topRight: false,
          topLeft: false
        }}
        resizeHandleClasses={{
          bottomRight: "rnd-resize-handle bottom-right",
          bottom: "rnd-resize-handle bottom",
          right: "rnd-resize-handle right"
        }}
        onDragStop={(e, d) => {
          setPosition({ x: d.x, y: d.y });
        }}
        onResizeStop={(e, direction, ref, delta, position) => {
          setSize({
            width: parseInt(ref.style.width),
            height: parseInt(ref.style.height)
          });
          setPosition(position);
        }}
      >
        <div ref={dialogRef} className="governance-dialog">
          {/* Dialog header with all original buttons */}
          <DialogHeader
            onClose={onClose}
            onResetSize={handleResetSize}
            onResetPosition={handleResetPosition}
            onMinimize={handleMinimize}
            title="Governance Agent"
            isCollapsed={isCollapsed}
          />

          {!isCollapsed && (
            <div className="dialog-content">
              {/* Main message area */}
              <MessageList
                messages={messages}
                onAction={handleActionClick}
                onBuildMindmap={handleBuildMindmap}
                showBuildMindmapButton={
                  // Only show the button if we're in mindmap context OR
                  // if there's a teleological message that doesn't already have a mindmap
                  selectedContext === 'mindmap' ||
                  (messages.some(msg =>
                    msg.sender === 'assistant' &&
                    msg.responseType?.type === 'teleological' &&
                    !msg.mindmapCreated // Don't show if a mindmap was already created
                  ))
                }
                showLogging={showLogging}
              />

              {/* Model selector between message list and input */}
              <div className="model-selector-row">
                <ModelSelector
                  selectedModel={selectedModel}
                  onModelChange={handleModelChange}
                  useLiveLLM={isLiveLLMEnabled}
                  onLiveLLMToggle={handleLiveLLMToggle}
                  showLogging={showLogging}
                  onLoggingToggle={handleLoggingToggle}
                  selectedContext={selectedContext}
                  onContextChange={handleContextChange}
                  isTransitioning={isTransitioning}
                  isCircuitOpen={isCircuitOpen}
                />
              </div>

              {/* Message input */}
              <MessageInput
                placeholder="Type your message..."
                value={inputText}
                onChange={setInputText}
                onSubmit={() => {
                  if (inputText.trim()) {
                    // If we're in mindmap mode, handle the message differently
                    if (selectedContext === 'mindmap') {
                      // Create a user message
                      const userMessage = {
                        id: Date.now().toString(),
                        text: inputText,
                        sender: 'user',
                        timestamp: new Date()
                      };

                      // Add the user message to the chat
                      useChatStore.getState().addMessage(userMessage);

                      // Create a user_message action
                      const action = {
                        type: 'user_message',
                        data: {
                          text: inputText
                        }
                      };

                      // Handle the action
                      handleActionClick(action);
                    } else {
                      // Normal message handling
                      handleMessageSubmit(inputText);
                    }
                    setInputText('');
                  }
                }}
                disabled={isSubmitting}
              />
            </div>
          )}
        </div>
      </Rnd>
    </>
  );
};

export { Implementation };