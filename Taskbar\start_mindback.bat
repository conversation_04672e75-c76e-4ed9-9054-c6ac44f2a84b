@echo off
echo Starting MindBack...
echo.

REM Change to the project root directory
cd /d "C:\Users\<USER>\Documents\VSCode\MindBack_Backup\MindBack_V1"

echo Stopping any existing servers...
REM Kill existing uvicorn processes (backend)
taskkill /f /im uvicorn.exe >nul 2>&1
REM Kill existing node processes (frontend)
taskkill /f /im node.exe >nul 2>&1
REM Kill any python processes that might be running the backend
taskkill /f /im python.exe >nul 2>&1

echo Cleaned up existing processes.
echo.

echo Starting MindBack servers...
REM Run the PowerShell setup script
powershell -ExecutionPolicy Bypass -File "run_setup.ps1"

pause 