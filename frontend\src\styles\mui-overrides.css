/* Material-UI overrides for z-index issues */

/* Make sure MUI Select dropdowns appear above dialogs */
.MuiPopover-root {
  z-index: var(--z-index-popovers) !important; /* Using CSS variable for z-index */
}

/* Make sure MUI Select dropdowns appear above everything else */
.MuiMenu-root {
  z-index: var(--z-index-popovers) !important; /* Using CSS variable for z-index */
}

/* Ensure Select components in dialogs work properly */
.MuiDialog-root .MuiPopover-root,
.MuiDialog-root .MuiMenu-root {
  z-index: var(--z-index-popovers) !important; /* Using CSS variable for z-index */
}

/* Specifically target the model selector dropdown */
#model-select-menu,
#node-select-menu {
  z-index: var(--z-index-popovers) !important; /* Using CSS variable for z-index */
}