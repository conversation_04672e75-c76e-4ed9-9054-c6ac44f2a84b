# MindBack Architecture Overview (20250612)

This document provides a high‑level summary of the MindBack system for coding agents. It describes the main components, data flow and technologies used in the project.

## 1. Core Components

- **React Frontend** – Vite + React + TypeScript application located in `frontend/`.
- **FastAPI Backend** – Python API server in `backend/` exposing REST and WebSocket endpoints.
- **LLM Integration** – Backend `llm` router integrates multiple LLM providers using the MindBack Content Protocol (MBCP).
- **MindBook Architecture** – Session persistence system storing multiple MindSheets (mind maps, chat forks, etc.).
- **Governance Box & Agents** – UI dialogue and specialized agents (Six Thinking Hats, CrewAI) for AI‑assisted brainstorming and decision making.

## 2. Directory Structure

```
frontend/        # React UI
backend/         # FastAPI server and AI services
docs/            # General documentation
architecture/    # Architecture reports and dependency graphs
```

Important subdirectories:
- `frontend/src/components/MindMap` – mind map rendering and interaction logic.
- `frontend/src/governance` – governance dialog, agent templates and RAG integration.
- `backend/api/routes/llm.py` – routes that handle LLM requests and intent routing.
- `backend/app/routers/mindmap.py` – basic project save/load API.

## 3. Data Flow

1. User interacts with the Governance Box or mind map in the frontend.
2. Frontend sends API requests to the FastAPI backend.
3. Backend calls the appropriate LLM service and processes the response.
4. Results (e.g., mind map nodes) are returned to the frontend and rendered.

## 4. MindBook Session Management

The MindBook architecture unifies session persistence across the app. A MindBook is similar to an Excel workbook and contains multiple MindSheets. The `MindBookPersistenceService` saves and restores complete sessions, with auto‑save and named MindBook storage.

## 5. Technologies

- **Frontend:** React, Vite, TypeScript, Zustand for state management, Konva for canvas rendering.
- **Backend:** FastAPI, SQLAlchemy/Alembic, OpenAI client, CrewAI, Celery for background tasks.
- **Development Tools:** ESLint, Prettier, Dependency Cruiser, Vitest/Pytest.

## 6. Suggested Improvements

- **State Immutability and Selectors** – enforce immutable updates in stores and expose selector helpers for efficiency.
- **Error Handling** – add error boundaries in React components and centralized service‑level error logging.
- **Dependency Injection** – decouple services in the backend and frontend to improve testability.
- **Performance Optimization** – memoize React components and virtualize large node lists.
- **Testing Strategy** – increase unit and integration test coverage, mock external services.
- **Internationalization and Accessibility** – integrate i18n support and implement accessibility attributes in UI components.

These improvements draw from `docs/codebase-improvement-plan.md` and `docs/architectural_document_additions.md` and aim to make the architecture more maintainable and robust.

## 7. Review Highlights

The following points summarize architecture strengths and open questions raised during the review:

- **Modular Design:** Clear separation between the React frontend and FastAPI backend helps coding agents work independently on each side.
- **Session Persistence:** The MindBook architecture is central for saving and restoring workspaces. Agents should interact with `MindBookPersistenceService` rather than localStorage directly.
- **LLM Routing:** All LLM calls flow through `backend/api/routes/llm.py`. Future work may introduce rate limiting and improved logging for these requests.
- **Testing Gaps:** Both frontend and backend would benefit from additional unit tests. Agents should reference the test strategy outlined in `docs/codebase-improvement-plan.md`.
- **Accessibility:** UI components currently lack comprehensive accessibility attributes. Review `docs/architectural_document_additions.md` for examples of compliant patterns.


