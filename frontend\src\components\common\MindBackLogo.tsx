import React from 'react';
import { LogoAssets } from '../../utils/assetPaths';

interface MindBackLogoProps {
  size?: 'small' | 'medium' | 'large';
  className?: string;
  onClick?: () => void;
  title?: string;
  style?: React.CSSProperties;
}

/**
 * A reusable logo component with built-in fallback handling
 */
const MindBackLogo: React.FC<MindBackLogoProps> = ({ 
  size = 'medium', 
  className = '', 
  onClick,
  title = 'MindBack Logo',
  style = {}
}) => {
  // Get the logo configuration with fallback handling
  const logoConfig = LogoAssets.getPrimaryLogo();
  
  // Determine dimensions based on size
  const dimensions = {
    small: { width: 24, height: 24 },
    medium: { width: 32, height: 32 },
    large: { width: 48, height: 48 }
  }[size];
  
  return (
    <img
      src={logoConfig.src}
      alt="MindBack Logo"
      className={className}
      onClick={onClick}
      onError={logoConfig.onError}
      title={title}
      style={{
        ...dimensions,
        ...style,
        cursor: onClick ? 'pointer' : 'default'
      }}
    />
  );
};

export default MindBackLogo; 