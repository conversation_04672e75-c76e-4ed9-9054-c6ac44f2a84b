import React, { useRef, useEffect } from 'react';
import Button from '@mui/material/Button';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import { Typography } from '@mui/material';
import ExploreIcon from '@mui/icons-material/Explore';
import './styles/logging.css';
import useChatStore from './state/ChatStore';
import { useMindBookStore } from '../../../core/state/MindBookStore';
import { initializeMindMap } from '../../../core/adapters/MindMapAdapter';
import { v4 as uuidv4 } from 'uuid';
import RegistrationManager, { EventType } from '../../../core/services/RegistrationManager';

interface MessageListProps {
  messages: any[];
  onAction?: (action: any) => void;
  onBuildMindmap?: () => void;
  showBuildMindmapButton?: boolean;
  showLogging?: boolean;
}

// Format timestamp to display date and time
const formatTimestamp = (timestamp: Date): string => {
  if (!timestamp) return '';
  return timestamp.toLocaleString();
};

// Determine the CSS class based on the message content
const getMessageTypeClass = (text: string): string => {
  if (!text) return '';

  // Check for error messages
  if (text.toLowerCase().includes('error') || text.toLowerCase().includes('failed')) {
    return 'error';
  }

  // Check for warning messages
  if (text.toLowerCase().includes('warning') || text.toLowerCase().includes('caution')) {
    return 'warning';
  }

  // Check for success messages
  if (text.toLowerCase().includes('success') || text.toLowerCase().includes('created') ||
      text.toLowerCase().includes('completed')) {
    return 'success';
  }

  // Check for info messages
  if (text.toLowerCase().includes('info') || text.toLowerCase().includes('selected') ||
      text.toLowerCase().includes('addressing')) {
    return 'info';
  }

  return '';
};

// Simple MessageList component that resembles the original
const MessageList: React.FC<MessageListProps> = ({
  messages,
  onAction,
  onBuildMindmap,
  showBuildMindmapButton = true, // Show the button by default for teleological messages
  showLogging = true // Show logging by default
}) => {
  // Log when showBuildMindmapButton prop changes
  useEffect(() => {
    console.log('MessageList - showBuildMindmapButton prop changed to:', showBuildMindmapButton);
  }, [showBuildMindmapButton]);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Log when showLogging prop changes
  useEffect(() => {
    console.log('MessageList - showLogging prop changed to:', showLogging);
  }, [showLogging]);

  // Handle exploratory intent content
  const handleExploreTopic = (message) => {
    if (!onAction) return;

    // If the message already has a show_chatfork action, use that
    const existingAction = message.suggestedActions?.find(
      action => action.type === 'show_chatfork'
    );

    if (existingAction) {
      console.log('Using existing show_chatfork action:', existingAction);
      onAction(existingAction);
      return;
    }

    // Otherwise create a new action from the message data
    console.log('Creating new show_chatfork action for exploratory content');
    const exploratoryAction = {
      type: 'show_chatfork',
      label: 'Explore Topic',
      data: {
        title: message.text,
        description: message.text,
        intent: 'exploratory',
        responseType: {
          type: 'exploratory',
          requiresMindmap: false,
          requiresChatFork: true
        },
        content: message.responseType?.mbcpData || {
          intent: 'exploratory',
          text: message.text
        }
      }
    };

    console.log('Generated exploratory action:', exploratoryAction);
    onAction(exploratoryAction);
  };

  // Get showLogging from the store
  const storeShowLogging = useChatStore(state => state.showLogging);

  // Use the store's showLogging state or the prop (for backward compatibility)
  const isLoggingEnabled = typeof showLogging !== 'undefined' ? Boolean(showLogging) : storeShowLogging;

  // Separate user/assistant messages from system messages
  const userAssistantMessages = messages.filter(msg => msg.sender !== 'system');
  // Only include system messages if logging is enabled
  const systemMessages = isLoggingEnabled ? messages.filter(msg => msg.sender === 'system') : [];
  console.log('MessageList - showLogging prop:', showLogging, 'store showLogging:', storeShowLogging, 'isLoggingEnabled:', isLoggingEnabled, 'systemMessages count:', systemMessages.length);

  // Check if we have any teleological messages
  const hasTeleologicalMessage = userAssistantMessages.some(
    msg => msg.sender === 'assistant' && msg.responseType?.type === 'teleological'
  );

  // Never show the global button - we'll only use the per-message button for teleological intents
  const shouldShowGlobalButton = false;

  return (
    <div className="message-list">
      {/* Global Build Mindmap button when showBuildMindmapButton is true AND there are no teleological messages */}
      {shouldShowGlobalButton && (
        <div className="build-mindmap-container" style={{ marginBottom: '20px', textAlign: 'center' }}>
          <button
            className="build-mindmap-button"
            onClick={() => {
              console.log('GLOBAL BUILD MINDMAP BUTTON CLICKED');
              if (onBuildMindmap) {
                console.log('Calling onBuildMindmap handler from global button');
                onBuildMindmap();
              } else {
                console.error('No onBuildMindmap handler available for global button');
                alert('Error: No handler available for building mindmap');
              }
            }}
            style={{
              padding: '10px 20px',
              backgroundColor: '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '16px',
              fontWeight: 'bold'
            }}
          >
            Build Mind Map
          </button>
        </div>
      )}

      {/* Render user and assistant messages */}
      {userAssistantMessages.map((message, index) => {
        // Find system messages that should appear after this message
        const nextUserAssistantIndex = userAssistantMessages.findIndex((m, i) => i > index);
        const relatedSystemMessages = systemMessages.filter(sysMsg => {
          // If this is the last user/assistant message, show all remaining system messages
          if (nextUserAssistantIndex === -1) {
            return sysMsg.timestamp >= message.timestamp;
          }
          // Otherwise, show system messages between this message and the next user/assistant message
          return sysMsg.timestamp >= message.timestamp &&
                 sysMsg.timestamp < userAssistantMessages[nextUserAssistantIndex].timestamp;
        });

        return (
          <React.Fragment key={index}>
            <div
              className={`message ${message.sender === 'user' ? 'user' : 'assistant'}`}
            >
              <div className="message-header" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                <Typography variant="subtitle2" style={{ fontWeight: 'bold' }}>
                  {message.sender === 'user' ? 'User' : 'Agent'}
                </Typography>
                <Typography variant="caption" color="textSecondary">
                  {formatTimestamp(message.timestamp)}
                </Typography>
              </div>

              <Typography variant="body1" style={{ fontSize: '1rem', lineHeight: '1.5' }}>
                {message.text}
              </Typography>

              {/* Show action buttons from suggestedActions */}
              {message.suggestedActions && message.suggestedActions.length > 0 && (
                <div className="message-actions" style={{ marginTop: '10px' }}>
                  {message.suggestedActions.map((action, actionIndex) => (
                    <button
                      key={actionIndex}
                      className="action-button"
                      onClick={() => onAction && onAction(action)}
                      style={{ marginRight: '8px', padding: '6px 12px' }}
                    >
                      {action.label || 'Action'}
                    </button>
                  ))}
                </div>
              )}

              {/* Special case for exploratory intent - make sure it always shows a button */}
              {message.sender === 'assistant' &&
               message.responseType?.type === 'exploratory' &&
               (!message.suggestedActions || message.suggestedActions.length === 0) && (
                <div className="message-actions" style={{ marginTop: '10px' }}>
                  <button
                    className="action-button exploratory-button"
                    onClick={() => handleExploreTopic(message)}
                    style={{
                      marginRight: '8px',
                      padding: '6px 12px',
                      display: 'flex',
                      alignItems: 'center',
                      backgroundColor: '#0066cc',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer'
                    }}
                  >
                    <ExploreIcon style={{ marginRight: '4px', fontSize: '16px' }} />
                    Explore Topic
                  </button>
                </div>
              )}

              {/* Special case for teleological intent - make sure it always shows a Build Mindmap button */}
              {message.sender === 'assistant' &&
               message.responseType?.type === 'teleological' &&
               !message.mindmapCreated && // Add check for mindmapCreated flag
               onBuildMindmap && (
                <div className="message-actions" style={{ marginTop: '10px' }}>
                  <button
                    className="action-button teleological-button"
                    onClick={() => {
                      console.log('BUILD MINDMAP BUTTON CLICKED');
                      console.log('Message state before click:', message);

                      // Simply call the onBuildMindmap handler which is provided by useChat
                      if (onBuildMindmap) {
                        console.log('Calling onBuildMindmap handler');
                        onBuildMindmap();
                      } else {
                        console.error('No onBuildMindmap handler available');
                        alert('Error: No handler available for building mindmap');
                      }
                    }}
                    style={{
                      marginRight: '8px',
                      padding: '6px 12px',
                      display: 'flex',
                      alignItems: 'center',
                      backgroundColor: '#4CAF50',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer'
                    }}
                  >
                    Build Mind Map
                  </button>
                </div>
              )}
            </div>

            {/* Render related system messages after this user/assistant message */}
            {relatedSystemMessages.map((sysMsg, sysIndex) => (
              <div key={`sys-${index}-${sysIndex}`} className="system-message">
                <div className="system-message-text">
                  <span className="system-message-timestamp">{formatTimestamp(sysMsg.timestamp)}</span>
                  <span className="system-message-separator">│</span>
                  {sysMsg.text}
                </div>
              </div>
            ))}
          </React.Fragment>
        );
      })}

      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;