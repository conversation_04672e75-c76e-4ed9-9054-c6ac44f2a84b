<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 12.2.1 (20241206.2353)
 -->
<!-- Title: dependency&#45;cruiser output Pages: 1 -->
<svg width="1351pt" height="7050pt"
 viewBox="0.00 0.00 1351.00 7050.50" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 7046.5)">
<title>dependency&#45;cruiser output</title>
<polygon fill="white" stroke="none" points="-4,4 -4,-7046.5 1347,-7046.5 1347,4 -4,4"/>
<g id="clust1" class="cluster">
<title>cluster_..</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M904,-1333.5C904,-1333.5 1030,-1333.5 1030,-1333.5 1036,-1333.5 1042,-1339.5 1042,-1345.5 1042,-1345.5 1042,-1737.5 1042,-1737.5 1042,-1743.5 1036,-1749.5 1030,-1749.5 1030,-1749.5 904,-1749.5 904,-1749.5 898,-1749.5 892,-1743.5 892,-1737.5 892,-1737.5 892,-1345.5 892,-1345.5 892,-1339.5 898,-1333.5 904,-1333.5"/>
<text text-anchor="middle" x="967" y="-1736.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">..</text>
</g>
<g id="clust2" class="cluster">
<title>cluster_../..</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M912,-1341.5C912,-1341.5 1022,-1341.5 1022,-1341.5 1028,-1341.5 1034,-1347.5 1034,-1353.5 1034,-1353.5 1034,-1710.5 1034,-1710.5 1034,-1716.5 1028,-1722.5 1022,-1722.5 1022,-1722.5 912,-1722.5 912,-1722.5 906,-1722.5 900,-1716.5 900,-1710.5 900,-1710.5 900,-1353.5 900,-1353.5 900,-1347.5 906,-1341.5 912,-1341.5"/>
<text text-anchor="middle" x="967" y="-1709.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">..</text>
</g>
<g id="clust3" class="cluster">
<title>cluster_../../..</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M920,-1445.5C920,-1445.5 1014,-1445.5 1014,-1445.5 1020,-1445.5 1026,-1451.5 1026,-1457.5 1026,-1457.5 1026,-1683.5 1026,-1683.5 1026,-1689.5 1020,-1695.5 1014,-1695.5 1014,-1695.5 920,-1695.5 920,-1695.5 914,-1695.5 908,-1689.5 908,-1683.5 908,-1683.5 908,-1457.5 908,-1457.5 908,-1451.5 914,-1445.5 920,-1445.5"/>
<text text-anchor="middle" x="967" y="-1682.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">..</text>
</g>
<g id="clust4" class="cluster">
<title>cluster_../../../..</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M928,-1453.5C928,-1453.5 1006,-1453.5 1006,-1453.5 1012,-1453.5 1018,-1459.5 1018,-1465.5 1018,-1465.5 1018,-1564.5 1018,-1564.5 1018,-1570.5 1012,-1576.5 1006,-1576.5 1006,-1576.5 928,-1576.5 928,-1576.5 922,-1576.5 916,-1570.5 916,-1564.5 916,-1564.5 916,-1465.5 916,-1465.5 916,-1459.5 922,-1453.5 928,-1453.5"/>
<text text-anchor="middle" x="967" y="-1563.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">..</text>
</g>
<g id="clust5" class="cluster">
<title>cluster_../../../../core</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M936,-1461.5C936,-1461.5 998,-1461.5 998,-1461.5 1004,-1461.5 1010,-1467.5 1010,-1473.5 1010,-1473.5 1010,-1537.5 1010,-1537.5 1010,-1543.5 1004,-1549.5 998,-1549.5 998,-1549.5 936,-1549.5 936,-1549.5 930,-1549.5 924,-1543.5 924,-1537.5 924,-1537.5 924,-1473.5 924,-1473.5 924,-1467.5 930,-1461.5 936,-1461.5"/>
<text text-anchor="middle" x="967" y="-1536.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">core</text>
</g>
<g id="clust6" class="cluster">
<title>cluster_../../../../core/models</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M944,-1469.5C944,-1469.5 990,-1469.5 990,-1469.5 996,-1469.5 1002,-1475.5 1002,-1481.5 1002,-1481.5 1002,-1510.5 1002,-1510.5 1002,-1516.5 996,-1522.5 990,-1522.5 990,-1522.5 944,-1522.5 944,-1522.5 938,-1522.5 932,-1516.5 932,-1510.5 932,-1510.5 932,-1481.5 932,-1481.5 932,-1475.5 938,-1469.5 944,-1469.5"/>
<text text-anchor="middle" x="967" y="-1509.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">models</text>
</g>
<g id="clust7" class="cluster">
<title>cluster_../../../contexts</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M928.12,-1584.5C928.12,-1584.5 1005.88,-1584.5 1005.88,-1584.5 1011.88,-1584.5 1017.88,-1590.5 1017.88,-1596.5 1017.88,-1596.5 1017.88,-1656.5 1017.88,-1656.5 1017.88,-1662.5 1011.88,-1668.5 1005.88,-1668.5 1005.88,-1668.5 928.12,-1668.5 928.12,-1668.5 922.12,-1668.5 916.12,-1662.5 916.12,-1656.5 916.12,-1656.5 916.12,-1596.5 916.12,-1596.5 916.12,-1590.5 922.12,-1584.5 928.12,-1584.5"/>
<text text-anchor="middle" x="967" y="-1655.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">contexts</text>
</g>
<g id="clust8" class="cluster">
<title>cluster_../../services</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M920.88,-1349.5C920.88,-1349.5 1013.12,-1349.5 1013.12,-1349.5 1019.12,-1349.5 1025.12,-1355.5 1025.12,-1361.5 1025.12,-1361.5 1025.12,-1425.5 1025.12,-1425.5 1025.12,-1431.5 1019.12,-1437.5 1013.12,-1437.5 1013.12,-1437.5 920.88,-1437.5 920.88,-1437.5 914.88,-1437.5 908.88,-1431.5 908.88,-1425.5 908.88,-1425.5 908.88,-1361.5 908.88,-1361.5 908.88,-1355.5 914.88,-1349.5 920.88,-1349.5"/>
<text text-anchor="middle" x="967" y="-1424.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">services</text>
</g>
<g id="clust9" class="cluster">
<title>cluster_../../services/api</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M928.88,-1357.5C928.88,-1357.5 1005.12,-1357.5 1005.12,-1357.5 1011.12,-1357.5 1017.12,-1363.5 1017.12,-1369.5 1017.12,-1369.5 1017.12,-1398.5 1017.12,-1398.5 1017.12,-1404.5 1011.12,-1410.5 1005.12,-1410.5 1005.12,-1410.5 928.88,-1410.5 928.88,-1410.5 922.88,-1410.5 916.88,-1404.5 916.88,-1398.5 916.88,-1398.5 916.88,-1369.5 916.88,-1369.5 916.88,-1363.5 922.88,-1357.5 928.88,-1357.5"/>
<text text-anchor="middle" x="967" y="-1397.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">api</text>
</g>
<g id="clust10" class="cluster">
<title>cluster_.</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M407.25,-345.5C407.25,-345.5 517,-345.5 517,-345.5 523,-345.5 529,-351.5 529,-357.5 529,-357.5 529,-456.5 529,-456.5 529,-462.5 523,-468.5 517,-468.5 517,-468.5 407.25,-468.5 407.25,-468.5 401.25,-468.5 395.25,-462.5 395.25,-456.5 395.25,-456.5 395.25,-357.5 395.25,-357.5 395.25,-351.5 401.25,-345.5 407.25,-345.5"/>
<text text-anchor="middle" x="462.12" y="-455.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">.</text>
</g>
<g id="clust11" class="cluster">
<title>cluster_./components</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M415.25,-353.5C415.25,-353.5 509,-353.5 509,-353.5 515,-353.5 521,-359.5 521,-365.5 521,-365.5 521,-429.5 521,-429.5 521,-435.5 515,-441.5 509,-441.5 509,-441.5 415.25,-441.5 415.25,-441.5 409.25,-441.5 403.25,-435.5 403.25,-429.5 403.25,-429.5 403.25,-365.5 403.25,-365.5 403.25,-359.5 409.25,-353.5 415.25,-353.5"/>
<text text-anchor="middle" x="462.12" y="-428.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">components</text>
</g>
<g id="clust12" class="cluster">
<title>cluster_./components/Canvas</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M423.25,-361.5C423.25,-361.5 501,-361.5 501,-361.5 507,-361.5 513,-367.5 513,-373.5 513,-373.5 513,-402.5 513,-402.5 513,-408.5 507,-414.5 501,-414.5 501,-414.5 423.25,-414.5 423.25,-414.5 417.25,-414.5 411.25,-408.5 411.25,-402.5 411.25,-402.5 411.25,-373.5 411.25,-373.5 411.25,-367.5 417.25,-361.5 423.25,-361.5"/>
<text text-anchor="middle" x="462.12" y="-401.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Canvas</text>
</g>
<g id="clust13" class="cluster">
<title>cluster_node_modules</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1072.25,-1655.5C1072.25,-1655.5 1154,-1655.5 1154,-1655.5 1160,-1655.5 1166,-1661.5 1166,-1667.5 1166,-1667.5 1166,-2093.5 1166,-2093.5 1166,-2099.5 1160,-2105.5 1154,-2105.5 1154,-2105.5 1072.25,-2105.5 1072.25,-2105.5 1066.25,-2105.5 1060.25,-2099.5 1060.25,-2093.5 1060.25,-2093.5 1060.25,-1667.5 1060.25,-1667.5 1060.25,-1661.5 1066.25,-1655.5 1072.25,-1655.5"/>
<text text-anchor="middle" x="1113.12" y="-2092.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">node_modules</text>
</g>
<g id="clust14" class="cluster">
<title>cluster_node_modules/@mui</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1080.25,-1813.5C1080.25,-1813.5 1146,-1813.5 1146,-1813.5 1152,-1813.5 1158,-1819.5 1158,-1825.5 1158,-1825.5 1158,-1885.5 1158,-1885.5 1158,-1891.5 1152,-1897.5 1146,-1897.5 1146,-1897.5 1080.25,-1897.5 1080.25,-1897.5 1074.25,-1897.5 1068.25,-1891.5 1068.25,-1885.5 1068.25,-1885.5 1068.25,-1825.5 1068.25,-1825.5 1068.25,-1819.5 1074.25,-1813.5 1080.25,-1813.5"/>
<text text-anchor="middle" x="1113.12" y="-1884.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">@mui</text>
</g>
<g id="clust15" class="cluster">
<title>cluster_src</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M20,-2811.5C20,-2811.5 1323,-2811.5 1323,-2811.5 1329,-2811.5 1335,-2817.5 1335,-2823.5 1335,-2823.5 1335,-7022.5 1335,-7022.5 1335,-7028.5 1329,-7034.5 1323,-7034.5 1323,-7034.5 20,-7034.5 20,-7034.5 14,-7034.5 8,-7028.5 8,-7022.5 8,-7022.5 8,-2823.5 8,-2823.5 8,-2817.5 14,-2811.5 20,-2811.5"/>
<text text-anchor="middle" x="671.5" y="-7021.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">src</text>
</g>
<g id="clust16" class="cluster">
<title>cluster_src/components</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M100,-2819.5C100,-2819.5 1315,-2819.5 1315,-2819.5 1321,-2819.5 1327,-2825.5 1327,-2831.5 1327,-2831.5 1327,-6408.5 1327,-6408.5 1327,-6414.5 1321,-6420.5 1315,-6420.5 1315,-6420.5 100,-6420.5 100,-6420.5 94,-6420.5 88,-6414.5 88,-6408.5 88,-6408.5 88,-2831.5 88,-2831.5 88,-2825.5 94,-2819.5 100,-2819.5"/>
<text text-anchor="middle" x="707.5" y="-6407.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">components</text>
</g>
<g id="clust17" class="cluster">
<title>cluster_src/components/ChatFork</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M117.38,-6257.5C117.38,-6257.5 831.62,-6257.5 831.62,-6257.5 837.62,-6257.5 843.62,-6263.5 843.62,-6269.5 843.62,-6269.5 843.62,-6371.5 843.62,-6371.5 843.62,-6377.5 837.62,-6383.5 831.62,-6383.5 831.62,-6383.5 117.38,-6383.5 117.38,-6383.5 111.38,-6383.5 105.38,-6377.5 105.38,-6371.5 105.38,-6371.5 105.38,-6269.5 105.38,-6269.5 105.38,-6263.5 111.38,-6257.5 117.38,-6257.5"/>
<text text-anchor="middle" x="474.5" y="-6370.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ChatFork</text>
</g>
<g id="clust18" class="cluster">
<title>cluster_src/components/MindMap</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M205.25,-2827.5C205.25,-2827.5 1307,-2827.5 1307,-2827.5 1313,-2827.5 1319,-2833.5 1319,-2839.5 1319,-2839.5 1319,-6137.5 1319,-6137.5 1319,-6143.5 1313,-6149.5 1307,-6149.5 1307,-6149.5 205.25,-6149.5 205.25,-6149.5 199.25,-6149.5 193.25,-6143.5 193.25,-6137.5 193.25,-6137.5 193.25,-2839.5 193.25,-2839.5 193.25,-2833.5 199.25,-2827.5 205.25,-2827.5"/>
<text text-anchor="middle" x="756.12" y="-6136.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MindMap</text>
</g>
<g id="clust19" class="cluster">
<title>cluster_src/components/MindMap/components</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M365,-5043.5C365,-5043.5 1165.75,-5043.5 1165.75,-5043.5 1171.75,-5043.5 1177.75,-5049.5 1177.75,-5055.5 1177.75,-5055.5 1177.75,-6085.5 1177.75,-6085.5 1177.75,-6091.5 1171.75,-6097.5 1165.75,-6097.5 1165.75,-6097.5 365,-6097.5 365,-6097.5 359,-6097.5 353,-6091.5 353,-6085.5 353,-6085.5 353,-5055.5 353,-5055.5 353,-5049.5 359,-5043.5 365,-5043.5"/>
<text text-anchor="middle" x="765.38" y="-6084.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">components</text>
</g>
<g id="clust20" class="cluster">
<title>cluster_src/components/MindMap/components/Agents</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M414.25,-5138.5C414.25,-5138.5 510,-5138.5 510,-5138.5 516,-5138.5 522,-5144.5 522,-5150.5 522,-5150.5 522,-5241.5 522,-5241.5 522,-5247.5 516,-5253.5 510,-5253.5 510,-5253.5 414.25,-5253.5 414.25,-5253.5 408.25,-5253.5 402.25,-5247.5 402.25,-5241.5 402.25,-5241.5 402.25,-5150.5 402.25,-5150.5 402.25,-5144.5 408.25,-5138.5 414.25,-5138.5"/>
<text text-anchor="middle" x="462.12" y="-5240.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Agents</text>
</g>
<g id="clust21" class="cluster">
<title>cluster_src/components/MindMap/components/Canvas</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M579.62,-5530.5C579.62,-5530.5 844.12,-5530.5 844.12,-5530.5 850.12,-5530.5 856.12,-5536.5 856.12,-5542.5 856.12,-5542.5 856.12,-5694.5 856.12,-5694.5 856.12,-5700.5 850.12,-5706.5 844.12,-5706.5 844.12,-5706.5 579.62,-5706.5 579.62,-5706.5 573.62,-5706.5 567.62,-5700.5 567.62,-5694.5 567.62,-5694.5 567.62,-5542.5 567.62,-5542.5 567.62,-5536.5 573.62,-5530.5 579.62,-5530.5"/>
<text text-anchor="middle" x="711.88" y="-5693.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Canvas</text>
</g>
<g id="clust22" class="cluster">
<title>cluster_src/components/MindMap/components/Canvas/hooks</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M587.62,-5538.5C587.62,-5538.5 694.62,-5538.5 694.62,-5538.5 700.62,-5538.5 706.62,-5544.5 706.62,-5550.5 706.62,-5550.5 706.62,-5641.5 706.62,-5641.5 706.62,-5647.5 700.62,-5653.5 694.62,-5653.5 694.62,-5653.5 587.62,-5653.5 587.62,-5653.5 581.62,-5653.5 575.62,-5647.5 575.62,-5641.5 575.62,-5641.5 575.62,-5550.5 575.62,-5550.5 575.62,-5544.5 581.62,-5538.5 587.62,-5538.5"/>
<text text-anchor="middle" x="641.12" y="-5640.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">hooks</text>
</g>
<g id="clust23" class="cluster">
<title>cluster_src/components/MindMap/components/Canvas/types</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M765.88,-5569.5C765.88,-5569.5 836.12,-5569.5 836.12,-5569.5 842.12,-5569.5 848.12,-5575.5 848.12,-5581.5 848.12,-5581.5 848.12,-5610.5 848.12,-5610.5 848.12,-5616.5 842.12,-5622.5 836.12,-5622.5 836.12,-5622.5 765.88,-5622.5 765.88,-5622.5 759.88,-5622.5 753.88,-5616.5 753.88,-5610.5 753.88,-5610.5 753.88,-5581.5 753.88,-5581.5 753.88,-5575.5 759.88,-5569.5 765.88,-5569.5"/>
<text text-anchor="middle" x="801" y="-5609.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">types</text>
</g>
<g id="clust24" class="cluster">
<title>cluster_src/components/MindMap/components/ControlPanel</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M407.5,-5077.5C407.5,-5077.5 516.75,-5077.5 516.75,-5077.5 522.75,-5077.5 528.75,-5083.5 528.75,-5089.5 528.75,-5089.5 528.75,-5118.5 528.75,-5118.5 528.75,-5124.5 522.75,-5130.5 516.75,-5130.5 516.75,-5130.5 407.5,-5130.5 407.5,-5130.5 401.5,-5130.5 395.5,-5124.5 395.5,-5118.5 395.5,-5118.5 395.5,-5089.5 395.5,-5089.5 395.5,-5083.5 401.5,-5077.5 407.5,-5077.5"/>
<text text-anchor="middle" x="462.12" y="-5117.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">ControlPanel</text>
</g>
<g id="clust25" class="cluster">
<title>cluster_src/components/MindMap/components/Controls</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M594,-5383.5C594,-5383.5 688.25,-5383.5 688.25,-5383.5 694.25,-5383.5 700.25,-5389.5 700.25,-5395.5 700.25,-5395.5 700.25,-5486.5 700.25,-5486.5 700.25,-5492.5 694.25,-5498.5 688.25,-5498.5 688.25,-5498.5 594,-5498.5 594,-5498.5 588,-5498.5 582,-5492.5 582,-5486.5 582,-5486.5 582,-5395.5 582,-5395.5 582,-5389.5 588,-5383.5 594,-5383.5"/>
<text text-anchor="middle" x="641.12" y="-5485.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Controls</text>
</g>
<g id="clust26" class="cluster">
<title>cluster_src/components/MindMap/components/Dialogs</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M585,-5714.5C585,-5714.5 1157.75,-5714.5 1157.75,-5714.5 1163.75,-5714.5 1169.75,-5720.5 1169.75,-5726.5 1169.75,-5726.5 1169.75,-6058.5 1169.75,-6058.5 1169.75,-6064.5 1163.75,-6070.5 1157.75,-6070.5 1157.75,-6070.5 585,-6070.5 585,-6070.5 579,-6070.5 573,-6064.5 573,-6058.5 573,-6058.5 573,-5726.5 573,-5726.5 573,-5720.5 579,-5714.5 585,-5714.5"/>
<text text-anchor="middle" x="871.38" y="-6057.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Dialogs</text>
</g>
<g id="clust27" class="cluster">
<title>cluster_src/components/MindMap/components/Dialogs/NodeDialog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M745.62,-5779.5C745.62,-5779.5 1149.75,-5779.5 1149.75,-5779.5 1155.75,-5779.5 1161.75,-5785.5 1161.75,-5791.5 1161.75,-5791.5 1161.75,-6031.5 1161.75,-6031.5 1161.75,-6037.5 1155.75,-6043.5 1149.75,-6043.5 1149.75,-6043.5 745.62,-6043.5 745.62,-6043.5 739.62,-6043.5 733.62,-6037.5 733.62,-6031.5 733.62,-6031.5 733.62,-5791.5 733.62,-5791.5 733.62,-5785.5 739.62,-5779.5 745.62,-5779.5"/>
<text text-anchor="middle" x="947.69" y="-6030.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">NodeDialog</text>
</g>
<g id="clust28" class="cluster">
<title>cluster_src/components/MindMap/components/Dialogs/NodeDialog/hooks</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M760.25,-5875.5C760.25,-5875.5 841.75,-5875.5 841.75,-5875.5 847.75,-5875.5 853.75,-5881.5 853.75,-5887.5 853.75,-5887.5 853.75,-5916.5 853.75,-5916.5 853.75,-5922.5 847.75,-5928.5 841.75,-5928.5 841.75,-5928.5 760.25,-5928.5 760.25,-5928.5 754.25,-5928.5 748.25,-5922.5 748.25,-5916.5 748.25,-5916.5 748.25,-5887.5 748.25,-5887.5 748.25,-5881.5 754.25,-5875.5 760.25,-5875.5"/>
<text text-anchor="middle" x="801" y="-5915.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">hooks</text>
</g>
<g id="clust29" class="cluster">
<title>cluster_src/components/MindMap/components/Dialogs/NodeDialog/utils</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M941.62,-5813.5C941.62,-5813.5 992.38,-5813.5 992.38,-5813.5 998.38,-5813.5 1004.38,-5819.5 1004.38,-5825.5 1004.38,-5825.5 1004.38,-5854.5 1004.38,-5854.5 1004.38,-5860.5 998.38,-5866.5 992.38,-5866.5 992.38,-5866.5 941.62,-5866.5 941.62,-5866.5 935.62,-5866.5 929.62,-5860.5 929.62,-5854.5 929.62,-5854.5 929.62,-5825.5 929.62,-5825.5 929.62,-5819.5 935.62,-5813.5 941.62,-5813.5"/>
<text text-anchor="middle" x="967" y="-5853.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">utils</text>
</g>
<g id="clust30" class="cluster">
<title>cluster_src/components/MindMap/components/Dialogs/StartupDialog</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M602.25,-5722.5C602.25,-5722.5 680,-5722.5 680,-5722.5 686,-5722.5 692,-5728.5 692,-5734.5 692,-5734.5 692,-5763.5 692,-5763.5 692,-5769.5 686,-5775.5 680,-5775.5 680,-5775.5 602.25,-5775.5 602.25,-5775.5 596.25,-5775.5 590.25,-5769.5 590.25,-5763.5 590.25,-5763.5 590.25,-5734.5 590.25,-5734.5 590.25,-5728.5 596.25,-5722.5 602.25,-5722.5"/>
<text text-anchor="middle" x="641.12" y="-5762.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">StartupDialog</text>
</g>
<g id="clust31" class="cluster">
<title>cluster_src/components/MindMap/components/Manager</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M373,-5261.5C373,-5261.5 871,-5261.5 871,-5261.5 877,-5261.5 883,-5267.5 883,-5273.5 883,-5273.5 883,-5302.5 883,-5302.5 883,-5308.5 877,-5314.5 871,-5314.5 871,-5314.5 373,-5314.5 373,-5314.5 367,-5314.5 361,-5308.5 361,-5302.5 361,-5302.5 361,-5273.5 361,-5273.5 361,-5267.5 367,-5261.5 373,-5261.5"/>
<text text-anchor="middle" x="622" y="-5301.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Manager</text>
</g>
<g id="clust32" class="cluster">
<title>cluster_src/components/MindMap/components/MindMapManager</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M414.25,-5322.5C414.25,-5322.5 690.12,-5322.5 690.12,-5322.5 696.12,-5322.5 702.12,-5328.5 702.12,-5334.5 702.12,-5334.5 702.12,-5363.5 702.12,-5363.5 702.12,-5369.5 696.12,-5375.5 690.12,-5375.5 690.12,-5375.5 414.25,-5375.5 414.25,-5375.5 408.25,-5375.5 402.25,-5369.5 402.25,-5363.5 402.25,-5363.5 402.25,-5334.5 402.25,-5334.5 402.25,-5328.5 408.25,-5322.5 414.25,-5322.5"/>
<text text-anchor="middle" x="552.19" y="-5362.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">MindMapManager</text>
</g>
<g id="clust33" class="cluster">
<title>cluster_src/components/MindMap/components/Node</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M742.62,-5322.5C742.62,-5322.5 859.38,-5322.5 859.38,-5322.5 865.38,-5322.5 871.38,-5328.5 871.38,-5334.5 871.38,-5334.5 871.38,-5425.5 871.38,-5425.5 871.38,-5431.5 865.38,-5437.5 859.38,-5437.5 859.38,-5437.5 742.62,-5437.5 742.62,-5437.5 736.62,-5437.5 730.62,-5431.5 730.62,-5425.5 730.62,-5425.5 730.62,-5334.5 730.62,-5334.5 730.62,-5328.5 736.62,-5322.5 742.62,-5322.5"/>
<text text-anchor="start" x="789.75" y="-5424.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">Node</text>
</g>
<g id="clust34" class="cluster">
<title>cluster_src/components/MindMap/config</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M248.38,-3154.5C248.38,-3154.5 294.38,-3154.5 294.38,-3154.5 300.38,-3154.5 306.38,-3160.5 306.38,-3166.5 306.38,-3166.5 306.38,-3195.5 306.38,-3195.5 306.38,-3201.5 300.38,-3207.5 294.38,-3207.5 294.38,-3207.5 248.38,-3207.5 248.38,-3207.5 242.38,-3207.5 236.38,-3201.5 236.38,-3195.5 236.38,-3195.5 236.38,-3166.5 236.38,-3166.5 236.38,-3160.5 242.38,-3154.5 248.38,-3154.5"/>
<text text-anchor="middle" x="271.38" y="-3194.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">config</text>
</g>
<g id="clust35" class="cluster">
<title>cluster_src/components/MindMap/context</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M921.38,-3629.5C921.38,-3629.5 1012.62,-3629.5 1012.62,-3629.5 1018.62,-3629.5 1024.62,-3635.5 1024.62,-3641.5 1024.62,-3641.5 1024.62,-3670.5 1024.62,-3670.5 1024.62,-3676.5 1018.62,-3682.5 1012.62,-3682.5 1012.62,-3682.5 921.38,-3682.5 921.38,-3682.5 915.38,-3682.5 909.38,-3676.5 909.38,-3670.5 909.38,-3670.5 909.38,-3641.5 909.38,-3641.5 909.38,-3635.5 915.38,-3629.5 921.38,-3629.5"/>
<text text-anchor="middle" x="967" y="-3669.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">context</text>
</g>
<g id="clust36" class="cluster">
<title>cluster_src/components/MindMap/core</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M590.12,-4654.5C590.12,-4654.5 1299,-4654.5 1299,-4654.5 1305,-4654.5 1311,-4660.5 1311,-4666.5 1311,-4666.5 1311,-5023.5 1311,-5023.5 1311,-5029.5 1305,-5035.5 1299,-5035.5 1299,-5035.5 590.12,-5035.5 590.12,-5035.5 584.12,-5035.5 578.12,-5029.5 578.12,-5023.5 578.12,-5023.5 578.12,-4666.5 578.12,-4666.5 578.12,-4660.5 584.12,-4654.5 590.12,-4654.5"/>
<text text-anchor="middle" x="944.56" y="-5022.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">core</text>
</g>
<g id="clust37" class="cluster">
<title>cluster_src/components/MindMap/core/adapters</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M598.12,-4955.5C598.12,-4955.5 684.12,-4955.5 684.12,-4955.5 690.12,-4955.5 696.12,-4961.5 696.12,-4967.5 696.12,-4967.5 696.12,-4996.5 696.12,-4996.5 696.12,-5002.5 690.12,-5008.5 684.12,-5008.5 684.12,-5008.5 598.12,-5008.5 598.12,-5008.5 592.12,-5008.5 586.12,-5002.5 586.12,-4996.5 586.12,-4996.5 586.12,-4967.5 586.12,-4967.5 586.12,-4961.5 592.12,-4955.5 598.12,-4955.5"/>
<text text-anchor="middle" x="641.12" y="-4995.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">adapters</text>
</g>
<g id="clust38" class="cluster">
<title>cluster_src/components/MindMap/core/models</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1072.38,-4879.5C1072.38,-4879.5 1265.62,-4879.5 1265.62,-4879.5 1271.62,-4879.5 1277.62,-4885.5 1277.62,-4891.5 1277.62,-4891.5 1277.62,-4951.5 1277.62,-4951.5 1277.62,-4957.5 1271.62,-4963.5 1265.62,-4963.5 1265.62,-4963.5 1072.38,-4963.5 1072.38,-4963.5 1066.38,-4963.5 1060.38,-4957.5 1060.38,-4951.5 1060.38,-4951.5 1060.38,-4891.5 1060.38,-4891.5 1060.38,-4885.5 1066.38,-4879.5 1072.38,-4879.5"/>
<text text-anchor="middle" x="1169" y="-4950.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">models</text>
</g>
<g id="clust39" class="cluster">
<title>cluster_src/components/MindMap/core/operations</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M914.62,-4886.5C914.62,-4886.5 1019.38,-4886.5 1019.38,-4886.5 1025.38,-4886.5 1031.38,-4892.5 1031.38,-4898.5 1031.38,-4898.5 1031.38,-4989.5 1031.38,-4989.5 1031.38,-4995.5 1025.38,-5001.5 1019.38,-5001.5 1019.38,-5001.5 914.62,-5001.5 914.62,-5001.5 908.62,-5001.5 902.62,-4995.5 902.62,-4989.5 902.62,-4989.5 902.62,-4898.5 902.62,-4898.5 902.62,-4892.5 908.62,-4886.5 914.62,-4886.5"/>
<text text-anchor="middle" x="967" y="-4988.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">operations</text>
</g>
<g id="clust40" class="cluster">
<title>cluster_src/components/MindMap/core/rag</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M606.38,-4760.5C606.38,-4760.5 675.88,-4760.5 675.88,-4760.5 681.88,-4760.5 687.88,-4766.5 687.88,-4772.5 687.88,-4772.5 687.88,-4863.5 687.88,-4863.5 687.88,-4869.5 681.88,-4875.5 675.88,-4875.5 675.88,-4875.5 606.38,-4875.5 606.38,-4875.5 600.38,-4875.5 594.38,-4869.5 594.38,-4863.5 594.38,-4863.5 594.38,-4772.5 594.38,-4772.5 594.38,-4766.5 600.38,-4760.5 606.38,-4760.5"/>
<text text-anchor="middle" x="641.12" y="-4862.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">rag</text>
</g>
<g id="clust41" class="cluster">
<title>cluster_src/components/MindMap/core/state</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M753.12,-4664.5C753.12,-4664.5 1291,-4664.5 1291,-4664.5 1297,-4664.5 1303,-4670.5 1303,-4676.5 1303,-4676.5 1303,-4859.5 1303,-4859.5 1303,-4865.5 1297,-4871.5 1291,-4871.5 1291,-4871.5 753.12,-4871.5 753.12,-4871.5 747.12,-4871.5 741.12,-4865.5 741.12,-4859.5 741.12,-4859.5 741.12,-4676.5 741.12,-4676.5 741.12,-4670.5 747.12,-4664.5 753.12,-4664.5"/>
<text text-anchor="middle" x="1022.06" y="-4858.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">state</text>
</g>
<g id="clust42" class="cluster">
<title>cluster_src/components/MindMap/core/state/store</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M778,-4672.5C778,-4672.5 1283,-4672.5 1283,-4672.5 1289,-4672.5 1295,-4678.5 1295,-4684.5 1295,-4684.5 1295,-4775.5 1295,-4775.5 1295,-4781.5 1289,-4787.5 1283,-4787.5 1283,-4787.5 778,-4787.5 778,-4787.5 772,-4787.5 766,-4781.5 766,-4775.5 766,-4775.5 766,-4684.5 766,-4684.5 766,-4678.5 772,-4672.5 778,-4672.5"/>
<text text-anchor="middle" x="1030.5" y="-4774.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">store</text>
</g>
<g id="clust43" class="cluster">
<title>cluster_src/components/MindMap/events</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M213.25,-3374.5C213.25,-3374.5 329.5,-3374.5 329.5,-3374.5 335.5,-3374.5 341.5,-3380.5 341.5,-3386.5 341.5,-3386.5 341.5,-3569.5 341.5,-3569.5 341.5,-3575.5 335.5,-3581.5 329.5,-3581.5 329.5,-3581.5 213.25,-3581.5 213.25,-3581.5 207.25,-3581.5 201.25,-3575.5 201.25,-3569.5 201.25,-3569.5 201.25,-3386.5 201.25,-3386.5 201.25,-3380.5 207.25,-3374.5 213.25,-3374.5"/>
<text text-anchor="middle" x="271.38" y="-3568.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">events</text>
</g>
<g id="clust44" class="cluster">
<title>cluster_src/components/MindMap/events/handlers</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M221.25,-3382.5C221.25,-3382.5 321.5,-3382.5 321.5,-3382.5 327.5,-3382.5 333.5,-3388.5 333.5,-3394.5 333.5,-3394.5 333.5,-3485.5 333.5,-3485.5 333.5,-3491.5 327.5,-3497.5 321.5,-3497.5 321.5,-3497.5 221.25,-3497.5 221.25,-3497.5 215.25,-3497.5 209.25,-3491.5 209.25,-3485.5 209.25,-3485.5 209.25,-3394.5 209.25,-3394.5 209.25,-3388.5 215.25,-3382.5 221.25,-3382.5"/>
<text text-anchor="middle" x="271.38" y="-3484.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">handlers</text>
</g>
<g id="clust45" class="cluster">
<title>cluster_src/components/MindMap/hooks</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M586.12,-3335.5C586.12,-3335.5 858.25,-3335.5 858.25,-3335.5 864.25,-3335.5 870.25,-3341.5 870.25,-3347.5 870.25,-3347.5 870.25,-3655.5 870.25,-3655.5 870.25,-3661.5 864.25,-3667.5 858.25,-3667.5 858.25,-3667.5 586.12,-3667.5 586.12,-3667.5 580.12,-3667.5 574.12,-3661.5 574.12,-3655.5 574.12,-3655.5 574.12,-3347.5 574.12,-3347.5 574.12,-3341.5 580.12,-3335.5 586.12,-3335.5"/>
<text text-anchor="middle" x="722.19" y="-3654.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">hooks</text>
</g>
<g id="clust46" class="cluster">
<title>cluster_src/components/MindMap/layouts</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M618.12,-4027.5C618.12,-4027.5 1265.62,-4027.5 1265.62,-4027.5 1271.62,-4027.5 1277.62,-4033.5 1277.62,-4039.5 1277.62,-4039.5 1277.62,-4403.5 1277.62,-4403.5 1277.62,-4409.5 1271.62,-4415.5 1265.62,-4415.5 1265.62,-4415.5 618.12,-4415.5 618.12,-4415.5 612.12,-4415.5 606.12,-4409.5 606.12,-4403.5 606.12,-4403.5 606.12,-4039.5 606.12,-4039.5 606.12,-4033.5 612.12,-4027.5 618.12,-4027.5"/>
<text text-anchor="middle" x="941.88" y="-4402.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">layouts</text>
</g>
<g id="clust47" class="cluster">
<title>cluster_src/components/MindMap/layouts/adapters</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M746.75,-4335.5C746.75,-4335.5 855.25,-4335.5 855.25,-4335.5 861.25,-4335.5 867.25,-4341.5 867.25,-4347.5 867.25,-4347.5 867.25,-4376.5 867.25,-4376.5 867.25,-4382.5 861.25,-4388.5 855.25,-4388.5 855.25,-4388.5 746.75,-4388.5 746.75,-4388.5 740.75,-4388.5 734.75,-4382.5 734.75,-4376.5 734.75,-4376.5 734.75,-4347.5 734.75,-4347.5 734.75,-4341.5 740.75,-4335.5 746.75,-4335.5"/>
<text text-anchor="middle" x="801" y="-4375.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">adapters</text>
</g>
<g id="clust48" class="cluster">
<title>cluster_src/components/MindMap/layouts/components</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M759.88,-4130.5C759.88,-4130.5 1009.25,-4130.5 1009.25,-4130.5 1015.25,-4130.5 1021.25,-4136.5 1021.25,-4142.5 1021.25,-4142.5 1021.25,-4171.5 1021.25,-4171.5 1021.25,-4177.5 1015.25,-4183.5 1009.25,-4183.5 1009.25,-4183.5 759.88,-4183.5 759.88,-4183.5 753.88,-4183.5 747.88,-4177.5 747.88,-4171.5 747.88,-4171.5 747.88,-4142.5 747.88,-4142.5 747.88,-4136.5 753.88,-4130.5 759.88,-4130.5"/>
<text text-anchor="middle" x="884.56" y="-4170.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">components</text>
</g>
<g id="clust49" class="cluster">
<title>cluster_src/components/MindMap/layouts/hooks</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M937.12,-4035.5C937.12,-4035.5 996.88,-4035.5 996.88,-4035.5 1002.88,-4035.5 1008.88,-4041.5 1008.88,-4047.5 1008.88,-4047.5 1008.88,-4076.5 1008.88,-4076.5 1008.88,-4082.5 1002.88,-4088.5 996.88,-4088.5 996.88,-4088.5 937.12,-4088.5 937.12,-4088.5 931.12,-4088.5 925.12,-4082.5 925.12,-4076.5 925.12,-4076.5 925.12,-4047.5 925.12,-4047.5 925.12,-4041.5 931.12,-4035.5 937.12,-4035.5"/>
<text text-anchor="middle" x="967" y="-4075.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">hooks</text>
</g>
<g id="clust50" class="cluster">
<title>cluster_src/components/MindMap/layouts/strategies</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M903,-4201.5C903,-4201.5 1031,-4201.5 1031,-4201.5 1037,-4201.5 1043,-4207.5 1043,-4213.5 1043,-4213.5 1043,-4366.5 1043,-4366.5 1043,-4372.5 1037,-4378.5 1031,-4378.5 1031,-4378.5 903,-4378.5 903,-4378.5 897,-4378.5 891,-4372.5 891,-4366.5 891,-4366.5 891,-4213.5 891,-4213.5 891,-4207.5 897,-4201.5 903,-4201.5"/>
<text text-anchor="middle" x="967" y="-4365.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">strategies</text>
</g>
<g id="clust51" class="cluster">
<title>cluster_src/components/MindMap/services</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M593.5,-2835.5C593.5,-2835.5 832,-2835.5 832,-2835.5 838,-2835.5 844,-2841.5 844,-2847.5 844,-2847.5 844,-3188.5 844,-3188.5 844,-3194.5 838,-3200.5 832,-3200.5 832,-3200.5 593.5,-3200.5 593.5,-3200.5 587.5,-3200.5 581.5,-3194.5 581.5,-3188.5 581.5,-3188.5 581.5,-2847.5 581.5,-2847.5 581.5,-2841.5 587.5,-2835.5 593.5,-2835.5"/>
<text text-anchor="middle" x="712.75" y="-3187.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">services</text>
</g>
<g id="clust52" class="cluster">
<title>cluster_src/components/MindMap/services/api</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M601.5,-2904.5C601.5,-2904.5 824,-2904.5 824,-2904.5 830,-2904.5 836,-2910.5 836,-2916.5 836,-2916.5 836,-3038.5 836,-3038.5 836,-3044.5 830,-3050.5 824,-3050.5 824,-3050.5 601.5,-3050.5 601.5,-3050.5 595.5,-3050.5 589.5,-3044.5 589.5,-3038.5 589.5,-3038.5 589.5,-2916.5 589.5,-2916.5 589.5,-2910.5 595.5,-2904.5 601.5,-2904.5"/>
<text text-anchor="middle" x="712.75" y="-3037.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">api</text>
</g>
<g id="clust53" class="cluster">
<title>cluster_src/components/MindMap/services/prompts</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M606.38,-2843.5C606.38,-2843.5 675.88,-2843.5 675.88,-2843.5 681.88,-2843.5 687.88,-2849.5 687.88,-2855.5 687.88,-2855.5 687.88,-2884.5 687.88,-2884.5 687.88,-2890.5 681.88,-2896.5 675.88,-2896.5 675.88,-2896.5 606.38,-2896.5 606.38,-2896.5 600.38,-2896.5 594.38,-2890.5 594.38,-2884.5 594.38,-2884.5 594.38,-2855.5 594.38,-2855.5 594.38,-2849.5 600.38,-2843.5 606.38,-2843.5"/>
<text text-anchor="middle" x="641.12" y="-2883.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">prompts</text>
</g>
<g id="clust54" class="cluster">
<title>cluster_src/components/MindMap/services/storage</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M602.25,-3058.5C602.25,-3058.5 680,-3058.5 680,-3058.5 686,-3058.5 692,-3064.5 692,-3070.5 692,-3070.5 692,-3161.5 692,-3161.5 692,-3167.5 686,-3173.5 680,-3173.5 680,-3173.5 602.25,-3173.5 602.25,-3173.5 596.25,-3173.5 590.25,-3167.5 590.25,-3161.5 590.25,-3161.5 590.25,-3070.5 590.25,-3070.5 590.25,-3064.5 596.25,-3058.5 602.25,-3058.5"/>
<text text-anchor="middle" x="641.12" y="-3160.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">storage</text>
</g>
<g id="clust55" class="cluster">
<title>cluster_src/components/MindMap/types</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M927,-3773.5C927,-3773.5 1007,-3773.5 1007,-3773.5 1013,-3773.5 1019,-3779.5 1019,-3785.5 1019,-3785.5 1019,-3845.5 1019,-3845.5 1019,-3851.5 1013,-3857.5 1007,-3857.5 1007,-3857.5 927,-3857.5 927,-3857.5 921,-3857.5 915,-3851.5 915,-3845.5 915,-3845.5 915,-3785.5 915,-3785.5 915,-3779.5 921,-3773.5 927,-3773.5"/>
<text text-anchor="middle" x="967" y="-3844.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">types</text>
</g>
<g id="clust56" class="cluster">
<title>cluster_src/components/MindMap/utils</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M754.62,-4466.5C754.62,-4466.5 1020.88,-4466.5 1020.88,-4466.5 1026.88,-4466.5 1032.88,-4472.5 1032.88,-4478.5 1032.88,-4478.5 1032.88,-4600.5 1032.88,-4600.5 1032.88,-4606.5 1026.88,-4612.5 1020.88,-4612.5 1020.88,-4612.5 754.62,-4612.5 754.62,-4612.5 748.62,-4612.5 742.62,-4606.5 742.62,-4600.5 742.62,-4600.5 742.62,-4478.5 742.62,-4478.5 742.62,-4472.5 748.62,-4466.5 754.62,-4466.5"/>
<text text-anchor="middle" x="887.75" y="-4599.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">utils</text>
</g>
<g id="clust57" class="cluster">
<title>cluster_src/governance</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M395,-6428.5C395,-6428.5 1152.12,-6428.5 1152.12,-6428.5 1158.12,-6428.5 1164.12,-6434.5 1164.12,-6440.5 1164.12,-6440.5 1164.12,-6698.5 1164.12,-6698.5 1164.12,-6704.5 1158.12,-6710.5 1152.12,-6710.5 1152.12,-6710.5 395,-6710.5 395,-6710.5 389,-6710.5 383,-6704.5 383,-6698.5 383,-6698.5 383,-6440.5 383,-6440.5 383,-6434.5 389,-6428.5 395,-6428.5"/>
<text text-anchor="middle" x="773.56" y="-6697.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">governance</text>
</g>
<g id="clust58" class="cluster">
<title>cluster_src/governance/chat</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M403,-6436.5C403,-6436.5 1144.12,-6436.5 1144.12,-6436.5 1150.12,-6436.5 1156.12,-6442.5 1156.12,-6448.5 1156.12,-6448.5 1156.12,-6671.5 1156.12,-6671.5 1156.12,-6677.5 1150.12,-6683.5 1144.12,-6683.5 1144.12,-6683.5 403,-6683.5 403,-6683.5 397,-6683.5 391,-6677.5 391,-6671.5 391,-6671.5 391,-6448.5 391,-6448.5 391,-6442.5 397,-6436.5 403,-6436.5"/>
<text text-anchor="middle" x="773.56" y="-6670.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">chat</text>
</g>
<g id="clust59" class="cluster">
<title>cluster_src/governance/chat/components</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M761,-6470.5C761,-6470.5 841,-6470.5 841,-6470.5 847,-6470.5 853,-6476.5 853,-6482.5 853,-6482.5 853,-6604.5 853,-6604.5 853,-6610.5 847,-6616.5 841,-6616.5 841,-6616.5 761,-6616.5 761,-6616.5 755,-6616.5 749,-6610.5 749,-6604.5 749,-6604.5 749,-6482.5 749,-6482.5 749,-6476.5 755,-6470.5 761,-6470.5"/>
<text text-anchor="middle" x="801" y="-6603.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">components</text>
</g>
<g id="clust60" class="cluster">
<title>cluster_src/governance/chat/hooks</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M918.38,-6504.5C918.38,-6504.5 1015.62,-6504.5 1015.62,-6504.5 1021.62,-6504.5 1027.62,-6510.5 1027.62,-6516.5 1027.62,-6516.5 1027.62,-6576.5 1027.62,-6576.5 1027.62,-6582.5 1021.62,-6588.5 1015.62,-6588.5 1015.62,-6588.5 918.38,-6588.5 918.38,-6588.5 912.38,-6588.5 906.38,-6582.5 906.38,-6576.5 906.38,-6576.5 906.38,-6516.5 906.38,-6516.5 906.38,-6510.5 912.38,-6504.5 918.38,-6504.5"/>
<text text-anchor="middle" x="967" y="-6575.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">hooks</text>
</g>
<g id="clust61" class="cluster">
<title>cluster_src/governance/chat/types</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1090.12,-6603.5C1090.12,-6603.5 1136.12,-6603.5 1136.12,-6603.5 1142.12,-6603.5 1148.12,-6609.5 1148.12,-6615.5 1148.12,-6615.5 1148.12,-6644.5 1148.12,-6644.5 1148.12,-6650.5 1142.12,-6656.5 1136.12,-6656.5 1136.12,-6656.5 1090.12,-6656.5 1090.12,-6656.5 1084.12,-6656.5 1078.12,-6650.5 1078.12,-6644.5 1078.12,-6644.5 1078.12,-6615.5 1078.12,-6615.5 1078.12,-6609.5 1084.12,-6603.5 1090.12,-6603.5"/>
<text text-anchor="middle" x="1113.12" y="-6643.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">types</text>
</g>
<g id="clust62" class="cluster">
<title>cluster_src/governance/chat/utils</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M439.12,-6444.5C439.12,-6444.5 485.12,-6444.5 485.12,-6444.5 491.12,-6444.5 497.12,-6450.5 497.12,-6456.5 497.12,-6456.5 497.12,-6485.5 497.12,-6485.5 497.12,-6491.5 491.12,-6497.5 485.12,-6497.5 485.12,-6497.5 439.12,-6497.5 439.12,-6497.5 433.12,-6497.5 427.12,-6491.5 427.12,-6485.5 427.12,-6485.5 427.12,-6456.5 427.12,-6456.5 427.12,-6450.5 433.12,-6444.5 439.12,-6444.5"/>
<text text-anchor="middle" x="462.12" y="-6484.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">utils</text>
</g>
<g id="clust63" class="cluster">
<title>cluster_src/services</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1063,-6731.5C1063,-6731.5 1293.25,-6731.5 1293.25,-6731.5 1299.25,-6731.5 1305.25,-6737.5 1305.25,-6743.5 1305.25,-6743.5 1305.25,-6807.5 1305.25,-6807.5 1305.25,-6813.5 1299.25,-6819.5 1293.25,-6819.5 1293.25,-6819.5 1063,-6819.5 1063,-6819.5 1057,-6819.5 1051,-6813.5 1051,-6807.5 1051,-6807.5 1051,-6743.5 1051,-6743.5 1051,-6737.5 1057,-6731.5 1063,-6731.5"/>
<text text-anchor="middle" x="1178.12" y="-6806.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">services</text>
</g>
<g id="clust64" class="cluster">
<title>cluster_src/services/api</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1200,-6739.5C1200,-6739.5 1285.25,-6739.5 1285.25,-6739.5 1291.25,-6739.5 1297.25,-6745.5 1297.25,-6751.5 1297.25,-6751.5 1297.25,-6780.5 1297.25,-6780.5 1297.25,-6786.5 1291.25,-6792.5 1285.25,-6792.5 1285.25,-6792.5 1200,-6792.5 1200,-6792.5 1194,-6792.5 1188,-6786.5 1188,-6780.5 1188,-6780.5 1188,-6751.5 1188,-6751.5 1188,-6745.5 1194,-6739.5 1200,-6739.5"/>
<text text-anchor="middle" x="1242.62" y="-6779.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">api</text>
</g>
<g id="clust65" class="cluster">
<title>cluster_src/stores</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1074.62,-6893.5C1074.62,-6893.5 1151.62,-6893.5 1151.62,-6893.5 1157.62,-6893.5 1163.62,-6899.5 1163.62,-6905.5 1163.62,-6905.5 1163.62,-6934.5 1163.62,-6934.5 1163.62,-6940.5 1157.62,-6946.5 1151.62,-6946.5 1151.62,-6946.5 1074.62,-6946.5 1074.62,-6946.5 1068.62,-6946.5 1062.62,-6940.5 1062.62,-6934.5 1062.62,-6934.5 1062.62,-6905.5 1062.62,-6905.5 1062.62,-6899.5 1068.62,-6893.5 1074.62,-6893.5"/>
<text text-anchor="middle" x="1113.12" y="-6933.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">stores</text>
</g>
<g id="clust66" class="cluster">
<title>cluster_src/styles</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M231.38,-6837.5C231.38,-6837.5 311.38,-6837.5 311.38,-6837.5 317.38,-6837.5 323.38,-6843.5 323.38,-6849.5 323.38,-6849.5 323.38,-6878.5 323.38,-6878.5 323.38,-6884.5 317.38,-6890.5 311.38,-6890.5 311.38,-6890.5 231.38,-6890.5 231.38,-6890.5 225.38,-6890.5 219.38,-6884.5 219.38,-6878.5 219.38,-6878.5 219.38,-6849.5 219.38,-6849.5 219.38,-6843.5 225.38,-6837.5 231.38,-6837.5"/>
<text text-anchor="middle" x="271.38" y="-6877.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">styles</text>
</g>
<g id="clust67" class="cluster">
<title>cluster_src/types</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1197.75,-6827.5C1197.75,-6827.5 1287.5,-6827.5 1287.5,-6827.5 1293.5,-6827.5 1299.5,-6833.5 1299.5,-6839.5 1299.5,-6839.5 1299.5,-6930.5 1299.5,-6930.5 1299.5,-6936.5 1293.5,-6942.5 1287.5,-6942.5 1287.5,-6942.5 1197.75,-6942.5 1197.75,-6942.5 1191.75,-6942.5 1185.75,-6936.5 1185.75,-6930.5 1185.75,-6930.5 1185.75,-6839.5 1185.75,-6839.5 1185.75,-6833.5 1191.75,-6827.5 1197.75,-6827.5"/>
<text text-anchor="middle" x="1242.62" y="-6929.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">types</text>
</g>
<g id="clust68" class="cluster">
<title>cluster_src/utils</title>
<path fill="#ffffff" stroke="black" stroke-width="2" d="M1066.38,-6954.5C1066.38,-6954.5 1159.88,-6954.5 1159.88,-6954.5 1165.88,-6954.5 1171.88,-6960.5 1171.88,-6966.5 1171.88,-6966.5 1171.88,-6995.5 1171.88,-6995.5 1171.88,-7001.5 1165.88,-7007.5 1159.88,-7007.5 1159.88,-7007.5 1066.38,-7007.5 1066.38,-7007.5 1060.38,-7007.5 1054.38,-7001.5 1054.38,-6995.5 1054.38,-6995.5 1054.38,-6966.5 1054.38,-6966.5 1054.38,-6960.5 1060.38,-6954.5 1066.38,-6954.5"/>
<text text-anchor="middle" x="1113.12" y="-6994.95" font-family="Helvetica,sans-Serif" font-weight="bold" font-size="9.00">utils</text>
</g>
<!-- ../../../../core/models/Node -->
<g id="node1" class="node">
<title>../../../../core/models/Node</title>
<g id="a_node1"><a xlink:title="Node">
<path fill="#ffffcc" stroke="black" d="M987.83,-1495.75C987.83,-1495.75 946.17,-1495.75 946.17,-1495.75 943.08,-1495.75 940,-1492.66 940,-1489.58 940,-1489.58 940,-1483.41 940,-1483.41 940,-1480.33 943.08,-1477.25 946.17,-1477.25 946.17,-1477.25 987.83,-1477.25 987.83,-1477.25 990.92,-1477.25 994,-1480.33 994,-1483.41 994,-1483.41 994,-1489.58 994,-1489.58 994,-1492.66 990.92,-1495.75 987.83,-1495.75"/>
<text text-anchor="start" x="955.75" y="-1483.2" font-family="Helvetica,sans-Serif" font-size="9.00">Node</text>
</a>
</g>
</g>
<!-- ../../../contexts/LLMContext -->
<g id="node2" class="node">
<title>../../../contexts/LLMContext</title>
<g id="a_node2"><a xlink:title="LLMContext">
<path fill="#ffffcc" stroke="black" d="M993.58,-1610.75C993.58,-1610.75 940.42,-1610.75 940.42,-1610.75 937.33,-1610.75 934.25,-1607.66 934.25,-1604.58 934.25,-1604.58 934.25,-1598.41 934.25,-1598.41 934.25,-1595.33 937.33,-1592.25 940.42,-1592.25 940.42,-1592.25 993.58,-1592.25 993.58,-1592.25 996.67,-1592.25 999.75,-1595.33 999.75,-1598.41 999.75,-1598.41 999.75,-1604.58 999.75,-1604.58 999.75,-1607.66 996.67,-1610.75 993.58,-1610.75"/>
<text text-anchor="start" x="942.25" y="-1598.2" font-family="Helvetica,sans-Serif" font-size="9.00">LLMContext</text>
</a>
</g>
</g>
<!-- ../../../contexts/MindMapContext -->
<g id="node3" class="node">
<title>../../../contexts/MindMapContext</title>
<g id="a_node3"><a xlink:title="MindMapContext">
<path fill="#ffffcc" stroke="black" d="M1003.71,-1641.75C1003.71,-1641.75 930.29,-1641.75 930.29,-1641.75 927.21,-1641.75 924.12,-1638.66 924.12,-1635.58 924.12,-1635.58 924.12,-1629.41 924.12,-1629.41 924.12,-1626.33 927.21,-1623.25 930.29,-1623.25 930.29,-1623.25 1003.71,-1623.25 1003.71,-1623.25 1006.79,-1623.25 1009.88,-1626.33 1009.88,-1629.41 1009.88,-1629.41 1009.88,-1635.58 1009.88,-1635.58 1009.88,-1638.66 1006.79,-1641.75 1003.71,-1641.75"/>
<text text-anchor="start" x="932.12" y="-1629.2" font-family="Helvetica,sans-Serif" font-size="9.00">MindMapContext</text>
</a>
</g>
</g>
<!-- ../../services/api/GovernanceLLM -->
<g id="node4" class="node">
<title>../../services/api/GovernanceLLM</title>
<g id="a_node4"><a xlink:title="GovernanceLLM">
<path fill="#ffffcc" stroke="black" d="M1002.96,-1383.75C1002.96,-1383.75 931.04,-1383.75 931.04,-1383.75 927.96,-1383.75 924.88,-1380.66 924.88,-1377.58 924.88,-1377.58 924.88,-1371.41 924.88,-1371.41 924.88,-1368.33 927.96,-1365.25 931.04,-1365.25 931.04,-1365.25 1002.96,-1365.25 1002.96,-1365.25 1006.04,-1365.25 1009.12,-1368.33 1009.12,-1371.41 1009.12,-1371.41 1009.12,-1377.58 1009.12,-1377.58 1009.12,-1380.66 1006.04,-1383.75 1002.96,-1383.75"/>
<text text-anchor="start" x="932.88" y="-1371.2" font-family="Helvetica,sans-Serif" font-size="9.00">GovernanceLLM</text>
</a>
</g>
</g>
<!-- ./ConnectionRenderer -->
<g id="node5" class="node">
<title>./ConnectionRenderer</title>
<g id="a_node5"><a xlink:title="ConnectionRenderer">
<path fill="#ffffcc" stroke="black" d="M686.08,-497.75C686.08,-497.75 596.17,-497.75 596.17,-497.75 593.08,-497.75 590,-494.66 590,-491.58 590,-491.58 590,-485.41 590,-485.41 590,-482.33 593.08,-479.25 596.17,-479.25 596.17,-479.25 686.08,-479.25 686.08,-479.25 689.17,-479.25 692.25,-482.33 692.25,-485.41 692.25,-485.41 692.25,-491.58 692.25,-491.58 692.25,-494.66 689.17,-497.75 686.08,-497.75"/>
<text text-anchor="start" x="598" y="-485.2" font-family="Helvetica,sans-Serif" font-size="9.00">ConnectionRenderer</text>
</a>
</g>
</g>
<!-- ./NodeRenderer -->
<g id="node6" class="node">
<title>./NodeRenderer</title>
<g id="a_node6"><a xlink:title="NodeRenderer">
<path fill="#ffffcc" stroke="black" d="M673.71,-553.75C673.71,-553.75 608.54,-553.75 608.54,-553.75 605.46,-553.75 602.38,-550.66 602.38,-547.58 602.38,-547.58 602.38,-541.41 602.38,-541.41 602.38,-538.33 605.46,-535.25 608.54,-535.25 608.54,-535.25 673.71,-535.25 673.71,-535.25 676.79,-535.25 679.88,-538.33 679.88,-541.41 679.88,-541.41 679.88,-547.58 679.88,-547.58 679.88,-550.66 676.79,-553.75 673.71,-553.75"/>
<text text-anchor="start" x="610.38" y="-541.2" font-family="Helvetica,sans-Serif" font-size="9.00">NodeRenderer</text>
</a>
</g>
</g>
<!-- ./StartupDialog.css -->
<g id="node7" class="node">
<title>./StartupDialog.css</title>
<g id="a_node7"><a xlink:title="StartupDialog.css">
<path fill="#ffffcc" stroke="black" d="M838.83,-1225.75C838.83,-1225.75 763.17,-1225.75 763.17,-1225.75 760.08,-1225.75 757,-1222.66 757,-1219.58 757,-1219.58 757,-1213.41 757,-1213.41 757,-1210.33 760.08,-1207.25 763.17,-1207.25 763.17,-1207.25 838.83,-1207.25 838.83,-1207.25 841.92,-1207.25 845,-1210.33 845,-1213.41 845,-1213.41 845,-1219.58 845,-1219.58 845,-1222.66 841.92,-1225.75 838.83,-1225.75"/>
<text text-anchor="start" x="765" y="-1213.2" font-family="Helvetica,sans-Serif" font-size="9.00">StartupDialog.css</text>
</a>
</g>
</g>
<!-- ./components/Canvas/MindMapCanvas -->
<g id="node8" class="node">
<title>./components/Canvas/MindMapCanvas</title>
<g id="a_node8"><a xlink:title="MindMapCanvas">
<path fill="#ffffcc" stroke="black" d="M498.83,-387.75C498.83,-387.75 425.42,-387.75 425.42,-387.75 422.33,-387.75 419.25,-384.66 419.25,-381.58 419.25,-381.58 419.25,-375.41 419.25,-375.41 419.25,-372.33 422.33,-369.25 425.42,-369.25 425.42,-369.25 498.83,-369.25 498.83,-369.25 501.92,-369.25 505,-372.33 505,-375.41 505,-375.41 505,-381.58 505,-381.58 505,-384.66 501.92,-387.75 498.83,-387.75"/>
<text text-anchor="start" x="427.25" y="-375.2" font-family="Helvetica,sans-Serif" font-size="9.00">MindMapCanvas</text>
</a>
</g>
</g>
<!-- node_modules/@mui/icons&#45;material -->
<g id="node9" class="node">
<title>node_modules/@mui/icons&#45;material</title>
<g id="a_node9"><a xlink:href="https://www.npmjs.com/package/@mui/icons-material" xlink:title="icons&#45;material">
<polygon fill="#c40b0a" fill-opacity="0.101961" stroke="black" points="1150,-1870.75 1078.31,-1870.75 1076.25,-1868.69 1076.25,-1852.25 1147.94,-1852.25 1150,-1854.3 1150,-1870.75"/>
<polyline fill="none" stroke="black" points="1147.94,-1868.69 1076.25,-1868.69"/>
<polyline fill="none" stroke="black" points="1147.94,-1868.69 1147.94,-1852.25"/>
<polyline fill="none" stroke="black" points="1147.94,-1868.69 1150,-1870.75"/>
<text text-anchor="start" x="1084.25" y="-1858.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="#c40b0a">icons&#45;material</text>
</a>
</g>
</g>
<!-- node_modules/@mui/material -->
<g id="node10" class="node">
<title>node_modules/@mui/material</title>
<g id="a_node10"><a xlink:href="https://www.npmjs.com/package/@mui/material" xlink:title="material">
<polygon fill="#c40b0a" fill-opacity="0.101961" stroke="black" points="1140.12,-1839.75 1088.18,-1839.75 1086.12,-1837.69 1086.12,-1821.25 1138.07,-1821.25 1140.12,-1823.3 1140.12,-1839.75"/>
<polyline fill="none" stroke="black" points="1138.07,-1837.69 1086.12,-1837.69"/>
<polyline fill="none" stroke="black" points="1138.07,-1837.69 1138.07,-1821.25"/>
<polyline fill="none" stroke="black" points="1138.07,-1837.69 1140.12,-1839.75"/>
<text text-anchor="start" x="1096.62" y="-1827.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="#c40b0a">material</text>
</a>
</g>
</g>
<!-- node_modules/js&#45;yaml -->
<g id="node11" class="node">
<title>node_modules/js&#45;yaml</title>
<g id="a_node11"><a xlink:href="https://www.npmjs.com/package/js-yaml" xlink:title="js&#45;yaml">
<polygon fill="#c40b0a" fill-opacity="0.101961" stroke="black" points="1140.12,-1743.75 1088.18,-1743.75 1086.12,-1741.69 1086.12,-1725.25 1138.07,-1725.25 1140.12,-1727.3 1140.12,-1743.75"/>
<polyline fill="none" stroke="black" points="1138.07,-1741.69 1086.12,-1741.69"/>
<polyline fill="none" stroke="black" points="1138.07,-1741.69 1138.07,-1725.25"/>
<polyline fill="none" stroke="black" points="1138.07,-1741.69 1140.12,-1743.75"/>
<text text-anchor="start" x="1098.5" y="-1731.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="#c40b0a">js&#45;yaml</text>
</a>
</g>
</g>
<!-- node_modules/konva -->
<g id="node12" class="node">
<title>node_modules/konva</title>
<g id="a_node12"><a xlink:href="https://www.npmjs.com/package/konva" xlink:title="konva">
<polygon fill="#c40b0a" fill-opacity="0.101961" stroke="black" points="1140.12,-1954.75 1088.18,-1954.75 1086.12,-1952.69 1086.12,-1936.25 1138.07,-1936.25 1140.12,-1938.3 1140.12,-1954.75"/>
<polyline fill="none" stroke="black" points="1138.07,-1952.69 1086.12,-1952.69"/>
<polyline fill="none" stroke="black" points="1138.07,-1952.69 1138.07,-1936.25"/>
<polyline fill="none" stroke="black" points="1138.07,-1952.69 1140.12,-1954.75"/>
<text text-anchor="start" x="1100.75" y="-1942.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="#c40b0a">konva</text>
</a>
</g>
</g>
<!-- node_modules/react -->
<g id="node13" class="node">
<title>node_modules/react</title>
<g id="a_node13"><a xlink:href="https://www.npmjs.com/package/react" xlink:title="react">
<polygon fill="#c40b0a" fill-opacity="0.101961" stroke="black" points="1140.12,-1923.75 1088.18,-1923.75 1086.12,-1921.69 1086.12,-1905.25 1138.07,-1905.25 1140.12,-1907.3 1140.12,-1923.75"/>
<polyline fill="none" stroke="black" points="1138.07,-1921.69 1086.12,-1921.69"/>
<polyline fill="none" stroke="black" points="1138.07,-1921.69 1138.07,-1905.25"/>
<polyline fill="none" stroke="black" points="1138.07,-1921.69 1140.12,-1923.75"/>
<text text-anchor="start" x="1103" y="-1911.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="#c40b0a">react</text>
</a>
</g>
</g>
<!-- node_modules/react&#45;dom -->
<g id="node14" class="node">
<title>node_modules/react&#45;dom</title>
<g id="a_node14"><a xlink:href="https://www.npmjs.com/package/react-dom" xlink:title="react&#45;dom">
<polygon fill="#c40b0a" fill-opacity="0.101961" stroke="black" points="1141.75,-1805.75 1086.56,-1805.75 1084.5,-1803.69 1084.5,-1787.25 1139.69,-1787.25 1141.75,-1789.3 1141.75,-1805.75"/>
<polyline fill="none" stroke="black" points="1139.69,-1803.69 1084.5,-1803.69"/>
<polyline fill="none" stroke="black" points="1139.69,-1803.69 1139.69,-1787.25"/>
<polyline fill="none" stroke="black" points="1139.69,-1803.69 1141.75,-1805.75"/>
<text text-anchor="start" x="1092.5" y="-1793.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="#c40b0a">react&#45;dom</text>
</a>
</g>
</g>
<!-- node_modules/react&#45;draggable -->
<g id="node15" class="node">
<title>node_modules/react&#45;draggable</title>
<g id="a_node15"><a xlink:href="https://www.npmjs.com/package/react-draggable" xlink:title="react&#45;draggable">
<polygon fill="#c40b0a" fill-opacity="0.101961" stroke="black" points="1153.75,-1985.75 1074.56,-1985.75 1072.5,-1983.69 1072.5,-1967.25 1151.69,-1967.25 1153.75,-1969.3 1153.75,-1985.75"/>
<polyline fill="none" stroke="black" points="1151.69,-1983.69 1072.5,-1983.69"/>
<polyline fill="none" stroke="black" points="1151.69,-1983.69 1151.69,-1967.25"/>
<polyline fill="none" stroke="black" points="1151.69,-1983.69 1153.75,-1985.75"/>
<text text-anchor="start" x="1080.5" y="-1973.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="#c40b0a">react&#45;draggable</text>
</a>
</g>
</g>
<!-- node_modules/react&#45;konva -->
<g id="node16" class="node">
<title>node_modules/react&#45;konva</title>
<g id="a_node16"><a xlink:href="https://www.npmjs.com/package/react-konva" xlink:title="react&#45;konva">
<polygon fill="#c40b0a" fill-opacity="0.101961" stroke="black" points="1145.12,-2047.75 1083.18,-2047.75 1081.12,-2045.69 1081.12,-2029.25 1143.07,-2029.25 1145.12,-2031.3 1145.12,-2047.75"/>
<polyline fill="none" stroke="black" points="1143.07,-2045.69 1081.12,-2045.69"/>
<polyline fill="none" stroke="black" points="1143.07,-2045.69 1143.07,-2029.25"/>
<polyline fill="none" stroke="black" points="1143.07,-2045.69 1145.12,-2047.75"/>
<text text-anchor="start" x="1089.12" y="-2035.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="#c40b0a">react&#45;konva</text>
</a>
</g>
</g>
<!-- node_modules/react&#45;rnd -->
<g id="node17" class="node">
<title>node_modules/react&#45;rnd</title>
<g id="a_node17"><a xlink:href="https://www.npmjs.com/package/react-rnd" xlink:title="react&#45;rnd">
<polygon fill="#c40b0a" fill-opacity="0.101961" stroke="black" points="1140.12,-1774.75 1088.18,-1774.75 1086.12,-1772.69 1086.12,-1756.25 1138.07,-1756.25 1140.12,-1758.3 1140.12,-1774.75"/>
<polyline fill="none" stroke="black" points="1138.07,-1772.69 1086.12,-1772.69"/>
<polyline fill="none" stroke="black" points="1138.07,-1772.69 1138.07,-1756.25"/>
<polyline fill="none" stroke="black" points="1138.07,-1772.69 1140.12,-1774.75"/>
<text text-anchor="start" x="1094.75" y="-1762.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="#c40b0a">react&#45;rnd</text>
</a>
</g>
</g>
<!-- node_modules/react&#45;router&#45;dom -->
<g id="node18" class="node">
<title>node_modules/react&#45;router&#45;dom</title>
<g id="a_node18"><a xlink:href="https://www.npmjs.com/package/react-router-dom" xlink:title="react&#45;router&#45;dom">
<polygon fill="#c40b0a" fill-opacity="0.101961" stroke="black" points="1155.25,-1712.75 1073.06,-1712.75 1071,-1710.69 1071,-1694.25 1153.19,-1694.25 1155.25,-1696.3 1155.25,-1712.75"/>
<polyline fill="none" stroke="black" points="1153.19,-1710.69 1071,-1710.69"/>
<polyline fill="none" stroke="black" points="1153.19,-1710.69 1153.19,-1694.25"/>
<polyline fill="none" stroke="black" points="1153.19,-1710.69 1155.25,-1712.75"/>
<text text-anchor="start" x="1079" y="-1700.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="#c40b0a">react&#45;router&#45;dom</text>
</a>
</g>
</g>
<!-- node_modules/uuid -->
<g id="node19" class="node">
<title>node_modules/uuid</title>
<g id="a_node19"><a xlink:href="https://www.npmjs.com/package/uuid" xlink:title="uuid">
<polygon fill="#c40b0a" fill-opacity="0.101961" stroke="black" points="1140.12,-2016.75 1088.18,-2016.75 1086.12,-2014.69 1086.12,-1998.25 1138.07,-1998.25 1140.12,-2000.3 1140.12,-2016.75"/>
<polyline fill="none" stroke="black" points="1138.07,-2014.69 1086.12,-2014.69"/>
<polyline fill="none" stroke="black" points="1138.07,-2014.69 1138.07,-1998.25"/>
<polyline fill="none" stroke="black" points="1138.07,-2014.69 1140.12,-2016.75"/>
<text text-anchor="start" x="1104.12" y="-2004.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="#c40b0a">uuid</text>
</a>
</g>
</g>
<!-- node_modules/vite -->
<g id="node20" class="node">
<title>node_modules/vite</title>
<g id="a_node20"><a xlink:href="https://www.npmjs.com/package/vite" xlink:title="vite">
<polygon fill="#c40b0a" fill-opacity="0.101961" stroke="black" points="1140.12,-1681.75 1088.18,-1681.75 1086.12,-1679.69 1086.12,-1663.25 1138.07,-1663.25 1140.12,-1665.3 1140.12,-1681.75"/>
<polyline fill="none" stroke="black" points="1138.07,-1679.69 1086.12,-1679.69"/>
<polyline fill="none" stroke="black" points="1138.07,-1679.69 1138.07,-1663.25"/>
<polyline fill="none" stroke="black" points="1138.07,-1679.69 1140.12,-1681.75"/>
<text text-anchor="start" x="1106" y="-1669.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="#c40b0a">vite</text>
</a>
</g>
</g>
<!-- node_modules/zustand -->
<g id="node21" class="node">
<title>node_modules/zustand</title>
<g id="a_node21"><a xlink:href="https://www.npmjs.com/package/zustand" xlink:title="zustand">
<polygon fill="#c40b0a" fill-opacity="0.101961" stroke="black" points="1140.12,-2078.75 1088.18,-2078.75 1086.12,-2076.69 1086.12,-2060.25 1138.07,-2060.25 1140.12,-2062.3 1140.12,-2078.75"/>
<polyline fill="none" stroke="black" points="1138.07,-2076.69 1086.12,-2076.69"/>
<polyline fill="none" stroke="black" points="1138.07,-2076.69 1138.07,-2060.25"/>
<polyline fill="none" stroke="black" points="1138.07,-2076.69 1140.12,-2078.75"/>
<text text-anchor="start" x="1097" y="-2066.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="#c40b0a">zustand</text>
</a>
</g>
</g>
<!-- src/App.css -->
<g id="node22" class="node">
<title>src/App.css</title>
<g id="a_node22"><a xlink:href="src/App.css" xlink:title="App.css">
<path fill="#ffffcc" stroke="black" d="M292.21,-6446.75C292.21,-6446.75 250.54,-6446.75 250.54,-6446.75 247.46,-6446.75 244.38,-6443.66 244.38,-6440.58 244.38,-6440.58 244.38,-6434.41 244.38,-6434.41 244.38,-6431.33 247.46,-6428.25 250.54,-6428.25 250.54,-6428.25 292.21,-6428.25 292.21,-6428.25 295.29,-6428.25 298.38,-6431.33 298.38,-6434.41 298.38,-6434.41 298.38,-6440.58 298.38,-6440.58 298.38,-6443.66 295.29,-6446.75 292.21,-6446.75"/>
<text text-anchor="start" x="255.25" y="-6434.2" font-family="Helvetica,sans-Serif" font-size="9.00">App.css</text>
</a>
</g>
</g>
<!-- src/App.tsx -->
<g id="node23" class="node">
<title>src/App.tsx</title>
<g id="a_node23"><a xlink:href="src/App.tsx" xlink:title="App.tsx">
<path fill="#bbfeff" stroke="black" d="M161.21,-6446.75C161.21,-6446.75 119.54,-6446.75 119.54,-6446.75 116.46,-6446.75 113.38,-6443.66 113.38,-6440.58 113.38,-6440.58 113.38,-6434.41 113.38,-6434.41 113.38,-6431.33 116.46,-6428.25 119.54,-6428.25 119.54,-6428.25 161.21,-6428.25 161.21,-6428.25 164.29,-6428.25 167.38,-6431.33 167.38,-6434.41 167.38,-6434.41 167.38,-6440.58 167.38,-6440.58 167.38,-6443.66 164.29,-6446.75 161.21,-6446.75"/>
<text text-anchor="start" x="125.38" y="-6434.2" font-family="Helvetica,sans-Serif" font-size="9.00">App.tsx</text>
</a>
</g>
</g>
<!-- src/App.tsx&#45;&gt;node_modules/react -->
<g id="edge7" class="edge">
<title>src/App.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M153.18,-6428.02C163.74,-6418.72 178.47,-6403.51 184.75,-6386.5 192.53,-6365.42 192.58,-3173.95 193.25,-3151.5 202.54,-2839.57 126.15,-561.79 353,-347.5 408.73,-294.85 989.5,-298.58 1043,-353.5 1057.99,-368.89 1037.57,-1883.73 1051,-1900.5 1057.69,-1908.85 1068.26,-1912.85 1078.74,-1914.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.21,-1916.66 1084.43,-1915.31 1078.73,-1912.49 1078.21,-1916.66"/>
</g>
<!-- src/App.tsx&#45;&gt;node_modules/react&#45;router&#45;dom -->
<g id="edge8" class="edge">
<title>src/App.tsx&#45;&gt;node_modules/react&#45;router&#45;dom</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M153.19,-6428.02C163.74,-6418.73 178.48,-6403.51 184.75,-6386.5 191.99,-6366.85 181.84,-373.05 193.25,-355.5 300.12,-191.08 903.98,20.81 1043,-117.5 1058.46,-132.87 1037.63,-1670.27 1051,-1687.5 1054.38,-1691.84 1058.78,-1695.1 1063.67,-1697.53"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1062.85,-1699.47 1069.2,-1699.78 1064.44,-1695.58 1062.85,-1699.47"/>
</g>
<!-- src/App.tsx&#45;&gt;src/App.css -->
<g id="edge1" class="edge">
<title>src/App.tsx&#45;&gt;src/App.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M167.79,-6437.5C187.27,-6437.5 213.97,-6437.5 235.24,-6437.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="234.97,-6439.6 240.97,-6437.5 234.97,-6435.4 234.97,-6439.6"/>
</g>
<!-- src/components/ChatFork/ChatForkView.tsx -->
<g id="node24" class="node">
<title>src/components/ChatFork/ChatForkView.tsx</title>
<g id="a_node24"><a xlink:href="src/components/ChatFork/ChatForkView.tsx" xlink:title="ChatForkView.tsx">
<path fill="#bbfeff" stroke="black" d="M678.21,-6314.75C678.21,-6314.75 604.04,-6314.75 604.04,-6314.75 600.96,-6314.75 597.88,-6311.66 597.88,-6308.58 597.88,-6308.58 597.88,-6302.41 597.88,-6302.41 597.88,-6299.33 600.96,-6296.25 604.04,-6296.25 604.04,-6296.25 678.21,-6296.25 678.21,-6296.25 681.29,-6296.25 684.38,-6299.33 684.38,-6302.41 684.38,-6302.41 684.38,-6308.58 684.38,-6308.58 684.38,-6311.66 681.29,-6314.75 678.21,-6314.75"/>
<text text-anchor="start" x="605.88" y="-6302.2" font-family="Helvetica,sans-Serif" font-size="9.00">ChatForkView.tsx</text>
</a>
</g>
</g>
<!-- src/App.tsx&#45;&gt;src/components/ChatFork/ChatForkView.tsx -->
<g id="edge2" class="edge">
<title>src/App.tsx&#45;&gt;src/components/ChatFork/ChatForkView.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M167.56,-6441.72C174.2,-6444.08 180.62,-6447.76 184.75,-6453.5 202.3,-6477.85 171.69,-6702.61 193.25,-6723.5 308.79,-6835.46 439.08,-6834.81 555.25,-6723.5 578.54,-6701.18 558.74,-6464.5 567.62,-6433.5 580.07,-6390.04 609.96,-6345.49 627.23,-6322.13"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="628.8,-6323.53 630.73,-6317.47 625.45,-6321 628.8,-6323.53"/>
</g>
<!-- src/components/MindMap/components/Dialogs/index.ts -->
<g id="node25" class="node">
<title>src/components/MindMap/components/Dialogs/index.ts</title>
<g id="a_node25"><a xlink:href="src/components/MindMap/components/Dialogs/index.ts" xlink:title="index.ts">
<path fill="#ddfeff" stroke="black" d="M661.96,-5894.75C661.96,-5894.75 620.29,-5894.75 620.29,-5894.75 617.21,-5894.75 614.12,-5891.66 614.12,-5888.58 614.12,-5888.58 614.12,-5882.41 614.12,-5882.41 614.12,-5879.33 617.21,-5876.25 620.29,-5876.25 620.29,-5876.25 661.96,-5876.25 661.96,-5876.25 665.04,-5876.25 668.12,-5879.33 668.12,-5882.41 668.12,-5882.41 668.12,-5888.58 668.12,-5888.58 668.12,-5891.66 665.04,-5894.75 661.96,-5894.75"/>
<text text-anchor="start" x="625.38" y="-5882.2" font-family="Helvetica,sans-Serif" font-size="9.00">index.ts</text>
</a>
</g>
</g>
<!-- src/App.tsx&#45;&gt;src/components/MindMap/components/Dialogs/index.ts -->
<g id="edge3" class="edge">
<title>src/App.tsx&#45;&gt;src/components/MindMap/components/Dialogs/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M167.44,-6444.28C173.48,-6446.59 179.64,-6449.61 184.75,-6453.5 286.48,-6530.73 239.27,-6628.34 353,-6686.5 393.02,-6706.96 523.02,-6717.82 555.25,-6686.5 572.47,-6669.76 564.96,-6278.36 567.62,-6254.5 582.85,-6118.28 621.86,-5957.33 635.41,-5903.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="637.43,-5904.36 636.88,-5898.03 633.36,-5903.32 637.43,-5904.36"/>
</g>
<!-- src/components/MindMap/core/state/MindMapStore.ts -->
<g id="node26" class="node">
<title>src/components/MindMap/core/state/MindMapStore.ts</title>
<g id="a_node26"><a xlink:href="src/components/MindMap/core/state/MindMapStore.ts" xlink:title="MindMapStore.ts">
<path fill="#ddfeff" stroke="black" d="M1003.33,-4844.75C1003.33,-4844.75 930.67,-4844.75 930.67,-4844.75 927.58,-4844.75 924.5,-4841.66 924.5,-4838.58 924.5,-4838.58 924.5,-4832.41 924.5,-4832.41 924.5,-4829.33 927.58,-4826.25 930.67,-4826.25 930.67,-4826.25 1003.33,-4826.25 1003.33,-4826.25 1006.42,-4826.25 1009.5,-4829.33 1009.5,-4832.41 1009.5,-4832.41 1009.5,-4838.58 1009.5,-4838.58 1009.5,-4841.66 1006.42,-4844.75 1003.33,-4844.75"/>
<text text-anchor="start" x="932.5" y="-4832.2" font-family="Helvetica,sans-Serif" font-size="9.00">MindMapStore.ts</text>
</a>
</g>
</g>
<!-- src/App.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge4" class="edge">
<title>src/App.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M167.58,-6441.71C174.22,-6444.07 180.63,-6447.76 184.75,-6453.5 205.16,-6481.95 171,-6741.46 193.25,-6768.5 232.02,-6815.59 400.12,-6828.5 461.12,-6828.5 461.12,-6828.5 461.12,-6828.5 642.12,-6828.5 766.4,-6828.5 821.11,-6794.26 883,-6686.5 895.47,-6664.78 878.93,-4905.44 891,-4883.5 899.63,-4867.81 915.8,-4856.49 930.92,-4848.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="931.44,-4850.88 935.96,-4846.41 929.64,-4847.09 931.44,-4850.88"/>
</g>
<!-- src/components/OptimizedMindMap_Modular.tsx -->
<g id="node27" class="node">
<title>src/components/OptimizedMindMap_Modular.tsx</title>
<g id="a_node27"><a xlink:href="src/components/OptimizedMindMap_Modular.tsx" xlink:title="OptimizedMindMap_Modular.tsx">
<path fill="#bbfeff" stroke="black" d="M338.83,-6175.75C338.83,-6175.75 203.92,-6175.75 203.92,-6175.75 200.83,-6175.75 197.75,-6172.66 197.75,-6169.58 197.75,-6169.58 197.75,-6163.41 197.75,-6163.41 197.75,-6160.33 200.83,-6157.25 203.92,-6157.25 203.92,-6157.25 338.83,-6157.25 338.83,-6157.25 341.92,-6157.25 345,-6160.33 345,-6163.41 345,-6163.41 345,-6169.58 345,-6169.58 345,-6172.66 341.92,-6175.75 338.83,-6175.75"/>
<text text-anchor="start" x="205.75" y="-6163.2" font-family="Helvetica,sans-Serif" font-size="9.00">OptimizedMindMap_Modular.tsx</text>
</a>
</g>
</g>
<!-- src/App.tsx&#45;&gt;src/components/OptimizedMindMap_Modular.tsx -->
<g id="edge5" class="edge">
<title>src/App.tsx&#45;&gt;src/components/OptimizedMindMap_Modular.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M152.95,-6427.92C163.33,-6418.56 177.95,-6403.3 184.75,-6386.5 206.8,-6332 170.79,-6308.82 193.25,-6254.5 205.58,-6224.66 232.32,-6197.96 250.83,-6181.99"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="252.14,-6183.63 255.39,-6178.17 249.45,-6180.41 252.14,-6183.63"/>
</g>
<!-- src/styles/mui&#45;overrides.css -->
<g id="node28" class="node">
<title>src/styles/mui&#45;overrides.css</title>
<g id="a_node28"><a xlink:href="src/styles/mui-overrides.css" xlink:title="mui&#45;overrides.css">
<path fill="#ffffcc" stroke="black" d="M309.21,-6863.75C309.21,-6863.75 233.54,-6863.75 233.54,-6863.75 230.46,-6863.75 227.38,-6860.66 227.38,-6857.58 227.38,-6857.58 227.38,-6851.41 227.38,-6851.41 227.38,-6848.33 230.46,-6845.25 233.54,-6845.25 233.54,-6845.25 309.21,-6845.25 309.21,-6845.25 312.29,-6845.25 315.38,-6848.33 315.38,-6851.41 315.38,-6851.41 315.38,-6857.58 315.38,-6857.58 315.38,-6860.66 312.29,-6863.75 309.21,-6863.75"/>
<text text-anchor="start" x="235.38" y="-6851.2" font-family="Helvetica,sans-Serif" font-size="9.00">mui&#45;overrides.css</text>
</a>
</g>
</g>
<!-- src/App.tsx&#45;&gt;src/styles/mui&#45;overrides.css -->
<g id="edge6" class="edge">
<title>src/App.tsx&#45;&gt;src/styles/mui&#45;overrides.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M167.6,-6441.69C174.24,-6444.06 180.65,-6447.75 184.75,-6453.5 197.27,-6471.05 179.15,-6825.18 193.25,-6841.5 199.78,-6849.04 208.91,-6853.33 218.61,-6855.6"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="217.99,-6857.63 224.26,-6856.59 218.71,-6853.49 217.99,-6857.63"/>
</g>
<!-- src/components/ChatFork/ChatForkView.tsx&#45;&gt;node_modules/react -->
<g id="edge22" class="edge">
<title>src/components/ChatFork/ChatForkView.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M684.5,-6305.35C695.77,-6303.1 706.79,-6298.46 714,-6289.5 735.09,-6263.26 709.91,-1471.5 727,-1442.5 767.3,-1374.1 837.18,-1420.32 883,-1355.5 895.23,-1338.19 874.81,-1322.16 891,-1308.5 916.81,-1286.71 1018.95,-1284.77 1043,-1308.5 1054.71,-1320.05 1040.66,-1887.7 1051,-1900.5 1057.61,-1908.67 1067.93,-1912.67 1078.22,-1914.48"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1077.93,-1916.56 1084.15,-1915.25 1078.47,-1912.4 1077.93,-1916.56"/>
</g>
<!-- src/components/ChatFork/ChatFork.css -->
<g id="node29" class="node">
<title>src/components/ChatFork/ChatFork.css</title>
<g id="a_node29"><a xlink:href="src/components/ChatFork/ChatFork.css" xlink:title="ChatFork.css">
<path fill="#ffffcc" stroke="black" d="M829.46,-6325.75C829.46,-6325.75 772.54,-6325.75 772.54,-6325.75 769.46,-6325.75 766.38,-6322.66 766.38,-6319.58 766.38,-6319.58 766.38,-6313.41 766.38,-6313.41 766.38,-6310.33 769.46,-6307.25 772.54,-6307.25 772.54,-6307.25 829.46,-6307.25 829.46,-6307.25 832.54,-6307.25 835.62,-6310.33 835.62,-6313.41 835.62,-6313.41 835.62,-6319.58 835.62,-6319.58 835.62,-6322.66 832.54,-6325.75 829.46,-6325.75"/>
<text text-anchor="start" x="774.38" y="-6313.2" font-family="Helvetica,sans-Serif" font-size="9.00">ChatFork.css</text>
</a>
</g>
</g>
<!-- src/components/ChatFork/ChatForkView.tsx&#45;&gt;src/components/ChatFork/ChatFork.css -->
<g id="edge21" class="edge">
<title>src/components/ChatFork/ChatForkView.tsx&#45;&gt;src/components/ChatFork/ChatFork.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M684.85,-6308.47C707.24,-6310.03 734.6,-6311.94 757.03,-6313.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="756.84,-6315.59 762.97,-6313.92 757.13,-6311.4 756.84,-6315.59"/>
</g>
<!-- src/components/MindMap/core/adapters/ChatForkAdapter.ts -->
<g id="node35" class="node">
<title>src/components/MindMap/core/adapters/ChatForkAdapter.ts</title>
<g id="a_node35"><a xlink:href="src/components/MindMap/core/adapters/ChatForkAdapter.ts" xlink:title="ChatForkAdapter.ts">
<path fill="#ddfeff" stroke="black" d="M681.96,-4981.75C681.96,-4981.75 600.29,-4981.75 600.29,-4981.75 597.21,-4981.75 594.12,-4978.66 594.12,-4975.58 594.12,-4975.58 594.12,-4969.41 594.12,-4969.41 594.12,-4966.33 597.21,-4963.25 600.29,-4963.25 600.29,-4963.25 681.96,-4963.25 681.96,-4963.25 685.04,-4963.25 688.12,-4966.33 688.12,-4969.41 688.12,-4969.41 688.12,-4975.58 688.12,-4975.58 688.12,-4978.66 685.04,-4981.75 681.96,-4981.75"/>
<text text-anchor="start" x="602.12" y="-4969.2" font-family="Helvetica,sans-Serif" font-size="9.00">ChatForkAdapter.ts</text>
</a>
</g>
</g>
<!-- src/components/ChatFork/ChatForkView.tsx&#45;&gt;src/components/MindMap/core/adapters/ChatForkAdapter.ts -->
<g id="edge19" class="edge">
<title>src/components/ChatFork/ChatForkView.tsx&#45;&gt;src/components/MindMap/core/adapters/ChatForkAdapter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M597.65,-6306.18C582.33,-6303.8 567.42,-6297.64 561.44,-6283.5 554.44,-6266.97 554.44,-5008.02 561.44,-4991.5 565.72,-4981.37 574.69,-4975.62 585.12,-4972.54"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="585.39,-4974.63 590.78,-4971.27 584.47,-4970.53 585.39,-4974.63"/>
</g>
<!-- src/components/MindMap/core/state/ChatForkStore.ts -->
<g id="node36" class="node">
<title>src/components/MindMap/core/state/ChatForkStore.ts</title>
<g id="a_node36"><a xlink:href="src/components/MindMap/core/state/ChatForkStore.ts" xlink:title="ChatForkStore.ts">
<path fill="#ddfeff" stroke="black" d="M836.96,-4813.75C836.96,-4813.75 765.04,-4813.75 765.04,-4813.75 761.96,-4813.75 758.88,-4810.66 758.88,-4807.58 758.88,-4807.58 758.88,-4801.41 758.88,-4801.41 758.88,-4798.33 761.96,-4795.25 765.04,-4795.25 765.04,-4795.25 836.96,-4795.25 836.96,-4795.25 840.04,-4795.25 843.12,-4798.33 843.12,-4801.41 843.12,-4801.41 843.12,-4807.58 843.12,-4807.58 843.12,-4810.66 840.04,-4813.75 836.96,-4813.75"/>
<text text-anchor="start" x="766.88" y="-4801.2" font-family="Helvetica,sans-Serif" font-size="9.00">ChatForkStore.ts</text>
</a>
</g>
</g>
<!-- src/components/ChatFork/ChatForkView.tsx&#45;&gt;src/components/MindMap/core/state/ChatForkStore.ts -->
<g id="edge20" class="edge">
<title>src/components/ChatFork/ChatForkView.tsx&#45;&gt;src/components/MindMap/core/state/ChatForkStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M684.84,-6305.24C695.98,-6302.97 706.84,-6298.34 714,-6289.5 739.67,-6257.77 701.28,-4852.18 727,-4820.5 732.83,-4813.32 741.06,-4808.9 749.91,-4806.26"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="750.23,-4808.34 755.59,-4804.92 749.27,-4804.25 750.23,-4808.34"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog.tsx -->
<g id="node72" class="node">
<title>src/components/MindMap/components/Dialogs/NodeDialog.tsx</title>
<g id="a_node72"><a xlink:href="src/components/MindMap/components/Dialogs/NodeDialog.tsx" xlink:title="NodeDialog.tsx">
<path fill="#bbfeff" stroke="black" d="M834.33,-5740.75C834.33,-5740.75 767.67,-5740.75 767.67,-5740.75 764.58,-5740.75 761.5,-5737.66 761.5,-5734.58 761.5,-5734.58 761.5,-5728.41 761.5,-5728.41 761.5,-5725.33 764.58,-5722.25 767.67,-5722.25 767.67,-5722.25 834.33,-5722.25 834.33,-5722.25 837.42,-5722.25 840.5,-5725.33 840.5,-5728.41 840.5,-5728.41 840.5,-5734.58 840.5,-5734.58 840.5,-5737.66 837.42,-5740.75 834.33,-5740.75"/>
<text text-anchor="start" x="769.5" y="-5728.2" font-family="Helvetica,sans-Serif" font-size="9.00">NodeDialog.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Dialogs/index.ts&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog.tsx -->
<g id="edge146" class="edge">
<title>src/components/MindMap/components/Dialogs/index.ts&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M668.47,-5886.73C684,-5885.94 702.76,-5882.08 714,-5869.5 750.32,-5828.82 690.61,-5788.11 727,-5747.5 734.1,-5739.57 744.15,-5735.08 754.52,-5732.64"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="760.78,-5733.57 754.49,-5732.64 759.98,-5729.44 760.78,-5733.57"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx -->
<g id="node79" class="node">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx</title>
<g id="a_node79"><a xlink:href="src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx" xlink:title="NodeDialogContainer.tsx">
<path fill="#bbfeff" stroke="black" d="M854.21,-6016.75C854.21,-6016.75 747.79,-6016.75 747.79,-6016.75 744.71,-6016.75 741.62,-6013.66 741.62,-6010.58 741.62,-6010.58 741.62,-6004.41 741.62,-6004.41 741.62,-6001.33 744.71,-5998.25 747.79,-5998.25 747.79,-5998.25 854.21,-5998.25 854.21,-5998.25 857.29,-5998.25 860.38,-6001.33 860.38,-6004.41 860.38,-6004.41 860.38,-6010.58 860.38,-6010.58 860.38,-6013.66 857.29,-6016.75 854.21,-6016.75"/>
<text text-anchor="start" x="749.62" y="-6004.2" font-family="Helvetica,sans-Serif" font-size="9.00">NodeDialogContainer.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Dialogs/index.ts&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx -->
<g id="edge147" class="edge">
<title>src/components/MindMap/components/Dialogs/index.ts&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M668.57,-5883.2C683.94,-5883.39 702.49,-5886.52 714,-5898.5 742.91,-5928.59 698.64,-5960.87 727,-5991.5 729.53,-5994.23 732.42,-5996.56 735.55,-5998.53"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="739.53,-6003.07 735.25,-5998.37 741.53,-5999.38 739.53,-6003.07"/>
</g>
<!-- src/components/MindMap/components/Dialogs/ProjectManagementDialog.tsx -->
<g id="node87" class="node">
<title>src/components/MindMap/components/Dialogs/ProjectManagementDialog.tsx</title>
<g id="a_node87"><a xlink:href="src/components/MindMap/components/Dialogs/ProjectManagementDialog.tsx" xlink:title="ProjectManagementDialog.tsx">
<path fill="#bbfeff" stroke="black" d="M864.33,-5771.75C864.33,-5771.75 737.67,-5771.75 737.67,-5771.75 734.58,-5771.75 731.5,-5768.66 731.5,-5765.58 731.5,-5765.58 731.5,-5759.41 731.5,-5759.41 731.5,-5756.33 734.58,-5753.25 737.67,-5753.25 737.67,-5753.25 864.33,-5753.25 864.33,-5753.25 867.42,-5753.25 870.5,-5756.33 870.5,-5759.41 870.5,-5759.41 870.5,-5765.58 870.5,-5765.58 870.5,-5768.66 867.42,-5771.75 864.33,-5771.75"/>
<text text-anchor="start" x="739.5" y="-5759.2" font-family="Helvetica,sans-Serif" font-size="9.00">ProjectManagementDialog.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Dialogs/index.ts&#45;&gt;src/components/MindMap/components/Dialogs/ProjectManagementDialog.tsx -->
<g id="edge148" class="edge">
<title>src/components/MindMap/components/Dialogs/index.ts&#45;&gt;src/components/MindMap/components/Dialogs/ProjectManagementDialog.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M668.32,-5886.6C683.8,-5885.76 702.56,-5881.9 714,-5869.5 740.87,-5840.38 702.53,-5810 727.33,-5777.41"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="732.81,-5774.29 727.29,-5777.45 729.63,-5771.54 732.81,-5774.29"/>
</g>
<!-- src/governance/chat/index.ts -->
<g id="node89" class="node">
<title>src/governance/chat/index.ts</title>
<g id="a_node89"><a xlink:href="src/governance/chat/index.ts" xlink:title="index.ts">
<path fill="#ddfeff" stroke="black" d="M482.96,-6554.75C482.96,-6554.75 441.29,-6554.75 441.29,-6554.75 438.21,-6554.75 435.12,-6551.66 435.12,-6548.58 435.12,-6548.58 435.12,-6542.41 435.12,-6542.41 435.12,-6539.33 438.21,-6536.25 441.29,-6536.25 441.29,-6536.25 482.96,-6536.25 482.96,-6536.25 486.04,-6536.25 489.12,-6539.33 489.12,-6542.41 489.12,-6542.41 489.12,-6548.58 489.12,-6548.58 489.12,-6551.66 486.04,-6554.75 482.96,-6554.75"/>
<text text-anchor="start" x="446.38" y="-6542.2" font-family="Helvetica,sans-Serif" font-size="9.00">index.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Dialogs/index.ts&#45;&gt;src/governance/chat/index.ts -->
<g id="edge145" class="edge">
<title>src/components/MindMap/components/Dialogs/index.ts&#45;&gt;src/governance/chat/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M637.69,-5895.1C627.11,-5937.23 584.98,-6109.94 567.62,-6254.5 565.8,-6269.68 565.36,-6518.02 555.25,-6529.5 540.95,-6545.73 516.57,-6549.4 496.48,-6549.18"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="490.64,-6546.89 496.57,-6549.19 490.51,-6551.09 490.64,-6546.89"/>
</g>
<!-- src/components/MindMap/core/state/MindMapStore.ts&#45;&gt;node_modules/zustand -->
<g id="edge213" class="edge">
<title>src/components/MindMap/core/state/MindMapStore.ts&#45;&gt;node_modules/zustand</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M997.87,-4825.81C1014.29,-4818.78 1033.26,-4807.49 1043,-4790.5 1048.29,-4781.26 1050.78,-4035.13 1051,-4024.5 1067.8,-3218.95 1105.97,-2227.2 1111.46,-2086.42"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1113.56,-2086.61 1111.69,-2080.54 1109.36,-2086.45 1113.56,-2086.61"/>
</g>
<!-- src/components/MindMap/core/models/Connection.ts -->
<g id="node57" class="node">
<title>src/components/MindMap/core/models/Connection.ts</title>
<g id="a_node57"><a xlink:href="src/components/MindMap/core/models/Connection.ts" xlink:title="Connection.ts">
<path fill="#ddfeff" stroke="black" d="M1143.08,-4905.75C1143.08,-4905.75 1083.17,-4905.75 1083.17,-4905.75 1080.08,-4905.75 1077,-4902.66 1077,-4899.58 1077,-4899.58 1077,-4893.41 1077,-4893.41 1077,-4890.33 1080.08,-4887.25 1083.17,-4887.25 1083.17,-4887.25 1143.08,-4887.25 1143.08,-4887.25 1146.17,-4887.25 1149.25,-4890.33 1149.25,-4893.41 1149.25,-4893.41 1149.25,-4899.58 1149.25,-4899.58 1149.25,-4902.66 1146.17,-4905.75 1143.08,-4905.75"/>
<text text-anchor="start" x="1085" y="-4893.2" font-family="Helvetica,sans-Serif" font-size="9.00">Connection.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/core/state/MindMapStore.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts -->
<g id="edge210" class="edge">
<title>src/components/MindMap/core/state/MindMapStore.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M990.84,-4845.16C1015.29,-4855.51 1054.24,-4872 1081.38,-4883.48"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1080.26,-4885.29 1086.61,-4885.69 1081.9,-4881.42 1080.26,-4885.29"/>
</g>
<!-- src/components/MindMap/core/models/Node.ts -->
<g id="node58" class="node">
<title>src/components/MindMap/core/models/Node.ts</title>
<g id="a_node58"><a xlink:href="src/components/MindMap/core/models/Node.ts" xlink:title="Node.ts">
<path fill="#ddfeff" stroke="black" d="M1263.46,-4936.75C1263.46,-4936.75 1221.79,-4936.75 1221.79,-4936.75 1218.71,-4936.75 1215.62,-4933.66 1215.62,-4930.58 1215.62,-4930.58 1215.62,-4924.41 1215.62,-4924.41 1215.62,-4921.33 1218.71,-4918.25 1221.79,-4918.25 1221.79,-4918.25 1263.46,-4918.25 1263.46,-4918.25 1266.54,-4918.25 1269.62,-4921.33 1269.62,-4924.41 1269.62,-4924.41 1269.62,-4930.58 1269.62,-4930.58 1269.62,-4933.66 1266.54,-4936.75 1263.46,-4936.75"/>
<text text-anchor="start" x="1226.88" y="-4924.2" font-family="Helvetica,sans-Serif" font-size="9.00">Node.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/core/state/MindMapStore.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge211" class="edge">
<title>src/components/MindMap/core/state/MindMapStore.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M993.8,-4845.23C1010.62,-4852.97 1031.52,-4865.51 1043,-4883.5 1057.24,-4905.8 1031.07,-4925.09 1051,-4942.5 1072.21,-4961.03 1149.77,-4945.74 1177.75,-4942.5 1187.18,-4941.4 1197.24,-4939.43 1206.53,-4937.27"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1206.87,-4939.35 1212.2,-4935.88 1205.87,-4935.27 1206.87,-4939.35"/>
</g>
<!-- src/components/MindMap/layouts/types.ts -->
<g id="node93" class="node">
<title>src/components/MindMap/layouts/types.ts</title>
<g id="a_node93"><a xlink:href="src/components/MindMap/layouts/types.ts" xlink:title="types.ts">
<path fill="#ddfeff" stroke="black" d="M1263.46,-4295.75C1263.46,-4295.75 1221.79,-4295.75 1221.79,-4295.75 1218.71,-4295.75 1215.62,-4292.66 1215.62,-4289.58 1215.62,-4289.58 1215.62,-4283.41 1215.62,-4283.41 1215.62,-4280.33 1218.71,-4277.25 1221.79,-4277.25 1221.79,-4277.25 1263.46,-4277.25 1263.46,-4277.25 1266.54,-4277.25 1269.62,-4280.33 1269.62,-4283.41 1269.62,-4283.41 1269.62,-4289.58 1269.62,-4289.58 1269.62,-4292.66 1266.54,-4295.75 1263.46,-4295.75"/>
<text text-anchor="start" x="1227.25" y="-4283.2" font-family="Helvetica,sans-Serif" font-size="9.00">types.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/core/state/MindMapStore.ts&#45;&gt;src/components/MindMap/layouts/types.ts -->
<g id="edge209" class="edge">
<title>src/components/MindMap/core/state/MindMapStore.ts&#45;&gt;src/components/MindMap/layouts/types.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M997.14,-4825.77C1013.53,-4818.68 1032.71,-4807.34 1043,-4790.5 1057.04,-4767.5 1041.71,-4694.79 1051,-4669.5 1083.61,-4580.69 1135.31,-4583.04 1177.75,-4498.5 1211.61,-4431.04 1231.11,-4342.34 1238.35,-4304.67"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1240.34,-4305.5 1239.38,-4299.21 1236.21,-4304.72 1240.34,-4305.5"/>
</g>
<!-- src/components/MindMap/core/operations/LayoutEngine.ts -->
<g id="node107" class="node">
<title>src/components/MindMap/core/operations/LayoutEngine.ts</title>
<g id="a_node107"><a xlink:href="src/components/MindMap/core/operations/LayoutEngine.ts" xlink:title="LayoutEngine.ts">
<path fill="#ddfeff" stroke="black" d="M1001.83,-4912.75C1001.83,-4912.75 932.17,-4912.75 932.17,-4912.75 929.08,-4912.75 926,-4909.66 926,-4906.58 926,-4906.58 926,-4900.41 926,-4900.41 926,-4897.33 929.08,-4894.25 932.17,-4894.25 932.17,-4894.25 1001.83,-4894.25 1001.83,-4894.25 1004.92,-4894.25 1008,-4897.33 1008,-4900.41 1008,-4900.41 1008,-4906.58 1008,-4906.58 1008,-4909.66 1004.92,-4912.75 1001.83,-4912.75"/>
<text text-anchor="start" x="934" y="-4900.2" font-family="Helvetica,sans-Serif" font-size="9.00">LayoutEngine.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/core/state/MindMapStore.ts&#45;&gt;src/components/MindMap/core/operations/LayoutEngine.ts -->
<g id="edge212" class="edge">
<title>src/components/MindMap/core/state/MindMapStore.ts&#45;&gt;src/components/MindMap/core/operations/LayoutEngine.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M967,-4845.06C967,-4858.42 967,-4871.77 967,-4885.13"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="964.9,-4884.88 967,-4890.88 969.1,-4884.88 964.9,-4884.88"/>
</g>
<!-- src/components/MindMap/layouts/LayoutManager.ts -->
<g id="node113" class="node">
<title>src/components/MindMap/layouts/LayoutManager.ts</title>
<g id="a_node113"><a xlink:href="src/components/MindMap/layouts/LayoutManager.ts" xlink:title="LayoutManager.ts">
<path fill="#ddfeff" stroke="black" d="M839.21,-4298.75C839.21,-4298.75 762.79,-4298.75 762.79,-4298.75 759.71,-4298.75 756.62,-4295.66 756.62,-4292.58 756.62,-4292.58 756.62,-4286.41 756.62,-4286.41 756.62,-4283.33 759.71,-4280.25 762.79,-4280.25 762.79,-4280.25 839.21,-4280.25 839.21,-4280.25 842.29,-4280.25 845.38,-4283.33 845.38,-4286.41 845.38,-4286.41 845.38,-4292.58 845.38,-4292.58 845.38,-4295.66 842.29,-4298.75 839.21,-4298.75"/>
<text text-anchor="start" x="764.62" y="-4286.2" font-family="Helvetica,sans-Serif" font-size="9.00">LayoutManager.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/core/state/MindMapStore.ts&#45;&gt;src/components/MindMap/layouts/LayoutManager.ts -->
<g id="edge208" class="edge">
<title>src/components/MindMap/core/state/MindMapStore.ts&#45;&gt;src/components/MindMap/layouts/LayoutManager.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M936.17,-4825.78C919.77,-4818.75 900.8,-4807.46 891,-4790.5 878.27,-4768.46 896.3,-4354.19 883,-4332.5 874.57,-4318.75 860.14,-4309.16 845.77,-4302.57"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="846.97,-4300.8 840.62,-4300.4 845.33,-4304.67 846.97,-4300.8"/>
</g>
<!-- src/components/OptimizedMindMap_Modular.tsx&#45;&gt;node_modules/@mui/material -->
<g id="edge339" class="edge">
<title>src/components/OptimizedMindMap_Modular.tsx&#45;&gt;node_modules/@mui/material</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M340.73,-6156.83C342.26,-6155.52 343.69,-6154.08 345,-6152.5 357.52,-6137.35 341.84,-510.66 353,-494.5 407.64,-415.34 481.08,-478.74 555.25,-417.5 563.31,-410.84 562.03,-406.33 567.62,-397.5 637.32,-287.43 612.55,-213.72 727,-151.5 788.69,-117.95 993.23,-101.96 1043,-151.5 1059.4,-167.82 1036.4,-1799.55 1051,-1817.5 1057.61,-1825.62 1067.93,-1829.47 1078.22,-1831.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1077.96,-1833.19 1084.15,-1831.77 1078.42,-1829.01 1077.96,-1833.19"/>
</g>
<!-- src/components/OptimizedMindMap_Modular.tsx&#45;&gt;node_modules/react -->
<g id="edge340" class="edge">
<title>src/components/OptimizedMindMap_Modular.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M340.73,-6156.83C342.26,-6155.52 343.69,-6154.08 345,-6152.5 357.39,-6137.51 341.71,-567.33 353,-551.5 408.23,-474.05 492.12,-552.65 555.25,-481.5 572.45,-462.1 547.44,-440.77 567.62,-424.5 608.75,-391.34 1005.55,-387.25 1043,-424.5 1057.54,-438.95 1038.18,-1884.5 1051,-1900.5 1057.69,-1908.85 1068.26,-1912.85 1078.74,-1914.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.22,-1916.66 1084.43,-1915.31 1078.73,-1912.49 1078.22,-1916.66"/>
</g>
<!-- src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/ChatFork/ChatForkView.tsx -->
<g id="edge327" class="edge">
<title>src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/ChatFork/ChatForkView.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M324.32,-6176.21C333.84,-6177.79 343.7,-6179.3 353,-6180.5 375.37,-6183.37 538.91,-6181.95 555.25,-6197.5 585.14,-6225.93 539.69,-6259.13 567.62,-6289.5 573.39,-6295.76 581.01,-6299.86 589.13,-6302.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="588.47,-6304.49 594.81,-6303.97 589.53,-6300.42 588.47,-6304.49"/>
</g>
<!-- src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge336" class="edge">
<title>src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M279.55,-6176.1C292.28,-6192.3 320.56,-6224.63 353,-6238.5 394.46,-6256.22 523.14,-6222.85 555.25,-6254.5 576.23,-6275.18 545.84,-6366.66 567.62,-6386.5 619.44,-6433.68 833.31,-6435.92 883,-6386.5 897.8,-6371.77 880.93,-4901.78 891,-4883.5 899.64,-4867.81 915.81,-4856.5 930.93,-4848.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="931.45,-4850.88 935.96,-4846.41 929.64,-4847.09 931.45,-4850.88"/>
</g>
<!-- src/components/ChatFork/ChatForkContainer.tsx -->
<g id="node32" class="node">
<title>src/components/ChatFork/ChatForkContainer.tsx</title>
<g id="a_node32"><a xlink:href="src/components/ChatFork/ChatForkContainer.tsx" xlink:title="ChatForkContainer.tsx">
<path fill="#bbfeff" stroke="black" d="M318.58,-6354.75C318.58,-6354.75 224.17,-6354.75 224.17,-6354.75 221.08,-6354.75 218,-6351.66 218,-6348.58 218,-6348.58 218,-6342.41 218,-6342.41 218,-6339.33 221.08,-6336.25 224.17,-6336.25 224.17,-6336.25 318.58,-6336.25 318.58,-6336.25 321.67,-6336.25 324.75,-6339.33 324.75,-6342.41 324.75,-6342.41 324.75,-6348.58 324.75,-6348.58 324.75,-6351.66 321.67,-6354.75 318.58,-6354.75"/>
<text text-anchor="start" x="226" y="-6342.2" font-family="Helvetica,sans-Serif" font-size="9.00">ChatForkContainer.tsx</text>
</a>
</g>
</g>
<!-- src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/ChatFork/ChatForkContainer.tsx -->
<g id="edge326" class="edge">
<title>src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/ChatFork/ChatForkContainer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M257.71,-6176.08C237.56,-6191.23 200.98,-6222.86 189,-6260.5 185.63,-6271.08 185.63,-6299.91 189,-6310.5 192.35,-6321.01 200.18,-6328.38 209.74,-6333.55"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="208.66,-6335.36 214.99,-6335.97 210.42,-6331.55 208.66,-6335.36"/>
</g>
<!-- src/services/api/GovernanceLLM.ts -->
<g id="node33" class="node">
<title>src/services/api/GovernanceLLM.ts</title>
<g id="a_node33"><a xlink:href="src/services/api/GovernanceLLM.ts" xlink:title="GovernanceLLM.ts">
<path fill="#ddfeff" stroke="black" d="M1283.08,-6765.75C1283.08,-6765.75 1202.17,-6765.75 1202.17,-6765.75 1199.08,-6765.75 1196,-6762.66 1196,-6759.58 1196,-6759.58 1196,-6753.41 1196,-6753.41 1196,-6750.33 1199.08,-6747.25 1202.17,-6747.25 1202.17,-6747.25 1283.08,-6747.25 1283.08,-6747.25 1286.17,-6747.25 1289.25,-6750.33 1289.25,-6753.41 1289.25,-6753.41 1289.25,-6759.58 1289.25,-6759.58 1289.25,-6762.66 1286.17,-6765.75 1283.08,-6765.75"/>
<text text-anchor="start" x="1204" y="-6753.2" font-family="Helvetica,sans-Serif" font-size="9.00">GovernanceLLM.ts</text>
</a>
</g>
</g>
<!-- src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/services/api/GovernanceLLM.ts -->
<g id="edge325" class="edge">
<title>src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/services/api/GovernanceLLM.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M284.22,-6176.07C301.41,-6190.8 332.56,-6220.87 345,-6254.5 353.33,-6277.01 340.35,-6666.1 353,-6686.5 406.39,-6772.56 495.2,-6709.94 555.25,-6791.5 573.72,-6816.58 544.16,-6839.99 567.62,-6860.5 606.55,-6894.51 748.31,-6870.5 800,-6870.5 800,-6870.5 800,-6870.5 968,-6870.5 991.34,-6870.5 1159.87,-6873.51 1177.75,-6858.5 1189.64,-6848.51 1178.87,-6838.41 1185.75,-6824.5 1195.67,-6804.44 1212.53,-6785.07 1225.14,-6772.17"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1226.38,-6773.91 1229.15,-6768.18 1223.41,-6770.93 1226.38,-6773.91"/>
</g>
<!-- src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/MindMap/core/adapters/ChatForkAdapter.ts -->
<g id="edge334" class="edge">
<title>src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/MindMap/core/adapters/ChatForkAdapter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M339.73,-6156.93C341.6,-6155.6 343.36,-6154.13 345,-6152.5 358.14,-6139.38 339,-6123.68 353,-6111.5 369.98,-6096.72 539.65,-6116.72 555.25,-6100.5 571.46,-6083.63 564.56,-5281.68 567.62,-5258.5 581.29,-5155.14 619.05,-5035.36 633.96,-4990.59"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="635.89,-4991.44 635.81,-4985.08 631.91,-4990.1 635.89,-4991.44"/>
</g>
<!-- src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/MindMap/core/state/ChatForkStore.ts -->
<g id="edge335" class="edge">
<title>src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/MindMap/core/state/ChatForkStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M345.22,-6161.95C470.53,-6154.13 713.61,-6138.91 714,-6138.5 739.19,-6111.92 703.91,-4848.91 727,-4820.5 732.83,-4813.32 741.07,-4808.9 749.92,-4806.26"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="750.23,-4808.34 755.59,-4804.93 749.27,-4804.25 750.23,-4808.34"/>
</g>
<!-- src/components/MindMap/context/MindMapContext.tsx -->
<g id="node42" class="node">
<title>src/components/MindMap/context/MindMapContext.tsx</title>
<g id="a_node42"><a xlink:href="src/components/MindMap/context/MindMapContext.tsx" xlink:title="MindMapContext.tsx">
<path fill="#bbfeff" stroke="black" d="M1010.46,-3655.75C1010.46,-3655.75 923.54,-3655.75 923.54,-3655.75 920.46,-3655.75 917.38,-3652.66 917.38,-3649.58 917.38,-3649.58 917.38,-3643.41 917.38,-3643.41 917.38,-3640.33 920.46,-3637.25 923.54,-3637.25 923.54,-3637.25 1010.46,-3637.25 1010.46,-3637.25 1013.54,-3637.25 1016.62,-3640.33 1016.62,-3643.41 1016.62,-3643.41 1016.62,-3649.58 1016.62,-3649.58 1016.62,-3652.66 1013.54,-3655.75 1010.46,-3655.75"/>
<text text-anchor="start" x="925.38" y="-3643.2" font-family="Helvetica,sans-Serif" font-size="9.00">MindMapContext.tsx</text>
</a>
</g>
</g>
<!-- src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx -->
<g id="edge333" class="edge">
<title>src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M280.65,-6175.82C294.13,-6190.2 322.38,-6217.22 353,-6227.5 395.61,-6241.8 510.81,-6234.2 555.25,-6227.5 719.79,-6202.68 809.78,-6195.92 883,-6046.5 896.91,-6018.11 882.45,-3800.93 891,-3770.5 902.92,-3728.06 933.65,-3685.63 951.86,-3663.07"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="953.39,-3664.52 955.59,-3658.55 950.15,-3661.85 953.39,-3664.52"/>
</g>
<!-- src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx -->
<g id="node56" class="node">
<title>src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx</title>
<g id="a_node56"><a xlink:href="src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx" xlink:title="MindMapCanvasSimple.tsx">
<path fill="#bbfeff" stroke="black" d="M698.83,-5679.75C698.83,-5679.75 583.42,-5679.75 583.42,-5679.75 580.33,-5679.75 577.25,-5676.66 577.25,-5673.58 577.25,-5673.58 577.25,-5667.41 577.25,-5667.41 577.25,-5664.33 580.33,-5661.25 583.42,-5661.25 583.42,-5661.25 698.83,-5661.25 698.83,-5661.25 701.92,-5661.25 705,-5664.33 705,-5667.41 705,-5667.41 705,-5673.58 705,-5673.58 705,-5676.66 701.92,-5679.75 698.83,-5679.75"/>
<text text-anchor="start" x="585.25" y="-5667.2" font-family="Helvetica,sans-Serif" font-size="9.00">MindMapCanvasSimple.tsx</text>
</a>
</g>
</g>
<!-- src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx -->
<g id="edge329" class="edge">
<title>src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M339.09,-6156.79C341.16,-6155.5 343.14,-6154.08 345,-6152.5 352.92,-6145.74 344.78,-6136.88 353,-6130.5 388.67,-6102.79 524.4,-6143.48 555.25,-6110.5 584.94,-6078.75 546.63,-5757.56 567.62,-5719.5 576.29,-5703.78 592.4,-5692.14 607.23,-5684.15"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="608.17,-5686.03 612.58,-5681.45 606.28,-5682.28 608.17,-5686.03"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NewNodeDialog.tsx -->
<g id="node74" class="node">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NewNodeDialog.tsx</title>
<g id="a_node74"><a xlink:href="src/components/MindMap/components/Dialogs/NodeDialog/NewNodeDialog.tsx" xlink:title="NewNodeDialog.tsx">
<path fill="#bbfeff" stroke="black" d="M843.71,-5836.75C843.71,-5836.75 758.29,-5836.75 758.29,-5836.75 755.21,-5836.75 752.12,-5833.66 752.12,-5830.58 752.12,-5830.58 752.12,-5824.41 752.12,-5824.41 752.12,-5821.33 755.21,-5818.25 758.29,-5818.25 758.29,-5818.25 843.71,-5818.25 843.71,-5818.25 846.79,-5818.25 849.88,-5821.33 849.88,-5824.41 849.88,-5824.41 849.88,-5830.58 849.88,-5830.58 849.88,-5833.66 846.79,-5836.75 843.71,-5836.75"/>
<text text-anchor="start" x="760.12" y="-5824.2" font-family="Helvetica,sans-Serif" font-size="9.00">NewNodeDialog.tsx</text>
</a>
</g>
</g>
<!-- src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/NewNodeDialog.tsx -->
<g id="edge330" class="edge">
<title>src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/NewNodeDialog.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M345.22,-6171.87C463.82,-6179.85 688.12,-6191.36 714,-6166.5 739.9,-6141.61 703.93,-5871.02 727,-5843.5 731.45,-5838.18 737.23,-5834.39 743.58,-5831.71"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="744.01,-5833.79 749.01,-5829.87 742.65,-5829.81 744.01,-5833.79"/>
</g>
<!-- src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/MindMap/components/Dialogs/ProjectManagementDialog.tsx -->
<g id="edge331" class="edge">
<title>src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/MindMap/components/Dialogs/ProjectManagementDialog.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M345.14,-6166.75C467.19,-6166.82 701.18,-6165.27 714,-6152.5 742.1,-6124.48 704.07,-5839.48 727.12,-5779.78"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="728.78,-5781.1 729.88,-5774.84 725.12,-5779.05 728.78,-5781.1"/>
</g>
<!-- src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/governance/chat/index.ts -->
<g id="edge324" class="edge">
<title>src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/governance/chat/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M284.17,-6176.09C301.3,-6190.84 332.39,-6220.93 345,-6254.5 350.38,-6268.8 342.85,-6518.07 353,-6529.5 370.77,-6549.49 401.76,-6552.32 426.02,-6550.82"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="426.13,-6552.92 431.93,-6550.32 425.78,-6548.73 426.13,-6552.92"/>
</g>
<!-- src/components/MindMap/components/Manager/EnhancedMindMapManagerContainer.tsx -->
<g id="node94" class="node">
<title>src/components/MindMap/components/Manager/EnhancedMindMapManagerContainer.tsx</title>
<g id="a_node94"><a xlink:href="src/components/MindMap/components/Manager/EnhancedMindMapManagerContainer.tsx" xlink:title="EnhancedMindMapManagerContainer.tsx">
<path fill="#bbfeff" stroke="black" d="M549.08,-5287.75C549.08,-5287.75 375.17,-5287.75 375.17,-5287.75 372.08,-5287.75 369,-5284.66 369,-5281.58 369,-5281.58 369,-5275.41 369,-5275.41 369,-5272.33 372.08,-5269.25 375.17,-5269.25 375.17,-5269.25 549.08,-5269.25 549.08,-5269.25 552.17,-5269.25 555.25,-5272.33 555.25,-5275.41 555.25,-5275.41 555.25,-5281.58 555.25,-5281.58 555.25,-5284.66 552.17,-5287.75 549.08,-5287.75"/>
<text text-anchor="start" x="377" y="-5275.2" font-family="Helvetica,sans-Serif" font-size="9.00">EnhancedMindMapManagerContainer.tsx</text>
</a>
</g>
</g>
<!-- src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/MindMap/components/Manager/EnhancedMindMapManagerContainer.tsx -->
<g id="edge332" class="edge">
<title>src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/MindMap/components/Manager/EnhancedMindMapManagerContainer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M340.72,-6156.81C342.25,-6155.51 343.68,-6154.07 345,-6152.5 359.82,-6134.72 339.76,-5338.48 353,-5319.5 362.21,-5306.3 376.25,-5297.34 391.19,-5291.27"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="391.82,-5293.28 396.72,-5289.23 390.37,-5289.34 391.82,-5293.28"/>
</g>
<!-- src/components/MindMap/components/index.ts -->
<g id="node102" class="node">
<title>src/components/MindMap/components/index.ts</title>
<g id="a_node102"><a xlink:href="src/components/MindMap/components/index.ts" xlink:title="index.ts">
<path fill="#ddfeff" stroke="black" d="M482.96,-5679.75C482.96,-5679.75 441.29,-5679.75 441.29,-5679.75 438.21,-5679.75 435.12,-5676.66 435.12,-5673.58 435.12,-5673.58 435.12,-5667.41 435.12,-5667.41 435.12,-5664.33 438.21,-5661.25 441.29,-5661.25 441.29,-5661.25 482.96,-5661.25 482.96,-5661.25 486.04,-5661.25 489.12,-5664.33 489.12,-5667.41 489.12,-5667.41 489.12,-5673.58 489.12,-5673.58 489.12,-5676.66 486.04,-5679.75 482.96,-5679.75"/>
<text text-anchor="start" x="446.38" y="-5667.2" font-family="Helvetica,sans-Serif" font-size="9.00">index.ts</text>
</a>
</g>
</g>
<!-- src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/MindMap/components/index.ts -->
<g id="edge328" class="edge">
<title>src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/MindMap/components/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M340.7,-6156.8C342.24,-6155.5 343.68,-6154.07 345,-6152.5 361.76,-6132.54 335.54,-5702.84 353,-5683.5 370.86,-5663.7 401.84,-5661.77 426.08,-5664.03"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="425.78,-5666.11 431.99,-5664.72 426.27,-5661.93 425.78,-5666.11"/>
</g>
<!-- src/components/MindMap/utils/MBCPProcessor.ts -->
<g id="node157" class="node">
<title>src/components/MindMap/utils/MBCPProcessor.ts</title>
<g id="a_node157"><a xlink:href="src/components/MindMap/utils/MBCPProcessor.ts" xlink:title="MBCPProcessor.ts">
<path fill="#ddfeff" stroke="black" d="M840.71,-4585.75C840.71,-4585.75 761.29,-4585.75 761.29,-4585.75 758.21,-4585.75 755.12,-4582.66 755.12,-4579.58 755.12,-4579.58 755.12,-4573.41 755.12,-4573.41 755.12,-4570.33 758.21,-4567.25 761.29,-4567.25 761.29,-4567.25 840.71,-4567.25 840.71,-4567.25 843.79,-4567.25 846.88,-4570.33 846.88,-4573.41 846.88,-4573.41 846.88,-4579.58 846.88,-4579.58 846.88,-4582.66 843.79,-4585.75 840.71,-4585.75"/>
<text text-anchor="start" x="763.12" y="-4573.2" font-family="Helvetica,sans-Serif" font-size="9.00">MBCPProcessor.ts</text>
</a>
</g>
</g>
<!-- src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/MindMap/utils/MBCPProcessor.ts -->
<g id="edge337" class="edge">
<title>src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/MindMap/utils/MBCPProcessor.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M333.63,-6156.8C337.54,-6155.53 341.36,-6154.11 345,-6152.5 349.06,-6150.7 348.87,-6148.13 353,-6146.5 505.26,-6086.4 616.95,-6205.32 714,-6073.5 737.12,-6042.09 714.09,-4706.3 727,-4669.5 737.98,-4638.2 764.17,-4609.37 782.01,-4592.35"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="783.42,-4593.9 786.39,-4588.28 780.57,-4590.82 783.42,-4593.9"/>
</g>
<!-- src/components/OptimizedMindMap.css -->
<g id="node161" class="node">
<title>src/components/OptimizedMindMap.css</title>
<g id="a_node161"><a xlink:href="src/components/OptimizedMindMap.css" xlink:title="OptimizedMindMap.css">
<path fill="#ffffcc" stroke="black" d="M511.58,-6221.75C511.58,-6221.75 412.67,-6221.75 412.67,-6221.75 409.58,-6221.75 406.5,-6218.66 406.5,-6215.58 406.5,-6215.58 406.5,-6209.41 406.5,-6209.41 406.5,-6206.33 409.58,-6203.25 412.67,-6203.25 412.67,-6203.25 511.58,-6203.25 511.58,-6203.25 514.67,-6203.25 517.75,-6206.33 517.75,-6209.41 517.75,-6209.41 517.75,-6215.58 517.75,-6215.58 517.75,-6218.66 514.67,-6221.75 511.58,-6221.75"/>
<text text-anchor="start" x="414.5" y="-6209.2" font-family="Helvetica,sans-Serif" font-size="9.00">OptimizedMindMap.css</text>
</a>
</g>
</g>
<!-- src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/OptimizedMindMap.css -->
<g id="edge338" class="edge">
<title>src/components/OptimizedMindMap_Modular.tsx&#45;&gt;src/components/OptimizedMindMap.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M296.75,-6176.17C312.57,-6182.15 333.76,-6189.59 353,-6194.5 367.26,-6198.13 382.77,-6201.21 397.47,-6203.74"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="396.77,-6205.75 403.04,-6204.67 397.46,-6201.61 396.77,-6205.75"/>
</g>
<!-- src/components/ChatFork/ChatForkConnection.tsx -->
<g id="node30" class="node">
<title>src/components/ChatFork/ChatForkConnection.tsx</title>
<g id="a_node30"><a xlink:href="src/components/ChatFork/ChatForkConnection.tsx" xlink:title="ChatForkConnection.tsx">
<path fill="#bbfeff" stroke="black" d="M322.33,-6310.75C322.33,-6310.75 220.42,-6310.75 220.42,-6310.75 217.33,-6310.75 214.25,-6307.66 214.25,-6304.58 214.25,-6304.58 214.25,-6298.41 214.25,-6298.41 214.25,-6295.33 217.33,-6292.25 220.42,-6292.25 220.42,-6292.25 322.33,-6292.25 322.33,-6292.25 325.42,-6292.25 328.5,-6295.33 328.5,-6298.41 328.5,-6298.41 328.5,-6304.58 328.5,-6304.58 328.5,-6307.66 325.42,-6310.75 322.33,-6310.75"/>
<text text-anchor="start" x="222.25" y="-6298.2" font-family="Helvetica,sans-Serif" font-size="9.00">ChatForkConnection.tsx</text>
</a>
</g>
</g>
<!-- src/components/ChatFork/ChatForkConnection.tsx&#45;&gt;node_modules/react -->
<g id="edge11" class="edge">
<title>src/components/ChatFork/ChatForkConnection.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M328.72,-6299.75C334.97,-6297.3 340.64,-6293.7 345,-6288.5 357.74,-6273.3 340.8,-593.13 353,-577.5 410.03,-504.41 492.56,-594.79 555.25,-526.5 578.39,-501.28 541.55,-472.66 567.62,-450.5 607.86,-416.28 1005.55,-413.25 1043,-450.5 1057.28,-464.7 1038.41,-1884.78 1051,-1900.5 1057.69,-1908.85 1068.26,-1912.85 1078.74,-1914.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.22,-1916.66 1084.43,-1915.31 1078.73,-1912.49 1078.22,-1916.66"/>
</g>
<!-- src/components/ChatFork/ChatForkConnection.tsx&#45;&gt;src/components/ChatFork/ChatFork.css -->
<g id="edge9" class="edge">
<title>src/components/ChatFork/ChatForkConnection.tsx&#45;&gt;src/components/ChatFork/ChatFork.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M328.89,-6296.24C386.9,-6292.6 479.16,-6291.73 555.25,-6313.5 561.33,-6315.23 561.51,-6318.89 567.62,-6320.5 630.54,-6337.06 648.96,-6322.01 714,-6320.5 728.1,-6320.17 743.48,-6319.54 757.29,-6318.87"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="757.2,-6320.98 763.09,-6318.59 756.99,-6316.79 757.2,-6320.98"/>
</g>
<!-- src/components/ChatFork/index.tsx -->
<g id="node31" class="node">
<title>src/components/ChatFork/index.tsx</title>
<g id="a_node31"><a xlink:href="src/components/ChatFork/index.tsx" xlink:title="index.tsx">
<path fill="#bbfeff" stroke="black" d="M482.96,-6337.75C482.96,-6337.75 441.29,-6337.75 441.29,-6337.75 438.21,-6337.75 435.12,-6334.66 435.12,-6331.58 435.12,-6331.58 435.12,-6325.41 435.12,-6325.41 435.12,-6322.33 438.21,-6319.25 441.29,-6319.25 441.29,-6319.25 482.96,-6319.25 482.96,-6319.25 486.04,-6319.25 489.12,-6322.33 489.12,-6325.41 489.12,-6325.41 489.12,-6331.58 489.12,-6331.58 489.12,-6334.66 486.04,-6337.75 482.96,-6337.75"/>
<text text-anchor="start" x="444.12" y="-6325.2" font-family="Helvetica,sans-Serif" font-size="9.00">index.tsx</text>
</a>
</g>
</g>
<!-- src/components/ChatFork/ChatForkConnection.tsx&#45;&gt;src/components/ChatFork/index.tsx -->
<g id="edge10" class="edge">
<title>src/components/ChatFork/ChatForkConnection.tsx&#45;&gt;src/components/ChatFork/index.tsx</title>
<g id="a_edge10"><a xlink:title="no&#45;circular">
<path fill="none" stroke="orange" stroke-width="2" d="M328.75,-6305.4C356.71,-6308.87 390.01,-6313.72 416.22,-6318.17"/>
<polygon fill="orange" stroke="orange" stroke-width="2" points="425.42,-6321.95 431.69,-6320.94 426.15,-6317.82 425.42,-6321.95"/>
<polyline fill="none" stroke="orange" stroke-width="2" points="424.8,-6319.71 421.85,-6319.18"/>
<ellipse fill="none" stroke="orange" stroke-width="2" cx="418.5" cy="-6318.58" rx="2.4" ry="2.4"/>
</a>
</g>
<text text-anchor="middle" x="360.2" y="-6304.17" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">no&#45;circular</text>
</g>
<!-- src/components/ChatFork/index.tsx&#45;&gt;node_modules/react -->
<g id="edge31" class="edge">
<title>src/components/ChatFork/index.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M489.25,-6333.05C510.45,-6335.09 539.34,-6333.77 555.25,-6315.5 580.54,-6286.45 543.45,-800.46 567.62,-770.5 609.44,-718.67 669.16,-787.73 714,-738.5 745.96,-703.41 691.45,-663.95 727,-632.5 753.3,-609.23 1018.09,-607.75 1043,-632.5 1055.49,-644.91 1039.98,-1886.76 1051,-1900.5 1057.7,-1908.84 1068.26,-1912.85 1078.74,-1914.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.22,-1916.66 1084.43,-1915.31 1078.73,-1912.49 1078.22,-1916.66"/>
</g>
<!-- src/components/ChatFork/index.tsx&#45;&gt;src/components/ChatFork/ChatFork.css -->
<g id="edge28" class="edge">
<title>src/components/ChatFork/index.tsx&#45;&gt;src/components/ChatFork/ChatFork.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M489.56,-6330.51C535.36,-6333.63 632.42,-6338.56 714,-6331.5 728.24,-6330.26 743.67,-6327.9 757.47,-6325.41"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="757.79,-6327.49 763.3,-6324.32 757.02,-6323.36 757.79,-6327.49"/>
</g>
<!-- src/components/ChatFork/index.tsx&#45;&gt;src/components/ChatFork/ChatForkConnection.tsx -->
<g id="edge29" class="edge">
<title>src/components/ChatFork/index.tsx&#45;&gt;src/components/ChatFork/ChatForkConnection.tsx</title>
<g id="a_edge29"><a xlink:title="no&#45;circular">
<path fill="none" stroke="orange" stroke-width="2" d="M434.63,-6327.95C408.15,-6325.39 366.52,-6319.77 332.06,-6314.25"/>
<polygon fill="orange" stroke="orange" stroke-width="2" points="323.05,-6310.63 316.78,-6311.72 322.36,-6314.77 323.05,-6310.63"/>
<polyline fill="none" stroke="orange" stroke-width="2" points="323.69,-6312.86 326.65,-6313.35"/>
<ellipse fill="none" stroke="orange" stroke-width="2" cx="330" cy="-6313.91" rx="2.4" ry="2.4"/>
</a>
</g>
<text text-anchor="middle" x="352.22" y="-6322.47" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">no&#45;circular</text>
</g>
<!-- src/components/ChatFork/index.tsx&#45;&gt;src/services/api/GovernanceLLM.ts -->
<g id="edge27" class="edge">
<title>src/components/ChatFork/index.tsx&#45;&gt;src/services/api/GovernanceLLM.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M474.98,-6338.17C495.83,-6356.02 538.43,-6396.44 555.25,-6441.5 563.12,-6462.58 551.81,-6830.48 567.62,-6846.5 603.94,-6883.27 748.31,-6856.5 800,-6856.5 800,-6856.5 800,-6856.5 968,-6856.5 1014.66,-6856.5 1140.18,-6874.17 1177.75,-6846.5 1186.13,-6840.32 1180.6,-6833.53 1185.75,-6824.5 1196.97,-6804.83 1213.92,-6785.15 1226.21,-6772.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1227.43,-6773.82 1230.06,-6768.03 1224.39,-6770.91 1227.43,-6773.82"/>
</g>
<!-- src/components/ChatFork/ChatForkNode.tsx -->
<g id="node34" class="node">
<title>src/components/ChatFork/ChatForkNode.tsx</title>
<g id="a_node34"><a xlink:href="src/components/ChatFork/ChatForkNode.tsx" xlink:title="ChatForkNode.tsx">
<path fill="#bbfeff" stroke="black" d="M679.71,-6283.75C679.71,-6283.75 602.54,-6283.75 602.54,-6283.75 599.46,-6283.75 596.38,-6280.66 596.38,-6277.58 596.38,-6277.58 596.38,-6271.41 596.38,-6271.41 596.38,-6268.33 599.46,-6265.25 602.54,-6265.25 602.54,-6265.25 679.71,-6265.25 679.71,-6265.25 682.79,-6265.25 685.88,-6268.33 685.88,-6271.41 685.88,-6271.41 685.88,-6277.58 685.88,-6277.58 685.88,-6280.66 682.79,-6283.75 679.71,-6283.75"/>
<text text-anchor="start" x="604.38" y="-6271.2" font-family="Helvetica,sans-Serif" font-size="9.00">ChatForkNode.tsx</text>
</a>
</g>
</g>
<!-- src/components/ChatFork/index.tsx&#45;&gt;src/components/ChatFork/ChatForkNode.tsx -->
<g id="edge30" class="edge">
<title>src/components/ChatFork/index.tsx&#45;&gt;src/components/ChatFork/ChatForkNode.tsx</title>
<g id="a_edge30"><a xlink:title="no&#45;circular">
<path fill="none" stroke="orange" stroke-width="2" d="M477.13,-6318.75C497,-6308.71 534.63,-6294.09 567.62,-6284.5 571.06,-6283.5 574.6,-6282.53 578.19,-6281.6"/>
<polygon fill="orange" stroke="orange" stroke-width="2" points="587.88,-6281.48 593.24,-6278.06 586.92,-6277.39 587.88,-6281.48"/>
<polyline fill="none" stroke="orange" stroke-width="2" points="586.42,-6279.66 583.5,-6280.35"/>
<ellipse fill="none" stroke="orange" stroke-width="2" cx="580.19" cy="-6281.13" rx="2.4" ry="2.4"/>
</a>
</g>
<text text-anchor="middle" x="513.87" y="-6286.26" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">no&#45;circular</text>
</g>
<!-- src/components/ChatFork/ChatForkContainer.tsx&#45;&gt;node_modules/react -->
<g id="edge15" class="edge">
<title>src/components/ChatFork/ChatForkContainer.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M319.88,-6335.8C329.75,-6331.53 338.97,-6325.37 345,-6316.5 351.81,-6306.46 352.43,-2836.61 353,-2824.5 353.76,-2808.12 555.52,-484.55 567.62,-473.5 583.25,-459.23 1012.2,-445.59 1043,-476.5 1056.96,-490.5 1038.63,-1885.06 1051,-1900.5 1057.69,-1908.85 1068.26,-1912.85 1078.74,-1914.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.22,-1916.66 1084.43,-1915.31 1078.73,-1912.49 1078.22,-1916.66"/>
</g>
<!-- src/components/ChatFork/ChatForkContainer.tsx&#45;&gt;src/components/ChatFork/ChatFork.css -->
<g id="edge13" class="edge">
<title>src/components/ChatFork/ChatForkContainer.tsx&#45;&gt;src/components/ChatFork/ChatFork.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M325.04,-6348.61C408.16,-6352.62 574.64,-6356.94 714,-6337.5 728.75,-6335.44 744.65,-6331.93 758.72,-6328.36"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="759.02,-6330.46 764.29,-6326.91 757.96,-6326.39 759.02,-6330.46"/>
</g>
<!-- src/components/ChatFork/ChatForkContainer.tsx&#45;&gt;src/components/ChatFork/index.tsx -->
<g id="edge14" class="edge">
<title>src/components/ChatFork/ChatForkContainer.tsx&#45;&gt;src/components/ChatFork/index.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M325.2,-6340.74C357.38,-6337.84 397.62,-6334.22 425.93,-6331.66"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="425.88,-6333.78 431.67,-6331.15 425.5,-6329.6 425.88,-6333.78"/>
</g>
<!-- src/components/ChatFork/ChatForkContainer.tsx&#45;&gt;src/services/api/GovernanceLLM.ts -->
<g id="edge12" class="edge">
<title>src/components/ChatFork/ChatForkContainer.tsx&#45;&gt;src/services/api/GovernanceLLM.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M325.04,-6346.05C332.71,-6348.45 339.76,-6352.35 345,-6358.5 360.58,-6376.76 339.29,-6770.79 353,-6790.5 468.99,-6957.11 596.99,-6884.5 800,-6884.5 800,-6884.5 800,-6884.5 968,-6884.5 991.33,-6884.5 1160.36,-6890.05 1177.75,-6874.5 1194.52,-6859.49 1176.51,-6845.01 1185.75,-6824.5 1194.94,-6804.09 1211.91,-6784.77 1224.74,-6771.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1225.98,-6773.7 1228.84,-6768.02 1223.06,-6770.68 1225.98,-6773.7"/>
</g>
<!-- src/components/ChatFork/ChatForkNode.tsx&#45;&gt;node_modules/react -->
<g id="edge18" class="edge">
<title>src/components/ChatFork/ChatForkNode.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M646.95,-6264.78C660.82,-6236.3 700.6,-6150.16 714,-6073.5 719.63,-6041.28 706.76,-1390.18 727,-1364.5 771.16,-1308.44 834.05,-1378.42 883,-1326.5 896.63,-1312.03 875.74,-1295.23 891,-1282.5 916.93,-1260.85 1018.96,-1258.77 1043,-1282.5 1055.22,-1294.56 1040.21,-1887.14 1051,-1900.5 1057.6,-1908.67 1067.92,-1912.67 1078.21,-1914.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1077.93,-1916.57 1084.15,-1915.25 1078.47,-1912.4 1077.93,-1916.57"/>
</g>
<!-- src/components/ChatFork/ChatForkNode.tsx&#45;&gt;src/components/ChatFork/ChatFork.css -->
<g id="edge16" class="edge">
<title>src/components/ChatFork/ChatForkNode.tsx&#45;&gt;src/components/ChatFork/ChatFork.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M686.13,-6280.17C695.69,-6282.51 705.48,-6285.82 714,-6290.5 721.16,-6294.43 719.76,-6299.72 727,-6303.5 736.18,-6308.28 746.8,-6311.35 757.05,-6313.3"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="756.66,-6315.36 762.92,-6314.25 757.34,-6311.22 756.66,-6315.36"/>
</g>
<!-- src/components/ChatFork/ChatForkNode.tsx&#45;&gt;src/components/ChatFork/index.tsx -->
<g id="edge17" class="edge">
<title>src/components/ChatFork/ChatForkNode.tsx&#45;&gt;src/components/ChatFork/index.tsx</title>
<g id="a_edge17"><a xlink:title="no&#45;circular">
<path fill="none" stroke="orange" stroke-width="2" d="M616.09,-6284.21C601.88,-6287.83 583.59,-6291.85 567.62,-6296.5 547.43,-6302.37 525.5,-6310.12 506.97,-6316.63"/>
<polygon fill="orange" stroke="orange" stroke-width="2" points="497.27,-6317.78 492.3,-6321.73 498.65,-6321.74 497.27,-6317.78"/>
<polyline fill="none" stroke="orange" stroke-width="2" points="498.91,-6319.43 501.74,-6318.45"/>
<ellipse fill="none" stroke="orange" stroke-width="2" cx="504.95" cy="-6317.33" rx="2.4" ry="2.4"/>
</a>
</g>
<text text-anchor="middle" x="573.5" y="-6292.81" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">no&#45;circular</text>
</g>
<!-- src/components/MindMap/core/adapters/ChatForkAdapter.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge195" class="edge">
<title>src/components/MindMap/core/adapters/ChatForkAdapter.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M652.84,-4962.77C669.43,-4946.97 700.77,-4913.91 714,-4878.5 722.14,-4856.7 710.04,-4685.43 727,-4669.5 777.52,-4622.02 832.38,-4622.11 883,-4669.5 902.67,-4687.91 876.96,-4767.5 891,-4790.5 899.68,-4804.71 914.69,-4815.01 929.01,-4822.13"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="927.78,-4823.87 934.11,-4824.49 929.55,-4820.06 927.78,-4823.87"/>
</g>
<!-- src/components/MindMap/core/adapters/ChatForkAdapter.ts&#45;&gt;src/services/api/GovernanceLLM.ts -->
<g id="edge193" class="edge">
<title>src/components/MindMap/core/adapters/ChatForkAdapter.ts&#45;&gt;src/services/api/GovernanceLLM.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M645.47,-4982.21C657.66,-5018.24 699.79,-5147.79 714,-5258.5 719.05,-5297.84 705.49,-6653.16 727,-6686.5 809.76,-6814.77 899.38,-6782.74 1051,-6800.5 1106.95,-6807.05 1123.96,-6817.23 1177.75,-6800.5 1195.81,-6794.88 1213.28,-6782.18 1225.37,-6771.86"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1226.47,-6773.7 1229.56,-6768.14 1223.68,-6770.55 1226.47,-6773.7"/>
</g>
<!-- src/components/MindMap/core/adapters/ChatForkAdapter.ts&#45;&gt;src/components/MindMap/core/state/ChatForkStore.ts -->
<g id="edge194" class="edge">
<title>src/components/MindMap/core/adapters/ChatForkAdapter.ts&#45;&gt;src/components/MindMap/core/state/ChatForkStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M651.94,-4962.76C667.55,-4946.69 697.93,-4912.93 714,-4878.5 725.17,-4854.56 708.28,-4839.13 727,-4820.5 733.28,-4814.25 741.39,-4810.2 749.93,-4807.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="750.28,-4809.69 755.6,-4806.21 749.28,-4805.61 750.28,-4809.69"/>
</g>
<!-- src/components/MindMap/core/state/ChatForkStore.ts&#45;&gt;node_modules/zustand -->
<g id="edge207" class="edge">
<title>src/components/MindMap/core/state/ChatForkStore.ts&#45;&gt;node_modules/zustand</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M843.61,-4807.52C858.23,-4806.25 873.4,-4801.82 883,-4790.5 893.46,-4778.16 889.81,-3642.62 891,-3626.5 915.04,-3302.15 1085.85,-2232.98 1109.43,-2086.24"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1111.49,-2086.67 1110.37,-2080.41 1107.34,-2086.01 1111.49,-2086.67"/>
</g>
<!-- src/components/MindMap/core/state/ChatForkStore.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge206" class="edge">
<title>src/components/MindMap/core/state/ChatForkStore.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M843.38,-4812.32C865.26,-4816.45 892.3,-4821.56 915.41,-4825.93"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="914.8,-4827.95 921.08,-4827 915.58,-4823.83 914.8,-4827.95"/>
</g>
<!-- src/components/MindMap/core/state/ChatForkStore.ts&#45;&gt;src/services/api/GovernanceLLM.ts -->
<g id="edge205" class="edge">
<title>src/components/MindMap/core/state/ChatForkStore.ts&#45;&gt;src/services/api/GovernanceLLM.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M843.3,-4802.51C858.07,-4804.11 873.42,-4808.89 883,-4820.5 898.66,-4839.46 877.77,-6570.76 891,-6591.5 909.63,-6620.7 1125.23,-6709.71 1208.81,-6743.4"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1208.01,-6745.34 1214.36,-6745.63 1209.58,-6741.44 1208.01,-6745.34"/>
</g>
<!-- src/components/ChatFork/index.ts -->
<g id="node37" class="node">
<title>src/components/ChatFork/index.ts</title>
<g id="a_node37"><a xlink:href="src/components/ChatFork/index.ts" xlink:title="index.ts">
<path fill="#ddfeff" stroke="black" d="M161.21,-6321.75C161.21,-6321.75 119.54,-6321.75 119.54,-6321.75 116.46,-6321.75 113.38,-6318.66 113.38,-6315.58 113.38,-6315.58 113.38,-6309.41 113.38,-6309.41 113.38,-6306.33 116.46,-6303.25 119.54,-6303.25 119.54,-6303.25 161.21,-6303.25 161.21,-6303.25 164.29,-6303.25 167.38,-6306.33 167.38,-6309.41 167.38,-6309.41 167.38,-6315.58 167.38,-6315.58 167.38,-6318.66 164.29,-6321.75 161.21,-6321.75"/>
<text text-anchor="start" x="124.62" y="-6309.2" font-family="Helvetica,sans-Serif" font-size="9.00">index.ts</text>
</a>
</g>
</g>
<!-- src/components/ChatFork/index.ts&#45;&gt;src/components/ChatFork/ChatForkConnection.tsx -->
<g id="edge23" class="edge">
<title>src/components/ChatFork/index.ts&#45;&gt;src/components/ChatFork/ChatForkConnection.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M167.79,-6310.24C179.26,-6309.26 193.22,-6308.07 207.11,-6306.89"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="213.1,-6308.49 206.94,-6306.9 212.74,-6304.3 213.1,-6308.49"/>
</g>
<!-- src/components/ChatFork/index.ts&#45;&gt;src/components/ChatFork/index.tsx -->
<g id="edge26" class="edge">
<title>src/components/ChatFork/index.ts&#45;&gt;src/components/ChatFork/index.tsx</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M167.75,-6314.69C175.92,-6315.33 184.96,-6315.99 193.25,-6316.5 277.45,-6321.63 376.66,-6325.51 427.44,-6327.33"/>
<polygon fill="none" stroke="#000000" stroke-opacity="0.200000" points="427.29,-6329.43 433.36,-6327.55 427.44,-6325.23 427.29,-6329.43"/>
</g>
<!-- src/components/ChatFork/index.ts&#45;&gt;src/components/ChatFork/ChatForkContainer.tsx -->
<g id="edge24" class="edge">
<title>src/components/ChatFork/index.ts&#45;&gt;src/components/ChatFork/ChatForkContainer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M167.69,-6320.39C175.86,-6322.77 184.9,-6325.32 193.25,-6327.5 201.94,-6329.76 211.22,-6332.05 220.22,-6334.19"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="225.58,-6337.61 220.23,-6334.19 226.55,-6333.53 225.58,-6337.61"/>
</g>
<!-- src/components/ChatFork/index.ts&#45;&gt;src/components/ChatFork/ChatForkNode.tsx -->
<g id="edge25" class="edge">
<title>src/components/ChatFork/index.ts&#45;&gt;src/components/ChatFork/ChatForkNode.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M167.54,-6307.85C173.59,-6305.94 179.71,-6303.26 184.75,-6299.5 190.28,-6295.36 187.22,-6289.86 193.25,-6286.5 210.26,-6277 475.51,-6275.02 589.12,-6274.6"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="595.06,-6276.68 589.05,-6274.6 595.04,-6272.48 595.06,-6276.68"/>
</g>
<!-- src/components/ErrorBoundary.tsx -->
<g id="node38" class="node">
<title>src/components/ErrorBoundary.tsx</title>
<g id="a_node38"><a xlink:href="src/components/ErrorBoundary.tsx" xlink:title="ErrorBoundary.tsx">
<path fill="#bbfeff" stroke="black" d="M178.58,-2845.75C178.58,-2845.75 102.17,-2845.75 102.17,-2845.75 99.08,-2845.75 96,-2842.66 96,-2839.58 96,-2839.58 96,-2833.41 96,-2833.41 96,-2830.33 99.08,-2827.25 102.17,-2827.25 102.17,-2827.25 178.58,-2827.25 178.58,-2827.25 181.67,-2827.25 184.75,-2830.33 184.75,-2833.41 184.75,-2833.41 184.75,-2839.58 184.75,-2839.58 184.75,-2842.66 181.67,-2845.75 178.58,-2845.75"/>
<text text-anchor="start" x="104" y="-2833.2" font-family="Helvetica,sans-Serif" font-size="9.00">ErrorBoundary.tsx</text>
</a>
</g>
</g>
<!-- src/components/ErrorBoundary.tsx&#45;&gt;node_modules/react -->
<g id="edge32" class="edge">
<title>src/components/ErrorBoundary.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M141.38,-2826.97C141.54,-2659.56 144.63,-342.14 193.25,-294.5 226.99,-261.44 1010.86,-285.89 1043,-320.5 1057.93,-336.57 1037.28,-1883.37 1051,-1900.5 1057.69,-1908.85 1068.26,-1912.85 1078.74,-1914.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.21,-1916.66 1084.43,-1915.31 1078.73,-1912.49 1078.21,-1916.66"/>
</g>
<!-- src/components/MindMap/Connection.tsx -->
<g id="node39" class="node">
<title>src/components/MindMap/Connection.tsx</title>
<g id="a_node39"><a xlink:href="src/components/MindMap/Connection.tsx" xlink:title="Connection.tsx">
<path fill="#bbfeff" stroke="black" d="M833.21,-3737.75C833.21,-3737.75 768.79,-3737.75 768.79,-3737.75 765.71,-3737.75 762.62,-3734.66 762.62,-3731.58 762.62,-3731.58 762.62,-3725.41 762.62,-3725.41 762.62,-3722.33 765.71,-3719.25 768.79,-3719.25 768.79,-3719.25 833.21,-3719.25 833.21,-3719.25 836.29,-3719.25 839.38,-3722.33 839.38,-3725.41 839.38,-3725.41 839.38,-3731.58 839.38,-3731.58 839.38,-3734.66 836.29,-3737.75 833.21,-3737.75"/>
<text text-anchor="start" x="770.62" y="-3725.2" font-family="Helvetica,sans-Serif" font-size="9.00">Connection.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/Connection.tsx&#45;&gt;node_modules/react -->
<g id="edge34" class="edge">
<title>src/components/MindMap/Connection.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M839.62,-3724.55C855.72,-3720.79 873.07,-3713.51 883,-3699.5 895.48,-3681.87 877.4,-2161.27 891,-2144.5 934.53,-2090.79 998.43,-2164.34 1043,-2111.5 1055.98,-2096.11 1038.35,-1946.15 1051,-1930.5 1057.72,-1922.18 1068.19,-1917.87 1078.57,-1915.73"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.62,-1917.85 1084.21,-1914.82 1077.95,-1913.71 1078.62,-1917.85"/>
</g>
<!-- src/components/MindMap/types/index.ts -->
<g id="node40" class="node">
<title>src/components/MindMap/types/index.ts</title>
<g id="a_node40"><a xlink:href="src/components/MindMap/types/index.ts" xlink:title="index.ts">
<path fill="#ddfeff" stroke="black" d="M987.83,-3799.75C987.83,-3799.75 946.17,-3799.75 946.17,-3799.75 943.08,-3799.75 940,-3796.66 940,-3793.58 940,-3793.58 940,-3787.41 940,-3787.41 940,-3784.33 943.08,-3781.25 946.17,-3781.25 946.17,-3781.25 987.83,-3781.25 987.83,-3781.25 990.92,-3781.25 994,-3784.33 994,-3787.41 994,-3787.41 994,-3793.58 994,-3793.58 994,-3796.66 990.92,-3799.75 987.83,-3799.75"/>
<text text-anchor="start" x="951.25" y="-3787.2" font-family="Helvetica,sans-Serif" font-size="9.00">index.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/Connection.tsx&#45;&gt;src/components/MindMap/types/index.ts -->
<g id="edge33" class="edge">
<title>src/components/MindMap/Connection.tsx&#45;&gt;src/components/MindMap/types/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M827.62,-3738.18C855.7,-3748.8 900.88,-3765.88 931.93,-3777.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="931.1,-3779.55 937.45,-3779.7 932.58,-3775.62 931.1,-3779.55"/>
</g>
<!-- src/components/MindMap/DesignControls.tsx -->
<g id="node41" class="node">
<title>src/components/MindMap/DesignControls.tsx</title>
<g id="a_node41"><a xlink:href="src/components/MindMap/DesignControls.tsx" xlink:title="DesignControls.tsx">
<path fill="#bbfeff" stroke="black" d="M841.46,-3693.75C841.46,-3693.75 760.54,-3693.75 760.54,-3693.75 757.46,-3693.75 754.38,-3690.66 754.38,-3687.58 754.38,-3687.58 754.38,-3681.41 754.38,-3681.41 754.38,-3678.33 757.46,-3675.25 760.54,-3675.25 760.54,-3675.25 841.46,-3675.25 841.46,-3675.25 844.54,-3675.25 847.62,-3678.33 847.62,-3681.41 847.62,-3681.41 847.62,-3687.58 847.62,-3687.58 847.62,-3690.66 844.54,-3693.75 841.46,-3693.75"/>
<text text-anchor="start" x="762.38" y="-3681.2" font-family="Helvetica,sans-Serif" font-size="9.00">DesignControls.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/DesignControls.tsx&#45;&gt;node_modules/react -->
<g id="edge37" class="edge">
<title>src/components/MindMap/DesignControls.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M847.85,-3687.07C861.11,-3685.34 874.33,-3680.73 883,-3670.5 896.93,-3654.04 877.53,-2135.33 891,-2118.5 934.31,-2064.37 998.43,-2136.59 1043,-2083.5 1064.89,-2057.42 1029.5,-1956.89 1051,-1930.5 1057.75,-1922.2 1068.23,-1917.91 1078.61,-1915.76"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.66,-1917.88 1084.25,-1914.85 1077.99,-1913.74 1078.66,-1917.88"/>
</g>
<!-- src/components/MindMap/DesignControls.tsx&#45;&gt;src/components/MindMap/types/index.ts -->
<g id="edge36" class="edge">
<title>src/components/MindMap/DesignControls.tsx&#45;&gt;src/components/MindMap/types/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M838.9,-3694.23C853.25,-3698.89 869.47,-3705.28 883,-3713.5 910.49,-3730.19 936.75,-3757 952.12,-3774.18"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="950.47,-3775.49 956.01,-3778.61 953.63,-3772.72 950.47,-3775.49"/>
</g>
<!-- src/components/MindMap/DesignControls.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx -->
<g id="edge35" class="edge">
<title>src/components/MindMap/DesignControls.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M848.01,-3677.45C859.49,-3675.44 871.74,-3673.09 883,-3670.5 897.53,-3667.15 913.28,-3662.79 927.12,-3658.7"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="927.42,-3660.8 932.57,-3657.07 926.22,-3656.78 927.42,-3660.8"/>
</g>
<!-- src/components/MindMap/context/MindMapContext.tsx&#45;&gt;node_modules/react -->
<g id="edge191" class="edge">
<title>src/components/MindMap/context/MindMapContext.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M969.14,-3637.06C977.24,-3569.67 1026.18,-3154.17 1043,-2814.5 1043.61,-2802.23 1043.45,-1940.18 1051,-1930.5 1057.63,-1921.99 1068.18,-1917.66 1078.67,-1915.55"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.77,-1917.66 1084.37,-1914.65 1078.12,-1913.51 1078.77,-1917.66"/>
</g>
<!-- src/components/MindMap/context/MindMapContext.tsx&#45;&gt;src/components/MindMap/types/index.ts -->
<g id="edge190" class="edge">
<title>src/components/MindMap/context/MindMapContext.tsx&#45;&gt;src/components/MindMap/types/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M967,-3656.06C967,-3694.71 967,-3733.36 967,-3772.01"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="964.9,-3771.75 967,-3777.75 969.1,-3771.75 964.9,-3771.75"/>
</g>
<!-- src/components/MindMap/LineStyleDialog.tsx -->
<g id="node43" class="node">
<title>src/components/MindMap/LineStyleDialog.tsx</title>
<g id="a_node43"><a xlink:href="src/components/MindMap/LineStyleDialog.tsx" xlink:title="LineStyleDialog.tsx">
<path fill="#bbfeff" stroke="black" d="M842.21,-3257.75C842.21,-3257.75 759.79,-3257.75 759.79,-3257.75 756.71,-3257.75 753.62,-3254.66 753.62,-3251.58 753.62,-3251.58 753.62,-3245.41 753.62,-3245.41 753.62,-3242.33 756.71,-3239.25 759.79,-3239.25 759.79,-3239.25 842.21,-3239.25 842.21,-3239.25 845.29,-3239.25 848.38,-3242.33 848.38,-3245.41 848.38,-3245.41 848.38,-3251.58 848.38,-3251.58 848.38,-3254.66 845.29,-3257.75 842.21,-3257.75"/>
<text text-anchor="start" x="761.62" y="-3245.2" font-family="Helvetica,sans-Serif" font-size="9.00">LineStyleDialog.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/LineStyleDialog.tsx&#45;&gt;node_modules/react -->
<g id="edge39" class="edge">
<title>src/components/MindMap/LineStyleDialog.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M848.78,-3249.72C861.77,-3247.58 874.59,-3242.66 883,-3232.5 904.11,-3206.98 872.28,-2067.81 891,-2040.5 932.06,-1980.58 997.52,-2037.12 1043,-1980.5 1057.09,-1962.95 1035.88,-1947.16 1051,-1930.5 1058.06,-1922.71 1068.39,-1918.52 1078.55,-1916.31"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.84,-1918.39 1084.39,-1915.29 1078.12,-1914.25 1078.84,-1918.39"/>
</g>
<!-- src/components/MindMap/LineStyleDialog.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx -->
<g id="edge38" class="edge">
<title>src/components/MindMap/LineStyleDialog.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M848.46,-3249.8C861.12,-3252.36 873.85,-3257.31 883,-3266.5 935.13,-3318.81 958.77,-3559.16 964.55,-3628.02"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="962.45,-3628.06 965.03,-3633.87 966.63,-3627.72 962.45,-3628.06"/>
</g>
<!-- src/components/MindMap/MindMap.css -->
<g id="node44" class="node">
<title>src/components/MindMap/MindMap.css</title>
<g id="a_node44"><a xlink:href="src/components/MindMap/MindMap.css" xlink:title="MindMap.css">
<path fill="#ffffcc" stroke="black" d="M829.83,-3921.75C829.83,-3921.75 772.17,-3921.75 772.17,-3921.75 769.08,-3921.75 766,-3918.66 766,-3915.58 766,-3915.58 766,-3909.41 766,-3909.41 766,-3906.33 769.08,-3903.25 772.17,-3903.25 772.17,-3903.25 829.83,-3903.25 829.83,-3903.25 832.92,-3903.25 836,-3906.33 836,-3909.41 836,-3909.41 836,-3915.58 836,-3915.58 836,-3918.66 832.92,-3921.75 829.83,-3921.75"/>
<text text-anchor="start" x="774" y="-3909.2" font-family="Helvetica,sans-Serif" font-size="9.00">MindMap.css</text>
</a>
</g>
</g>
<!-- src/components/MindMap/MindMap.tsx -->
<g id="node45" class="node">
<title>src/components/MindMap/MindMap.tsx</title>
<g id="a_node45"><a xlink:href="src/components/MindMap/MindMap.tsx" xlink:title="MindMap.tsx">
<path fill="#bbfeff" stroke="black" d="M299.08,-3687.75C299.08,-3687.75 243.67,-3687.75 243.67,-3687.75 240.58,-3687.75 237.5,-3684.66 237.5,-3681.58 237.5,-3681.58 237.5,-3675.41 237.5,-3675.41 237.5,-3672.33 240.58,-3669.25 243.67,-3669.25 243.67,-3669.25 299.08,-3669.25 299.08,-3669.25 302.17,-3669.25 305.25,-3672.33 305.25,-3675.41 305.25,-3675.41 305.25,-3681.58 305.25,-3681.58 305.25,-3684.66 302.17,-3687.75 299.08,-3687.75"/>
<text text-anchor="start" x="245.5" y="-3675.2" font-family="Helvetica,sans-Serif" font-size="9.00">MindMap.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/MindMap.tsx&#45;&gt;node_modules/react -->
<g id="edge41" class="edge">
<title>src/components/MindMap/MindMap.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M283.46,-3668.89C300.56,-3653.25 332.7,-3620.39 345,-3584.5 351.89,-3564.4 342.05,-543.7 353,-525.5 405.52,-438.17 480.14,-486.36 555.25,-417.5 562.68,-410.69 558.77,-403.3 567.62,-398.5 614.06,-373.32 1005.55,-361.25 1043,-398.5 1057.79,-413.21 1037.96,-1884.21 1051,-1900.5 1057.69,-1908.85 1068.26,-1912.85 1078.74,-1914.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.22,-1916.66 1084.43,-1915.31 1078.73,-1912.49 1078.22,-1916.66"/>
</g>
<!-- src/components/index.ts -->
<g id="node46" class="node">
<title>src/components/index.ts</title>
<g id="a_node46"><a xlink:href="src/components/index.ts" xlink:title="index.ts">
<path fill="#ddfeff" stroke="black" d="M161.21,-4931.75C161.21,-4931.75 119.54,-4931.75 119.54,-4931.75 116.46,-4931.75 113.38,-4928.66 113.38,-4925.58 113.38,-4925.58 113.38,-4919.41 113.38,-4919.41 113.38,-4916.33 116.46,-4913.25 119.54,-4913.25 119.54,-4913.25 161.21,-4913.25 161.21,-4913.25 164.29,-4913.25 167.38,-4916.33 167.38,-4919.41 167.38,-4919.41 167.38,-4925.58 167.38,-4925.58 167.38,-4928.66 164.29,-4931.75 161.21,-4931.75"/>
<text text-anchor="start" x="124.62" y="-4919.2" font-family="Helvetica,sans-Serif" font-size="9.00">index.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/MindMap.tsx&#45;&gt;src/components/index.ts -->
<g id="edge40" class="edge">
<title>src/components/MindMap/MindMap.tsx&#45;&gt;src/components/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M247.7,-3688.1C229.46,-3697.16 205,-3712.63 193.25,-3734.5 135.56,-3841.82 140.16,-4761.54 141.22,-4903.99"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="139.12,-4903.87 141.27,-4909.86 143.32,-4903.84 139.12,-4903.87"/>
</g>
<!-- src/components/index.ts&#45;&gt;src/components/OptimizedMindMap_Modular.tsx -->
<g id="edge341" class="edge">
<title>src/components/index.ts&#45;&gt;src/components/OptimizedMindMap_Modular.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M141.44,-4931.99C142.19,-5040.81 150.27,-6050.43 194.37,-6151"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="195.57,-6157.19 194.33,-6150.95 199.19,-6155.05 195.57,-6157.19"/>
</g>
<!-- src/components/MindMap/Node.tsx -->
<g id="node47" class="node">
<title>src/components/MindMap/Node.tsx</title>
<g id="a_node47"><a xlink:href="src/components/MindMap/Node.tsx" xlink:title="Node.tsx">
<path fill="#bbfeff" stroke="black" d="M661.96,-3327.75C661.96,-3327.75 620.29,-3327.75 620.29,-3327.75 617.21,-3327.75 614.12,-3324.66 614.12,-3321.58 614.12,-3321.58 614.12,-3315.41 614.12,-3315.41 614.12,-3312.33 617.21,-3309.25 620.29,-3309.25 620.29,-3309.25 661.96,-3309.25 661.96,-3309.25 665.04,-3309.25 668.12,-3312.33 668.12,-3315.41 668.12,-3315.41 668.12,-3321.58 668.12,-3321.58 668.12,-3324.66 665.04,-3327.75 661.96,-3327.75"/>
<text text-anchor="start" x="623.12" y="-3315.2" font-family="Helvetica,sans-Serif" font-size="9.00">Node.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/Node.tsx&#45;&gt;node_modules/react -->
<g id="edge45" class="edge">
<title>src/components/MindMap/Node.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M668.57,-3320.13C684.35,-3319.53 703.33,-3315.73 714,-3302.5 736.14,-3275.03 704.02,-789.26 727,-762.5 775.07,-706.5 817.43,-742.29 891,-736.5 958.35,-731.19 995.07,-688.89 1043,-736.5 1054.47,-747.89 1040.88,-1887.89 1051,-1900.5 1057.7,-1908.84 1068.27,-1912.84 1078.75,-1914.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.22,-1916.66 1084.43,-1915.31 1078.74,-1912.49 1078.22,-1916.66"/>
</g>
<!-- src/components/MindMap/Node.tsx&#45;&gt;src/components/MindMap/types/index.ts -->
<g id="edge44" class="edge">
<title>src/components/MindMap/Node.tsx&#45;&gt;src/components/MindMap/types/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M668.32,-3319.07C729.21,-3320.55 875.64,-3324.87 883,-3332.5 896.62,-3346.61 884.8,-3666.88 891,-3685.5 902.91,-3721.27 931.59,-3755.4 949.83,-3774.57"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="948.22,-3775.94 953.91,-3778.77 951.23,-3773.01 948.22,-3775.94"/>
</g>
<!-- src/components/MindMap/Node.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx -->
<g id="edge42" class="edge">
<title>src/components/MindMap/Node.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M668.61,-3315.07C724.18,-3308.48 850.5,-3296.84 883,-3322.5 932.21,-3361.35 957.46,-3565.2 964.15,-3628.04"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="962.05,-3628.1 964.76,-3633.85 966.23,-3627.67 962.05,-3628.1"/>
</g>
<!-- src/components/MindMap/hooks/useNodeManagement.ts -->
<g id="node48" class="node">
<title>src/components/MindMap/hooks/useNodeManagement.ts</title>
<g id="a_node48"><a xlink:href="src/components/MindMap/hooks/useNodeManagement.ts" xlink:title="useNodeManagement.ts">
<path fill="#ddfeff" stroke="black" d="M853.08,-3640.75C853.08,-3640.75 748.92,-3640.75 748.92,-3640.75 745.83,-3640.75 742.75,-3637.66 742.75,-3634.58 742.75,-3634.58 742.75,-3628.41 742.75,-3628.41 742.75,-3625.33 745.83,-3622.25 748.92,-3622.25 748.92,-3622.25 853.08,-3622.25 853.08,-3622.25 856.17,-3622.25 859.25,-3625.33 859.25,-3628.41 859.25,-3628.41 859.25,-3634.58 859.25,-3634.58 859.25,-3637.66 856.17,-3640.75 853.08,-3640.75"/>
<text text-anchor="start" x="750.75" y="-3628.2" font-family="Helvetica,sans-Serif" font-size="9.00">useNodeManagement.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/Node.tsx&#45;&gt;src/components/MindMap/hooks/useNodeManagement.ts -->
<g id="edge43" class="edge">
<title>src/components/MindMap/Node.tsx&#45;&gt;src/components/MindMap/hooks/useNodeManagement.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M668.52,-3316.34C684.08,-3316.63 702.84,-3319.96 714,-3332.5 739.19,-3360.79 715.96,-3466.25 727,-3502.5 740.27,-3546.06 770.08,-3591.05 787.22,-3614.67"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="785.46,-3615.81 790.71,-3619.39 788.84,-3613.32 785.46,-3615.81"/>
</g>
<!-- src/components/MindMap/hooks/useNodeManagement.ts&#45;&gt;node_modules/react -->
<g id="edge245" class="edge">
<title>src/components/MindMap/hooks/useNodeManagement.ts&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M810.88,-3621.76C828.73,-3601.44 868.52,-3552.26 883,-3502.5 888.47,-3483.69 878.95,-2107.93 891,-2092.5 933.91,-2037.51 998.48,-2107.18 1043,-2053.5 1060.48,-2032.41 1033.57,-1951.62 1051,-1930.5 1057.81,-1922.25 1068.3,-1917.96 1078.67,-1915.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.72,-1917.93 1084.3,-1914.89 1078.04,-1913.79 1078.72,-1917.93"/>
</g>
<!-- src/components/MindMap/hooks/useNodeManagement.ts&#45;&gt;src/components/MindMap/types/index.ts -->
<g id="edge244" class="edge">
<title>src/components/MindMap/hooks/useNodeManagement.ts&#45;&gt;src/components/MindMap/types/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M859.7,-3631.89C868.37,-3634.3 876.55,-3638.26 883,-3644.5 896.35,-3657.4 883.66,-3668.44 891,-3685.5 905.99,-3720.31 933.93,-3754.98 951.15,-3774.48"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="949.37,-3775.64 954.95,-3778.7 952.5,-3772.83 949.37,-3775.64"/>
</g>
<!-- src/components/MindMap/hooks/useNodeManagement.ts&#45;&gt;src/components/MindMap/context/MindMapContext.tsx -->
<g id="edge243" class="edge">
<title>src/components/MindMap/hooks/useNodeManagement.ts&#45;&gt;src/components/MindMap/context/MindMapContext.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M859.36,-3636.74C875.26,-3638.2 892.52,-3639.77 908.43,-3641.23"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="907.93,-3643.29 914.1,-3641.75 908.32,-3639.11 907.93,-3643.29"/>
</g>
<!-- src/components/MindMap/NodeDialog.tsx -->
<g id="node49" class="node">
<title>src/components/MindMap/NodeDialog.tsx</title>
<g id="a_node49"><a xlink:href="src/components/MindMap/NodeDialog.tsx" xlink:title="NodeDialog.tsx">
<path fill="#bbfeff" stroke="black" d="M834.33,-3226.75C834.33,-3226.75 767.67,-3226.75 767.67,-3226.75 764.58,-3226.75 761.5,-3223.66 761.5,-3220.58 761.5,-3220.58 761.5,-3214.41 761.5,-3214.41 761.5,-3211.33 764.58,-3208.25 767.67,-3208.25 767.67,-3208.25 834.33,-3208.25 834.33,-3208.25 837.42,-3208.25 840.5,-3211.33 840.5,-3214.41 840.5,-3214.41 840.5,-3220.58 840.5,-3220.58 840.5,-3223.66 837.42,-3226.75 834.33,-3226.75"/>
<text text-anchor="start" x="769.5" y="-3214.2" font-family="Helvetica,sans-Serif" font-size="9.00">NodeDialog.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/NodeDialog.tsx&#45;&gt;node_modules/react -->
<g id="edge47" class="edge">
<title>src/components/MindMap/NodeDialog.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M808.82,-3207.75C825.48,-3183.24 868.03,-3116.52 883,-3053.5 889.67,-3025.41 875.98,-2039.14 891,-2014.5 931.24,-1948.49 1027.39,-1925.73 1078.63,-1918.13"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.7,-1920.24 1084.35,-1917.33 1078.12,-1916.08 1078.7,-1920.24"/>
</g>
<!-- src/components/MindMap/NodeDialog.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx -->
<g id="edge46" class="edge">
<title>src/components/MindMap/NodeDialog.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M840.92,-3216.4C855.91,-3218.01 872.04,-3222.57 883,-3233.5 940.02,-3290.32 960.35,-3554.83 964.93,-3627.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="962.83,-3627.91 965.29,-3633.77 967.02,-3627.65 962.83,-3627.91"/>
</g>
<!-- src/components/MindMap/ProjectDialog.tsx -->
<g id="node50" class="node">
<title>src/components/MindMap/ProjectDialog.tsx</title>
<g id="a_node50"><a xlink:href="src/components/MindMap/ProjectDialog.tsx" xlink:title="ProjectDialog.tsx">
<path fill="#bbfeff" stroke="black" d="M677.46,-3296.75C677.46,-3296.75 604.79,-3296.75 604.79,-3296.75 601.71,-3296.75 598.62,-3293.66 598.62,-3290.58 598.62,-3290.58 598.62,-3284.41 598.62,-3284.41 598.62,-3281.33 601.71,-3278.25 604.79,-3278.25 604.79,-3278.25 677.46,-3278.25 677.46,-3278.25 680.54,-3278.25 683.62,-3281.33 683.62,-3284.41 683.62,-3284.41 683.62,-3290.58 683.62,-3290.58 683.62,-3293.66 680.54,-3296.75 677.46,-3296.75"/>
<text text-anchor="start" x="606.62" y="-3284.2" font-family="Helvetica,sans-Serif" font-size="9.00">ProjectDialog.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/ProjectDialog.tsx&#45;&gt;node_modules/react -->
<g id="edge50" class="edge">
<title>src/components/MindMap/ProjectDialog.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M683.89,-3279.79C695.57,-3275.51 706.97,-3268.85 714,-3258.5 733.67,-3229.51 704.18,-763.07 727,-736.5 749.95,-709.76 1018.01,-685.67 1043,-710.5 1054.73,-722.14 1040.66,-1887.6 1051,-1900.5 1057.7,-1908.84 1068.27,-1912.84 1078.74,-1914.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.22,-1916.66 1084.43,-1915.31 1078.74,-1912.49 1078.22,-1916.66"/>
</g>
<!-- src/components/MindMap/ProjectDialog.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx -->
<g id="edge48" class="edge">
<title>src/components/MindMap/ProjectDialog.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M684.09,-3289C751.1,-3291.59 875.53,-3297.2 883,-3303.5 933.93,-3346.38 958.21,-3563.68 964.38,-3628.33"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="962.26,-3628.24 964.91,-3634.02 966.44,-3627.85 962.26,-3628.24"/>
</g>
<!-- src/components/MindMap/hooks/useProjectManagement.ts -->
<g id="node51" class="node">
<title>src/components/MindMap/hooks/useProjectManagement.ts</title>
<g id="a_node51"><a xlink:href="src/components/MindMap/hooks/useProjectManagement.ts" xlink:title="useProjectManagement.ts">
<path fill="#ddfeff" stroke="black" d="M856.08,-3496.75C856.08,-3496.75 745.92,-3496.75 745.92,-3496.75 742.83,-3496.75 739.75,-3493.66 739.75,-3490.58 739.75,-3490.58 739.75,-3484.41 739.75,-3484.41 739.75,-3481.33 742.83,-3478.25 745.92,-3478.25 745.92,-3478.25 856.08,-3478.25 856.08,-3478.25 859.17,-3478.25 862.25,-3481.33 862.25,-3484.41 862.25,-3484.41 862.25,-3490.58 862.25,-3490.58 862.25,-3493.66 859.17,-3496.75 856.08,-3496.75"/>
<text text-anchor="start" x="747.75" y="-3484.2" font-family="Helvetica,sans-Serif" font-size="9.00">useProjectManagement.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/ProjectDialog.tsx&#45;&gt;src/components/MindMap/hooks/useProjectManagement.ts -->
<g id="edge49" class="edge">
<title>src/components/MindMap/ProjectDialog.tsx&#45;&gt;src/components/MindMap/hooks/useProjectManagement.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M684.09,-3287.98C695.35,-3290.24 706.47,-3294.8 714,-3303.5 738.95,-3332.3 701.44,-3446.23 727,-3474.5 728.55,-3476.21 730.24,-3477.74 732.04,-3479.12"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="730.71,-3480.77 736.92,-3482.1 732.89,-3477.18 730.71,-3480.77"/>
</g>
<!-- src/components/MindMap/hooks/useProjectManagement.ts&#45;&gt;node_modules/react -->
<g id="edge248" class="edge">
<title>src/components/MindMap/hooks/useProjectManagement.ts&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M862.36,-3487.58C870.28,-3485 877.53,-3480.87 883,-3474.5 895.73,-3459.65 879.3,-2082.17 891,-2066.5 933.22,-2009.94 998.47,-2075.25 1043,-2020.5 1055.67,-2004.92 1038.04,-1945.83 1051,-1930.5 1057.84,-1922.41 1068.21,-1918.14 1078.49,-1915.97"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.83,-1918.04 1084.4,-1914.98 1078.14,-1913.9 1078.83,-1918.04"/>
</g>
<!-- src/components/MindMap/hooks/useProjectManagement.ts&#45;&gt;src/components/MindMap/types/index.ts -->
<g id="edge247" class="edge">
<title>src/components/MindMap/hooks/useProjectManagement.ts&#45;&gt;src/components/MindMap/types/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M810.17,-3497.06C827.07,-3517.45 865.72,-3567.35 883,-3616.5 893.24,-3645.62 879.9,-3656.69 891,-3685.5 904.56,-3720.68 932.71,-3754.99 950.41,-3774.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="948.71,-3775.63 954.34,-3778.59 951.79,-3772.77 948.71,-3775.63"/>
</g>
<!-- src/components/MindMap/hooks/useProjectManagement.ts&#45;&gt;src/components/MindMap/context/MindMapContext.tsx -->
<g id="edge246" class="edge">
<title>src/components/MindMap/hooks/useProjectManagement.ts&#45;&gt;src/components/MindMap/context/MindMapContext.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M811.77,-3496.97C839.05,-3523.41 916.26,-3598.27 949.95,-3630.93"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="948.13,-3632.1 953.9,-3634.77 951.06,-3629.08 948.13,-3632.1"/>
</g>
<!-- src/components/MindMap/Toolbar.tsx -->
<g id="node52" class="node">
<title>src/components/MindMap/Toolbar.tsx</title>
<g id="a_node52"><a xlink:href="src/components/MindMap/Toolbar.tsx" xlink:title="Toolbar.tsx">
<path fill="#bbfeff" stroke="black" d="M664.71,-3252.75C664.71,-3252.75 617.54,-3252.75 617.54,-3252.75 614.46,-3252.75 611.38,-3249.66 611.38,-3246.58 611.38,-3246.58 611.38,-3240.41 611.38,-3240.41 611.38,-3237.33 614.46,-3234.25 617.54,-3234.25 617.54,-3234.25 664.71,-3234.25 664.71,-3234.25 667.79,-3234.25 670.88,-3237.33 670.88,-3240.41 670.88,-3240.41 670.88,-3246.58 670.88,-3246.58 670.88,-3249.66 667.79,-3252.75 664.71,-3252.75"/>
<text text-anchor="start" x="619.38" y="-3240.2" font-family="Helvetica,sans-Serif" font-size="9.00">Toolbar.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/Toolbar.tsx&#45;&gt;node_modules/react -->
<g id="edge53" class="edge">
<title>src/components/MindMap/Toolbar.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M658.63,-3233.89C676.11,-3222.66 702.71,-3202.28 714,-3176.5 727.74,-3145.12 704.69,-736.48 727,-710.5 772.9,-657.03 993.01,-634.84 1043,-684.5 1054.98,-696.4 1040.43,-1887.32 1051,-1900.5 1057.7,-1908.84 1068.27,-1912.84 1078.74,-1914.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.22,-1916.66 1084.43,-1915.31 1078.74,-1912.49 1078.22,-1916.66"/>
</g>
<!-- src/components/MindMap/Toolbar.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx -->
<g id="edge51" class="edge">
<title>src/components/MindMap/Toolbar.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M671.01,-3251.23C687.35,-3255.37 708.2,-3260.27 727,-3263.5 761.37,-3269.39 856.97,-3257.29 883,-3280.5 936.07,-3327.82 958.98,-3560.5 964.58,-3628.07"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="962.47,-3627.97 965.04,-3633.78 966.65,-3627.63 962.47,-3627.97"/>
</g>
<!-- src/components/MindMap/Toolbar.tsx&#45;&gt;src/components/MindMap/hooks/useProjectManagement.ts -->
<g id="edge52" class="edge">
<title>src/components/MindMap/Toolbar.tsx&#45;&gt;src/components/MindMap/hooks/useProjectManagement.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M671.22,-3247.7C686.5,-3251.51 704.07,-3258.75 714,-3272.5 740.35,-3308.95 697.02,-3440.96 727,-3474.5 728.54,-3476.22 730.22,-3477.76 732.02,-3479.14"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="730.67,-3480.78 736.88,-3482.13 732.87,-3477.2 730.67,-3480.78"/>
</g>
<!-- src/components/MindMap/components/Agents/GovernanceAgent.tsx -->
<g id="node53" class="node">
<title>src/components/MindMap/components/Agents/GovernanceAgent.tsx</title>
<g id="a_node53"><a xlink:href="src/components/MindMap/components/Agents/GovernanceAgent.tsx" xlink:title="GovernanceAgent.tsx">
<path fill="#bbfeff" stroke="black" d="M507.83,-5164.75C507.83,-5164.75 416.42,-5164.75 416.42,-5164.75 413.33,-5164.75 410.25,-5161.66 410.25,-5158.58 410.25,-5158.58 410.25,-5152.41 410.25,-5152.41 410.25,-5149.33 413.33,-5146.25 416.42,-5146.25 416.42,-5146.25 507.83,-5146.25 507.83,-5146.25 510.92,-5146.25 514,-5149.33 514,-5152.41 514,-5152.41 514,-5158.58 514,-5158.58 514,-5161.66 510.92,-5164.75 507.83,-5164.75"/>
<text text-anchor="start" x="418.25" y="-5152.2" font-family="Helvetica,sans-Serif" font-size="9.00">GovernanceAgent.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Agents/GovernanceAgent.tsx&#45;&gt;node_modules/@mui/icons&#45;material -->
<g id="edge57" class="edge">
<title>src/components/MindMap/components/Agents/GovernanceAgent.tsx&#45;&gt;node_modules/@mui/icons&#45;material</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M514.14,-5155.05C529.73,-5152.19 545.35,-5146 555.25,-5133.5 574.41,-5109.29 547.84,-711.2 567.62,-687.5 610.02,-636.71 668.51,-708.53 714,-660.5 745.12,-627.63 693.02,-589.39 727,-559.5 779.73,-513.12 993.19,-510 1043,-559.5 1055.67,-572.09 1040.04,-1831.39 1051,-1845.5 1055.54,-1851.34 1061.94,-1855.22 1068.9,-1857.76"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1068.19,-1859.74 1074.54,-1859.44 1069.39,-1855.71 1068.19,-1859.74"/>
</g>
<!-- src/components/MindMap/components/Agents/GovernanceAgent.tsx&#45;&gt;node_modules/@mui/material -->
<g id="edge58" class="edge">
<title>src/components/MindMap/components/Agents/GovernanceAgent.tsx&#45;&gt;node_modules/@mui/material</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M514.14,-5155.05C529.73,-5152.19 545.35,-5146 555.25,-5133.5 574.52,-5109.14 551.75,-688.18 567.62,-661.5 605.53,-597.78 669.71,-640.95 714,-581.5 730.56,-559.27 705.45,-537.93 727,-520.5 780.89,-476.88 822.5,-486.62 883,-520.5 1001.72,-586.97 1005.55,-650.69 1043,-781.5 1046.96,-795.33 1041.9,-1806.35 1051,-1817.5 1057.62,-1825.61 1067.95,-1829.45 1078.24,-1831.09"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1077.97,-1833.18 1084.17,-1831.76 1078.44,-1829 1077.97,-1833.18"/>
</g>
<!-- src/components/MindMap/components/Agents/GovernanceAgent.tsx&#45;&gt;node_modules/react -->
<g id="edge59" class="edge">
<title>src/components/MindMap/components/Agents/GovernanceAgent.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M514.14,-5155.05C529.73,-5152.19 545.35,-5146 555.25,-5133.5 574.28,-5109.46 548.37,-742.35 567.62,-718.5 609.46,-666.68 669.16,-735.73 714,-686.5 745.96,-651.41 691.45,-611.95 727,-580.5 753.3,-557.23 1018.1,-555.74 1043,-580.5 1056,-593.42 1039.53,-1886.19 1051,-1900.5 1057.7,-1908.85 1068.26,-1912.85 1078.74,-1914.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.22,-1916.66 1084.43,-1915.31 1078.73,-1912.49 1078.22,-1916.66"/>
</g>
<!-- src/components/MindMap/components/Agents/GovernanceAgent.tsx&#45;&gt;src/components/ChatFork/ChatFork.css -->
<g id="edge55" class="edge">
<title>src/components/MindMap/components/Agents/GovernanceAgent.tsx&#45;&gt;src/components/ChatFork/ChatFork.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M514.25,-5152.54C529.65,-5154.43 545.14,-5159.64 555.25,-5171.5 571.51,-5190.56 558.14,-6050.3 567.62,-6073.5 611.69,-6181.19 728.02,-6268.5 777.36,-6301.88"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="776.01,-6303.5 782.17,-6305.09 778.34,-6300.01 776.01,-6303.5"/>
</g>
<!-- src/components/MindMap/components/Agents/GovernanceAgent.tsx&#45;&gt;src/components/ChatFork/ChatForkContainer.tsx -->
<g id="edge56" class="edge">
<title>src/components/MindMap/components/Agents/GovernanceAgent.tsx&#45;&gt;src/components/ChatFork/ChatForkContainer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M409.93,-5149.77C389.18,-5150.43 366.88,-5155.52 353,-5171.5 332.14,-5195.5 362.93,-6290.23 345,-6316.5 340.56,-6323 334.4,-6328.05 327.53,-6331.96"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="327,-6329.88 322.56,-6334.43 328.87,-6333.64 327,-6329.88"/>
</g>
<!-- src/components/MindMap/components/Agents/GovernanceAgent.tsx&#45;&gt;src/services/api/GovernanceLLM.ts -->
<g id="edge54" class="edge">
<title>src/components/MindMap/components/Agents/GovernanceAgent.tsx&#45;&gt;src/services/api/GovernanceLLM.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M514.29,-5152.51C529.69,-5154.39 545.17,-5159.61 555.25,-5171.5 582.47,-5203.59 547.68,-6649.44 567.62,-6686.5 626.59,-6796.02 675.61,-6842.5 800,-6842.5 800,-6842.5 800,-6842.5 968,-6842.5 1062.37,-6842.5 1091.75,-6848.35 1177.75,-6809.5 1197.03,-6800.79 1215.2,-6784.63 1227.2,-6772.44"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1228.49,-6774.13 1231.11,-6768.34 1225.45,-6771.23 1228.49,-6774.13"/>
</g>
<!-- src/components/MindMap/components/Agents/HatAgents.tsx -->
<g id="node54" class="node">
<title>src/components/MindMap/components/Agents/HatAgents.tsx</title>
<g id="a_node54"><a xlink:href="src/components/MindMap/components/Agents/HatAgents.tsx" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M492.08,-5195.75C492.08,-5195.75 432.17,-5195.75 432.17,-5195.75 429.08,-5195.75 426,-5192.66 426,-5189.58 426,-5189.58 426,-5183.41 426,-5183.41 426,-5180.33 429.08,-5177.25 432.17,-5177.25 432.17,-5177.25 492.08,-5177.25 492.08,-5177.25 495.17,-5177.25 498.25,-5180.33 498.25,-5183.41 498.25,-5183.41 498.25,-5189.58 498.25,-5189.58 498.25,-5192.66 495.17,-5195.75 492.08,-5195.75"/>
<text text-anchor="start" x="434" y="-5183.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">HatAgents.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Agents/SpecialistAgents.tsx -->
<g id="node55" class="node">
<title>src/components/MindMap/components/Agents/SpecialistAgents.tsx</title>
<g id="a_node55"><a xlink:href="src/components/MindMap/components/Agents/SpecialistAgents.tsx" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M504.83,-5226.75C504.83,-5226.75 419.42,-5226.75 419.42,-5226.75 416.33,-5226.75 413.25,-5223.66 413.25,-5220.58 413.25,-5220.58 413.25,-5214.41 413.25,-5214.41 413.25,-5211.33 416.33,-5208.25 419.42,-5208.25 419.42,-5208.25 504.83,-5208.25 504.83,-5208.25 507.92,-5208.25 511,-5211.33 511,-5214.41 511,-5214.41 511,-5220.58 511,-5220.58 511,-5223.66 507.92,-5226.75 504.83,-5226.75"/>
<text text-anchor="start" x="421.25" y="-5214.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">SpecialistAgents.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx&#45;&gt;node_modules/konva -->
<g id="edge65" class="edge">
<title>src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx&#45;&gt;node_modules/konva</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M705.28,-5664.1C708.53,-5662.04 711.49,-5659.53 714,-5656.5 733.83,-5632.5 704.95,-1195.48 727,-1173.5 826.9,-1073.88 952.31,-1095.43 1043,-1203.5 1055.97,-1218.94 1038.59,-1913.6 1051,-1929.5 1057.64,-1938 1068.19,-1942.33 1078.67,-1944.44"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.12,-1946.48 1084.38,-1945.33 1078.78,-1942.33 1078.12,-1946.48"/>
</g>
<!-- src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx&#45;&gt;node_modules/react -->
<g id="edge66" class="edge">
<title>src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M705.28,-5664.1C708.53,-5662.04 711.49,-5659.53 714,-5656.5 734.04,-5632.25 709.94,-1152.92 727,-1126.5 768.32,-1062.5 834.47,-1114.21 883,-1055.5 892.62,-1043.86 879.14,-1031.83 891,-1022.5 917.55,-1001.61 1019.01,-998.72 1043,-1022.5 1060.32,-1039.66 1035.71,-1881.49 1051,-1900.5 1057.59,-1908.69 1067.9,-1912.69 1078.2,-1914.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1077.91,-1916.58 1084.13,-1915.27 1078.45,-1912.42 1077.91,-1916.58"/>
</g>
<!-- src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx&#45;&gt;node_modules/react&#45;konva -->
<g id="edge67" class="edge">
<title>src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx&#45;&gt;node_modules/react&#45;konva</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M705.28,-5664.1C708.53,-5662.04 711.49,-5659.53 714,-5656.5 733.71,-5632.65 705.09,-1223.34 727,-1201.5 776.1,-1152.54 818.65,-1175.68 883,-1201.5 974.32,-1238.13 1003.79,-1264.25 1043,-1354.5 1050.39,-1371.51 1039.57,-2007.87 1051,-2022.5 1056.6,-2029.66 1064.97,-2033.86 1073.74,-2036.27"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1073.03,-2038.26 1079.34,-2037.51 1073.94,-2034.16 1073.03,-2038.26"/>
</g>
<!-- src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge62" class="edge">
<title>src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M705.22,-5664.05C708.49,-5662 711.46,-5659.51 714,-5656.5 738.13,-5627.78 704.95,-5011.84 727,-4981.5 770.13,-4922.14 835.25,-4983.2 883,-4927.5 895.94,-4912.4 879.93,-4900 891,-4883.5 901.19,-4868.3 917.99,-4856.89 933.11,-4849.02"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="933.65,-4851.1 938.11,-4846.56 931.79,-4847.33 933.65,-4851.1"/>
</g>
<!-- src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx&#45;&gt;src/components/MindMap/core/models/Connection.ts -->
<g id="edge60" class="edge">
<title>src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx&#45;&gt;src/components/MindMap/core/models/Connection.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M705.24,-5664.07C708.51,-5662.02 711.47,-5659.52 714,-5656.5 732,-5634.94 707.01,-4665.23 727,-4645.5 739.49,-4633.16 1030.25,-4633.43 1043,-4645.5 1054.72,-4656.59 1046.4,-4775.03 1051,-4790.5 1061.21,-4824.83 1084.49,-4859.92 1099.19,-4879.88"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1097.44,-4881.04 1102.72,-4884.57 1100.79,-4878.51 1097.44,-4881.04"/>
</g>
<!-- src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge61" class="edge">
<title>src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M705.49,-5663.82C708.65,-5661.81 711.53,-5659.4 714,-5656.5 742.69,-5622.81 699.52,-5293.17 727,-5258.5 771.6,-5202.23 814.23,-5237.13 883,-5216.5 1015.64,-5176.71 1080.55,-5214.13 1177.75,-5115.5 1225.06,-5067.49 1237.36,-4983.14 1240.54,-4945.97"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1242.61,-4946.36 1240.96,-4940.22 1238.42,-4946.05 1242.61,-4946.36"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.tsx -->
<g id="node59" class="node">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.tsx</title>
<g id="a_node59"><a xlink:href="src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.tsx" xlink:title="NodeDialog.tsx">
<path fill="#bbfeff" stroke="black" d="M1000.33,-5892.75C1000.33,-5892.75 933.67,-5892.75 933.67,-5892.75 930.58,-5892.75 927.5,-5889.66 927.5,-5886.58 927.5,-5886.58 927.5,-5880.41 927.5,-5880.41 927.5,-5877.33 930.58,-5874.25 933.67,-5874.25 933.67,-5874.25 1000.33,-5874.25 1000.33,-5874.25 1003.42,-5874.25 1006.5,-5877.33 1006.5,-5880.41 1006.5,-5880.41 1006.5,-5886.58 1006.5,-5886.58 1006.5,-5889.66 1003.42,-5892.75 1000.33,-5892.75"/>
<text text-anchor="start" x="935.5" y="-5880.2" font-family="Helvetica,sans-Serif" font-size="9.00">NodeDialog.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.tsx -->
<g id="edge63" class="edge">
<title>src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M705.44,-5661.59C761,-5657.73 839.54,-5662.58 883,-5711.5 906.35,-5737.78 867.86,-5843.03 891,-5869.5 898.02,-5877.52 908.03,-5881.88 918.47,-5884.09"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="918.03,-5886.15 924.29,-5885.01 918.68,-5882 918.03,-5886.15"/>
</g>
<!-- src/components/MindMap/components/Node/NodeRenderer.tsx -->
<g id="node60" class="node">
<title>src/components/MindMap/components/Node/NodeRenderer.tsx</title>
<g id="a_node60"><a xlink:href="src/components/MindMap/components/Node/NodeRenderer.tsx" xlink:title="NodeRenderer.tsx">
<path fill="#bbfeff" stroke="black" d="M839.96,-5410.75C839.96,-5410.75 762.04,-5410.75 762.04,-5410.75 758.96,-5410.75 755.88,-5407.66 755.88,-5404.58 755.88,-5404.58 755.88,-5398.41 755.88,-5398.41 755.88,-5395.33 758.96,-5392.25 762.04,-5392.25 762.04,-5392.25 839.96,-5392.25 839.96,-5392.25 843.04,-5392.25 846.12,-5395.33 846.12,-5398.41 846.12,-5398.41 846.12,-5404.58 846.12,-5404.58 846.12,-5407.66 843.04,-5410.75 839.96,-5410.75"/>
<text text-anchor="start" x="763.88" y="-5398.2" font-family="Helvetica,sans-Serif" font-size="9.00">NodeRenderer.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx&#45;&gt;src/components/MindMap/components/Node/NodeRenderer.tsx -->
<g id="edge64" class="edge">
<title>src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx&#45;&gt;src/components/MindMap/components/Node/NodeRenderer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M705.44,-5663.29C708.57,-5661.41 711.46,-5659.16 714,-5656.5 727.94,-5641.86 721.42,-5585.92 727,-5566.5 743.15,-5510.33 774.54,-5448.38 790.37,-5418.92"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="792.11,-5420.12 793.13,-5413.84 788.42,-5418.11 792.11,-5420.12"/>
</g>
<!-- src/components/MindMap/core/models/Connection.ts&#45;&gt;node_modules/uuid -->
<g id="edge197" class="edge">
<title>src/components/MindMap/core/models/Connection.ts&#45;&gt;node_modules/uuid</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1104.94,-4886.84C1089.91,-4868.41 1058.42,-4826.32 1047,-4784.5 1041.96,-4766.04 1041.46,-2047.81 1047,-2029.5 1051.03,-2016.18 1064.78,-2010.39 1078.7,-2008.05"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.76,-2010.16 1084.44,-2007.31 1078.22,-2005.99 1078.76,-2010.16"/>
</g>
<!-- src/components/MindMap/core/models/Node.ts&#45;&gt;node_modules/uuid -->
<g id="edge200" class="edge">
<title>src/components/MindMap/core/models/Node.ts&#45;&gt;node_modules/uuid</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1236.2,-4917.93C1224.21,-4896.16 1195.71,-4840.89 1185.75,-4790.5 1182.03,-4771.64 1189.59,-2038.63 1177.75,-2023.5 1170.61,-2014.37 1159.1,-2009.97 1147.82,-2007.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1148.22,-2005.91 1141.99,-2007.2 1147.66,-2010.08 1148.22,-2005.91"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.tsx&#45;&gt;node_modules/@mui/material -->
<g id="edge114" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.tsx&#45;&gt;node_modules/@mui/material</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1006.99,-5885.71C1020.43,-5884.3 1034.27,-5879.98 1043,-5869.5 1060.87,-5848.02 1033.91,-1868.59 1051,-1846.5 1057.6,-1837.96 1068.14,-1833.63 1078.63,-1831.52"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.73,-1833.63 1084.34,-1830.63 1078.09,-1829.48 1078.73,-1833.63"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.tsx&#45;&gt;node_modules/react -->
<g id="edge115" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1006.99,-5885.71C1020.43,-5884.3 1034.27,-5879.98 1043,-5869.5 1060.5,-5848.47 1034.26,-1952.13 1051,-1930.5 1057.6,-1921.96 1068.14,-1917.63 1078.63,-1915.52"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.73,-1917.63 1084.34,-1914.63 1078.09,-1913.48 1078.73,-1917.63"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.tsx&#45;&gt;src/services/api/GovernanceLLM.ts -->
<g id="edge110" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.tsx&#45;&gt;src/services/api/GovernanceLLM.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M978.98,-5893.03C995.41,-5907.91 1026.26,-5938.29 1043,-5970.5 1189.61,-6252.46 1232.12,-6648.51 1240.11,-6738.03"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1238.01,-6738.18 1240.62,-6743.97 1242.19,-6737.82 1238.01,-6738.18"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.tsx&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge111" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.tsx&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M991.54,-5893.2C1034.72,-5909.27 1126.82,-5935.15 1177.75,-5887.5 1213.52,-5854.02 1237.38,-5075.44 1241.11,-4945.74"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1243.2,-4946.03 1241.28,-4939.97 1239.01,-4945.91 1243.2,-4946.03"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.css -->
<g id="node76" class="node">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.css</title>
<g id="a_node76"><a xlink:href="src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.css" xlink:title="NodeDialog.css">
<path fill="#ffffcc" stroke="black" d="M1147.58,-5850.75C1147.58,-5850.75 1078.67,-5850.75 1078.67,-5850.75 1075.58,-5850.75 1072.5,-5847.66 1072.5,-5844.58 1072.5,-5844.58 1072.5,-5838.41 1072.5,-5838.41 1072.5,-5835.33 1075.58,-5832.25 1078.67,-5832.25 1078.67,-5832.25 1147.58,-5832.25 1147.58,-5832.25 1150.67,-5832.25 1153.75,-5835.33 1153.75,-5838.41 1153.75,-5838.41 1153.75,-5844.58 1153.75,-5844.58 1153.75,-5847.66 1150.67,-5850.75 1147.58,-5850.75"/>
<text text-anchor="start" x="1080.5" y="-5838.2" font-family="Helvetica,sans-Serif" font-size="9.00">NodeDialog.css</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.tsx&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.css -->
<g id="edge112" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.tsx&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1006.84,-5882.14C1019.19,-5880.22 1032.39,-5876.5 1043,-5869.5 1048.35,-5865.96 1045.78,-5861.22 1051,-5857.5 1054.91,-5854.71 1059.26,-5852.41 1063.8,-5850.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1064.34,-5852.55 1069.27,-5848.53 1062.91,-5848.6 1064.34,-5852.55"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/types.ts -->
<g id="node77" class="node">
<title>src/components/MindMap/components/Dialogs/NodeDialog/types.ts</title>
<g id="a_node77"><a xlink:href="src/components/MindMap/components/Dialogs/NodeDialog/types.ts" xlink:title="types.ts">
<path fill="#ddfeff" stroke="black" d="M1133.96,-5881.75C1133.96,-5881.75 1092.29,-5881.75 1092.29,-5881.75 1089.21,-5881.75 1086.12,-5878.66 1086.12,-5875.58 1086.12,-5875.58 1086.12,-5869.41 1086.12,-5869.41 1086.12,-5866.33 1089.21,-5863.25 1092.29,-5863.25 1092.29,-5863.25 1133.96,-5863.25 1133.96,-5863.25 1137.04,-5863.25 1140.12,-5866.33 1140.12,-5869.41 1140.12,-5869.41 1140.12,-5875.58 1140.12,-5875.58 1140.12,-5878.66 1137.04,-5881.75 1133.96,-5881.75"/>
<text text-anchor="start" x="1097.75" y="-5869.2" font-family="Helvetica,sans-Serif" font-size="9.00">types.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.tsx&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/types.ts -->
<g id="edge113" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.tsx&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/types.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1006.62,-5880.55C1028.51,-5878.88 1055.69,-5876.8 1076.98,-5875.18"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1076.86,-5877.29 1082.69,-5874.74 1076.55,-5873.1 1076.86,-5877.29"/>
</g>
<!-- src/components/MindMap/components/Node/NodeRenderer.tsx&#45;&gt;node_modules/konva -->
<g id="edge179" class="edge">
<title>src/components/MindMap/components/Node/NodeRenderer.tsx&#45;&gt;node_modules/konva</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M846.27,-5403.12C860.1,-5401.22 874.07,-5396.34 883,-5385.5 895.36,-5370.5 884.49,-2604.81 891,-2586.5 926.97,-2485.25 1006.25,-2499.46 1043,-2398.5 1051.31,-2375.68 1035.99,-1980.58 1051,-1961.5 1057.67,-1953.02 1068.23,-1948.69 1078.71,-1946.58"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.81,-1948.69 1084.41,-1945.68 1078.16,-1944.54 1078.81,-1948.69"/>
</g>
<!-- src/components/MindMap/components/Node/NodeRenderer.tsx&#45;&gt;node_modules/react -->
<g id="edge180" class="edge">
<title>src/components/MindMap/components/Node/NodeRenderer.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M846.27,-5403.12C860.1,-5401.22 874.07,-5396.34 883,-5385.5 896.85,-5368.68 877.25,-2265.39 891,-2248.5 934.57,-2194.94 998.76,-2269.49 1043,-2216.5 1063.37,-2192.09 1031.22,-1955.39 1051,-1930.5 1057.65,-1922.12 1068.1,-1917.8 1078.5,-1915.67"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.55,-1917.79 1084.14,-1914.76 1077.88,-1913.64 1078.55,-1917.79"/>
</g>
<!-- src/components/MindMap/components/Node/NodeRenderer.tsx&#45;&gt;node_modules/react&#45;konva -->
<g id="edge181" class="edge">
<title>src/components/MindMap/components/Node/NodeRenderer.tsx&#45;&gt;node_modules/react&#45;konva</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M846.26,-5403.11C860.09,-5401.21 874.06,-5396.33 883,-5385.5 890.78,-5376.08 890.11,-3638.68 891,-3626.5 925.95,-3146.56 1005.3,-3034.23 1043,-2554.5 1044.09,-2540.65 1042.42,-2065.42 1051,-2054.5 1056.61,-2047.35 1064.99,-2043.15 1073.76,-2040.74"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1073.96,-2042.85 1079.36,-2039.5 1073.05,-2038.75 1073.96,-2042.85"/>
</g>
<!-- src/components/MindMap/components/Node/NodeRenderer.tsx&#45;&gt;src/services/api/GovernanceLLM.ts -->
<g id="edge177" class="edge">
<title>src/components/MindMap/components/Node/NodeRenderer.tsx&#45;&gt;src/services/api/GovernanceLLM.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M808.58,-5410.95C825.08,-5435.32 867.98,-5502.82 883,-5566.5 886.69,-5582.14 880.1,-6711.69 891,-6723.5 893.71,-6726.43 1173.77,-6748.07 1177.75,-6748.5 1180.73,-6748.81 1183.79,-6749.15 1186.87,-6749.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1186.39,-6751.56 1192.59,-6750.17 1186.87,-6747.39 1186.39,-6751.56"/>
</g>
<!-- src/components/MindMap/components/Node/NodeRenderer.tsx&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge178" class="edge">
<title>src/components/MindMap/components/Node/NodeRenderer.tsx&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M808.54,-5410.96C824.95,-5435.35 867.67,-5502.9 883,-5566.5 886.12,-5579.46 881.49,-6037.14 891,-6046.5 936.43,-6091.18 1131.43,-6090.25 1177.75,-6046.5 1219.5,-6007.06 1238.57,-5088.7 1241.28,-4946.04"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1243.38,-4946.2 1241.39,-4940.16 1239.18,-4946.12 1243.38,-4946.2"/>
</g>
<!-- src/components/MindMap/components/Canvas/hooks/useCanvasInteraction.ts -->
<g id="node61" class="node">
<title>src/components/MindMap/components/Canvas/hooks/useCanvasInteraction.ts</title>
<g id="a_node61"><a xlink:href="src/components/MindMap/components/Canvas/hooks/useCanvasInteraction.ts" xlink:title="useCanvasInteraction.ts">
<path fill="#ddfeff" stroke="black" d="M692.08,-5564.75C692.08,-5564.75 590.17,-5564.75 590.17,-5564.75 587.08,-5564.75 584,-5561.66 584,-5558.58 584,-5558.58 584,-5552.41 584,-5552.41 584,-5549.33 587.08,-5546.25 590.17,-5546.25 590.17,-5546.25 692.08,-5546.25 692.08,-5546.25 695.17,-5546.25 698.25,-5549.33 698.25,-5552.41 698.25,-5552.41 698.25,-5558.58 698.25,-5558.58 698.25,-5561.66 695.17,-5564.75 692.08,-5564.75"/>
<text text-anchor="start" x="592" y="-5552.2" font-family="Helvetica,sans-Serif" font-size="9.00">useCanvasInteraction.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Canvas/hooks/useCanvasInteraction.ts&#45;&gt;node_modules/react -->
<g id="edge71" class="edge">
<title>src/components/MindMap/components/Canvas/hooks/useCanvasInteraction.ts&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M664.18,-5545.8C681.24,-5536.97 703.65,-5522.15 714,-5501.5 728.02,-5473.53 707.19,-1020.71 727,-996.5 771.82,-941.71 828.7,-1009.89 883,-964.5 890.35,-958.35 883.07,-949.86 891,-944.5 918.97,-925.56 1019.02,-920.71 1043,-944.5 1061.86,-963.19 1034.36,-1879.8 1051,-1900.5 1057.59,-1908.69 1067.9,-1912.69 1078.19,-1914.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1077.91,-1916.58 1084.13,-1915.27 1078.45,-1912.42 1077.91,-1916.58"/>
</g>
<!-- src/components/MindMap/components/Canvas/hooks/useCanvasInteraction.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge70" class="edge">
<title>src/components/MindMap/components/Canvas/hooks/useCanvasInteraction.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M663.73,-5545.92C680.77,-5537.08 703.38,-5522.18 714,-5501.5 728.75,-5472.77 705.7,-4944.76 727,-4920.5 773.27,-4867.77 824.55,-4935.28 883,-4896.5 888.65,-4892.74 886.25,-4888.33 891,-4883.5 904.65,-4869.6 923.04,-4857.75 938.19,-4849.3"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="938.84,-4851.34 943.12,-4846.64 936.84,-4847.65 938.84,-4851.34"/>
</g>
<!-- src/components/MindMap/components/Canvas/hooks/useCanvasInteraction.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge68" class="edge">
<title>src/components/MindMap/components/Canvas/hooks/useCanvasInteraction.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M663.66,-5545.89C680.66,-5537.03 703.25,-5522.12 714,-5501.5 733.12,-5464.82 700.62,-5161.35 727,-5129.5 858,-4971.29 1009.68,-5145.57 1177.75,-5027.5 1207.08,-5006.89 1226.05,-4968.09 1235.19,-4945.35"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1237.14,-4946.14 1237.33,-4939.78 1233.22,-4944.63 1237.14,-4946.14"/>
</g>
<!-- src/components/MindMap/core/operations/NodeOperations.ts -->
<g id="node62" class="node">
<title>src/components/MindMap/core/operations/NodeOperations.ts</title>
<g id="a_node62"><a xlink:href="src/components/MindMap/core/operations/NodeOperations.ts" xlink:title="NodeOperations.ts">
<path fill="#ddfeff" stroke="black" d="M1007.08,-4943.75C1007.08,-4943.75 926.92,-4943.75 926.92,-4943.75 923.83,-4943.75 920.75,-4940.66 920.75,-4937.58 920.75,-4937.58 920.75,-4931.41 920.75,-4931.41 920.75,-4928.33 923.83,-4925.25 926.92,-4925.25 926.92,-4925.25 1007.08,-4925.25 1007.08,-4925.25 1010.17,-4925.25 1013.25,-4928.33 1013.25,-4931.41 1013.25,-4931.41 1013.25,-4937.58 1013.25,-4937.58 1013.25,-4940.66 1010.17,-4943.75 1007.08,-4943.75"/>
<text text-anchor="start" x="928.75" y="-4931.2" font-family="Helvetica,sans-Serif" font-size="9.00">NodeOperations.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Canvas/hooks/useCanvasInteraction.ts&#45;&gt;src/components/MindMap/core/operations/NodeOperations.ts -->
<g id="edge69" class="edge">
<title>src/components/MindMap/components/Canvas/hooks/useCanvasInteraction.ts&#45;&gt;src/components/MindMap/core/operations/NodeOperations.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M663.7,-5545.91C680.73,-5537.06 703.32,-5522.15 714,-5501.5 738.14,-5454.79 698.31,-5072.55 727,-5028.5 767.07,-4966.96 854.49,-4945.7 911.76,-4938.35"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="911.97,-4940.45 917.68,-4937.65 911.47,-4936.27 911.97,-4940.45"/>
</g>
<!-- src/components/MindMap/core/operations/NodeOperations.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge204" class="edge">
<title>src/components/MindMap/core/operations/NodeOperations.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M920.47,-4935.6C905.36,-4933.15 891.34,-4926.83 887,-4912.5 883.23,-4900.04 881.69,-4892.58 891,-4883.5 905.17,-4869.67 923.97,-4857.76 939.32,-4849.26"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="940.03,-4851.26 944.32,-4846.57 938.05,-4847.56 940.03,-4851.26"/>
</g>
<!-- src/components/MindMap/core/operations/NodeOperations.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge203" class="edge">
<title>src/components/MindMap/core/operations/NodeOperations.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1013.38,-4937.31C1023.94,-4939.73 1034.55,-4943.79 1043,-4950.5 1049.54,-4955.68 1043.95,-4963.02 1051,-4967.5 1074.78,-4982.59 1150.68,-4975.27 1177.75,-4967.5 1194.83,-4962.59 1211.76,-4951.63 1223.9,-4942.42"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1225.01,-4944.22 1228.42,-4938.86 1222.41,-4940.93 1225.01,-4944.22"/>
</g>
<!-- src/components/MindMap/components/Canvas/hooks/useCanvasRendering.ts -->
<g id="node63" class="node">
<title>src/components/MindMap/components/Canvas/hooks/useCanvasRendering.ts</title>
<g id="a_node63"><a xlink:href="src/components/MindMap/components/Canvas/hooks/useCanvasRendering.ts" xlink:title="useCanvasRendering.ts">
<path fill="#ddfeff" stroke="black" d="M692.46,-5595.75C692.46,-5595.75 589.79,-5595.75 589.79,-5595.75 586.71,-5595.75 583.62,-5592.66 583.62,-5589.58 583.62,-5589.58 583.62,-5583.41 583.62,-5583.41 583.62,-5580.33 586.71,-5577.25 589.79,-5577.25 589.79,-5577.25 692.46,-5577.25 692.46,-5577.25 695.54,-5577.25 698.62,-5580.33 698.62,-5583.41 698.62,-5583.41 698.62,-5589.58 698.62,-5589.58 698.62,-5592.66 695.54,-5595.75 692.46,-5595.75"/>
<text text-anchor="start" x="591.62" y="-5583.2" font-family="Helvetica,sans-Serif" font-size="9.00">useCanvasRendering.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Canvas/hooks/useCanvasRendering.ts&#45;&gt;node_modules/react -->
<g id="edge75" class="edge">
<title>src/components/MindMap/components/Canvas/hooks/useCanvasRendering.ts&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M699.06,-5581.76C704.81,-5579.12 709.99,-5575.48 714,-5570.5 733.79,-5545.88 707,-1046.94 727,-1022.5 771.82,-967.71 828.7,-1035.89 883,-990.5 890.35,-984.35 883.07,-975.86 891,-970.5 918.97,-951.56 1019.01,-946.71 1043,-970.5 1061.35,-988.68 1034.81,-1880.36 1051,-1900.5 1057.59,-1908.69 1067.9,-1912.69 1078.19,-1914.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1077.91,-1916.58 1084.13,-1915.27 1078.45,-1912.42 1077.91,-1916.58"/>
</g>
<!-- src/components/MindMap/components/Canvas/hooks/useCanvasRendering.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts -->
<g id="edge72" class="edge">
<title>src/components/MindMap/components/Canvas/hooks/useCanvasRendering.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M699.01,-5581.72C704.77,-5579.09 709.97,-5575.45 714,-5570.5 730.46,-5550.26 708.43,-4649.81 727,-4631.5 739.5,-4619.17 1030.28,-4619.4 1043,-4631.5 1055.82,-4643.68 1046,-4773.53 1051,-4790.5 1061.13,-4824.85 1084.43,-4859.94 1099.16,-4879.88"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1097.41,-4881.05 1102.7,-4884.57 1100.76,-4878.52 1097.41,-4881.05"/>
</g>
<!-- src/components/MindMap/components/Canvas/hooks/useCanvasRendering.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge73" class="edge">
<title>src/components/MindMap/components/Canvas/hooks/useCanvasRendering.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M698.9,-5581.63C704.67,-5579.01 709.91,-5575.41 714,-5570.5 738.7,-5540.85 704.46,-5254.81 727,-5223.5 852.06,-5049.71 1018.36,-5196.46 1177.75,-5053.5 1210.9,-5023.76 1229.16,-4972.81 1236.98,-4945.71"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1238.95,-4946.45 1238.51,-4940.11 1234.9,-4945.34 1238.95,-4946.45"/>
</g>
<!-- src/components/MindMap/components/Canvas/types/canvas.types.ts -->
<g id="node64" class="node">
<title>src/components/MindMap/components/Canvas/types/canvas.types.ts</title>
<g id="a_node64"><a xlink:href="src/components/MindMap/components/Canvas/types/canvas.types.ts" xlink:title="canvas.types.ts">
<path fill="#ddfeff" stroke="black" d="M833.96,-5595.75C833.96,-5595.75 768.04,-5595.75 768.04,-5595.75 764.96,-5595.75 761.88,-5592.66 761.88,-5589.58 761.88,-5589.58 761.88,-5583.41 761.88,-5583.41 761.88,-5580.33 764.96,-5577.25 768.04,-5577.25 768.04,-5577.25 833.96,-5577.25 833.96,-5577.25 837.04,-5577.25 840.12,-5580.33 840.12,-5583.41 840.12,-5583.41 840.12,-5589.58 840.12,-5589.58 840.12,-5592.66 837.04,-5595.75 833.96,-5595.75"/>
<text text-anchor="start" x="769.88" y="-5583.2" font-family="Helvetica,sans-Serif" font-size="9.00">canvas.types.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Canvas/hooks/useCanvasRendering.ts&#45;&gt;src/components/MindMap/components/Canvas/types/canvas.types.ts -->
<g id="edge74" class="edge">
<title>src/components/MindMap/components/Canvas/hooks/useCanvasRendering.ts&#45;&gt;src/components/MindMap/components/Canvas/types/canvas.types.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M699.12,-5586.5C716.87,-5586.5 736.23,-5586.5 753.2,-5586.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="752.81,-5588.6 758.81,-5586.5 752.81,-5584.4 752.81,-5588.6"/>
</g>
<!-- src/components/MindMap/components/Canvas/types/canvas.types.ts&#45;&gt;src/components/MindMap/types/index.ts -->
<g id="edge82" class="edge">
<title>src/components/MindMap/components/Canvas/types/canvas.types.ts&#45;&gt;src/components/MindMap/types/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M809.57,-5576.98C826.73,-5554.72 868.26,-5496.86 883,-5440.5 888.74,-5418.54 876.68,-3824.1 891,-3806.5 900.46,-3794.86 916.11,-3790.37 930.68,-3788.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="930.65,-3791.09 936.52,-3788.66 930.41,-3786.89 930.65,-3791.09"/>
</g>
<!-- src/components/MindMap/components/Canvas/types/canvas.types.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts -->
<g id="edge80" class="edge">
<title>src/components/MindMap/components/Canvas/types/canvas.types.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M808.04,-5576.85C822.29,-5553.86 858.64,-5493.74 883,-5440.5 968.88,-5252.8 998.11,-5205.97 1043,-5004.5 1047.46,-4984.46 1037.77,-4928.18 1051,-4912.5 1055.53,-4907.13 1061.61,-4903.45 1068.18,-4900.94"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1068.67,-4902.99 1073.8,-4899.24 1067.45,-4898.97 1068.67,-4902.99"/>
</g>
<!-- src/components/MindMap/components/Canvas/types/canvas.types.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge81" class="edge">
<title>src/components/MindMap/components/Canvas/types/canvas.types.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M811.08,-5596.2C828.93,-5616 868.16,-5663.3 883,-5711.5 888.82,-5730.4 876.86,-6053.66 891,-6067.5 936.56,-6112.04 1131.61,-6110.45 1177.75,-6066.5 1220.11,-6026.14 1238.7,-5089.62 1241.3,-4945.92"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1243.4,-4946.03 1241.41,-4940 1239.2,-4945.96 1243.4,-4946.03"/>
</g>
<!-- src/components/MindMap/components/Canvas/hooks/useCanvasState.ts -->
<g id="node65" class="node">
<title>src/components/MindMap/components/Canvas/hooks/useCanvasState.ts</title>
<g id="a_node65"><a xlink:href="src/components/MindMap/components/Canvas/hooks/useCanvasState.ts" xlink:title="useCanvasState.ts">
<path fill="#ddfeff" stroke="black" d="M681.21,-5626.75C681.21,-5626.75 601.04,-5626.75 601.04,-5626.75 597.96,-5626.75 594.88,-5623.66 594.88,-5620.58 594.88,-5620.58 594.88,-5614.41 594.88,-5614.41 594.88,-5611.33 597.96,-5608.25 601.04,-5608.25 601.04,-5608.25 681.21,-5608.25 681.21,-5608.25 684.29,-5608.25 687.38,-5611.33 687.38,-5614.41 687.38,-5614.41 687.38,-5620.58 687.38,-5620.58 687.38,-5623.66 684.29,-5626.75 681.21,-5626.75"/>
<text text-anchor="start" x="602.88" y="-5614.2" font-family="Helvetica,sans-Serif" font-size="9.00">useCanvasState.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Canvas/hooks/useCanvasState.ts&#45;&gt;node_modules/react -->
<g id="edge79" class="edge">
<title>src/components/MindMap/components/Canvas/hooks/useCanvasState.ts&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M687.87,-5616.6C697.93,-5614.13 707.51,-5609.56 714,-5601.5 733.7,-5577 709.02,-1100.28 727,-1074.5 769.31,-1013.82 830.06,-1068.15 883,-1016.5 889.85,-1009.81 883.07,-1001.86 891,-996.5 918.97,-977.56 1019.01,-972.72 1043,-996.5 1060.84,-1014.17 1035.26,-1880.93 1051,-1900.5 1057.59,-1908.69 1067.9,-1912.69 1078.2,-1914.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1077.91,-1916.58 1084.13,-1915.27 1078.45,-1912.42 1077.91,-1916.58"/>
</g>
<!-- src/components/MindMap/components/Canvas/hooks/useCanvasState.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge77" class="edge">
<title>src/components/MindMap/components/Canvas/hooks/useCanvasState.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M687.78,-5616.52C697.84,-5614.05 707.44,-5609.51 714,-5601.5 737.47,-5572.81 702.42,-4962.23 727,-4934.5 773.44,-4882.09 830.4,-4958.71 883,-4912.5 893.04,-4903.67 883.07,-4894.26 891,-4883.5 902.02,-4868.56 919.2,-4857.07 934.34,-4849.09"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="934.9,-4851.15 939.32,-4846.59 933.01,-4847.4 934.9,-4851.15"/>
</g>
<!-- src/components/MindMap/components/Canvas/hooks/useCanvasState.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge76" class="edge">
<title>src/components/MindMap/components/Canvas/hooks/useCanvasState.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M687.68,-5616.45C697.75,-5613.98 707.38,-5609.45 714,-5601.5 739.53,-5570.81 703.17,-5274.51 727,-5242.5 758.58,-5200.07 1139.46,-5125.98 1177.75,-5089.5 1219.58,-5049.64 1234.56,-4978.99 1239.46,-4945.75"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1241.5,-4946.29 1240.23,-4940.06 1237.34,-4945.73 1241.5,-4946.29"/>
</g>
<!-- src/components/MindMap/components/Canvas/hooks/useCanvasState.ts&#45;&gt;src/components/MindMap/components/Canvas/types/canvas.types.ts -->
<g id="edge78" class="edge">
<title>src/components/MindMap/components/Canvas/hooks/useCanvasState.ts&#45;&gt;src/components/MindMap/components/Canvas/types/canvas.types.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M686.56,-5607.79C699.57,-5605.02 713.84,-5602.06 727,-5599.5 735.35,-5597.87 744.25,-5596.21 752.86,-5594.66"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="753.18,-5596.73 758.71,-5593.61 752.43,-5592.6 753.18,-5596.73"/>
</g>
<!-- src/components/MindMap/components/ControlPanel/ManualNodeControls.tsx -->
<g id="node66" class="node">
<title>src/components/MindMap/components/ControlPanel/ManualNodeControls.tsx</title>
<g id="a_node66"><a xlink:href="src/components/MindMap/components/ControlPanel/ManualNodeControls.tsx" xlink:title="ManualNodeControls.tsx">
<path fill="#bbfeff" stroke="black" d="M514.58,-5103.75C514.58,-5103.75 409.67,-5103.75 409.67,-5103.75 406.58,-5103.75 403.5,-5100.66 403.5,-5097.58 403.5,-5097.58 403.5,-5091.41 403.5,-5091.41 403.5,-5088.33 406.58,-5085.25 409.67,-5085.25 409.67,-5085.25 514.58,-5085.25 514.58,-5085.25 517.67,-5085.25 520.75,-5088.33 520.75,-5091.41 520.75,-5091.41 520.75,-5097.58 520.75,-5097.58 520.75,-5100.66 517.67,-5103.75 514.58,-5103.75"/>
<text text-anchor="start" x="411.5" y="-5091.2" font-family="Helvetica,sans-Serif" font-size="9.00">ManualNodeControls.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/ControlPanel/ManualNodeControls.tsx&#45;&gt;node_modules/@mui/icons&#45;material -->
<g id="edge85" class="edge">
<title>src/components/MindMap/components/ControlPanel/ManualNodeControls.tsx&#45;&gt;node_modules/@mui/icons&#45;material</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M520.89,-5098.48C533.97,-5096.4 546.55,-5091.48 555.25,-5081.5 575.65,-5058.08 547.72,-633.34 567.62,-609.5 610.02,-558.71 666.94,-628.99 714,-582.5 735.27,-561.48 703.91,-535.48 727,-516.5 835.48,-427.3 943.39,-417.49 1043,-516.5 1056.09,-529.51 1039.68,-1830.92 1051,-1845.5 1055.54,-1851.34 1061.94,-1855.22 1068.9,-1857.76"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1068.19,-1859.74 1074.54,-1859.44 1069.39,-1855.71 1068.19,-1859.74"/>
</g>
<!-- src/components/MindMap/components/ControlPanel/ManualNodeControls.tsx&#45;&gt;node_modules/@mui/material -->
<g id="edge86" class="edge">
<title>src/components/MindMap/components/ControlPanel/ManualNodeControls.tsx&#45;&gt;node_modules/@mui/material</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M520.89,-5098.48C533.97,-5096.4 546.55,-5091.48 555.25,-5081.5 575.77,-5057.94 547.37,-607.27 567.62,-583.5 610.37,-533.31 670.44,-608.98 714,-559.5 741.03,-528.78 697.65,-220 727,-191.5 777.37,-142.57 993.22,-140.96 1043,-190.5 1059.02,-206.43 1036.74,-1799.97 1051,-1817.5 1057.61,-1825.62 1067.93,-1829.47 1078.22,-1831.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1077.96,-1833.19 1084.15,-1831.77 1078.43,-1829.01 1077.96,-1833.19"/>
</g>
<!-- src/components/MindMap/components/ControlPanel/ManualNodeControls.tsx&#45;&gt;node_modules/react -->
<g id="edge87" class="edge">
<title>src/components/MindMap/components/ControlPanel/ManualNodeControls.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M520.89,-5098.48C533.97,-5096.4 546.55,-5091.48 555.25,-5081.5 575.39,-5058.38 550.36,-691.83 567.62,-666.5 607.03,-608.67 668.13,-661.34 714,-608.5 730.18,-589.85 707.61,-569.77 727,-554.5 782.16,-511.04 993.2,-504.99 1043,-554.5 1056.26,-567.67 1039.31,-1885.91 1051,-1900.5 1057.69,-1908.85 1068.26,-1912.85 1078.74,-1914.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.22,-1916.66 1084.43,-1915.31 1078.73,-1912.49 1078.22,-1916.66"/>
</g>
<!-- src/components/MindMap/components/ControlPanel/ManualNodeControls.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge84" class="edge">
<title>src/components/MindMap/components/ControlPanel/ManualNodeControls.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M520.83,-5098.43C533.91,-5096.35 546.51,-5091.44 555.25,-5081.5 575.33,-5058.65 552.2,-4012.72 567.62,-3986.5 577.96,-3968.92 707.29,-3900.73 727,-3895.5 743.75,-3891.05 870.69,-3883.29 883,-3895.5 900.66,-3913 878.65,-4768.92 891,-4790.5 899.21,-4804.84 913.99,-4815.12 928.28,-4822.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="927.04,-4823.94 933.37,-4824.55 928.8,-4820.13 927.04,-4823.94"/>
</g>
<!-- src/components/MindMap/components/ControlPanel/ManualNodeControls.tsx&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge83" class="edge">
<title>src/components/MindMap/components/ControlPanel/ManualNodeControls.tsx&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M520.84,-5098.44C533.92,-5096.36 546.51,-5091.45 555.25,-5081.5 576.93,-5056.8 545.23,-3922.54 567.62,-3898.5 591.53,-3872.83 856.59,-3862.42 883,-3885.5 902.93,-3902.9 876.27,-3922.51 891,-3944.5 934.45,-4009.34 1003.31,-3965.28 1043,-4032.5 1053.91,-4050.96 1036.34,-4402.84 1051,-4418.5 1070.32,-4439.13 1158.41,-4408.87 1177.75,-4429.5 1191.47,-4444.13 1181.67,-4770.85 1185.75,-4790.5 1195.13,-4835.69 1218.87,-4884.93 1232.19,-4910.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1230.17,-4910.95 1234.85,-4915.25 1233.87,-4908.97 1230.17,-4910.95"/>
</g>
<!-- src/components/MindMap/components/Controls/LLMControls.tsx -->
<g id="node67" class="node">
<title>src/components/MindMap/components/Controls/LLMControls.tsx</title>
<g id="a_node67"><a xlink:href="src/components/MindMap/components/Controls/LLMControls.tsx" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M675.96,-5409.75C675.96,-5409.75 606.29,-5409.75 606.29,-5409.75 603.21,-5409.75 600.12,-5406.66 600.12,-5403.58 600.12,-5403.58 600.12,-5397.41 600.12,-5397.41 600.12,-5394.33 603.21,-5391.25 606.29,-5391.25 606.29,-5391.25 675.96,-5391.25 675.96,-5391.25 679.04,-5391.25 682.12,-5394.33 682.12,-5397.41 682.12,-5397.41 682.12,-5403.58 682.12,-5403.58 682.12,-5406.66 679.04,-5409.75 675.96,-5409.75"/>
<text text-anchor="start" x="608.12" y="-5397.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">LLMControls.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Controls/MindMapControls.tsx -->
<g id="node68" class="node">
<title>src/components/MindMap/components/Controls/MindMapControls.tsx</title>
<g id="a_node68"><a xlink:href="src/components/MindMap/components/Controls/MindMapControls.tsx" xlink:title="MindMapControls.tsx">
<path fill="#bbfeff" stroke="black" d="M686.08,-5471.75C686.08,-5471.75 596.17,-5471.75 596.17,-5471.75 593.08,-5471.75 590,-5468.66 590,-5465.58 590,-5465.58 590,-5459.41 590,-5459.41 590,-5456.33 593.08,-5453.25 596.17,-5453.25 596.17,-5453.25 686.08,-5453.25 686.08,-5453.25 689.17,-5453.25 692.25,-5456.33 692.25,-5459.41 692.25,-5459.41 692.25,-5465.58 692.25,-5465.58 692.25,-5468.66 689.17,-5471.75 686.08,-5471.75"/>
<text text-anchor="start" x="598" y="-5459.2" font-family="Helvetica,sans-Serif" font-size="9.00">MindMapControls.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Controls/MindMapControls.tsx&#45;&gt;../../services/api/GovernanceLLM -->
<g id="edge90" class="edge">
<title>src/components/MindMap/components/Controls/MindMapControls.tsx&#45;&gt;../../services/api/GovernanceLLM</title>
<g id="a_edge90"><a xlink:title="not&#45;to&#45;unresolvable">
<path fill="none" stroke="red" stroke-width="2" d="M692.67,-5460.24C700.93,-5457.57 708.55,-5453.27 714,-5446.5 732.48,-5423.52 706.12,-1222.31 727,-1201.5 776.1,-1152.54 826.49,-1161.32 883,-1201.5 891.82,-1207.77 886.79,-1214.52 891,-1224.5 911.82,-1273.85 941.45,-1329.73 956.47,-1357.27"/>
<polygon fill="red" stroke="red" stroke-width="2" points="954.46,-1357.97 959.19,-1362.22 958.14,-1355.95 954.46,-1357.97"/>
</a>
</g>
<text text-anchor="middle" x="759.49" y="-3403.93" font-family="Helvetica,sans-Serif" font-size="9.00" fill="red">not&#45;to&#45;unresolvable</text>
</g>
<!-- src/components/MindMap/components/Controls/MindMapControls.tsx&#45;&gt;node_modules/react -->
<g id="edge91" class="edge">
<title>src/components/MindMap/components/Controls/MindMapControls.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M692.67,-5460.24C700.93,-5457.57 708.55,-5453.27 714,-5446.5 733.48,-5422.27 707.32,-994.55 727,-970.5 771.82,-915.71 828.7,-983.89 883,-938.5 890.35,-932.35 883.07,-923.86 891,-918.5 918.97,-899.56 1019.02,-894.71 1043,-918.5 1062.37,-937.71 1033.91,-1879.23 1051,-1900.5 1057.58,-1908.69 1067.9,-1912.69 1078.19,-1914.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1077.91,-1916.58 1084.13,-1915.27 1078.45,-1912.42 1077.91,-1916.58"/>
</g>
<!-- src/components/MindMap/components/Controls/MindMapControls.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge88" class="edge">
<title>src/components/MindMap/components/Controls/MindMapControls.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M692.55,-5460.15C700.82,-5457.49 708.48,-5453.21 714,-5446.5 733.05,-5423.31 707.54,-4929.34 727,-4906.5 772.73,-4852.81 816.2,-4900.09 883,-4877.5 904.17,-4870.34 926.7,-4858.65 942.94,-4849.42"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="943.61,-4851.45 947.75,-4846.63 941.5,-4847.82 943.61,-4851.45"/>
</g>
<!-- src/components/MindMap/components/Controls/MindMapControls.tsx&#45;&gt;src/components/MindMap/MindMap.css -->
<g id="edge89" class="edge">
<title>src/components/MindMap/components/Controls/MindMapControls.tsx&#45;&gt;src/components/MindMap/MindMap.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M692.63,-5460.22C700.9,-5457.55 708.53,-5453.25 714,-5446.5 740.49,-5413.75 700.89,-3963.54 727,-3930.5 734.29,-3921.27 745.48,-3916.31 756.93,-3913.75"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="757.14,-3915.85 762.69,-3912.75 756.42,-3911.71 757.14,-3915.85"/>
</g>
<!-- src/components/MindMap/components/Controls/MindMapToolbar.tsx -->
<g id="node69" class="node">
<title>src/components/MindMap/components/Controls/MindMapToolbar.tsx</title>
<g id="a_node69"><a xlink:href="src/components/MindMap/components/Controls/MindMapToolbar.tsx" xlink:title="MindMapToolbar.tsx">
<path fill="#bbfeff" stroke="black" d="M683.83,-5440.75C683.83,-5440.75 598.42,-5440.75 598.42,-5440.75 595.33,-5440.75 592.25,-5437.66 592.25,-5434.58 592.25,-5434.58 592.25,-5428.41 592.25,-5428.41 592.25,-5425.33 595.33,-5422.25 598.42,-5422.25 598.42,-5422.25 683.83,-5422.25 683.83,-5422.25 686.92,-5422.25 690,-5425.33 690,-5428.41 690,-5428.41 690,-5434.58 690,-5434.58 690,-5437.66 686.92,-5440.75 683.83,-5440.75"/>
<text text-anchor="start" x="600.25" y="-5428.2" font-family="Helvetica,sans-Serif" font-size="9.00">MindMapToolbar.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Controls/MindMapToolbar.tsx&#45;&gt;node_modules/react -->
<g id="edge95" class="edge">
<title>src/components/MindMap/components/Controls/MindMapToolbar.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M690.47,-5429.91C699.56,-5427.31 708.07,-5422.87 714,-5415.5 733.46,-5391.3 707.41,-968.59 727,-944.5 771.71,-889.51 828.36,-956.63 883,-911.5 890.06,-905.66 883.37,-897.56 891,-892.5 919.14,-873.81 1019.02,-868.71 1043,-892.5 1062.88,-912.22 1033.46,-1878.67 1051,-1900.5 1057.58,-1908.69 1067.9,-1912.7 1078.19,-1914.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1077.91,-1916.59 1084.13,-1915.27 1078.44,-1912.42 1077.91,-1916.59"/>
</g>
<!-- src/components/MindMap/components/Controls/MindMapToolbar.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx -->
<g id="edge92" class="edge">
<title>src/components/MindMap/components/Controls/MindMapToolbar.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M690.44,-5429.89C699.54,-5427.29 708.05,-5422.85 714,-5415.5 742.99,-5379.63 695.32,-3789 727,-3755.5 738.93,-3742.87 867.5,-3753.34 883,-3745.5 918.47,-3727.54 944.61,-3687 957.34,-3663.75"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="959.05,-3665 960,-3658.71 955.34,-3663.04 959.05,-3665"/>
</g>
<!-- src/components/MindMap/components/Controls/MindMapToolbar.tsx&#45;&gt;src/components/MindMap/hooks/useNodeManagement.ts -->
<g id="edge93" class="edge">
<title>src/components/MindMap/components/Controls/MindMapToolbar.tsx&#45;&gt;src/components/MindMap/hooks/useNodeManagement.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M690.44,-5429.89C699.54,-5427.29 708.05,-5422.85 714,-5415.5 744.92,-5377.23 695.27,-3682.09 727,-3644.5 729.21,-3641.88 731.74,-3639.67 734.51,-3637.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="735.35,-3639.74 739.66,-3635.07 733.37,-3636.03 735.35,-3639.74"/>
</g>
<!-- src/components/MindMap/components/Controls/MindMapToolbar.tsx&#45;&gt;src/components/MindMap/hooks/useProjectManagement.ts -->
<g id="edge94" class="edge">
<title>src/components/MindMap/components/Controls/MindMapToolbar.tsx&#45;&gt;src/components/MindMap/hooks/useProjectManagement.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M690.44,-5429.89C699.54,-5427.3 708.06,-5422.85 714,-5415.5 729.7,-5396.06 720.55,-3640.64 727,-3616.5 738.76,-3572.49 769.15,-3527.67 786.79,-3504.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="788.42,-3505.53 790.4,-3499.49 785.08,-3502.98 788.42,-3505.53"/>
</g>
<!-- src/components/MindMap/components/Dialogs/ConnectionDialog.tsx -->
<g id="node70" class="node">
<title>src/components/MindMap/components/Dialogs/ConnectionDialog.tsx</title>
<g id="a_node70"><a xlink:href="src/components/MindMap/components/Dialogs/ConnectionDialog.tsx" xlink:title="ConnectionDialog.tsx">
<path fill="#bbfeff" stroke="black" d="M686.83,-5863.75C686.83,-5863.75 595.42,-5863.75 595.42,-5863.75 592.33,-5863.75 589.25,-5860.66 589.25,-5857.58 589.25,-5857.58 589.25,-5851.41 589.25,-5851.41 589.25,-5848.33 592.33,-5845.25 595.42,-5845.25 595.42,-5845.25 686.83,-5845.25 686.83,-5845.25 689.92,-5845.25 693,-5848.33 693,-5851.41 693,-5851.41 693,-5857.58 693,-5857.58 693,-5860.66 689.92,-5863.75 686.83,-5863.75"/>
<text text-anchor="start" x="597.25" y="-5851.2" font-family="Helvetica,sans-Serif" font-size="9.00">ConnectionDialog.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Dialogs/ConnectionDialog.tsx&#45;&gt;node_modules/react -->
<g id="edge98" class="edge">
<title>src/components/MindMap/components/Dialogs/ConnectionDialog.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M693.39,-5852C701.37,-5849.32 708.71,-5845.07 714,-5838.5 733.58,-5814.14 708.1,-1363.38 727,-1338.5 770.56,-1281.12 833.81,-1347.12 883,-1294.5 894.79,-1281.89 877.6,-1267.37 891,-1256.5 943.46,-1213.93 994.94,-1209.02 1043,-1256.5 1055.73,-1269.07 1039.76,-1886.57 1051,-1900.5 1057.6,-1908.68 1067.92,-1912.68 1078.21,-1914.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1077.93,-1916.57 1084.15,-1915.25 1078.46,-1912.4 1077.93,-1916.57"/>
</g>
<!-- src/components/MindMap/components/Dialogs/ConnectionDialog.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge97" class="edge">
<title>src/components/MindMap/components/Dialogs/ConnectionDialog.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M693.29,-5851.92C701.28,-5849.24 708.65,-5845.02 714,-5838.5 734.44,-5813.58 719,-5289.72 727,-5258.5 764.32,-5112.92 839.61,-5102.38 883,-4958.5 892.68,-4926.4 873.37,-4912.01 891,-4883.5 900.56,-4868.04 917.14,-4856.66 932.29,-4848.87"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="932.83,-4850.95 937.31,-4846.44 931,-4847.17 932.83,-4850.95"/>
</g>
<!-- src/components/MindMap/components/Dialogs/ConnectionDialog.tsx&#45;&gt;src/components/MindMap/core/models/Connection.ts -->
<g id="edge96" class="edge">
<title>src/components/MindMap/components/Dialogs/ConnectionDialog.tsx&#45;&gt;src/components/MindMap/core/models/Connection.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M693.35,-5851.97C701.33,-5849.29 708.69,-5845.05 714,-5838.5 734.46,-5813.27 703.91,-4692.33 727,-4669.5 751.96,-4644.81 1017.37,-4645.5 1043,-4669.5 1052.84,-4678.7 1047.09,-4777.6 1051,-4790.5 1061.4,-4824.77 1084.62,-4859.88 1099.26,-4879.86"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1097.49,-4881 1102.77,-4884.55 1100.86,-4878.49 1097.49,-4881"/>
</g>
<!-- src/components/MindMap/components/Dialogs/DesignControlsDialog.tsx -->
<g id="node71" class="node">
<title>src/components/MindMap/components/Dialogs/DesignControlsDialog.tsx</title>
<g id="a_node71"><a xlink:href="src/components/MindMap/components/Dialogs/DesignControlsDialog.tsx" xlink:title="DesignControlsDialog.tsx">
<path fill="#bbfeff" stroke="black" d="M695.08,-5832.75C695.08,-5832.75 587.17,-5832.75 587.17,-5832.75 584.08,-5832.75 581,-5829.66 581,-5826.58 581,-5826.58 581,-5820.41 581,-5820.41 581,-5817.33 584.08,-5814.25 587.17,-5814.25 587.17,-5814.25 695.08,-5814.25 695.08,-5814.25 698.17,-5814.25 701.25,-5817.33 701.25,-5820.41 701.25,-5820.41 701.25,-5826.58 701.25,-5826.58 701.25,-5829.66 698.17,-5832.75 695.08,-5832.75"/>
<text text-anchor="start" x="589" y="-5820.2" font-family="Helvetica,sans-Serif" font-size="9.00">DesignControlsDialog.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Dialogs/DesignControlsDialog.tsx&#45;&gt;node_modules/react -->
<g id="edge101" class="edge">
<title>src/components/MindMap/components/Dialogs/DesignControlsDialog.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M701.44,-5817.59C706.24,-5815.07 710.55,-5811.78 714,-5807.5 733.56,-5783.17 709,-1338 727,-1312.5 769.47,-1252.31 838.33,-1315.07 883,-1256.5 897.05,-1238.06 873.99,-1168.24 891,-1152.5 940.57,-1106.59 994.98,-1104.98 1043,-1152.5 1057.77,-1167.11 1037.96,-1884.31 1051,-1900.5 1057.6,-1908.68 1067.91,-1912.68 1078.2,-1914.49"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1077.92,-1916.57 1084.14,-1915.26 1078.46,-1912.41 1077.92,-1916.57"/>
</g>
<!-- src/components/MindMap/components/Dialogs/DesignControlsDialog.tsx&#45;&gt;src/components/MindMap/types/index.ts -->
<g id="edge100" class="edge">
<title>src/components/MindMap/components/Dialogs/DesignControlsDialog.tsx&#45;&gt;src/components/MindMap/types/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M701.42,-5817.58C706.22,-5815.06 710.54,-5811.77 714,-5807.5 743.34,-5771.2 707.59,-4169.94 727,-4127.5 765.38,-4043.58 843.13,-4073.71 883,-3990.5 891.84,-3972.04 877.79,-3822.12 891,-3806.5 900.68,-3795.05 916.36,-3790.57 930.89,-3789.16"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="930.83,-3791.26 936.7,-3788.81 930.58,-3787.07 930.83,-3791.26"/>
</g>
<!-- src/components/MindMap/components/Dialogs/DesignControlsDialog.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx -->
<g id="edge99" class="edge">
<title>src/components/MindMap/components/Dialogs/DesignControlsDialog.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M701.42,-5817.58C706.22,-5815.06 710.55,-5811.77 714,-5807.5 745.77,-5768.17 695.02,-4026.66 727,-3987.5 771.82,-3932.6 837.19,-4008.57 883,-3954.5 896.23,-3938.88 885.08,-3790.08 891,-3770.5 903.83,-3728.09 934.49,-3685.35 952.39,-3662.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="953.88,-3664.3 956.02,-3658.31 950.61,-3661.66 953.88,-3664.3"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog.tsx&#45;&gt;node_modules/react -->
<g id="edge104" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M813.14,-5721.76C831.88,-5704.56 869,-5666.72 883,-5625.5 890.48,-5603.46 876.32,-2292.55 891,-2274.5 934.56,-2220.94 998.82,-2295.54 1043,-2242.5 1065.19,-2215.85 1029.46,-1957.67 1051,-1930.5 1057.7,-1922.04 1068.27,-1917.72 1078.75,-1915.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.84,-1917.72 1084.44,-1914.71 1078.19,-1913.57 1078.84,-1917.72"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx -->
<g id="edge102" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M813.14,-5721.76C831.86,-5704.56 868.96,-5666.71 883,-5625.5 899.62,-5576.72 877.05,-3820.1 891,-3770.5 902.93,-3728.06 933.66,-3685.63 951.87,-3663.08"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="953.4,-3664.52 955.59,-3658.55 950.16,-3661.85 953.4,-3664.52"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog.tsx&#45;&gt;src/components/MindMap/hooks/useNodeManagement.ts -->
<g id="edge103" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog.tsx&#45;&gt;src/components/MindMap/hooks/useNodeManagement.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M790.78,-5721.92C772.43,-5703.95 734.52,-5663.03 720.5,-5619.5 716.31,-5606.48 715.02,-3663.02 720.5,-3650.5 723.42,-3643.84 728.33,-3639.07 734.33,-3635.7"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="735.02,-3637.69 739.67,-3633.37 733.34,-3633.84 735.02,-3637.69"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NewNodeDialog.css -->
<g id="node73" class="node">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NewNodeDialog.css</title>
<g id="a_node73"><a xlink:href="src/components/MindMap/components/Dialogs/NodeDialog/NewNodeDialog.css" xlink:title="NewNodeDialog.css">
<path fill="#ffffcc" stroke="black" d="M1010.83,-5805.75C1010.83,-5805.75 923.17,-5805.75 923.17,-5805.75 920.08,-5805.75 917,-5802.66 917,-5799.58 917,-5799.58 917,-5793.41 917,-5793.41 917,-5790.33 920.08,-5787.25 923.17,-5787.25 923.17,-5787.25 1010.83,-5787.25 1010.83,-5787.25 1013.92,-5787.25 1017,-5790.33 1017,-5793.41 1017,-5793.41 1017,-5799.58 1017,-5799.58 1017,-5802.66 1013.92,-5805.75 1010.83,-5805.75"/>
<text text-anchor="start" x="925" y="-5793.2" font-family="Helvetica,sans-Serif" font-size="9.00">NewNodeDialog.css</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NewNodeDialog.tsx&#45;&gt;node_modules/@mui/material -->
<g id="edge107" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NewNodeDialog.tsx&#45;&gt;node_modules/@mui/material</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M850.08,-5828.52C862.63,-5826.28 874.89,-5821.34 883,-5811.5 900.68,-5790.03 871.31,-1826.14 891,-1806.5 938.82,-1758.77 982.8,-1775.84 1043,-1806.5 1048.39,-1809.24 1045.93,-1814.2 1051,-1817.5 1059.19,-1822.81 1069.19,-1826 1078.7,-1827.89"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.08,-1829.92 1084.35,-1828.85 1078.78,-1825.78 1078.08,-1829.92"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NewNodeDialog.tsx&#45;&gt;node_modules/react -->
<g id="edge108" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NewNodeDialog.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M850.08,-5828.52C862.63,-5826.28 874.89,-5821.34 883,-5811.5 898.27,-5792.95 875.84,-2371.13 891,-2352.5 934.56,-2298.94 998.95,-2373.66 1043,-2320.5 1056.83,-2303.81 1037.58,-1947.51 1051,-1930.5 1057.68,-1922.03 1068.24,-1917.7 1078.72,-1915.59"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.82,-1917.7 1084.42,-1914.69 1078.16,-1913.55 1078.82,-1917.7"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NewNodeDialog.tsx&#45;&gt;node_modules/react&#45;draggable -->
<g id="edge109" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NewNodeDialog.tsx&#45;&gt;node_modules/react&#45;draggable</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M850.07,-5828.52C862.62,-5826.27 874.88,-5821.34 883,-5811.5 892.66,-5799.79 889.95,-3641.63 891,-3626.5 925.77,-3123.59 1005.2,-3005.18 1043,-2502.5 1044.06,-2488.37 1042.25,-2003.64 1051,-1992.5 1054.8,-1987.66 1059.86,-1984.17 1065.46,-1981.68"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1065.9,-1983.75 1070.81,-1979.7 1064.45,-1979.81 1065.9,-1983.75"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NewNodeDialog.tsx&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge105" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NewNodeDialog.tsx&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M850.31,-5826.7C862.71,-5828.99 874.83,-5833.88 883,-5843.5 900.56,-5864.17 871.51,-6068.63 891,-6087.5 913.9,-6109.65 1154.61,-6109.4 1177.75,-6087.5 1221.07,-6046.5 1238.87,-5090.72 1241.33,-4945.83"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1243.42,-4946.21 1241.42,-4940.17 1239.22,-4946.14 1243.42,-4946.21"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NewNodeDialog.tsx&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/NewNodeDialog.css -->
<g id="edge106" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NewNodeDialog.tsx&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/NewNodeDialog.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M850.03,-5818.28C863.3,-5815.75 877.71,-5813.01 891,-5810.5 896.6,-5809.44 902.43,-5808.34 908.26,-5807.25"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="908.29,-5809.38 913.8,-5806.21 907.52,-5805.25 908.29,-5809.38"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeDesignTab.tsx -->
<g id="node75" class="node">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeDesignTab.tsx</title>
<g id="a_node75"><a xlink:href="src/components/MindMap/components/Dialogs/NodeDialog/NodeDesignTab.tsx" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M842.96,-5867.75C842.96,-5867.75 759.04,-5867.75 759.04,-5867.75 755.96,-5867.75 752.88,-5864.66 752.88,-5861.58 752.88,-5861.58 752.88,-5855.41 752.88,-5855.41 752.88,-5852.33 755.96,-5849.25 759.04,-5849.25 759.04,-5849.25 842.96,-5849.25 842.96,-5849.25 846.04,-5849.25 849.12,-5852.33 849.12,-5855.41 849.12,-5855.41 849.12,-5861.58 849.12,-5861.58 849.12,-5864.66 846.04,-5867.75 842.96,-5867.75"/>
<text text-anchor="start" x="760.88" y="-5855.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">NodeDesignTab.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/types.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge132" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/types.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1140.52,-5872.73C1153.72,-5871.35 1168.76,-5867.24 1177.75,-5856.5 1238.34,-5784.05 1241.54,-5069.84 1241.63,-4945.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1243.73,-4946.08 1241.64,-4940.08 1239.53,-4946.08 1243.73,-4946.08"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.css -->
<g id="node78" class="node">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.css</title>
<g id="a_node78"><a xlink:href="src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.css" xlink:title="NodeDialogContainer.css">
<path fill="#ffffcc" stroke="black" d="M1021.33,-5994.75C1021.33,-5994.75 912.67,-5994.75 912.67,-5994.75 909.58,-5994.75 906.5,-5991.66 906.5,-5988.58 906.5,-5988.58 906.5,-5982.41 906.5,-5982.41 906.5,-5979.33 909.58,-5976.25 912.67,-5976.25 912.67,-5976.25 1021.33,-5976.25 1021.33,-5976.25 1024.42,-5976.25 1027.5,-5979.33 1027.5,-5982.41 1027.5,-5982.41 1027.5,-5988.58 1027.5,-5988.58 1027.5,-5991.66 1024.42,-5994.75 1021.33,-5994.75"/>
<text text-anchor="start" x="914.5" y="-5982.2" font-family="Helvetica,sans-Serif" font-size="9.00">NodeDialogContainer.css</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx&#45;&gt;node_modules/react -->
<g id="edge121" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M860.5,-6005.94C869.19,-6003.12 877.17,-5998.58 883,-5991.5 898.84,-5972.27 875.28,-2423.82 891,-2404.5 934.56,-2350.93 999.02,-2425.71 1043,-2372.5 1058.65,-2353.56 1035.82,-1949.8 1051,-1930.5 1057.67,-1922.02 1068.23,-1917.69 1078.71,-1915.58"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.81,-1917.69 1084.41,-1914.68 1078.15,-1913.54 1078.81,-1917.69"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx&#45;&gt;node_modules/react&#45;dom -->
<g id="edge122" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx&#45;&gt;node_modules/react&#45;dom</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M860.5,-6005.95C869.19,-6003.12 877.17,-5998.58 883,-5991.5 901.47,-5969.06 870.9,-1827.49 891,-1806.5 915.67,-1780.73 1020.87,-1787.08 1076.81,-1792.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1076.35,-1794.57 1082.53,-1793.08 1076.77,-1790.4 1076.35,-1794.57"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge117" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M860.47,-6005.92C869.16,-6003.09 877.15,-5998.56 883,-5991.5 902.63,-5967.79 876.13,-4910.44 891,-4883.5 899.65,-4867.82 915.82,-4856.5 930.94,-4848.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="931.46,-4850.89 935.98,-4846.42 929.66,-4847.1 931.46,-4850.89"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge116" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M807.58,-6017.18C819.72,-6037.14 850.69,-6082.41 891,-6099.5 1008.34,-6149.24 1085.21,-6187.13 1177.75,-6099.5 1221.55,-6058.02 1238.97,-5090.41 1241.34,-4945.57"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1243.43,-4945.97 1241.43,-4939.93 1239.23,-4945.9 1243.43,-4945.97"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.tsx -->
<g id="edge118" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/NodeDialog.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M860.59,-6003.34C868.75,-6000.74 876.54,-5996.95 883,-5991.5 890.63,-5985.05 885.88,-5979.07 891,-5970.5 907.5,-5942.82 933.04,-5915.43 949.65,-5898.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="950.75,-5900.84 953.57,-5895.15 947.81,-5897.84 950.75,-5900.84"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/types.ts -->
<g id="edge120" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/types.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M839.52,-6017.2C889.89,-6027.96 980.28,-6039.15 1043,-6000.5 1082.79,-5975.97 1101.18,-5920 1108.31,-5890.88"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1110.35,-5891.38 1109.64,-5885.06 1106.26,-5890.44 1110.35,-5891.38"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.css -->
<g id="edge119" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M860.74,-5999.61C872.58,-5998.03 885.12,-5996.35 897.26,-5994.72"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="897.34,-5996.82 903.01,-5993.95 896.78,-5992.66 897.34,-5996.82"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeHatTab.tsx -->
<g id="node80" class="node">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeHatTab.tsx</title>
<g id="a_node80"><a xlink:href="src/components/MindMap/components/Dialogs/NodeDialog/NodeHatTab.tsx" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M835.46,-5954.75C835.46,-5954.75 766.54,-5954.75 766.54,-5954.75 763.46,-5954.75 760.38,-5951.66 760.38,-5948.58 760.38,-5948.58 760.38,-5942.41 760.38,-5942.41 760.38,-5939.33 763.46,-5936.25 766.54,-5936.25 766.54,-5936.25 835.46,-5936.25 835.46,-5936.25 838.54,-5936.25 841.62,-5939.33 841.62,-5942.41 841.62,-5942.41 841.62,-5948.58 841.62,-5948.58 841.62,-5951.66 838.54,-5954.75 835.46,-5954.75"/>
<text text-anchor="start" x="768.38" y="-5942.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">NodeHatTab.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeInfoTab.tsx -->
<g id="node81" class="node">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeInfoTab.tsx</title>
<g id="a_node81"><a xlink:href="src/components/MindMap/components/Dialogs/NodeDialog/NodeInfoTab.tsx" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M835.83,-5985.75C835.83,-5985.75 766.17,-5985.75 766.17,-5985.75 763.08,-5985.75 760,-5982.66 760,-5979.58 760,-5979.58 760,-5973.41 760,-5973.41 760,-5970.33 763.08,-5967.25 766.17,-5967.25 766.17,-5967.25 835.83,-5967.25 835.83,-5967.25 838.92,-5967.25 842,-5970.33 842,-5973.41 842,-5973.41 842,-5979.58 842,-5979.58 842,-5982.66 838.92,-5985.75 835.83,-5985.75"/>
<text text-anchor="start" x="768" y="-5973.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">NodeInfoTab.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeLLMTab.tsx -->
<g id="node82" class="node">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeLLMTab.tsx</title>
<g id="a_node82"><a xlink:href="src/components/MindMap/components/Dialogs/NodeDialog/NodeLLMTab.tsx" xlink:title="NodeLLMTab.tsx">
<path fill="#bbfeff" stroke="black" d="M837.33,-5805.75C837.33,-5805.75 764.67,-5805.75 764.67,-5805.75 761.58,-5805.75 758.5,-5802.66 758.5,-5799.58 758.5,-5799.58 758.5,-5793.41 758.5,-5793.41 758.5,-5790.33 761.58,-5787.25 764.67,-5787.25 764.67,-5787.25 837.33,-5787.25 837.33,-5787.25 840.42,-5787.25 843.5,-5790.33 843.5,-5793.41 843.5,-5793.41 843.5,-5799.58 843.5,-5799.58 843.5,-5802.66 840.42,-5805.75 837.33,-5805.75"/>
<text text-anchor="start" x="766.5" y="-5793.2" font-family="Helvetica,sans-Serif" font-size="9.00">NodeLLMTab.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeLLMTab.tsx&#45;&gt;../../../contexts/LLMContext -->
<g id="edge123" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeLLMTab.tsx&#45;&gt;../../../contexts/LLMContext</title>
<g id="a_edge123"><a xlink:title="not&#45;to&#45;unresolvable">
<path fill="none" stroke="red" stroke-width="2" d="M843.58,-5800.1C858.21,-5799.02 873.38,-5794.76 883,-5783.5 901.79,-5761.49 872.78,-1639.97 891,-1617.5 899.24,-1607.33 912.22,-1602.61 925.07,-1600.66"/>
<polygon fill="red" stroke="red" stroke-width="2" points="925.14,-1602.76 930.88,-1600.03 924.69,-1598.58 925.14,-1602.76"/>
</a>
</g>
<text text-anchor="middle" x="926.96" y="-3691.36" font-family="Helvetica,sans-Serif" font-size="9.00" fill="red">not&#45;to&#45;unresolvable</text>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeLLMTab.tsx&#45;&gt;../../../contexts/MindMapContext -->
<g id="edge124" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeLLMTab.tsx&#45;&gt;../../../contexts/MindMapContext</title>
<g id="a_edge124"><a xlink:title="not&#45;to&#45;unresolvable">
<path fill="none" stroke="red" stroke-width="2" d="M843.58,-5800.1C858.21,-5799.02 873.38,-5794.76 883,-5783.5 892.09,-5772.86 887.18,-1767.95 891,-1754.5 902.89,-1712.59 933.63,-1670.9 951.86,-1648.76"/>
<polygon fill="red" stroke="red" stroke-width="2" points="953.31,-1650.29 955.56,-1644.35 950.1,-1647.59 953.31,-1650.29"/>
</a>
</g>
<text text-anchor="middle" x="928.74" y="-3712.23" font-family="Helvetica,sans-Serif" font-size="9.00" fill="red">not&#45;to&#45;unresolvable</text>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeLLMTab.tsx&#45;&gt;node_modules/react -->
<g id="edge126" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeLLMTab.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M843.58,-5800.1C858.21,-5799.01 873.38,-5794.76 883,-5783.5 898.59,-5765.24 875.85,-2345.12 891,-2326.5 934.56,-2272.94 998.92,-2347.62 1043,-2294.5 1055.92,-2278.93 1038.46,-1946.37 1051,-1930.5 1057.68,-1922.03 1068.25,-1917.71 1078.73,-1915.59"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.83,-1917.7 1084.43,-1914.7 1078.17,-1913.56 1078.83,-1917.7"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/NodeLLMTab.tsx&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge125" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/NodeLLMTab.tsx&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M843.71,-5799.81C858.17,-5798.64 873.19,-5794.41 883,-5783.5 894.3,-5770.92 885.22,-5647.39 891,-5631.5 963.45,-5432.33 1091.29,-5443.99 1177.75,-5250.5 1226.17,-5142.12 1238.07,-4997.04 1240.83,-4946.11"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1242.92,-4946.33 1241.12,-4940.24 1238.73,-4946.13 1242.92,-4946.33"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/hooks/useNodeDialog.ts -->
<g id="node83" class="node">
<title>src/components/MindMap/components/Dialogs/NodeDialog/hooks/useNodeDialog.ts</title>
<g id="a_node83"><a xlink:href="src/components/MindMap/components/Dialogs/NodeDialog/hooks/useNodeDialog.ts" xlink:title="useNodeDialog.ts">
<path fill="#ddfeff" stroke="black" d="M839.58,-5901.75C839.58,-5901.75 762.42,-5901.75 762.42,-5901.75 759.33,-5901.75 756.25,-5898.66 756.25,-5895.58 756.25,-5895.58 756.25,-5889.41 756.25,-5889.41 756.25,-5886.33 759.33,-5883.25 762.42,-5883.25 762.42,-5883.25 839.58,-5883.25 839.58,-5883.25 842.67,-5883.25 845.75,-5886.33 845.75,-5889.41 845.75,-5889.41 845.75,-5895.58 845.75,-5895.58 845.75,-5898.66 842.67,-5901.75 839.58,-5901.75"/>
<text text-anchor="start" x="764.25" y="-5889.2" font-family="Helvetica,sans-Serif" font-size="9.00">useNodeDialog.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/hooks/useNodeDialog.ts&#45;&gt;node_modules/react -->
<g id="edge131" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/hooks/useNodeDialog.ts&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M846.12,-5895.88C859.94,-5894.5 873.93,-5890.11 883,-5879.5 898.79,-5861.01 875.66,-2397.36 891,-2378.5 934.56,-2324.94 998.99,-2399.68 1043,-2346.5 1057.74,-2328.69 1036.7,-1948.66 1051,-1930.5 1057.67,-1922.02 1068.23,-1917.7 1078.71,-1915.58"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.81,-1917.69 1084.41,-1914.69 1078.16,-1913.55 1078.81,-1917.69"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/hooks/useNodeDialog.ts&#45;&gt;src/services/api/GovernanceLLM.ts -->
<g id="edge127" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/hooks/useNodeDialog.ts&#45;&gt;src/services/api/GovernanceLLM.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M846.07,-5889.15C859.89,-5890.53 873.9,-5894.92 883,-5905.5 898.08,-5923.01 875.03,-6720.79 891,-6737.5 901.04,-6747.99 1090.49,-6753.39 1186.63,-6755.46"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1186.57,-6757.56 1192.61,-6755.59 1186.66,-6753.36 1186.57,-6757.56"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/hooks/useNodeDialog.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge128" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/hooks/useNodeDialog.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M845.85,-5895.64C859.65,-5894.24 873.7,-5889.9 883,-5879.5 895.46,-5865.56 884.12,-5728.87 891,-5711.5 929.31,-5614.78 979.57,-5615.95 1043,-5533.5 1107.23,-5450 1138.73,-5436.35 1177.75,-5338.5 1234.76,-5195.51 1241.12,-5005.68 1241.65,-4946.15"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1243.75,-4946.2 1241.68,-4940.19 1239.55,-4946.18 1243.75,-4946.2"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/hooks/useNodeDialog.ts&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/types.ts -->
<g id="edge129" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/hooks/useNodeDialog.ts&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/types.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M845.95,-5898.91C894.58,-5904.79 975.06,-5910.85 1043,-5898.5 1056.94,-5895.96 1071.72,-5890.77 1084.03,-5885.69"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1084.64,-5887.71 1089.33,-5883.41 1082.98,-5883.85 1084.64,-5887.71"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/utils/treeUtils.ts -->
<g id="node84" class="node">
<title>src/components/MindMap/components/Dialogs/NodeDialog/utils/treeUtils.ts</title>
<g id="a_node84"><a xlink:href="src/components/MindMap/components/Dialogs/NodeDialog/utils/treeUtils.ts" xlink:title="treeUtils.ts">
<path fill="#ddfeff" stroke="black" d="M990.21,-5839.75C990.21,-5839.75 943.79,-5839.75 943.79,-5839.75 940.71,-5839.75 937.62,-5836.66 937.62,-5833.58 937.62,-5833.58 937.62,-5827.41 937.62,-5827.41 937.62,-5824.33 940.71,-5821.25 943.79,-5821.25 943.79,-5821.25 990.21,-5821.25 990.21,-5821.25 993.29,-5821.25 996.38,-5824.33 996.38,-5827.41 996.38,-5827.41 996.38,-5833.58 996.38,-5833.58 996.38,-5836.66 993.29,-5839.75 990.21,-5839.75"/>
<text text-anchor="start" x="945.62" y="-5827.2" font-family="Helvetica,sans-Serif" font-size="9.00">treeUtils.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/hooks/useNodeDialog.ts&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/utils/treeUtils.ts -->
<g id="edge130" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/hooks/useNodeDialog.ts&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/utils/treeUtils.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M846.13,-5894.55C859.46,-5892.96 873.15,-5888.77 883,-5879.5 894.93,-5868.26 879.14,-5854.81 891,-5843.5 900.96,-5833.99 915.24,-5830.07 928.66,-5828.76"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="928.42,-5830.88 934.29,-5828.43 928.17,-5826.69 928.42,-5830.88"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/utils/treeUtils.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge134" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/utils/treeUtils.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M937.46,-5834.08C917.18,-5834.67 892.92,-5831.03 887,-5811.5 883.29,-5799.24 883.29,-4901.75 887,-4889.5 892.81,-4870.3 910.67,-4857.25 928.05,-4848.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="928.59,-4850.86 933.19,-4846.48 926.86,-4847.03 928.59,-4850.86"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/utils/treeUtils.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge133" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/utils/treeUtils.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M996.8,-5832.86C1012.46,-5832.53 1031.01,-5829.24 1043,-5817.5 1056.27,-5804.51 1042.6,-5793.05 1051,-5776.5 1088.54,-5702.52 1144.08,-5715.3 1177.75,-5639.5 1235.35,-5509.83 1241.01,-5043.81 1241.57,-4945.7"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1243.66,-4945.96 1241.59,-4939.95 1239.46,-4945.94 1243.66,-4945.96"/>
</g>
<!-- src/components/MindMap/components/Dialogs/NodeDialog/utils/treeUtils.ts&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/types.ts -->
<g id="edge135" class="edge">
<title>src/components/MindMap/components/Dialogs/NodeDialog/utils/treeUtils.ts&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/types.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M996.8,-5829.99C1011.7,-5831.02 1029.54,-5834.38 1043,-5843.5 1048.62,-5847.3 1045.55,-5852.46 1051,-5856.5 1058.61,-5862.14 1068.05,-5865.79 1077.21,-5868.16"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1076.58,-5870.17 1082.89,-5869.41 1077.48,-5866.07 1076.58,-5870.17"/>
</g>
<!-- src/components/MindMap/components/Dialogs/ProjectDialog.tsx -->
<g id="node85" class="node">
<title>src/components/MindMap/components/Dialogs/ProjectDialog.tsx</title>
<g id="a_node85"><a xlink:href="src/components/MindMap/components/Dialogs/ProjectDialog.tsx" xlink:title="ProjectDialog.tsx">
<path fill="#bbfeff" stroke="black" d="M677.46,-5801.75C677.46,-5801.75 604.79,-5801.75 604.79,-5801.75 601.71,-5801.75 598.62,-5798.66 598.62,-5795.58 598.62,-5795.58 598.62,-5789.41 598.62,-5789.41 598.62,-5786.33 601.71,-5783.25 604.79,-5783.25 604.79,-5783.25 677.46,-5783.25 677.46,-5783.25 680.54,-5783.25 683.62,-5786.33 683.62,-5789.41 683.62,-5789.41 683.62,-5795.58 683.62,-5795.58 683.62,-5798.66 680.54,-5801.75 677.46,-5801.75"/>
<text text-anchor="start" x="606.62" y="-5789.2" font-family="Helvetica,sans-Serif" font-size="9.00">ProjectDialog.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Dialogs/ProjectDialog.tsx&#45;&gt;node_modules/react -->
<g id="edge138" class="edge">
<title>src/components/MindMap/components/Dialogs/ProjectDialog.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M684.03,-5793.66C695.42,-5791.76 706.63,-5787.41 714,-5778.5 733.99,-5754.31 706.91,-1284.59 727,-1260.5 772.16,-1206.33 836.55,-1284.56 883,-1231.5 894.5,-1218.35 878.34,-1086.53 891,-1074.5 915.48,-1051.23 1019,-1050.73 1043,-1074.5 1059.3,-1090.64 1036.61,-1882.62 1051,-1900.5 1057.59,-1908.68 1067.91,-1912.69 1078.2,-1914.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1077.92,-1916.58 1084.13,-1915.26 1078.45,-1912.41 1077.92,-1916.58"/>
</g>
<!-- src/components/MindMap/components/Dialogs/ProjectDialog.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx -->
<g id="edge136" class="edge">
<title>src/components/MindMap/components/Dialogs/ProjectDialog.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M684,-5793.64C695.39,-5791.74 706.61,-5787.4 714,-5778.5 746.29,-5739.59 693.11,-3996.01 727,-3958.5 750.38,-3932.62 859.12,-3966.9 883,-3941.5 896.03,-3927.64 885.46,-3788.69 891,-3770.5 903.9,-3728.11 934.54,-3685.36 952.41,-3662.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="953.9,-3664.3 956.04,-3658.32 950.63,-3661.67 953.9,-3664.3"/>
</g>
<!-- src/components/MindMap/components/Dialogs/ProjectDialog.tsx&#45;&gt;src/components/MindMap/hooks/useProjectManagement.ts -->
<g id="edge137" class="edge">
<title>src/components/MindMap/components/Dialogs/ProjectDialog.tsx&#45;&gt;src/components/MindMap/hooks/useProjectManagement.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M684.01,-5793.64C695.4,-5791.74 706.62,-5787.4 714,-5778.5 733.17,-5755.38 719.27,-3645.51 727,-3616.5 738.73,-3572.49 769.13,-3527.67 786.78,-3504.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="788.41,-3505.53 790.39,-3499.49 785.08,-3502.97 788.41,-3505.53"/>
</g>
<!-- src/components/MindMap/components/Dialogs/ProjectManagementDialog.css -->
<g id="node86" class="node">
<title>src/components/MindMap/components/Dialogs/ProjectManagementDialog.css</title>
<g id="a_node86"><a xlink:href="src/components/MindMap/components/Dialogs/ProjectManagementDialog.css" xlink:title="ProjectManagementDialog.css">
<path fill="#ffffcc" stroke="black" d="M1031.46,-5771.75C1031.46,-5771.75 902.54,-5771.75 902.54,-5771.75 899.46,-5771.75 896.38,-5768.66 896.38,-5765.58 896.38,-5765.58 896.38,-5759.41 896.38,-5759.41 896.38,-5756.33 899.46,-5753.25 902.54,-5753.25 902.54,-5753.25 1031.46,-5753.25 1031.46,-5753.25 1034.54,-5753.25 1037.62,-5756.33 1037.62,-5759.41 1037.62,-5759.41 1037.62,-5765.58 1037.62,-5765.58 1037.62,-5768.66 1034.54,-5771.75 1031.46,-5771.75"/>
<text text-anchor="start" x="904.38" y="-5759.2" font-family="Helvetica,sans-Serif" font-size="9.00">ProjectManagementDialog.css</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Dialogs/ProjectManagementDialog.tsx&#45;&gt;node_modules/react -->
<g id="edge141" class="edge">
<title>src/components/MindMap/components/Dialogs/ProjectManagementDialog.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M870.64,-5756.59C875.32,-5754.01 879.54,-5750.7 883,-5746.5 898.22,-5728.02 875.9,-2319.06 891,-2300.5 934.56,-2246.94 998.87,-2321.59 1043,-2268.5 1055.01,-2254.05 1039.35,-1945.23 1051,-1930.5 1057.69,-1922.04 1068.26,-1917.72 1078.74,-1915.6"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.83,-1917.71 1084.43,-1914.7 1078.18,-1913.56 1078.83,-1917.71"/>
</g>
<!-- src/components/MindMap/components/Dialogs/ProjectManagementDialog.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge139" class="edge">
<title>src/components/MindMap/components/Dialogs/ProjectManagementDialog.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M870.98,-5756.36C875.51,-5753.81 879.61,-5750.58 883,-5746.5 898.31,-5728.04 879.39,-4904.47 891,-4883.5 899.67,-4867.83 915.84,-4856.51 930.96,-4848.82"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="931.47,-4850.9 935.99,-4846.42 929.67,-4847.11 931.47,-4850.9"/>
</g>
<!-- src/components/MindMap/components/Dialogs/ProjectManagementDialog.tsx&#45;&gt;src/components/MindMap/components/Dialogs/ProjectManagementDialog.css -->
<g id="edge140" class="edge">
<title>src/components/MindMap/components/Dialogs/ProjectManagementDialog.tsx&#45;&gt;src/components/MindMap/components/Dialogs/ProjectManagementDialog.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M870.6,-5762.5C876.13,-5762.5 881.74,-5762.5 887.34,-5762.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="887.07,-5764.6 893.07,-5762.5 887.07,-5760.4 887.07,-5764.6"/>
</g>
<!-- src/components/MindMap/components/Dialogs/StartupDialog/StartupDialog.tsx -->
<g id="node88" class="node">
<title>src/components/MindMap/components/Dialogs/StartupDialog/StartupDialog.tsx</title>
<g id="a_node88"><a xlink:href="src/components/MindMap/components/Dialogs/StartupDialog/StartupDialog.tsx" xlink:title="StartupDialog.tsx">
<path fill="#bbfeff" stroke="black" d="M677.83,-5748.75C677.83,-5748.75 604.42,-5748.75 604.42,-5748.75 601.33,-5748.75 598.25,-5745.66 598.25,-5742.58 598.25,-5742.58 598.25,-5736.41 598.25,-5736.41 598.25,-5733.33 601.33,-5730.25 604.42,-5730.25 604.42,-5730.25 677.83,-5730.25 677.83,-5730.25 680.92,-5730.25 684,-5733.33 684,-5736.41 684,-5736.41 684,-5742.58 684,-5742.58 684,-5745.66 680.92,-5748.75 677.83,-5748.75"/>
<text text-anchor="start" x="606.25" y="-5736.2" font-family="Helvetica,sans-Serif" font-size="9.00">StartupDialog.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Dialogs/StartupDialog/StartupDialog.tsx&#45;&gt;./StartupDialog.css -->
<g id="edge143" class="edge">
<title>src/components/MindMap/components/Dialogs/StartupDialog/StartupDialog.tsx&#45;&gt;./StartupDialog.css</title>
<g id="a_edge143"><a xlink:title="not&#45;to&#45;unresolvable">
<path fill="none" stroke="red" stroke-width="2" d="M684.31,-5731.07C695.86,-5726.62 707.09,-5719.84 714,-5709.5 731.27,-5683.66 707.8,-1258.93 727,-1234.5 732.44,-1227.57 740.09,-1223.04 748.4,-1220.13"/>
<polygon fill="red" stroke="red" stroke-width="2" points="748.77,-1222.21 753.97,-1218.56 747.62,-1218.17 748.77,-1222.21"/>
</a>
</g>
<text text-anchor="middle" x="759.53" y="-3465.77" font-family="Helvetica,sans-Serif" font-size="9.00" fill="red">not&#45;to&#45;unresolvable</text>
</g>
<!-- src/components/MindMap/components/Dialogs/StartupDialog/StartupDialog.tsx&#45;&gt;node_modules/react -->
<g id="edge144" class="edge">
<title>src/components/MindMap/components/Dialogs/StartupDialog/StartupDialog.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M684.31,-5731.07C695.86,-5726.62 707.09,-5719.84 714,-5709.5 748.79,-5657.44 696.88,-1256.39 727,-1201.5 766.61,-1129.29 838.19,-1170.59 883,-1101.5 895.96,-1081.51 872.94,-1064.03 891,-1048.5 916.6,-1026.47 1019,-1024.72 1043,-1048.5 1059.81,-1065.15 1036.16,-1882.05 1051,-1900.5 1057.59,-1908.69 1067.91,-1912.69 1078.2,-1914.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1077.91,-1916.58 1084.13,-1915.26 1078.45,-1912.41 1077.91,-1916.58"/>
</g>
<!-- src/components/MindMap/components/Dialogs/StartupDialog/StartupDialog.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx -->
<g id="edge142" class="edge">
<title>src/components/MindMap/components/Dialogs/StartupDialog/StartupDialog.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M684.29,-5731.05C695.83,-5726.6 707.06,-5719.82 714,-5709.5 741.33,-5668.79 693.9,-3980.66 727,-3944.5 750.51,-3918.8 858.94,-3954.68 883,-3929.5 895.22,-3916.71 885.82,-3787.41 891,-3770.5 903.97,-3728.13 934.58,-3685.38 952.43,-3662.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="953.92,-3664.31 956.05,-3658.32 950.65,-3661.68 953.92,-3664.31"/>
</g>
<!-- src/governance/chat/Implementation.tsx -->
<g id="node163" class="node">
<title>src/governance/chat/Implementation.tsx</title>
<g id="a_node163"><a xlink:href="src/governance/chat/Implementation.tsx" xlink:title="Implementation.tsx">
<path fill="#bbfeff" stroke="black" d="M681.21,-6523.75C681.21,-6523.75 601.04,-6523.75 601.04,-6523.75 597.96,-6523.75 594.88,-6520.66 594.88,-6517.58 594.88,-6517.58 594.88,-6511.41 594.88,-6511.41 594.88,-6508.33 597.96,-6505.25 601.04,-6505.25 601.04,-6505.25 681.21,-6505.25 681.21,-6505.25 684.29,-6505.25 687.38,-6508.33 687.38,-6511.41 687.38,-6511.41 687.38,-6517.58 687.38,-6517.58 687.38,-6520.66 684.29,-6523.75 681.21,-6523.75"/>
<text text-anchor="start" x="602.88" y="-6511.2" font-family="Helvetica,sans-Serif" font-size="9.00">Implementation.tsx</text>
</a>
</g>
</g>
<!-- src/governance/chat/index.ts&#45;&gt;src/governance/chat/Implementation.tsx -->
<g id="edge378" class="edge">
<title>src/governance/chat/index.ts&#45;&gt;src/governance/chat/Implementation.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M489.34,-6540.96C507.77,-6537.77 533.02,-6533.39 555.25,-6529.5 565.77,-6527.65 577.02,-6525.67 587.78,-6523.77"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="594.14,-6524.78 587.87,-6523.76 593.41,-6520.64 594.14,-6524.78"/>
</g>
<!-- src/components/MindMap/components/Manager/EnhancedMindMapManager.css -->
<g id="node90" class="node">
<title>src/components/MindMap/components/Manager/EnhancedMindMapManager.css</title>
<g id="a_node90"><a xlink:href="src/components/MindMap/components/Manager/EnhancedMindMapManager.css" xlink:title="EnhancedMindMapManager.css">
<path fill="#ffffcc" stroke="black" d="M868.83,-5287.75C868.83,-5287.75 733.17,-5287.75 733.17,-5287.75 730.08,-5287.75 727,-5284.66 727,-5281.58 727,-5281.58 727,-5275.41 727,-5275.41 727,-5272.33 730.08,-5269.25 733.17,-5269.25 733.17,-5269.25 868.83,-5269.25 868.83,-5269.25 871.92,-5269.25 875,-5272.33 875,-5275.41 875,-5275.41 875,-5281.58 875,-5281.58 875,-5284.66 871.92,-5287.75 868.83,-5287.75"/>
<text text-anchor="start" x="735" y="-5275.2" font-family="Helvetica,sans-Serif" font-size="9.00">EnhancedMindMapManager.css</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Manager/EnhancedMindMapManager.tsx -->
<g id="node91" class="node">
<title>src/components/MindMap/components/Manager/EnhancedMindMapManager.tsx</title>
<g id="a_node91"><a xlink:href="src/components/MindMap/components/Manager/EnhancedMindMapManager.tsx" xlink:title="EnhancedMindMapManager.tsx">
<path fill="#bbfeff" stroke="black" d="M707.83,-5287.75C707.83,-5287.75 574.42,-5287.75 574.42,-5287.75 571.33,-5287.75 568.25,-5284.66 568.25,-5281.58 568.25,-5281.58 568.25,-5275.41 568.25,-5275.41 568.25,-5272.33 571.33,-5269.25 574.42,-5269.25 574.42,-5269.25 707.83,-5269.25 707.83,-5269.25 710.92,-5269.25 714,-5272.33 714,-5275.41 714,-5275.41 714,-5281.58 714,-5281.58 714,-5284.66 710.92,-5287.75 707.83,-5287.75"/>
<text text-anchor="start" x="576.25" y="-5275.2" font-family="Helvetica,sans-Serif" font-size="9.00">EnhancedMindMapManager.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Manager/EnhancedMindMapManager.tsx&#45;&gt;node_modules/@mui/material -->
<g id="edge153" class="edge">
<title>src/components/MindMap/components/Manager/EnhancedMindMapManager.tsx&#45;&gt;node_modules/@mui/material</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M645.66,-5268.98C658.15,-5234.8 700.18,-5114.74 714,-5011.5 717.8,-4983.07 708.86,-903.71 727,-881.5 816.34,-772.08 942.71,-748.03 1043,-847.5 1062.13,-866.47 1033.96,-1796.62 1051,-1817.5 1057.63,-1825.61 1067.95,-1829.45 1078.24,-1831.09"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1077.97,-1833.17 1084.17,-1831.76 1078.44,-1829 1077.97,-1833.17"/>
</g>
<!-- src/components/MindMap/components/Manager/EnhancedMindMapManager.tsx&#45;&gt;node_modules/react -->
<g id="edge154" class="edge">
<title>src/components/MindMap/components/Manager/EnhancedMindMapManager.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M645.66,-5268.98C658.15,-5234.8 700.18,-5114.74 714,-5011.5 717.77,-4983.32 709.13,-940.6 727,-918.5 771.6,-863.3 827.98,-929.31 883,-884.5 889.79,-878.97 883.66,-871.27 891,-866.5 919.32,-848.09 1019.02,-842.7 1043,-866.5 1063.39,-886.73 1033.01,-1878.1 1051,-1900.5 1057.58,-1908.69 1067.9,-1912.7 1078.19,-1914.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1077.91,-1916.59 1084.13,-1915.27 1078.44,-1912.42 1077.91,-1916.59"/>
</g>
<!-- src/components/MindMap/components/Manager/EnhancedMindMapManager.tsx&#45;&gt;node_modules/react&#45;draggable -->
<g id="edge155" class="edge">
<title>src/components/MindMap/components/Manager/EnhancedMindMapManager.tsx&#45;&gt;node_modules/react&#45;draggable</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M645.66,-5268.98C658.14,-5234.8 700.17,-5114.74 714,-5011.5 717.55,-4985.02 708.08,-1184.35 727,-1165.5 828.56,-1064.28 962.28,-1111.99 1043,-1230.5 1054.42,-1247.26 1038.52,-1944.51 1051,-1960.5 1054.78,-1965.34 1059.84,-1968.83 1065.44,-1971.33"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1064.43,-1973.2 1070.78,-1973.3 1065.88,-1969.26 1064.43,-1973.2"/>
</g>
<!-- src/components/MindMap/components/Manager/EnhancedMindMapManager.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge149" class="edge">
<title>src/components/MindMap/components/Manager/EnhancedMindMapManager.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M645.28,-5268.92C656.49,-5234.53 694.75,-5113.87 714,-5011.5 718.92,-4985.35 709.4,-4912.45 727,-4892.5 733.31,-4885.34 847.46,-4860.36 915.68,-4845.96"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="915.77,-4848.08 921.21,-4844.79 914.91,-4843.97 915.77,-4848.08"/>
</g>
<!-- src/components/MindMap/components/Manager/EnhancedMindMapManager.tsx&#45;&gt;src/components/MindMap/components/Manager/EnhancedMindMapManager.css -->
<g id="edge152" class="edge">
<title>src/components/MindMap/components/Manager/EnhancedMindMapManager.tsx&#45;&gt;src/components/MindMap/components/Manager/EnhancedMindMapManager.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M714.13,-5278.5C715.45,-5278.5 716.78,-5278.5 718.1,-5278.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="717.82,-5280.6 723.82,-5278.5 717.82,-5276.4 717.82,-5280.6"/>
</g>
<!-- src/components/MindMap/layouts/components/LayoutSelector.tsx -->
<g id="node92" class="node">
<title>src/components/MindMap/layouts/components/LayoutSelector.tsx</title>
<g id="a_node92"><a xlink:href="src/components/MindMap/layouts/components/LayoutSelector.tsx" xlink:title="LayoutSelector.tsx">
<path fill="#bbfeff" stroke="black" d="M839.96,-4156.75C839.96,-4156.75 762.04,-4156.75 762.04,-4156.75 758.96,-4156.75 755.88,-4153.66 755.88,-4150.58 755.88,-4150.58 755.88,-4144.41 755.88,-4144.41 755.88,-4141.33 758.96,-4138.25 762.04,-4138.25 762.04,-4138.25 839.96,-4138.25 839.96,-4138.25 843.04,-4138.25 846.12,-4141.33 846.12,-4144.41 846.12,-4144.41 846.12,-4150.58 846.12,-4150.58 846.12,-4153.66 843.04,-4156.75 839.96,-4156.75"/>
<text text-anchor="start" x="763.88" y="-4144.2" font-family="Helvetica,sans-Serif" font-size="9.00">LayoutSelector.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Manager/EnhancedMindMapManager.tsx&#45;&gt;src/components/MindMap/layouts/components/LayoutSelector.tsx -->
<g id="edge150" class="edge">
<title>src/components/MindMap/components/Manager/EnhancedMindMapManager.tsx&#45;&gt;src/components/MindMap/layouts/components/LayoutSelector.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M645.62,-5268.98C657.96,-5234.77 699.55,-5114.66 714,-5011.5 717.28,-4988.08 711.68,-4178.5 727,-4160.5 732.27,-4154.3 739.38,-4150.37 747.1,-4147.97"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="747.38,-4150.06 752.73,-4146.63 746.4,-4145.98 747.38,-4150.06"/>
</g>
<!-- src/components/MindMap/components/Manager/EnhancedMindMapManager.tsx&#45;&gt;src/components/MindMap/layouts/types.ts -->
<g id="edge151" class="edge">
<title>src/components/MindMap/components/Manager/EnhancedMindMapManager.tsx&#45;&gt;src/components/MindMap/layouts/types.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M645.59,-5268.97C657.82,-5234.75 699.12,-5114.6 714,-5011.5 718.35,-4981.35 707.76,-4487.1 727,-4463.5 818.06,-4351.75 902.77,-4423.86 1043,-4390.5 1102.98,-4376.22 1124.84,-4389.15 1177.75,-4357.5 1200.49,-4343.89 1219.74,-4319.59 1230.99,-4303.26"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1232.59,-4304.64 1234.17,-4298.49 1229.09,-4302.31 1232.59,-4304.64"/>
</g>
<!-- src/components/MindMap/layouts/components/LayoutSelector.tsx&#45;&gt;node_modules/react -->
<g id="edge278" class="edge">
<title>src/components/MindMap/layouts/components/LayoutSelector.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M846.53,-4150.83C860.21,-4149.4 874.02,-4144.99 883,-4134.5 900.74,-4113.77 873.77,-2191.64 891,-2170.5 934.61,-2116.97 998.48,-2191.26 1043,-2138.5 1057.92,-2120.82 1036.52,-1948.53 1051,-1930.5 1057.7,-1922.16 1068.16,-1917.85 1078.55,-1915.71"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.6,-1917.83 1084.19,-1914.8 1077.93,-1913.68 1078.6,-1917.83"/>
</g>
<!-- src/components/MindMap/layouts/components/LayoutSelector.tsx&#45;&gt;src/components/MindMap/layouts/types.ts -->
<g id="edge276" class="edge">
<title>src/components/MindMap/layouts/components/LayoutSelector.tsx&#45;&gt;src/components/MindMap/layouts/types.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M846.27,-4141.09C858.23,-4139.17 871.14,-4136.92 883,-4134.5 886.59,-4133.76 887.36,-4132.94 891,-4132.5 958.05,-4124.24 977.1,-4117.64 1043,-4132.5 1107.68,-4147.07 1126.98,-4153.86 1177.75,-4196.5 1202.29,-4217.1 1222.03,-4249.2 1232.77,-4268.97"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1230.86,-4269.86 1235.52,-4274.19 1234.57,-4267.9 1230.86,-4269.86"/>
</g>
<!-- src/components/MindMap/layouts/components/LayoutSelector.css -->
<g id="node142" class="node">
<title>src/components/MindMap/layouts/components/LayoutSelector.css</title>
<g id="a_node142"><a xlink:href="src/components/MindMap/layouts/components/LayoutSelector.css" xlink:title="LayoutSelector.css">
<path fill="#ffffcc" stroke="black" d="M1007.08,-4156.75C1007.08,-4156.75 926.92,-4156.75 926.92,-4156.75 923.83,-4156.75 920.75,-4153.66 920.75,-4150.58 920.75,-4150.58 920.75,-4144.41 920.75,-4144.41 920.75,-4141.33 923.83,-4138.25 926.92,-4138.25 926.92,-4138.25 1007.08,-4138.25 1007.08,-4138.25 1010.17,-4138.25 1013.25,-4141.33 1013.25,-4144.41 1013.25,-4144.41 1013.25,-4150.58 1013.25,-4150.58 1013.25,-4153.66 1010.17,-4156.75 1007.08,-4156.75"/>
<text text-anchor="start" x="928.75" y="-4144.2" font-family="Helvetica,sans-Serif" font-size="9.00">LayoutSelector.css</text>
</a>
</g>
</g>
<!-- src/components/MindMap/layouts/components/LayoutSelector.tsx&#45;&gt;src/components/MindMap/layouts/components/LayoutSelector.css -->
<g id="edge277" class="edge">
<title>src/components/MindMap/layouts/components/LayoutSelector.tsx&#45;&gt;src/components/MindMap/layouts/components/LayoutSelector.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M846.38,-4147.5C866.47,-4147.5 890.4,-4147.5 911.56,-4147.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="911.41,-4149.6 917.41,-4147.5 911.41,-4145.4 911.41,-4149.6"/>
</g>
<!-- src/components/MindMap/layouts/hooks/useLayout.ts -->
<g id="node143" class="node">
<title>src/components/MindMap/layouts/hooks/useLayout.ts</title>
<g id="a_node143"><a xlink:href="src/components/MindMap/layouts/hooks/useLayout.ts" xlink:title="useLayout.ts">
<path fill="#ddfeff" stroke="black" d="M994.71,-4061.75C994.71,-4061.75 939.29,-4061.75 939.29,-4061.75 936.21,-4061.75 933.12,-4058.66 933.12,-4055.58 933.12,-4055.58 933.12,-4049.41 933.12,-4049.41 933.12,-4046.33 936.21,-4043.25 939.29,-4043.25 939.29,-4043.25 994.71,-4043.25 994.71,-4043.25 997.79,-4043.25 1000.88,-4046.33 1000.88,-4049.41 1000.88,-4049.41 1000.88,-4055.58 1000.88,-4055.58 1000.88,-4058.66 997.79,-4061.75 994.71,-4061.75"/>
<text text-anchor="start" x="941.12" y="-4049.2" font-family="Helvetica,sans-Serif" font-size="9.00">useLayout.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/layouts/components/LayoutSelector.tsx&#45;&gt;src/components/MindMap/layouts/hooks/useLayout.ts -->
<g id="edge275" class="edge">
<title>src/components/MindMap/layouts/components/LayoutSelector.tsx&#45;&gt;src/components/MindMap/layouts/hooks/useLayout.ts</title>
<g id="a_edge275"><a xlink:title="no&#45;circular">
<path fill="none" stroke="orange" stroke-width="2" d="M846.29,-4150.18C859.79,-4148.67 873.55,-4144.37 883,-4134.5 904.35,-4112.19 869.79,-4087.93 891,-4065.5 897.25,-4058.88 905.57,-4054.88 914.39,-4052.58"/>
<polygon fill="orange" stroke="orange" stroke-width="2" points="924.06,-4053.29 929.69,-4050.35 923.45,-4049.13 924.06,-4053.29"/>
<polyline fill="none" stroke="orange" stroke-width="2" points="922.77,-4051.36 919.8,-4051.79"/>
<ellipse fill="none" stroke="orange" stroke-width="2" cx="916.43" cy="-4052.28" rx="2.4" ry="2.4"/>
</a>
</g>
<text text-anchor="middle" x="865.26" y="-4091.36" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">no&#45;circular</text>
</g>
<!-- src/components/MindMap/layouts/types.ts&#45;&gt;node_modules/konva -->
<g id="edge315" class="edge">
<title>src/components/MindMap/layouts/types.ts&#45;&gt;node_modules/konva</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1241.52,-4276.85C1239.81,-4116.46 1216.63,-2009.98 1177.75,-1961.5 1170.5,-1952.46 1158.97,-1948.08 1147.7,-1946.08"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1148.11,-1944.01 1141.88,-1945.29 1147.54,-1948.17 1148.11,-1944.01"/>
</g>
<!-- src/components/MindMap/layouts/types.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts -->
<g id="edge313" class="edge">
<title>src/components/MindMap/layouts/types.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1239.92,-4296.23C1232.35,-4339.65 1201.56,-4520.14 1185.75,-4669.5 1182.91,-4696.29 1185.8,-4764.78 1177.75,-4790.5 1166.95,-4825.01 1142.81,-4860 1127.56,-4879.9"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1126.11,-4878.35 1124.07,-4884.37 1129.42,-4880.94 1126.11,-4878.35"/>
</g>
<!-- src/components/MindMap/layouts/types.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge314" class="edge">
<title>src/components/MindMap/layouts/types.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1240.59,-4296.19C1227.96,-4356.93 1161.55,-4685.5 1181.75,-4784.5 1191.53,-4832.45 1218.01,-4884.23 1232.61,-4910.35"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1230.62,-4911.08 1235.41,-4915.26 1234.27,-4909 1230.62,-4911.08"/>
</g>
<!-- src/components/MindMap/components/Manager/EnhancedMindMapManagerContainer.tsx&#45;&gt;node_modules/react -->
<g id="edge158" class="edge">
<title>src/components/MindMap/components/Manager/EnhancedMindMapManagerContainer.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M540.49,-5268.95C546.11,-5265.74 551.17,-5261.65 555.25,-5256.5 574.7,-5231.93 547.94,-768.88 567.62,-744.5 609.45,-692.68 669.16,-761.73 714,-712.5 745.96,-677.41 691.45,-637.95 727,-606.5 753.3,-583.23 1018.1,-581.74 1043,-606.5 1055.75,-619.16 1039.76,-1886.47 1051,-1900.5 1057.7,-1908.84 1068.26,-1912.85 1078.74,-1914.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.22,-1916.66 1084.43,-1915.31 1078.73,-1912.49 1078.22,-1916.66"/>
</g>
<!-- src/components/MindMap/components/Manager/EnhancedMindMapManagerContainer.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge156" class="edge">
<title>src/components/MindMap/components/Manager/EnhancedMindMapManagerContainer.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M540.45,-5268.92C546.08,-5265.71 551.15,-5261.64 555.25,-5256.5 576.76,-5229.52 543.11,-4038.77 567.62,-4014.5 592.53,-3989.84 858.09,-3989.84 883,-4014.5 898.32,-4029.66 880.28,-4771.79 891,-4790.5 899.22,-4804.84 914,-4815.12 928.29,-4822.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="927.05,-4823.94 933.38,-4824.54 928.81,-4820.12 927.05,-4823.94"/>
</g>
<!-- src/components/MindMap/components/Manager/EnhancedMindMapManagerContainer.tsx&#45;&gt;src/components/MindMap/components/Manager/EnhancedMindMapManager.tsx -->
<g id="edge157" class="edge">
<title>src/components/MindMap/components/Manager/EnhancedMindMapManagerContainer.tsx&#45;&gt;src/components/MindMap/components/Manager/EnhancedMindMapManager.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M555.51,-5278.5C556.69,-5278.5 557.87,-5278.5 559.05,-5278.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="558.99,-5280.6 564.99,-5278.5 558.99,-5276.4 558.99,-5280.6"/>
</g>
<!-- src/components/MindMap/components/MindMapManager/MindMapManager.css -->
<g id="node95" class="node">
<title>src/components/MindMap/components/MindMapManager/MindMapManager.css</title>
<g id="a_node95"><a xlink:href="src/components/MindMap/components/MindMapManager/MindMapManager.css" xlink:title="MindMapManager.css">
<path fill="#ffffcc" stroke="black" d="M687.96,-5348.75C687.96,-5348.75 594.29,-5348.75 594.29,-5348.75 591.21,-5348.75 588.12,-5345.66 588.12,-5342.58 588.12,-5342.58 588.12,-5336.41 588.12,-5336.41 588.12,-5333.33 591.21,-5330.25 594.29,-5330.25 594.29,-5330.25 687.96,-5330.25 687.96,-5330.25 691.04,-5330.25 694.12,-5333.33 694.12,-5336.41 694.12,-5336.41 694.12,-5342.58 694.12,-5342.58 694.12,-5345.66 691.04,-5348.75 687.96,-5348.75"/>
<text text-anchor="start" x="596.12" y="-5336.2" font-family="Helvetica,sans-Serif" font-size="9.00">MindMapManager.css</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/MindMapManager/MindMapManager.tsx -->
<g id="node96" class="node">
<title>src/components/MindMap/components/MindMapManager/MindMapManager.tsx</title>
<g id="a_node96"><a xlink:href="src/components/MindMap/components/MindMapManager/MindMapManager.tsx" xlink:title="MindMapManager.tsx">
<path fill="#bbfeff" stroke="black" d="M507.83,-5348.75C507.83,-5348.75 416.42,-5348.75 416.42,-5348.75 413.33,-5348.75 410.25,-5345.66 410.25,-5342.58 410.25,-5342.58 410.25,-5336.41 410.25,-5336.41 410.25,-5333.33 413.33,-5330.25 416.42,-5330.25 416.42,-5330.25 507.83,-5330.25 507.83,-5330.25 510.92,-5330.25 514,-5333.33 514,-5336.41 514,-5336.41 514,-5342.58 514,-5342.58 514,-5345.66 510.92,-5348.75 507.83,-5348.75"/>
<text text-anchor="start" x="418.25" y="-5336.2" font-family="Helvetica,sans-Serif" font-size="9.00">MindMapManager.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/MindMapManager/MindMapManager.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge159" class="edge">
<title>src/components/MindMap/components/MindMapManager/MindMapManager.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M514.09,-5339.01C529.67,-5336.15 545.31,-5329.97 555.25,-5317.5 577.64,-5289.41 542.11,-4049.77 567.62,-4024.5 590.74,-4001.6 682.68,-4015.7 714,-4024.5 798.69,-4048.27 839.46,-4051.06 883,-4127.5 901.23,-4159.5 872.66,-4758.55 891,-4790.5 899.23,-4804.83 914.02,-4815.11 928.3,-4822.19"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="927.06,-4823.93 933.39,-4824.53 928.82,-4820.12 927.06,-4823.93"/>
</g>
<!-- src/components/MindMap/components/MindMapManager/MindMapManager.tsx&#45;&gt;src/components/MindMap/components/MindMapManager/MindMapManager.css -->
<g id="edge160" class="edge">
<title>src/components/MindMap/components/MindMapManager/MindMapManager.tsx&#45;&gt;src/components/MindMap/components/MindMapManager/MindMapManager.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M514.32,-5339.5C534.51,-5339.5 557.87,-5339.5 578.94,-5339.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="578.79,-5341.6 584.79,-5339.5 578.79,-5337.4 578.79,-5341.6"/>
</g>
<!-- src/components/MindMap/components/MindMapView.tsx -->
<g id="node97" class="node">
<title>src/components/MindMap/components/MindMapView.tsx</title>
<g id="a_node97"><a xlink:href="src/components/MindMap/components/MindMapView.tsx" xlink:title="MindMapView.tsx">
<path fill="#bbfeff" stroke="black" d="M499.58,-5069.75C499.58,-5069.75 424.67,-5069.75 424.67,-5069.75 421.58,-5069.75 418.5,-5066.66 418.5,-5063.58 418.5,-5063.58 418.5,-5057.41 418.5,-5057.41 418.5,-5054.33 421.58,-5051.25 424.67,-5051.25 424.67,-5051.25 499.58,-5051.25 499.58,-5051.25 502.67,-5051.25 505.75,-5054.33 505.75,-5057.41 505.75,-5057.41 505.75,-5063.58 505.75,-5063.58 505.75,-5066.66 502.67,-5069.75 499.58,-5069.75"/>
<text text-anchor="start" x="426.5" y="-5057.2" font-family="Helvetica,sans-Serif" font-size="9.00">MindMapView.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/MindMapView.tsx&#45;&gt;./ConnectionRenderer -->
<g id="edge165" class="edge">
<title>src/components/MindMap/components/MindMapView.tsx&#45;&gt;./ConnectionRenderer</title>
<g id="a_edge165"><a xlink:title="not&#45;to&#45;unresolvable">
<path fill="none" stroke="red" stroke-width="2" d="M506.02,-5065.74C523.91,-5065.4 543.26,-5061.26 555.25,-5047.5 575.96,-5023.72 548.16,-531.31 567.62,-506.5 571.39,-501.7 576.22,-498.05 581.59,-495.3"/>
<polygon fill="red" stroke="red" stroke-width="2" points="582.27,-497.29 587,-493.05 580.66,-493.42 582.27,-497.29"/>
</a>
</g>
<text text-anchor="middle" x="522.16" y="-2770.07" font-family="Helvetica,sans-Serif" font-size="9.00" fill="red">not&#45;to&#45;unresolvable</text>
</g>
<!-- src/components/MindMap/components/MindMapView.tsx&#45;&gt;./NodeRenderer -->
<g id="edge166" class="edge">
<title>src/components/MindMap/components/MindMapView.tsx&#45;&gt;./NodeRenderer</title>
<g id="a_edge166"><a xlink:title="not&#45;to&#45;unresolvable">
<path fill="none" stroke="red" stroke-width="2" d="M506.02,-5065.74C523.91,-5065.4 543.26,-5061.26 555.25,-5047.5 575.71,-5024.01 548.4,-587 567.62,-562.5 574.07,-554.28 583.62,-549.46 593.7,-546.7"/>
<polygon fill="red" stroke="red" stroke-width="2" points="593.85,-548.81 599.27,-545.48 592.96,-544.71 593.85,-548.81"/>
</a>
</g>
<text text-anchor="middle" x="522.15" y="-2797.24" font-family="Helvetica,sans-Serif" font-size="9.00" fill="red">not&#45;to&#45;unresolvable</text>
</g>
<!-- src/components/MindMap/components/MindMapView.tsx&#45;&gt;node_modules/react -->
<g id="edge167" class="edge">
<title>src/components/MindMap/components/MindMapView.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M506.02,-5065.74C523.91,-5065.4 543.26,-5061.26 555.25,-5047.5 575.86,-5023.84 547.34,-553.43 567.62,-529.5 579.23,-515.79 709.1,-504 727,-502.5 796.98,-496.61 993.2,-452.98 1043,-502.5 1056.77,-516.19 1038.86,-1885.34 1051,-1900.5 1057.69,-1908.85 1068.26,-1912.85 1078.74,-1914.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.22,-1916.66 1084.43,-1915.31 1078.73,-1912.49 1078.22,-1916.66"/>
</g>
<!-- src/components/MindMap/components/MindMapView.tsx&#45;&gt;src/components/MindMap/types/index.ts -->
<g id="edge164" class="edge">
<title>src/components/MindMap/components/MindMapView.tsx&#45;&gt;src/components/MindMap/types/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M505.97,-5065.7C523.85,-5065.35 543.21,-5061.21 555.25,-5047.5 577.04,-5022.67 546.19,-3883.63 567.62,-3858.5 614.34,-3803.71 842.2,-3793.06 930.73,-3790.99"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="930.61,-3793.1 936.57,-3790.87 930.52,-3788.9 930.61,-3793.1"/>
</g>
<!-- src/components/MindMap/components/MindMapView.tsx&#45;&gt;src/components/MindMap/MindMap.css -->
<g id="edge163" class="edge">
<title>src/components/MindMap/components/MindMapView.tsx&#45;&gt;src/components/MindMap/MindMap.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M505.97,-5065.69C523.84,-5065.34 543.2,-5061.2 555.25,-5047.5 574.71,-5025.36 550.44,-4010.44 567.62,-3986.5 610.52,-3926.7 702.3,-3913.99 756.8,-3911.99"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="756.57,-3914.1 762.51,-3911.83 756.46,-3909.9 756.57,-3914.1"/>
</g>
<!-- src/components/MindMap/hooks/useMindMapCore.ts -->
<g id="node98" class="node">
<title>src/components/MindMap/hooks/useMindMapCore.ts</title>
<g id="a_node98"><a xlink:href="src/components/MindMap/hooks/useMindMapCore.ts" xlink:title="useMindMapCore.ts">
<path fill="#ddfeff" stroke="black" d="M684.21,-3547.75C684.21,-3547.75 598.04,-3547.75 598.04,-3547.75 594.96,-3547.75 591.88,-3544.66 591.88,-3541.58 591.88,-3541.58 591.88,-3535.41 591.88,-3535.41 591.88,-3532.33 594.96,-3529.25 598.04,-3529.25 598.04,-3529.25 684.21,-3529.25 684.21,-3529.25 687.29,-3529.25 690.38,-3532.33 690.38,-3535.41 690.38,-3535.41 690.38,-3541.58 690.38,-3541.58 690.38,-3544.66 687.29,-3547.75 684.21,-3547.75"/>
<text text-anchor="start" x="599.88" y="-3535.2" font-family="Helvetica,sans-Serif" font-size="9.00">useMindMapCore.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/MindMapView.tsx&#45;&gt;src/components/MindMap/hooks/useMindMapCore.ts -->
<g id="edge161" class="edge">
<title>src/components/MindMap/components/MindMapView.tsx&#45;&gt;src/components/MindMap/hooks/useMindMapCore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M505.99,-5065.71C523.86,-5065.37 543.22,-5061.22 555.25,-5047.5 582.58,-5016.3 541.52,-3586.72 567.62,-3554.5 571.75,-3549.4 577.1,-3545.7 583.03,-3543.04"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="583.62,-3545.06 588.55,-3541.05 582.2,-3541.1 583.62,-3545.06"/>
</g>
<!-- src/components/MindMap/hooks/useZoomAndPan.ts -->
<g id="node99" class="node">
<title>src/components/MindMap/hooks/useZoomAndPan.ts</title>
<g id="a_node99"><a xlink:href="src/components/MindMap/hooks/useZoomAndPan.ts" xlink:title="useZoomAndPan.ts">
<path fill="#ddfeff" stroke="black" d="M683.08,-3423.75C683.08,-3423.75 599.17,-3423.75 599.17,-3423.75 596.08,-3423.75 593,-3420.66 593,-3417.58 593,-3417.58 593,-3411.41 593,-3411.41 593,-3408.33 596.08,-3405.25 599.17,-3405.25 599.17,-3405.25 683.08,-3405.25 683.08,-3405.25 686.17,-3405.25 689.25,-3408.33 689.25,-3411.41 689.25,-3411.41 689.25,-3417.58 689.25,-3417.58 689.25,-3420.66 686.17,-3423.75 683.08,-3423.75"/>
<text text-anchor="start" x="601" y="-3411.2" font-family="Helvetica,sans-Serif" font-size="9.00">useZoomAndPan.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/MindMapView.tsx&#45;&gt;src/components/MindMap/hooks/useZoomAndPan.ts -->
<g id="edge162" class="edge">
<title>src/components/MindMap/components/MindMapView.tsx&#45;&gt;src/components/MindMap/hooks/useZoomAndPan.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M505.99,-5065.71C523.87,-5065.37 543.23,-5061.23 555.25,-5047.5 584.84,-5013.7 539.36,-3465.4 567.62,-3430.5 572.02,-3425.07 577.79,-3421.23 584.17,-3418.53"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="584.64,-3420.59 589.65,-3416.68 583.29,-3416.62 584.64,-3420.59"/>
</g>
<!-- src/components/MindMap/hooks/useMindMapCore.ts&#45;&gt;node_modules/react -->
<g id="edge242" class="edge">
<title>src/components/MindMap/hooks/useMindMapCore.ts&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M690.82,-3536.8C699.78,-3534.18 708.14,-3529.76 714,-3522.5 736.92,-3494.05 704.19,-921.03 727,-892.5 771.44,-836.92 827.09,-900.51 883,-856.5 889.25,-851.58 884.24,-844.67 891,-840.5 919.73,-822.74 1019.03,-816.7 1043,-840.5 1063.9,-861.24 1032.56,-1877.54 1051,-1900.5 1057.58,-1908.69 1067.9,-1912.7 1078.19,-1914.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1077.91,-1916.59 1084.13,-1915.27 1078.44,-1912.42 1077.91,-1916.59"/>
</g>
<!-- src/components/MindMap/hooks/useMindMapCore.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge240" class="edge">
<title>src/components/MindMap/hooks/useMindMapCore.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M690.57,-3540.4C699.55,-3543 707.98,-3547.36 714,-3554.5 750.75,-3598.02 691.35,-3766.07 727,-3810.5 772.01,-3866.58 839.18,-3796.47 883,-3853.5 898.86,-3874.13 878.08,-4767.9 891,-4790.5 899.2,-4804.84 913.99,-4815.12 928.28,-4822.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="927.04,-4823.95 933.37,-4824.55 928.8,-4820.13 927.04,-4823.95"/>
</g>
<!-- src/components/MindMap/hooks/useMindMapCore.ts&#45;&gt;src/components/MindMap/types/index.ts -->
<g id="edge241" class="edge">
<title>src/components/MindMap/hooks/useMindMapCore.ts&#45;&gt;src/components/MindMap/types/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M690.52,-3540.45C699.5,-3543.04 707.94,-3547.39 714,-3554.5 744.92,-3590.74 693.84,-3734.29 727,-3768.5 754.38,-3796.74 871.38,-3795.29 930.91,-3792.58"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="930.69,-3794.69 936.58,-3792.31 930.48,-3790.5 930.69,-3794.69"/>
</g>
<!-- src/components/MindMap/hooks/useMindMapCore.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts -->
<g id="edge238" class="edge">
<title>src/components/MindMap/hooks/useMindMapCore.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M690.87,-3540.57C699.71,-3543.17 708.02,-3547.5 714,-3554.5 743.67,-3589.19 694.27,-3727.68 727,-3759.5 752.19,-3783.98 1018.49,-3745.32 1043,-3770.5 1052.88,-3780.65 1047.28,-4776.82 1051,-4790.5 1060.35,-4824.88 1083.69,-4859.71 1098.67,-4879.64"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1096.97,-4880.88 1102.3,-4884.36 1100.3,-4878.32 1096.97,-4880.88"/>
</g>
<!-- src/components/MindMap/hooks/useMindMapCore.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge239" class="edge">
<title>src/components/MindMap/hooks/useMindMapCore.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M690.74,-3540.69C699.59,-3543.28 707.93,-3547.58 714,-3554.5 735.62,-3579.15 703.06,-3679.08 727,-3701.5 745.29,-3718.61 1160.17,-3693.65 1177.75,-3711.5 1188.27,-3722.17 1182.81,-4775.8 1185.75,-4790.5 1194.8,-4835.76 1218.67,-4884.97 1232.1,-4910.3"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1230.08,-4910.98 1234.78,-4915.26 1233.78,-4908.99 1230.08,-4910.98"/>
</g>
<!-- src/components/MindMap/hooks/useZoomAndPan.ts&#45;&gt;node_modules/react -->
<g id="edge250" class="edge">
<title>src/components/MindMap/hooks/useZoomAndPan.ts&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M689.71,-3413.11C699.09,-3410.54 707.9,-3406.06 714,-3398.5 736.07,-3371.12 705.45,-894.29 727,-866.5 770.92,-809.84 823.81,-865.94 883,-825.5 887.99,-822.08 885.61,-817.24 891,-814.5 921.1,-799.17 1019.03,-790.7 1043,-814.5 1064.41,-835.75 1032.11,-1876.97 1051,-1900.5 1057.58,-1908.69 1067.89,-1912.7 1078.19,-1914.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1077.91,-1916.59 1084.12,-1915.27 1078.44,-1912.42 1077.91,-1916.59"/>
</g>
<!-- src/components/MindMap/hooks/useZoomAndPan.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge249" class="edge">
<title>src/components/MindMap/hooks/useZoomAndPan.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M689.54,-3416.03C698.92,-3418.59 707.78,-3423.03 714,-3430.5 739.04,-3460.57 701.43,-3752.86 727,-3782.5 773.07,-3835.89 837.7,-3757.45 883,-3811.5 900.47,-3832.34 877.5,-4766.88 891,-4790.5 899.2,-4804.84 913.99,-4815.13 928.27,-4822.21"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="927.04,-4823.95 933.37,-4824.55 928.79,-4820.13 927.04,-4823.95"/>
</g>
<!-- src/components/MindMap/components/Node/ConnectionComponent.tsx -->
<g id="node100" class="node">
<title>src/components/MindMap/components/Node/ConnectionComponent.tsx</title>
<g id="a_node100"><a xlink:href="src/components/MindMap/components/Node/ConnectionComponent.tsx" xlink:title="ConnectionComponent.tsx">
<path fill="#bbfeff" stroke="black" d="M857.21,-5348.75C857.21,-5348.75 744.79,-5348.75 744.79,-5348.75 741.71,-5348.75 738.62,-5345.66 738.62,-5342.58 738.62,-5342.58 738.62,-5336.41 738.62,-5336.41 738.62,-5333.33 741.71,-5330.25 744.79,-5330.25 744.79,-5330.25 857.21,-5330.25 857.21,-5330.25 860.29,-5330.25 863.38,-5333.33 863.38,-5336.41 863.38,-5336.41 863.38,-5342.58 863.38,-5342.58 863.38,-5345.66 860.29,-5348.75 857.21,-5348.75"/>
<text text-anchor="start" x="746.62" y="-5336.2" font-family="Helvetica,sans-Serif" font-size="9.00">ConnectionComponent.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Node/ConnectionComponent.tsx&#45;&gt;node_modules/react -->
<g id="edge170" class="edge">
<title>src/components/MindMap/components/Node/ConnectionComponent.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M863.58,-5332.23C871.13,-5328.83 877.95,-5324.09 883,-5317.5 896.19,-5300.29 877.32,-2213.31 891,-2196.5 934.57,-2142.94 998.59,-2217.35 1043,-2164.5 1059.73,-2144.58 1034.75,-1950.81 1051,-1930.5 1057.68,-1922.14 1068.14,-1917.83 1078.53,-1915.69"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.58,-1917.81 1084.17,-1914.78 1077.91,-1913.67 1078.58,-1917.81"/>
</g>
<!-- src/components/MindMap/components/Node/ConnectionComponent.tsx&#45;&gt;node_modules/react&#45;konva -->
<g id="edge171" class="edge">
<title>src/components/MindMap/components/Node/ConnectionComponent.tsx&#45;&gt;node_modules/react&#45;konva</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M863.58,-5332.23C871.13,-5328.83 877.95,-5324.09 883,-5317.5 894.32,-5302.73 887.94,-2656.85 891,-2638.5 925.54,-2431.09 1002.69,-2396.85 1043,-2190.5 1045.9,-2175.64 1041.4,-2066.2 1051,-2054.5 1056.65,-2047.6 1064.87,-2043.47 1073.46,-2041.05"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1073.86,-2043.11 1079.24,-2039.73 1072.92,-2039.02 1073.86,-2043.11"/>
</g>
<!-- src/components/MindMap/components/Node/ConnectionComponent.tsx&#45;&gt;src/components/MindMap/types/index.ts -->
<g id="edge169" class="edge">
<title>src/components/MindMap/components/Node/ConnectionComponent.tsx&#45;&gt;src/components/MindMap/types/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M863.56,-5332.22C871.11,-5328.82 877.94,-5324.08 883,-5317.5 895.79,-5300.86 877.76,-3822.77 891,-3806.5 900.46,-3794.86 916.12,-3790.37 930.68,-3788.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="930.65,-3791.09 936.52,-3788.66 930.41,-3786.9 930.65,-3791.09"/>
</g>
<!-- src/components/MindMap/components/Node/ConnectionComponent.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx -->
<g id="edge168" class="edge">
<title>src/components/MindMap/components/Node/ConnectionComponent.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M863.56,-5332.22C871.11,-5328.82 877.94,-5324.08 883,-5317.5 896.1,-5300.46 885.17,-3791.18 891,-3770.5 902.95,-3728.07 933.67,-3685.64 951.87,-3663.08"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="953.4,-3664.52 955.6,-3658.55 950.16,-3661.85 953.4,-3664.52"/>
</g>
<!-- src/components/MindMap/components/Node/NodeComponent.tsx -->
<g id="node101" class="node">
<title>src/components/MindMap/components/Node/NodeComponent.tsx</title>
<g id="a_node101"><a xlink:href="src/components/MindMap/components/Node/NodeComponent.tsx" xlink:title="NodeComponent.tsx">
<path fill="#bbfeff" stroke="black" d="M844.83,-5379.75C844.83,-5379.75 757.17,-5379.75 757.17,-5379.75 754.08,-5379.75 751,-5376.66 751,-5373.58 751,-5373.58 751,-5367.41 751,-5367.41 751,-5364.33 754.08,-5361.25 757.17,-5361.25 757.17,-5361.25 844.83,-5361.25 844.83,-5361.25 847.92,-5361.25 851,-5364.33 851,-5367.41 851,-5367.41 851,-5373.58 851,-5373.58 851,-5376.66 847.92,-5379.75 844.83,-5379.75"/>
<text text-anchor="start" x="759" y="-5367.2" font-family="Helvetica,sans-Serif" font-size="9.00">NodeComponent.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/components/Node/NodeComponent.tsx&#45;&gt;node_modules/konva -->
<g id="edge174" class="edge">
<title>src/components/MindMap/components/Node/NodeComponent.tsx&#45;&gt;node_modules/konva</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M851.35,-5371.28C863.45,-5368.93 875.16,-5364.01 883,-5354.5 895.34,-5339.52 886.97,-2579.47 891,-2560.5 925.76,-2397 1005.92,-2379.48 1043,-2216.5 1046.14,-2202.67 1042.16,-1972.58 1051,-1961.5 1057.67,-1953.13 1068.12,-1948.82 1078.51,-1946.68"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.57,-1948.8 1084.15,-1945.77 1077.9,-1944.66 1078.57,-1948.8"/>
</g>
<!-- src/components/MindMap/components/Node/NodeComponent.tsx&#45;&gt;node_modules/react -->
<g id="edge175" class="edge">
<title>src/components/MindMap/components/Node/NodeComponent.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M851.35,-5371.29C863.45,-5368.94 875.16,-5364.01 883,-5354.5 896.83,-5337.71 877.27,-2239.37 891,-2222.5 934.57,-2168.94 998.68,-2243.43 1043,-2190.5 1061.55,-2168.33 1032.99,-1953.1 1051,-1930.5 1057.66,-1922.13 1068.12,-1917.82 1078.51,-1915.68"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.56,-1917.8 1084.15,-1914.77 1077.89,-1913.65 1078.56,-1917.8"/>
</g>
<!-- src/components/MindMap/components/Node/NodeComponent.tsx&#45;&gt;node_modules/react&#45;konva -->
<g id="edge176" class="edge">
<title>src/components/MindMap/components/Node/NodeComponent.tsx&#45;&gt;node_modules/react&#45;konva</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M851.35,-5371.28C863.45,-5368.93 875.16,-5364.01 883,-5354.5 894.88,-5340.08 885.77,-2682.43 891,-2664.5 926.35,-2543.28 1006.52,-2545.37 1043,-2424.5 1048.94,-2404.81 1038.26,-2070.63 1051,-2054.5 1056.58,-2047.43 1064.85,-2043.25 1073.53,-2040.83"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1073.99,-2042.88 1079.39,-2039.52 1073.07,-2038.78 1073.99,-2042.88"/>
</g>
<!-- src/components/MindMap/components/Node/NodeComponent.tsx&#45;&gt;src/components/MindMap/types/index.ts -->
<g id="edge173" class="edge">
<title>src/components/MindMap/components/Node/NodeComponent.tsx&#45;&gt;src/components/MindMap/types/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M851.33,-5371.27C863.43,-5368.92 875.15,-5364 883,-5354.5 896.69,-5337.92 877.43,-3823.17 891,-3806.5 900.46,-3794.86 916.11,-3790.37 930.68,-3788.98"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="930.65,-3791.09 936.52,-3788.66 930.41,-3786.9 930.65,-3791.09"/>
</g>
<!-- src/components/MindMap/components/Node/NodeComponent.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx -->
<g id="edge172" class="edge">
<title>src/components/MindMap/components/Node/NodeComponent.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M851.33,-5371.27C863.43,-5368.92 875.15,-5364 883,-5354.5 897.01,-5337.53 885.04,-3791.67 891,-3770.5 902.95,-3728.07 933.67,-3685.64 951.87,-3663.08"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="953.4,-3664.52 955.6,-3658.55 950.16,-3661.85 953.4,-3664.52"/>
</g>
<!-- src/components/MindMap/components/index.ts&#45;&gt;src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx -->
<g id="edge183" class="edge">
<title>src/components/MindMap/components/index.ts&#45;&gt;src/components/MindMap/components/Canvas/MindMapCanvasSimple.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M489.62,-5670.5C510.87,-5670.5 541.7,-5670.5 569.93,-5670.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="576.05,-5672.6 570.05,-5670.5 576.05,-5668.4 576.05,-5672.6"/>
</g>
<!-- src/components/MindMap/components/index.ts&#45;&gt;src/components/MindMap/components/Controls/MindMapToolbar.tsx -->
<g id="edge184" class="edge">
<title>src/components/MindMap/components/index.ts&#45;&gt;src/components/MindMap/components/Controls/MindMapToolbar.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M464.54,-5661.06C470.24,-5626.49 494.85,-5505.73 567.62,-5447.5 572.91,-5443.27 579.07,-5440.1 585.54,-5437.74"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="591.77,-5437.94 585.42,-5437.78 590.48,-5433.94 591.77,-5437.94"/>
</g>
<!-- src/components/MindMap/components/index.ts&#45;&gt;src/components/MindMap/components/Dialogs/DesignControlsDialog.tsx -->
<g id="edge185" class="edge">
<title>src/components/MindMap/components/index.ts&#45;&gt;src/components/MindMap/components/Dialogs/DesignControlsDialog.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M489.22,-5666.31C510.11,-5664.58 538.58,-5666.13 555.25,-5683.5 574.43,-5703.47 549.23,-5786.8 567.62,-5807.5 569.81,-5809.95 572.28,-5812.08 574.96,-5813.92"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="578.8,-5818.61 574.68,-5813.76 580.92,-5814.98 578.8,-5818.61"/>
</g>
<!-- src/components/MindMap/components/index.ts&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog.tsx -->
<g id="edge186" class="edge">
<title>src/components/MindMap/components/index.ts&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M489.41,-5666.13C510.42,-5664.29 538.95,-5665.78 555.25,-5683.5 587.95,-5719.04 532.51,-5867.33 567.62,-5900.5 579.45,-5911.66 702.02,-5911.5 714,-5900.5 739.13,-5877.41 704.5,-5773.15 727,-5747.5 734.02,-5739.49 744.03,-5734.98 754.4,-5732.53"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="760.66,-5733.46 754.37,-5732.54 759.86,-5729.34 760.66,-5733.46"/>
</g>
<!-- src/components/MindMap/components/index.ts&#45;&gt;src/components/MindMap/components/Dialogs/ProjectDialog.tsx -->
<g id="edge187" class="edge">
<title>src/components/MindMap/components/index.ts&#45;&gt;src/components/MindMap/components/Dialogs/ProjectDialog.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M489.5,-5666.41C510.27,-5664.81 538.44,-5666.5 555.25,-5683.5 585.2,-5713.76 538.45,-5747.48 567.62,-5778.5 573.92,-5785.19 582.38,-5789.24 591.3,-5791.59"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="596.76,-5794.91 591.34,-5791.59 597.64,-5790.81 596.76,-5794.91"/>
</g>
<!-- src/components/MindMap/components/index.ts&#45;&gt;src/governance/chat/index.ts -->
<g id="edge182" class="edge">
<title>src/components/MindMap/components/index.ts&#45;&gt;src/governance/chat/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M434.65,-5664.34C404.38,-5659.3 358.7,-5657.47 349,-5689.5 345.64,-5700.58 345.64,-6512.41 349,-6523.5 358.02,-6553.28 397.83,-6554.99 427.7,-6551.75"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="433.93,-6553.09 427.72,-6551.75 433.42,-6548.93 433.93,-6553.09"/>
</g>
<!-- src/components/MindMap/components/index.ts&#45;&gt;src/components/MindMap/components/Node/ConnectionComponent.tsx -->
<g id="edge188" class="edge">
<title>src/components/MindMap/components/index.ts&#45;&gt;src/components/MindMap/components/Node/ConnectionComponent.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M466.13,-5660.82C475.59,-5632.18 507.78,-5547.62 567.62,-5512.5 595.76,-5495.98 690.78,-5524.4 714,-5501.5 737.19,-5478.62 705.48,-5379.94 727,-5355.5 728.73,-5353.53 730.64,-5351.77 732.7,-5350.21"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="738.74,-5348.8 732.56,-5350.3 736.45,-5345.27 738.74,-5348.8"/>
</g>
<!-- src/components/MindMap/components/index.ts&#45;&gt;src/components/MindMap/components/Node/NodeComponent.tsx -->
<g id="edge189" class="edge">
<title>src/components/MindMap/components/index.ts&#45;&gt;src/components/MindMap/components/Node/NodeComponent.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M467.35,-5661C479.12,-5635.78 514.92,-5566.9 567.62,-5535.5 624.26,-5501.75 668.78,-5559.46 714,-5511.5 752.32,-5470.85 689.78,-5428.14 727,-5386.5 731.67,-5381.27 737.62,-5377.54 744.09,-5374.9"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="750.46,-5374.93 744.11,-5374.9 749.09,-5370.96 750.46,-5374.93"/>
</g>
<!-- src/components/MindMap/config/api.ts -->
<g id="node103" class="node">
<title>src/components/MindMap/config/api.ts</title>
<g id="a_node103"><a xlink:href="src/components/MindMap/config/api.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M292.21,-3180.75C292.21,-3180.75 250.54,-3180.75 250.54,-3180.75 247.46,-3180.75 244.38,-3177.66 244.38,-3174.58 244.38,-3174.58 244.38,-3168.41 244.38,-3168.41 244.38,-3165.33 247.46,-3162.25 250.54,-3162.25 250.54,-3162.25 292.21,-3162.25 292.21,-3162.25 295.29,-3162.25 298.38,-3165.33 298.38,-3168.41 298.38,-3168.41 298.38,-3174.58 298.38,-3174.58 298.38,-3177.66 295.29,-3180.75 292.21,-3180.75"/>
<text text-anchor="start" x="260.5" y="-3168.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">api.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/core/MindMapCore.ts -->
<g id="node104" class="node">
<title>src/components/MindMap/core/MindMapCore.ts</title>
<g id="a_node104"><a xlink:href="src/components/MindMap/core/MindMapCore.ts" xlink:title="MindMapCore.ts">
<path fill="#ddfeff" stroke="black" d="M676.71,-4680.75C676.71,-4680.75 605.54,-4680.75 605.54,-4680.75 602.46,-4680.75 599.38,-4677.66 599.38,-4674.58 599.38,-4674.58 599.38,-4668.41 599.38,-4668.41 599.38,-4665.33 602.46,-4662.25 605.54,-4662.25 605.54,-4662.25 676.71,-4662.25 676.71,-4662.25 679.79,-4662.25 682.88,-4665.33 682.88,-4668.41 682.88,-4668.41 682.88,-4674.58 682.88,-4674.58 682.88,-4677.66 679.79,-4680.75 676.71,-4680.75"/>
<text text-anchor="start" x="607.38" y="-4668.2" font-family="Helvetica,sans-Serif" font-size="9.00">MindMapCore.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/core/MindMapCore.ts&#45;&gt;src/components/MindMap/types/index.ts -->
<g id="edge192" class="edge">
<title>src/components/MindMap/core/MindMapCore.ts&#45;&gt;src/components/MindMap/types/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M645.73,-4662.03C658.11,-4629.07 698.88,-4515.96 714,-4418.5 717.56,-4395.54 711.87,-4018.13 727,-4000.5 772.92,-3946.97 836.62,-4024.62 883,-3971.5 895.07,-3957.67 879.11,-3820.48 891,-3806.5 900.71,-3795.07 916.39,-3790.6 930.91,-3789.18"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="930.86,-3791.29 936.72,-3788.83 930.61,-3787.09 930.86,-3791.29"/>
</g>
<!-- src/components/MindMap/core/createNode.ts -->
<g id="node105" class="node">
<title>src/components/MindMap/core/createNode.ts</title>
<g id="a_node105"><a xlink:href="src/components/MindMap/core/createNode.ts" xlink:title="createNode.ts">
<path fill="#ddfeff" stroke="black" d="M1143.46,-5008.75C1143.46,-5008.75 1082.79,-5008.75 1082.79,-5008.75 1079.71,-5008.75 1076.62,-5005.66 1076.62,-5002.58 1076.62,-5002.58 1076.62,-4996.41 1076.62,-4996.41 1076.62,-4993.33 1079.71,-4990.25 1082.79,-4990.25 1082.79,-4990.25 1143.46,-4990.25 1143.46,-4990.25 1146.54,-4990.25 1149.62,-4993.33 1149.62,-4996.41 1149.62,-4996.41 1149.62,-5002.58 1149.62,-5002.58 1149.62,-5005.66 1146.54,-5008.75 1143.46,-5008.75"/>
<text text-anchor="start" x="1084.62" y="-4996.2" font-family="Helvetica,sans-Serif" font-size="9.00">createNode.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/core/createNode.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge196" class="edge">
<title>src/components/MindMap/core/createNode.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1149.83,-4992.26C1159.23,-4989.6 1169.13,-4986.08 1177.75,-4981.5 1196.57,-4971.49 1214.81,-4955.2 1226.95,-4943.12"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1228.21,-4944.83 1230.91,-4939.08 1225.21,-4941.9 1228.21,-4944.83"/>
</g>
<!-- src/components/MindMap/core/models/HatContribution.ts -->
<g id="node106" class="node">
<title>src/components/MindMap/core/models/HatContribution.ts</title>
<g id="a_node106"><a xlink:href="src/components/MindMap/core/models/HatContribution.ts" xlink:title="HatContribution.ts">
<path fill="#ddfeff" stroke="black" d="M1151.71,-4936.75C1151.71,-4936.75 1074.54,-4936.75 1074.54,-4936.75 1071.46,-4936.75 1068.38,-4933.66 1068.38,-4930.58 1068.38,-4930.58 1068.38,-4924.41 1068.38,-4924.41 1068.38,-4921.33 1071.46,-4918.25 1074.54,-4918.25 1074.54,-4918.25 1151.71,-4918.25 1151.71,-4918.25 1154.79,-4918.25 1157.88,-4921.33 1157.88,-4924.41 1157.88,-4924.41 1157.88,-4930.58 1157.88,-4930.58 1157.88,-4933.66 1154.79,-4936.75 1151.71,-4936.75"/>
<text text-anchor="start" x="1076.38" y="-4924.2" font-family="Helvetica,sans-Serif" font-size="9.00">HatContribution.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/core/models/HatContribution.ts&#45;&gt;node_modules/uuid -->
<g id="edge199" class="edge">
<title>src/components/MindMap/core/models/HatContribution.ts&#45;&gt;node_modules/uuid</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1068.02,-4924.34C1058.27,-4921.07 1049.98,-4915.34 1047,-4905.5 1044.11,-4895.94 1044.11,-2039.05 1047,-2029.5 1051.03,-2016.18 1064.78,-2010.39 1078.7,-2008.05"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.76,-2010.16 1084.44,-2007.31 1078.22,-2005.99 1078.76,-2010.16"/>
</g>
<!-- src/components/MindMap/core/models/HatContribution.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge198" class="edge">
<title>src/components/MindMap/core/models/HatContribution.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1158.36,-4927.5C1174.08,-4927.5 1191.56,-4927.5 1206.43,-4927.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1206.28,-4929.6 1212.28,-4927.5 1206.28,-4925.4 1206.28,-4929.6"/>
</g>
<!-- src/components/MindMap/core/operations/LayoutEngine.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts -->
<g id="edge201" class="edge">
<title>src/components/MindMap/core/operations/LayoutEngine.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1008.15,-4901.55C1026.7,-4900.64 1048.79,-4899.57 1067.82,-4898.65"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1067.7,-4900.76 1073.59,-4898.37 1067.49,-4896.56 1067.7,-4900.76"/>
</g>
<!-- src/components/MindMap/core/operations/LayoutEngine.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge202" class="edge">
<title>src/components/MindMap/core/operations/LayoutEngine.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1008.18,-4903.89C1020.77,-4905.97 1033.77,-4910.46 1043,-4919.5 1054.09,-4930.36 1038.83,-4943.86 1051,-4953.5 1073.08,-4970.98 1150.11,-4958.91 1177.75,-4953.5 1190.48,-4951 1203.88,-4945.96 1215.1,-4940.97"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1215.9,-4942.92 1220.47,-4938.49 1214.14,-4939.11 1215.9,-4942.92"/>
</g>
<!-- src/components/MindMap/core/operations/RelationshipManager.ts -->
<g id="node108" class="node">
<title>src/components/MindMap/core/operations/RelationshipManager.ts</title>
<g id="a_node108"><a xlink:href="src/components/MindMap/core/operations/RelationshipManager.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M1017.21,-4974.75C1017.21,-4974.75 916.79,-4974.75 916.79,-4974.75 913.71,-4974.75 910.62,-4971.66 910.62,-4968.58 910.62,-4968.58 910.62,-4962.41 910.62,-4962.41 910.62,-4959.33 913.71,-4956.25 916.79,-4956.25 916.79,-4956.25 1017.21,-4956.25 1017.21,-4956.25 1020.29,-4956.25 1023.38,-4959.33 1023.38,-4962.41 1023.38,-4962.41 1023.38,-4968.58 1023.38,-4968.58 1023.38,-4971.66 1020.29,-4974.75 1017.21,-4974.75"/>
<text text-anchor="start" x="918.62" y="-4962.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">RelationshipManager.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/core/rag/GraphRAG.ts -->
<g id="node109" class="node">
<title>src/components/MindMap/core/rag/GraphRAG.ts</title>
<g id="a_node109"><a xlink:href="src/components/MindMap/core/rag/GraphRAG.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M669.96,-4786.75C669.96,-4786.75 612.29,-4786.75 612.29,-4786.75 609.21,-4786.75 606.12,-4783.66 606.12,-4780.58 606.12,-4780.58 606.12,-4774.41 606.12,-4774.41 606.12,-4771.33 609.21,-4768.25 612.29,-4768.25 612.29,-4768.25 669.96,-4768.25 669.96,-4768.25 673.04,-4768.25 676.12,-4771.33 676.12,-4774.41 676.12,-4774.41 676.12,-4780.58 676.12,-4780.58 676.12,-4783.66 673.04,-4786.75 669.96,-4786.75"/>
<text text-anchor="start" x="614.12" y="-4774.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">GraphRAG.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/core/rag/HybridRAG.ts -->
<g id="node110" class="node">
<title>src/components/MindMap/core/rag/HybridRAG.ts</title>
<g id="a_node110"><a xlink:href="src/components/MindMap/core/rag/HybridRAG.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M670.71,-4817.75C670.71,-4817.75 611.54,-4817.75 611.54,-4817.75 608.46,-4817.75 605.38,-4814.66 605.38,-4811.58 605.38,-4811.58 605.38,-4805.41 605.38,-4805.41 605.38,-4802.33 608.46,-4799.25 611.54,-4799.25 611.54,-4799.25 670.71,-4799.25 670.71,-4799.25 673.79,-4799.25 676.88,-4802.33 676.88,-4805.41 676.88,-4805.41 676.88,-4811.58 676.88,-4811.58 676.88,-4814.66 673.79,-4817.75 670.71,-4817.75"/>
<text text-anchor="start" x="613.38" y="-4805.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">HybridRAG.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/core/rag/MemoryRAG.ts -->
<g id="node111" class="node">
<title>src/components/MindMap/core/rag/MemoryRAG.ts</title>
<g id="a_node111"><a xlink:href="src/components/MindMap/core/rag/MemoryRAG.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M673.71,-4848.75C673.71,-4848.75 608.54,-4848.75 608.54,-4848.75 605.46,-4848.75 602.38,-4845.66 602.38,-4842.58 602.38,-4842.58 602.38,-4836.41 602.38,-4836.41 602.38,-4833.33 605.46,-4830.25 608.54,-4830.25 608.54,-4830.25 673.71,-4830.25 673.71,-4830.25 676.79,-4830.25 679.88,-4833.33 679.88,-4836.41 679.88,-4836.41 679.88,-4842.58 679.88,-4842.58 679.88,-4845.66 676.79,-4848.75 673.71,-4848.75"/>
<text text-anchor="start" x="610.38" y="-4836.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">MemoryRAG.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/core/state/CommandManager.ts -->
<g id="node112" class="node">
<title>src/components/MindMap/core/state/CommandManager.ts</title>
<g id="a_node112"><a xlink:href="src/components/MindMap/core/state/CommandManager.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M846.71,-4844.75C846.71,-4844.75 755.29,-4844.75 755.29,-4844.75 752.21,-4844.75 749.12,-4841.66 749.12,-4838.58 749.12,-4838.58 749.12,-4832.41 749.12,-4832.41 749.12,-4829.33 752.21,-4826.25 755.29,-4826.25 755.29,-4826.25 846.71,-4826.25 846.71,-4826.25 849.79,-4826.25 852.88,-4829.33 852.88,-4832.41 852.88,-4832.41 852.88,-4838.58 852.88,-4838.58 852.88,-4841.66 849.79,-4844.75 846.71,-4844.75"/>
<text text-anchor="start" x="757.12" y="-4832.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">CommandManager.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/layouts/LayoutManager.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts -->
<g id="edge262" class="edge">
<title>src/components/MindMap/layouts/LayoutManager.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M835.97,-4299.13C852.88,-4305.73 872.01,-4316.29 883,-4332.5 901.32,-4359.51 869.92,-4380.58 891,-4405.5 936.34,-4459.1 1000,-4393 1043,-4448.5 1054.64,-4463.52 1045.87,-4772.19 1051,-4790.5 1060.67,-4824.99 1084.12,-4860.03 1099,-4879.93"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1097.27,-4881.13 1102.58,-4884.62 1100.61,-4878.57 1097.27,-4881.13"/>
</g>
<!-- src/components/MindMap/layouts/LayoutManager.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge263" class="edge">
<title>src/components/MindMap/layouts/LayoutManager.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M835.22,-4299.21C852.13,-4305.86 871.49,-4316.45 883,-4332.5 895.86,-4350.42 875.46,-4365.83 891,-4381.5 939.28,-4430.14 996.71,-4356.95 1043,-4407.5 1057.76,-4423.61 1036.26,-4489.36 1051,-4505.5 1089.56,-4547.71 1140.28,-4484.31 1177.75,-4527.5 1187.33,-4538.54 1182.72,-4776.19 1185.75,-4790.5 1195.32,-4835.65 1218.98,-4884.91 1232.24,-4910.27"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1230.21,-4910.93 1234.88,-4915.24 1233.92,-4908.95 1230.21,-4910.93"/>
</g>
<!-- src/components/MindMap/layouts/LayoutManager.ts&#45;&gt;src/components/MindMap/layouts/types.ts -->
<g id="edge269" class="edge">
<title>src/components/MindMap/layouts/LayoutManager.ts&#45;&gt;src/components/MindMap/layouts/types.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M807.61,-4279.91C819.8,-4260.17 850.87,-4215.38 891,-4198.5 920.43,-4186.11 1149.46,-4202.69 1177.75,-4217.5 1200.87,-4229.59 1220.02,-4253.48 1231.15,-4269.72"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1229.22,-4270.62 1234.28,-4274.47 1232.73,-4268.31 1229.22,-4270.62"/>
</g>
<!-- src/components/MindMap/layouts/strategies/BottomUpLayout.ts -->
<g id="node136" class="node">
<title>src/components/MindMap/layouts/strategies/BottomUpLayout.ts</title>
<g id="a_node136"><a xlink:href="src/components/MindMap/layouts/strategies/BottomUpLayout.ts" xlink:title="BottomUpLayout.ts">
<path fill="#ddfeff" stroke="black" d="M1007.46,-4351.75C1007.46,-4351.75 926.54,-4351.75 926.54,-4351.75 923.46,-4351.75 920.38,-4348.66 920.38,-4345.58 920.38,-4345.58 920.38,-4339.41 920.38,-4339.41 920.38,-4336.33 923.46,-4333.25 926.54,-4333.25 926.54,-4333.25 1007.46,-4333.25 1007.46,-4333.25 1010.54,-4333.25 1013.62,-4336.33 1013.62,-4339.41 1013.62,-4339.41 1013.62,-4345.58 1013.62,-4345.58 1013.62,-4348.66 1010.54,-4351.75 1007.46,-4351.75"/>
<text text-anchor="start" x="928.38" y="-4339.2" font-family="Helvetica,sans-Serif" font-size="9.00">BottomUpLayout.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/layouts/LayoutManager.ts&#45;&gt;src/components/MindMap/layouts/strategies/BottomUpLayout.ts -->
<g id="edge264" class="edge">
<title>src/components/MindMap/layouts/LayoutManager.ts&#45;&gt;src/components/MindMap/layouts/strategies/BottomUpLayout.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M827.03,-4299.12C843.14,-4305.45 864.46,-4314.1 883,-4322.5 886.62,-4324.14 887.25,-4325.18 891,-4326.5 897.45,-4328.75 904.33,-4330.75 911.2,-4332.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="910.61,-4334.52 916.94,-4333.87 911.59,-4330.43 910.61,-4334.52"/>
</g>
<!-- src/components/MindMap/layouts/strategies/CompactLeftToRightLayout.ts -->
<g id="node137" class="node">
<title>src/components/MindMap/layouts/strategies/CompactLeftToRightLayout.ts</title>
<g id="a_node137"><a xlink:href="src/components/MindMap/layouts/strategies/CompactLeftToRightLayout.ts" xlink:title="CompactLeftToRightLayout.ts">
<path fill="#ddfeff" stroke="black" d="M1028.83,-4258.75C1028.83,-4258.75 905.17,-4258.75 905.17,-4258.75 902.08,-4258.75 899,-4255.66 899,-4252.58 899,-4252.58 899,-4246.41 899,-4246.41 899,-4243.33 902.08,-4240.25 905.17,-4240.25 905.17,-4240.25 1028.83,-4240.25 1028.83,-4240.25 1031.92,-4240.25 1035,-4243.33 1035,-4246.41 1035,-4246.41 1035,-4252.58 1035,-4252.58 1035,-4255.66 1031.92,-4258.75 1028.83,-4258.75"/>
<text text-anchor="start" x="907" y="-4246.2" font-family="Helvetica,sans-Serif" font-size="9.00">CompactLeftToRightLayout.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/layouts/LayoutManager.ts&#45;&gt;src/components/MindMap/layouts/strategies/CompactLeftToRightLayout.ts -->
<g id="edge265" class="edge">
<title>src/components/MindMap/layouts/LayoutManager.ts&#45;&gt;src/components/MindMap/layouts/strategies/CompactLeftToRightLayout.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M836.12,-4279.84C852.64,-4275.28 872.8,-4269.89 891,-4265.5 897.14,-4264.01 903.58,-4262.53 910,-4261.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="910.34,-4263.18 915.75,-4259.84 909.44,-4259.07 910.34,-4263.18"/>
</g>
<!-- src/components/MindMap/layouts/strategies/LeftToRightLayout.ts -->
<g id="node138" class="node">
<title>src/components/MindMap/layouts/strategies/LeftToRightLayout.ts</title>
<g id="a_node138"><a xlink:href="src/components/MindMap/layouts/strategies/LeftToRightLayout.ts" xlink:title="LeftToRightLayout.ts">
<path fill="#ddfeff" stroke="black" d="M1010.46,-4227.75C1010.46,-4227.75 923.54,-4227.75 923.54,-4227.75 920.46,-4227.75 917.38,-4224.66 917.38,-4221.58 917.38,-4221.58 917.38,-4215.41 917.38,-4215.41 917.38,-4212.33 920.46,-4209.25 923.54,-4209.25 923.54,-4209.25 1010.46,-4209.25 1010.46,-4209.25 1013.54,-4209.25 1016.62,-4212.33 1016.62,-4215.41 1016.62,-4215.41 1016.62,-4221.58 1016.62,-4221.58 1016.62,-4224.66 1013.54,-4227.75 1010.46,-4227.75"/>
<text text-anchor="start" x="925.38" y="-4215.2" font-family="Helvetica,sans-Serif" font-size="9.00">LeftToRightLayout.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/layouts/LayoutManager.ts&#45;&gt;src/components/MindMap/layouts/strategies/LeftToRightLayout.ts -->
<g id="edge266" class="edge">
<title>src/components/MindMap/layouts/LayoutManager.ts&#45;&gt;src/components/MindMap/layouts/strategies/LeftToRightLayout.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M814.52,-4279.81C831.1,-4267.45 861.67,-4246.27 891,-4234.5 896.69,-4232.21 902.76,-4230.23 908.89,-4228.52"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="909.05,-4230.65 914.33,-4227.11 907.99,-4226.58 909.05,-4230.65"/>
</g>
<!-- src/components/MindMap/layouts/strategies/RadialLayout.ts -->
<g id="node139" class="node">
<title>src/components/MindMap/layouts/strategies/RadialLayout.ts</title>
<g id="a_node139"><a xlink:href="src/components/MindMap/layouts/strategies/RadialLayout.ts" xlink:title="RadialLayout.ts">
<path fill="#ddfeff" stroke="black" d="M1000.71,-4289.75C1000.71,-4289.75 933.29,-4289.75 933.29,-4289.75 930.21,-4289.75 927.12,-4286.66 927.12,-4283.58 927.12,-4283.58 927.12,-4277.41 927.12,-4277.41 927.12,-4274.33 930.21,-4271.25 933.29,-4271.25 933.29,-4271.25 1000.71,-4271.25 1000.71,-4271.25 1003.79,-4271.25 1006.88,-4274.33 1006.88,-4277.41 1006.88,-4277.41 1006.88,-4283.58 1006.88,-4283.58 1006.88,-4286.66 1003.79,-4289.75 1000.71,-4289.75"/>
<text text-anchor="start" x="935.12" y="-4277.2" font-family="Helvetica,sans-Serif" font-size="9.00">RadialLayout.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/layouts/LayoutManager.ts&#45;&gt;src/components/MindMap/layouts/strategies/RadialLayout.ts -->
<g id="edge267" class="edge">
<title>src/components/MindMap/layouts/LayoutManager.ts&#45;&gt;src/components/MindMap/layouts/strategies/RadialLayout.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M845.52,-4287.11C867.8,-4285.88 894.99,-4284.39 917.89,-4283.14"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="917.87,-4285.24 923.75,-4282.81 917.64,-4281.05 917.87,-4285.24"/>
</g>
<!-- src/components/MindMap/layouts/strategies/TopDownLayout.ts -->
<g id="node140" class="node">
<title>src/components/MindMap/layouts/strategies/TopDownLayout.ts</title>
<g id="a_node140"><a xlink:href="src/components/MindMap/layouts/strategies/TopDownLayout.ts" xlink:title="TopDownLayout.ts">
<path fill="#ddfeff" stroke="black" d="M1006.71,-4320.75C1006.71,-4320.75 927.29,-4320.75 927.29,-4320.75 924.21,-4320.75 921.12,-4317.66 921.12,-4314.58 921.12,-4314.58 921.12,-4308.41 921.12,-4308.41 921.12,-4305.33 924.21,-4302.25 927.29,-4302.25 927.29,-4302.25 1006.71,-4302.25 1006.71,-4302.25 1009.79,-4302.25 1012.88,-4305.33 1012.88,-4308.41 1012.88,-4308.41 1012.88,-4314.58 1012.88,-4314.58 1012.88,-4317.66 1009.79,-4320.75 1006.71,-4320.75"/>
<text text-anchor="start" x="929.12" y="-4308.2" font-family="Helvetica,sans-Serif" font-size="9.00">TopDownLayout.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/layouts/LayoutManager.ts&#45;&gt;src/components/MindMap/layouts/strategies/TopDownLayout.ts -->
<g id="edge268" class="edge">
<title>src/components/MindMap/layouts/LayoutManager.ts&#45;&gt;src/components/MindMap/layouts/strategies/TopDownLayout.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M845.52,-4295.33C865.95,-4298.07 890.51,-4301.37 912.11,-4304.27"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="911.62,-4306.32 917.85,-4305.04 912.18,-4302.16 911.62,-4306.32"/>
</g>
<!-- src/components/MindMap/core/state/store/connectionSlice.ts -->
<g id="node114" class="node">
<title>src/components/MindMap/core/state/store/connectionSlice.ts</title>
<g id="a_node114"><a xlink:href="src/components/MindMap/core/state/store/connectionSlice.ts" xlink:title="connectionSlice.ts">
<path fill="#ddfeff" stroke="black" d="M1005.96,-4712.75C1005.96,-4712.75 928.04,-4712.75 928.04,-4712.75 924.96,-4712.75 921.88,-4709.66 921.88,-4706.58 921.88,-4706.58 921.88,-4700.41 921.88,-4700.41 921.88,-4697.33 924.96,-4694.25 928.04,-4694.25 928.04,-4694.25 1005.96,-4694.25 1005.96,-4694.25 1009.04,-4694.25 1012.12,-4697.33 1012.12,-4700.41 1012.12,-4700.41 1012.12,-4706.58 1012.12,-4706.58 1012.12,-4709.66 1009.04,-4712.75 1005.96,-4712.75"/>
<text text-anchor="start" x="929.88" y="-4700.2" font-family="Helvetica,sans-Serif" font-size="9.00">connectionSlice.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/core/state/store/connectionSlice.ts&#45;&gt;node_modules/zustand -->
<g id="edge215" class="edge">
<title>src/components/MindMap/core/state/store/connectionSlice.ts&#45;&gt;node_modules/zustand</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M980.54,-4693.77C998.37,-4679.08 1030.28,-4649.32 1043,-4615.5 1048.78,-4600.13 1050.63,-4040.91 1051,-4024.5 1069.04,-3218.97 1106.15,-2227.2 1111.48,-2086.42"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1113.58,-2086.61 1111.7,-2080.54 1109.38,-2086.45 1113.58,-2086.61"/>
</g>
<!-- src/components/MindMap/core/state/store/types.ts -->
<g id="node115" class="node">
<title>src/components/MindMap/core/state/store/types.ts</title>
<g id="a_node115"><a xlink:href="src/components/MindMap/core/state/store/types.ts" xlink:title="types.ts">
<path fill="#ddfeff" stroke="black" d="M1133.96,-4726.75C1133.96,-4726.75 1092.29,-4726.75 1092.29,-4726.75 1089.21,-4726.75 1086.12,-4723.66 1086.12,-4720.58 1086.12,-4720.58 1086.12,-4714.41 1086.12,-4714.41 1086.12,-4711.33 1089.21,-4708.25 1092.29,-4708.25 1092.29,-4708.25 1133.96,-4708.25 1133.96,-4708.25 1137.04,-4708.25 1140.12,-4711.33 1140.12,-4714.41 1140.12,-4714.41 1140.12,-4720.58 1140.12,-4720.58 1140.12,-4723.66 1137.04,-4726.75 1133.96,-4726.75"/>
<text text-anchor="start" x="1097.75" y="-4714.2" font-family="Helvetica,sans-Serif" font-size="9.00">types.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/core/state/store/connectionSlice.ts&#45;&gt;src/components/MindMap/core/state/store/types.ts -->
<g id="edge214" class="edge">
<title>src/components/MindMap/core/state/store/connectionSlice.ts&#45;&gt;src/components/MindMap/core/state/store/types.ts</title>
<g id="a_edge214"><a xlink:title="no&#45;circular">
<path fill="none" stroke="orange" stroke-width="2" d="M1012.42,-4703.62C1029.85,-4704.9 1049.82,-4706.84 1067.28,-4708.94"/>
<polygon fill="orange" stroke="orange" stroke-width="2" points="1076.45,-4712.26 1082.67,-4710.96 1077,-4708.09 1076.45,-4712.26"/>
<polyline fill="none" stroke="orange" stroke-width="2" points="1075.73,-4710.05 1072.76,-4709.66"/>
<ellipse fill="none" stroke="orange" stroke-width="2" cx="1069.39" cy="-4709.21" rx="2.4" ry="2.4"/>
</a>
</g>
<text text-anchor="middle" x="1070.77" y="-4698.35" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">no&#45;circular</text>
</g>
<!-- src/components/MindMap/core/state/store/types.ts&#45;&gt;src/components/MindMap/core/state/store/connectionSlice.ts -->
<g id="edge226" class="edge">
<title>src/components/MindMap/core/state/store/types.ts&#45;&gt;src/components/MindMap/core/state/store/connectionSlice.ts</title>
<g id="a_edge226"><a xlink:title="no&#45;circular">
<path fill="none" stroke="orange" stroke-width="2" d="M1085.67,-4718.5C1070.32,-4717.74 1050.28,-4716.09 1031.11,-4714.09"/>
<polygon fill="orange" stroke="orange" stroke-width="2" points="1021.62,-4710.92 1015.43,-4712.34 1021.16,-4715.09 1021.62,-4710.92"/>
<polyline fill="none" stroke="orange" stroke-width="2" points="1022.38,-4713.12 1025.36,-4713.45"/>
<ellipse fill="none" stroke="orange" stroke-width="2" cx="1028.74" cy="-4713.83" rx="2.4" ry="2.4"/>
</a>
</g>
<text text-anchor="middle" x="1026.97" y="-4717.76" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">no&#45;circular</text>
</g>
<!-- src/components/MindMap/core/state/store/nodeSlice.ts -->
<g id="node117" class="node">
<title>src/components/MindMap/core/state/store/nodeSlice.ts</title>
<g id="a_node117"><a xlink:href="src/components/MindMap/core/state/store/nodeSlice.ts" xlink:title="nodeSlice.ts">
<path fill="#ddfeff" stroke="black" d="M1269.58,-4698.75C1269.58,-4698.75 1215.67,-4698.75 1215.67,-4698.75 1212.58,-4698.75 1209.5,-4695.66 1209.5,-4692.58 1209.5,-4692.58 1209.5,-4686.41 1209.5,-4686.41 1209.5,-4683.33 1212.58,-4680.25 1215.67,-4680.25 1215.67,-4680.25 1269.58,-4680.25 1269.58,-4680.25 1272.67,-4680.25 1275.75,-4683.33 1275.75,-4686.41 1275.75,-4686.41 1275.75,-4692.58 1275.75,-4692.58 1275.75,-4695.66 1272.67,-4698.75 1269.58,-4698.75"/>
<text text-anchor="start" x="1217.5" y="-4686.2" font-family="Helvetica,sans-Serif" font-size="9.00">nodeSlice.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/core/state/store/types.ts&#45;&gt;src/components/MindMap/core/state/store/nodeSlice.ts -->
<g id="edge227" class="edge">
<title>src/components/MindMap/core/state/store/types.ts&#45;&gt;src/components/MindMap/core/state/store/nodeSlice.ts</title>
<g id="a_edge227"><a xlink:title="no&#45;circular">
<path fill="none" stroke="orange" stroke-width="2" d="M1140.54,-4707.96C1155.14,-4704.07 1173.77,-4699.75 1191.05,-4696.19"/>
<polygon fill="orange" stroke="orange" stroke-width="2" points="1200.9,-4696.4 1206.39,-4693.19 1200.1,-4692.28 1200.9,-4696.4"/>
<polyline fill="none" stroke="orange" stroke-width="2" points="1199.52,-4694.53 1196.58,-4695.11"/>
<ellipse fill="none" stroke="orange" stroke-width="2" cx="1193.24" cy="-4695.76" rx="2.4" ry="2.4"/>
</a>
</g>
<text text-anchor="middle" x="1153.28" y="-4691.07" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">no&#45;circular</text>
</g>
<!-- src/components/MindMap/core/state/store/projectSlice.ts -->
<g id="node118" class="node">
<title>src/components/MindMap/core/state/store/projectSlice.ts</title>
<g id="a_node118"><a xlink:href="src/components/MindMap/core/state/store/projectSlice.ts" xlink:title="projectSlice.ts">
<path fill="#ddfeff" stroke="black" d="M1272.96,-4729.75C1272.96,-4729.75 1212.29,-4729.75 1212.29,-4729.75 1209.21,-4729.75 1206.12,-4726.66 1206.12,-4723.58 1206.12,-4723.58 1206.12,-4717.41 1206.12,-4717.41 1206.12,-4714.33 1209.21,-4711.25 1212.29,-4711.25 1212.29,-4711.25 1272.96,-4711.25 1272.96,-4711.25 1276.04,-4711.25 1279.12,-4714.33 1279.12,-4717.41 1279.12,-4717.41 1279.12,-4723.58 1279.12,-4723.58 1279.12,-4726.66 1276.04,-4729.75 1272.96,-4729.75"/>
<text text-anchor="start" x="1214.12" y="-4717.2" font-family="Helvetica,sans-Serif" font-size="9.00">projectSlice.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/core/state/store/types.ts&#45;&gt;src/components/MindMap/core/state/store/projectSlice.ts -->
<g id="edge228" class="edge">
<title>src/components/MindMap/core/state/store/types.ts&#45;&gt;src/components/MindMap/core/state/store/projectSlice.ts</title>
<g id="a_edge228"><a xlink:title="no&#45;circular">
<path fill="none" stroke="orange" stroke-width="2" d="M1140.54,-4714.39C1154.1,-4714.07 1171.13,-4714.23 1187.33,-4714.76"/>
<polygon fill="orange" stroke="orange" stroke-width="2" points="1196.94,-4717.28 1203.03,-4715.44 1197.12,-4713.08 1196.94,-4717.28"/>
<polyline fill="none" stroke="orange" stroke-width="2" points="1196.03,-4715.14 1193.04,-4715.01"/>
<ellipse fill="none" stroke="orange" stroke-width="2" cx="1189.64" cy="-4714.86" rx="2.4" ry="2.4"/>
</a>
</g>
<text text-anchor="middle" x="1194.93" y="-4705.85" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">no&#45;circular</text>
</g>
<!-- src/components/MindMap/core/state/store/undoRedoSlice.ts -->
<g id="node119" class="node">
<title>src/components/MindMap/core/state/store/undoRedoSlice.ts</title>
<g id="a_node119"><a xlink:href="src/components/MindMap/core/state/store/undoRedoSlice.ts" xlink:title="undoRedoSlice.ts">
<path fill="#ddfeff" stroke="black" d="M1280.83,-4760.75C1280.83,-4760.75 1204.42,-4760.75 1204.42,-4760.75 1201.33,-4760.75 1198.25,-4757.66 1198.25,-4754.58 1198.25,-4754.58 1198.25,-4748.41 1198.25,-4748.41 1198.25,-4745.33 1201.33,-4742.25 1204.42,-4742.25 1204.42,-4742.25 1280.83,-4742.25 1280.83,-4742.25 1283.92,-4742.25 1287,-4745.33 1287,-4748.41 1287,-4748.41 1287,-4754.58 1287,-4754.58 1287,-4757.66 1283.92,-4760.75 1280.83,-4760.75"/>
<text text-anchor="start" x="1206.25" y="-4748.2" font-family="Helvetica,sans-Serif" font-size="9.00">undoRedoSlice.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/core/state/store/types.ts&#45;&gt;src/components/MindMap/core/state/store/undoRedoSlice.ts -->
<g id="edge229" class="edge">
<title>src/components/MindMap/core/state/store/types.ts&#45;&gt;src/components/MindMap/core/state/store/undoRedoSlice.ts</title>
<g id="a_edge229"><a xlink:title="no&#45;circular">
<path fill="none" stroke="orange" stroke-width="2" d="M1140.54,-4720.81C1157.92,-4724.63 1181.01,-4730.62 1200.74,-4736.36"/>
<polygon fill="orange" stroke="orange" stroke-width="2" points="1209.41,-4741.2 1215.76,-4740.94 1210.64,-4737.18 1209.41,-4741.2"/>
<polyline fill="none" stroke="orange" stroke-width="2" points="1209.07,-4738.9 1206.2,-4738.02"/>
<ellipse fill="none" stroke="orange" stroke-width="2" cx="1202.95" cy="-4737.03" rx="2.4" ry="2.4"/>
</a>
</g>
<text text-anchor="middle" x="1201.81" y="-4732.53" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">no&#45;circular</text>
</g>
<!-- src/components/MindMap/core/state/store/index.ts -->
<g id="node116" class="node">
<title>src/components/MindMap/core/state/store/index.ts</title>
<g id="a_node116"><a xlink:href="src/components/MindMap/core/state/store/index.ts" xlink:title="index.ts">
<path fill="#ddfeff" stroke="black" d="M821.83,-4723.75C821.83,-4723.75 780.17,-4723.75 780.17,-4723.75 777.08,-4723.75 774,-4720.66 774,-4717.58 774,-4717.58 774,-4711.41 774,-4711.41 774,-4708.33 777.08,-4705.25 780.17,-4705.25 780.17,-4705.25 821.83,-4705.25 821.83,-4705.25 824.92,-4705.25 828,-4708.33 828,-4711.41 828,-4711.41 828,-4717.58 828,-4717.58 828,-4720.66 824.92,-4723.75 821.83,-4723.75"/>
<text text-anchor="start" x="785.25" y="-4711.2" font-family="Helvetica,sans-Serif" font-size="9.00">index.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/core/state/store/index.ts&#45;&gt;node_modules/zustand -->
<g id="edge221" class="edge">
<title>src/components/MindMap/core/state/store/index.ts&#45;&gt;node_modules/zustand</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M814.03,-4704.8C832.99,-4688.64 869.11,-4654.16 883,-4615.5 891.92,-4590.67 886.62,-2742.5 891,-2716.5 935.01,-2455.07 1070.52,-2157.37 1104.39,-2085.64"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1106.19,-2086.75 1106.86,-2080.43 1102.39,-2084.95 1106.19,-2086.75"/>
</g>
<!-- src/components/MindMap/core/state/store/index.ts&#45;&gt;src/components/MindMap/core/state/store/connectionSlice.ts -->
<g id="edge216" class="edge">
<title>src/components/MindMap/core/state/store/index.ts&#45;&gt;src/components/MindMap/core/state/store/connectionSlice.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M828.35,-4712.73C850.98,-4711.21 884.32,-4708.97 912.52,-4707.08"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="912.61,-4709.18 918.46,-4706.68 912.33,-4704.99 912.61,-4709.18"/>
</g>
<!-- src/components/MindMap/core/state/store/index.ts&#45;&gt;src/components/MindMap/core/state/store/types.ts -->
<g id="edge219" class="edge">
<title>src/components/MindMap/core/state/store/index.ts&#45;&gt;src/components/MindMap/core/state/store/types.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M828.47,-4716C846.17,-4716.92 869.97,-4718.02 891,-4718.5 958.54,-4720.01 975.45,-4718.98 1043,-4718.5 1054.06,-4718.42 1066.12,-4718.26 1077.03,-4718.1"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1076.87,-4720.2 1082.84,-4718.01 1076.81,-4716 1076.87,-4720.2"/>
</g>
<!-- src/components/MindMap/core/state/store/index.ts&#45;&gt;src/components/MindMap/core/state/store/nodeSlice.ts -->
<g id="edge217" class="edge">
<title>src/components/MindMap/core/state/store/index.ts&#45;&gt;src/components/MindMap/core/state/store/nodeSlice.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M828.05,-4704.76C845.56,-4698.74 869.33,-4691.6 891,-4688.5 1001.79,-4672.64 1134.29,-4680.08 1200.17,-4685.52"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1199.85,-4687.6 1206,-4686.02 1200.2,-4683.42 1199.85,-4687.6"/>
</g>
<!-- src/components/MindMap/core/state/store/index.ts&#45;&gt;src/components/MindMap/core/state/store/projectSlice.ts -->
<g id="edge218" class="edge">
<title>src/components/MindMap/core/state/store/index.ts&#45;&gt;src/components/MindMap/core/state/store/projectSlice.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M828.47,-4717.06C846.16,-4718.73 869.96,-4720.89 891,-4722.5 1018.15,-4732.18 1050.78,-4744.32 1177.75,-4732.5 1184.01,-4731.91 1190.56,-4731.02 1196.99,-4729.97"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1197.11,-4732.08 1202.66,-4728.98 1196.39,-4727.94 1197.11,-4732.08"/>
</g>
<!-- src/components/MindMap/core/state/store/index.ts&#45;&gt;src/components/MindMap/core/state/store/undoRedoSlice.ts -->
<g id="edge220" class="edge">
<title>src/components/MindMap/core/state/store/index.ts&#45;&gt;src/components/MindMap/core/state/store/undoRedoSlice.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M828.42,-4715.52C844.7,-4716.92 865.69,-4720.15 883,-4727.5 887.09,-4729.23 886.82,-4731.98 891,-4733.5 944.22,-4752.77 1104.78,-4753.55 1188.91,-4752.53"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1188.79,-4754.63 1194.76,-4752.45 1188.74,-4750.43 1188.79,-4754.63"/>
</g>
<!-- src/components/MindMap/core/state/store/nodeSlice.ts&#45;&gt;node_modules/zustand -->
<g id="edge223" class="edge">
<title>src/components/MindMap/core/state/store/nodeSlice.ts&#45;&gt;node_modules/zustand</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1238.91,-4679.76C1229.3,-4644.79 1196.91,-4522.14 1185.75,-4418.5 1177.23,-4339.39 1179.74,-3782.03 1177.75,-3702.5 1161.02,-3035.01 1121.28,-2214.75 1114.97,-2086.65"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1117.08,-2086.63 1114.68,-2080.74 1112.88,-2086.84 1117.08,-2086.63"/>
</g>
<!-- src/components/MindMap/core/state/store/nodeSlice.ts&#45;&gt;src/components/MindMap/core/state/store/types.ts -->
<g id="edge222" class="edge">
<title>src/components/MindMap/core/state/store/nodeSlice.ts&#45;&gt;src/components/MindMap/core/state/store/types.ts</title>
<g id="a_edge222"><a xlink:title="no&#45;circular">
<path fill="none" stroke="orange" stroke-width="2" d="M1214.8,-4699.13C1198.59,-4703.44 1177.51,-4708.26 1158.83,-4711.99"/>
<polygon fill="orange" stroke="orange" stroke-width="2" points="1149.03,-4711.69 1143.52,-4714.86 1149.8,-4715.82 1149.03,-4711.69"/>
<polyline fill="none" stroke="orange" stroke-width="2" points="1150.4,-4713.57 1153.35,-4713.02"/>
<ellipse fill="none" stroke="orange" stroke-width="2" cx="1156.69" cy="-4712.4" rx="2.4" ry="2.4"/>
</a>
</g>
<text text-anchor="middle" x="1155.99" y="-4709.98" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">no&#45;circular</text>
</g>
<!-- src/components/MindMap/core/state/store/projectSlice.ts&#45;&gt;node_modules/zustand -->
<g id="edge225" class="edge">
<title>src/components/MindMap/core/state/store/projectSlice.ts&#45;&gt;node_modules/zustand</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1205.64,-4717.32C1197.92,-4714.89 1190.63,-4710.92 1185.75,-4704.5 1177.33,-4693.42 1178.08,-3716.41 1177.75,-3702.5 1162.09,-3034.99 1121.45,-2214.74 1115,-2086.65"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1117.1,-2086.63 1114.7,-2080.74 1112.9,-2086.84 1117.1,-2086.63"/>
</g>
<!-- src/components/MindMap/core/state/store/projectSlice.ts&#45;&gt;src/components/MindMap/core/state/store/types.ts -->
<g id="edge224" class="edge">
<title>src/components/MindMap/core/state/store/projectSlice.ts&#45;&gt;src/components/MindMap/core/state/store/types.ts</title>
<g id="a_edge224"><a xlink:title="no&#45;circular">
<path fill="none" stroke="orange" stroke-width="2" d="M1206.05,-4723.74C1191.53,-4723.86 1174.56,-4723.54 1159.18,-4722.88"/>
<polygon fill="orange" stroke="orange" stroke-width="2" points="1149.67,-4720.25 1143.57,-4722.01 1149.44,-4724.44 1149.67,-4720.25"/>
<polyline fill="none" stroke="orange" stroke-width="2" points="1150.55,-4722.4 1153.55,-4722.57"/>
<ellipse fill="none" stroke="orange" stroke-width="2" cx="1156.94" cy="-4722.75" rx="2.4" ry="2.4"/>
</a>
</g>
<text text-anchor="middle" x="1151.44" y="-4725.32" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">no&#45;circular</text>
</g>
<!-- src/components/MindMap/core/state/store/undoRedoSlice.ts&#45;&gt;node_modules/zustand -->
<g id="edge231" class="edge">
<title>src/components/MindMap/core/state/store/undoRedoSlice.ts&#45;&gt;node_modules/zustand</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1197.78,-4745.17C1193.12,-4742.81 1188.92,-4739.67 1185.75,-4735.5 1177.07,-4724.07 1178.08,-3716.84 1177.75,-3702.5 1162.17,-3034.99 1121.47,-2214.74 1115,-2086.65"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1117.1,-2086.62 1114.7,-2080.74 1112.9,-2086.84 1117.1,-2086.62"/>
</g>
<!-- src/components/MindMap/core/state/store/undoRedoSlice.ts&#45;&gt;src/components/MindMap/core/state/store/types.ts -->
<g id="edge230" class="edge">
<title>src/components/MindMap/core/state/store/undoRedoSlice.ts&#45;&gt;src/components/MindMap/core/state/store/types.ts</title>
<g id="a_edge230"><a xlink:title="no&#45;circular">
<path fill="none" stroke="orange" stroke-width="2" d="M1197.82,-4744.11C1183.98,-4740.71 1168.75,-4736.63 1155.11,-4732.66"/>
<polygon fill="orange" stroke="orange" stroke-width="2" points="1146.46,-4727.83 1140.11,-4728.09 1145.24,-4731.85 1146.46,-4727.83"/>
<polyline fill="none" stroke="orange" stroke-width="2" points="1146.81,-4730.13 1149.68,-4731.01"/>
<ellipse fill="none" stroke="orange" stroke-width="2" cx="1152.93" cy="-4732" rx="2.4" ry="2.4"/>
</a>
</g>
<text text-anchor="middle" x="1145.85" y="-4738.15" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">no&#45;circular</text>
</g>
<!-- src/components/MindMap/events/EventBus.ts -->
<g id="node120" class="node">
<title>src/components/MindMap/events/EventBus.ts</title>
<g id="a_node120"><a xlink:href="src/components/MindMap/events/EventBus.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M297.21,-3523.75C297.21,-3523.75 245.54,-3523.75 245.54,-3523.75 242.46,-3523.75 239.38,-3520.66 239.38,-3517.58 239.38,-3517.58 239.38,-3511.41 239.38,-3511.41 239.38,-3508.33 242.46,-3505.25 245.54,-3505.25 245.54,-3505.25 297.21,-3505.25 297.21,-3505.25 300.29,-3505.25 303.38,-3508.33 303.38,-3511.41 303.38,-3511.41 303.38,-3517.58 303.38,-3517.58 303.38,-3520.66 300.29,-3523.75 297.21,-3523.75"/>
<text text-anchor="start" x="247.38" y="-3511.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">EventBus.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/events/EventTypes.ts -->
<g id="node121" class="node">
<title>src/components/MindMap/events/EventTypes.ts</title>
<g id="a_node121"><a xlink:href="src/components/MindMap/events/EventTypes.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M301.71,-3554.75C301.71,-3554.75 241.04,-3554.75 241.04,-3554.75 237.96,-3554.75 234.88,-3551.66 234.88,-3548.58 234.88,-3548.58 234.88,-3542.41 234.88,-3542.41 234.88,-3539.33 237.96,-3536.25 241.04,-3536.25 241.04,-3536.25 301.71,-3536.25 301.71,-3536.25 304.79,-3536.25 307.88,-3539.33 307.88,-3542.41 307.88,-3542.41 307.88,-3548.58 307.88,-3548.58 307.88,-3551.66 304.79,-3554.75 301.71,-3554.75"/>
<text text-anchor="start" x="242.88" y="-3542.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">EventTypes.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/events/handlers/LLMEventHandlers.ts -->
<g id="node122" class="node">
<title>src/components/MindMap/events/handlers/LLMEventHandlers.ts</title>
<g id="a_node122"><a xlink:href="src/components/MindMap/events/handlers/LLMEventHandlers.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M317.08,-3408.75C317.08,-3408.75 225.67,-3408.75 225.67,-3408.75 222.58,-3408.75 219.5,-3405.66 219.5,-3402.58 219.5,-3402.58 219.5,-3396.41 219.5,-3396.41 219.5,-3393.33 222.58,-3390.25 225.67,-3390.25 225.67,-3390.25 317.08,-3390.25 317.08,-3390.25 320.17,-3390.25 323.25,-3393.33 323.25,-3396.41 323.25,-3396.41 323.25,-3402.58 323.25,-3402.58 323.25,-3405.66 320.17,-3408.75 317.08,-3408.75"/>
<text text-anchor="start" x="227.5" y="-3396.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">LLMEventHandlers.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/events/handlers/NodeEventHandlers.ts -->
<g id="node123" class="node">
<title>src/components/MindMap/events/handlers/NodeEventHandlers.ts</title>
<g id="a_node123"><a xlink:href="src/components/MindMap/events/handlers/NodeEventHandlers.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M319.33,-3439.75C319.33,-3439.75 223.42,-3439.75 223.42,-3439.75 220.33,-3439.75 217.25,-3436.66 217.25,-3433.58 217.25,-3433.58 217.25,-3427.41 217.25,-3427.41 217.25,-3424.33 220.33,-3421.25 223.42,-3421.25 223.42,-3421.25 319.33,-3421.25 319.33,-3421.25 322.42,-3421.25 325.5,-3424.33 325.5,-3427.41 325.5,-3427.41 325.5,-3433.58 325.5,-3433.58 325.5,-3436.66 322.42,-3439.75 319.33,-3439.75"/>
<text text-anchor="start" x="225.25" y="-3427.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">NodeEventHandlers.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/events/handlers/UIEventHandlers.ts -->
<g id="node124" class="node">
<title>src/components/MindMap/events/handlers/UIEventHandlers.ts</title>
<g id="a_node124"><a xlink:href="src/components/MindMap/events/handlers/UIEventHandlers.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M312.58,-3470.75C312.58,-3470.75 230.17,-3470.75 230.17,-3470.75 227.08,-3470.75 224,-3467.66 224,-3464.58 224,-3464.58 224,-3458.41 224,-3458.41 224,-3455.33 227.08,-3452.25 230.17,-3452.25 230.17,-3452.25 312.58,-3452.25 312.58,-3452.25 315.67,-3452.25 318.75,-3455.33 318.75,-3458.41 318.75,-3458.41 318.75,-3464.58 318.75,-3464.58 318.75,-3467.66 315.67,-3470.75 312.58,-3470.75"/>
<text text-anchor="start" x="232" y="-3458.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">UIEventHandlers.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/hooks/useAutosave.ts -->
<g id="node125" class="node">
<title>src/components/MindMap/hooks/useAutosave.ts</title>
<g id="a_node125"><a xlink:href="src/components/MindMap/hooks/useAutosave.ts" xlink:title="useAutosave.ts">
<path fill="#ddfeff" stroke="black" d="M674.08,-3392.75C674.08,-3392.75 608.17,-3392.75 608.17,-3392.75 605.08,-3392.75 602,-3389.66 602,-3386.58 602,-3386.58 602,-3380.41 602,-3380.41 602,-3377.33 605.08,-3374.25 608.17,-3374.25 608.17,-3374.25 674.08,-3374.25 674.08,-3374.25 677.17,-3374.25 680.25,-3377.33 680.25,-3380.41 680.25,-3380.41 680.25,-3386.58 680.25,-3386.58 680.25,-3389.66 677.17,-3392.75 674.08,-3392.75"/>
<text text-anchor="start" x="610" y="-3380.2" font-family="Helvetica,sans-Serif" font-size="9.00">useAutosave.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/hooks/useAutosave.ts&#45;&gt;node_modules/react -->
<g id="edge233" class="edge">
<title>src/components/MindMap/hooks/useAutosave.ts&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M680.73,-3384C693.25,-3382.07 705.98,-3377.45 714,-3367.5 736.03,-3340.17 706.2,-868.76 727,-840.5 772.32,-778.91 815.43,-800.19 891,-788.5 957.76,-778.16 995.06,-740.9 1043,-788.5 1064.92,-810.26 1031.66,-1876.41 1051,-1900.5 1057.58,-1908.69 1067.89,-1912.7 1078.19,-1914.51"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1077.91,-1916.59 1084.12,-1915.27 1078.44,-1912.42 1077.91,-1916.59"/>
</g>
<!-- src/components/MindMap/hooks/useAutosave.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge232" class="edge">
<title>src/components/MindMap/hooks/useAutosave.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M680.57,-3383.12C693.08,-3385.07 705.83,-3389.66 714,-3399.5 740.57,-3431.48 698.03,-3743.67 727,-3773.5 751.2,-3798.42 859.13,-3758.25 883,-3783.5 902.22,-3803.82 877.12,-4766.21 891,-4790.5 899.2,-4804.85 913.99,-4815.13 928.27,-4822.21"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="927.04,-4823.95 933.37,-4824.55 928.79,-4820.13 927.04,-4823.95"/>
</g>
<!-- src/components/MindMap/hooks/useCanvas.ts -->
<g id="node126" class="node">
<title>src/components/MindMap/hooks/useCanvas.ts</title>
<g id="a_node126"><a xlink:href="src/components/MindMap/hooks/useCanvas.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M670.71,-3454.75C670.71,-3454.75 611.54,-3454.75 611.54,-3454.75 608.46,-3454.75 605.38,-3451.66 605.38,-3448.58 605.38,-3448.58 605.38,-3442.41 605.38,-3442.41 605.38,-3439.33 608.46,-3436.25 611.54,-3436.25 611.54,-3436.25 670.71,-3436.25 670.71,-3436.25 673.79,-3436.25 676.88,-3439.33 676.88,-3442.41 676.88,-3442.41 676.88,-3448.58 676.88,-3448.58 676.88,-3451.66 673.79,-3454.75 670.71,-3454.75"/>
<text text-anchor="start" x="613.38" y="-3442.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">useCanvas.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/hooks/useGovernance.ts -->
<g id="node127" class="node">
<title>src/components/MindMap/hooks/useGovernance.ts</title>
<g id="a_node127"><a xlink:href="src/components/MindMap/hooks/useGovernance.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M680.08,-3485.75C680.08,-3485.75 602.17,-3485.75 602.17,-3485.75 599.08,-3485.75 596,-3482.66 596,-3479.58 596,-3479.58 596,-3473.41 596,-3473.41 596,-3470.33 599.08,-3467.25 602.17,-3467.25 602.17,-3467.25 680.08,-3467.25 680.08,-3467.25 683.17,-3467.25 686.25,-3470.33 686.25,-3473.41 686.25,-3473.41 686.25,-3479.58 686.25,-3479.58 686.25,-3482.66 683.17,-3485.75 680.08,-3485.75"/>
<text text-anchor="start" x="604" y="-3473.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">useGovernance.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/hooks/useHatContributions.ts -->
<g id="node128" class="node">
<title>src/components/MindMap/hooks/useHatContributions.ts</title>
<g id="a_node128"><a xlink:href="src/components/MindMap/hooks/useHatContributions.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M689.46,-3516.75C689.46,-3516.75 592.79,-3516.75 592.79,-3516.75 589.71,-3516.75 586.62,-3513.66 586.62,-3510.58 586.62,-3510.58 586.62,-3504.41 586.62,-3504.41 586.62,-3501.33 589.71,-3498.25 592.79,-3498.25 592.79,-3498.25 689.46,-3498.25 689.46,-3498.25 692.54,-3498.25 695.62,-3501.33 695.62,-3504.41 695.62,-3504.41 695.62,-3510.58 695.62,-3510.58 695.62,-3513.66 692.54,-3516.75 689.46,-3516.75"/>
<text text-anchor="start" x="594.62" y="-3504.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">useHatContributions.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/hooks/useKeyboardShortcuts.ts -->
<g id="node129" class="node">
<title>src/components/MindMap/hooks/useKeyboardShortcuts.ts</title>
<g id="a_node129"><a xlink:href="src/components/MindMap/hooks/useKeyboardShortcuts.ts" xlink:title="useKeyboardShortcuts.ts">
<path fill="#ddfeff" stroke="black" d="M693.96,-3361.75C693.96,-3361.75 588.29,-3361.75 588.29,-3361.75 585.21,-3361.75 582.12,-3358.66 582.12,-3355.58 582.12,-3355.58 582.12,-3349.41 582.12,-3349.41 582.12,-3346.33 585.21,-3343.25 588.29,-3343.25 588.29,-3343.25 693.96,-3343.25 693.96,-3343.25 697.04,-3343.25 700.12,-3346.33 700.12,-3349.41 700.12,-3349.41 700.12,-3355.58 700.12,-3355.58 700.12,-3358.66 697.04,-3361.75 693.96,-3361.75"/>
<text text-anchor="start" x="590.12" y="-3349.2" font-family="Helvetica,sans-Serif" font-size="9.00">useKeyboardShortcuts.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/hooks/useKeyboardShortcuts.ts&#45;&gt;node_modules/react -->
<g id="edge237" class="edge">
<title>src/components/MindMap/hooks/useKeyboardShortcuts.ts&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M700.3,-3349.62C705.53,-3347.24 710.25,-3343.96 714,-3339.5 736.78,-3312.36 703.92,-815.38 727,-788.5 775.07,-732.5 817.43,-768.29 891,-762.5 958.35,-757.19 995.06,-714.89 1043,-762.5 1065.43,-784.77 1031.21,-1875.84 1051,-1900.5 1057.7,-1908.84 1068.27,-1912.84 1078.75,-1914.6"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.22,-1916.66 1084.43,-1915.31 1078.74,-1912.49 1078.22,-1916.66"/>
</g>
<!-- src/components/MindMap/hooks/useKeyboardShortcuts.ts&#45;&gt;src/components/MindMap/context/MindMapContext.tsx -->
<g id="edge234" class="edge">
<title>src/components/MindMap/hooks/useKeyboardShortcuts.ts&#45;&gt;src/components/MindMap/context/MindMapContext.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M700.35,-3359.95C705.3,-3362.17 709.95,-3364.97 714,-3368.5 726.51,-3379.4 715.63,-3391.41 727,-3403.5 778.95,-3458.71 828.63,-3419.66 883,-3472.5 929.29,-3517.48 952.94,-3593.44 961.79,-3628.18"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="959.72,-3628.55 963.18,-3633.88 963.8,-3627.55 959.72,-3628.55"/>
</g>
<!-- src/components/MindMap/hooks/useKeyboardShortcuts.ts&#45;&gt;src/components/MindMap/hooks/useNodeManagement.ts -->
<g id="edge235" class="edge">
<title>src/components/MindMap/hooks/useKeyboardShortcuts.ts&#45;&gt;src/components/MindMap/hooks/useNodeManagement.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M700.35,-3358.4C705.5,-3360.9 710.19,-3364.19 714,-3368.5 733.82,-3390.9 717.99,-3473.97 727,-3502.5 740.71,-3545.93 770.36,-3590.97 787.35,-3614.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="785.57,-3615.75 790.8,-3619.36 788.96,-3613.27 785.57,-3615.75"/>
</g>
<!-- src/components/MindMap/hooks/useKeyboardShortcuts.ts&#45;&gt;src/components/MindMap/hooks/useProjectManagement.ts -->
<g id="edge236" class="edge">
<title>src/components/MindMap/hooks/useKeyboardShortcuts.ts&#45;&gt;src/components/MindMap/hooks/useProjectManagement.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M700.6,-3359.66C705.51,-3361.94 710.08,-3364.83 714,-3368.5 728.9,-3382.41 716.7,-3394.9 727,-3412.5 741.06,-3436.51 764.41,-3458.63 780.92,-3472.53"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="779.22,-3473.85 785.19,-3476.04 781.89,-3470.61 779.22,-3473.85"/>
</g>
<!-- src/components/MindMap/hooks/useLLMIntegration.ts -->
<g id="node130" class="node">
<title>src/components/MindMap/hooks/useLLMIntegration.ts</title>
<g id="a_node130"><a xlink:href="src/components/MindMap/hooks/useLLMIntegration.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M685.71,-3578.75C685.71,-3578.75 596.54,-3578.75 596.54,-3578.75 593.46,-3578.75 590.38,-3575.66 590.38,-3572.58 590.38,-3572.58 590.38,-3566.41 590.38,-3566.41 590.38,-3563.33 593.46,-3560.25 596.54,-3560.25 596.54,-3560.25 685.71,-3560.25 685.71,-3560.25 688.79,-3560.25 691.88,-3563.33 691.88,-3566.41 691.88,-3566.41 691.88,-3572.58 691.88,-3572.58 691.88,-3575.66 688.79,-3578.75 685.71,-3578.75"/>
<text text-anchor="start" x="598.38" y="-3566.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">useLLMIntegration.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/hooks/useNodeInteraction.ts -->
<g id="node131" class="node">
<title>src/components/MindMap/hooks/useNodeInteraction.ts</title>
<g id="a_node131"><a xlink:href="src/components/MindMap/hooks/useNodeInteraction.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M687.58,-3609.75C687.58,-3609.75 594.67,-3609.75 594.67,-3609.75 591.58,-3609.75 588.5,-3606.66 588.5,-3603.58 588.5,-3603.58 588.5,-3597.41 588.5,-3597.41 588.5,-3594.33 591.58,-3591.25 594.67,-3591.25 594.67,-3591.25 687.58,-3591.25 687.58,-3591.25 690.67,-3591.25 693.75,-3594.33 693.75,-3597.41 693.75,-3597.41 693.75,-3603.58 693.75,-3603.58 693.75,-3606.66 690.67,-3609.75 687.58,-3609.75"/>
<text text-anchor="start" x="596.5" y="-3597.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">useNodeInteraction.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/hooks/useVirtualRendering.ts -->
<g id="node132" class="node">
<title>src/components/MindMap/hooks/useVirtualRendering.ts</title>
<g id="a_node132"><a xlink:href="src/components/MindMap/hooks/useVirtualRendering.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M689.83,-3640.75C689.83,-3640.75 592.42,-3640.75 592.42,-3640.75 589.33,-3640.75 586.25,-3637.66 586.25,-3634.58 586.25,-3634.58 586.25,-3628.41 586.25,-3628.41 586.25,-3625.33 589.33,-3622.25 592.42,-3622.25 592.42,-3622.25 689.83,-3622.25 689.83,-3622.25 692.92,-3622.25 696,-3625.33 696,-3628.41 696,-3628.41 696,-3634.58 696,-3634.58 696,-3637.66 692.92,-3640.75 689.83,-3640.75"/>
<text text-anchor="start" x="594.25" y="-3628.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">useVirtualRendering.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/index.css -->
<g id="node133" class="node">
<title>src/components/MindMap/index.css</title>
<g id="a_node133"><a xlink:href="src/components/MindMap/index.css" xlink:title="index.css">
<path fill="#ffffcc" stroke="black" d="M483.08,-3793.75C483.08,-3793.75 441.17,-3793.75 441.17,-3793.75 438.08,-3793.75 435,-3790.66 435,-3787.58 435,-3787.58 435,-3781.41 435,-3781.41 435,-3778.33 438.08,-3775.25 441.17,-3775.25 441.17,-3775.25 483.08,-3775.25 483.08,-3775.25 486.17,-3775.25 489.25,-3778.33 489.25,-3781.41 489.25,-3781.41 489.25,-3787.58 489.25,-3787.58 489.25,-3790.66 486.17,-3793.75 483.08,-3793.75"/>
<text text-anchor="start" x="443" y="-3781.2" font-family="Helvetica,sans-Serif" font-size="9.00">index.css</text>
</a>
</g>
</g>
<!-- src/components/MindMap/index.ts -->
<g id="node134" class="node">
<title>src/components/MindMap/index.ts</title>
<g id="a_node134"><a xlink:href="src/components/MindMap/index.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M292.21,-3758.75C292.21,-3758.75 250.54,-3758.75 250.54,-3758.75 247.46,-3758.75 244.38,-3755.66 244.38,-3752.58 244.38,-3752.58 244.38,-3746.41 244.38,-3746.41 244.38,-3743.33 247.46,-3740.25 250.54,-3740.25 250.54,-3740.25 292.21,-3740.25 292.21,-3740.25 295.29,-3740.25 298.38,-3743.33 298.38,-3746.41 298.38,-3746.41 298.38,-3752.58 298.38,-3752.58 298.38,-3755.66 295.29,-3758.75 292.21,-3758.75"/>
<text text-anchor="start" x="255.62" y="-3746.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">index.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/index.tsx -->
<g id="node135" class="node">
<title>src/components/MindMap/index.tsx</title>
<g id="a_node135"><a xlink:href="src/components/MindMap/index.tsx" xlink:title="index.tsx">
<path fill="#bbfeff" stroke="black" d="M292.21,-3829.75C292.21,-3829.75 250.54,-3829.75 250.54,-3829.75 247.46,-3829.75 244.38,-3826.66 244.38,-3823.58 244.38,-3823.58 244.38,-3817.41 244.38,-3817.41 244.38,-3814.33 247.46,-3811.25 250.54,-3811.25 250.54,-3811.25 292.21,-3811.25 292.21,-3811.25 295.29,-3811.25 298.38,-3814.33 298.38,-3817.41 298.38,-3817.41 298.38,-3823.58 298.38,-3823.58 298.38,-3826.66 295.29,-3829.75 292.21,-3829.75"/>
<text text-anchor="start" x="253.38" y="-3817.2" font-family="Helvetica,sans-Serif" font-size="9.00">index.tsx</text>
</a>
</g>
</g>
<!-- src/components/MindMap/index.tsx&#45;&gt;./components/Canvas/MindMapCanvas -->
<g id="edge251" class="edge">
<title>src/components/MindMap/index.tsx&#45;&gt;./components/Canvas/MindMapCanvas</title>
<g id="a_edge251"><a xlink:title="not&#45;to&#45;unresolvable">
<path fill="none" stroke="red" stroke-width="2" d="M293.64,-3810.84C311.01,-3801.67 334.37,-3786.06 345,-3764.5 365.22,-3723.5 334.1,-515.12 353,-473.5 369.76,-436.59 408.69,-408.31 434.97,-392.54"/>
<polygon fill="red" stroke="red" stroke-width="2" points="435.94,-394.41 440.07,-389.58 433.82,-390.78 435.94,-394.41"/>
</a>
</g>
<text text-anchor="middle" x="309.68" y="-2091.04" font-family="Helvetica,sans-Serif" font-size="9.00" fill="red">not&#45;to&#45;unresolvable</text>
</g>
<!-- src/components/MindMap/index.tsx&#45;&gt;node_modules/react -->
<g id="edge261" class="edge">
<title>src/components/MindMap/index.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M293.64,-3810.84C311.01,-3801.67 334.37,-3786.06 345,-3764.5 355.46,-3743.28 336.26,-375.2 353,-358.5 364.92,-346.6 1017.56,-346.45 1043,-372.5 1057.83,-387.68 1037.73,-1883.93 1051,-1900.5 1057.69,-1908.85 1068.26,-1912.85 1078.74,-1914.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.22,-1916.66 1084.43,-1915.31 1078.73,-1912.49 1078.22,-1916.66"/>
</g>
<!-- src/components/MindMap/index.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge258" class="edge">
<title>src/components/MindMap/index.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M298.57,-3822.74C312.68,-3824.55 330.18,-3827.78 345,-3833.5 348.91,-3835 349.02,-3837.16 353,-3838.5 465.11,-3876.08 802.79,-3798.63 883,-3885.5 900.06,-3903.96 878.52,-4768.67 891,-4790.5 899.21,-4804.84 913.99,-4815.12 928.28,-4822.2"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="927.04,-4823.95 933.37,-4824.55 928.8,-4820.13 927.04,-4823.95"/>
</g>
<!-- src/components/MindMap/index.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx -->
<g id="edge256" class="edge">
<title>src/components/MindMap/index.tsx&#45;&gt;src/components/MindMap/context/MindMapContext.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M298.85,-3817.19C314.57,-3815.31 334.9,-3813.04 353,-3811.5 375.43,-3809.58 539.62,-3815.7 555.25,-3799.5 575.58,-3778.42 546.68,-3292.96 567.62,-3272.5 590.89,-3249.77 681.83,-3267.66 714,-3272.5 719.98,-3273.39 721.06,-3275.35 727,-3276.5 761.16,-3283.09 856.68,-3266.75 883,-3289.5 935.43,-3334.81 958.73,-3561.46 964.51,-3628.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="962.41,-3628.19 965.01,-3633.99 966.6,-3627.84 962.41,-3628.19"/>
</g>
<!-- src/components/MindMap/index.tsx&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge257" class="edge">
<title>src/components/MindMap/index.tsx&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M298.52,-3822.04C348.45,-3825.03 460.8,-3832 555.25,-3839.5 701.07,-3851.06 741.53,-3832.31 883,-3869.5 958.42,-3889.32 974.1,-3904.97 1043,-3941.5 1105.15,-3974.44 1143.13,-3963.27 1177.75,-4024.5 1188.22,-4043.02 1181.55,-4769.64 1185.75,-4790.5 1194.87,-4835.74 1218.71,-4884.97 1232.12,-4910.29"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1230.1,-4910.97 1234.8,-4915.26 1233.8,-4908.98 1230.1,-4910.97"/>
</g>
<!-- src/components/MindMap/index.tsx&#45;&gt;src/components/MindMap/components/ControlPanel/ManualNodeControls.tsx -->
<g id="edge252" class="edge">
<title>src/components/MindMap/index.tsx&#45;&gt;src/components/MindMap/components/ControlPanel/ManualNodeControls.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M298.6,-3817.59C314.56,-3817.42 333.94,-3820.37 345,-3833.5 367.33,-3860.01 329.91,-5055.63 353,-5081.5 363.52,-5093.28 378.68,-5098.75 394.34,-5100.79"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="393.89,-5102.85 400.05,-5101.31 394.27,-5098.67 393.89,-5102.85"/>
</g>
<!-- src/components/MindMap/index.tsx&#45;&gt;src/components/MindMap/components/Controls/MindMapControls.tsx -->
<g id="edge253" class="edge">
<title>src/components/MindMap/index.tsx&#45;&gt;src/components/MindMap/components/Controls/MindMapControls.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M298.61,-3817.59C314.58,-3817.41 333.96,-3820.36 345,-3833.5 361.57,-3853.21 334.75,-5669.33 353,-5687.5 416.7,-5750.92 489.16,-5748.42 555.25,-5687.5 580.17,-5664.52 553.45,-5566.28 567.62,-5535.5 578.81,-5511.22 601.68,-5490.38 618.77,-5477.26"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="619.69,-5479.19 623.25,-5473.92 617.18,-5475.82 619.69,-5479.19"/>
</g>
<!-- src/components/MindMap/index.tsx&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx -->
<g id="edge254" class="edge">
<title>src/components/MindMap/index.tsx&#45;&gt;src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M283.12,-3810.83C297.71,-3798.43 325.16,-3777.56 353,-3769.5 439.34,-3744.48 491.37,-3706.25 555.25,-3769.5 579.77,-3793.77 558.34,-4978.27 567.62,-5011.5 601.96,-5134.39 679.18,-5135.73 714,-5258.5 725.11,-5297.68 701.19,-5959.99 727,-5991.5 728.93,-5993.85 731.12,-5995.91 733.51,-5997.71"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="732.39,-5999.49 738.61,-6000.78 734.56,-5995.89 732.39,-5999.49"/>
</g>
<!-- src/components/MindMap/index.tsx&#45;&gt;src/components/MindMap/components/Dialogs/ProjectDialog.tsx -->
<g id="edge255" class="edge">
<title>src/components/MindMap/index.tsx&#45;&gt;src/components/MindMap/components/Dialogs/ProjectDialog.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M298.61,-3817.59C314.58,-3817.41 333.96,-3820.36 345,-3833.5 361.81,-3853.49 336.37,-5694.34 353,-5714.5 411.32,-5785.17 482.03,-5699.41 555.25,-5754.5 564.84,-5761.71 558.02,-5771.3 567.62,-5778.5 574.11,-5783.35 581.8,-5786.66 589.72,-5788.89"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="589.08,-5790.9 595.4,-5790.22 590.04,-5786.81 589.08,-5790.9"/>
</g>
<!-- src/components/MindMap/index.tsx&#45;&gt;src/components/MindMap/hooks/useAutosave.ts -->
<g id="edge259" class="edge">
<title>src/components/MindMap/index.tsx&#45;&gt;src/components/MindMap/hooks/useAutosave.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M298.49,-3821.55C365.11,-3823.98 537.4,-3828.44 555.25,-3810.5 587.47,-3778.1 538.48,-3434.68 567.62,-3399.5 574.05,-3391.74 583.3,-3387.23 593.06,-3384.7"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="593.27,-3386.8 598.74,-3383.56 592.45,-3382.69 593.27,-3386.8"/>
</g>
<!-- src/components/MindMap/index.tsx&#45;&gt;src/components/MindMap/index.css -->
<g id="edge260" class="edge">
<title>src/components/MindMap/index.tsx&#45;&gt;src/components/MindMap/index.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M298.81,-3814.21C314.51,-3810.57 334.84,-3806.03 353,-3802.5 377.35,-3797.75 404.94,-3793.17 426.16,-3789.82"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="426.24,-3791.93 431.84,-3788.93 425.59,-3787.78 426.24,-3791.93"/>
</g>
<!-- src/components/MindMap/layouts/strategies/BottomUpLayout.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts -->
<g id="edge293" class="edge">
<title>src/components/MindMap/layouts/strategies/BottomUpLayout.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M979.07,-4352.17C996.54,-4368.25 1029.85,-4402.37 1043,-4439.5 1056.03,-4476.27 1040.48,-4752.93 1051,-4790.5 1060.66,-4824.99 1084.11,-4860.03 1099,-4879.93"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1097.27,-4881.13 1102.58,-4884.62 1100.6,-4878.58 1097.27,-4881.13"/>
</g>
<!-- src/components/MindMap/layouts/strategies/BottomUpLayout.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge294" class="edge">
<title>src/components/MindMap/layouts/strategies/BottomUpLayout.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1014.04,-4341.02C1024.97,-4342.99 1035.59,-4347.23 1043,-4355.5 1062.17,-4376.89 1032.82,-4462.26 1051,-4484.5 1087.92,-4529.65 1141.57,-4472.74 1177.75,-4518.5 1187.13,-4530.35 1182.62,-4775.7 1185.75,-4790.5 1195.3,-4835.65 1218.97,-4884.91 1232.23,-4910.27"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1230.21,-4910.93 1234.88,-4915.24 1233.91,-4908.96 1230.21,-4910.93"/>
</g>
<!-- src/components/MindMap/layouts/strategies/BottomUpLayout.ts&#45;&gt;src/components/MindMap/layouts/types.ts -->
<g id="edge295" class="edge">
<title>src/components/MindMap/layouts/strategies/BottomUpLayout.ts&#45;&gt;src/components/MindMap/layouts/types.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1014.08,-4335.44C1056.76,-4328.61 1121.86,-4317.4 1177.75,-4304.5 1187.19,-4302.31 1197.33,-4299.62 1206.68,-4296.99"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1207.26,-4299.01 1212.45,-4295.34 1206.1,-4294.97 1207.26,-4299.01"/>
</g>
<!-- src/components/MindMap/layouts/utils.ts -->
<g id="node145" class="node">
<title>src/components/MindMap/layouts/utils.ts</title>
<g id="a_node145"><a xlink:href="src/components/MindMap/layouts/utils.ts" xlink:title="utils.ts">
<path fill="#ddfeff" stroke="black" d="M1133.96,-4284.75C1133.96,-4284.75 1092.29,-4284.75 1092.29,-4284.75 1089.21,-4284.75 1086.12,-4281.66 1086.12,-4278.58 1086.12,-4278.58 1086.12,-4272.41 1086.12,-4272.41 1086.12,-4269.33 1089.21,-4266.25 1092.29,-4266.25 1092.29,-4266.25 1133.96,-4266.25 1133.96,-4266.25 1137.04,-4266.25 1140.12,-4269.33 1140.12,-4272.41 1140.12,-4272.41 1140.12,-4278.58 1140.12,-4278.58 1140.12,-4281.66 1137.04,-4284.75 1133.96,-4284.75"/>
<text text-anchor="start" x="1100.38" y="-4272.2" font-family="Helvetica,sans-Serif" font-size="9.00">utils.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/layouts/strategies/BottomUpLayout.ts&#45;&gt;src/components/MindMap/layouts/utils.ts -->
<g id="edge296" class="edge">
<title>src/components/MindMap/layouts/strategies/BottomUpLayout.ts&#45;&gt;src/components/MindMap/layouts/utils.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1013.8,-4335.96C1023.69,-4333.66 1033.89,-4330.59 1043,-4326.5 1062.74,-4317.61 1082.31,-4302.41 1095.56,-4290.91"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1096.89,-4292.54 1099.98,-4286.98 1094.1,-4289.4 1096.89,-4292.54"/>
</g>
<!-- src/components/MindMap/layouts/strategies/CompactLeftToRightLayout.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts -->
<g id="edge297" class="edge">
<title>src/components/MindMap/layouts/strategies/CompactLeftToRightLayout.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1035.19,-4258.33C1038.08,-4260.35 1040.72,-4262.72 1043,-4265.5 1052.26,-4276.76 1047.12,-4776.44 1051,-4790.5 1060.48,-4824.84 1083.78,-4859.69 1098.72,-4879.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1097.01,-4880.86 1102.33,-4884.35 1100.35,-4878.31 1097.01,-4880.86"/>
</g>
<!-- src/components/MindMap/layouts/strategies/CompactLeftToRightLayout.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge298" class="edge">
<title>src/components/MindMap/layouts/strategies/CompactLeftToRightLayout.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1035.42,-4258.64C1038.21,-4260.58 1040.76,-4262.85 1043,-4265.5 1055.73,-4280.52 1037.59,-4428.08 1051,-4442.5 1089.63,-4484.04 1139.56,-4415.55 1177.75,-4457.5 1190.21,-4471.18 1181.97,-4772.38 1185.75,-4790.5 1195.17,-4835.68 1218.9,-4884.93 1232.2,-4910.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1230.18,-4910.94 1234.85,-4915.25 1233.88,-4908.96 1230.18,-4910.94"/>
</g>
<!-- src/components/MindMap/layouts/strategies/CompactLeftToRightLayout.ts&#45;&gt;src/components/MindMap/layouts/types.ts -->
<g id="edge299" class="edge">
<title>src/components/MindMap/layouts/strategies/CompactLeftToRightLayout.ts&#45;&gt;src/components/MindMap/layouts/types.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1035.37,-4247.36C1076.88,-4247.44 1130.95,-4250.06 1177.75,-4260.5 1190.23,-4263.28 1203.41,-4268.26 1214.54,-4273.13"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1213.51,-4274.97 1219.84,-4275.54 1215.25,-4271.15 1213.51,-4274.97"/>
</g>
<!-- src/components/MindMap/layouts/strategies/CompactLeftToRightLayout.ts&#45;&gt;src/components/MindMap/layouts/utils.ts -->
<g id="edge300" class="edge">
<title>src/components/MindMap/layouts/strategies/CompactLeftToRightLayout.ts&#45;&gt;src/components/MindMap/layouts/utils.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1021.67,-4259.18C1040.17,-4262.51 1060.41,-4266.17 1077.04,-4269.17"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1076.62,-4271.22 1082.89,-4270.22 1077.36,-4267.09 1076.62,-4271.22"/>
</g>
<!-- src/components/MindMap/layouts/strategies/LeftToRightLayout.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts -->
<g id="edge301" class="edge">
<title>src/components/MindMap/layouts/strategies/LeftToRightLayout.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1016.8,-4219.3C1026.87,-4221.85 1036.41,-4226.47 1043,-4234.5 1052.8,-4246.43 1046.9,-4775.6 1051,-4790.5 1060.46,-4824.85 1083.76,-4859.69 1098.71,-4879.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1097.01,-4880.87 1102.33,-4884.35 1100.34,-4878.31 1097.01,-4880.87"/>
</g>
<!-- src/components/MindMap/layouts/strategies/LeftToRightLayout.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge302" class="edge">
<title>src/components/MindMap/layouts/strategies/LeftToRightLayout.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1017.03,-4219.53C1026.97,-4222.09 1036.39,-4226.66 1043,-4234.5 1056.91,-4250.98 1036.34,-4412.67 1051,-4428.5 1089.55,-4470.11 1139.58,-4401.53 1177.75,-4443.5 1190.72,-4457.76 1181.82,-4771.62 1185.75,-4790.5 1195.15,-4835.68 1218.88,-4884.93 1232.2,-4910.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1230.17,-4910.94 1234.85,-4915.25 1233.88,-4908.96 1230.17,-4910.94"/>
</g>
<!-- src/components/MindMap/layouts/strategies/LeftToRightLayout.ts&#45;&gt;src/components/MindMap/layouts/types.ts -->
<g id="edge303" class="edge">
<title>src/components/MindMap/layouts/strategies/LeftToRightLayout.ts&#45;&gt;src/components/MindMap/layouts/types.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1016.95,-4218.32C1060.43,-4219.61 1125.06,-4225.11 1177.75,-4244.5 1194.67,-4250.72 1211.61,-4262.16 1223.79,-4271.53"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1222.35,-4273.06 1228.35,-4275.15 1224.96,-4269.77 1222.35,-4273.06"/>
</g>
<!-- src/components/MindMap/layouts/strategies/LeftToRightLayout.ts&#45;&gt;src/components/MindMap/layouts/utils.ts -->
<g id="edge304" class="edge">
<title>src/components/MindMap/layouts/strategies/LeftToRightLayout.ts&#45;&gt;src/components/MindMap/layouts/utils.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1017.09,-4226.56C1025.87,-4228.68 1034.82,-4231.29 1043,-4234.5 1060.27,-4241.25 1078.24,-4252.04 1091.53,-4260.86"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1090.12,-4262.44 1096.27,-4264.08 1092.48,-4258.97 1090.12,-4262.44"/>
</g>
<!-- src/components/MindMap/layouts/strategies/RadialLayout.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts -->
<g id="edge305" class="edge">
<title>src/components/MindMap/layouts/strategies/RadialLayout.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1007.01,-4279.46C1020.45,-4281.25 1034.29,-4285.9 1043,-4296.5 1051.72,-4307.09 1047.34,-4777.27 1051,-4790.5 1060.49,-4824.84 1083.79,-4859.68 1098.72,-4879.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1097.02,-4880.86 1102.33,-4884.35 1100.35,-4878.31 1097.02,-4880.86"/>
</g>
<!-- src/components/MindMap/layouts/strategies/RadialLayout.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge306" class="edge">
<title>src/components/MindMap/layouts/strategies/RadialLayout.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1007.21,-4279.67C1020.5,-4281.51 1034.2,-4286.15 1043,-4296.5 1054.54,-4310.05 1038.85,-4443.49 1051,-4456.5 1089.73,-4497.94 1139.54,-4429.57 1177.75,-4471.5 1189.69,-4484.6 1182.12,-4773.14 1185.75,-4790.5 1195.2,-4835.67 1218.91,-4884.93 1232.21,-4910.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1230.18,-4910.94 1234.86,-4915.24 1233.89,-4908.96 1230.18,-4910.94"/>
</g>
<!-- src/components/MindMap/layouts/strategies/RadialLayout.ts&#45;&gt;src/components/MindMap/layouts/types.ts -->
<g id="edge307" class="edge">
<title>src/components/MindMap/layouts/strategies/RadialLayout.ts&#45;&gt;src/components/MindMap/layouts/types.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1007.18,-4286.19C1020.99,-4287.94 1036.66,-4289.63 1051,-4290.5 1107.23,-4293.87 1121.44,-4292.26 1177.75,-4290.5 1187,-4290.21 1196.98,-4289.68 1206.25,-4289.11"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1206.36,-4291.2 1212.21,-4288.72 1206.08,-4287.01 1206.36,-4291.2"/>
</g>
<!-- src/components/MindMap/layouts/strategies/RadialLayout.ts&#45;&gt;src/components/MindMap/layouts/utils.ts -->
<g id="edge308" class="edge">
<title>src/components/MindMap/layouts/strategies/RadialLayout.ts&#45;&gt;src/components/MindMap/layouts/utils.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1007,-4279.14C1028.79,-4278.39 1055.72,-4277.45 1076.87,-4276.72"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1076.9,-4278.82 1082.82,-4276.51 1076.75,-4274.62 1076.9,-4278.82"/>
</g>
<!-- src/components/MindMap/layouts/strategies/TopDownLayout.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts -->
<g id="edge309" class="edge">
<title>src/components/MindMap/layouts/strategies/TopDownLayout.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1013.27,-4311.52C1024.62,-4313.84 1035.64,-4318.55 1043,-4327.5 1059.35,-4347.35 1044.13,-4765.7 1051,-4790.5 1060.51,-4824.83 1083.8,-4859.68 1098.73,-4879.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1097.02,-4880.86 1102.34,-4884.34 1100.36,-4878.3 1097.02,-4880.86"/>
</g>
<!-- src/components/MindMap/layouts/strategies/TopDownLayout.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge310" class="edge">
<title>src/components/MindMap/layouts/strategies/TopDownLayout.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1013.05,-4311.71C1024.39,-4314.03 1035.47,-4318.7 1043,-4327.5 1063.7,-4351.67 1029.2,-4447.3 1051,-4470.5 1089.85,-4511.83 1139.52,-4443.59 1177.75,-4485.5 1189.17,-4498.02 1182.27,-4773.91 1185.75,-4790.5 1195.22,-4835.67 1218.93,-4884.92 1232.22,-4910.27"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1230.19,-4910.94 1234.86,-4915.24 1233.9,-4908.96 1230.19,-4910.94"/>
</g>
<!-- src/components/MindMap/layouts/strategies/TopDownLayout.ts&#45;&gt;src/components/MindMap/layouts/types.ts -->
<g id="edge311" class="edge">
<title>src/components/MindMap/layouts/strategies/TopDownLayout.ts&#45;&gt;src/components/MindMap/layouts/types.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1013.08,-4308.89C1055.61,-4306.24 1121.11,-4301.64 1177.75,-4295.5 1187.12,-4294.48 1197.22,-4293.16 1206.57,-4291.84"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1206.62,-4293.96 1212.26,-4291.03 1206.02,-4289.8 1206.62,-4293.96"/>
</g>
<!-- src/components/MindMap/layouts/strategies/TopDownLayout.ts&#45;&gt;src/components/MindMap/layouts/utils.ts -->
<g id="edge312" class="edge">
<title>src/components/MindMap/layouts/strategies/TopDownLayout.ts&#45;&gt;src/components/MindMap/layouts/utils.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1013.33,-4302.37C1023.15,-4300.24 1033.44,-4297.89 1043,-4295.5 1054.19,-4292.69 1066.29,-4289.3 1077.2,-4286.12"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1077.59,-4288.2 1082.75,-4284.49 1076.4,-4284.17 1077.59,-4288.2"/>
</g>
<!-- src/components/MindMap/layouts/adapters/MindMapStoreAdapter.ts -->
<g id="node141" class="node">
<title>src/components/MindMap/layouts/adapters/MindMapStoreAdapter.ts</title>
<g id="a_node141"><a xlink:href="src/components/MindMap/layouts/adapters/MindMapStoreAdapter.ts" xlink:title="MindMapStoreAdapter.ts">
<path fill="#ddfeff" stroke="black" d="M853.08,-4361.75C853.08,-4361.75 748.92,-4361.75 748.92,-4361.75 745.83,-4361.75 742.75,-4358.66 742.75,-4355.58 742.75,-4355.58 742.75,-4349.41 742.75,-4349.41 742.75,-4346.33 745.83,-4343.25 748.92,-4343.25 748.92,-4343.25 853.08,-4343.25 853.08,-4343.25 856.17,-4343.25 859.25,-4346.33 859.25,-4349.41 859.25,-4349.41 859.25,-4355.58 859.25,-4355.58 859.25,-4358.66 856.17,-4361.75 853.08,-4361.75"/>
<text text-anchor="start" x="750.75" y="-4349.2" font-family="Helvetica,sans-Serif" font-size="9.00">MindMapStoreAdapter.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/layouts/adapters/MindMapStoreAdapter.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge272" class="edge">
<title>src/components/MindMap/layouts/adapters/MindMapStoreAdapter.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M812.18,-4361.98C830.47,-4379.8 868.35,-4420.35 883,-4463.5 894.69,-4497.91 872.71,-4759.09 891,-4790.5 899.32,-4804.78 914.12,-4815.05 928.39,-4822.14"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="927.14,-4823.88 933.47,-4824.49 928.9,-4820.06 927.14,-4823.88"/>
</g>
<!-- src/components/MindMap/layouts/adapters/MindMapStoreAdapter.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts -->
<g id="edge270" class="edge">
<title>src/components/MindMap/layouts/adapters/MindMapStoreAdapter.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M807.65,-4362.15C819.92,-4382.1 851.13,-4427.67 891,-4447.5 921.41,-4462.62 1019.97,-4438.53 1043,-4463.5 1055.32,-4476.85 1046.09,-4773 1051,-4790.5 1060.68,-4824.98 1084.13,-4860.02 1099.01,-4879.93"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1097.28,-4881.12 1102.59,-4884.61 1100.61,-4878.57 1097.28,-4881.12"/>
</g>
<!-- src/components/MindMap/layouts/adapters/MindMapStoreAdapter.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge271" class="edge">
<title>src/components/MindMap/layouts/adapters/MindMapStoreAdapter.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M813.6,-4362.03C829.89,-4374.94 860.92,-4397.82 891,-4410.5 954.79,-4437.38 997.15,-4392.63 1043,-4444.5 1055.13,-4458.21 1043.68,-4509.71 1051,-4526.5 1084.95,-4604.34 1145.02,-4591.13 1177.75,-4669.5 1188.14,-4694.36 1179.69,-4764.24 1185.75,-4790.5 1196.08,-4835.28 1219.26,-4884.38 1232.28,-4909.89"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1230.41,-4910.84 1235.04,-4915.2 1234.14,-4908.9 1230.41,-4910.84"/>
</g>
<!-- src/components/MindMap/layouts/adapters/MindMapStoreAdapter.ts&#45;&gt;src/components/MindMap/layouts/types.ts -->
<g id="edge274" class="edge">
<title>src/components/MindMap/layouts/adapters/MindMapStoreAdapter.ts&#45;&gt;src/components/MindMap/layouts/types.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M824.93,-4362.18C842.62,-4369.11 867.89,-4377.82 891,-4381.5 957.72,-4392.09 976.96,-4395.75 1043,-4381.5 1112.13,-4366.58 1185.08,-4323.6 1220.22,-4300.92"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1221.12,-4302.84 1224.99,-4297.8 1218.82,-4299.32 1221.12,-4302.84"/>
</g>
<!-- src/components/MindMap/layouts/adapters/MindMapStoreAdapter.ts&#45;&gt;src/components/MindMap/layouts/LayoutManager.ts -->
<g id="edge273" class="edge">
<title>src/components/MindMap/layouts/adapters/MindMapStoreAdapter.ts&#45;&gt;src/components/MindMap/layouts/LayoutManager.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M801,-4342.86C801,-4331.17 801,-4319.48 801,-4307.79"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="803.1,-4307.87 801,-4301.87 798.9,-4307.87 803.1,-4307.87"/>
</g>
<!-- src/components/MindMap/layouts/hooks/useLayout.ts&#45;&gt;node_modules/react -->
<g id="edge281" class="edge">
<title>src/components/MindMap/layouts/hooks/useLayout.ts&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M973.24,-4042.89C987.87,-4015.44 1029,-3933.97 1043,-3860.5 1045.51,-3847.33 1042.79,-1941.09 1051,-1930.5 1057.61,-1921.97 1068.15,-1917.64 1078.64,-1915.53"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.74,-1917.64 1084.35,-1914.63 1078.09,-1913.49 1078.74,-1917.64"/>
</g>
<!-- src/components/MindMap/layouts/hooks/useLayout.ts&#45;&gt;src/components/MindMap/layouts/types.ts -->
<g id="edge280" class="edge">
<title>src/components/MindMap/layouts/hooks/useLayout.ts&#45;&gt;src/components/MindMap/layouts/types.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1001.2,-4049.33C1046.76,-4046.91 1128.59,-4049.67 1177.75,-4093.5 1229.97,-4140.04 1239.62,-4230.2 1241.31,-4268.54"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1239.2,-4268.16 1241.49,-4274.09 1243.4,-4268.02 1239.2,-4268.16"/>
</g>
<!-- src/components/MindMap/layouts/index.ts -->
<g id="node144" class="node">
<title>src/components/MindMap/layouts/index.ts</title>
<g id="a_node144"><a xlink:href="src/components/MindMap/layouts/index.ts" xlink:title="index.ts">
<path fill="#ddfeff" stroke="black" d="M661.96,-4241.75C661.96,-4241.75 620.29,-4241.75 620.29,-4241.75 617.21,-4241.75 614.12,-4238.66 614.12,-4235.58 614.12,-4235.58 614.12,-4229.41 614.12,-4229.41 614.12,-4226.33 617.21,-4223.25 620.29,-4223.25 620.29,-4223.25 661.96,-4223.25 661.96,-4223.25 665.04,-4223.25 668.12,-4226.33 668.12,-4229.41 668.12,-4229.41 668.12,-4235.58 668.12,-4235.58 668.12,-4238.66 665.04,-4241.75 661.96,-4241.75"/>
<text text-anchor="start" x="625.38" y="-4229.2" font-family="Helvetica,sans-Serif" font-size="9.00">index.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/layouts/hooks/useLayout.ts&#45;&gt;src/components/MindMap/layouts/index.ts -->
<g id="edge279" class="edge">
<title>src/components/MindMap/layouts/hooks/useLayout.ts&#45;&gt;src/components/MindMap/layouts/index.ts</title>
<g id="a_edge279"><a xlink:title="no&#45;circular">
<path fill="none" stroke="orange" stroke-width="2" d="M932.71,-4055.97C870.34,-4062.81 740.91,-4078.62 727,-4093.5 688.55,-4134.62 752.26,-4178.2 714,-4219.5 706.98,-4227.07 697.15,-4231.14 687,-4233.17"/>
<polygon fill="orange" stroke="orange" stroke-width="2" points="677.16,-4232 671.38,-4234.66 677.56,-4236.18 677.16,-4232"/>
<polyline fill="none" stroke="orange" stroke-width="2" points="678.35,-4233.99 681.34,-4233.71"/>
<ellipse fill="none" stroke="orange" stroke-width="2" cx="684.72" cy="-4233.39" rx="2.4" ry="2.4"/>
</a>
</g>
<text text-anchor="middle" x="698.68" y="-4147.86" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">no&#45;circular</text>
</g>
<!-- src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/components/LayoutSelector.tsx -->
<g id="edge283" class="edge">
<title>src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/components/LayoutSelector.tsx</title>
<g id="a_edge283"><a xlink:title="no&#45;circular">
<path fill="none" stroke="orange" stroke-width="2" d="M650.17,-4222.98C664.09,-4207.21 694.31,-4175.88 727,-4160.5 730.46,-4158.87 734.1,-4157.44 737.82,-4156.2"/>
<polygon fill="orange" stroke="orange" stroke-width="2" points="747.41,-4155.87 752.68,-4152.32 746.35,-4151.8 747.41,-4155.87"/>
<polyline fill="none" stroke="orange" stroke-width="2" points="745.91,-4154.09 743,-4154.85"/>
<ellipse fill="none" stroke="orange" stroke-width="2" cx="739.71" cy="-4155.7" rx="2.4" ry="2.4"/>
</a>
</g>
<text text-anchor="middle" x="675.05" y="-4170.6" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">no&#45;circular</text>
</g>
<!-- src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/types.ts -->
<g id="edge291" class="edge">
<title>src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/types.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M668.31,-4234.89C683.79,-4234.77 702.54,-4231.69 714,-4219.5 748.01,-4183.3 689.89,-4141.51 727,-4108.5 753.23,-4085.16 1008.01,-4105.53 1043,-4108.5 1103.54,-4113.62 1130.3,-4093.55 1177.75,-4131.5 1221.94,-4166.83 1235.85,-4237.89 1239.99,-4270.03"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1238.6,-4276.23 1239.99,-4270.02 1242.77,-4275.74 1238.6,-4276.23"/>
</g>
<!-- src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/LayoutManager.ts -->
<g id="edge285" class="edge">
<title>src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/LayoutManager.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M666.14,-4242.23C683.07,-4248.99 706.31,-4258.08 727,-4265.5 738.55,-4269.63 751.22,-4273.9 762.71,-4277.66"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="767.73,-4281.5 762.67,-4277.65 769.03,-4277.51 767.73,-4281.5"/>
</g>
<!-- src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/strategies/BottomUpLayout.ts -->
<g id="edge286" class="edge">
<title>src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/strategies/BottomUpLayout.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M648.76,-4242.02C661.58,-4259.58 691.64,-4296.79 727,-4313.5 727.26,-4313.62 842.57,-4327.57 912.9,-4336.08"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="918.74,-4338.9 913.03,-4336.09 919.24,-4334.73 918.74,-4338.9"/>
</g>
<!-- src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/strategies/CompactLeftToRightLayout.ts -->
<g id="edge287" class="edge">
<title>src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/strategies/CompactLeftToRightLayout.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M668.35,-4233.87C716.5,-4236.4 820.56,-4241.86 891.94,-4245.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="897.64,-4248.01 891.76,-4245.6 897.86,-4243.82 897.64,-4248.01"/>
</g>
<!-- src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/strategies/LeftToRightLayout.ts -->
<g id="edge288" class="edge">
<title>src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/strategies/LeftToRightLayout.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M668.35,-4231.36C720.7,-4229.1 839.13,-4223.98 909.81,-4220.92"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="916.01,-4222.76 909.93,-4220.92 915.83,-4218.56 916.01,-4222.76"/>
</g>
<!-- src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/strategies/RadialLayout.ts -->
<g id="edge289" class="edge">
<title>src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/strategies/RadialLayout.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M668.5,-4236.58C685.25,-4239.16 707.39,-4242.56 727,-4245.5 794,-4255.53 871.62,-4266.84 919.77,-4273.82"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="925.37,-4276.75 919.73,-4273.81 925.97,-4272.59 925.37,-4276.75"/>
</g>
<!-- src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/strategies/TopDownLayout.ts -->
<g id="edge290" class="edge">
<title>src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/strategies/TopDownLayout.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M649.52,-4242.02C662.89,-4258.4 692.9,-4291.41 727,-4304.5 759.82,-4317.09 853.82,-4316.08 913.69,-4313.96"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="919.89,-4315.83 913.82,-4313.95 919.73,-4311.63 919.89,-4315.83"/>
</g>
<!-- src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/adapters/MindMapStoreAdapter.ts -->
<g id="edge282" class="edge">
<title>src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/adapters/MindMapStoreAdapter.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M646.69,-4242.13C657.38,-4263.55 686.28,-4315.19 727,-4339.5 729.23,-4340.83 731.56,-4342.03 733.96,-4343.11"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="733.18,-4345.06 739.54,-4345.27 734.7,-4341.15 733.18,-4345.06"/>
</g>
<!-- src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/hooks/useLayout.ts -->
<g id="edge284" class="edge">
<title>src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/hooks/useLayout.ts</title>
<g id="a_edge284"><a xlink:title="no&#45;circular">
<path fill="none" stroke="orange" stroke-width="2" d="M668.46,-4235.02C683.99,-4234.96 702.75,-4231.89 714,-4219.5 737.68,-4193.41 701.54,-4085.85 727,-4061.5 733.66,-4055.13 845.96,-4053.26 914.22,-4052.72"/>
<polygon fill="orange" stroke="orange" stroke-width="2" points="923.99,-4054.75 929.98,-4052.61 923.96,-4050.55 923.99,-4054.75"/>
<polyline fill="none" stroke="orange" stroke-width="2" points="922.98,-4052.66 919.98,-4052.68"/>
<ellipse fill="none" stroke="orange" stroke-width="2" cx="916.58" cy="-4052.7" rx="2.4" ry="2.4"/>
</a>
</g>
<text text-anchor="middle" x="698.08" y="-4131.28" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">no&#45;circular</text>
</g>
<!-- src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/utils.ts -->
<g id="edge292" class="edge">
<title>src/components/MindMap/layouts/index.ts&#45;&gt;src/components/MindMap/layouts/utils.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M668.56,-4234.79C683.93,-4234.59 702.48,-4231.47 714,-4219.5 742.63,-4189.74 695.82,-4154.58 727,-4127.5 753.51,-4104.47 1013.38,-4108.64 1043,-4127.5 1089.51,-4157.1 1105.24,-4227.1 1110.12,-4259.06"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1108.88,-4265.28 1110.12,-4259.04 1113.04,-4264.69 1108.88,-4265.28"/>
</g>
<!-- src/components/MindMap/layouts/utils.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts -->
<g id="edge316" class="edge">
<title>src/components/MindMap/layouts/utils.ts&#45;&gt;src/components/MindMap/core/models/Connection.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1110.96,-4285.02C1101.04,-4329.12 1059.93,-4518.01 1047,-4675.5 1045.02,-4699.64 1040.62,-4761.13 1047,-4784.5 1057.18,-4821.74 1083.26,-4859.2 1099.46,-4879.97"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1097.67,-4881.09 1103.05,-4884.47 1100.95,-4878.47 1097.67,-4881.09"/>
</g>
<!-- src/components/MindMap/layouts/utils.ts&#45;&gt;src/components/MindMap/core/models/Node.ts -->
<g id="edge317" class="edge">
<title>src/components/MindMap/layouts/utils.ts&#45;&gt;src/components/MindMap/core/models/Node.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1120.02,-4285.23C1133.4,-4307.92 1165.82,-4366.54 1177.75,-4420.5 1195.5,-4500.8 1169.05,-4709.97 1185.75,-4790.5 1195.12,-4835.69 1218.86,-4884.94 1232.19,-4910.28"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1230.16,-4910.95 1234.84,-4915.25 1233.87,-4908.97 1230.16,-4910.95"/>
</g>
<!-- src/components/MindMap/layouts/utils.ts&#45;&gt;src/components/MindMap/layouts/types.ts -->
<g id="edge318" class="edge">
<title>src/components/MindMap/layouts/utils.ts&#45;&gt;src/components/MindMap/layouts/types.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1140.54,-4277.77C1159.6,-4279.42 1185.54,-4281.66 1206.37,-4283.45"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1206.07,-4285.54 1212.22,-4283.96 1206.43,-4281.35 1206.07,-4285.54"/>
</g>
<!-- src/components/MindMap/services/api/HatLLM.ts -->
<g id="node146" class="node">
<title>src/components/MindMap/services/api/HatLLM.ts</title>
<g id="a_node146"><a xlink:href="src/components/MindMap/services/api/HatLLM.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M663.58,-2930.75C663.58,-2930.75 618.67,-2930.75 618.67,-2930.75 615.58,-2930.75 612.5,-2927.66 612.5,-2924.58 612.5,-2924.58 612.5,-2918.41 612.5,-2918.41 612.5,-2915.33 615.58,-2912.25 618.67,-2912.25 618.67,-2912.25 663.58,-2912.25 663.58,-2912.25 666.67,-2912.25 669.75,-2915.33 669.75,-2918.41 669.75,-2918.41 669.75,-2924.58 669.75,-2924.58 669.75,-2927.66 666.67,-2930.75 663.58,-2930.75"/>
<text text-anchor="start" x="620.5" y="-2918.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">HatLLM.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/services/api/LLMService.ts -->
<g id="node147" class="node">
<title>src/components/MindMap/services/api/LLMService.ts</title>
<g id="a_node147"><a xlink:href="src/components/MindMap/services/api/LLMService.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M671.83,-2961.75C671.83,-2961.75 610.42,-2961.75 610.42,-2961.75 607.33,-2961.75 604.25,-2958.66 604.25,-2955.58 604.25,-2955.58 604.25,-2949.41 604.25,-2949.41 604.25,-2946.33 607.33,-2943.25 610.42,-2943.25 610.42,-2943.25 671.83,-2943.25 671.83,-2943.25 674.92,-2943.25 678,-2946.33 678,-2949.41 678,-2949.41 678,-2955.58 678,-2955.58 678,-2958.66 674.92,-2961.75 671.83,-2961.75"/>
<text text-anchor="start" x="612.25" y="-2949.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">LLMService.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/services/api/PromptRegistry.ts -->
<g id="node148" class="node">
<title>src/components/MindMap/services/api/PromptRegistry.ts</title>
<g id="a_node148"><a xlink:href="src/components/MindMap/services/api/PromptRegistry.ts" xlink:title="PromptRegistry.ts">
<path fill="#ddfeff" stroke="black" d="M678.58,-2992.75C678.58,-2992.75 603.67,-2992.75 603.67,-2992.75 600.58,-2992.75 597.5,-2989.66 597.5,-2986.58 597.5,-2986.58 597.5,-2980.41 597.5,-2980.41 597.5,-2977.33 600.58,-2974.25 603.67,-2974.25 603.67,-2974.25 678.58,-2974.25 678.58,-2974.25 681.67,-2974.25 684.75,-2977.33 684.75,-2980.41 684.75,-2980.41 684.75,-2986.58 684.75,-2986.58 684.75,-2989.66 681.67,-2992.75 678.58,-2992.75"/>
<text text-anchor="start" x="605.5" y="-2980.2" font-family="Helvetica,sans-Serif" font-size="9.00">PromptRegistry.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/services/api/types.ts -->
<g id="node149" class="node">
<title>src/components/MindMap/services/api/types.ts</title>
<g id="a_node149"><a xlink:href="src/components/MindMap/services/api/types.ts" xlink:title="types.ts">
<path fill="#ddfeff" stroke="black" d="M821.83,-2992.75C821.83,-2992.75 780.17,-2992.75 780.17,-2992.75 777.08,-2992.75 774,-2989.66 774,-2986.58 774,-2986.58 774,-2980.41 774,-2980.41 774,-2977.33 777.08,-2974.25 780.17,-2974.25 780.17,-2974.25 821.83,-2974.25 821.83,-2974.25 824.92,-2974.25 828,-2977.33 828,-2980.41 828,-2980.41 828,-2986.58 828,-2986.58 828,-2989.66 824.92,-2992.75 821.83,-2992.75"/>
<text text-anchor="start" x="785.62" y="-2980.2" font-family="Helvetica,sans-Serif" font-size="9.00">types.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/services/api/PromptRegistry.ts&#45;&gt;src/components/MindMap/services/api/types.ts -->
<g id="edge319" class="edge">
<title>src/components/MindMap/services/api/PromptRegistry.ts&#45;&gt;src/components/MindMap/services/api/types.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M684.85,-2983.5C709.84,-2983.5 741.01,-2983.5 764.62,-2983.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="764.56,-2985.6 770.56,-2983.5 764.56,-2981.4 764.56,-2985.6"/>
</g>
<!-- src/components/MindMap/services/api/debug.ts -->
<g id="node150" class="node">
<title>src/components/MindMap/services/api/debug.ts</title>
<g id="a_node150"><a xlink:href="src/components/MindMap/services/api/debug.ts" xlink:title="debug.ts">
<path fill="#ddfeff" stroke="black" d="M661.96,-3023.75C661.96,-3023.75 620.29,-3023.75 620.29,-3023.75 617.21,-3023.75 614.12,-3020.66 614.12,-3017.58 614.12,-3017.58 614.12,-3011.41 614.12,-3011.41 614.12,-3008.33 617.21,-3005.25 620.29,-3005.25 620.29,-3005.25 661.96,-3005.25 661.96,-3005.25 665.04,-3005.25 668.12,-3008.33 668.12,-3011.41 668.12,-3011.41 668.12,-3017.58 668.12,-3017.58 668.12,-3020.66 665.04,-3023.75 661.96,-3023.75"/>
<text text-anchor="start" x="623.5" y="-3011.2" font-family="Helvetica,sans-Serif" font-size="9.00">debug.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/utils/debug.ts -->
<g id="node151" class="node">
<title>src/components/MindMap/utils/debug.ts</title>
<g id="a_node151"><a xlink:href="src/components/MindMap/utils/debug.ts" xlink:title="debug.ts">
<path fill="#ddfeff" stroke="black" d="M821.83,-4492.75C821.83,-4492.75 780.17,-4492.75 780.17,-4492.75 777.08,-4492.75 774,-4489.66 774,-4486.58 774,-4486.58 774,-4480.41 774,-4480.41 774,-4477.33 777.08,-4474.25 780.17,-4474.25 780.17,-4474.25 821.83,-4474.25 821.83,-4474.25 824.92,-4474.25 828,-4477.33 828,-4480.41 828,-4480.41 828,-4486.58 828,-4486.58 828,-4489.66 824.92,-4492.75 821.83,-4492.75"/>
<text text-anchor="start" x="783.38" y="-4480.2" font-family="Helvetica,sans-Serif" font-size="9.00">debug.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/services/api/debug.ts&#45;&gt;src/components/MindMap/utils/debug.ts -->
<g id="edge320" class="edge">
<title>src/components/MindMap/services/api/debug.ts&#45;&gt;src/components/MindMap/utils/debug.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M668.53,-3022.11C684.73,-3028.29 704.18,-3038.77 714,-3055.5 732.8,-3087.5 714.61,-4356.51 727,-4391.5 738.28,-4423.37 765.58,-4452.56 783.41,-4469.16"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="786.46,-4474.78 783.44,-4469.19 789.29,-4471.67 786.46,-4474.78"/>
</g>
<!-- src/components/MindMap/services/prompts/YAMLLoader.ts -->
<g id="node152" class="node">
<title>src/components/MindMap/services/prompts/YAMLLoader.ts</title>
<g id="a_node152"><a xlink:href="src/components/MindMap/services/prompts/YAMLLoader.ts" xlink:title="YAMLLoader.ts">
<path fill="#ddfeff" stroke="black" d="M673.71,-2869.75C673.71,-2869.75 608.54,-2869.75 608.54,-2869.75 605.46,-2869.75 602.38,-2866.66 602.38,-2863.58 602.38,-2863.58 602.38,-2857.41 602.38,-2857.41 602.38,-2854.33 605.46,-2851.25 608.54,-2851.25 608.54,-2851.25 673.71,-2851.25 673.71,-2851.25 676.79,-2851.25 679.88,-2854.33 679.88,-2857.41 679.88,-2857.41 679.88,-2863.58 679.88,-2863.58 679.88,-2866.66 676.79,-2869.75 673.71,-2869.75"/>
<text text-anchor="start" x="610.38" y="-2857.2" font-family="Helvetica,sans-Serif" font-size="9.00">YAMLLoader.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/services/prompts/YAMLLoader.ts&#45;&gt;node_modules/js&#45;yaml -->
<g id="edge321" class="edge">
<title>src/components/MindMap/services/prompts/YAMLLoader.ts&#45;&gt;node_modules/js&#45;yaml</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M642.14,-2850.9C642.45,-2713.51 647.7,-1140.31 727,-684.5 768.54,-445.75 695.57,-307.78 891,-164.5 945.48,-124.55 995.11,-116.85 1043,-164.5 1058.3,-179.72 1037.77,-1701.44 1051,-1718.5 1057.61,-1727.01 1068.16,-1731.35 1078.65,-1733.46"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.1,-1735.5 1084.35,-1734.35 1078.75,-1731.35 1078.1,-1735.5"/>
</g>
<!-- src/components/MindMap/services/storage/ExportService.ts -->
<g id="node153" class="node">
<title>src/components/MindMap/services/storage/ExportService.ts</title>
<g id="a_node153"><a xlink:href="src/components/MindMap/services/storage/ExportService.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M675.96,-3084.75C675.96,-3084.75 606.29,-3084.75 606.29,-3084.75 603.21,-3084.75 600.12,-3081.66 600.12,-3078.58 600.12,-3078.58 600.12,-3072.41 600.12,-3072.41 600.12,-3069.33 603.21,-3066.25 606.29,-3066.25 606.29,-3066.25 675.96,-3066.25 675.96,-3066.25 679.04,-3066.25 682.12,-3069.33 682.12,-3072.41 682.12,-3072.41 682.12,-3078.58 682.12,-3078.58 682.12,-3081.66 679.04,-3084.75 675.96,-3084.75"/>
<text text-anchor="start" x="608.12" y="-3072.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">ExportService.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/services/storage/LocalStorage.ts -->
<g id="node154" class="node">
<title>src/components/MindMap/services/storage/LocalStorage.ts</title>
<g id="a_node154"><a xlink:href="src/components/MindMap/services/storage/LocalStorage.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M674.83,-3115.75C674.83,-3115.75 607.42,-3115.75 607.42,-3115.75 604.33,-3115.75 601.25,-3112.66 601.25,-3109.58 601.25,-3109.58 601.25,-3103.41 601.25,-3103.41 601.25,-3100.33 604.33,-3097.25 607.42,-3097.25 607.42,-3097.25 674.83,-3097.25 674.83,-3097.25 677.92,-3097.25 681,-3100.33 681,-3103.41 681,-3103.41 681,-3109.58 681,-3109.58 681,-3112.66 677.92,-3115.75 674.83,-3115.75"/>
<text text-anchor="start" x="609.25" y="-3103.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">LocalStorage.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/services/storage/ProjectStorage.ts -->
<g id="node155" class="node">
<title>src/components/MindMap/services/storage/ProjectStorage.ts</title>
<g id="a_node155"><a xlink:href="src/components/MindMap/services/storage/ProjectStorage.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M677.83,-3146.75C677.83,-3146.75 604.42,-3146.75 604.42,-3146.75 601.33,-3146.75 598.25,-3143.66 598.25,-3140.58 598.25,-3140.58 598.25,-3134.41 598.25,-3134.41 598.25,-3131.33 601.33,-3128.25 604.42,-3128.25 604.42,-3128.25 677.83,-3128.25 677.83,-3128.25 680.92,-3128.25 684,-3131.33 684,-3134.41 684,-3134.41 684,-3140.58 684,-3140.58 684,-3143.66 680.92,-3146.75 677.83,-3146.75"/>
<text text-anchor="start" x="606.25" y="-3134.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">ProjectStorage.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/types/MessageStatus.ts -->
<g id="node156" class="node">
<title>src/components/MindMap/types/MessageStatus.ts</title>
<g id="a_node156"><a xlink:href="src/components/MindMap/types/MessageStatus.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M1004.83,-3830.75C1004.83,-3830.75 929.17,-3830.75 929.17,-3830.75 926.08,-3830.75 923,-3827.66 923,-3824.58 923,-3824.58 923,-3818.41 923,-3818.41 923,-3815.33 926.08,-3812.25 929.17,-3812.25 929.17,-3812.25 1004.83,-3812.25 1004.83,-3812.25 1007.92,-3812.25 1011,-3815.33 1011,-3818.41 1011,-3818.41 1011,-3824.58 1011,-3824.58 1011,-3827.66 1007.92,-3830.75 1004.83,-3830.75"/>
<text text-anchor="start" x="931" y="-3818.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">MessageStatus.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/utils/MBCPProcessor.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge322" class="edge">
<title>src/components/MindMap/utils/MBCPProcessor.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M816.26,-4586.02C835.37,-4599.71 868.56,-4626.91 883,-4659.5 894.82,-4686.16 875.86,-4765.57 891,-4790.5 899.64,-4804.73 914.65,-4815.03 928.98,-4822.15"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="927.75,-4823.9 934.08,-4824.51 929.52,-4820.09 927.75,-4823.9"/>
</g>
<!-- src/components/MindMap/utils/ManualJsonProcessor.ts -->
<g id="node158" class="node">
<title>src/components/MindMap/utils/ManualJsonProcessor.ts</title>
<g id="a_node158"><a xlink:href="src/components/MindMap/utils/ManualJsonProcessor.ts" xlink:title="ManualJsonProcessor.ts">
<path fill="#ddfeff" stroke="black" d="M1018.71,-4554.75C1018.71,-4554.75 915.29,-4554.75 915.29,-4554.75 912.21,-4554.75 909.12,-4551.66 909.12,-4548.58 909.12,-4548.58 909.12,-4542.41 909.12,-4542.41 909.12,-4539.33 912.21,-4536.25 915.29,-4536.25 915.29,-4536.25 1018.71,-4536.25 1018.71,-4536.25 1021.79,-4536.25 1024.88,-4539.33 1024.88,-4542.41 1024.88,-4542.41 1024.88,-4548.58 1024.88,-4548.58 1024.88,-4551.66 1021.79,-4554.75 1018.71,-4554.75"/>
<text text-anchor="start" x="917.12" y="-4542.2" font-family="Helvetica,sans-Serif" font-size="9.00">ManualJsonProcessor.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/utils/security.ts -->
<g id="node159" class="node">
<title>src/components/MindMap/utils/security.ts</title>
<g id="a_node159"><a xlink:href="src/components/MindMap/utils/security.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M822.71,-4554.75C822.71,-4554.75 779.29,-4554.75 779.29,-4554.75 776.21,-4554.75 773.12,-4551.66 773.12,-4548.58 773.12,-4548.58 773.12,-4542.41 773.12,-4542.41 773.12,-4539.33 776.21,-4536.25 779.29,-4536.25 779.29,-4536.25 822.71,-4536.25 822.71,-4536.25 825.79,-4536.25 828.88,-4539.33 828.88,-4542.41 828.88,-4542.41 828.88,-4548.58 828.88,-4548.58 828.88,-4551.66 825.79,-4554.75 822.71,-4554.75"/>
<text text-anchor="start" x="781.12" y="-4542.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">security.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/utils/testManualMethod.ts -->
<g id="node160" class="node">
<title>src/components/MindMap/utils/testManualMethod.ts</title>
<g id="a_node160"><a xlink:href="src/components/MindMap/utils/testManualMethod.ts" xlink:title="testManualMethod.ts">
<path fill="#ddfeff" stroke="black" d="M845.21,-4523.75C845.21,-4523.75 756.79,-4523.75 756.79,-4523.75 753.71,-4523.75 750.62,-4520.66 750.62,-4517.58 750.62,-4517.58 750.62,-4511.41 750.62,-4511.41 750.62,-4508.33 753.71,-4505.25 756.79,-4505.25 756.79,-4505.25 845.21,-4505.25 845.21,-4505.25 848.29,-4505.25 851.38,-4508.33 851.38,-4511.41 851.38,-4511.41 851.38,-4517.58 851.38,-4517.58 851.38,-4520.66 848.29,-4523.75 845.21,-4523.75"/>
<text text-anchor="start" x="758.62" y="-4511.2" font-family="Helvetica,sans-Serif" font-size="9.00">testManualMethod.ts</text>
</a>
</g>
</g>
<!-- src/components/MindMap/utils/testManualMethod.ts&#45;&gt;src/components/MindMap/utils/ManualJsonProcessor.ts -->
<g id="edge323" class="edge">
<title>src/components/MindMap/utils/testManualMethod.ts&#45;&gt;src/components/MindMap/utils/ManualJsonProcessor.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M851.65,-4523.88C868.75,-4527.11 888.07,-4530.77 905.91,-4534.14"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="905.27,-4536.15 911.56,-4535.2 906.05,-4532.03 905.27,-4536.15"/>
</g>
<!-- src/governance/chat/GovernanceChatDialog.tsx -->
<g id="node162" class="node">
<title>src/governance/chat/GovernanceChatDialog.tsx</title>
<g id="a_node162"><a xlink:href="src/governance/chat/GovernanceChatDialog.tsx" xlink:title="GovernanceChatDialog.tsx">
<path fill="#bbfeff" stroke="black" d="M519.08,-6523.75C519.08,-6523.75 405.17,-6523.75 405.17,-6523.75 402.08,-6523.75 399,-6520.66 399,-6517.58 399,-6517.58 399,-6511.41 399,-6511.41 399,-6508.33 402.08,-6505.25 405.17,-6505.25 405.17,-6505.25 519.08,-6505.25 519.08,-6505.25 522.17,-6505.25 525.25,-6508.33 525.25,-6511.41 525.25,-6511.41 525.25,-6517.58 525.25,-6517.58 525.25,-6520.66 522.17,-6523.75 519.08,-6523.75"/>
<text text-anchor="start" x="407" y="-6511.2" font-family="Helvetica,sans-Serif" font-size="9.00">GovernanceChatDialog.tsx</text>
</a>
</g>
</g>
<!-- src/governance/chat/GovernanceChatDialog.tsx&#45;&gt;node_modules/react -->
<g id="edge343" class="edge">
<title>src/governance/chat/GovernanceChatDialog.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M525.7,-6516.86C537.03,-6514.28 547.64,-6509.32 555.25,-6500.5 563.55,-6490.87 567.14,-2853.19 567.62,-2840.5 568.2,-2825.3 715.96,-666.95 727,-656.5 752.5,-632.35 1018.09,-628.75 1043,-653.5 1055.29,-665.7 1040.16,-1886.98 1051,-1900.5 1057.7,-1908.84 1068.26,-1912.85 1078.74,-1914.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.22,-1916.66 1084.43,-1915.31 1078.73,-1912.49 1078.22,-1916.66"/>
</g>
<!-- src/governance/chat/GovernanceChatDialog.tsx&#45;&gt;src/governance/chat/Implementation.tsx -->
<g id="edge342" class="edge">
<title>src/governance/chat/GovernanceChatDialog.tsx&#45;&gt;src/governance/chat/Implementation.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M525.53,-6514.5C545.12,-6514.5 566.58,-6514.5 585.58,-6514.5"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="585.38,-6516.6 591.38,-6514.5 585.38,-6512.4 585.38,-6516.6"/>
</g>
<!-- src/governance/chat/Implementation.tsx&#45;&gt;node_modules/@mui/icons&#45;material -->
<g id="edge349" class="edge">
<title>src/governance/chat/Implementation.tsx&#45;&gt;node_modules/@mui/icons&#45;material</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M649.78,-6504.97C665.55,-6484.68 701.23,-6435.05 714,-6386.5 722.74,-6353.26 704.83,-1463.76 727,-1437.5 772.38,-1383.72 835.44,-1462.35 883,-1410.5 897.17,-1395.04 875.56,-1330.68 891,-1316.5 940.75,-1270.8 994.87,-1269.09 1043,-1316.5 1063.94,-1337.12 1032.86,-1822.36 1051,-1845.5 1055.57,-1851.32 1061.98,-1855.19 1068.94,-1857.72"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1068.24,-1859.71 1074.59,-1859.41 1069.44,-1855.68 1068.24,-1859.71"/>
</g>
<!-- src/governance/chat/Implementation.tsx&#45;&gt;node_modules/@mui/material -->
<g id="edge350" class="edge">
<title>src/governance/chat/Implementation.tsx&#45;&gt;node_modules/@mui/material</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M649.78,-6504.97C665.55,-6484.68 701.23,-6435.05 714,-6386.5 722.79,-6353.08 710.73,-1441.97 727,-1411.5 766.37,-1337.77 833.87,-1374.11 883,-1306.5 890.61,-1296.02 880.67,-1286.29 891,-1278.5 944.93,-1237.81 994.88,-1231.08 1043,-1278.5 1064.33,-1299.51 1031.98,-1794.36 1051,-1817.5 1057.65,-1825.59 1067.98,-1829.43 1078.27,-1831.07"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078,-1833.15 1084.2,-1831.73 1078.47,-1828.97 1078,-1833.15"/>
</g>
<!-- src/governance/chat/Implementation.tsx&#45;&gt;node_modules/react -->
<g id="edge351" class="edge">
<title>src/governance/chat/Implementation.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M649.78,-6504.97C665.54,-6484.67 701.2,-6435.04 714,-6386.5 726.34,-6339.69 724.34,-2949.83 727,-2901.5 762.93,-2247.69 816.54,-2087.91 883,-1436.5 883.93,-1427.39 884.22,-1360.65 891,-1354.5 941.03,-1309.1 994.88,-1307.08 1043,-1354.5 1064.61,-1375.79 1031.92,-1876.91 1051,-1900.5 1057.61,-1908.67 1067.93,-1912.67 1078.22,-1914.48"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1077.94,-1916.56 1084.16,-1915.25 1078.47,-1912.39 1077.94,-1916.56"/>
</g>
<!-- src/governance/chat/Implementation.tsx&#45;&gt;node_modules/react&#45;rnd -->
<g id="edge352" class="edge">
<title>src/governance/chat/Implementation.tsx&#45;&gt;node_modules/react&#45;rnd</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M649.78,-6504.97C665.55,-6484.68 701.23,-6435.05 714,-6386.5 722.97,-6352.38 707.14,-1336.65 727,-1307.5 767.36,-1248.24 991.91,-1192.2 1043,-1242.5 1063.08,-1262.26 1033.61,-1727.33 1051,-1749.5 1057.66,-1757.98 1068.22,-1762.31 1078.7,-1764.42"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.14,-1766.46 1084.4,-1765.32 1078.8,-1762.31 1078.14,-1766.46"/>
</g>
<!-- src/governance/chat/components/DialogHeader.tsx -->
<g id="node164" class="node">
<title>src/governance/chat/components/DialogHeader.tsx</title>
<g id="a_node164"><a xlink:href="src/governance/chat/components/DialogHeader.tsx" xlink:title="DialogHeader.tsx">
<path fill="#bbfeff" stroke="black" d="M838.08,-6558.75C838.08,-6558.75 763.92,-6558.75 763.92,-6558.75 760.83,-6558.75 757.75,-6555.66 757.75,-6552.58 757.75,-6552.58 757.75,-6546.41 757.75,-6546.41 757.75,-6543.33 760.83,-6540.25 763.92,-6540.25 763.92,-6540.25 838.08,-6540.25 838.08,-6540.25 841.17,-6540.25 844.25,-6543.33 844.25,-6546.41 844.25,-6546.41 844.25,-6552.58 844.25,-6552.58 844.25,-6555.66 841.17,-6558.75 838.08,-6558.75"/>
<text text-anchor="start" x="765.75" y="-6546.2" font-family="Helvetica,sans-Serif" font-size="9.00">DialogHeader.tsx</text>
</a>
</g>
</g>
<!-- src/governance/chat/Implementation.tsx&#45;&gt;src/governance/chat/components/DialogHeader.tsx -->
<g id="edge344" class="edge">
<title>src/governance/chat/Implementation.tsx&#45;&gt;src/governance/chat/components/DialogHeader.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M687.79,-6520.96C696.59,-6522.7 705.66,-6524.86 714,-6527.5 720.07,-6529.41 720.97,-6531.47 727,-6533.5 733.98,-6535.84 741.46,-6537.93 748.87,-6539.77"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="748.23,-6541.78 754.55,-6541.11 749.2,-6537.69 748.23,-6541.78"/>
</g>
<!-- src/governance/chat/components/MessageInput.tsx -->
<g id="node165" class="node">
<title>src/governance/chat/components/MessageInput.tsx</title>
<g id="a_node165"><a xlink:href="src/governance/chat/components/MessageInput.tsx" xlink:title="MessageInput.tsx">
<path fill="#bbfeff" stroke="black" d="M838.46,-6527.75C838.46,-6527.75 763.54,-6527.75 763.54,-6527.75 760.46,-6527.75 757.38,-6524.66 757.38,-6521.58 757.38,-6521.58 757.38,-6515.41 757.38,-6515.41 757.38,-6512.33 760.46,-6509.25 763.54,-6509.25 763.54,-6509.25 838.46,-6509.25 838.46,-6509.25 841.54,-6509.25 844.62,-6512.33 844.62,-6515.41 844.62,-6515.41 844.62,-6521.58 844.62,-6521.58 844.62,-6524.66 841.54,-6527.75 838.46,-6527.75"/>
<text text-anchor="start" x="765.38" y="-6515.2" font-family="Helvetica,sans-Serif" font-size="9.00">MessageInput.tsx</text>
</a>
</g>
</g>
<!-- src/governance/chat/Implementation.tsx&#45;&gt;src/governance/chat/components/MessageInput.tsx -->
<g id="edge345" class="edge">
<title>src/governance/chat/Implementation.tsx&#45;&gt;src/governance/chat/components/MessageInput.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M687.79,-6515.65C706.58,-6516.13 728.45,-6516.68 747.86,-6517.17"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="747.8,-6519.27 753.85,-6517.33 747.91,-6515.07 747.8,-6519.27"/>
</g>
<!-- src/governance/chat/hooks/useChat.ts -->
<g id="node166" class="node">
<title>src/governance/chat/hooks/useChat.ts</title>
<g id="a_node166"><a xlink:href="src/governance/chat/hooks/useChat.ts" xlink:title="useChat.ts">
<path fill="#ddfeff" stroke="black" d="M990.58,-6530.75C990.58,-6530.75 943.42,-6530.75 943.42,-6530.75 940.33,-6530.75 937.25,-6527.66 937.25,-6524.58 937.25,-6524.58 937.25,-6518.41 937.25,-6518.41 937.25,-6515.33 940.33,-6512.25 943.42,-6512.25 943.42,-6512.25 990.58,-6512.25 990.58,-6512.25 993.67,-6512.25 996.75,-6515.33 996.75,-6518.41 996.75,-6518.41 996.75,-6524.58 996.75,-6524.58 996.75,-6527.66 993.67,-6530.75 990.58,-6530.75"/>
<text text-anchor="start" x="945.25" y="-6518.2" font-family="Helvetica,sans-Serif" font-size="9.00">useChat.ts</text>
</a>
</g>
</g>
<!-- src/governance/chat/Implementation.tsx&#45;&gt;src/governance/chat/hooks/useChat.ts -->
<g id="edge346" class="edge">
<title>src/governance/chat/Implementation.tsx&#45;&gt;src/governance/chat/hooks/useChat.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M646.31,-6524.14C656.41,-6546.04 684.48,-6599.19 727,-6619.5 742.64,-6626.96 870.16,-6631.14 883,-6619.5 896.56,-6607.19 878.81,-6551.15 891,-6537.5 900.31,-6527.07 914.6,-6522.5 928.22,-6520.74"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="928.21,-6522.85 934,-6520.23 927.84,-6518.67 928.21,-6522.85"/>
</g>
<!-- src/governance/chat/MessageList.tsx -->
<g id="node167" class="node">
<title>src/governance/chat/MessageList.tsx</title>
<g id="a_node167"><a xlink:href="src/governance/chat/MessageList.tsx" xlink:title="MessageList.tsx">
<path fill="#bbfeff" stroke="black" d="M835.46,-6462.75C835.46,-6462.75 766.54,-6462.75 766.54,-6462.75 763.46,-6462.75 760.38,-6459.66 760.38,-6456.58 760.38,-6456.58 760.38,-6450.41 760.38,-6450.41 760.38,-6447.33 763.46,-6444.25 766.54,-6444.25 766.54,-6444.25 835.46,-6444.25 835.46,-6444.25 838.54,-6444.25 841.62,-6447.33 841.62,-6450.41 841.62,-6450.41 841.62,-6456.58 841.62,-6456.58 841.62,-6459.66 838.54,-6462.75 835.46,-6462.75"/>
<text text-anchor="start" x="768.38" y="-6450.2" font-family="Helvetica,sans-Serif" font-size="9.00">MessageList.tsx</text>
</a>
</g>
</g>
<!-- src/governance/chat/Implementation.tsx&#45;&gt;src/governance/chat/MessageList.tsx -->
<g id="edge347" class="edge">
<title>src/governance/chat/Implementation.tsx&#45;&gt;src/governance/chat/MessageList.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M656.18,-6504.88C672.49,-6494.16 700.55,-6477.08 727,-6467.5 734.66,-6464.72 742.99,-6462.46 751.18,-6460.63"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="751.57,-6462.7 757.02,-6459.43 750.72,-6458.58 751.57,-6462.7"/>
</g>
<!-- src/governance/chat/styles.css -->
<g id="node168" class="node">
<title>src/governance/chat/styles.css</title>
<g id="a_node168"><a xlink:href="src/governance/chat/styles.css" xlink:title="styles.css">
<path fill="#ffffcc" stroke="black" d="M822.33,-6656.75C822.33,-6656.75 779.67,-6656.75 779.67,-6656.75 776.58,-6656.75 773.5,-6653.66 773.5,-6650.58 773.5,-6650.58 773.5,-6644.41 773.5,-6644.41 773.5,-6641.33 776.58,-6638.25 779.67,-6638.25 779.67,-6638.25 822.33,-6638.25 822.33,-6638.25 825.42,-6638.25 828.5,-6641.33 828.5,-6644.41 828.5,-6644.41 828.5,-6650.58 828.5,-6650.58 828.5,-6653.66 825.42,-6656.75 822.33,-6656.75"/>
<text text-anchor="start" x="781.5" y="-6644.2" font-family="Helvetica,sans-Serif" font-size="9.00">styles.css</text>
</a>
</g>
</g>
<!-- src/governance/chat/Implementation.tsx&#45;&gt;src/governance/chat/styles.css -->
<g id="edge348" class="edge">
<title>src/governance/chat/Implementation.tsx&#45;&gt;src/governance/chat/styles.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M687.8,-6514.12C697.62,-6516.21 707.1,-6520.23 714,-6527.5 745.46,-6560.64 696.6,-6595.38 727,-6629.5 736.4,-6640.05 750.91,-6644.89 764.53,-6646.97"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="764.07,-6649.03 770.27,-6647.63 764.55,-6644.86 764.07,-6649.03"/>
</g>
<!-- src/governance/chat/components/DialogHeader.tsx&#45;&gt;node_modules/@mui/icons&#45;material -->
<g id="edge356" class="edge">
<title>src/governance/chat/components/DialogHeader.tsx&#45;&gt;node_modules/@mui/icons&#45;material</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M844.58,-6551.34C858.96,-6549.61 873.71,-6544.78 883,-6533.5 891.88,-6522.71 887.66,-2522.07 891,-2508.5 925.92,-2366.41 1006.57,-2358.19 1043,-2216.5 1047.73,-2198.09 1038.87,-1889.12 1051,-1874.5 1055.62,-1868.92 1061.99,-1865.37 1068.88,-1863.16"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1069.13,-1865.27 1074.43,-1861.76 1068.1,-1861.2 1069.13,-1865.27"/>
</g>
<!-- src/governance/chat/components/DialogHeader.tsx&#45;&gt;node_modules/@mui/material -->
<g id="edge357" class="edge">
<title>src/governance/chat/components/DialogHeader.tsx&#45;&gt;node_modules/@mui/material</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M844.58,-6551.35C858.96,-6549.62 873.71,-6544.78 883,-6533.5 903.18,-6508.99 872.29,-1988.14 891,-1962.5 932.84,-1905.16 997.88,-1968.29 1043,-1913.5 1062.06,-1890.34 1031.3,-1869.1 1051,-1846.5 1057.9,-1838.58 1068.19,-1834.34 1078.37,-1832.15"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.66,-1834.23 1084.22,-1831.14 1077.95,-1830.09 1078.66,-1834.23"/>
</g>
<!-- src/governance/chat/components/DialogHeader.tsx&#45;&gt;node_modules/react -->
<g id="edge358" class="edge">
<title>src/governance/chat/components/DialogHeader.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M844.58,-6551.34C858.96,-6549.61 873.71,-6544.78 883,-6533.5 899.51,-6513.45 881.42,-2818.63 891,-2794.5 927.42,-2702.73 1006.15,-2724.09 1043,-2632.5 1050.28,-2614.4 1039,-1945.86 1051,-1930.5 1057.64,-1922 1068.19,-1917.67 1078.68,-1915.55"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.78,-1917.66 1084.38,-1914.66 1078.13,-1913.52 1078.78,-1917.66"/>
</g>
<!-- src/governance/chat/components/MessageInput.tsx&#45;&gt;node_modules/@mui/icons&#45;material -->
<g id="edge359" class="edge">
<title>src/governance/chat/components/MessageInput.tsx&#45;&gt;node_modules/@mui/icons&#45;material</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M845,-6520.29C859.25,-6518.52 873.8,-6513.67 883,-6502.5 891.87,-6491.72 889.12,-2496.33 891,-2482.5 925.34,-2230.36 991.12,-2179.62 1043,-1930.5 1045.56,-1918.19 1042.42,-1883.68 1051,-1874.5 1055.83,-1869.32 1062.17,-1865.94 1068.94,-1863.78"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1069.06,-1865.92 1074.34,-1862.38 1068.01,-1861.85 1069.06,-1865.92"/>
</g>
<!-- src/governance/chat/components/MessageInput.tsx&#45;&gt;node_modules/@mui/material -->
<g id="edge360" class="edge">
<title>src/governance/chat/components/MessageInput.tsx&#45;&gt;node_modules/@mui/material</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M845,-6520.29C859.25,-6518.52 873.8,-6513.67 883,-6502.5 903.15,-6478.02 873.66,-1963.04 891,-1936.5 931.38,-1874.69 992.94,-1923.76 1043,-1869.5 1050.34,-1861.54 1043.05,-1853.84 1051,-1846.5 1058.52,-1839.55 1068.68,-1835.54 1078.56,-1833.25"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.69,-1835.36 1084.18,-1832.16 1077.89,-1831.24 1078.69,-1835.36"/>
</g>
<!-- src/governance/chat/components/MessageInput.tsx&#45;&gt;node_modules/react -->
<g id="edge361" class="edge">
<title>src/governance/chat/components/MessageInput.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M845,-6520.29C859.25,-6518.52 873.8,-6513.67 883,-6502.5 891.24,-6492.49 887.9,-2781.09 891,-2768.5 925.93,-2626.42 1007.19,-2618.35 1043,-2476.5 1046.71,-2461.79 1041.64,-1942.43 1051,-1930.5 1057.65,-1922.01 1068.21,-1917.68 1078.69,-1915.57"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.79,-1917.68 1084.39,-1914.67 1078.14,-1913.53 1078.79,-1917.68"/>
</g>
<!-- src/governance/chat/hooks/useChat.ts&#45;&gt;node_modules/react -->
<g id="edge375" class="edge">
<title>src/governance/chat/hooks/useChat.ts&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M970.09,-6511.86C980.63,-6462.78 1027.99,-6235.42 1043,-6046.5 1044.13,-6032.25 1042.26,-1941.8 1051,-1930.5 1057.6,-1921.96 1068.14,-1917.63 1078.63,-1915.52"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.73,-1917.63 1084.34,-1914.63 1078.08,-1913.48 1078.73,-1917.63"/>
</g>
<!-- src/governance/chat/hooks/useChat.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge374" class="edge">
<title>src/governance/chat/hooks/useChat.ts&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M964.38,-6511.95C952.38,-6467.78 902.67,-6278.48 887,-6119.5 885.32,-6102.49 882.05,-4905.85 887,-4889.5 892.81,-4870.3 910.67,-4857.25 928.05,-4848.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="928.59,-4850.86 933.19,-4846.48 926.86,-4847.03 928.59,-4850.86"/>
</g>
<!-- src/governance/chat/MessageList.tsx&#45;&gt;node_modules/@mui/icons&#45;material -->
<g id="edge353" class="edge">
<title>src/governance/chat/MessageList.tsx&#45;&gt;node_modules/@mui/icons&#45;material</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M821.64,-6443.84C841.3,-6432.96 870.39,-6413.23 883,-6386.5 889.67,-6372.36 880.52,-1896.1 891,-1884.5 934.89,-1835.9 1017.72,-1841.61 1068.61,-1850.99"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1068.1,-1853.03 1074.39,-1852.11 1068.9,-1848.91 1068.1,-1853.03"/>
</g>
<!-- src/governance/chat/MessageList.tsx&#45;&gt;node_modules/@mui/material -->
<g id="edge354" class="edge">
<title>src/governance/chat/MessageList.tsx&#45;&gt;node_modules/@mui/material</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M821.64,-6443.84C841.3,-6432.96 870.39,-6413.23 883,-6386.5 889.75,-6372.19 879.86,-1843.72 891,-1832.5 916.37,-1806.94 1023.28,-1817.76 1078.62,-1825.37"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.01,-1827.41 1084.25,-1826.16 1078.6,-1823.25 1078.01,-1827.41"/>
</g>
<!-- src/governance/chat/MessageList.tsx&#45;&gt;node_modules/react -->
<g id="edge355" class="edge">
<title>src/governance/chat/MessageList.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M821.64,-6443.84C841.3,-6432.96 870.38,-6413.23 883,-6386.5 894.72,-6361.65 873.67,-2451.81 891,-2430.5 934.55,-2376.93 999.05,-2451.73 1043,-2398.5 1059.56,-2378.44 1034.93,-1950.94 1051,-1930.5 1057.66,-1922.01 1068.22,-1917.69 1078.7,-1915.58"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.8,-1917.69 1084.4,-1914.68 1078.15,-1913.54 1078.8,-1917.69"/>
</g>
<!-- src/governance/chat/components/MessageList.tsx -->
<g id="node169" class="node">
<title>src/governance/chat/components/MessageList.tsx</title>
<g id="a_node169"><a xlink:href="src/governance/chat/components/MessageList.tsx" xlink:title="MessageList.tsx">
<path fill="#bbfeff" stroke="black" d="M835.46,-6589.75C835.46,-6589.75 766.54,-6589.75 766.54,-6589.75 763.46,-6589.75 760.38,-6586.66 760.38,-6583.58 760.38,-6583.58 760.38,-6577.41 760.38,-6577.41 760.38,-6574.33 763.46,-6571.25 766.54,-6571.25 766.54,-6571.25 835.46,-6571.25 835.46,-6571.25 838.54,-6571.25 841.62,-6574.33 841.62,-6577.41 841.62,-6577.41 841.62,-6583.58 841.62,-6583.58 841.62,-6586.66 838.54,-6589.75 835.46,-6589.75"/>
<text text-anchor="start" x="768.38" y="-6577.2" font-family="Helvetica,sans-Serif" font-size="9.00">MessageList.tsx</text>
</a>
</g>
</g>
<!-- src/governance/chat/components/MessageList.tsx&#45;&gt;node_modules/@mui/icons&#45;material -->
<g id="edge367" class="edge">
<title>src/governance/chat/components/MessageList.tsx&#45;&gt;node_modules/@mui/icons&#45;material</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M842.05,-6582.62C857.2,-6581.16 873.16,-6576.45 883,-6564.5 891.48,-6554.19 888.26,-2733.56 891,-2720.5 925.68,-2555.32 1007.17,-2537.42 1043,-2372.5 1045.94,-2358.98 1042.21,-1885.18 1051,-1874.5 1055.6,-1868.9 1061.96,-1865.34 1068.85,-1863.14"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1069.1,-1865.24 1074.39,-1861.73 1068.06,-1861.17 1069.1,-1865.24"/>
</g>
<!-- src/governance/chat/components/MessageList.tsx&#45;&gt;node_modules/@mui/material -->
<g id="edge368" class="edge">
<title>src/governance/chat/components/MessageList.tsx&#45;&gt;node_modules/@mui/material</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M842.05,-6582.62C857.21,-6581.16 873.16,-6576.45 883,-6564.5 892.26,-6553.25 888.48,-2383.84 891,-2369.5 925.48,-2173.57 1001.07,-2142.96 1043,-1948.5 1047.79,-1926.27 1036.41,-1863.94 1051,-1846.5 1057.86,-1838.29 1068.36,-1834.02 1078.74,-1831.87"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.78,-1833.99 1084.36,-1830.94 1078.1,-1829.84 1078.78,-1833.99"/>
</g>
<!-- src/governance/chat/components/MessageList.tsx&#45;&gt;node_modules/react -->
<g id="edge369" class="edge">
<title>src/governance/chat/components/MessageList.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M842.04,-6582.61C857.2,-6581.16 873.15,-6576.44 883,-6564.5 889.49,-6556.62 890.14,-3636.66 891,-3626.5 925.45,-3220.98 1007.06,-3128.88 1043,-2723.5 1043.97,-2712.52 1044.23,-1939.18 1051,-1930.5 1057.63,-1921.99 1068.19,-1917.66 1078.67,-1915.55"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.77,-1917.66 1084.37,-1914.65 1078.12,-1913.51 1078.77,-1917.66"/>
</g>
<!-- src/governance/chat/components/MessageList.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts -->
<g id="edge362" class="edge">
<title>src/governance/chat/components/MessageList.tsx&#45;&gt;src/components/MindMap/core/state/MindMapStore.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M842.03,-6582.6C857.19,-6581.14 873.14,-6576.43 883,-6564.5 897.87,-6546.49 879.74,-4903.95 891,-4883.5 899.63,-4867.81 915.8,-4856.49 930.93,-4848.8"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="931.44,-4850.88 935.96,-4846.41 929.64,-4847.09 931.44,-4850.88"/>
</g>
<!-- src/governance/chat/components/MessageList.tsx&#45;&gt;src/services/api/GovernanceLLM.ts -->
<g id="edge364" class="edge">
<title>src/governance/chat/components/MessageList.tsx&#45;&gt;src/services/api/GovernanceLLM.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M842.04,-6577.05C857,-6578.02 872.82,-6582.15 883,-6593.5 894.74,-6606.58 878.35,-6739.29 891,-6751.5 911.75,-6771.5 1093.7,-6764.64 1186.98,-6759.72"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1186.82,-6761.83 1192.7,-6759.41 1186.59,-6757.64 1186.82,-6761.83"/>
</g>
<!-- src/governance/chat/components/MessageList.tsx&#45;&gt;src/components/MindMap/utils/ManualJsonProcessor.ts -->
<g id="edge363" class="edge">
<title>src/governance/chat/components/MessageList.tsx&#45;&gt;src/components/MindMap/utils/ManualJsonProcessor.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M842.03,-6582.61C857.19,-6581.15 873.15,-6576.43 883,-6564.5 891.38,-6554.35 887.44,-4682.16 891,-4669.5 902.93,-4627.06 933.66,-4584.63 951.87,-4562.08"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="953.4,-4563.52 955.59,-4557.55 950.16,-4560.85 953.4,-4563.52"/>
</g>
<!-- src/governance/chat/components/MessageList.tsx&#45;&gt;src/governance/chat/hooks/useChat.ts -->
<g id="edge366" class="edge">
<title>src/governance/chat/components/MessageList.tsx&#45;&gt;src/governance/chat/hooks/useChat.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M842.1,-6580.89C856.55,-6579.06 871.98,-6574.53 883,-6564.5 892.25,-6556.07 881.81,-6545.99 891,-6537.5 900.93,-6528.31 914.78,-6523.9 927.88,-6521.91"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="928.05,-6524.01 933.77,-6521.24 927.57,-6519.84 928.05,-6524.01"/>
</g>
<!-- src/types/MessageStatus.ts -->
<g id="node170" class="node">
<title>src/types/MessageStatus.ts</title>
<g id="a_node170"><a xlink:href="src/types/MessageStatus.ts" xlink:title="MessageStatus.ts">
<path fill="#ddfeff" stroke="black" d="M1280.46,-6853.75C1280.46,-6853.75 1204.79,-6853.75 1204.79,-6853.75 1201.71,-6853.75 1198.62,-6850.66 1198.62,-6847.58 1198.62,-6847.58 1198.62,-6841.41 1198.62,-6841.41 1198.62,-6838.33 1201.71,-6835.25 1204.79,-6835.25 1204.79,-6835.25 1280.46,-6835.25 1280.46,-6835.25 1283.54,-6835.25 1286.62,-6838.33 1286.62,-6841.41 1286.62,-6841.41 1286.62,-6847.58 1286.62,-6847.58 1286.62,-6850.66 1283.54,-6853.75 1280.46,-6853.75"/>
<text text-anchor="start" x="1206.62" y="-6841.2" font-family="Helvetica,sans-Serif" font-size="9.00">MessageStatus.ts</text>
</a>
</g>
</g>
<!-- src/governance/chat/components/MessageList.tsx&#45;&gt;src/types/MessageStatus.ts -->
<g id="edge365" class="edge">
<title>src/governance/chat/components/MessageList.tsx&#45;&gt;src/types/MessageStatus.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M842.06,-6577.03C857.03,-6578 872.84,-6582.13 883,-6593.5 895.53,-6607.51 879.93,-6747.3 891,-6762.5 958.53,-6855.18 1109.3,-6857.14 1189.35,-6850.81"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1189.47,-6852.9 1195.27,-6850.3 1189.11,-6848.72 1189.47,-6852.9"/>
</g>
<!-- src/governance/chat/components/ModelSelector.tsx -->
<g id="node171" class="node">
<title>src/governance/chat/components/ModelSelector.tsx</title>
<g id="a_node171"><a xlink:href="src/governance/chat/components/ModelSelector.tsx" xlink:title="ModelSelector.tsx">
<path fill="#bbfeff" stroke="black" d="M838.83,-6496.75C838.83,-6496.75 763.17,-6496.75 763.17,-6496.75 760.08,-6496.75 757,-6493.66 757,-6490.58 757,-6490.58 757,-6484.41 757,-6484.41 757,-6481.33 760.08,-6478.25 763.17,-6478.25 763.17,-6478.25 838.83,-6478.25 838.83,-6478.25 841.92,-6478.25 845,-6481.33 845,-6484.41 845,-6484.41 845,-6490.58 845,-6490.58 845,-6493.66 841.92,-6496.75 838.83,-6496.75"/>
<text text-anchor="start" x="765" y="-6484.2" font-family="Helvetica,sans-Serif" font-size="9.00">ModelSelector.tsx</text>
</a>
</g>
</g>
<!-- src/governance/chat/components/ModelSelector.tsx&#45;&gt;../../../../core/models/Node -->
<g id="edge370" class="edge">
<title>src/governance/chat/components/ModelSelector.tsx&#45;&gt;../../../../core/models/Node</title>
<g id="a_edge370"><a xlink:title="not&#45;to&#45;unresolvable">
<path fill="none" stroke="red" stroke-width="2" d="M845.28,-6490.96C859.37,-6489.68 873.75,-6485.33 883,-6474.5 894.03,-6461.57 885.41,-1597.54 891,-1581.5 902.32,-1548.97 929.88,-1519.16 948.25,-1501.91"/>
<polygon fill="red" stroke="red" stroke-width="2" points="949.31,-1503.79 952.32,-1498.19 946.48,-1500.69 949.31,-1503.79"/>
</a>
</g>
<text text-anchor="middle" x="849.29" y="-3985.33" font-family="Helvetica,sans-Serif" font-size="9.00" fill="red">not&#45;to&#45;unresolvable</text>
</g>
<!-- src/governance/chat/components/ModelSelector.tsx&#45;&gt;node_modules/@mui/icons&#45;material -->
<g id="edge371" class="edge">
<title>src/governance/chat/components/ModelSelector.tsx&#45;&gt;node_modules/@mui/icons&#45;material</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M845.27,-6490.96C859.37,-6489.68 873.75,-6485.33 883,-6474.5 892.14,-6463.79 888.99,-2435.42 891,-2421.5 927.17,-2170.79 865.3,-2046.76 1051,-1874.5 1055.96,-1869.89 1062.14,-1866.76 1068.65,-1864.65"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1069.07,-1866.71 1074.33,-1863.14 1067.99,-1862.65 1069.07,-1866.71"/>
</g>
<!-- src/governance/chat/components/ModelSelector.tsx&#45;&gt;node_modules/@mui/material -->
<g id="edge372" class="edge">
<title>src/governance/chat/components/ModelSelector.tsx&#45;&gt;node_modules/@mui/material</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M845.27,-6490.96C859.37,-6489.68 873.75,-6485.33 883,-6474.5 903.71,-6450.24 871.12,-1906.44 891,-1881.5 914.09,-1852.53 1022.58,-1838.6 1078.54,-1833.24"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.47,-1835.36 1084.24,-1832.71 1078.08,-1831.18 1078.47,-1835.36"/>
</g>
<!-- src/governance/chat/components/ModelSelector.tsx&#45;&gt;node_modules/react -->
<g id="edge373" class="edge">
<title>src/governance/chat/components/ModelSelector.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M845.27,-6490.96C859.37,-6489.68 873.75,-6485.32 883,-6474.5 891.42,-6464.64 888.12,-2755.13 891,-2742.5 925.8,-2589.76 1007.13,-2576.98 1043,-2424.5 1046.14,-2411.14 1042.53,-1941.29 1051,-1930.5 1057.66,-1922.01 1068.22,-1917.68 1078.7,-1915.57"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.8,-1917.68 1084.4,-1914.68 1078.15,-1913.53 1078.8,-1917.68"/>
</g>
<!-- src/governance/chat/hooks/useDragAndResize.ts -->
<g id="node172" class="node">
<title>src/governance/chat/hooks/useDragAndResize.ts</title>
<g id="a_node172"><a xlink:href="src/governance/chat/hooks/useDragAndResize.ts" xlink:title="useDragAndResize.ts">
<path fill="#ddfeff" stroke="black" d="M1013.46,-6561.75C1013.46,-6561.75 920.54,-6561.75 920.54,-6561.75 917.46,-6561.75 914.38,-6558.66 914.38,-6555.58 914.38,-6555.58 914.38,-6549.41 914.38,-6549.41 914.38,-6546.33 917.46,-6543.25 920.54,-6543.25 920.54,-6543.25 1013.46,-6543.25 1013.46,-6543.25 1016.54,-6543.25 1019.62,-6546.33 1019.62,-6549.41 1019.62,-6549.41 1019.62,-6555.58 1019.62,-6555.58 1019.62,-6558.66 1016.54,-6561.75 1013.46,-6561.75"/>
<text text-anchor="start" x="922.38" y="-6549.2" font-family="Helvetica,sans-Serif" font-size="9.00">useDragAndResize.ts</text>
</a>
</g>
</g>
<!-- src/governance/chat/hooks/useDragAndResize.ts&#45;&gt;node_modules/react -->
<g id="edge377" class="edge">
<title>src/governance/chat/hooks/useDragAndResize.ts&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1019.95,-6550.9C1028.86,-6548.21 1037.13,-6543.73 1043,-6536.5 1063.14,-6511.65 1031.43,-1955.8 1051,-1930.5 1057.6,-1921.96 1068.14,-1917.63 1078.63,-1915.52"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.73,-1917.63 1084.34,-1914.62 1078.08,-1913.48 1078.73,-1917.63"/>
</g>
<!-- src/governance/chat/types/index.ts -->
<g id="node173" class="node">
<title>src/governance/chat/types/index.ts</title>
<g id="a_node173"><a xlink:href="src/governance/chat/types/index.ts" xlink:title="index.ts">
<path fill="#ddfeff" stroke="black" d="M1133.96,-6629.75C1133.96,-6629.75 1092.29,-6629.75 1092.29,-6629.75 1089.21,-6629.75 1086.12,-6626.66 1086.12,-6623.58 1086.12,-6623.58 1086.12,-6617.41 1086.12,-6617.41 1086.12,-6614.33 1089.21,-6611.25 1092.29,-6611.25 1092.29,-6611.25 1133.96,-6611.25 1133.96,-6611.25 1137.04,-6611.25 1140.12,-6614.33 1140.12,-6617.41 1140.12,-6617.41 1140.12,-6623.58 1140.12,-6623.58 1140.12,-6626.66 1137.04,-6629.75 1133.96,-6629.75"/>
<text text-anchor="start" x="1097.38" y="-6617.2" font-family="Helvetica,sans-Serif" font-size="9.00">index.ts</text>
</a>
</g>
</g>
<!-- src/governance/chat/hooks/useDragAndResize.ts&#45;&gt;src/governance/chat/types/index.ts -->
<g id="edge376" class="edge">
<title>src/governance/chat/hooks/useDragAndResize.ts&#45;&gt;src/governance/chat/types/index.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M988.65,-6562.24C1013.59,-6574 1055.66,-6593.85 1083.66,-6607.07"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1082.45,-6608.82 1088.78,-6609.48 1084.25,-6605.02 1082.45,-6608.82"/>
</g>
<!-- src/governance/chat/types/index.ts&#45;&gt;src/types/MessageStatus.ts -->
<g id="edge380" class="edge">
<title>src/governance/chat/types/index.ts&#45;&gt;src/types/MessageStatus.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1121.78,-6630.23C1135.58,-6648.38 1164.45,-6689.11 1177.75,-6728.5 1187.34,-6756.91 1171.77,-6768.96 1185.75,-6795.5 1193.1,-6809.45 1206.17,-6821.25 1217.82,-6829.77"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1216.58,-6831.47 1222.71,-6833.16 1218.98,-6828.01 1216.58,-6831.47"/>
</g>
<!-- src/services/ChatMemoryService.ts -->
<g id="node174" class="node">
<title>src/services/ChatMemoryService.ts</title>
<g id="a_node174"><a xlink:href="src/services/ChatMemoryService.ts" xlink:title="ChatMemoryService.ts">
<path fill="#ddfeff" stroke="black" d="M1161.08,-6792.75C1161.08,-6792.75 1065.17,-6792.75 1065.17,-6792.75 1062.08,-6792.75 1059,-6789.66 1059,-6786.58 1059,-6786.58 1059,-6780.41 1059,-6780.41 1059,-6777.33 1062.08,-6774.25 1065.17,-6774.25 1065.17,-6774.25 1161.08,-6774.25 1161.08,-6774.25 1164.17,-6774.25 1167.25,-6777.33 1167.25,-6780.41 1167.25,-6780.41 1167.25,-6786.58 1167.25,-6786.58 1167.25,-6789.66 1164.17,-6792.75 1161.08,-6792.75"/>
<text text-anchor="start" x="1067" y="-6780.2" font-family="Helvetica,sans-Serif" font-size="9.00">ChatMemoryService.ts</text>
</a>
</g>
</g>
<!-- src/governance/chat/types/index.ts&#45;&gt;src/services/ChatMemoryService.ts -->
<g id="edge379" class="edge">
<title>src/governance/chat/types/index.ts&#45;&gt;src/services/ChatMemoryService.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1113.12,-6630.05C1113.12,-6675 1113.12,-6719.96 1113.12,-6764.91"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1111.03,-6764.88 1113.13,-6770.88 1115.23,-6764.88 1111.03,-6764.88"/>
</g>
<!-- src/services/ChatMemoryService.ts&#45;&gt;src/services/api/GovernanceLLM.ts -->
<g id="edge386" class="edge">
<title>src/services/ChatMemoryService.ts&#45;&gt;src/services/api/GovernanceLLM.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1160.16,-6773.75C1168.82,-6771.91 1177.96,-6769.98 1186.87,-6768.09"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1187.27,-6770.15 1192.7,-6766.86 1186.4,-6766.04 1187.27,-6770.15"/>
</g>
<!-- src/services/ChatMemoryService.ts&#45;&gt;src/types/MessageStatus.ts -->
<g id="edge385" class="edge">
<title>src/services/ChatMemoryService.ts&#45;&gt;src/types/MessageStatus.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1134.33,-6793.16C1155.68,-6803.38 1189.53,-6819.57 1213.49,-6831.03"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1212.47,-6832.87 1218.79,-6833.57 1214.28,-6829.08 1212.47,-6832.87"/>
</g>
<!-- src/governance/chat/utils/debug.ts -->
<g id="node175" class="node">
<title>src/governance/chat/utils/debug.ts</title>
<g id="a_node175"><a xlink:href="src/governance/chat/utils/debug.ts" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M482.96,-6470.75C482.96,-6470.75 441.29,-6470.75 441.29,-6470.75 438.21,-6470.75 435.12,-6467.66 435.12,-6464.58 435.12,-6464.58 435.12,-6458.41 435.12,-6458.41 435.12,-6455.33 438.21,-6452.25 441.29,-6452.25 441.29,-6452.25 482.96,-6452.25 482.96,-6452.25 486.04,-6452.25 489.12,-6455.33 489.12,-6458.41 489.12,-6458.41 489.12,-6464.58 489.12,-6464.58 489.12,-6467.66 486.04,-6470.75 482.96,-6470.75"/>
<text text-anchor="start" x="444.5" y="-6458.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">debug.ts</text>
</a>
</g>
</g>
<!-- src/index.css -->
<g id="node176" class="node">
<title>src/index.css</title>
<g id="a_node176"><a xlink:href="src/index.css" xlink:title="index.css">
<path fill="#ffffcc" stroke="black" d="M161.33,-6477.75C161.33,-6477.75 119.42,-6477.75 119.42,-6477.75 116.33,-6477.75 113.25,-6474.66 113.25,-6471.58 113.25,-6471.58 113.25,-6465.41 113.25,-6465.41 113.25,-6462.33 116.33,-6459.25 119.42,-6459.25 119.42,-6459.25 161.33,-6459.25 161.33,-6459.25 164.42,-6459.25 167.5,-6462.33 167.5,-6465.41 167.5,-6465.41 167.5,-6471.58 167.5,-6471.58 167.5,-6474.66 164.42,-6477.75 161.33,-6477.75"/>
<text text-anchor="start" x="121.25" y="-6465.2" font-family="Helvetica,sans-Serif" font-size="9.00">index.css</text>
</a>
</g>
</g>
<!-- src/index.tsx -->
<g id="node177" class="node">
<title>src/index.tsx</title>
<g id="a_node177"><a xlink:href="src/index.tsx" xlink:title="no&#45;orphans">
<path fill="#ccffcc" stroke="orange" d="M68.83,-2837.75C68.83,-2837.75 27.17,-2837.75 27.17,-2837.75 24.08,-2837.75 21,-2834.66 21,-2831.58 21,-2831.58 21,-2825.41 21,-2825.41 21,-2822.33 24.08,-2819.25 27.17,-2819.25 27.17,-2819.25 68.83,-2819.25 68.83,-2819.25 71.92,-2819.25 75,-2822.33 75,-2825.41 75,-2825.41 75,-2831.58 75,-2831.58 75,-2834.66 71.92,-2837.75 68.83,-2837.75"/>
<text text-anchor="start" x="30" y="-2825.2" font-family="Helvetica,sans-Serif" font-size="9.00" fill="orange">index.tsx</text>
</a>
</g>
</g>
<!-- src/main.tsx -->
<g id="node178" class="node">
<title>src/main.tsx</title>
<g id="a_node178"><a xlink:href="src/main.tsx" xlink:title="main.tsx">
<path fill="#bbfeff" stroke="black" d="M68.83,-2868.75C68.83,-2868.75 27.17,-2868.75 27.17,-2868.75 24.08,-2868.75 21,-2865.66 21,-2862.58 21,-2862.58 21,-2856.41 21,-2856.41 21,-2853.33 24.08,-2850.25 27.17,-2850.25 27.17,-2850.25 68.83,-2850.25 68.83,-2850.25 71.92,-2850.25 75,-2853.33 75,-2856.41 75,-2856.41 75,-2862.58 75,-2862.58 75,-2865.66 71.92,-2868.75 68.83,-2868.75"/>
<text text-anchor="start" x="31.12" y="-2856.2" font-family="Helvetica,sans-Serif" font-size="9.00">main.tsx</text>
</a>
</g>
</g>
<!-- src/main.tsx&#45;&gt;node_modules/react -->
<g id="edge383" class="edge">
<title>src/main.tsx&#45;&gt;node_modules/react</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M74.14,-2849.88C76.45,-2848.09 78.48,-2845.98 80,-2843.5 90.08,-2827 74.92,-74.73 88,-60.5 143.05,-0.58 189.01,-44.5 270.38,-44.5 270.38,-44.5 270.38,-44.5 463.12,-44.5 743.6,-44.5 888.1,-59.67 1043,-293.5 1055.33,-312.1 1037.05,-1883.07 1051,-1900.5 1057.69,-1908.85 1068.26,-1912.85 1078.74,-1914.61"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1078.21,-1916.66 1084.43,-1915.31 1078.73,-1912.5 1078.21,-1916.66"/>
</g>
<!-- src/main.tsx&#45;&gt;node_modules/react&#45;dom -->
<g id="edge384" class="edge">
<title>src/main.tsx&#45;&gt;node_modules/react&#45;dom</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M74.14,-2849.88C76.45,-2848.09 78.48,-2845.98 80,-2843.5 90.19,-2826.82 74.58,-43.7 88,-29.5 143.75,29.55 189.17,-18.5 270.38,-18.5 270.38,-18.5 270.38,-18.5 802,-18.5 917.8,-18.5 978.29,-21.46 1043,-117.5 1055.91,-136.65 1036.84,-1762.25 1051,-1780.5 1057.28,-1788.59 1067.1,-1792.9 1077.05,-1795.12"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1076.51,-1797.16 1082.78,-1796.13 1077.24,-1793.02 1076.51,-1797.16"/>
</g>
<!-- src/main.tsx&#45;&gt;src/App.tsx -->
<g id="edge381" class="edge">
<title>src/main.tsx&#45;&gt;src/App.tsx</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M74.14,-2869.11C76.45,-2870.9 78.48,-2873.01 80,-2875.5 92.71,-2896.3 78.6,-6364 88,-6386.5 94.04,-6400.95 106.38,-6413.43 117.46,-6422.42"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="116.04,-6423.97 122.07,-6425.97 118.6,-6420.64 116.04,-6423.97"/>
</g>
<!-- src/main.tsx&#45;&gt;src/index.css -->
<g id="edge382" class="edge">
<title>src/main.tsx&#45;&gt;src/index.css</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M74.14,-2869.1C76.45,-2870.9 78.48,-2873.01 80,-2875.5 92.95,-2896.69 73.25,-6432.51 88,-6452.5 92.09,-6458.05 98.1,-6461.72 104.57,-6464.15"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="103.66,-6466.07 110.01,-6465.75 104.85,-6462.04 103.66,-6466.07"/>
</g>
<!-- src/stores/MindMapStore.ts -->
<g id="node179" class="node">
<title>src/stores/MindMapStore.ts</title>
<g id="a_node179"><a xlink:href="src/stores/MindMapStore.ts" xlink:title="MindMapStore.ts">
<path fill="#ddfeff" stroke="black" d="M1149.46,-6919.75C1149.46,-6919.75 1076.79,-6919.75 1076.79,-6919.75 1073.71,-6919.75 1070.62,-6916.66 1070.62,-6913.58 1070.62,-6913.58 1070.62,-6907.41 1070.62,-6907.41 1070.62,-6904.33 1073.71,-6901.25 1076.79,-6901.25 1076.79,-6901.25 1149.46,-6901.25 1149.46,-6901.25 1152.54,-6901.25 1155.62,-6904.33 1155.62,-6907.41 1155.62,-6907.41 1155.62,-6913.58 1155.62,-6913.58 1155.62,-6916.66 1152.54,-6919.75 1149.46,-6919.75"/>
<text text-anchor="start" x="1078.62" y="-6907.2" font-family="Helvetica,sans-Serif" font-size="9.00">MindMapStore.ts</text>
</a>
</g>
</g>
<!-- src/stores/MindMapStore.ts&#45;&gt;node_modules/zustand -->
<g id="edge388" class="edge">
<title>src/stores/MindMapStore.ts&#45;&gt;node_modules/zustand</title>
<path fill="none" stroke="#000000" stroke-opacity="0.200000" d="M1070.22,-6904.13C1059.59,-6900.03 1050.29,-6893.35 1047,-6882.5 1041.26,-6863.54 1046.67,-4050.3 1047,-4030.5 1060.62,-3222.35 1105.8,-2227.67 1112.33,-2086.47"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-opacity="0.200000" points="1114.43,-2086.66 1112.61,-2080.56 1110.23,-2086.46 1114.43,-2086.66"/>
</g>
<!-- src/stores/MindMapStore.ts&#45;&gt;src/services/api/GovernanceLLM.ts -->
<g id="edge387" class="edge">
<title>src/stores/MindMapStore.ts&#45;&gt;src/services/api/GovernanceLLM.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1155.96,-6903.4C1164.28,-6900.14 1172.16,-6895.4 1177.75,-6888.5 1195.8,-6866.23 1174.43,-6850.83 1185.75,-6824.5 1194.59,-6803.94 1211.61,-6784.64 1224.55,-6771.9"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1225.8,-6773.61 1228.69,-6767.95 1222.9,-6770.57 1225.8,-6773.61"/>
</g>
<!-- src/types.d.ts -->
<g id="node180" class="node">
<title>src/types.d.ts</title>
<g id="a_node180"><a xlink:href="src/types.d.ts" xlink:title="types.d.ts">
<path fill="#ccffcc" stroke="black" d="M68.96,-2899.75C68.96,-2899.75 27.04,-2899.75 27.04,-2899.75 23.96,-2899.75 20.88,-2896.66 20.88,-2893.58 20.88,-2893.58 20.88,-2887.41 20.88,-2887.41 20.88,-2884.33 23.96,-2881.25 27.04,-2881.25 27.04,-2881.25 68.96,-2881.25 68.96,-2881.25 72.04,-2881.25 75.12,-2884.33 75.12,-2887.41 75.12,-2887.41 75.12,-2893.58 75.12,-2893.58 75.12,-2896.66 72.04,-2899.75 68.96,-2899.75"/>
<text text-anchor="start" x="28.88" y="-2887.2" font-family="Helvetica,sans-Serif" font-size="9.00">types.d.ts</text>
</a>
</g>
</g>
<!-- src/types/mindmap.ts -->
<g id="node181" class="node">
<title>src/types/mindmap.ts</title>
<g id="a_node181"><a xlink:href="src/types/mindmap.ts" xlink:title="mindmap.ts">
<path fill="#ddfeff" stroke="black" d="M1268.08,-6884.75C1268.08,-6884.75 1217.17,-6884.75 1217.17,-6884.75 1214.08,-6884.75 1211,-6881.66 1211,-6878.58 1211,-6878.58 1211,-6872.41 1211,-6872.41 1211,-6869.33 1214.08,-6866.25 1217.17,-6866.25 1217.17,-6866.25 1268.08,-6866.25 1268.08,-6866.25 1271.17,-6866.25 1274.25,-6869.33 1274.25,-6872.41 1274.25,-6872.41 1274.25,-6878.58 1274.25,-6878.58 1274.25,-6881.66 1271.17,-6884.75 1268.08,-6884.75"/>
<text text-anchor="start" x="1219" y="-6872.2" font-family="Helvetica,sans-Serif" font-size="9.00">mindmap.ts</text>
</a>
</g>
</g>
<!-- src/types/react&#45;draggable.d.ts -->
<g id="node182" class="node">
<title>src/types/react&#45;draggable.d.ts</title>
<g id="a_node182"><a xlink:href="src/types/react-draggable.d.ts" xlink:title="react&#45;draggable.d.ts">
<path fill="#ccffcc" stroke="black" d="M1285.33,-6915.75C1285.33,-6915.75 1199.92,-6915.75 1199.92,-6915.75 1196.83,-6915.75 1193.75,-6912.66 1193.75,-6909.58 1193.75,-6909.58 1193.75,-6903.41 1193.75,-6903.41 1193.75,-6900.33 1196.83,-6897.25 1199.92,-6897.25 1199.92,-6897.25 1285.33,-6897.25 1285.33,-6897.25 1288.42,-6897.25 1291.5,-6900.33 1291.5,-6903.41 1291.5,-6903.41 1291.5,-6909.58 1291.5,-6909.58 1291.5,-6912.66 1288.42,-6915.75 1285.33,-6915.75"/>
<text text-anchor="start" x="1201.75" y="-6903.2" font-family="Helvetica,sans-Serif" font-size="9.00">react&#45;draggable.d.ts</text>
</a>
</g>
</g>
<!-- src/utils/keyboardShortcuts.ts -->
<g id="node183" class="node">
<title>src/utils/keyboardShortcuts.ts</title>
<g id="a_node183"><a xlink:href="src/utils/keyboardShortcuts.ts" xlink:title="keyboardShortcuts.ts">
<path fill="#ddfeff" stroke="black" d="M1157.71,-6980.75C1157.71,-6980.75 1068.54,-6980.75 1068.54,-6980.75 1065.46,-6980.75 1062.38,-6977.66 1062.38,-6974.58 1062.38,-6974.58 1062.38,-6968.41 1062.38,-6968.41 1062.38,-6965.33 1065.46,-6962.25 1068.54,-6962.25 1068.54,-6962.25 1157.71,-6962.25 1157.71,-6962.25 1160.79,-6962.25 1163.88,-6965.33 1163.88,-6968.41 1163.88,-6968.41 1163.88,-6974.58 1163.88,-6974.58 1163.88,-6977.66 1160.79,-6980.75 1157.71,-6980.75"/>
<text text-anchor="start" x="1070.38" y="-6968.2" font-family="Helvetica,sans-Serif" font-size="9.00">keyboardShortcuts.ts</text>
</a>
</g>
</g>
<!-- src/utils/keyboardShortcuts.ts&#45;&gt;src/types/mindmap.ts -->
<g id="edge389" class="edge">
<title>src/utils/keyboardShortcuts.ts&#45;&gt;src/types/mindmap.ts</title>
<path fill="none" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" d="M1161.71,-6961.77C1167.83,-6958.73 1173.45,-6954.73 1177.75,-6949.5 1194.26,-6929.38 1168.7,-6911.15 1185.75,-6891.5 1190.07,-6886.52 1195.82,-6883.04 1201.99,-6880.62"/>
<polygon fill="#000000" fill-opacity="0.200000" stroke="#000000" stroke-width="2" stroke-opacity="0.200000" points="1202.59,-6882.63 1207.69,-6878.83 1201.33,-6878.62 1202.59,-6882.63"/>
</g>
<!-- src/vite&#45;env.d.ts -->
<g id="node184" class="node">
<title>src/vite&#45;env.d.ts</title>
<g id="a_node184"><a xlink:href="src/vite-env.d.ts" xlink:title="vite&#45;env.d.ts">
<path fill="#ddfeff" stroke="black" d="M73.83,-2930.75C73.83,-2930.75 22.17,-2930.75 22.17,-2930.75 19.08,-2930.75 16,-2927.66 16,-2924.58 16,-2924.58 16,-2918.41 16,-2918.41 16,-2915.33 19.08,-2912.25 22.17,-2912.25 22.17,-2912.25 73.83,-2912.25 73.83,-2912.25 76.92,-2912.25 80,-2915.33 80,-2918.41 80,-2918.41 80,-2924.58 80,-2924.58 80,-2927.66 76.92,-2930.75 73.83,-2930.75"/>
<text text-anchor="start" x="24" y="-2918.2" font-family="Helvetica,sans-Serif" font-size="9.00">vite&#45;env.d.ts</text>
</a>
</g>
</g>
<!-- src/vite&#45;env.d.ts&#45;&gt;node_modules/vite -->
<g id="edge390" class="edge">
<title>src/vite&#45;env.d.ts&#45;&gt;node_modules/vite</title>
<g id="a_edge390"><a xlink:title="not&#45;to&#45;dev&#45;dep">
<path fill="none" stroke="red" d="M74.04,-2911.82C76.37,-2910.04 78.44,-2907.95 80,-2905.5 90,-2889.84 188.77,-250.53 193.25,-232.5 212.46,-155.1 190.63,-70.5 270.38,-70.5 270.38,-70.5 270.38,-70.5 463.12,-70.5 591.99,-70.5 949.94,19.64 1043,-69.5 1103.4,-127.35 1111.31,-1487.82 1112.06,-1655.58"/>
<polygon fill="none" stroke="red" points="1109.96,-1655.54 1112.08,-1661.53 1114.16,-1655.52 1109.96,-1655.54"/>
</a>
</g>
<text text-anchor="middle" x="71.28" y="-2475.23" font-family="Helvetica,sans-Serif" font-size="9.00" fill="red">not&#45;to&#45;dev&#45;dep</text>
</g>
</g>
</svg>
