/**
 * StageCompatWrapper.css
 *
 * Styles for the StageCompatWrapper component.
 */

/* Ensure the Konva stage is visible and properly positioned */
.konvajs-content {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: 1500 !important;
  background-color: #f8fafc;
}

/* Make the stage focusable but hide the outline */
.konvajs-content canvas {
  outline: none !important;
  z-index: 1500 !important;
}

/* Custom focus indicator for debugging */
.konvajs-content canvas:focus {
  /* Subtle indicator that doesn't interfere with the UI */
  box-shadow: inset 0 0 0 2px rgba(52, 152, 219, 0.3);
}
