// Mobile reactive fixes
(function() {
  try {
    console.log('Applying mobile reactive fixes...');
    
    // Check if we're on a mobile device
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    // Set a flag on the window object
    window.isMobileDevice = isMobile;
    
    if (!isMobile) {
      console.log('Not on mobile device, applying desktop optimizations');
      
      // Add desktop-specific optimizations
      if (typeof document !== 'undefined') {
        // Add style for desktop optimizations
        const style = document.createElement('style');
        style.textContent = `
          body {
            overflow: hidden;
          }
          
          .desktop-only {
            display: block !important;
          }
          
          .mobile-only {
            display: none !important;
          }
        `;
        document.head.appendChild(style);
        console.log('Added desktop optimization styles');
      }
      
      return;
    }
    
    console.log('Mobile device detected, applying reactive fixes');
    
    // Add mobile-specific styles
    if (typeof document !== 'undefined') {
      // Add style for mobile optimizations
      const style = document.createElement('style');
      style.textContent = `
        body {
          touch-action: manipulation;
          -webkit-text-size-adjust: 100%;
        }
        
        input, textarea, select, button {
          font-size: 16px; /* Prevents iOS zoom on focus */
        }
        
        .desktop-only {
          display: none !important;
        }
        
        .mobile-only {
          display: block !important;
        }
        
        /* Improve tap targets for mobile */
        button, 
        [role="button"],
        input[type="button"],
        input[type="submit"],
        input[type="reset"] {
          min-height: 44px;
          min-width: 44px;
        }
      `;
      document.head.appendChild(style);
      console.log('Added mobile optimization styles');
    }
    
    // Add viewport meta tag if not present
    if (typeof document !== 'undefined') {
      let viewportMeta = document.querySelector('meta[name="viewport"]');
      if (!viewportMeta) {
        viewportMeta = document.createElement('meta');
        viewportMeta.name = 'viewport';
        viewportMeta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
        document.head.appendChild(viewportMeta);
        console.log('Added viewport meta tag');
      }
    }
    
    // Add orientation change handler
    if (typeof window !== 'undefined') {
      window.addEventListener('orientationchange', function() {
        // Force redraw after orientation change
        setTimeout(function() {
          const event = new CustomEvent('mobile:orientation-changed', {
            detail: {
              orientation: window.orientation,
              width: window.innerWidth,
              height: window.innerHeight
            }
          });
          window.dispatchEvent(event);
          console.log('Orientation changed, dispatched event');
        }, 100);
      });
      console.log('Added orientation change handler');
    }
    
    console.log('Mobile reactive fixes applied successfully');
  } catch (error) {
    console.error('Error applying mobile reactive fixes:', error);
  }
})();
