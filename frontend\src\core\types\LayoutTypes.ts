/**
 * Unified Layout Types
 * 
 * This file contains the single source of truth for all layout-related types.
 * It replaces the fragmented type definitions that were causing conflicts.
 */

// Unified Layout Strategy Type - replaces all conflicting definitions
export type LayoutStrategyType = 
  | 'leftToRight'        // Standard horizontal tree layout
  | 'topDown'           // Vertical tree layout from top to bottom
  | 'bottomUp'          // Vertical tree layout from bottom to top
  | 'radial'            // Circular layout with root in center
  | 'compactLeftToRight'; // Optimized compact horizontal layout

// Layout Request - describes a request to change layout
export interface LayoutRequest {
  strategy: LayoutStrategyType;
  sheetId: string;
  requestOrigin: 'user' | 'system' | 'auto';
  preventIfStable?: boolean;
  preserveViewport?: boolean;
  reason?: string; // Why this layout change is being requested
}

// Layout Response - result of a layout change request
export interface LayoutResponse {
  success: boolean;
  strategy: LayoutStrategyType;
  reason?: string;
  governanceBlocked?: boolean;
  timestamp: number;
}

// Layout Configuration
export interface LayoutConfig {
  // Node spacing
  nodeWidth: number;
  nodeHeight: number;
  horizontalSpacing: number;
  verticalSpacing: number;
  levelSpacing: number;
  
  // Advanced options
  preventOverlap: boolean;
  centerLayout: boolean;
  adaptiveSpacing?: boolean;
  
  // Compact layout specific
  siblingCompactionFactor?: number;
  levelCompactionFactor?: number;
  minimumHorizontalSpacing?: number;
  minimumVerticalSpacing?: number;
}

// Default layout configuration
export const DEFAULT_LAYOUT_CONFIG: LayoutConfig = {
  nodeWidth: 180,
  nodeHeight: 70,
  horizontalSpacing: 80,
  verticalSpacing: 60,
  levelSpacing: 200,
  preventOverlap: true,
  centerLayout: true,
  adaptiveSpacing: false,
  
  // Compact layout defaults
  siblingCompactionFactor: 0.6,
  levelCompactionFactor: 0.7,
  minimumHorizontalSpacing: 30,
  minimumVerticalSpacing: 25
};

// Layout Strategy Information
export interface LayoutStrategyInfo {
  type: LayoutStrategyType;
  displayName: string;
  description: string;
  icon?: string;
  isDefault?: boolean;
  isExperimental?: boolean;
}

// Available layout strategies with metadata
export const LAYOUT_STRATEGIES: LayoutStrategyInfo[] = [
  {
    type: 'leftToRight',
    displayName: 'Left to Right',
    description: 'Standard horizontal tree layout',
    icon: '→',
    isDefault: true
  },
  {
    type: 'topDown',
    displayName: 'Top Down',
    description: 'Vertical tree layout from top to bottom',
    icon: '↓'
  },
  {
    type: 'bottomUp',
    displayName: 'Bottom Up',
    description: 'Vertical tree layout from bottom to top',
    icon: '↑'
  },
  {
    type: 'radial',
    displayName: 'Radial',
    description: 'Circular layout with root in center',
    icon: '⭕'
  },
  {
    type: 'compactLeftToRight',
    displayName: 'Compact Layout',
    description: 'Optimized compact horizontal layout',
    icon: '⚡',
    isExperimental: true
  }
];

// Layout validation utilities
export const LayoutTypeUtils = {
  /**
   * Check if a string is a valid layout strategy type
   */
  isValidStrategy(strategy: string): strategy is LayoutStrategyType {
    return LAYOUT_STRATEGIES.some(s => s.type === strategy);
  },

  /**
   * Get strategy info by type
   */
  getStrategyInfo(strategy: LayoutStrategyType): LayoutStrategyInfo | undefined {
    return LAYOUT_STRATEGIES.find(s => s.type === strategy);
  },

  /**
   * Get the default layout strategy
   */
  getDefaultStrategy(): LayoutStrategyType {
    const defaultStrategy = LAYOUT_STRATEGIES.find(s => s.isDefault);
    return defaultStrategy?.type || 'leftToRight';
  },

  /**
   * Get all available strategies
   */
  getAllStrategies(): LayoutStrategyType[] {
    return LAYOUT_STRATEGIES.map(s => s.type);
  },

  /**
   * Get strategies excluding experimental ones
   */
  getStableStrategies(): LayoutStrategyType[] {
    return LAYOUT_STRATEGIES
      .filter(s => !s.isExperimental)
      .map(s => s.type);
  }
};

// Layout event types for system communication
export type LayoutEventType = 
  | 'layout_request'
  | 'layout_complete'
  | 'layout_rejected'
  | 'layout_preference_changed'
  | 'governance_rule_violated';

export interface LayoutEvent {
  type: LayoutEventType;
  sheetId: string;
  strategy?: LayoutStrategyType;
  timestamp: number;
  metadata?: Record<string, any>;
} 