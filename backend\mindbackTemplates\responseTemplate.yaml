# this file describes the response tool for simple factual queries
tool_id: response
name: "Simple Response"
purpose: "Provide direct answers to simple factual queries without special visualization."
used_for:
  - Factual Inquiry
input_type: "Simple factual question"
output_type: "Direct answer"
llm_prompt: |
  You are being asked a simple factual query: "{user_input}"
  
  Provide a direct, concise answer with just the essential information.
  If it's a simple question like "What is the capital of Sweden?", respond with just "Stockholm."
  Avoid lengthy explanations unless absolutely necessary.
  
  Format your response as a simple JSON object.
return_structure:
  format: "json"
  schema:
    response:
      answer: "The direct factual answer"
      confidence: "High/Medium/Low"
  validation: "Must be a valid JSON object with a direct answer." 