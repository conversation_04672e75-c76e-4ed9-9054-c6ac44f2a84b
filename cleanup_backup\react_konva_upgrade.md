# React-Konva Upgrade Documentation

## Changes Made

We've made the following changes to resolve the dependency conflict between React 18 and react-konva:

1. Updated `frontend/package.json` to use react-konva version 18.2.10 instead of 17.0.2-6:
   ```diff
   - "react-konva": "17.0.2-6",
   + "react-konva": "^18.2.10",
   ```

2. Modified `run_setup.ps1` to use the `--force` flag with `npm audit fix`:
   ```diff
   - & $npmCmd audit fix
   + & $npmCmd audit fix --force
   ```

## Why These Changes Were Needed

The application was using react-konva version 17.0.2-6, which depends on react-reconciler version 0.26.2. This version of react-reconciler is only compatible with React 17.0.2, but the application is using React 18.2.0.

This version mismatch was causing:
1. Dependency conflicts during npm install
2. React Hooks violations in the mindmap creation workflows
3. Inconsistent component behavior due to the version mismatch

By upgrading to react-konva version 18.2.10, which is compatible with React 18, we resolve these issues.

## How to Test the Changes

1. Run the application using the updated `run_setup.ps1` script:
   ```
   .\run_setup.ps1
   ```

2. Test both mindmap creation workflows:
   - **Manual Workflow**: Enter a prompt that will trigger teleological intent, wait for the response, and click the "Build Mind Map" button.
   - **Automatic Workflow**: Select "Teleological" from the intent dropdown, enter a prompt, and wait for the response.

3. Verify that:
   - No React Hooks violations appear in the console
   - Mindmaps are created correctly
   - You can interact with the mindmaps (add nodes, move nodes, etc.)
   - Switching between mindsheets works correctly

## Potential Issues

### Breaking Changes in react-konva API

The upgrade from version 17 to 18 might introduce breaking changes in the react-konva API. If you encounter any issues, check the [react-konva changelog](https://github.com/konvajs/react-konva/blob/master/CHANGELOG.md) for details on breaking changes.

Common issues to watch for:
- Changes in event handling
- Changes in component props
- Changes in the way refs work

### Compatibility with Existing Code

Your existing code might rely on behavior specific to react-konva version 17. If you encounter issues, you might need to update your code to be compatible with version 18.

### Performance Issues

The new version might have different performance characteristics. Monitor the application for any performance regressions.

## Rollback Plan

If you encounter issues that can't be easily resolved, you can roll back to the previous version:

1. Revert the changes to `frontend/package.json`:
   ```diff
   - "react-konva": "^18.2.10",
   + "react-konva": "17.0.2-6",
   ```

2. Revert the changes to `run_setup.ps1`:
   ```diff
   - & $npmCmd audit fix --force
   + & $npmCmd audit fix
   ```

3. Run `npm install` to revert to the previous dependencies.

## Long-term Considerations

While this upgrade should resolve the immediate issues, you might want to consider:

1. **Evaluating Alternative Libraries**: If you continue to experience issues with react-konva, consider evaluating alternative libraries for canvas-based rendering.

2. **Reducing Dependency on Canvas-Based Rendering**: If possible, consider reducing your dependency on canvas-based rendering by using standard React components and CSS for simpler visualizations.

3. **Regular Dependency Updates**: Regularly update your dependencies to avoid similar issues in the future.
