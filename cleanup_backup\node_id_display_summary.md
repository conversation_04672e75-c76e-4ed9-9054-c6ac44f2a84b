# Node ID Display Summary

## Problem Description

The nodes in the mindmap were only displaying the title text, but the requirement was to display both the ID and the title in each node.

## Changes Made

### 1. Updated Node Rendering in MindMapCanvasSimple.tsx

Modified the node rendering code to display both the ID and the title:

```typescript
{/* Node ID at the top */}
<Text
  text={`ID: ${node.id.substring(0, 8)}`}
  width={node.width}
  height={20}
  y={-node.height/2 + 5}
  align="center"
  verticalAlign="top"
  fontSize={10}
  fontFamily="Arial, sans-serif"
  fontStyle="normal"
  fontVariant="normal"
  fill="#666666"
/>

{/* Node Title */}
<Text
  text={node.text}
  width={node.width}
  height={node.height - 20}
  y={-node.height/2 + 20}
  align="center"
  verticalAlign="middle"
  fontSize={14}
  fontFamily="Arial, sans-serif"
  fontStyle="normal"
  fontVariant="normal"
  fill="#333333"
  padding={8} // Increased padding for better readability
  lineHeight={1.3} // Better line spacing
/>
```

Key changes:
- Added a new Text component to display the node ID at the top of the node
- Truncated the ID to 8 characters for better readability
- Adjusted the position and size of the title text to accommodate the ID text
- Used a lighter color for the ID text to differentiate it from the title

### 2. Increased Default Node Height in MindMapStore.ts

Increased the default node height to accommodate both the ID and title text:

```typescript
// Default values
export const defaultNodeValues = {
  width: 180, // Increased width for better text display
  height: 90, // Increased height to accommodate ID and title
  color: '#ffffff',
  borderColor: '#2c3e50',
  shape: 'rectangle' as const,
};
```

Changed the height from 70 to 90 pixels to provide more space for both the ID and title text.

## Why These Changes Work

1. **Separate Text Components**: By using separate Text components for the ID and title, we can control their positioning and styling independently.

2. **Vertical Positioning**: The ID text is positioned at the top of the node, and the title text is positioned below it, creating a clear visual hierarchy.

3. **Visual Differentiation**: The ID text is smaller and lighter in color than the title text, making it clear which is which.

4. **Increased Node Height**: The increased node height provides enough space for both the ID and title text without overcrowding.

## Testing Instructions

To verify the changes:

1. Start the application using `run_setup.ps1`
2. Open the application in your browser at http://localhost:5173/
3. Select "mindmap" from the intention dropdown
4. Verify that each node now displays both the ID and title
5. Create a new node and verify that it also displays both the ID and title
6. Double-click on a node to open the NodeBox and verify that the ID displayed in the NodeBox matches the ID displayed in the node

## Expected Results

- Each node should display the ID at the top in a smaller, lighter font
- Each node should display the title below the ID in a larger, darker font
- The ID should be truncated to 8 characters for better readability
- The nodes should be tall enough to accommodate both the ID and title text without overcrowding
