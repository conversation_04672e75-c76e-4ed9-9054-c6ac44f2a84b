# MindBack_V1 - Enhanced Backend Architecture

```mermaid
graph TD
    APIGateway[API Gateway] --> RequestRouter[Request Router]
    
    subgraph "API Layer (15 endpoints found)"
        API_POST_mindmap_save[POST /mindmap/save]
        API_GET_mindmap_load1[GET /mindmap/load]
        API_GET2[GET /]
        API_GET_api_health3[GET /api/health]
        API_GET4[GET /]
        API_GET_health5[GET /health]
        API_GET_api_health6[GET /api/health]
        API_POST_chat7[POST /chat]
        RequestRouter --> API_0
        RequestRouter --> API_GET_mindmap_load1
        RequestRouter --> API_GET2
        RequestRouter --> API_GET_api_health3
        RequestRouter --> API_GET4
        RequestRouter --> API_GET_health5
        RequestRouter --> API_GET_api_health6
        RequestRouter --> API_POST_chat7
    end
    
    subgraph "Service Layer"
        CLS0[ConnectionManager]
        CLS0 -->|Methods| CLS0Methods[__init__, disconnect]
        CLS1[MindMapNode]
        CLS2[Settings]
        CLS3[Config]
        CLS4[LLMChatRequest]
        CLS5[ActionModel]
        CLS5 -->|Methods| CLS5Methods[validate_status]
    end
    API0 --> CLS0
    
    subgraph "Function Layer (12 functions)"
        FUNC0[__init__()]
        FUNC0 -->|In file| FUNC0File[backend\main.py]
        FUNC1[disconnect()]
        FUNC1 -->|In file| FUNC1File[backend\main.py]
        FUNC2[get_settings()]
        FUNC2 -->|In file| FUNC2File[backend\api\config\settings.py]
        FUNC3[validate_status()]
        FUNC3 -->|In file| FUNC3File[backend\api\models\mbcp_models.py]
        FUNC4[validate_intent()]
        FUNC4 -->|In file| FUNC4File[backend\api\models\mbcp_models.py]
        FUNC5[validate_agent()]
        FUNC5 -->|In file| FUNC5File[backend\api\models\mbcp_models.py]
        FUNC6[load_yaml_prompt()]
        FUNC6 -->|In file| FUNC6File[backend\api\routes\llm_fixed.py]
        FUNC7[format_prompt_with_values()]
        FUNC7 -->|In file| FUNC7File[backend\api\routes\llm_fixed.py]
    end
    
    subgraph "Data Layer"
        MODEL0[ActionModel]
        MODEL1[MetadataModel]
        MODEL2[NodeModel]
        MODEL3[ActionModel]
        FastAPICore[FastAPI Core]
    end
    
    subgraph "File Structure & Dependencies"
        PythonFiles[Python Files: 10917]
        ImportRelations[Import Relations: 976]
    end
    API0 --> CLS0
    CLS0 --> FUNC0
    FUNC0 --> MODEL0

    
    %% Enhanced Project Statistics:
    %% Backend Files: 10917
    %% API Endpoints: 15
    %% Functions: 12
    %% Classes: 15
    %% Model Classes: 6
    %% Import Relations: 976
    %% Frameworks: Python, Python, Python, FastAPI, React, Vite
    
    classDef api fill:#7e57c2,stroke:#4527a0,color:white;
    classDef service fill:#26a69a,stroke:#00796b,color:white;
    classDef function fill:#42a5f5,stroke:#1976d2,color:white;
    classDef storage fill:#ef5350,stroke:#c62828,color:white;
    classDef realdata fill:#ff9800,stroke:#e65100,color:white;
    classDef file fill:#607d8b,stroke:#263238,color:white;
    
    class RequestRouter,APIGateway api;
    class BusinessLogic,DataAccess service;
    class Database,FileSystem,FastAPICore,DjangoORM storage;
    class PythonFiles,ImportRelations file;
    class API0,API1,API2,API3,API4,API5,API6,API7 realdata;
    class CLS_ConnectionManager,CLS_MindMapNode1,CLS_Settings2,CLS_Config3 realdata;
    class FUNC_init,FUNC_disconnect1,FUNC_get_settings2,FUNC_validate_status3,FUNC_validate_intent4,FUNC_validate_agent5,FUNC_load_yaml_prompt6,FUNC_format_prompt_with_values7 function;
    class MODEL_ActionModel,MODEL_MetadataModel1,MODEL_NodeModel2,MODEL_ActionModel3 storage;
    class PythonFiles,ConfigFiles,TestFiles fileinfo;
    class FastAPICore framework;
```