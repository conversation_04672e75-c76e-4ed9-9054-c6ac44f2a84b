import React from 'react';
import Implementation from './Implementation';

interface GovernanceChatDialogProps {
  isOpen: boolean;
  isCollapsed?: boolean;
  onClose: () => void;
  onCollapse?: () => void;
  mindMapState?: any;
  onAction?: (action: any) => void;
  isContextPanelOpen?: boolean; // New prop to indicate if context panel is open
}

const GovernanceChatDialog: React.FC<GovernanceChatDialogProps> = ({
  isOpen,
  isCollapsed = false,
  onClose,
  onCollapse,
  mindMapState,
  onAction,
  isContextPanelOpen = false // Default to false
}) => {
  // Simply pass props to the implementation
  return (
    <Implementation
      isOpen={isOpen}
      isCollapsed={isCollapsed}
      onClose={onClose}
      onCollapse={onCollapse}
      handleDrag={true}
      mindMapState={mindMapState}
      onAction={onAction}
      isContextPanelOpen={isContextPanelOpen}
    />
  );
};

export default GovernanceChatDialog;