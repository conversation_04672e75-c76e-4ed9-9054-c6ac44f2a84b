import React, { useCallback, useMemo } from 'react';
import Konva from 'konva';
import { Group, Rect, Text } from 'react-konva';
import { Node } from '../../core/models/Node';
import { NodeMetadata } from '../../../../services/api/GovernanceLLM';

// Cast Konva components to any to bypass TypeScript errors
const KonvaGroup = Group as any;
const KonvaRect = Rect as any;
const KonvaText = Text as any;

interface NodeRendererProps {
  node: Node;
  selected: boolean;
  onDragMove?: (e: Konva.KonvaEventObject<DragEvent>) => void;
  onDragEnd?: (e: Konva.KonvaEventObject<DragEvent>) => void;
  onClick?: () => void;
  onDblClick?: () => void;
}

// Color mapping for different intents
const INTENT_COLORS = {
  factual: '#E3F2FD', // Light blue
  exploratory: '#F3E5F5', // Light purple
  teleological: '#E8F5E9', // Light green
  instantiation: '#FFF3E0', // Light orange
  miscellaneous: '#F5F5F5' // Light grey
};

// Color mapping for different agents
const AGENT_COLORS = {
  blue_hat: '#E3F2FD',
  white_hat: '#FFFFFF',
  red_hat: '#FFEBEE',
  black_hat: '#212121',
  yellow_hat: '#FFF8E1',
  green_hat: '#E8F5E9'
};

// Badge indicator colors
const BADGE_COLORS = {
  auto: '#3498db', // Blue for auto-generated nodes
  manual: '#2ecc71'  // Green for manually added nodes
};

const NodeRenderer: React.FC<NodeRendererProps> = ({ 
  node, 
  selected,
  onDragMove,
  onDragEnd,
  onClick,
  onDblClick
}) => {
  const metadata = node.metadata as NodeMetadata;
  const intent = metadata?.intent || 'miscellaneous';
  const agent = metadata?.agent || null;
  const tags = metadata?.tags || [];
  const action = metadata?.action || null;
  const isManuallyAdded = metadata?.isManuallyAdded || metadata?.creationSource === 'manual';
  // Get the node path from metadata, or use a default
  const nodePath = metadata?.nodePath || '1.0';

  const nodeStyle = useMemo(() => ({
    shadowBlur: selected ? 10 : 5,
    shadowColor: 'rgba(0,0,0,0.3)',
    shadowOffsetX: 2,
    shadowOffsetY: 2,
    stroke: selected ? '#000000' : '#696969',
    strokeWidth: selected ? 3 : 2,
    cornerRadius: 8,
    // Apply intent/agent color as base
    fill: agent ? AGENT_COLORS[agent] : INTENT_COLORS[intent],
    // Apply dash for manually added nodes
    dash: isManuallyAdded ? [5, 2] : undefined
  }), [selected, intent, agent, isManuallyAdded]);

  return (
    <KonvaGroup
      x={node.x}
      y={node.y}
      draggable
      onDragMove={onDragMove}
      onDragEnd={onDragEnd}
      onClick={onClick}
      onDblClick={onDblClick}
    >
      {/* Main node rectangle */}
      <KonvaRect
        width={node.width}
        height={node.height}
        {...nodeStyle}
        x={-node.width / 2}
        y={-node.height / 2}
      />
      
      {/* Node Path (index) indicator - replaced intent display */}
      <KonvaText
        text={nodePath}
        fontSize={8}
        fontFamily="Arial"
        fill="#666666"
        x={-node.width / 2 + 8}
        y={-node.height / 2 + 8}
        opacity={0.85}
      />
      
      {/* Node title */}
      <KonvaText
        text={node.text}
        width={node.width - 16}
        fontSize={12}
        fontFamily="Arial"
        fill="#000000"
        align="left"
        x={-node.width / 2 + 8}
        y={-node.height / 2 + 30}
      />
      
      {/* Tags */}
      {tags.length > 0 && (
        <KonvaText
          text={tags.join(', ')}
          width={node.width - 16}
          fontSize={8}
          fontFamily="Arial"
          fill="#666666"
          align="left"
          x={-node.width / 2 + 8}
          y={node.height / 2 - 30}
        />
      )}
      
      {/* Manual node badge/indicator */}
      {isManuallyAdded && (
        <>
          {/* Manual node indicator badge */}
          <KonvaRect
            width={10}
            height={10}
            fill={BADGE_COLORS.manual}
            stroke="#27ae60"
            strokeWidth={1}
            cornerRadius={5}
            x={node.width / 2 - 16}
            y={-node.height / 2 + 6}
          />
          
          {/* Manual node label */}
          <KonvaText
            text="Manual"
            fontSize={8}
            fontFamily="Arial"
            fill="#27ae60"
            x={node.width / 2 - 60}
            y={-node.height / 2 + 6}
            opacity={0.9}
          />
        </>
      )}
      
      {/* Action indicator */}
      {action && (
        <KonvaRect
          width={12}
          height={12}
          fill="#FFA726"
          stroke="#F57C00"
          strokeWidth={1}
          cornerRadius={2}
          x={node.width / 2 - 20}
          y={node.height / 2 - 16}
        />
      )}
    </KonvaGroup>
  );
};

export default NodeRenderer; 