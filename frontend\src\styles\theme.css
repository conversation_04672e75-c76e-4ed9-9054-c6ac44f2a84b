/* Theme Variables */
:root {
  /* Colors */
  --color-header: #000000;
  --color-text-primary: #000000;
  --color-text-secondary: #666666;
  --color-text-light: #757575;
  
  /* Background Colors */
  --color-background-primary: #ffffff;
  --color-background-secondary: #f8f9fa;
  --color-background-user-message: #e3f2fd;
  --color-background-llm-message: #f5f5f5;
  
  /* Border Colors */
  --color-border-light: #e0e0e0;
  --color-border-header: rgba(255, 255, 255, 0.1);
  
  /* Interactive Colors */
  --color-primary: #1976d2;
  --color-primary-hover: #1565c0;
  --color-disabled: rgba(0, 0, 0, 0.26);
  
  /* Shadows */
  --shadow-dialog: 0 8px 32px rgba(0, 0, 0, 0.12);
  --shadow-card: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  /* Typography */
  --font-family-primary: Arial, sans-serif;
  --font-size-small: 10px;
  --font-size-regular: 13px;
  --font-size-large: 16px;
  
  /* Spacing */
  --spacing-small: 8px;
  --spacing-medium: 12px;
  --spacing-large: 16px;
  --spacing-xlarge: 20px;
  
  /* Dialog Dimensions */
  --dialog-width: 600px;
  --dialog-height: 400px;
  --dialog-min-height: 200px;
  
  /* Border Radius */
  --border-radius-small: 4px;
  --border-radius-medium: 8px;
  --border-radius-large: 12px;
  
  /* Transitions */
  --transition-quick: 0.2s;
  --transition-medium: 0.3s;
}

/* Common Component Styles */
.dialog-header {
  background-color: var(--color-header);
  color: white;
  padding: var(--spacing-medium) var(--spacing-xlarge);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-large);
  font-weight: bold;
  border-bottom: 1px solid var(--color-border-header);
}

.message {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-regular);
  padding: var(--spacing-medium) var(--spacing-large);
  border-radius: var(--border-radius-medium);
  max-width: 85%;
  line-height: 1.4;
}

.message.user {
  background-color: var(--color-background-user-message);
  align-self: flex-end;
}

.message.llm {
  background-color: var(--color-background-llm-message);
  align-self: flex-start;
}

.input-field {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-regular);
  padding: var(--spacing-medium);
  border: 1px solid var(--color-border-light);
  border-radius: var(--border-radius-small);
  width: 100%;
}

.button {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-regular);
  padding: var(--spacing-medium) var(--spacing-large);
  border-radius: var(--border-radius-small);
  border: none;
  cursor: pointer;
  transition: background-color var(--transition-quick);
}

.button-primary {
  background-color: var(--color-primary);
  color: white;
}

.button-primary:hover {
  background-color: var(--color-primary-hover);
}

/* Dialog Base Styles */
.dialog {
  background-color: var(--color-background-primary);
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-dialog);
  width: var(--dialog-width);
  height: var(--dialog-height);
  min-height: var(--dialog-min-height);
  display: flex;
  flex-direction: column;
  overflow: hidden;
} 