/**
 * z-index.css
 * 
 * This file defines CSS variables for z-index values to ensure consistent stacking across the application.
 * All components should use these variables instead of hardcoded values.
 */

:root {
  /* Base content, canvas elements (0-999) */
  --z-index-canvas: 100;
  --z-index-nodes: 200;
  --z-index-connections: 150;
  --z-index-base-content: 500;
  
  /* Floating UI elements, toolbars (1000-1999) */
  --z-index-toolbar: 1000;
  --z-index-mindsheet-tabs: 1000;
  --z-index-footer: 1000;
  --z-index-context-panel: 1100;
  --z-index-floating-ui: 1500;
  
  /* Dialogs, modals (standard) (2000-2999) */
  --z-index-mindmap-manager: 2000;
  --z-index-node-dialog: 2100;
  --z-index-chatfork: 2500;
  --z-index-standard-dialog: 2900;
  
  /* Critical UI components (always on top) (3000-3999) */
  --z-index-governance-box: 3000;
  --z-index-critical-ui: 3500;
  
  /* Notifications, toasts (4000-4999) */
  --z-index-notifications: 4000;
  --z-index-toasts: 4500;
  
  /* Tooltips, popovers (5000-5999) */
  --z-index-tooltips: 5000;
  --z-index-popovers: 5000;
  --z-index-context-menu: 5500;
  
  /* Fullscreen overlays, loading screens (9000-9999) */
  --z-index-loading-overlay: 9000;
  --z-index-fullscreen-overlay: 9500;
}

/* Usage examples:
.governance-chat-dialog-container {
  z-index: var(--z-index-governance-box);
}

.nodebox-container {
  z-index: var(--z-index-node-dialog);
}

.MuiPopover-root {
  z-index: var(--z-index-popovers) !important;
}
*/
