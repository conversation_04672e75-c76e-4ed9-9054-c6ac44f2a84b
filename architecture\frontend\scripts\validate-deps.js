const depcruise = require('dependency-cruiser');
const path = require('path');

const CRITICAL_PATHS = [
  'src/components/MindMap',
  'src/components/ChatFork',
  'src/core',
  'src/features'
];

async function validateDependencies() {
  const results = await depcruise.cruise([
    'src'
  ], {
    configPath: path.resolve(__dirname, '../dependency-cruiser.config.js'),
    reporterOptions: {
      text: {
        highlightFocused: true
      }
    }
  });

  return results.output;
}

// Add to package.json scripts
module.exports = validateDependencies;