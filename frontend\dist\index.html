<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MindBack - Intelligence Moderation</title>

    <!-- Favicon configuration using MindBack logo -->
    <link rel="icon" href="/assets/MB_logo-hC0R0dzk.jpg" />
    <link rel="icon" type="image/x-icon" href="/assets/MB_logo-hC0R0dzk.jpg" />
    <link rel="icon" type="image/jpeg" href="/assets/MB_logo-hC0R0dzk.jpg" />
    <link rel="apple-touch-icon" href="/assets/MB_logo-hC0R0dzk.jpg" />
    <link rel="shortcut icon" href="/assets/MB_logo-hC0R0dzk.jpg" />
    
    <!-- PWA manifest for mobile -->
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="MindBack - Intelligence Moderation Platform" />
    
    <!-- Additional meta tags for SEO and social sharing -->
    <meta name="author" content="MindBack.ai" />
    <meta name="keywords" content="mindmap, AI, intelligence, moderation, mind mapping, productivity" />
    <meta property="og:title" content="MindBack - Intelligence Moderation" />
    <meta property="og:description" content="An intelligent platform for mind mapping and content moderation" />
    <meta property="og:image" content="/assets/MB_logo-hC0R0dzk.jpg" />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary" />
    <meta name="twitter:title" content="MindBack - Intelligence Moderation" />
    <meta name="twitter:description" content="An intelligent platform for mind mapping and content moderation" />
    <meta name="twitter:image" content="/assets/MB_logo-hC0R0dzk.jpg" />

    <!-- Critical pre-React fixes - must be first -->
    <script src="./Public/pre-react-fix.js"></script>
    <script src="./Public/react-fix-direct.js"></script>
    <script src="./Public/fix-init-react-compat.js"></script>
    <script src="./Public/fix-react-module.js"></script>
    <style>
      /* Define z-index variables inline for critical components */
      :root {
        --z-index-governance-box: 3000;
        --z-index-popovers: 5000;
        --z-index-node-dialog: 2100;
      }

      /* Critical styles to ensure governance box is visible */
      .governance-chat-dialog-container {
        z-index: var(--z-index-governance-box);
        position: fixed !important;
        visibility: visible !important;
      }
    </style>
    <!-- Polyfills for browser compatibility -->
    <script src="/polyfills.js"></script>
    <script src="/module-resolver.js"></script>
    <script src="/events-shim.js"></script>
    <script src="/direct-fix.js"></script>
    <script src="/vite-external-fix.js"></script>
    <script src="/react-compat.js"></script>
    <script src="/react-direct-fix.js"></script>
    <script src="/check-polyfills.js"></script>

    <!-- Additional compatibility fixes -->
    <script src="/react-file.js"></script>
    <script src="/react-direct-fix-1.js"></script>
    <script src="/fix-react-mobile.js"></script>
    <script src="/fix-react-mobile-1.js"></script>
    <script src="/@MUI/file.js"></script>
    <script src="/mobile-reactive.js"></script>
    <script src="/events-site.js"></script>
    <script src="/react-connect.js"></script>
    <script src="/site-padding-fix.js"></script>
    <script src="/check-notify.js"></script>
    <script>
      // Additional polyfills and fixes
      window.global = window;
      window.process = window.process || { env: {} };

      // Fix for events module
      if (!window.events) {
        window.events = {
          EventEmitter: window.EventEmitter || class EventEmitter {
            constructor() { this._events = {}; }
            on(event, listener) {
              if (!this._events[event]) this._events[event] = [];
              this._events[event].push(listener);
              return this;
            }
            emit(event, ...args) {
              if (!this._events[event]) return false;
              this._events[event].forEach(listener => listener.apply(this, args));
              return true;
            }
            once(event, listener) {
              const onceWrapper = (...args) => {
                this.off(event, onceWrapper);
                listener.apply(this, args);
              };
              this.on(event, onceWrapper);
              return this;
            }
            off(event, listener) {
              if (!this._events[event]) return this;
              this._events[event] = this._events[event].filter(l => l !== listener);
              return this;
            }
          }
        };
      }
    </script>
    <script type="module" crossorigin src="/assets/index-v114B1Cq.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-D-qkHQLI.css">
  </head>
  <body>
    <div id="root"></div>

  </body>
</html>