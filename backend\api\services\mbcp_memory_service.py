"""
MBCPMemoryService.py

MBCP-compatible memory service for conversation threading and context management.
Implements Phase 1.1.1 of the Development Plan.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
import json
import os
from dataclasses import dataclass, asdict
from enum import Enum

class MemoryType(Enum):
    CONVERSATION = "conversation"
    CONTEXT = "context"
    EVENT = "event"
    NODE_STATE = "node_state"

@dataclass
class MBCPMemoryEntry:
    """MBCP-compatible memory entry structure"""
    id: str
    type: MemoryType
    timestamp: str
    sheet_id: Optional[str]
    parent_id: Optional[str]  # For conversation threading
    content: Dict[str, Any]
    metadata: Dict[str, Any]
    relevance_score: float = 1.0

@dataclass
class ConversationThread:
    """Conversation thread for ChatFork lineage"""
    thread_id: str
    parent_thread_id: Optional[str]
    sheet_id: str
    messages: List[Dict[str, Any]]
    created_at: str
    last_updated: str
    context_summary: Optional[str] = None

class MBCPMemoryService:
    """
    MBCP-compatible memory service that implements:
    - Conversation threading for ChatFork lineage
    - Parent context retrieval for forked chats
    - MBCP-compatible memory storage format
    """
    
    def __init__(self, storage_dir: str = "backend/memory"):
        self.storage_dir = storage_dir
        self.memory_entries: Dict[str, MBCPMemoryEntry] = {}
        self.conversation_threads: Dict[str, ConversationThread] = {}
        self._ensure_storage_dir()
        self._load_memory()
    
    def _ensure_storage_dir(self):
        """Ensure storage directory exists"""
        os.makedirs(self.storage_dir, exist_ok=True)
    
    def _load_memory(self):
        """Load existing memory from storage"""
        try:
            # Load memory entries
            memory_file = os.path.join(self.storage_dir, "memory_entries.json")
            if os.path.exists(memory_file):
                with open(memory_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for entry_data in data:
                        entry = MBCPMemoryEntry(**entry_data)
                        entry.type = MemoryType(entry.type)  # Convert string back to enum
                        self.memory_entries[entry.id] = entry
            
            # Load conversation threads
            threads_file = os.path.join(self.storage_dir, "conversation_threads.json")
            if os.path.exists(threads_file):
                with open(threads_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for thread_data in data:
                        thread = ConversationThread(**thread_data)
                        self.conversation_threads[thread.thread_id] = thread
                        
        except Exception as e:
            print(f"Error loading memory: {e}")
    
    def _save_memory(self):
        """Save memory to storage"""
        try:
            # Save memory entries
            memory_file = os.path.join(self.storage_dir, "memory_entries.json")
            with open(memory_file, 'w', encoding='utf-8') as f:
                entries_data = []
                for entry in self.memory_entries.values():
                    entry_dict = asdict(entry)
                    entry_dict['type'] = entry.type.value  # Convert enum to string
                    entries_data.append(entry_dict)
                json.dump(entries_data, f, indent=2, ensure_ascii=False)
            
            # Save conversation threads
            threads_file = os.path.join(self.storage_dir, "conversation_threads.json")
            with open(threads_file, 'w', encoding='utf-8') as f:
                threads_data = [asdict(thread) for thread in self.conversation_threads.values()]
                json.dump(threads_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"Error saving memory: {e}")
    
    def store_memory_entry(self, entry: MBCPMemoryEntry) -> bool:
        """Store a memory entry in MBCP-compatible format"""
        try:
            self.memory_entries[entry.id] = entry
            self._save_memory()
            return True
        except Exception as e:
            print(f"Error storing memory entry: {e}")
            return False
    
    def create_conversation_thread(self, sheet_id: str, parent_thread_id: Optional[str] = None) -> str:
        """Create a new conversation thread for ChatFork lineage"""
        # Generate unique thread ID with timestamp + random component to prevent collisions
        import random
        import string
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]  # Include milliseconds
        random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
        thread_id = f"thread_{timestamp}_{random_suffix}_{sheet_id}"
        
        thread = ConversationThread(
            thread_id=thread_id,
            parent_thread_id=parent_thread_id,
            sheet_id=sheet_id,
            messages=[],
            created_at=datetime.now().isoformat(),
            last_updated=datetime.now().isoformat()
        )
        
        self.conversation_threads[thread_id] = thread
        self._save_memory()
        return thread_id
    
    def add_message_to_thread(self, thread_id: str, message: Dict[str, Any]) -> bool:
        """Add a message to a conversation thread"""
        try:
            if thread_id not in self.conversation_threads:
                return False
            
            thread = self.conversation_threads[thread_id]
            thread.messages.append(message)
            thread.last_updated = datetime.now().isoformat()
            
            self._save_memory()
            return True
        except Exception as e:
            print(f"Error adding message to thread: {e}")
            return False
    
    def get_parent_context(self, thread_id: str, depth: int = 3) -> Dict[str, Any]:
        """Retrieve parent context for forked chats"""
        context = {
            "thread_lineage": [],
            "parent_messages": [],
            "context_summary": None
        }
        
        try:
            current_thread = self.conversation_threads.get(thread_id)
            if not current_thread:
                return context
            
            # Build thread lineage
            lineage = []
            current = current_thread
            for _ in range(depth):
                if not current or not current.parent_thread_id:
                    break
                lineage.append(current.thread_id)
                current = self.conversation_threads.get(current.parent_thread_id)
            
            context["thread_lineage"] = lineage
            
            # Get parent messages
            if current_thread.parent_thread_id:
                parent_thread = self.conversation_threads.get(current_thread.parent_thread_id)
                if parent_thread:
                    # Get last few messages from parent thread
                    context["parent_messages"] = parent_thread.messages[-5:]
                    context["context_summary"] = parent_thread.context_summary
            
            return context
        except Exception as e:
            print(f"Error retrieving parent context: {e}")
            return context
    
    def get_memory_entries_by_sheet(self, sheet_id: str, limit: int = 10) -> List[MBCPMemoryEntry]:
        """Get memory entries for a specific sheet"""
        entries = [
            entry for entry in self.memory_entries.values()
            if entry.sheet_id == sheet_id
        ]
        
        # Sort by relevance score and timestamp
        entries.sort(key=lambda x: (x.relevance_score, x.timestamp), reverse=True)
        return entries[:limit]
    
    def get_conversation_thread(self, thread_id: str) -> Optional[ConversationThread]:
        """Get a conversation thread by ID"""
        return self.conversation_threads.get(thread_id)
    
    def update_context_summary(self, thread_id: str, summary: str) -> bool:
        """Update context summary for a thread"""
        try:
            if thread_id in self.conversation_threads:
                self.conversation_threads[thread_id].context_summary = summary
                self.conversation_threads[thread_id].last_updated = datetime.now().isoformat()
                self._save_memory()
                return True
            return False
        except Exception as e:
            print(f"Error updating context summary: {e}")
            return False
    
    def search_memory(self, query: str, sheet_id: Optional[str] = None, limit: int = 5) -> List[MBCPMemoryEntry]:
        """Search memory entries by content"""
        results = []
        query_lower = query.lower()
        
        for entry in self.memory_entries.values():
            if sheet_id and entry.sheet_id != sheet_id:
                continue
            
            # Simple text search in content
            content_str = json.dumps(entry.content).lower()
            if query_lower in content_str:
                results.append(entry)
        
        # Sort by relevance score
        results.sort(key=lambda x: x.relevance_score, reverse=True)
        return results[:limit]

# Global instance
_mbcp_memory_service = None

def get_mbcp_memory_service() -> MBCPMemoryService:
    """Get the global MBCP memory service instance"""
    global _mbcp_memory_service
    if _mbcp_memory_service is None:
        _mbcp_memory_service = MBCPMemoryService()
    return _mbcp_memory_service
