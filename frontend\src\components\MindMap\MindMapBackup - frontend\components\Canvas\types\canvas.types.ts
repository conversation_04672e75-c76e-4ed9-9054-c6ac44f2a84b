import { Node } from '../../../core/models/Node';
import { Connection } from '../../../core/models/Connection';
import { Direction } from '../../../types';

export interface MindMapCanvasProps {
  onNodeSelect?: (nodeId: string | null) => void;
  onNodeUpdate?: (nodeId: string, updates: Partial<Node>) => void;
  onConnectionUpdate?: (connection: Connection) => void;
  width?: number;
  height?: number;
}

export interface CanvasState {
  zoom: number;
  position: { x: number; y: number };
  isDragging: boolean;
  dragStart: { x: number; y: number } | null;
  dragOffset: { x: number; y: number };
}

export interface CanvasRenderingContext {
  ctx: CanvasRenderingContext2D;
  zoom: number;
  position: { x: number; y: number };
  selectedId: string | null;
  draggedNodeId: string | null;
}

export interface NodePosition {
  x: number;
  y: number;
  level: number;
  direction: Direction;
} 