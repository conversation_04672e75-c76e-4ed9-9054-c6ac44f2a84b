/* Main container for the entire application */
.mindback-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  background-color: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Project info bar */
.project-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  z-index: 90;
}

.project-name {
  font-size: 16px;
  font-weight: 500;
  color: #334155;
}

.help-button {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #3b82f6;
  color: white;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.2s;
}

.help-button:hover {
  background-color: #2563eb;
}

/* Main content area */
.mindmap-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  background-color: #f8fafc;
  margin-top: 0;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 50px); /* Adjust for header */
}

/* Governance section */
.governance-section {
  background-color: #f1f5f9;
  border-top: 1px solid #e2e8f0;
  padding: 12px 20px;
  display: flex;
  flex-direction: column;
  height: 250px;
}

.governance-header {
  font-weight: 600;
  color: #334155;
  margin-bottom: 10px;
  font-size: 16px;
}

.governance-messages {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 12px;
  padding: 8px 0;
  max-height: 120px;
  border-radius: 4px;
  background-color: #ffffff;
  border: 1px solid #e2e8f0;
  padding: 12px;
}

.agent-message {
  padding: 8px 12px;
  background-color: #f0f9ff;
  border-radius: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.5;
  color: #0f172a;
  max-width: 85%;
}

.user-message {
  padding: 8px 12px;
  background-color: #e0f2fe;
  border-radius: 8px;
  margin-bottom: 8px;
  margin-left: auto;
  font-size: 14px;
  line-height: 1.5;
  color: #0f172a;
  max-width: 85%;
}

.user-input-form {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.user-input {
  flex: 1;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  resize: none;
  min-height: 38px;
  max-height: 200px;
  font-family: inherit;
  transition: border-color 0.2s;
}

.user-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.send-button {
  padding: 8px 16px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  align-self: flex-end;
  height: 38px;
}

.send-button:hover {
  background-color: #2563eb;
}

.governance-controls {
  display: flex;
  gap: 8px;
}

/* Footer */
.mindback-footer {
  padding: 8px 20px;
  background-color: #f1f5f9;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: center;
}

.about-link {
  background: none;
  border: none;
  color: #6b7280;
  font-size: 14px;
  cursor: pointer;
  transition: color 0.2s;
}

.about-link:hover {
  color: #3b82f6;
  text-decoration: underline;
}

/* Dialogs */
.help-dialog,
.about-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 500px;
  max-width: 90vw;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
}

.help-dialog-header,
.about-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.help-dialog-header h3,
.about-dialog-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1e40af;
}

.help-dialog-header button,
.about-dialog-header button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #64748b;
}

.help-dialog-content,
.about-dialog-content {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.help-dialog-content h4 {
  color: #334155;
  margin-top: 0;
  margin-bottom: 12px;
}

.help-dialog-content ul {
  padding-left: 20px;
  margin-bottom: 20px;
}

.help-dialog-content li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.about-dialog-content p {
  margin: 8px 0;
  line-height: 1.5;
}

/* Canvas */
.mindmap-canvas {
  position: relative;
  width: 100%;
  height: calc(100vh - 200px); /* Adjust for header and agent section */
  overflow: hidden;
  background-color: #fbfbfd;
}

.canvas-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transform-origin: center center;
  will-change: transform;
}

.nodes-layer, 
.connections-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.nodes-layer * {
  pointer-events: auto;
}

.connections-layer * {
  pointer-events: auto;
}

/* Connection Styles */
.connection {
  pointer-events: all;
}

.connection path {
  cursor: pointer;
  transition: stroke-width 0.2s;
}

.connection path:hover {
  stroke-width: 3px;
}

.connection text {
  pointer-events: none;
  user-select: none;
}

/* Node */
.mindmap-node {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  color: #ffffff;
  font-weight: 500;
  padding: 8px;
  width: 140px;
  height: 60px;
  transform: translate(-50%, -50%); /* Center on its position */
  cursor: grab;
  user-select: none;
  pointer-events: all;
  z-index: 10;
  word-break: break-word;
  overflow: hidden;
}

.mindmap-node.selected {
  box-shadow: 0 0 0 2px #3b82f6, 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Node Dialog */
.node-dialog {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #ffffff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  width: 90vw;  /* 90% of viewport width */
  max-width: 1200px;  /* Maximum width */
  height: 90vh;  /* 90% of viewport height */
  max-height: 800px;  /* Maximum height */
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.node-dialog textarea {
  width: 100%;
  flex: 1;
  min-height: 80px;
  margin-bottom: 16px;
  padding: 8px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  font-family: inherit;
  resize: none;
}

.node-dialog-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.node-dialog button {
  padding: 6px 12px;
  border-radius: 4px;
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  cursor: pointer;
}

.node-dialog button.primary {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.node-dialog button:hover {
  opacity: 0.9;
}

/* Connection Dialog */
.connection-dialog {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  width: 400px;
}

/* Grabbing cursor for dragging */
.grabbing {
  cursor: grabbing !important;
}

/* Node Content Styles */
.mindmap-node-content {
  max-height: 100%;
  overflow: hidden;
  width: 100%;
}

/* Node Shape Variations */
.mindmap-node.rectangle {
  border-radius: 0;
}

.mindmap-node.rounded-rectangle {
  border-radius: 8px;
}

.mindmap-node.ellipse {
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 100px;
  min-height: 60px;
}

/* Hat Contribution Indicators */
.hat-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 4px;
}

.hat-contributions {
  display: flex;
  margin-top: 4px;
  justify-content: flex-start;
  gap: 2px;
}

/* MindMap Container */
.mindmap-container {
  position: fixed;
  top: 0; /* Start from the very top */
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #f8fafc;
}

/* MindMap Canvas */
.mindmap-canvas {
  position: relative;
  flex: 1;
  overflow: hidden;
  cursor: grab;
}

.mindmap-canvas:active {
  cursor: grabbing;
}

/* Connection Layer */
.connections-layer {
  pointer-events: none;
}

.connections-layer * {
  pointer-events: auto;
}

/* MindMap Controls */
.mindmap-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 200px;
}

.mindmap-controls-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  border-radius: 4px 4px 0 0;
}

.mindmap-controls-header button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
}

.mindmap-controls-content {
  padding: 12px;
}

.control-section {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.control-button {
  background-color: white;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.control-button:hover {
  background-color: #f8f9fa;
}

.settings-panel {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e9ecef;
}

.settings-panel h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #495057;
}

.settings-button {
  width: 100%;
  padding: 8px 16px;
  background-color: #4B9BFF;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.settings-button:hover {
  background-color: #3b82f6;
}

.mindback-header {
  height: 40px;
  background-color: #1a237e; /* Royal blue color to match the image */
  color: white;
  display: flex;
  align-items: center;
  padding: 0 1rem;
  box-shadow: none;
}

.header-title {
  font-size: 1.25rem;
  font-weight: 400;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.mindmap-content {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
}

.governance-agent-section {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-top: 1px solid #e2e8f0;
  padding: 1rem;
  height: 200px;
  min-height: 200px;
  max-height: 30vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: #f7f9fc;
}

.governance-agent-section h2 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem;
}

.agent-message {
  background-color: #f1f5f9;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  color: #1e293b;
}

.input-form {
  display: flex;
  gap: 0.5rem;
}

.input-field {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.2s;
}

.input-field:focus {
  border-color: #3b82f6;
}

.send-button {
  padding: 0.75rem 1.5rem;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.send-button:hover {
  background-color: #2563eb;
}

.send-button:active {
  background-color: #1d4ed8;
}

/* LLM Settings */
.llm-settings {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 10px;
}

.llm-model-select {
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  background-color: #f8fafc;
  font-size: 14px;
  flex-grow: 1;
}

.llm-settings label {
  font-size: 14px;
  color: #4b5563;
  min-width: 50px;
}

/* Add these styles for the mindmap stats in controls */
.mindmap-stats-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 12px;
}

.mindmap-stat-item {
  display: flex;
  justify-content: space-between;
  padding: 4px 8px;
  background-color: #edf2f7;
  border-radius: 4px;
  font-size: 14px;
}

.stat-label {
  color: #4a5568;
  font-weight: 500;
}

.stat-value {
  font-weight: 600;
  color: #2b6cb0;
}

/* Style for the node path number */
.node-path-number {
  position: absolute;
  top: 4px;
  left: 4px;
  font-size: 12px;
  font-family: inherit;
  font-weight: bold;
  color: inherit;
  padding: 1px 4px;
  z-index: 10;
  opacity: 0.85;
  pointer-events: none;
}

/* Remove the hover-based visibility since we want them always visible */
.mindmap-node:hover .node-path-number {
  opacity: 0.85;
  font-weight: bold;
} 