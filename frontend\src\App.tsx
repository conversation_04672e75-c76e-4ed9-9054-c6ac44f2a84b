/**
 * @deprecated This file is deprecated. Please use AppRefactored.tsx instead.
 * This file is kept for reference purposes only and to maintain any existing imports.
 * All functionality has been moved to AppRefactored.tsx.
 */

import React, { useEffect } from 'react';
import { ChatForkAdapter } from './components/MindMap/core/adapters/ChatForkAdapter';

const App: React.FC = () => {
  console.warn('App.tsx is deprecated. Please use AppRefactored.tsx instead.');

  // Initialize the ChatFork global key handler
  useEffect(() => {
    ChatForkAdapter.initializeGlobalKeyHandler();
    console.log('Initialized ChatFork global key handler');
  }, []);

  return null;
};

export default App; 