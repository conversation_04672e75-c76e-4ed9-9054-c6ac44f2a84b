import React, { useEffect, useState, useRef } from 'react';
import { ChatForkConnectionData, ChatForkNodeData } from './index.tsx';
import './ChatFork.css';

interface ChatForkConnectionProps {
  connection: ChatForkConnectionData;
  nodes: ChatForkNodeData[];
}

interface Point {
  x: number;
  y: number;
}

const ChatForkConnection: React.FC<ChatForkConnectionProps> = ({ connection, nodes }) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [points, setPoints] = useState<{ start: Point; end: Point } | null>(null);

  useEffect(() => {
    const calculatePoints = () => {
      const fromNode = document.querySelector(`[data-id="${connection.fromId}"]`) as HTMLElement;
      const toNode = document.querySelector(`[data-id="${connection.toId}"]`) as HTMLElement;
      
      if (!fromNode || !toNode) return null;
      
      const fromRect = fromNode.getBoundingClientRect();
      const toRect = toNode.getBoundingClientRect();
      
      // Get container position for relative coordinates
      const container = fromNode.closest('.chat-fork-container') as HTMLElement;
      const containerRect = container?.getBoundingClientRect() || { left: 0, top: 0 };
      
      // Calculate center points of each node
      const start = {
        x: fromRect.left + fromRect.width / 2 - containerRect.left,
        y: fromRect.top + fromRect.height / 2 - containerRect.top
      };
      
      const end = {
        x: toRect.left + toRect.width / 2 - containerRect.left,
        y: toRect.top + toRect.height / 2 - containerRect.top
      };
      
      return { start, end };
    };
    
    // Initial calculation
    setPoints(calculatePoints());
    
    // Recalculate on window resize
    const handleResize = () => {
      setPoints(calculatePoints());
    };
    
    window.addEventListener('resize', handleResize);
    
    // Set up a periodic check for node position changes
    const interval = setInterval(() => {
      setPoints(calculatePoints());
    }, 500);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      clearInterval(interval);
    };
  }, [connection, nodes]);
  
  if (!points) return null;
  
  // Calculate SVG dimensions and position
  const padding = 20; // Extra space around the connection
  const minX = Math.min(points.start.x, points.end.x) - padding;
  const minY = Math.min(points.start.y, points.end.y) - padding;
  const width = Math.abs(points.end.x - points.start.x) + padding * 2;
  const height = Math.abs(points.end.y - points.start.y) + padding * 2;
  
  // Adjust points to be relative to the SVG
  const adjustedStart = {
    x: points.start.x - minX,
    y: points.start.y - minY
  };
  
  const adjustedEnd = {
    x: points.end.x - minX,
    y: points.end.y - minY
  };
  
  // Create a curved path
  const curveOffset = 30; // How much the curve should bend
  const path = `
    M ${adjustedStart.x} ${adjustedStart.y}
    C ${adjustedStart.x + curveOffset} ${adjustedStart.y + curveOffset},
      ${adjustedEnd.x - curveOffset} ${adjustedEnd.y - curveOffset},
      ${adjustedEnd.x} ${adjustedEnd.y}
  `;
  
  // Calculate arrow points
  const arrowSize = 10;
  const angle = Math.atan2(adjustedEnd.y - adjustedStart.y, adjustedEnd.x - adjustedStart.x);
  const arrowPoint1 = {
    x: adjustedEnd.x - arrowSize * Math.cos(angle - Math.PI / 6),
    y: adjustedEnd.y - arrowSize * Math.sin(angle - Math.PI / 6)
  };
  const arrowPoint2 = {
    x: adjustedEnd.x - arrowSize * Math.cos(angle + Math.PI / 6),
    y: adjustedEnd.y - arrowSize * Math.sin(angle + Math.PI / 6)
  };
  
  const arrowPath = `
    M ${adjustedEnd.x} ${adjustedEnd.y}
    L ${arrowPoint1.x} ${arrowPoint1.y}
    L ${arrowPoint2.x} ${arrowPoint2.y}
    Z
  `;

  return (
    <svg
      ref={svgRef}
      className="chat-fork-connection"
      width={width}
      height={height}
      style={{
        position: 'absolute',
        left: minX,
        top: minY,
        pointerEvents: 'none', // Allow clicks to pass through
        zIndex: -1 // Place behind nodes
      }}
    >
      <path
        d={path}
        fill="none"
        stroke="#0066cc"
        strokeWidth="2"
        strokeDasharray="none"
      />
      <path
        d={arrowPath}
        fill="#0066cc"
        stroke="none"
      />
    </svg>
  );
};

export default ChatForkConnection; 