import { create } from 'zustand';
import { ChatResponse } from '../../services/api/GovernanceLLM';

interface ChatForkState {
  // State
  content: ChatResponse | null;
  isVisible: boolean;
  selectedText: string | null;
  selectionPosition: { x: number, y: number } | null;
  activeSheetId: string | null;

  // Actions
  showChatFork: (content?: ChatResponse, sheetId?: string) => void;
  hideChatFork: () => void;
  setSelectedText: (text: string | null, position: { x: number, y: number } | null) => void;
  clearSelection: () => void;
  setActiveSheetId: (sheetId: string | null) => void;
}

/**
 * ChatForkStore - Centralized state management for the canvas-based ChatFork
 * This store manages the visibility, content, and selection state of the ChatFork
 */
export const useChatForkStore = create<ChatForkState>((set) => ({
  // Initial state
  content: null,
  isVisible: false,
  selectedText: null,
  selectionPosition: null,
  activeSheetId: null,

  // Actions
  showChatFork: (content, sheetId) => {
    console.log('ChatForkStore: Showing ChatFork with content:', content);

    // Fail hard if no content is provided
    if (!content) {
      console.error('ChatForkStore: No content provided for ChatFork');
      throw new Error('No content provided for ChatFork - backend data is required');
    }

    set({
      content: content,
      isVisible: true,
      // Reset selection when showing new content
      selectedText: null,
      selectionPosition: null,
      // Set the active sheet ID if provided
      ...(sheetId ? { activeSheetId: sheetId } : {})
    });
  },

  hideChatFork: () => {
    console.log('ChatForkStore: Hiding ChatFork');
    set({
      isVisible: false,
      // Optionally clear content when hiding
      content: null,
      selectedText: null,
      selectionPosition: null
    });
  },

  setSelectedText: (text, position) => {
    console.log('ChatForkStore: Setting selected text:', text);
    set({
      selectedText: text,
      selectionPosition: position
    });
  },

  clearSelection: () => {
    console.log('ChatForkStore: Clearing selection');
    set({
      selectedText: null,
      selectionPosition: null
    });
  },

  setActiveSheetId: (sheetId) => {
    console.log('ChatForkStore: Setting active sheet ID:', sheetId);
    set({ activeSheetId: sheetId });
  }
}));
