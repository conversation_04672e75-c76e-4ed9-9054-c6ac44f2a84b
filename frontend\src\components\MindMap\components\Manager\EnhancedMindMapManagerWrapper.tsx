/**
 * EnhancedMindMapManagerWrapper Component
 *
 * A wrapper component that handles all store access for EnhancedMindMapManager.
 * This component ensures that Zustand store hooks are called at the top level
 * and passes down store references as props to the EnhancedMindMapManager component.
 */

import React from 'react';
import { EnhancedMindMapManager } from './EnhancedMindMapManager';
import { getMindMapStore } from '../../../../core/state/MindMapStoreFactory';

interface EnhancedMindMapManagerWrapperProps {
  open: boolean;
  onClose: () => void;
  sheetId: string;
}

const EnhancedMindMapManagerWrapper: React.FC<EnhancedMindMapManagerWrapperProps> = (props) => {
  // Get the sheet-specific store
  const store = getMindMapStore(props.sheetId);
  
  // Pass down all props and store references to the EnhancedMindMapManager component
  return (
    <EnhancedMindMapManager
      {...props}
      store={store}
    />
  );
};

export default EnhancedMindMapManagerWrapper;
