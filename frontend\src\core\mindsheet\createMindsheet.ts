import { useMindBookStore } from '../state/MindBookStore';
import RegistrationManager, { EventType } from '../services/RegistrationManager';
import { initializeMindMap } from '../adapters/MindMapAdapter';

/**
 * Creates a new mindsheet with the given title and data
 *
 * @param title The title of the mindsheet
 * @param data The data to initialize the mindsheet with
 * @returns The ID of the created mindsheet, or null if creation failed
 */
export const createMindsheet = (title: string, data: any): string | null => {
  try {
    console.log('createMindsheet: Creating mindsheet with title:', title);

    // Get the MindBookStore
    const mindBookStore = useMindBookStore.getState();

    // Create the mindsheet
    const sheetId = mindBookStore.createMindMapSheet(title, data);
    console.log('createMindsheet: Created mindsheet with ID:', sheetId);

    // Register the event
    RegistrationManager.registerEvent(EventType.MINDSHEET_CREATED, {
      id: sheetId,
      type: 'mindmap',
      title: title
    });

    // Set as active sheet
    mindBookStore.setActiveSheet(sheetId);
    console.log('createMindsheet: Set active sheet to:', sheetId);

    // Initialize the mindmap
    const success = initializeMindMap(data);
    console.log('createMindsheet: Initialized mindmap:', success);

    if (!success) {
      console.error('createMindsheet: Failed to initialize mindmap');
      return null;
    }

    return sheetId;
  } catch (error) {
    console.error('createMindsheet: Error creating mindsheet:', error);
    return null;
  }
};
