/**
 * MainRouter.ts
 *
 * Central routing component for the MindBack application.
 * Handles routing of user interactions and LLM responses based on intent type and context.
 *
 * See docs/frontend_MainRouter_implementation.md for detailed documentation.
 */

import { ResponseType } from '../../services/api/GovernanceLLM';

// Types of contexts where routing decisions can be made
export type SourceContext = 'govbox' | 'mindsheet';

// Routing decision result
export interface RoutingDecision {
  // The action to take
  action: 'display_in_govbox' | 'create_mindsheet' | 'extend_mindsheet';
  // The type of content (factual, exploratory, teleological, etc.)
  contentType: string;
  // Whether to use ChatFork visualization
  useChatFork: boolean;
  // Whether to use Mindmap visualization
  useMindmap: boolean;
  // The data to use for the action
  data: any;
  // For mindsheet actions, the type of mindsheet to create
  sheetType?: string;
  // For extend_mindsheet actions, the ID of the sheet to extend
  sheetId?: string;
  // The text to display (prioritized based on intent)
  displayText: string;
  // Additional metadata for the action
  metadata?: any;
}

// Context for routing decisions
export interface RoutingContext {
  // Whether this is a manual selection or automatic detection
  isManualSelection: boolean;
  // The context where the routing decision is being made
  sourceContext: SourceContext;
  // For mindsheet contexts, the ID of the existing sheet
  existingSheetId?: string;
}

/**
 * MainRouter class
 *
 * Central component for routing decisions in the MindBack application.
 */
export class MainRouter {
  /**
   * Route an intent to the appropriate action
   *
   * @param intent The intent to route
   * @param responseData The response data from the LLM
   * @param context The context for the routing decision
   * @returns A routing decision
   */
  static routeIntent(
    intent: string,
    responseData: any,
    context: RoutingContext
  ): RoutingDecision {
    console.log('MainRouter: Routing intent', intent, 'in context', context);
    console.log('MainRouter: Response data', responseData);

    // Extract response type information
    const responseType: ResponseType = responseData.responseType || {
      type: intent,
      requiresMindmap: intent === 'teleological',
      requiresChatFork: intent === 'exploratory'
    };

    // Extract text fields from response data
    const text = responseData.text || '';
    const description = responseData.description || '';
    const fullText = responseData.full_text || '';

    // Determine the appropriate display text based on intent
    let displayText = '';
    if (intent === 'factual') {
      // For factual intents, prioritize description (the answer) over text
      // This is because OpenAI puts the actual answer in the description field
      console.log('MainRouter.routeIntent - Factual intent detected, available fields:', {
        description,
        text,
        fullText,
        'responseData.description': responseData.description,
        'responseData.text': responseData.text,
        'responseData.content?.description': responseData.content?.description,
        'responseData.content?.text': responseData.content?.text
      });

      // DEBUGGING: Log the entire responseData object
      console.log('MainRouter.routeIntent - FULL responseData:', JSON.stringify(responseData, null, 2));

      displayText = description || text || fullText;
      console.log('MainRouter: Using description field for factual intent:', displayText);
    } else {
      // For other intents, description is often more appropriate
      displayText = description || text || fullText;
      console.log('MainRouter: Using description field for non-factual intent:', displayText);
    }

    // Handle factual intents
    if (intent === 'factual') {
      return {
        action: 'display_in_govbox',
        contentType: 'factual',
        useChatFork: false,
        useMindmap: false,
        displayText,
        data: responseData
      };
    }

    // Handle exploratory intents (chatfork)
    if (intent === 'exploratory' || intent === 'chatfork') {
      // If we're in a mindsheet context and extending, use that sheet
      if (context.sourceContext === 'mindsheet' && context.existingSheetId) {
        return {
          action: 'extend_mindsheet',
          contentType: 'exploratory',
          useChatFork: true,
          useMindmap: false,
          displayText,
          data: responseData,
          sheetType: 'chatfork',
          sheetId: context.existingSheetId
        };
      }

      // Otherwise create a new mindsheet
      return {
        action: 'create_mindsheet',
        contentType: 'exploratory',
        useChatFork: true,
        useMindmap: false,
        displayText,
        data: responseData,
        sheetType: 'chatfork'
      };
    }

    // Handle teleological intents (mindmap)
    if (intent === 'teleological' || intent === 'mindmap') {
      // If we're in a mindsheet context and extending, use that sheet
      if (context.sourceContext === 'mindsheet' && context.existingSheetId) {
        return {
          action: 'extend_mindsheet',
          contentType: 'teleological',
          useChatFork: false,
          useMindmap: true,
          displayText,
          data: responseData,
          sheetType: 'mindmap',
          sheetId: context.existingSheetId
        };
      }

      // For teleological intents from the govbox, display in govbox and let the button handle creation
      // This is for the case when teleological intent is detected during conversation
      if (context.sourceContext === 'govbox' && !context.isManualSelection) {
        console.log('MainRouter: Teleological intent from govbox conversation - displaying in govbox only');
        return {
          action: 'display_in_govbox',
          contentType: 'teleological',
          useChatFork: false,
          useMindmap: true, // Flag that this could be a mindmap, but don't create it yet
          displayText,
          data: responseData
        };
      }

      // For manual selection or other contexts, create a new mindsheet
      return {
        action: 'create_mindsheet',
        contentType: 'teleological',
        useChatFork: false,
        useMindmap: true,
        displayText,
        data: responseData,
        sheetType: 'mindmap'
      };
    }

    // Handle template-based intents
    if (['situational', 'backtrack', 'bmc', 'swot', 'triz', 'five_forces',
         'pestel', 'ansoff', 'value_prop', 'jtbd'].includes(intent)) {
      return {
        action: 'create_mindsheet',
        contentType: intent,
        useChatFork: false,
        useMindmap: false,
        displayText,
        data: responseData,
        sheetType: intent
      };
    }

    // Default fallback
    return {
      action: 'display_in_govbox',
      contentType: 'miscellaneous',
      useChatFork: false,
      useMindmap: false,
      displayText,
      data: responseData
    };
  }

  /**
   * Process a response from the LLM
   *
   * @param response The response from the LLM
   * @param context The context for the routing decision
   * @returns A routing decision
   */
  static processResponse(response: any, context: RoutingContext): RoutingDecision {
    console.log('MainRouter.processResponse - Raw response:', response);
    console.log('MainRouter.processResponse - Response structure:', {
      'response.content?.intent': response.content?.intent,
      'response.intent': response.intent,
      'response.content?.text': response.content?.text,
      'response.text': response.text,
      'response.content?.description': response.content?.description,
      'response.description': response.description
    });

    // DEBUGGING: Log the entire response object as JSON
    console.log('MainRouter.processResponse - FULL response JSON:', JSON.stringify(response, null, 2));

    // Extract intent from response
    const intent = response.content?.intent ||
                  response.intent ||
                  (context.isManualSelection ? context.sourceContext : 'factual');

    console.log('MainRouter.processResponse - Detected intent:', intent);

    // Route the intent
    const decision = this.routeIntent(intent, response, context);
    console.log('MainRouter.processResponse - Routing decision:', decision);

    return decision;
  }

  /**
   * Handle a mindsheet extension
   *
   * @param intent The intent to route
   * @param responseData The response data from the LLM
   * @param sheetId The ID of the sheet to extend
   * @returns A routing decision
   */
  static handleMindsheetExtension(
    intent: string,
    responseData: any,
    sheetId: string
  ): RoutingDecision {
    // Route with mindsheet context
    return this.routeIntent(intent, responseData, {
      isManualSelection: false,
      sourceContext: 'mindsheet',
      existingSheetId: sheetId
    });
  }

  /**
   * Get the appropriate display text for a response based on intent
   *
   * @param response The response data
   * @returns The appropriate display text
   */
  static getDisplayText(response: any): string {
    const intent = response.content?.intent || response.intent || 'factual';

    if (intent === 'factual') {
      // For factual intents, prioritize description (the answer) over text
      // This is because OpenAI puts the actual answer in the description field
      return response.content?.description ||
             response.description ||
             response.content?.text ||
             response.text ||
             'No response text available';
    } else {
      // For other intents, description is often more appropriate
      return response.content?.description ||
             response.description ||
             response.content?.text ||
             response.text ||
             'No response text available';
    }
  }
}
