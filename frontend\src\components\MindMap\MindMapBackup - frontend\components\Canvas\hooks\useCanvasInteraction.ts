import { useCallback, useRef } from 'react';
import { Node } from '../../../core/models/Node';
import { handleTabKeyPress } from '../../../core/operations/NodeOperations';
import { useMindMapStore } from '../../../core/state/MindMapStore';

export const useCanvasInteraction = (
  canvasState: any,
  onNodeSelect?: (nodeId: string | null) => void
) => {
  const {
    zoom,
    position,
    updateZoom,
    updatePosition,
    startDragging,
    stopDragging,
    updateDragOffset,
    nodes, // This is an array of nodes from Object.values(nodes)
    nodesObj, // This is the original nodes object from the store
    selectedId,
    selectNode,
    updateNode
  } = canvasState;

  // Store the last click time in a ref to persist between renders
  const lastClickTimeRef = useRef<number>(0);
  const DOUBLE_CLICK_THRESHOLD = 300; // ms

  const handleWheel = useCallback((e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY * -0.01;
    const newZoom = Math.min(Math.max(zoom + delta, 0.1), 2);
    updateZoom(newZoom);
  }, [zoom, updateZoom]);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    console.log('DEBUG: handleMouseDown called');
    const rect = (e.target as HTMLCanvasElement).getBoundingClientRect();
    const x = (e.clientX - rect.left) / zoom - position.x;
    const y = (e.clientY - rect.top) / zoom - position.y;

    console.log('DEBUG: Canvas click at:', { x, y });
    console.log('DEBUG: Available nodes (array):', nodes.length);
    console.log('DEBUG: Available nodes (object):', Object.keys(nodesObj || {}).length);

    // Check if clicked on a node
    const clickedNode = nodes.find(node => {
      const nodeX = node.x - 50;
      const nodeY = node.y - 25;
      const isClicked = x >= nodeX && x <= nodeX + 100 && y >= nodeY && y <= nodeY + 50;
      
      if (isClicked) {
        console.log('DEBUG: Clicked on node:', node);
      }
      
      return isClicked;
    });

    if (clickedNode) {
      // Check for double click
      const now = Date.now();
      const timeSinceLastClick = now - lastClickTimeRef.current;
      const isDoubleClick = timeSinceLastClick < DOUBLE_CLICK_THRESHOLD;
      console.log('DEBUG: Time since last click:', timeSinceLastClick, 'ms');
      console.log('DEBUG: Is double click?', isDoubleClick);
      
      lastClickTimeRef.current = now;

      // Verify the node exists in the store
      console.log('DEBUG: Checking if node exists in store:', clickedNode.id);
      console.log('DEBUG: Node in store:', nodesObj && nodesObj[clickedNode.id] ? 'Yes' : 'No');

      // Select the node
      console.log('DEBUG: Selecting node:', clickedNode.id);
      if (onNodeSelect) {
        console.log('DEBUG: Calling onNodeSelect with:', clickedNode.id);
        onNodeSelect(clickedNode.id);
      } else {
        console.warn('DEBUG: onNodeSelect is not defined!');
      }

      // If double-clicked, ensure the node is selected in the store with isEditing=true
      if (isDoubleClick) {
        console.log('DEBUG: Double-clicked on node:', clickedNode.id);
        
        // Directly update the MindMapStore for testing
        console.log('DEBUG: Directly updating MindMapStore selectedNodeId with isEditing=true');
        
        // First, make sure the node exists in the store's nodes object
        if (nodesObj && !nodesObj[clickedNode.id]) {
          console.warn('DEBUG: Node not found in store, adding it');
          const updatedNodes = { ...nodesObj };
          updatedNodes[clickedNode.id] = clickedNode;
          useMindMapStore.setState({ nodes: updatedNodes });
        }
        
        // Then set it as selected with isEditing=true
        useMindMapStore.setState({ selectedNodeId: clickedNode.id, isEditing: true });
        
        // Verify the update
        setTimeout(() => {
          const state = useMindMapStore.getState();
          console.log('DEBUG: Store state after update:', {
            selectedNodeId: state.selectedNodeId,
            isEditing: state.isEditing,
            nodeExists: state.nodes[clickedNode.id] ? true : false
          });
        }, 0);
      } else {
        // Single click - just select without editing
        useMindMapStore.setState({ selectedNodeId: clickedNode.id, isEditing: false });
      }
    } else {
      console.log('DEBUG: No node clicked, starting canvas drag');
      startDragging(e.clientX, e.clientY);
      if (onNodeSelect) {
        console.log('DEBUG: Deselecting node');
        onNodeSelect(null);
      }
    }
  }, [zoom, position, nodes, nodesObj, onNodeSelect, startDragging]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!canvasState.isDragging) return;

    const deltaX = e.clientX - (canvasState.dragStart?.x || 0);
    const deltaY = e.clientY - (canvasState.dragStart?.y || 0);

    updatePosition(
      position.x + deltaX / zoom,
      position.y + deltaY / zoom
    );
    updateDragOffset(deltaX, deltaY);
  }, [canvasState, zoom, position, updatePosition, updateDragOffset]);

  const handleMouseUp = useCallback(() => {
    stopDragging();
  }, [stopDragging]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    console.log('DEBUG: handleKeyDown called, key:', e.key);
    console.log('DEBUG: selectedId:', selectedId);
    
    // Handle Tab key for adding child nodes
    if (e.key === 'Tab' && selectedId) {
      e.preventDefault();
      console.log('DEBUG: Tab key pressed, adding child node');
      handleTabKeyPress(selectedId, e.shiftKey);
    }

    // Handle Enter key to open node dialog
    if (e.key === 'Enter' && selectedId) {
      e.preventDefault();
      console.log('DEBUG: Enter pressed on selected node:', selectedId);
      
      // Verify the node exists in the store
      console.log('DEBUG: Checking if node exists in store:', selectedId);
      console.log('DEBUG: Node in store:', nodesObj && nodesObj[selectedId] ? 'Yes' : 'No');
      
      // Ensure the node is selected in the store with isEditing=true
      if (onNodeSelect) {
        console.log('DEBUG: Calling onNodeSelect for Enter key with:', selectedId);
        onNodeSelect(selectedId);
        
        // Directly update the MindMapStore for testing
        console.log('DEBUG: Directly updating MindMapStore selectedNodeId with isEditing=true');
        useMindMapStore.setState({ selectedNodeId: selectedId, isEditing: true });
        
        // Verify the update
        setTimeout(() => {
          const state = useMindMapStore.getState();
          console.log('DEBUG: Store state after update:', {
            selectedNodeId: state.selectedNodeId,
            isEditing: state.isEditing,
            nodeExists: state.nodes[selectedId] ? true : false
          });
        }, 0);
      } else {
        console.warn('DEBUG: onNodeSelect is not defined!');
      }
    }
  }, [selectedId, nodesObj, onNodeSelect]);

  return {
    handleWheel,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleKeyDown
  };
}; 