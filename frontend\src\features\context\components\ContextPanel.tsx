/**
 * ContextPanel.tsx
 * 
 * Context Settings Panel - now integrated with ContextStore
 * Manages foundational, strategic, and operational context settings
 * that can be associated with MindBooks.
 */

import React, { useState, useEffect } from 'react';
import { useContextStore, ContextLevel, ContextInputType } from '../store/ContextStore';
import { useMindBookStore } from '../../../core/state/MindBookStore';
import { setContextSettings, autoSaveSession, saveMindBook } from '../../../core/services/MindBookPersistenceService';
import './ContextPanel.css';

interface ContextPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

const ContextPanel: React.FC<ContextPanelProps> = ({ isOpen, onClose }) => {
  const {
    currentContextSettings,
    activeLevel,
    setActiveLevel,
    updateContextInput,
    setActiveInputType,
    createNewContextSettings,
    saveContextSettings,
    getContextSettingsList,
    loadContextSettings
  } = useContextStore();

  // MindBook store for project association
  const mindBookStore = useMindBookStore();

  // Local UI state
  const [showNewContextDialog, setShowNewContextDialog] = useState(false);
  const [showLoadContextDialog, setShowLoadContextDialog] = useState(false);
  const [newContextName, setNewContextName] = useState('');
  const [newContextDescription, setNewContextDescription] = useState('');

  // Get current level data
  const currentLevelData = currentContextSettings?.[activeLevel];
  const activeInputType = currentLevelData?.activeInputType || 'text';
  const currentInput = currentLevelData?.inputs.find(input => input.type === activeInputType);

  // Simple context association - just trigger auto-save without forcing naming
  const associateContextWithProject = (contextSettingsId: string, contextName: string) => {
    try {
      // The context is already loaded in the ContextStore at this point
      // Just trigger an auto-save to capture the current state with the loaded context
      autoSaveSession();
      
      console.log('ContextPanel: Associated context with current project:', contextName);
    } catch (error) {
      console.error('ContextPanel: Failed to associate context with project:', error);
    }
  };

  // Handle input changes with auto-save
  const handleInputChange = (content: string) => {
    if (currentContextSettings) {
      updateContextInput(activeLevel, activeInputType, content);
      
      // Auto-save context settings
      saveContextSettings(currentContextSettings);
      
      // Auto-save to project
      associateContextWithProject(currentContextSettings.id, currentContextSettings.name);
    }
  };

  // Handle tab changes
  const handleTabChange = (inputType: ContextInputType) => {
    if (currentContextSettings) {
      setActiveInputType(activeLevel, inputType);
      
      // Auto-save context settings
      saveContextSettings(currentContextSettings);
      
      // Auto-save to project
      associateContextWithProject(currentContextSettings.id, currentContextSettings.name);
    }
  };

  // Handle saving (explicit save button)
  const handleSave = () => {
    if (currentContextSettings) {
      const success = saveContextSettings(currentContextSettings);
      if (success) {
        // Auto-save to project
        associateContextWithProject(currentContextSettings.id, currentContextSettings.name);
        console.log('ContextPanel: Context settings saved successfully and associated with project');
      }
    }
  };

  // Handle level changes with auto-save
  const handleLevelChange = (level: ContextLevel) => {
    setActiveLevel(level);
    
    if (currentContextSettings) {
      // Auto-save context settings
      saveContextSettings(currentContextSettings);
      
      // Auto-save to project
      associateContextWithProject(currentContextSettings.id, currentContextSettings.name);
    }
  };

  // Handle closing with auto-save
  const handleClose = () => {
    if (currentContextSettings) {
      // Final auto-save when closing
      saveContextSettings(currentContextSettings);
      associateContextWithProject(currentContextSettings.id, currentContextSettings.name);
    }
    onClose();
  };

  // Handle creating new context settings
  const handleCreateNew = () => {
    if (newContextName.trim()) {
      const newId = createNewContextSettings(newContextName.trim(), newContextDescription.trim());
      if (newId) {
        setShowNewContextDialog(false);
        setNewContextName('');
        setNewContextDescription('');
        
        // Auto-save to project
        associateContextWithProject(newId, newContextName.trim());
        
        console.log('ContextPanel: Created new context settings and associated with project:', newId);
      }
    }
  };

  // Handle loading context settings
  const handleLoadContextSettings = (settingsId: string) => {
    const success = loadContextSettings(settingsId);
    if (success) {
      // Get the context settings name for auto-save
      const contextSettings = getContextSettingsList().find(cs => cs.id === settingsId);
      if (contextSettings) {
        // Auto-save to project
        associateContextWithProject(settingsId, contextSettings.name);
      }
      
      console.log('ContextPanel: Successfully loaded context settings and associated with project:', settingsId);
    } else {
      console.error('ContextPanel: Failed to load context settings:', settingsId);
    }
    setShowLoadContextDialog(false);
  };

  return (
    <div className={`context-panel ${isOpen ? 'open' : ''}`}>
      <div className="context-panel-header">
        <div className="context-panel-title">
          Context Settings
          {currentContextSettings && (
            <span className="context-name"> - {currentContextSettings.name}</span>
          )}
        </div>
        <div className="context-panel-actions">
          <button
            className="context-action-button small"
            onClick={() => setShowNewContextDialog(true)}
            title="Create New Context Settings"
          >
            New
          </button>
          <button
            className="context-action-button small"
            onClick={() => setShowLoadContextDialog(true)}
            title="Load Context Settings"
          >
            Load
          </button>
          <button
            className="context-panel-close"
            onClick={handleClose}
            title="Close Context Panel"
          >
            ×
          </button>
        </div>
      </div>

      {/* New Context Dialog */}
      {showNewContextDialog && (
        <div className="context-new-dialog">
          <div className="context-new-dialog-content">
            <h4>Create New Context Settings</h4>
            <input
              type="text"
              placeholder="Context name..."
              value={newContextName}
              onChange={(e) => setNewContextName(e.target.value)}
              className="context-new-input"
            />
            <textarea
              placeholder="Description (optional)..."
              value={newContextDescription}
              onChange={(e) => setNewContextDescription(e.target.value)}
              className="context-new-textarea"
              rows={2}
            />
            <div className="context-new-actions">
              <button onClick={handleCreateNew} className="context-action-button">
                Create
              </button>
              <button 
                onClick={() => setShowNewContextDialog(false)} 
                className="context-action-button secondary"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Load Context Dialog */}
      {showLoadContextDialog && (
        <div className="context-new-dialog">
          <div className="context-new-dialog-content">
            <h4>Load Context Settings</h4>
            <div style={{ maxHeight: '200px', overflowY: 'auto', margin: '12px 0' }}>
              {getContextSettingsList().map(settings => (
                <div
                  key={settings.id}
                  style={{
                    padding: '8px 12px',
                    border: '1px solid #e0e0e0',
                    borderRadius: '4px',
                    margin: '4px 0',
                    cursor: 'pointer',
                    backgroundColor: currentContextSettings?.id === settings.id ? '#f0f7ff' : '#f8f8f8'
                  }}
                  onClick={() => handleLoadContextSettings(settings.id)}
                >
                  <div style={{ fontWeight: 'bold', fontSize: '14px' }}>{settings.name}</div>
                  {settings.description && (
                    <div style={{ fontSize: '12px', color: '#666' }}>{settings.description}</div>
                  )}
                  <div style={{ fontSize: '11px', color: '#999' }}>
                    Updated: {new Date(settings.updatedAt).toLocaleDateString()}
                  </div>
                </div>
              ))}
              {getContextSettingsList().length === 0 && (
                <div style={{ textAlign: 'center', color: '#666', padding: '20px' }}>
                  No context settings available. Create a new one first.
                </div>
              )}
            </div>
            <div className="context-new-actions">
              <button 
                onClick={() => setShowLoadContextDialog(false)} 
                className="context-action-button secondary"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="context-panel-content">
        {/* Level Selection */}
        <div className="context-levels">
          {(['foundational', 'strategic', 'operational'] as ContextLevel[]).map(level => (
            <button
              key={level}
              className={`context-level-button ${activeLevel === level ? 'active' : ''}`}
              onClick={() => handleLevelChange(level)}
            >
              {level.charAt(0).toUpperCase() + level.slice(1)}
            </button>
          ))}
        </div>

        {/* Current Level Content */}
        {currentContextSettings && currentLevelData && (
          <div className="context-level-section">
            <h3 className="context-level-title">
              {activeLevel.charAt(0).toUpperCase() + activeLevel.slice(1)}
            </h3>

            <div className="context-input-tabs">
              {(['text', 'file', 'reference'] as ContextInputType[]).map(type => (
                <button
                  key={type}
                  className={`context-input-tab ${activeInputType === type ? 'active' : ''}`}
                  onClick={() => handleTabChange(type)}
                >
                  {type === 'text' ? 'Text' : type === 'file' ? 'File Upload' : 'Internal Reference'}
                </button>
              ))}
            </div>

            <div className="context-input-content">
              {activeInputType === 'text' && (
                <div>
                  <textarea
                    className="context-text-input"
                    placeholder={`Enter ${activeLevel} context information...`}
                    value={currentInput?.content || ''}
                    onChange={(e) => handleInputChange(e.target.value)}
                  />
                  <div className="context-input-actions">
                    <button className="context-action-button" onClick={handleSave}>
                      Save
                    </button>
                  </div>
                </div>
              )}

              {activeInputType === 'file' && (
                <div>
                  <div className="context-file-upload-button">Browse Files</div>
                  <div className="context-file-upload-info">
                    Supported formats: PDF, DOC, DOCX, TXT, CSV, XLSX
                  </div>
                  <div className="context-input-actions">
                    <button className="context-action-button" disabled>Upload</button>
                  </div>
                </div>
              )}

              {activeInputType === 'reference' && (
                <div>
                  <select 
                    className="context-reference-selector"
                    value={currentInput?.content || ''}
                    onChange={(e) => handleInputChange(e.target.value)}
                  >
                    <option value="">Select a MindSheet...</option>
                    <option value="sheet1">Strategic Plan 2025</option>
                    <option value="sheet2">Market Analysis</option>
                  </select>
                  <div className="context-input-actions">
                    <button className="context-action-button" onClick={handleSave}>
                      Save Reference
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {!currentContextSettings && (
          <div className="context-no-settings">
            <p>No context settings loaded.</p>
            <button 
              className="context-action-button"
              onClick={() => setShowNewContextDialog(true)}
            >
              Create New Context Settings
            </button>
          </div>
        )}
      </div>

      <div className="context-panel-footer">
        <div className="context-info">
          {currentContextSettings && (
            <small>
              Last updated: {new Date(currentContextSettings.updatedAt).toLocaleString()}
            </small>
          )}
        </div>
        <button className="context-action-button" onClick={onClose}>
          Close
        </button>
      </div>
    </div>
  );
};

export default ContextPanel;
