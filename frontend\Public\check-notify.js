// Check and notify about browser compatibility
(function() {
  try {
    console.log('Checking browser compatibility...');
    
    // Check browser features
    const features = {
      localStorage: typeof localStorage !== 'undefined',
      sessionStorage: typeof sessionStorage !== 'undefined',
      fetch: typeof fetch !== 'undefined',
      Promise: typeof Promise !== 'undefined',
      Symbol: typeof Symbol !== 'undefined',
      Map: typeof Map !== 'undefined',
      Set: typeof Set !== 'undefined',
      WeakMap: typeof WeakMap !== 'undefined',
      WeakSet: typeof WeakSet !== 'undefined',
      Proxy: typeof Proxy !== 'undefined',
      Reflect: typeof Reflect !== 'undefined',
      IntersectionObserver: typeof IntersectionObserver !== 'undefined',
      ResizeObserver: typeof ResizeObserver !== 'undefined',
      MutationObserver: typeof MutationObserver !== 'undefined',
      WebSocket: typeof WebSocket !== 'undefined',
      Intl: typeof Intl !== 'undefined',
      BigInt: typeof BigInt !== 'undefined',
      globalThis: typeof globalThis !== 'undefined',
      EventSource: typeof EventSource !== 'undefined',
      WebAssembly: typeof WebAssembly !== 'undefined',
      SharedArrayBuffer: typeof SharedArrayBuffer !== 'undefined',
      Atomics: typeof Atomics !== 'undefined',
      TextEncoder: typeof TextEncoder !== 'undefined',
      TextDecoder: typeof TextDecoder !== 'undefined',
      URL: typeof URL !== 'undefined',
      URLSearchParams: typeof URLSearchParams !== 'undefined',
      Blob: typeof Blob !== 'undefined',
      File: typeof File !== 'undefined',
      FileReader: typeof FileReader !== 'undefined',
      FormData: typeof FormData !== 'undefined',
      IndexedDB: typeof indexedDB !== 'undefined',
      Worker: typeof Worker !== 'undefined',
      ServiceWorker: typeof navigator !== 'undefined' && 'serviceWorker' in navigator,
      WebGL: typeof document !== 'undefined' && !!document.createElement('canvas').getContext('webgl'),
      WebGL2: typeof document !== 'undefined' && !!document.createElement('canvas').getContext('webgl2'),
      Canvas: typeof document !== 'undefined' && !!document.createElement('canvas').getContext('2d'),
      Audio: typeof Audio !== 'undefined',
      Video: typeof document !== 'undefined' && !!document.createElement('video'),
      Geolocation: typeof navigator !== 'undefined' && 'geolocation' in navigator,
      DeviceOrientation: typeof window !== 'undefined' && 'DeviceOrientationEvent' in window,
      DeviceMotion: typeof window !== 'undefined' && 'DeviceMotionEvent' in window,
      TouchEvents: typeof document !== 'undefined' && 'ontouchstart' in document.documentElement,
      PointerEvents: typeof document !== 'undefined' && 'onpointerdown' in document.documentElement,
      CSS: typeof CSS !== 'undefined',
      CSSVariables: typeof document !== 'undefined' && CSS.supports('--a', '0'),
      Grid: typeof document !== 'undefined' && CSS.supports('display', 'grid'),
      Flexbox: typeof document !== 'undefined' && CSS.supports('display', 'flex'),
      WebAnimations: typeof document !== 'undefined' && 'animate' in document.documentElement,
      CustomElements: typeof window !== 'undefined' && 'customElements' in window,
      ShadowDOM: typeof document !== 'undefined' && 'attachShadow' in document.createElement('div'),
      ES6: typeof Symbol !== 'undefined' && Symbol.iterator && Symbol.asyncIterator,
      ES7: typeof Object.entries === 'function' && typeof Object.values === 'function',
      ES8: typeof Object.getOwnPropertyDescriptors === 'function' && typeof String.prototype.padStart === 'function',
      ES9: typeof Promise.prototype.finally === 'function' && typeof Array.prototype.flat === 'function',
      ES10: typeof BigInt === 'function' && typeof globalThis !== 'undefined',
      ES11: typeof Promise.allSettled === 'function' && typeof String.prototype.matchAll === 'function',
      ES12: typeof Promise.any === 'function' && typeof WeakRef !== 'undefined'
    };
    
    // Count missing features
    const missingFeatures = Object.entries(features).filter(([_, supported]) => !supported).map(([name]) => name);
    
    // Log compatibility report
    console.log('Browser compatibility report:');
    console.log(`- Total features checked: ${Object.keys(features).length}`);
    console.log(`- Features supported: ${Object.keys(features).length - missingFeatures.length}`);
    console.log(`- Features missing: ${missingFeatures.length}`);
    
    if (missingFeatures.length > 0) {
      console.log('Missing features:', missingFeatures);
    }
    
    // Notify user if there are significant compatibility issues
    if (missingFeatures.length > 10) {
      // Create a notification element
      if (typeof document !== 'undefined') {
        const notification = document.createElement('div');
        notification.style.position = 'fixed';
        notification.style.top = '0';
        notification.style.left = '0';
        notification.style.right = '0';
        notification.style.backgroundColor = '#f44336';
        notification.style.color = 'white';
        notification.style.padding = '10px';
        notification.style.textAlign = 'center';
        notification.style.zIndex = '9999';
        notification.style.fontFamily = 'Arial, sans-serif';
        notification.style.fontSize = '14px';
        notification.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.3)';
        
        notification.innerHTML = `
          <strong>Browser Compatibility Warning:</strong> 
          Your browser is missing ${missingFeatures.length} features that may affect the application's functionality. 
          Please consider updating your browser or using a modern browser like Chrome, Firefox, or Edge.
          <button style="margin-left: 10px; padding: 5px 10px; background-color: white; color: #f44336; border: none; border-radius: 3px; cursor: pointer;">Dismiss</button>
        `;
        
        // Add dismiss button functionality
        notification.querySelector('button').addEventListener('click', function() {
          notification.style.display = 'none';
        });
        
        // Add to document
        document.body.appendChild(notification);
        console.log('Added compatibility warning notification');
      }
    }
    
    console.log('Browser compatibility check completed');
  } catch (error) {
    console.error('Error checking browser compatibility:', error);
  }
})();
