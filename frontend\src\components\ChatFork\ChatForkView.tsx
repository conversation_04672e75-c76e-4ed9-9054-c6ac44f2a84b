import React, { useEffect, useState, useCallback, useRef } from 'react';
import './ChatFork.css';
import { useChatForkStore, TrackedSelection } from '../MindMap/core/state/ChatForkStore';
import { ChatForkAdapter } from '../MindMap/core/adapters/ChatForkAdapter';

/**
 * ChatForkView - Modern implementation for displaying exploratory content
 * This component only manages its own visibility without affecting GovernanceChat
 */
const ChatForkView: React.FC = () => {
  const { content, isVisible, selections } = useChatForkStore();
  const [extractedContent, setExtractedContent] = useState<string>('');
  const [title, setTitle] = useState<string>('');
  const contentRef = useRef<HTMLDivElement>(null);
  const forkId = useChatForkStore(state => state.activeSheetId) || 'default';
  
  // Get selections for this fork
  const forkSelections = useChatForkStore(state => 
    Array.from(state.selections.values())
      .filter(selection => selection.forkId === forkId)
  );

  // Initialize the global Ctrl+Tab handler
  useEffect(() => {
    console.log('ChatForkView - Initializing global Ctrl+Tab handler');
    ChatForkAdapter.initializeGlobalKeyHandler();
  }, []);

  // Debug log visibility changes
  useEffect(() => {
    console.log('ChatForkView - Visibility changed:', isVisible);
  }, [isVisible]);

  // Extract content from the response when content changes
  useEffect(() => {
    if (content && isVisible) {
      console.log('ChatForkView - Content updated:', {
        title: content.text,
        full_text: content.full_text
      });

      // Simple direct approach: text = headline, full_text = content
      const title = content.text || 'Error: Missing title';
      let extractedText = '';

      if (content.full_text && typeof content.full_text === 'string') {
        console.log('ChatForkView - Using full_text for content');
        extractedText = content.full_text;
      } else {
        console.error('ChatForkView - Error: Missing full_text field in response');
        extractedText = 'Error: Content not available. The response is missing the full_text field.';
      }

      // Handle HTML-like formatting in content
      const cleanedContent = extractedText
        .replace(/<br\s*\/?>/gi, '\n') // Replace <br> tags with newlines
        .replace(/<[^>]*>/g, '')       // Remove other HTML tags
        .trim();                        // Remove extra whitespace

      setExtractedContent(cleanedContent);
      setTitle(title);

      console.log('ChatForkView - Extracted content:', {
        title: title,
        contentLength: cleanedContent.length,
        contentPreview: cleanedContent.substring(0, 100) + '...'
      });
    }
  }, [content, isVisible]);

  // Handler to create a fork from a selection
  const createForkFromSelection = useCallback((selectionId?: string) => {
    const newNodeId = ChatForkAdapter.createForkFromSelection(selectionId);
    if (newNodeId) {
      // Hide this component after creating a node
      ChatForkAdapter.hideChatFork();
    }
  }, []);

  // Add test function to create a dummy selection for testing
  const createTestSelection = useCallback(() => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      ChatForkAdapter.createTrackedSelection(selection, forkId);
    } else {
      console.log('ChatForkView - No selection available for test');
    }
  }, [forkId]);

  // Test Ctrl+Tab handler directly
  const testCtrlTab = useCallback(() => {
    console.log('ChatForkView - Simulating Ctrl+Tab');
    // Create a synthetic keyboard event
    const event = new KeyboardEvent('keydown', {
      key: 'Tab',
      code: 'Tab',
      ctrlKey: true,
      bubbles: true,
      cancelable: true
    });
    document.dispatchEvent(event);
  }, []);

  // The actual render - we'll show a debug UI when not visible
  return (
    <div 
      className="chatfork-debug-container" 
      style={{ 
        position: 'fixed', 
        bottom: '10px', 
        right: '10px', 
        zIndex: 9999,
        background: 'rgba(0,0,0,0.7)',
        color: 'white',
        padding: '10px',
        borderRadius: '5px',
        fontSize: '12px',
        maxWidth: '300px',
        display: 'none' // Hidden by default, for debugging only
      }}
    >
      <div>ChatForkView Debug</div>
      <div>Status: {isVisible ? 'Visible' : 'Hidden'}</div>
      <div>Selections: {forkSelections.length}</div>
      <button onClick={testCtrlTab}>Test Ctrl+Tab</button>
      <button onClick={createTestSelection}>Test Selection</button>
      <button onClick={() => ChatForkAdapter.clearAllSelections()}>Clear All</button>
    </div>
  );
};

export default ChatForkView;