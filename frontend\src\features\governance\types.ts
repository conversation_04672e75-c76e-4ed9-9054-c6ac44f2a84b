/**
 * types.ts
 *
 * Types for the governance chat feature.
 */

// Message type
export interface Message {
  id: string;
  text: string;
  sender: 'user' | 'assistant';
  timestamp: Date;
  isError?: boolean;
  suggestedActions?: SuggestedAction[];
  mbcpData?: any; // Add mbcpData field to store the MBCP data
  responseType?: {
    type: 'teleological' | 'educational' | 'factual';
    confidence: number;
  };
}

// Suggested action type
export interface SuggestedAction {
  type: string;
  label: string;
  payload: any;
}
