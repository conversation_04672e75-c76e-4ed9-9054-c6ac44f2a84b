import { StateCreator } from 'zustand';
import { MindMapStore, Connection } from './types';

export interface ConnectionSlice {
  connections: Record<string, Connection>;
  selectedConnectionId: string | null;
  addConnection: (connection: Omit<Connection, 'id'>) => string;
  updateConnection: (id: string, updates: Partial<Connection>) => void;
  deleteConnection: (id: string) => void;
  selectConnection: (id: string | null) => void;
}

export const createConnectionSlice: StateCreator<MindMapStore, [], [], ConnectionSlice> = (set) => ({
  connections: {},
  selectedConnectionId: null,

  addConnection: (connection) => {
    const id = crypto.randomUUID();
    set((state) => ({
      connections: {
        ...state.connections,
        [id]: { ...connection, id }
      }
    }));
    return id;
  },

  updateConnection: (id, updates) => {
    set((state) => ({
      connections: {
        ...state.connections,
        [id]: { ...state.connections[id], ...updates }
      }
    }));
  },

  deleteConnection: (id) => {
    set((state) => {
      const { [id]: deletedConnection, ...remainingConnections } = state.connections;
      return { connections: remainingConnections };
    });
  },

  selectConnection: (id) => {
    set({ selectedConnectionId: id });
  }
}); 