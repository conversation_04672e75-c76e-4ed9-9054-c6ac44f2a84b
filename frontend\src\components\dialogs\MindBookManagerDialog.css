/* MindBookManagerDialog.css */

.mindbook-manager-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.mindbook-manager-dialog {
  width: 700px;
  max-width: 90vw;
  max-height: 85vh;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Header */
.mindbook-manager-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mindbook-manager-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Tabs */
.mindbook-manager-tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.tab-button {
  flex: 1;
  padding: 16px 20px;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.tab-button:hover:not(.active) {
  background-color: #f3f4f6;
  color: #374151;
}

.tab-button.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background-color: #ffffff;
}

/* Messages */
.message {
  margin: 16px 24px 0;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

.message.success {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.message.error {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}

/* Content */
.mindbook-manager-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

/* Save Tab */
.save-tab {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.current-session-info h3 {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.session-stats {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.stat {
  background-color: #f3f4f6;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.stat.auto-saved {
  background-color: #d1fae5;
  color: #065f46;
}

.save-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.form-group input,
.form-group textarea {
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.save-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 14px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  align-self: flex-start;
}

.save-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.save-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.empty-session {
  text-align: center;
  color: #6b7280;
  background-color: #f9fafb;
  padding: 32px;
  border-radius: 8px;
}

/* Open Tab */
.open-tab {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.mindbooks-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mindbooks-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.refresh-button {
  background: none;
  border: 1px solid #d1d5db;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s;
}

.refresh-button:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.no-mindbooks {
  text-align: center;
  color: #6b7280;
  background-color: #f9fafb;
  padding: 40px 20px;
  border-radius: 8px;
}

.no-mindbooks p {
  margin: 0 0 8px 0;
}

.mindbooks-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mindbook-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s;
  background-color: #ffffff;
}

.mindbook-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.mindbook-item.selected {
  border-color: #667eea;
  background-color: #fafbff;
}

.mindbook-info {
  flex: 1;
}

.mindbook-name {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.mindbook-meta {
  font-size: 14px;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 8px;
}

.mindbook-description {
  font-size: 14px;
  color: #374151;
  margin-top: 8px;
  font-style: italic;
}

.mindbook-actions {
  display: flex;
  gap: 8px;
}

.load-button {
  background-color: #667eea;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.load-button:hover:not(:disabled) {
  background-color: #5a67d8;
}

.load-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Organize Tab */
.organize-tab {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.organize-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.organize-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.organize-stats {
  font-size: 14px;
  color: #6b7280;
}

.organize-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.organize-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background-color: #ffffff;
}

.organize-info {
  flex: 1;
}

.organize-name {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.organize-meta {
  font-size: 14px;
  color: #6b7280;
}

.organize-description {
  font-size: 14px;
  color: #374151;
  margin-top: 8px;
  font-style: italic;
}

.organize-actions {
  display: flex;
  gap: 8px;
}

.delete-button {
  background-color: #ef4444;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.delete-button:hover:not(:disabled) {
  background-color: #dc2626;
}

.delete-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Footer */
.mindbook-manager-footer {
  border-top: 1px solid #e5e7eb;
  padding: 16px 24px;
  background-color: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.footer-info {
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

.close-footer-button {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 8px 20px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.close-footer-button:hover {
  background-color: #e5e7eb;
  border-color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 768px) {
  .mindbook-manager-dialog {
    width: 95vw;
    max-height: 90vh;
  }
  
  .mindbook-manager-content {
    padding: 16px;
  }
  
  .mindbook-item,
  .organize-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .mindbook-actions,
  .organize-actions {
    align-self: stretch;
    justify-content: flex-end;
  }
} 