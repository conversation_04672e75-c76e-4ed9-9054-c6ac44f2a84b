import { create } from 'zustand';

export interface Message {
  id: string;
  text: string;
  sender: 'user' | 'assistant' | 'system';
  timestamp: Date;
  suggestedActions?: any[];
  responseType?: {
    type: string;
    requiresMindmap: boolean;
    mbcpData?: any;
  };
  mindmapCreated?: boolean; // Flag to track if a mindmap has been created for this message
}

interface ChatState {
  messages: Message[];
  isSubmitting: boolean;
  showLogging: boolean; // Add showLogging state
  addMessage: (message: Message) => void;
  setMessages: (messages: Message[]) => void;
  setIsSubmitting: (isSubmitting: boolean) => void;
  clearMessages: () => void;
  toggleLogging: () => void; // Add toggleLogging function
}

// Create a store with initial welcome message
export const useChatStore = create<ChatState>((set) => ({
  messages: [
    {
      id: 'welcome',
      text: 'Welcome to the Governance Agent! How can I help you today?',
      sender: 'assistant',
      timestamp: new Date()
    }
  ],
  isSubmitting: false,
  showLogging: true, // Initialize showLogging to true
  addMessage: (message) => set((state) => ({
    messages: [...state.messages, message]
  })),
  setMessages: (messages) => set({ messages }),
  setIsSubmitting: (isSubmitting) => set({ isSubmitting }),
  clearMessages: () => set({
    messages: [
      {
        id: 'welcome',
        text: 'Welcome to the Governance Agent! How can I help you today?',
        sender: 'assistant',
        timestamp: new Date()
      }
    ]
  }),
  toggleLogging: () => set((state) => ({
    showLogging: !state.showLogging
  }))
}));

export default useChatStore;
