/* ContextManagerDialog.css */

.context-manager-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.context-manager-dialog {
  background: white;
  border-radius: 8px;
  width: 600px;
  max-width: 90vw;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.context-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
}

.context-manager-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.context-header-actions {
  display: flex;
  gap: 8px;
}

.context-action-button {
  background-color: #000000;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.context-action-button:hover {
  background-color: #333333;
}

.context-action-button.secondary {
  background-color: #6c757d;
}

.context-action-button.secondary:hover {
  background-color: #545b62;
}

.context-manager-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 0;
  margin-left: 16px;
}

.context-manager-close:hover {
  color: #333;
}

.context-manager-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.context-current-section,
.context-list-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.context-current-section:last-child,
.context-list-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.context-current-section h4,
.context-list-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.context-current-info {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
}

.context-current-name {
  font-weight: 600;
  font-size: 16px;
  color: #333;
  margin-bottom: 4px;
}

.context-current-description {
  color: #666;
  margin-bottom: 8px;
}

.context-current-meta {
  font-size: 12px;
  color: #999;
}

.context-no-current {
  color: #999;
  font-style: italic;
}

.context-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.context-list-actions {
  display: flex;
  gap: 8px;
}

.context-import-button {
  display: inline-block;
  background-color: #000000;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.context-import-button:hover {
  background-color: #333333;
}

.context-settings-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.context-setting-item {
  background-color: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.context-setting-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.context-setting-item.active {
  border-color: #007bff;
  background-color: #f8f9fa;
}

.context-setting-info {
  flex: 1;
  margin-right: 12px;
}

.context-setting-name {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.context-setting-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  line-height: 1.3;
}

.context-setting-meta {
  font-size: 11px;
  color: #999;
}

.context-setting-actions {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.context-action-button.small {
  padding: 4px 8px;
  font-size: 12px;
}

.context-action-button.danger {
  background-color: #dc3545;
}

.context-action-button.danger:hover {
  background-color: #c82333;
}

/* Create Dialog */
.context-create-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
}

.context-create-dialog {
  background: white;
  border-radius: 8px;
  padding: 20px;
  width: 400px;
  max-width: 90vw;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.context-create-dialog h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #333;
}

.context-create-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.context-create-input,
.context-create-textarea {
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  width: 100%;
}

.context-create-input:focus,
.context-create-textarea:focus {
  outline: none;
  border-color: #000000;
}

.context-create-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}

.context-manager-footer {
  border-top: 1px solid #e0e0e0;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.context-manager-info {
  color: #666;
  font-size: 12px;
}

/* Load Dialog */
.context-load-list {
  max-height: 300px;
  overflow-y: auto;
  margin: 16px 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.context-load-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.context-load-item:hover {
  border-color: #000000;
  background: #ffffff;
}

.context-load-item.active {
  border-color: #000000;
  background: #ffffff;
}

.context-load-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.context-load-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.context-load-meta {
  font-size: 11px;
  color: #999;
}

.context-load-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
}

.context-no-settings {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

/* Responsive styles */
@media (max-width: 768px) {
  .context-manager-dialog {
    width: 95%;
    max-height: 90vh;
  }
  
  .context-manager-header,
  .context-manager-content,
  .context-manager-footer {
    padding: 12px 16px;
  }
  
  .context-setting-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .context-setting-info {
    margin-right: 0;
    margin-bottom: 8px;
  }
  
  .context-setting-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .context-list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
} 