// Basic types and interfaces for the mind map components

// Node position and dimensions
export interface Position {
    x: number;
    y: number;
  }
  
  export interface Dimensions {
    width: number;
    height: number;
  }
  
  // Node interface
  export interface Node extends Position, Dimensions {
    id: string;
    text: string;
    color: string;
  }
  
  // Connection between nodes
  export interface Connection {
    from: string;
    to: string;
  }
  
  // Project data structure
  export interface Project {
    id: string;
    name: string;
    nodes: Node[];
    connections: Connection[];
    lastModified: string;
  }
  
  // Direction type for node layout
  export type Direction = 'right' | 'down' | 'left' | 'up';
  
  // UI State interfaces
  export interface DialogState {
    showNodeDialog: boolean;
    showProjectDialog: boolean;
    showDesignControls: boolean;
  }
  
  export interface NodeEditState {
    nodeText: string;
    nodeColor: string;
  }
  
  // Canvas state
  export interface CanvasState {
    scale: number;
    position: Position;
    direction: Direction;
  }
  
  // Project state
  export interface ProjectState {
    projectName: string;
    lastSaved: string | null;
  }
  
  // Event handler types
  export type NodeClickHandler = (id: string) => void;
  export type NodeEditHandler = (text: string, color: string) => void;
  export type DirectionChangeHandler = (direction: Direction) => void;
  
  // Utility types
  export interface DragState {
    isDragging: boolean;
    lastPosition: Position | null;
  }
  
  export interface ZoomConfig {
    minScale: number;
    maxScale: number;
    scaleStep: number;
  }
  
  // Theme and styling types
  export interface ThemeColors {
    primary: string;
    secondary: string;
    background: string;
    text: string;
    border: string;
  }
  
  // Error types
  export interface MindMapError {
    code: string;
    message: string;
    details?: unknown;
  }
  
  // Storage types
  export interface StorageData {
    nodes: Node[];
    connections: Connection[];
    projectName: string;
    direction: Direction;
    lastSaved: string | null;
  }
  
  // Command types for undo/redo
  export interface Command {
    type: 'ADD_NODE' | 'DELETE_NODE' | 'EDIT_NODE' | 'ADD_CONNECTION' | 'DELETE_CONNECTION' | 'MOVE_NODE';
    data: unknown;
    undo: () => void;
    redo: () => void;
  }
  
  // History state
  export interface HistoryState {
    past: Command[];
    future: Command[];
  }
  
  // Export/Import types
  export interface MindMapExport {
    version: string;
    data: StorageData;
    metadata: {
      createdAt: string;
      lastModified: string;
      author?: string;
    };
  }