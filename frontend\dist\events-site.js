// Site-specific event handling
(function() {
  try {
    console.log('Initializing site-specific event handling...');
    
    // Create a global event bus if it doesn't exist
    if (!window.eventBus) {
      window.eventBus = {
        _events: {},
        on: function(event, callback) {
          if (!this._events[event]) {
            this._events[event] = [];
          }
          this._events[event].push(callback);
          return this;
        },
        off: function(event, callback) {
          if (!this._events[event]) return this;
          if (!callback) {
            delete this._events[event];
          } else {
            this._events[event] = this._events[event].filter(cb => cb !== callback);
          }
          return this;
        },
        emit: function(event, ...args) {
          if (!this._events[event]) return false;
          this._events[event].forEach(callback => {
            try {
              callback.apply(this, args);
            } catch (error) {
              console.error('Error in event callback:', error);
            }
          });
          return true;
        },
        once: function(event, callback) {
          const onceWrapper = (...args) => {
            this.off(event, onceWrapper);
            callback.apply(this, args);
          };
          return this.on(event, onceWrapper);
        }
      };
      console.log('Created global event bus');
    }
    
    // Register common site events
    const commonEvents = [
      'app:initialized',
      'app:error',
      'user:login',
      'user:logout',
      'mindmap:created',
      'mindmap:updated',
      'mindmap:deleted',
      'node:created',
      'node:updated',
      'node:deleted',
      'node:selected',
      'connection:created',
      'connection:updated',
      'connection:deleted',
      'sheet:created',
      'sheet:updated',
      'sheet:deleted',
      'sheet:selected'
    ];
    
    // Add event logging
    commonEvents.forEach(event => {
      window.eventBus.on(event, function(...args) {
        console.log(`Event: ${event}`, ...args);
      });
    });
    
    // Add global error handler
    if (typeof window !== 'undefined') {
      window.addEventListener('error', function(event) {
        console.error('Global error:', event.error);
        window.eventBus.emit('app:error', {
          message: event.message,
          source: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          error: event.error
        });
      });
      console.log('Added global error handler');
    }
    
    // Add unhandled promise rejection handler
    if (typeof window !== 'undefined') {
      window.addEventListener('unhandledrejection', function(event) {
        console.error('Unhandled promise rejection:', event.reason);
        window.eventBus.emit('app:error', {
          message: event.reason?.message || 'Unhandled promise rejection',
          error: event.reason
        });
      });
      console.log('Added unhandled promise rejection handler');
    }
    
    console.log('Site-specific event handling initialized successfully');
  } catch (error) {
    console.error('Error initializing site-specific event handling:', error);
  }
})();
