/* MindMap component styles */

.mindmap-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Toolbar styling */
.mindmap-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  background-color: #2c3e50;
  color: white;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.mindmap-toolbar h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
}

.mindmap-toolbar button {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.mindmap-toolbar button:hover {
  background-color: #2980b9;
}

.mindmap-toolbar-center {
  display: flex;
  gap: 10px;
}

/* Main content area */
.mindmap-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* Canvas container */
.mindmap-canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  background-color: #f8fafc;
  cursor: default;
  width: 100%;
  height: 100%;
}

/* Canvas toolbar */
.mindmap-canvas-toolbar {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  display: flex;
  gap: 5px;
}

.center-button {
  background-color: rgba(52, 152, 219, 0.8);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.2s;
}

.center-button:hover {
  background-color: rgba(41, 128, 185, 1);
}

/* Canvas area */
.mindmap-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: all;
}

.connections-layer {
  position: absolute;
  pointer-events: none;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Loading state */
.loading-canvas {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 16px;
  color: #666;
}

/* Debug info */
.debug-info {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 5px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
}

/* Sidebar */
.mindmap-sidebar {
  width: 300px;
  background-color: white;
  box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.sidebar-section {
  margin-bottom: 20px;
}

.sidebar-section h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1.2rem;
  font-weight: 500;
  color: #2c3e50;
}

/* LLM Controls */
.llm-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.llm-controls select {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.llm-controls button {
  background-color: #2ecc71;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.llm-controls button:hover {
  background-color: #27ae60;
}

/* Dialog styling */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 500px;
  max-width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.dialog-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 500;
}

.dialog-header button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #aaa;
}

.dialog-content {
  padding: 20px;
  overflow-y: auto;
}

.dialog-footer {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.dialog-footer button {
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.dialog-footer button:first-child {
  background-color: white;
  border: 1px solid #ddd;
  color: #333;
}

.dialog-footer button:last-child {
  background-color: #3498db;
  border: none;
  color: white;
}

/* Tab styling */
.dialog-tabs {
  display: flex;
  border-bottom: 1px solid #eee;
}

.dialog-tabs button {
  padding: 10px 15px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  opacity: 0.7;
  border-bottom: 2px solid transparent;
}

.dialog-tabs button.active {
  opacity: 1;
  border-bottom-color: #3498db;
}

/* Form styling */
.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 14px;
  color: #333;
}

.form-group input[type="text"],
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

/* Node styling */
.mindmap-node {
  position: relative;
  background-color: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  min-width: 120px;
  cursor: pointer;
  user-select: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

.mindmap-node.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.mindmap-node.dragging {
  opacity: 0.7;
  cursor: grabbing;
}

/* Hover effects removed for better UX */

/* Connection styling */
.connection {
  pointer-events: auto;
  cursor: pointer;
}

.connection-selected {
  stroke-width: 3;
}

/* Suggestions styling */
.suggestions {
  margin-top: 15px;
}

.suggestions ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.suggestions li {
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.suggestions li:hover {
  background-color: #e9ecef;
}

/* Loading indicator */
.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
}

.loading-indicator::after {
  content: '';
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 