# MindBook Architecture - Unified Session Management

## Overview

This document describes the new unified MindBook architecture that replaces the fragmented session persistence implementations. The new system provides a comprehensive solution for managing complete MindBook sessions - collections of MindSheets that work together like an Excel workbook.

## The Problem We Solved

### Previous Fragmented Systems

Before this implementation, session persistence was handled by multiple, conflicting systems:

1. **SessionPersistenceService** - Only saved basic sheet data to `mindback_session`
2. **Project Manager** - Multiple versions saving individual mindmaps to `mindmap_${name}` keys
3. **MindMapStore** - Individual project saving with `mindback_last_active_project`
4. **StoreService** - Per-sheet state management with `mindmap_sheet_${id}` keys
5. **LayoutGovernanceService** - Layout preferences in separate storage

### Issues with Old System

- **Multiple Storage Keys**: Different systems using different localStorage keys
- **Incomplete Coverage**: No system saved complete MindBook state
- **No Session Consistency**: Refresh would lose active context
- **Race Conditions**: Multiple systems trying to save simultaneously
- **Poor User Experience**: No unified save/load interface

## New Unified Architecture

### Core Concept: MindBook

A **MindBook** is like an Excel workbook - it contains multiple sheets (MindSheets) of different types:
- **ChatFork Sheets**: Exploratory conversation flows
- **MindMap Sheets**: Visual mind mapping with nodes and connections
- **Action Tracking**: Governance actions and decisions (future)

### MindBookPersistenceService

The new `MindBookPersistenceService` provides:

```typescript
// Auto-save current session (temporary storage)
autoSaveSession(): boolean

// Restore auto-saved session on app startup
restoreAutoSavedSession(): boolean

// Save current session as a named MindBook
saveMindBook(name: string, description?: string): boolean

// Load a saved MindBook by ID
loadMindBook(mindBookId: string): boolean

// Delete a saved MindBook
deleteMindBook(mindBookId: string): boolean

// Get list of all saved MindBooks
getMindBooksList(): MindBookListItem[]
```

### Storage Schema

#### Primary Storage Keys

- `mindbook_autosave` - Auto-saved session (temporary)
- `mindbook_${id}` - Named MindBook storage
- `mindbooks_list` - List of all saved MindBooks
- `active_mindbook` - Currently active MindBook ID

#### Individual Sheet State

- `mindmap_sheet_${sheetId}` - MindMap sheet detailed state (nodes, connections, layout)

### Data Structure

```typescript
interface MindBookData {
  id: string;                    // Unique identifier
  name: string;                  // User-friendly name
  sheets: SheetData[];           // All sheets in the MindBook
  activeSheetId: string | null;  // Currently active sheet
  savedAt: number;               // Timestamp
  version: string;               // Format version
  metadata?: {
    description?: string;        // Optional description
    tags?: string[];            // Auto-generated tags
    creator?: string;           // Always 'mindback.ai'
  };
}
```

## User Interface

### MindBook Manager Dialog

The new **MindBook Manager** replaces the old Project Manager with three main sections:

#### 💾 Save Tab
- View current session status (number of sheets, auto-save status)
- Save current session as a named MindBook
- Add optional description
- Form validation and user feedback

#### 📂 Open Tab  
- Browse all saved MindBooks
- View MindBook details (name, sheet count, date, description)
- One-click loading of complete MindBooks
- Refresh functionality

#### 🗂️ Organize Tab
- Manage saved MindBooks
- Delete MindBooks with confirmation
- View storage statistics
- Future: rename, duplicate, export functionality

### Header Integration

The new header provides:
- **Session Status**: Live display of current sheets and auto-save status
- **MindBook Manager Button**: One-click access to save/load (Ctrl+S shortcut)
- **Context Panel Toggle**: Quick access to context settings

## Technical Implementation

### Auto-Save Strategy

1. **Debounced Auto-Save**: Changes trigger auto-save after 2-second delay
2. **Before Unload**: Immediate save when user closes/refreshes browser
3. **Comprehensive State**: Saves both MindBook structure and individual sheet states

### Session Restoration

1. **App Startup**: Automatically attempts to restore auto-saved session
2. **Loading State**: User-friendly loading screen during restoration
3. **Graceful Fallback**: Starts fresh if restoration fails

### Migration and Cleanup

The service automatically:
- Removes old fragmented storage keys
- Logs legacy projects for potential future migration
- Ensures clean slate for new unified system

## User Workflow

### Daily Usage

1. **Start Working**: App automatically restores last session
2. **Create Content**: Add MindSheets, ChatForks, work on mind maps
3. **Auto-Save**: Changes are automatically saved every 2 seconds
4. **Refresh Safe**: Browser refresh restores complete session

### Long-term Management

1. **Save MindBook**: Use Ctrl+S or header button to save named MindBook
2. **Organize Work**: Use MindBook Manager to browse and manage saved work
3. **Load Previous Work**: One-click loading of any saved MindBook
4. **Clean Up**: Delete old MindBooks when no longer needed

### Collaboration (Future)

The architecture supports future enhancements:
- Export MindBooks to files
- Import shared MindBooks
- Cloud storage integration
- Real-time collaboration

## Benefits

### For Users

1. **Never Lose Work**: Automatic session persistence across browser refreshes
2. **Organized Storage**: Named MindBooks like saving Excel workbooks
3. **Quick Access**: Fast save/load with keyboard shortcuts
4. **Visual Management**: Clear interface showing all saved work
5. **Complete Context**: Entire working session is preserved and restored

### For Developers

1. **Single Source of Truth**: One service handles all persistence
2. **Consistent API**: Unified interface for all session operations
3. **Extensible**: Easy to add new sheet types and features
4. **Debuggable**: Clear logging and error handling
5. **Maintainable**: No more fragmented storage systems

## Migration Notes

### From Old System

Users with existing data will experience:
- Old session persistence keys are cleaned up automatically
- Legacy project data is preserved (logged for potential migration)
- Fresh start with new unified system
- Instructions to re-save important work as MindBooks

### Development

Developers should:
- Use only the new `MindBookPersistenceService` for session management
- Remove references to old `SessionPersistenceService`
- Update any code that directly manipulates localStorage session keys
- Use the new MindBook Manager dialog instead of old Project Manager

## Future Enhancements

### Planned Features

1. **Import/Export**: Save MindBooks to files, share with others
2. **Templates**: Pre-built MindBook templates for common use cases
3. **Search**: Find MindBooks by content, tags, or metadata
4. **Versioning**: Keep history of MindBook changes
5. **Cloud Sync**: Synchronize MindBooks across devices

### Technical Improvements

1. **Compression**: Reduce storage size for large MindBooks
2. **Incremental Sync**: Only save changed sheets
3. **Background Sync**: Non-blocking save operations
4. **Storage Quotas**: Manage localStorage usage limits
5. **Migration Tools**: Import from other mind mapping tools

## Conclusion

The new MindBook architecture provides a complete, user-friendly solution for session management that scales from quick note-taking to complex project management. By unifying all session persistence under a single system, we've eliminated the confusion and data loss issues of the previous fragmented approach while providing a foundation for future collaborative and cloud-based features. 