import { create } from 'zustand';
import { createProjectSlice } from './projectSlice';
import { createNodeSlice } from './nodeSlice';
import { createConnectionSlice } from './connectionSlice';
import { createUndoRedoSlice } from './undoRedoSlice';
import { MindMapStore } from './types';

export const useMindMapStore = create<MindMapStore>()((...args) => ({
  ...createProjectSlice(...args),
  ...createNodeSlice(...args),
  ...createConnectionSlice(...args),
  ...createUndoRedoSlice(...args),
})); 