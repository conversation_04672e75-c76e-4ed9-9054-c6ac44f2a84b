# UI Positioning System Documentation

## Overview

The UI Positioning System is a centralized management system for handling the positioning, sizing, and visibility of UI elements in MindBack. It ensures that multiple UI components (such as the governance box, context panel, node boxes, etc.) can coexist harmoniously without conflicts.

## Key Features

- **Centralized Management**: All UI element positions are managed in one place
- **Collision Detection**: Automatic detection and handling of element overlaps
- **Flexible Positioning Strategies**: Different strategies for different UI elements
- **Priority System**: Elements with higher priority take precedence in positioning
- **Independent Components**: UI elements are decoupled but aware of the overall system
- **Future-Proof**: Easily extensible for new UI components
- **Window Resize Handling**: Automatic repositioning on window resize
- **User Control**: Preserves manual positioning by users

## Architecture

### Core Components

1. **UIPositioningManager**: Central manager class that tracks all UI elements
2. **PositioningManagerProvider**: React context provider for the positioning manager
3. **useUIElement**: Custom hook for UI elements to register with the system

### Element Types

The system supports various UI element types, including:

- `governance-box`: The governance agent dialog
- `context-panel`: The context settings panel
- `mindmap-manager`: The mindmap management interface
- `node-box`: Individual node editing boxes
- `project-manager`: Project management dialog
- `chat-fork`: Chat fork interface
- Custom types for future components

### Positioning Strategies

The system supports multiple positioning strategies:

- `default`: Use the element's default position
- `right-side`: Position on the right side of the screen
- `left-side`: Position on the left side of the screen
- `center`: Center on the screen
- `avoid-overlap`: Automatically position to avoid overlapping with other elements
- `manual`: Manual positioning (user-defined)

### Z-Index Layers

To ensure consistent stacking of UI elements, the system defines several z-index layers:

- `BASE = 1000`: Base layer for background elements
- `PANELS = 1100`: Panel elements like the context panel
- `DIALOGS = 1200`: Dialog elements like the governance box
- `POPOVERS = 1300`: Popover elements
- `TOOLTIPS = 1400`: Tooltip elements
- `MODALS = 1500`: Modal dialogs

## Implementation

### UIPositioningManager Class

The core manager class that handles all positioning logic:

```typescript
class UIPositioningManager {
  private elements: Record<string, UIElementPosition> = {};
  private collisionHandlers: Record<string, (collidingElements: string[]) => void> = {};
  private globalEventHandlers: Array<(elements: Record<string, UIElementPosition>) => void> = [];
  
  // Register an element with the manager
  registerElement(element: UIElementPosition): void;
  
  // Update an element's position
  updatePosition(id: string, position: { x: number, y: number }): void;
  
  // Update an element's visibility
  updateVisibility(id: string, visible: boolean): void;
  
  // Update an element's size
  updateSize(id: string, size: { width: number, height: number }): void;
  
  // Update an element's strategy
  updateStrategy(id: string, strategy: PositioningStrategy): void;
  
  // Apply the current strategy for an element
  applyStrategy(id: string): void;
  
  // Register a collision handler for an element
  registerCollisionHandler(id: string, handler: (collidingElements: string[]) => void): void;
  
  // Register a global event handler
  registerGlobalHandler(handler: (elements: Record<string, UIElementPosition>) => void): void;
  
  // Get all registered elements
  getElements(): Record<string, UIElementPosition>;
  
  // Get a specific element
  getElement(id: string): UIElementPosition | undefined;
  
  // Reset an element to its default position
  resetPosition(id: string): void;
  
  // Remove an element from the manager
  removeElement(id: string): void;
  
  // Handle window resize
  handleWindowResize(): void;
}
```

### React Context and Hooks

```typescript
// Create a context for the positioning manager
const PositioningManagerContext = createContext<UIPositioningManager | null>(null);

// Provider component
export const PositioningManagerProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Implementation...
};

// Hook to use the positioning manager
export const usePositioningManager = () => {
  // Implementation...
};

// Hook for individual UI elements
export const useUIElement = (
  id: string,
  type: UIElementType,
  defaultPosition: { x: number, y: number },
  defaultSize: { width: number, height: number },
  zIndex: number,
  strategy: PositioningStrategy = 'default',
  priority: number = 0
) => {
  // Implementation...
};
```

## Usage Examples

### Registering a UI Element

```typescript
const GovernanceBox: React.FC<GovernanceBoxProps> = ({ isOpen, onClose }) => {
  const {
    position,
    setPosition,
    size,
    setSize,
    visible,
    setVisible,
    registerCollisionHandler,
    updateStrategy
  } = useUIElement(
    'governance-box',
    'governance-box',
    { x: 100, y: 100 },
    { width: 450, height: 600 },
    ZIndexLayer.DIALOGS,
    'default',
    10 // High priority
  );
  
  // Component implementation...
};
```

### Handling Collisions

```typescript
useEffect(() => {
  registerCollisionHandler((collidingElements) => {
    // If colliding with context panel, move to right side
    if (collidingElements.includes('context-panel')) {
      updateStrategy('right-side');
    }
    
    // If colliding with mindmap manager, adjust position
    if (collidingElements.includes('mindmap-manager')) {
      updateStrategy('avoid-overlap');
    }
  });
}, []);
```

### Updating Position and Size

```typescript
<Rnd
  position={position}
  size={size}
  onDragStop={(e, d) => setPosition({ x: d.x, y: d.y })}
  onResizeStop={(e, direction, ref, delta, position) => {
    setSize({
      width: parseInt(ref.style.width),
      height: parseInt(ref.style.height)
    });
    setPosition(position);
  }}
  // Other props...
>
  {/* Component content */}
</Rnd>
```

## Migration Guide

### Step 1: Set Up the Core System

1. Create the `UIPositioningManager` class
2. Create the React context and hooks
3. Wrap the app with the `PositioningManagerProvider`

### Step 2: Migrate Existing Components

1. Start with the governance box and context panel
2. Update each component to use the `useUIElement` hook
3. Register appropriate collision handlers
4. Test interactions between components

### Step 3: Add New Components

1. Ensure new components register with the positioning system
2. Define appropriate positioning strategies and priorities
3. Test interactions with existing components

## Best Practices

1. **Assign Appropriate Priorities**: Give higher priorities to more important UI elements
2. **Choose Appropriate Strategies**: Select the right positioning strategy for each element
3. **Handle Collisions Gracefully**: Implement collision handlers that provide a good user experience
4. **Test with Multiple Elements**: Ensure the system works well with many UI elements open simultaneously
5. **Consider User Preferences**: Allow users to customize positioning behavior where appropriate

## Troubleshooting

### Common Issues

1. **Elements Jump Unexpectedly**: Check for conflicting positioning strategies or collision handlers
2. **Z-Index Problems**: Ensure z-index values are set correctly for each element
3. **Performance Issues**: Optimize collision detection and position updates
4. **Window Resize Problems**: Ensure the window resize handler is working correctly

### Debugging

The positioning manager includes logging for position changes and collisions. Check the console for messages like:

- "Element registered: [id]"
- "Position updated: [id], [position]"
- "Collision detected: [id] with [collidingElements]"
- "Strategy applied: [id], [strategy]"

## Future Enhancements

1. **Persistence**: Save element positions to localStorage
2. **Grouping**: Allow elements to be grouped and moved together
3. **Snapping**: Add grid snapping and element-to-element snapping
4. **Keyboard Navigation**: Add keyboard shortcuts for positioning
5. **Accessibility**: Enhance screen reader support
