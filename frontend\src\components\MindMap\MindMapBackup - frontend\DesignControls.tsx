import React from 'react';
import { useMindMap } from './context/MindMapContext';
import { Direction } from './types';

export const DesignControls: React.FC = () => {
  const {
    showDesignControls,
    setShowDesignControls,
    direction,
    setDirection
  } = useMindMap();

  if (!showDesignControls) return null;

  const directions: Direction[] = ['right', 'down', 'left', 'up'];

  return (
    <div className="design-controls-dialog" style={{
      position: 'absolute',
      top: '80px',
      right: '20px',
      backgroundColor: '#fff',
      padding: '20px',
      borderRadius: '5px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      zIndex: 1000
    }}>
      <div className="dialog-header">
        <h3>Design Controls</h3>
        <button 
          className="close-button"
          onClick={() => setShowDesignControls(false)}
          style={{
            position: 'absolute',
            top: '10px',
            right: '10px',
            background: 'none',
            border: 'none',
            fontSize: '20px',
            cursor: 'pointer'
          }}
        >
          ×
        </button>
      </div>
      <div className="direction-buttons" style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(2, 1fr)',
        gap: '10px',
        margin: '20px 0'
      }}>
        {directions.map(dir => (
          <button
            key={dir}
            className={`direction-button ${direction === dir ? 'selected' : ''}`}
            onClick={() => setDirection(dir)}
            style={{
              padding: '10px',
              border: direction === dir ? '2px solid #4dabf7' : '1px solid #dee2e6',
              borderRadius: '5px',
              backgroundColor: direction === dir ? '#e7f5ff' : '#fff',
              cursor: 'pointer'
            }}
          >
            {dir.charAt(0).toUpperCase() + dir.slice(1)}
          </button>
        ))}
      </div>
      <div className="color-scheme" style={{ marginTop: '20px' }}>
        <h4>Color Scheme</h4>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(4, 1fr)',
          gap: '5px',
          marginTop: '10px'
        }}>
          {['#4dabf7', '#51cf66', '#fcc419', '#ff6b6b'].map(color => (
            <button
              key={color}
              style={{
                width: '30px',
                height: '30px',
                backgroundColor: color,
                border: 'none',
                borderRadius: '5px',
                cursor: 'pointer'
              }}
              onClick={() => {
                // Color selection logic here
                console.log('Selected color:', color);
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
}; 