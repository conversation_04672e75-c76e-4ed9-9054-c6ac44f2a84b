# Development Workflow

## Process

1. Feature planning
2. Implementation
3. Testing
4. Review
5. Deployment


## Migrated Content

# MindBack Application Workflow

## Table of Contents
1. [System Overview](#system-overview)
2. [Key Workflows](#key-workflows)
   - [Application Startup](#application-startup)
   - [Mindmap Creation](#mindmap-creation)
   - [Node Interaction](#node-interaction)
   - [LLM Integration](#llm-integration)
   - [Project Management](#project-management)
   - [Export/Import](#export-import)
3. [Component Architecture](#component-architecture)
   - [MindMap Components](#mindmap-components)
   - [Dialog Components](#dialog-components)
   - [State Management](#state-management)
   - [AI Components](#ai-components)
4. [Development Guidelines](#development-guidelines)
5. [Recent Updates](#recent-updates)

## System Overview

MindBack is a mind-mapping application built with React and TypeScript that allows users to create, organize, and visualize hierarchical thought structures. The application features an interactive canvas where users can create nodes, establish connections between them, and organize ideas in a visual format.

The system consists of two main parts:
- **Frontend**: React/TypeScript application with custom components and state management
- **Backend**: FastAPI server providing LLM integration and data processing with CrewAI support

## Key Workflows

### Application Startup

```mermaid
graph TD
    A[run_setup.ps1] --> B[Activate Virtual Environment]
    B --> C[Install Dependencies]
    C --> D[Start Backend Server]
    D --> E[Start Frontend Server]
    E --> F[Initialize UI]
    F --> G[Load Prompt Registry]
    G --> H[Ready for User Input]
```

1. Environment setup via `run_setup.ps1`
2. Backend server launch (FastAPI)
3. Frontend server launch (Vite/React)
4. UI initialization and component mounting
5. Prompt registry loading for LLM interactions

### Mindmap Creation

#### User-Initiated Flow
1. User creates a new project or opens existing one via Project Management Dialog
2. User manually adds nodes and connections
3. User arranges nodes on the canvas
4. User edits node content via NodeDialog
5. User saves the project (automatic or manual)
6. Optional: User exports the project as a JSON file

#### LLM-Assisted Flow
1. User interacts with GovernanceChatDialog
2. User sends a query to the LLM
3. LLM responds with suggestions
4. User clicks "Build Mindmap" button (automatic or manual)
5. Dialog collapses and mindmap is generated
6. Nodes and connections are created based on LLM response
7. Layout is calculated and mindmap is rendered

#### JSON Processing and Mindmap Generation
```mermaid
flowchart TD
  A[Backend Response] --> B[Parse Outer JSON]
  B --> C[Extract Content Field]
  C --> D[Parse Inner JSON String]
  D --> E[Extract Mindmap Structure]
  E --> F[Create Root Node]
  F --> G[Process Child Nodes]
  G --> H[Update MindMapStore]
  H --> I[Calculate Layout]
  I --> J[Render Mindmap]
```

### Node Interaction

1. User selects a node
   - Single click: Node is selected but dialog doesn't open
   - Double click: Node is selected and dialog opens for editing
   - Enter key: Opens dialog for editing the selected node
2. Canvas interaction handled by useCanvasInteraction hook
   - `components/Canvas/hooks/useCanvasInteraction.ts`
   - Mouse event detection in handleMouseDown function
   - Double-click detection with timestamp comparison
   - Sets isEditing flag to true on double-click or Enter key
   - Enter key detection in handleKeyDown function
3. NodeDialogContainer responds to selection state
   - `components/Dialogs/NodeDialog/NodeDialogContainer.tsx`
   - Retrieves selected node data and isEditing flag from MindMapStore
   - Only opens dialog when isEditing is true
   - Displays node information in tabbed interface
4. NodeDialog renders with node data and specialized tabs
   - `components/Dialogs/NodeDialog/NodeDialog.tsx`
   - Node Info Tab: Displays node title and description for editing
   - Design Tab: Provides options for node appearance and styling
   - Hat Tabs: Interfaces for different thinking modes (Six Thinking Hats)
5. User edits node content and properties
   - Changes trigger onSave callback in NodeDialog
   - NodeDialogContainer updates node in MindMapStore
   - MindMapCanvas reflects changes immediately
6. User can request agent suggestions via thinking hat tabs
   - Each hat provides specialized assistance based on thinking mode
   - Suggestions are tailored to the active thinking mode
   - User can add suggested content or child nodes
7. Dialog closes on save or cancel
   - handleClose function resets state and isEditing flag
   - Canvas re-renders with updates from MindMapStore

### LLM Integration

#### Response Pattern Selection
The LLM uses a unified prompt to handle distinct interaction types:

```mermaid
graph TD
    A[User Input] --> B{Query Type}
    B -->|Factual Question| C[Direct Response]
    B -->|Strategic Issue| D[Mindmap Suggestion]
    B -->|Learning Concept| E[Mindmap Suggestion]
    B -->|Reflective Question| F{Complexity Check}
    F -->|Simple| G[Direct Response]
    F -->|Complex| H[Mindmap Suggestion]
```

1. **Simple Factual Answers**
   - Input: Direct questions
   - Process: Immediate response
   - Output: Concise, factual answer
   - Manual mindmap creation available

2. **Strategic Issues**
   - Input: Planning/decision questions
   - Process: Analysis suggestion
   - Output: Mindmap proposal
   - Structured breakdown

3. **Learning Breakdowns**
   - Input: Concept explanations
   - Process: Visual learning suggestion
   - Output: Mindmap proposal
   - Hierarchical knowledge structure

4. **Reflective Questions**
   - Input: Open-ended inquiries
   - Process: Complexity assessment
   - Output: Either direct response or mindmap
   - Based on depth needed

#### Six Thinking Hats Integration

Node dialog now includes tab-based interfaces for each thinking hat:

```mermaid
graph TD
    A[Node Dialog] --> B[Node Info Tab]
    A --> C[Design Tab]
    A --> D{Thinking Hats}
    D --> E[White Hat: Facts]
    D --> F[Red Hat: Emotions]
    D --> G[Black Hat: Criticism]
    D --> H[Yellow Hat: Benefits]
    D --> I[Green Hat: Creativity]
```

1. **Specialized Agent Interaction**
   - Each hat tab connects to specialized agent
   - Agent provides suggestions based on thinking mode
   - Suggestions are tailored to the specific hat's focus
   - User can apply suggestions to the node content

2. **Context Awareness**
   - Agents maintain awareness of current node content
   - Backend handles appropriate routing to specialized agents
   - Prompt templates are applied based on the active hat
   - Conversations preserve history within the tab session

#### Data Structure and Transformation Flow
```mermaid
graph TD
    A[LLM Response] --> B[MBCP Validation]
    B --> C[Data Structure Transform]
    C --> D[Node Creation]
    D --> E[Metadata Integration]
    E --> F[Connection Management]
    F --> G[Visual Rendering]
```

1. **LLM Response Processing**
   - Complete JSON structure with rich metadata
   - Node hierarchical relationships
   - Intent and agent information
   - Tags and action items

2. **Data Transformation Pipeline**
   - MBCP validation layer
   - Structure transformation
   - Metadata preservation
   - Relationship mapping

3. **Node Creation Process**
   - Proper node initialization
   - Metadata integration
   - Hierarchical structure maintenance
   - Connection tracking

4. **State Management**
   - Complete node tracking
   - Relationship preservation
   - Metadata storage
   - Connection state handling

### Project Management

The application provides comprehensive project management capabilities:

```mermaid
graph TD
    A[Project Management Dialog] --> B[Save Project]
    A --> C[Open Project]
    A --> D[Create New Project]
    A --> E[Export Project]
    A --> F[Import Project]
    B --> G[LocalStorage]
    C --> G
    E --> H[JSON File]
    F --> H
```

1. **Save Project Flow**
   - User clicks hamburger menu to open Project Dialog
   - User selects Save tab
   - User enters project name or selects existing project
   - System saves project to localStorage
   - System displays confirmation

2. **Open Project Flow**
   - User clicks hamburger menu to open Project Dialog
   - User selects Open tab
   - User selects project from list
   - System loads project from localStorage
   - Canvas refreshes with loaded project

3. **Create New Project Flow**
   - User clicks hamburger menu to open Project Dialog
   - User selects New tab
   - System prompts to save current project if changed
   - System creates new project with default root node
   - Canvas refreshes with new project

4. **Export/Import Flow**
   - User exports project to JSON file via Project Dialog
   - File is saved to downloads folder with standard naming
   - User can import the file later via Import option
   - System validates file format before importing

### Export/Import

The application supports export and import of mindmap projects:

1. **Export Process**
   - User selects Export option in Project Dialog
   - System retrieves project data from localStorage
   - System adds metadata (version, date, project name)
   - System creates JSON file and triggers download
   - File is saved with format: `mindback_ProjectName_YYYY-MM-DD.json`

2. **Import Process**
   - User selects Import option in Project Dialog
   - System opens file picker dialog
   - User selects previously exported JSON file
   - System validates file format and structure
   - System loads project into the application
   - If project name exists, user is prompted to resolve conflict

## Component Architecture

### MindMap Components
1. **MindMap** (`components/MindMap/index.tsx`)
   - Main container component
   - Manages canvas state and interactions
   - Integrates with MindMapStore
   - Handles autosave functionality

2. **MindMapCanvas** (`components/Canvas/MindMapCanvas.tsx`)
   - Renders the interactive canvas
   - Manages zoom and pan functionality
   - Handles node selection and interaction
   - Uses hooks for state and interaction management:
     - `useCanvasState.ts`
     - `useCanvasInteraction.ts`
     - `useCanvasRendering.ts`

3. **NodeRenderer** (`components/Canvas/NodeRenderer.tsx`)
   - Renders individual nodes on the canvas
   - Handles node appearance and styling
   - Displays node metadata (path, title)
   - Shows hat color indicators based on node settings

4. **ConnectionRenderer** (`components/Canvas/ConnectionRenderer.tsx`)
   - Renders connections between nodes
   - Handles connection styling and appearance
   - Manages connection interaction and selection
   - Supports different line styles and directions

### Dialog Components
1. **NodeDialog** (`components/Dialogs/NodeDialog/NodeDialog.tsx`)
   - Provides interface for editing nodes
   - Manages node properties and content
   - Provides tabbed interface for different functions:
     - Node Info Tab: Basic node information
     - Design Tab: Node appearance settings
     - Hat Tabs: Six Thinking Hats interfaces
   - Wrapped by NodeDialogContainer for state management

2. **GovernanceChatDialog** (`components/Dialogs/GovernanceChatDialog/index.tsx`)
   - Provides chat interface for LLM interaction
   - Draggable and resizable dialog
   - Manages message history and state
   - Integrates with LLM service
   - Components:
     - DialogHeader
     - MessageList (with "Build Mindmap" button)
     - MessageInput
     - ModelSelector
     - CrewAI toggle

3. **ProjectManagementDialog** (`components/MindMap/components/Dialogs/ProjectManagementDialog.tsx`)
   - Provides interface for project operations
   - Tabbed interface for different operations:
     - Save: Save current project
     - Open: Open existing project
     - New: Create new project
     - Export/Import: Import/export project files
   - Manages project list and operations
   - Integrates with MindMapStore for project data

### State Management
1. **MindMapStore** (`core/state/MindMapStore.ts`)
   - Zustand store for global state
   - Manages nodes and connections
   - Handles selection state (including isEditing flag)
   - Provides methods for node and connection operations
   - Manages project saving, loading, exporting, and importing

2. **ChatMemoryService** (`services/ChatMemoryService.ts`)
   - Manages chat history and context
   - Provides structured message storage
   - Handles message categorization and retrieval

### AI Components
1. **CrewAI Integration** (`backend/agentCrew/`)
   - Implements multi-agent system with CrewAI
   - Manages agent roles and responsibilities
   - Handles tool selection and specialization
   - Processes complex requests with specialized agents

2. **LLM Service** (`backend/services/llm_handler.py`)
   - Manages communication with LLM providers
   - Handles prompt formatting and response parsing
   - Provides interfaces for different LLM models
   - Implements contextual awareness for conversations

3. **Prompt Library** (`frontend/src/components/MindMap/components/Agents/Prompt_library/`)
   - Stores YAML-based prompt templates
   - Organizes prompts by thinking hat and function
   - Follows MBCP specification for prompt structure
   - Provides consistent interface for agent interactions

## Development Guidelines

### Component Structure
- Components exceeding 300 lines should be modularized
- Each module should focus on a specific responsibility
- Shared utilities and types should be extracted to separate files
- Maintain clear separation of concerns:
  - State management
  - UI rendering
  - Business logic
  - Side effects

### Module and Import Best Practices

Based on recent experiences with import issues, follow these guidelines:

1. **Path Consistency**
   - Use consistent casing (preferably lowercase) for all directory and file imports
   - Remember: Windows is case-insensitive but TypeScript module resolution is case-sensitive
   - Triple-check paths when moving files to new locations

2. **Relative Path Calculation**
   - Be precise with relative path calculations (`../`)
   - For deeply nested components, verify the exact directory depth
   - Consider using path aliases in tsconfig.json to avoid deep relative paths
   - Document complex import paths with comments when necessary

3. **Avoiding Circular Dependencies**
   - Implement wrapper patterns for complex components
   - Use dedicated implementation files (e.g., `Implementation.tsx`) to avoid circular references
   - Split component logic to ensure clean boundaries
   - Export only what's necessary from each module

4. **Component Interfaces**
   - Ensure prop names match exactly between component interfaces and usages
   - Use TypeScript interfaces to document expected props
   - Validate props with proper type checking and defaults
   - Keep interfaces and implementations in sync

5. **Import Extensions**
   - Avoid including `.ts`/`.tsx` extensions in import statements
   - Let TypeScript's module resolution handle file extensions
   - Be consistent with extension omission across the codebase

Following these practices will help prevent common import and module resolution issues that can cause difficult-to-debug runtime errors.

### State Management
- Use Zustand for global state
- Implement clean separation of concerns
- Minimize prop drilling through context/store usage
- Follow immutable update patterns

### AI Integration
- Store prompts in YAML files in the prompt library
- Communication between frontend and backend in JSON format
- Follow the MBCP specification for prompt structure
- Implement proper error handling for LLM failures

### Project Structure
- Organize components by feature area
- Keep related files together
- Use consistent naming conventions
- Document complex logic with comments

### Data Structure Guidelines
- Preserve complete LLM response data
- Maintain proper node relationships
- Implement robust validation
- Handle metadata consistently
- Ensure proper connection tracking

### State Management Guidelines
- Track all node relationships
- Preserve complete metadata
- Maintain connection states
- Handle updates efficiently
- Implement proper validation

## Recent Updates

### Build Mindmap Button Visualization Fix (May 2025)

Fixed the issue where the "Build Mindmap" button wasn't appearing for teleological intent responses:

1. **Root Cause Analysis**
   - Identified that the button was hardcoded to be hidden (`showBuildMindmapButton={false}`)
   - Found disconnections in the intent data flow from API response to UI
   - Discovered that the message object wasn't properly including response type information

2. **Implementation Fixes**
   - Modified `Implementation.tsx` to use conditional rendering based on intent type:
     ```typescript
     showBuildMindmapButton={
       messages.length > 0 && 
       messages[messages.length - 1].sender === 'assistant' && 
       (
         (messages[messages.length - 1].responseType?.type === 'teleological') ||
         (messages[messages.length - 1].suggestedActions?.some(
           action => action.type === 'create_mindmap'
         ))
       )
     }
     ```
   - Enhanced the `Message` interface in `useChat.ts` to properly handle response types:
     ```typescript
     interface Message {
       // existing properties
       responseType?: {
         type: string;
         requiresMindmap: boolean;
       };
     }
     ```
   - Updated message creation to properly extract intent data from API responses:
     ```typescript
     responseType: {
       type: data.content?.intent || 'miscellaneous',
       requiresMindmap: data.content?.intent === 'teleological'
     }
     ```
   - Implemented auto-generation of `create_mindmap` actions for teleological responses

3. **UI Improvements**
   - Added proper timestamp display and sender labels to each message
   - Improved text styling for better readability
   - Ensured consistent button styling across the application

These changes ensure that the "Build Mindmap" button appears properly when a teleological intent is detected in the LLM response, providing a seamless experience for mindmap creation.

### Backend Code Modularization (May 3, 2025)

Significant improvements to backend architecture and maintainability:

1. **API Route Modularization**
   - Restructured the monolithic `llm.py` file (800+ lines) into focused modules
   - Created a proper modular architecture with these components:
     - `models/mbcp_models.py`: Pydantic data models for MBCP structures
     - `services/prompt_service.py`: Prompt template handling
     - `services/response_processor.py`: LLM response processing
     - `services/openai_service.py`: OpenAI API integration
     - `schemas/mbcp_schemas.py`: JSON schemas for function calling

2. **Dependency Management**
   - Updated `run_setup.ps1` with missing dependencies
   - Added explicit package installations for `pydantic-settings`, `python-dotenv`, etc.
   - Improved startup script error handling

3. **Code Quality Improvements**
   - Fixed indentation errors causing runtime failures
   - Enhanced error logging for better debugging
   - Improved type annotations throughout the codebase
   - Created proper module initialization structure

These changes significantly improve maintainability, reduce complexity, and fix runtime errors that were preventing the application from starting correctly.

### Intention Visualization Diagnosis (May 2025)

Analysis and work in progress on node intention visualization:

1. **Issue Identification**
   - Identified that the backend correctly provides node intention data
   - Frontend `NodeRenderer.tsx` contains styling for different intentions
   - Rendering inconsistency prevents visualization of intention-based styling
   - Potential issue in metadata extraction during node creation

2. **Investigation Findings**
   - Frontend correctly receives node intentions (factual, exploratory, teleological)
   - `NodeRenderer.tsx` has color mappings (`INTENT_COLORS`) for styling
   - Metadata processing during node creation needs improvement
   - Connection between LLM intention response and node rendering requires optimization

3. **Proposed Solutions**
   - Verify metadata extraction in `processMindmapData` function
   - Ensure proper metadata preservation during node creation
   - Update visualization to correctly reflect intention-based styles
   - Add visual indicators for better intention visibility

This work is ongoing with the goal of properly visualizing node intentions to enhance the mindmap's communication of different thinking types.

### Export/Import Functionality (April 24, 2025)

Added the ability to export projects to JSON files and import them from disk:

1. **Project Export**
   - Enhanced MindMapStore with `exportProjectToFile` method
   - Added file generation and download functionality
   - Implemented metadata inclusion for better import experience
   - Created clear file naming convention with project name and date

2. **Project Import**
   - Added file input handling and validation
   - Implemented project data parsing and validation
   - Added conflict resolution for duplicate project names
   - Created user feedback for import success/failure

### CrewAI Integration (April 20, 2025)

Integrated CrewAI framework for enhanced multi-agent capabilities:

1. **Backend Structure**
   - Added `/backend/agentCrew/` directory
   - Implemented agent definition files
   - Created tool configuration for specialized capabilities
   - Enhanced API endpoints for agent communication

2. **Frontend Integration**
   - Added CrewAI toggle in GovernanceChatDialog
   - Implemented settings panel for agent configuration
   - Enhanced message handling for agent responses
   - Added visual indicators for active agents

### Six Thinking Hats Implementation (April 15, 2025)

Enhanced NodeDialog with Six Thinking Hats methodology:

1. **Tabbed Interface**
   - Added color-coded tabs for different thinking modes
   - Implemented specialized agent communication for each tab
   - Created visual design that reinforces the hat metaphor
   - Added appropriate icons and color coding

2. **Agent Specialization**
   - Added specialized prompt templates for each hat
   - Implemented context awareness across tab interactions
   - Created tailored suggestions based on thinking mode
   - Enhanced suggestion quality through specialized knowledge

### Node Dialog Interface Optimization (March 23, 2025)

1. **Improved Header Structure**
   - Node path index (e.g., 1.1, 1.2) now displayed in header with improved styling
   - Node ID moved to right side for better layout balance
   - Consistent typography and spacing throughout

2. **Space Optimization**
   - Inline title label to reduce vertical space consumption
   - Optimized padding and margins throughout dialog
   - More compact layout while maintaining readability

3. **Six Thinking Hats Integration**
   - Tabbed interface for different thinking modes based on de Bono's methodology
   - Color-coded tabs for quick identification:
     - White Agent: Facts and information
     - Red Agent: Emotions and feelings
     - Black Agent: Caution and critical judgment
     - Yellow Agent: Benefits and optimism
     - Green Agent: Creative alternatives
   - Blue Hat functionality handled by main governance agent
   - Each tab prepared for specialized agent interactions

This redesign lays the groundwork for advanced multi-agent interactions while improving the core node editing experience.

#### Mindmap Creation Process Analysis (May 2025)

1. **Workflow Analysis**
   - Compared manual and automatic mindmap creation processes
   - Identified strengths and weaknesses of each approach
   - Analyzed data structure utilization
   - Evaluated connection management systems

2. **Data Structure Assessment**
   - Identified rich JSON structure from LLM responses
   - Analyzed current data utilization gaps
   - Evaluated metadata handling
   - Assessed connection tracking system

3. **Areas for Improvement**
   - Data transformation layer needed
   - Enhanced node tracking required
   - Better connection management
   - Improved metadata utilization

4. **Implementation Plan**
   - Create proper transformation pipeline
   - Enhance node tracking system
   - Improve connection management
   - Implement metadata preservation

## Current Workflow

# MindBack Application Workflow

## Table of Contents
1. [System Overview](#system-overview)
2. [Key Workflows](#key-workflows)
   - [Application Startup](#application-startup)
   - [Mindmap Creation](#mindmap-creation)
   - [Node Interaction](#node-interaction)
   - [LLM Integration](#llm-integration)
   - [Project Management](#project-management)
   - [Export/Import](#export-import)
3. [Component Architecture](#component-architecture)
   - [MindMap Components](#mindmap-components)
   - [Dialog Components](#dialog-components)
   - [State Management](#state-management)
   - [AI Components](#ai-components)
4. [Development Guidelines](#development-guidelines)
5. [Recent Updates](#recent-updates)

## System Overview

MindBack is a mind-mapping application built with React and TypeScript that allows users to create, organize, and visualize hierarchical thought structures. The application features an interactive canvas where users can create nodes, establish connections between them, and organize ideas in a visual format.

The system consists of two main parts:
- **Frontend**: React/TypeScript application with custom components and state management
- **Backend**: FastAPI server providing LLM integration and data processing with CrewAI support

## Key Workflows

### Application Startup

```mermaid
graph TD
    A[run_setup.ps1] --> B[Activate Virtual Environment]
    B --> C[Install Dependencies]
    C --> D[Start Backend Server]
    D --> E[Start Frontend Server]
    E --> F[Initialize UI]
    F --> G[Load Prompt Registry]
    G --> H[Ready for User Input]
```

1. Environment setup via `run_setup.ps1`
2. Backend server launch (FastAPI)
3. Frontend server launch (Vite/React)
4. UI initialization and component mounting
5. Prompt registry loading for LLM interactions

### Mindmap Creation

#### User-Initiated Flow
1. User creates a new project or opens existing one via Project Management Dialog
2. User manually adds nodes and connections
3. User arranges nodes on the canvas
4. User edits node content via NodeDialog
5. User saves the project (automatic or manual)
6. Optional: User exports the project as a JSON file

#### LLM-Assisted Flow
1. User interacts with GovernanceChatDialog
2. User sends a query to the LLM
3. LLM responds with suggestions
4. User clicks "Build Mindmap" button (automatic or manual)
5. Dialog collapses and mindmap is generated
6. Nodes and connections are created based on LLM response
7. Layout is calculated and mindmap is rendered

#### JSON Processing and Mindmap Generation
```mermaid
flowchart TD
  A[Backend Response] --> B[Parse Outer JSON]
  B --> C[Extract Content Field]
  C --> D[Parse Inner JSON String]
  D --> E[Extract Mindmap Structure]
  E --> F[Create Root Node]
  F --> G[Process Child Nodes]
  G --> H[Update MindMapStore]
  H --> I[Calculate Layout]
  I --> J[Render Mindmap]
```

### Node Interaction

1. User selects a node
   - Single click: Node is selected but dialog doesn't open
   - Double click: Node is selected and dialog opens for editing
   - Enter key: Opens dialog for editing the selected node
2. Canvas interaction handled by useCanvasInteraction hook
   - `components/Canvas/hooks/useCanvasInteraction.ts`
   - Mouse event detection in handleMouseDown function
   - Double-click detection with timestamp comparison
   - Sets isEditing flag to true on double-click or Enter key
   - Enter key detection in handleKeyDown function
3. NodeDialogContainer responds to selection state
   - `components/Dialogs/NodeDialog/NodeDialogContainer.tsx`
   - Retrieves selected node data and isEditing flag from MindMapStore
   - Only opens dialog when isEditing is true
   - Displays node information in tabbed interface
4. NodeDialog renders with node data and specialized tabs
   - `components/Dialogs/NodeDialog/NodeDialog.tsx`
   - Node Info Tab: Displays node title and description for editing
   - Design Tab: Provides options for node appearance and styling
   - Hat Tabs: Interfaces for different thinking modes (Six Thinking Hats)
5. User edits node content and properties
   - Changes trigger onSave callback in NodeDialog
   - NodeDialogContainer updates node in MindMapStore
   - MindMapCanvas reflects changes immediately
6. User can request agent suggestions via thinking hat tabs
   - Each hat provides specialized assistance based on thinking mode
   - Suggestions are tailored to the active thinking mode
   - User can add suggested content or child nodes
7. Dialog closes on save or cancel
   - handleClose function resets state and isEditing flag
   - Canvas re-renders with updates from MindMapStore

### LLM Integration

#### Response Pattern Selection
The LLM uses a unified prompt to handle distinct interaction types:

```mermaid
graph TD
    A[User Input] --> B{Query Type}
    B -->|Factual Question| C[Direct Response]
    B -->|Strategic Issue| D[Mindmap Suggestion]
    B -->|Learning Concept| E[Mindmap Suggestion]
    B -->|Reflective Question| F{Complexity Check}
    F -->|Simple| G[Direct Response]
    F -->|Complex| H[Mindmap Suggestion]
```

1. **Simple Factual Answers**
   - Input: Direct questions
   - Process: Immediate response
   - Output: Concise, factual answer
   - Manual mindmap creation available

2. **Strategic Issues**
   - Input: Planning/decision questions
   - Process: Analysis suggestion
   - Output: Mindmap proposal
   - Structured breakdown

3. **Learning Breakdowns**
   - Input: Concept explanations
   - Process: Visual learning suggestion
   - Output: Mindmap proposal
   - Hierarchical knowledge structure

4. **Reflective Questions**
   - Input: Open-ended inquiries
   - Process: Complexity assessment
   - Output: Either direct response or mindmap
   - Based on depth needed

#### Six Thinking Hats Integration

Node dialog now includes tab-based interfaces for each thinking hat:

```mermaid
graph TD
    A[Node Dialog] --> B[Node Info Tab]
    A --> C[Design Tab]
    A --> D{Thinking Hats}
    D --> E[White Hat: Facts]
    D --> F[Red Hat: Emotions]
    D --> G[Black Hat: Criticism]
    D --> H[Yellow Hat: Benefits]
    D --> I[Green Hat: Creativity]
```

1. **Specialized Agent Interaction**
   - Each hat tab connects to specialized agent
   - Agent provides suggestions based on thinking mode
   - Suggestions are tailored to the specific hat's focus
   - User can apply suggestions to the node content

2. **Context Awareness**
   - Agents maintain awareness of current node content
   - Backend handles appropriate routing to specialized agents
   - Prompt templates are applied based on the active hat
   - Conversations preserve history within the tab session

#### Data Structure and Transformation Flow
```mermaid
graph TD
    A[LLM Response] --> B[MBCP Validation]
    B --> C[Data Structure Transform]
    C --> D[Node Creation]
    D --> E[Metadata Integration]
    E --> F[Connection Management]
    F --> G[Visual Rendering]
```

1. **LLM Response Processing**
   - Complete JSON structure with rich metadata
   - Node hierarchical relationships
   - Intent and agent information
   - Tags and action items

2. **Data Transformation Pipeline**
   - MBCP validation layer
   - Structure transformation
   - Metadata preservation
   - Relationship mapping

3. **Node Creation Process**
   - Proper node initialization
   - Metadata integration
   - Hierarchical structure maintenance
   - Connection tracking

4. **State Management**
   - Complete node tracking
   - Relationship preservation
   - Metadata storage
   - Connection state handling

### Project Management

The application provides comprehensive project management capabilities:

```mermaid
graph TD
    A[Project Management Dialog] --> B[Save Project]
    A --> C[Open Project]
    A --> D[Create New Project]
    A --> E[Export Project]
    A --> F[Import Project]
    B --> G[LocalStorage]
    C --> G
    E --> H[JSON File]
    F --> H
```

1. **Save Project Flow**
   - User clicks hamburger menu to open Project Dialog
   - User selects Save tab
   - User enters project name or selects existing project
   - System saves project to localStorage
   - System displays confirmation

2. **Open Project Flow**
   - User clicks hamburger menu to open Project Dialog
   - User selects Open tab
   - User selects project from list
   - System loads project from localStorage
   - Canvas refreshes with loaded project

3. **Create New Project Flow**
   - User clicks hamburger menu to open Project Dialog
   - User selects New tab
   - System prompts to save current project if changed
   - System creates new project with default root node
   - Canvas refreshes with new project

4. **Export/Import Flow**
   - User exports project to JSON file via Project Dialog
   - File is saved to downloads folder with standard naming
   - User can import the file later via Import option
   - System validates file format before importing

### Export/Import

The application supports export and import of mindmap projects:

1. **Export Process**
   - User selects Export option in Project Dialog
   - System retrieves project data from localStorage
   - System adds metadata (version, date, project name)
   - System creates JSON file and triggers download
   - File is saved with format: `mindback_ProjectName_YYYY-MM-DD.json`

2. **Import Process**
   - User selects Import option in Project Dialog
   - System opens file picker dialog
   - User selects previously exported JSON file
   - System validates file format and structure
   - System loads project into the application
   - If project name exists, user is prompted to resolve conflict

## Component Architecture

### MindMap Components
1. **MindMap** (`components/MindMap/index.tsx`)
   - Main container component
   - Manages canvas state and interactions
   - Integrates with MindMapStore
   - Handles autosave functionality

2. **MindMapCanvas** (`components/Canvas/MindMapCanvas.tsx`)
   - Renders the interactive canvas
   - Manages zoom and pan functionality
   - Handles node selection and interaction
   - Uses hooks for state and interaction management:
     - `useCanvasState.ts`
     - `useCanvasInteraction.ts`
     - `useCanvasRendering.ts`

3. **NodeRenderer** (`components/Canvas/NodeRenderer.tsx`)
   - Renders individual nodes on the canvas
   - Handles node appearance and styling
   - Displays node metadata (path, title)
   - Shows hat color indicators based on node settings

4. **ConnectionRenderer** (`components/Canvas/ConnectionRenderer.tsx`)
   - Renders connections between nodes
   - Handles connection styling and appearance
   - Manages connection interaction and selection
   - Supports different line styles and directions

### Dialog Components
1. **NodeDialog** (`components/Dialogs/NodeDialog/NodeDialog.tsx`)
   - Provides interface for editing nodes
   - Manages node properties and content
   - Provides tabbed interface for different functions:
     - Node Info Tab: Basic node information
     - Design Tab: Node appearance settings
     - Hat Tabs: Six Thinking Hats interfaces
   - Wrapped by NodeDialogContainer for state management

2. **GovernanceChatDialog** (`components/Dialogs/GovernanceChatDialog/index.tsx`)
   - Provides chat interface for LLM interaction
   - Draggable and resizable dialog
   - Manages message history and state
   - Integrates with LLM service
   - Components:
     - DialogHeader
     - MessageList (with "Build Mindmap" button)
     - MessageInput
     - ModelSelector
     - CrewAI toggle

3. **ProjectManagementDialog** (`components/MindMap/components/Dialogs/ProjectManagementDialog.tsx`)
   - Provides interface for project operations
   - Tabbed interface for different operations:
     - Save: Save current project
     - Open: Open existing project
     - New: Create new project
     - Export/Import: Import/export project files
   - Manages project list and operations
   - Integrates with MindMapStore for project data

### State Management
1. **MindMapStore** (`core/state/MindMapStore.ts`)
   - Zustand store for global state
   - Manages nodes and connections
   - Handles selection state (including isEditing flag)
   - Provides methods for node and connection operations
   - Manages project saving, loading, exporting, and importing

2. **ChatMemoryService** (`services/ChatMemoryService.ts`)
   - Manages chat history and context
   - Provides structured message storage
   - Handles message categorization and retrieval

### AI Components
1. **CrewAI Integration** (`backend/agentCrew/`)
   - Implements multi-agent system with CrewAI
   - Manages agent roles and responsibilities
   - Handles tool selection and specialization
   - Processes complex requests with specialized agents

2. **LLM Service** (`backend/services/llm_handler.py`)
   - Manages communication with LLM providers
   - Handles prompt formatting and response parsing
   - Provides interfaces for different LLM models
   - Implements contextual awareness for conversations

3. **Prompt Library** (`frontend/src/components/MindMap/components/Agents/Prompt_library/`)
   - Stores YAML-based prompt templates
   - Organizes prompts by thinking hat and function
   - Follows MBCP specification for prompt structure
   - Provides consistent interface for agent interactions

## Development Guidelines

### Component Structure
- Components exceeding 300 lines should be modularized
- Each module should focus on a specific responsibility
- Shared utilities and types should be extracted to separate files
- Maintain clear separation of concerns:
  - State management
  - UI rendering
  - Business logic
  - Side effects

### Module and Import Best Practices

Based on recent experiences with import issues, follow these guidelines:

1. **Path Consistency**
   - Use consistent casing (preferably lowercase) for all directory and file imports
   - Remember: Windows is case-insensitive but TypeScript module resolution is case-sensitive
   - Triple-check paths when moving files to new locations

2. **Relative Path Calculation**
   - Be precise with relative path calculations (`../`)
   - For deeply nested components, verify the exact directory depth
   - Consider using path aliases in tsconfig.json to avoid deep relative paths
   - Document complex import paths with comments when necessary

3. **Avoiding Circular Dependencies**
   - Implement wrapper patterns for complex components
   - Use dedicated implementation files (e.g., `Implementation.tsx`) to avoid circular references
   - Split component logic to ensure clean boundaries
   - Export only what's necessary from each module

4. **Component Interfaces**
   - Ensure prop names match exactly between component interfaces and usages
   - Use TypeScript interfaces to document expected props
   - Validate props with proper type checking and defaults
   - Keep interfaces and implementations in sync

5. **Import Extensions**
   - Avoid including `.ts`/`.tsx` extensions in import statements
   - Let TypeScript's module resolution handle file extensions
   - Be consistent with extension omission across the codebase

Following these practices will help prevent common import and module resolution issues that can cause difficult-to-debug runtime errors.

### State Management
- Use Zustand for global state
- Implement clean separation of concerns
- Minimize prop drilling through context/store usage
- Follow immutable update patterns

### AI Integration
- Store prompts in YAML files in the prompt library
- Communication between frontend and backend in JSON format
- Follow the MBCP specification for prompt structure
- Implement proper error handling for LLM failures

### Project Structure
- Organize components by feature area
- Keep related files together
- Use consistent naming conventions
- Document complex logic with comments

### Data Structure Guidelines
- Preserve complete LLM response data
- Maintain proper node relationships
- Implement robust validation
- Handle metadata consistently
- Ensure proper connection tracking

### State Management Guidelines
- Track all node relationships
- Preserve complete metadata
- Maintain connection states
- Handle updates efficiently
- Implement proper validation

## Recent Updates

### Build Mindmap Button Visualization Fix (May 2025)

Fixed the issue where the "Build Mindmap" button wasn't appearing for teleological intent responses:

1. **Root Cause Analysis**
   - Identified that the button was hardcoded to be hidden (`showBuildMindmapButton={false}`)
   - Found disconnections in the intent data flow from API response to UI
   - Discovered that the message object wasn't properly including response type information

2. **Implementation Fixes**
   - Modified `Implementation.tsx` to use conditional rendering based on intent type:
     ```typescript
     showBuildMindmapButton={
       messages.length > 0 && 
       messages[messages.length - 1].sender === 'assistant' && 
       (
         (messages[messages.length - 1].responseType?.type === 'teleological') ||
         (messages[messages.length - 1].suggestedActions?.some(
           action => action.type === 'create_mindmap'
         ))
       )
     }
     ```
   - Enhanced the `Message` interface in `useChat.ts` to properly handle response types:
     ```typescript
     interface Message {
       // existing properties
       responseType?: {
         type: string;
         requiresMindmap: boolean;
       };
     }
     ```
   - Updated message creation to properly extract intent data from API responses:
     ```typescript
     responseType: {
       type: data.content?.intent || 'miscellaneous',
       requiresMindmap: data.content?.intent === 'teleological'
     }
     ```
   - Implemented auto-generation of `create_mindmap` actions for teleological responses

3. **UI Improvements**
   - Added proper timestamp display and sender labels to each message
   - Improved text styling for better readability
   - Ensured consistent button styling across the application

These changes ensure that the "Build Mindmap" button appears properly when a teleological intent is detected in the LLM response, providing a seamless experience for mindmap creation.

### Backend Code Modularization (May 3, 2025)

Significant improvements to backend architecture and maintainability:

1. **API Route Modularization**
   - Restructured the monolithic `llm.py` file (800+ lines) into focused modules
   - Created a proper modular architecture with these components:
     - `models/mbcp_models.py`: Pydantic data models for MBCP structures
     - `services/prompt_service.py`: Prompt template handling
     - `services/response_processor.py`: LLM response processing
     - `services/openai_service.py`: OpenAI API integration
     - `schemas/mbcp_schemas.py`: JSON schemas for function calling

2. **Dependency Management**
   - Updated `run_setup.ps1` with missing dependencies
   - Added explicit package installations for `pydantic-settings`, `python-dotenv`, etc.
   - Improved startup script error handling

3. **Code Quality Improvements**
   - Fixed indentation errors causing runtime failures
   - Enhanced error logging for better debugging
   - Improved type annotations throughout the codebase
   - Created proper module initialization structure

These changes significantly improve maintainability, reduce complexity, and fix runtime errors that were preventing the application from starting correctly.

### Intention Visualization Diagnosis (May 2025)

Analysis and work in progress on node intention visualization:

1. **Issue Identification**
   - Identified that the backend correctly provides node intention data
   - Frontend `NodeRenderer.tsx` contains styling for different intentions
   - Rendering inconsistency prevents visualization of intention-based styling
   - Potential issue in metadata extraction during node creation

2. **Investigation Findings**
   - Frontend correctly receives node intentions (factual, exploratory, teleological)
   - `NodeRenderer.tsx` has color mappings (`INTENT_COLORS`) for styling
   - Metadata processing during node creation needs improvement
   - Connection between LLM intention response and node rendering requires optimization

3. **Proposed Solutions**
   - Verify metadata extraction in `processMindmapData` function
   - Ensure proper metadata preservation during node creation
   - Update visualization to correctly reflect intention-based styles
   - Add visual indicators for better intention visibility

This work is ongoing with the goal of properly visualizing node intentions to enhance the mindmap's communication of different thinking types.

### Export/Import Functionality (April 24, 2025)

Added the ability to export projects to JSON files and import them from disk:

1. **Project Export**
   - Enhanced MindMapStore with `exportProjectToFile` method
   - Added file generation and download functionality
   - Implemented metadata inclusion for better import experience
   - Created clear file naming convention with project name and date

2. **Project Import**
   - Added file input handling and validation
   - Implemented project data parsing and validation
   - Added conflict resolution for duplicate project names
   - Created user feedback for import success/failure

### CrewAI Integration (April 20, 2025)

Integrated CrewAI framework for enhanced multi-agent capabilities:

1. **Backend Structure**
   - Added `/backend/agentCrew/` directory
   - Implemented agent definition files
   - Created tool configuration for specialized capabilities
   - Enhanced API endpoints for agent communication

2. **Frontend Integration**
   - Added CrewAI toggle in GovernanceChatDialog
   - Implemented settings panel for agent configuration
   - Enhanced message handling for agent responses
   - Added visual indicators for active agents

### Six Thinking Hats Implementation (April 15, 2025)

Enhanced NodeDialog with Six Thinking Hats methodology:

1. **Tabbed Interface**
   - Added color-coded tabs for different thinking modes
   - Implemented specialized agent communication for each tab
   - Created visual design that reinforces the hat metaphor
   - Added appropriate icons and color coding

2. **Agent Specialization**
   - Added specialized prompt templates for each hat
   - Implemented context awareness across tab interactions
   - Created tailored suggestions based on thinking mode
   - Enhanced suggestion quality through specialized knowledge

### Node Dialog Interface Optimization (March 23, 2025)

1. **Improved Header Structure**
   - Node path index (e.g., 1.1, 1.2) now displayed in header with improved styling
   - Node ID moved to right side for better layout balance
   - Consistent typography and spacing throughout

2. **Space Optimization**
   - Inline title label to reduce vertical space consumption
   - Optimized padding and margins throughout dialog
   - More compact layout while maintaining readability

3. **Six Thinking Hats Integration**
   - Tabbed interface for different thinking modes based on de Bono's methodology
   - Color-coded tabs for quick identification:
     - White Agent: Facts and information
     - Red Agent: Emotions and feelings
     - Black Agent: Caution and critical judgment
     - Yellow Agent: Benefits and optimism
     - Green Agent: Creative alternatives
   - Blue Hat functionality handled by main governance agent
   - Each tab prepared for specialized agent interactions

This redesign lays the groundwork for advanced multi-agent interactions while improving the core node editing experience.

#### Mindmap Creation Process Analysis (May 2025)

1. **Workflow Analysis**
   - Compared manual and automatic mindmap creation processes
   - Identified strengths and weaknesses of each approach
   - Analyzed data structure utilization
   - Evaluated connection management systems

2. **Data Structure Assessment**
   - Identified rich JSON structure from LLM responses
   - Analyzed current data utilization gaps
   - Evaluated metadata handling
   - Assessed connection tracking system

3. **Areas for Improvement**
   - Data transformation layer needed
   - Enhanced node tracking required
   - Better connection management
   - Improved metadata utilization

4. **Implementation Plan**
   - Create proper transformation pipeline
   - Enhance node tracking system
   - Improve connection management
   - Implement metadata preservation

## Development Guidelines

## Development Guidelines

## Project Workflow

# Project Workflow
- Keep the following up-to-date:
  - C:\Users\<USER>\Documents\VSCode\MindBack_Backup\MindBack_V1\0StageOfDev.md
  - C:\Users\<USER>\Documents\VSCode\MindBack_Backup\MindBack_V1\WORKFLOW.md

## Additional Workflow Information

# MindBack Application Workflow

## Table of Contents
1. [System Overview](#system-overview)
2. [Key Workflows](#key-workflows)
   - [Application Startup](#application-startup)
   - [Mindmap Creation](#mindmap-creation)
   - [Node Interaction](#node-interaction)
   - [LLM Integration](#llm-integration)
   - [Project Management](#project-management)
   - [Export/Import](#export-import)
3. [Component Architecture](#component-architecture)
   - [MindMap Components](#mindmap-components)
   - [Dialog Components](#dialog-components)
   - [State Management](#state-management)
   - [AI Components](#ai-components)
4. [Development Guidelines](#development-guidelines)
5. [Recent Updates](#recent-updates)

## System Overview

MindBack is a mind-mapping application built with React and TypeScript that allows users to create, organize, and visualize hierarchical thought structures. The application features an interactive canvas where users can create nodes, establish connections between them, and organize ideas in a visual format.

The system consists of two main parts:
- **Frontend**: React/TypeScript application with custom components and state management
- **Backend**: FastAPI server providing LLM integration and data processing with CrewAI support

## Key Workflows

### Application Startup

```mermaid
graph TD
    A[run_setup.ps1] --> B[Activate Virtual Environment]
    B --> C[Install Dependencies]
    C --> D[Start Backend Server]
    D --> E[Start Frontend Server]
    E --> F[Initialize UI]
    F --> G[Load Prompt Registry]
    G --> H[Ready for User Input]
```

1. Environment setup via `run_setup.ps1`
2. Backend server launch (FastAPI)
3. Frontend server launch (Vite/React)
4. UI initialization and component mounting
5. Prompt registry loading for LLM interactions

### Mindmap Creation

#### User-Initiated Flow
1. User creates a new project or opens existing one via Project Management Dialog
2. User manually adds nodes and connections
3. User arranges nodes on the canvas
4. User edits node content via NodeDialog
5. User saves the project (automatic or manual)
6. Optional: User exports the project as a JSON file

#### LLM-Assisted Flow
1. User interacts with GovernanceChatDialog
2. User sends a query to the LLM
3. LLM responds with suggestions
4. User clicks "Build Mindmap" button (automatic or manual)
5. Dialog collapses and mindmap is generated
6. Nodes and connections are created based on LLM response
7. Layout is calculated and mindmap is rendered

#### JSON Processing and Mindmap Generation
```mermaid
flowchart TD
  A[Backend Response] --> B[Parse Outer JSON]
  B --> C[Extract Content Field]
  C --> D[Parse Inner JSON String]
  D --> E[Extract Mindmap Structure]
  E --> F[Create Root Node]
  F --> G[Process Child Nodes]
  G --> H[Update MindMapStore]
  H --> I[Calculate Layout]
  I --> J[Render Mindmap]
```

### Node Interaction

1. User selects a node
   - Single click: Node is selected but dialog doesn't open
   - Double click: Node is selected and dialog opens for editing
   - Enter key: Opens dialog for editing the selected node
2. Canvas interaction handled by useCanvasInteraction hook
   - `components/Canvas/hooks/useCanvasInteraction.ts`
   - Mouse event detection in handleMouseDown function
   - Double-click detection with timestamp comparison
   - Sets isEditing flag to true on double-click or Enter key
   - Enter key detection in handleKeyDown function
3. NodeDialogContainer responds to selection state
   - `components/Dialogs/NodeDialog/NodeDialogContainer.tsx`
   - Retrieves selected node data and isEditing flag from MindMapStore
   - Only opens dialog when isEditing is true
   - Displays node information in tabbed interface
4. NodeDialog renders with node data and specialized tabs
   - `components/Dialogs/NodeDialog/NodeDialog.tsx`
   - Node Info Tab: Displays node title and description for editing
   - Design Tab: Provides options for node appearance and styling
   - Hat Tabs: Interfaces for different thinking modes (Six Thinking Hats)
5. User edits node content and properties
   - Changes trigger onSave callback in NodeDialog
   - NodeDialogContainer updates node in MindMapStore
   - MindMapCanvas reflects changes immediately
6. User can request agent suggestions via thinking hat tabs
   - Each hat provides specialized assistance based on thinking mode
   - Suggestions are tailored to the active thinking mode
   - User can add suggested content or child nodes
7. Dialog closes on save or cancel
   - handleClose function resets state and isEditing flag
   - Canvas re-renders with updates from MindMapStore

### LLM Integration

#### Response Pattern Selection
The LLM uses a unified prompt to handle distinct interaction types:

```mermaid
graph TD
    A[User Input] --> B{Query Type}
    B -->|Factual Question| C[Direct Response]
    B -->|Strategic Issue| D[Mindmap Suggestion]
    B -->|Learning Concept| E[Mindmap Suggestion]
    B -->|Reflective Question| F{Complexity Check}
    F -->|Simple| G[Direct Response]
    F -->|Complex| H[Mindmap Suggestion]
```

1. **Simple Factual Answers**
   - Input: Direct questions
   - Process: Immediate response
   - Output: Concise, factual answer
   - Manual mindmap creation available

2. **Strategic Issues**
   - Input: Planning/decision questions
   - Process: Analysis suggestion
   - Output: Mindmap proposal
   - Structured breakdown

3. **Learning Breakdowns**
   - Input: Concept explanations
   - Process: Visual learning suggestion
   - Output: Mindmap proposal
   - Hierarchical knowledge structure

4. **Reflective Questions**
   - Input: Open-ended inquiries
   - Process: Complexity assessment
   - Output: Either direct response or mindmap
   - Based on depth needed

#### Six Thinking Hats Integration

Node dialog now includes tab-based interfaces for each thinking hat:

```mermaid
graph TD
    A[Node Dialog] --> B[Node Info Tab]
    A --> C[Design Tab]
    A --> D{Thinking Hats}
    D --> E[White Hat: Facts]
    D --> F[Red Hat: Emotions]
    D --> G[Black Hat: Criticism]
    D --> H[Yellow Hat: Benefits]
    D --> I[Green Hat: Creativity]
```

1. **Specialized Agent Interaction**
   - Each hat tab connects to specialized agent
   - Agent provides suggestions based on thinking mode
   - Suggestions are tailored to the specific hat's focus
   - User can apply suggestions to the node content

2. **Context Awareness**
   - Agents maintain awareness of current node content
   - Backend handles appropriate routing to specialized agents
   - Prompt templates are applied based on the active hat
   - Conversations preserve history within the tab session

#### Data Structure and Transformation Flow
```mermaid
graph TD
    A[LLM Response] --> B[MBCP Validation]
    B --> C[Data Structure Transform]
    C --> D[Node Creation]
    D --> E[Metadata Integration]
    E --> F[Connection Management]
    F --> G[Visual Rendering]
```

1. **LLM Response Processing**
   - Complete JSON structure with rich metadata
   - Node hierarchical relationships
   - Intent and agent information
   - Tags and action items

2. **Data Transformation Pipeline**
   - MBCP validation layer
   - Structure transformation
   - Metadata preservation
   - Relationship mapping

3. **Node Creation Process**
   - Proper node initialization
   - Metadata integration
   - Hierarchical structure maintenance
   - Connection tracking

4. **State Management**
   - Complete node tracking
   - Relationship preservation
   - Metadata storage
   - Connection state handling

### Project Management

The application provides comprehensive project management capabilities:

```mermaid
graph TD
    A[Project Management Dialog] --> B[Save Project]
    A --> C[Open Project]
    A --> D[Create New Project]
    A --> E[Export Project]
    A --> F[Import Project]
    B --> G[LocalStorage]
    C --> G
    E --> H[JSON File]
    F --> H
```

1. **Save Project Flow**
   - User clicks hamburger menu to open Project Dialog
   - User selects Save tab
   - User enters project name or selects existing project
   - System saves project to localStorage
   - System displays confirmation

2. **Open Project Flow**
   - User clicks hamburger menu to open Project Dialog
   - User selects Open tab
   - User selects project from list
   - System loads project from localStorage
   - Canvas refreshes with loaded project

3. **Create New Project Flow**
   - User clicks hamburger menu to open Project Dialog
   - User selects New tab
   - System prompts to save current project if changed
   - System creates new project with default root node
   - Canvas refreshes with new project

4. **Export/Import Flow**
   - User exports project to JSON file via Project Dialog
   - File is saved to downloads folder with standard naming
   - User can import the file later via Import option
   - System validates file format before importing

### Export/Import

The application supports export and import of mindmap projects:

1. **Export Process**
   - User selects Export option in Project Dialog
   - System retrieves project data from localStorage
   - System adds metadata (version, date, project name)
   - System creates JSON file and triggers download
   - File is saved with format: `mindback_ProjectName_YYYY-MM-DD.json`

2. **Import Process**
   - User selects Import option in Project Dialog
   - System opens file picker dialog
   - User selects previously exported JSON file
   - System validates file format and structure
   - System loads project into the application
   - If project name exists, user is prompted to resolve conflict

## Component Architecture

### MindMap Components
1. **MindMap** (`components/MindMap/index.tsx`)
   - Main container component
   - Manages canvas state and interactions
   - Integrates with MindMapStore
   - Handles autosave functionality

2. **MindMapCanvas** (`components/Canvas/MindMapCanvas.tsx`)
   - Renders the interactive canvas
   - Manages zoom and pan functionality
   - Handles node selection and interaction
   - Uses hooks for state and interaction management:
     - `useCanvasState.ts`
     - `useCanvasInteraction.ts`
     - `useCanvasRendering.ts`

3. **NodeRenderer** (`components/Canvas/NodeRenderer.tsx`)
   - Renders individual nodes on the canvas
   - Handles node appearance and styling
   - Displays node metadata (path, title)
   - Shows hat color indicators based on node settings

4. **ConnectionRenderer** (`components/Canvas/ConnectionRenderer.tsx`)
   - Renders connections between nodes
   - Handles connection styling and appearance
   - Manages connection interaction and selection
   - Supports different line styles and directions

### Dialog Components
1. **NodeDialog** (`components/Dialogs/NodeDialog/NodeDialog.tsx`)
   - Provides interface for editing nodes
   - Manages node properties and content
   - Provides tabbed interface for different functions:
     - Node Info Tab: Basic node information
     - Design Tab: Node appearance settings
     - Hat Tabs: Six Thinking Hats interfaces
   - Wrapped by NodeDialogContainer for state management

2. **GovernanceChatDialog** (`components/Dialogs/GovernanceChatDialog/index.tsx`)
   - Provides chat interface for LLM interaction
   - Draggable and resizable dialog
   - Manages message history and state
   - Integrates with LLM service
   - Components:
     - DialogHeader
     - MessageList (with "Build Mindmap" button)
     - MessageInput
     - ModelSelector
     - CrewAI toggle

3. **ProjectManagementDialog** (`components/MindMap/components/Dialogs/ProjectManagementDialog.tsx`)
   - Provides interface for project operations
   - Tabbed interface for different operations:
     - Save: Save current project
     - Open: Open existing project
     - New: Create new project
     - Export/Import: Import/export project files
   - Manages project list and operations
   - Integrates with MindMapStore for project data

### State Management
1. **MindMapStore** (`core/state/MindMapStore.ts`)
   - Zustand store for global state
   - Manages nodes and connections
   - Handles selection state (including isEditing flag)
   - Provides methods for node and connection operations
   - Manages project saving, loading, exporting, and importing

2. **ChatMemoryService** (`services/ChatMemoryService.ts`)
   - Manages chat history and context
   - Provides structured message storage
   - Handles message categorization and retrieval

### AI Components
1. **CrewAI Integration** (`backend/agentCrew/`)
   - Implements multi-agent system with CrewAI
   - Manages agent roles and responsibilities
   - Handles tool selection and specialization
   - Processes complex requests with specialized agents

2. **LLM Service** (`backend/services/llm_handler.py`)
   - Manages communication with LLM providers
   - Handles prompt formatting and response parsing
   - Provides interfaces for different LLM models
   - Implements contextual awareness for conversations

3. **Prompt Library** (`frontend/src/components/MindMap/components/Agents/Prompt_library/`)
   - Stores YAML-based prompt templates
   - Organizes prompts by thinking hat and function
   - Follows MBCP specification for prompt structure
   - Provides consistent interface for agent interactions

## Development Guidelines

### Component Structure
- Components exceeding 300 lines should be modularized
- Each module should focus on a specific responsibility
- Shared utilities and types should be extracted to separate files
- Maintain clear separation of concerns:
  - State management
  - UI rendering
  - Business logic
  - Side effects

### State Management
- Use Zustand for global state
- Implement clean separation of concerns
- Minimize prop drilling through context/store usage
- Follow immutable update patterns

### AI Integration
- Store prompts in YAML files in the prompt library
- Communication between frontend and backend in JSON format
- Follow the MBCP specification for prompt structure
- Implement proper error handling for LLM failures

### Project Structure
- Organize components by feature area
- Keep related files together
- Use consistent naming conventions
- Document complex logic with comments

### Data Structure Guidelines
- Preserve complete LLM response data
- Maintain proper node relationships
- Implement robust validation
- Handle metadata consistently
- Ensure proper connection tracking

### State Management Guidelines
- Track all node relationships
- Preserve complete metadata
- Maintain connection states
- Handle updates efficiently
- Implement proper validation

## Recent Updates

### Backend Code Modularization (May 3, 2025)

Significant improvements to backend architecture and maintainability:

1. **API Route Modularization**
   - Restructured the monolithic `llm.py` file (800+ lines) into focused modules
   - Created a proper modular architecture with these components:
     - `models/mbcp_models.py`: Pydantic data models for MBCP structures
     - `services/prompt_service.py`: Prompt template handling
     - `services/response_processor.py`: LLM response processing
     - `services/openai_service.py`: OpenAI API integration
     - `schemas/mbcp_schemas.py`: JSON schemas for function calling

2. **Dependency Management**
   - Updated `run_setup.ps1` with missing dependencies
   - Added explicit package installations for `pydantic-settings`, `python-dotenv`, etc.
   - Improved startup script error handling

3. **Code Quality Improvements**
   - Fixed indentation errors causing runtime failures
   - Enhanced error logging for better debugging
   - Improved type annotations throughout the codebase
   - Created proper module initialization structure

These changes significantly improve maintainability, reduce complexity, and fix runtime errors that were preventing the application from starting correctly.

### Intention Visualization Diagnosis (May 2025)

Analysis and work in progress on node intention visualization:

1. **Issue Identification**
   - Identified that the backend correctly provides node intention data
   - Frontend `NodeRenderer.tsx` contains styling for different intentions
   - Rendering inconsistency prevents visualization of intention-based styling
   - Potential issue in metadata extraction during node creation

2. **Investigation Findings**
   - Frontend correctly receives node intentions (factual, exploratory, teleological)
   - `NodeRenderer.tsx` has color mappings (`INTENT_COLORS`) for styling
   - Metadata processing during node creation needs improvement
   - Connection between LLM intention response and node rendering requires optimization

3. **Proposed Solutions**
   - Verify metadata extraction in `processMindmapData` function
   - Ensure proper metadata preservation during node creation
   - Update visualization to correctly reflect intention-based styles
   - Add visual indicators for better intention visibility

This work is ongoing with the goal of properly visualizing node intentions to enhance the mindmap's communication of different thinking types.

### Export/Import Functionality (April 24, 2025)

Added the ability to export projects to JSON files and import them from disk:

1. **Project Export**
   - Enhanced MindMapStore with `exportProjectToFile` method
   - Added file generation and download functionality
   - Implemented metadata inclusion for better import experience
   - Created clear file naming convention with project name and date

2. **Project Import**
   - Added file input handling and validation
   - Implemented project data parsing and validation
   - Added conflict resolution for duplicate project names
   - Created user feedback for import success/failure

### CrewAI Integration (April 20, 2025)

Integrated CrewAI framework for enhanced multi-agent capabilities:

1. **Backend Structure**
   - Added `/backend/agentCrew/` directory
   - Implemented agent definition files
   - Created tool configuration for specialized capabilities
   - Enhanced API endpoints for agent communication

2. **Frontend Integration**
   - Added CrewAI toggle in GovernanceChatDialog
   - Implemented settings panel for agent configuration
   - Enhanced message handling for agent responses
   - Added visual indicators for active agents

### Six Thinking Hats Implementation (April 15, 2025)

Enhanced NodeDialog with Six Thinking Hats methodology:

1. **Tabbed Interface**
   - Added color-coded tabs for different thinking modes
   - Implemented specialized agent communication for each tab
   - Created visual design that reinforces the hat metaphor
   - Added appropriate icons and color coding

2. **Agent Specialization**
   - Added specialized prompt templates for each hat
   - Implemented context awareness across tab interactions
   - Created tailored suggestions based on thinking mode
   - Enhanced suggestion quality through specialized knowledge

### Node Dialog Interface Optimization (March 23, 2025)

1. **Improved Header Structure**
   - Node path index (e.g., 1.1, 1.2) now displayed in header with improved styling
   - Node ID moved to right side for better layout balance
   - Consistent typography and spacing throughout

2. **Space Optimization**
   - Inline title label to reduce vertical space consumption
   - Optimized padding and margins throughout dialog
   - More compact layout while maintaining readability

3. **Six Thinking Hats Integration**
   - Tabbed interface for different thinking modes based on de Bono's methodology
   - Color-coded tabs for quick identification:
     - White Agent: Facts and information
     - Red Agent: Emotions and feelings
     - Black Agent: Caution and critical judgment
     - Yellow Agent: Benefits and optimism
     - Green Agent: Creative alternatives
   - Blue Hat functionality handled by main governance agent
   - Each tab prepared for specialized agent interactions

This redesign lays the groundwork for advanced multi-agent interactions while improving the core node editing experience.

#### Mindmap Creation Process Analysis (May 2025)

1. **Workflow Analysis**
   - Compared manual and automatic mindmap creation processes
   - Identified strengths and weaknesses of each approach
   - Analyzed data structure utilization
   - Evaluated connection management systems

2. **Data Structure Assessment**
   - Identified rich JSON structure from LLM responses
   - Analyzed current data utilization gaps
   - Evaluated metadata handling
   - Assessed connection tracking system

3. **Areas for Improvement**
   - Data transformation layer needed
   - Enhanced node tracking required
   - Better connection management
   - Improved metadata utilization

4. **Implementation Plan**
   - Create proper transformation pipeline
   - Enhance node tracking system
   - Improve connection management
   - Implement metadata preservation

## Build Pipeline

# Build Pipeline

## Development
1. ESLint + Prettier enforcement
2. Dependency validation (dependency-cruiser)
3. Type checking (TypeScript)
4. Unit test execution

## Production
1. Code optimization
2. Tree shaking
3. Bundle analysis
4. Performance benchmarking
5. Automated deployment

## Development Guidelines

## Development Guidelines

## ChatFork Implementation Plan

## Implementation Plan

## Mindmap Implementation Plan

## Implementation Plan (Revised)

## Recently Fixed Issues

### Recently Fixed Issues

## Mindmap Implementation Priorities

## Priority Implementation Order

1. **Immediate Fixes**
   - [ ] Stable layout algorithm to prevent chaotic repositioning
   - [ ] Position persistence for manually moved nodes
   - [ ] Collision detection and avoidance

2. **Core Functionality**
   - [ ] Enhanced node differentiation between manual and automatic nodes
   - [ ] Improved manual node UI
   - [ ] Connection management improvements

3. **User Experience**
   - [ ] Smart canvas management
   - [ ] Undo/redo system
   - [ ] Keyboard navigation enhancements

4. **Advanced Features**
   - [ ] Node merging functionality
   - [ ] Export and integration options
   - [ ] Collaboration features

## Additional Workflow Description

### Describing the workflow

as we will allow the user to ask simple questuins in the dialogue we need to have two prompts: 1. handling simple and sophisticated prompts, when sophisticated ask the user if we shall create a mindmap structure (this was all implemented but got wreckt) , if yes, then using the second prompt to build the mindmap, by a. deciding on an appropriate project naming to go into the title of the main node dialogbox, b. put the initial prompt of the user into description of the main node dialogbox, c. have the llm generate the rest of the nodes, each with their title and description to go into the respective title and description of the sub nodes... this was implemented and this i want to have installed again. help modify the yaml file initiate_mindmap accordingly and to adjust the code to follow this workflow


ok the lates reply is better, however here is a snippet of the reply: "That's great to hear! Entering the Swedish market for road maintenance can be a strategic move. Here are some steps you can consider to enter the market:

1. Research the Market: Understand the current road maintenance industry in Sweden, including key players, market size, growth trends, and regulatory environment.

2. Identify Opportunities: Explore specific opportunities within the road maintenance sector in Sweden, such as innovative technologies, sustainable practices, or unmet needs...." the prompt structure is setup in such a way (line 36-41), that key considerations are separately listed. These are: 1. Research the Market, 2. Identify Opportunities, ... and shall be listed in the reply to the user together with the prompt in line 44 and the green button (must be somewhere in the code already). Upon clicking the green button, the dialoguebox shall reduce in hight (300) and the bottom line shall align with the bottom line of the screen making place for the mindmapping. The mindmapping structure, inclusive numbering is provided in the completion of the llm in json, where the first part of the structure is the topic itself to represent the main node

once a mindmap is created there shall be a dropselectionbox in GovernanceChatDialog right next to the selection box of llm. this box shall include all nodes in the mindmap (index), when a new node is added the index of the new node shall be added to the selectionbox. When double clicking on a node the selectionbox shall automatically select the index of the node, also when selecting a node index in the selectionbox the node dialogbox shall open. further in the dialog field a user post shall be inserted "user select node: [index]". Be sure to consider the event that the user is loading a saved mindmap aswell. default in the new box shall be "Gov" and when closing the node "Gov" shall be selected. Further the box shall be leftalignd next to the llm model 

ChatFork: mindmap is one of several mindback tools. The next MB-Tool is the ChatFork. The Chatfork is used by the governance agent when a complex theme is explained. Like "what is the history of the internet" being explained over 10 points each containing several context subpoints. Each point and subpoint are organized in text fields under eachother. The user is enabled to address each point and subsection and fork comments, utterances and questions to each part and thereby initiate a subchat, which in turn can be forked in to another subchat, and so on. Based on the input from the user the initiation_Agent workflow selects the tool ChatForkTool.yaml and have the llm return the completion as described in the yaml file. mindback build the completion in the ui. The user interact with the displayed structur by clicking parts of the structure, pressing tab fork a dialog textfield for the user to follow a tread with statements or questions

well 1. agentic selection of tool, 2. prompting the llm to get the content for the selected tool, e.g. if the user states "we want to make 20% of our revenue in sweden in three years" the agents shall detect this as a purpose that require breakdown of the task and therefore selects the mindmap tool. based on this selection the governing agent or whoever shall make a prompt to the llm to receive back in the completion issues to be considered for the purpose in a format which allows mindback to build the mindmap structure. In the same token  for the initiation prompt "What is democracy", the agentic selection is tool for a verbose explanation of the topic, which allow the user to interact at each level of the returned text with statements and questions, therefore the agentic selection is  the ChatForktool and the government agent has to prompt the llm to give, e.g. a verbose completion with 10 points covering subtopics of democracy in such a structure that it enables mindback to display the completion in the ChatFork tool for further interaction with the user in terms of enabling the user to fork a dialog at any line of the description of the topic.