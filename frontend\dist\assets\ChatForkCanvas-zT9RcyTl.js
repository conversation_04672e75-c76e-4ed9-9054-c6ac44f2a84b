import{g as wi,j as q,b as et,c as Yn,E as qn}from"./index-v114B1Cq.js";function Js(e,t){const n={};return(e[e.length-1]===""?[...e,""]:e).join((n.padRight?" ":"")+","+(n.padLeft===!1?"":" ")).trim()}const Zs=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,eo=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,to={};function pu(e,t){return(to.jsx?eo:Zs).test(e)}const no=/[ \t\n\f\r]/g;function ro(e){return typeof e=="object"?e.type==="text"?mu(e.value):!1:mu(e)}function mu(e){return e.replace(no,"")===""}let un=class{constructor(t,n,r){this.normal=n,this.property=t,r&&(this.space=r)}};un.prototype.normal={};un.prototype.property={};un.prototype.space=void 0;function Mi(e,t){const n={},r={};for(const u of e)Object.assign(n,u.property),Object.assign(r,u.normal);return new un(n,r,t)}function fr(e){return e.toLowerCase()}let Ie=class{constructor(t,n){this.attribute=n,this.property=t}};Ie.prototype.attribute="";Ie.prototype.booleanish=!1;Ie.prototype.boolean=!1;Ie.prototype.commaOrSpaceSeparated=!1;Ie.prototype.commaSeparated=!1;Ie.prototype.defined=!1;Ie.prototype.mustUseProperty=!1;Ie.prototype.number=!1;Ie.prototype.overloadedBoolean=!1;Ie.prototype.property="";Ie.prototype.spaceSeparated=!1;Ie.prototype.space=void 0;let uo=0;const X=ht(),de=ht(),hr=ht(),R=ht(),se=ht(),kt=ht(),De=ht();function ht(){return 2**++uo}const dr=Object.freeze(Object.defineProperty({__proto__:null,boolean:X,booleanish:de,commaOrSpaceSeparated:De,commaSeparated:kt,number:R,overloadedBoolean:hr,spaceSeparated:se},Symbol.toStringTag,{value:"Module"})),jn=Object.keys(dr);let Dr=class extends Ie{constructor(t,n,r,u){let a=-1;if(super(t,n),Eu(this,"space",u),typeof r=="number")for(;++a<jn.length;){const i=jn[a];Eu(this,jn[a],(r&dr[i])===dr[i])}}};Dr.prototype.defined=!0;function Eu(e,t,n){n&&(e[t]=n)}function Nt(e){const t={},n={};for(const[r,u]of Object.entries(e.properties)){const a=new Dr(r,e.transform(e.attributes||{},r),u,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(a.mustUseProperty=!0),t[r]=a,n[fr(r)]=r,n[fr(a.attribute)]=r}return new un(t,n,e.space)}const Bi=Nt({properties:{ariaActiveDescendant:null,ariaAtomic:de,ariaAutoComplete:null,ariaBusy:de,ariaChecked:de,ariaColCount:R,ariaColIndex:R,ariaColSpan:R,ariaControls:se,ariaCurrent:null,ariaDescribedBy:se,ariaDetails:null,ariaDisabled:de,ariaDropEffect:se,ariaErrorMessage:null,ariaExpanded:de,ariaFlowTo:se,ariaGrabbed:de,ariaHasPopup:null,ariaHidden:de,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:se,ariaLevel:R,ariaLive:null,ariaModal:de,ariaMultiLine:de,ariaMultiSelectable:de,ariaOrientation:null,ariaOwns:se,ariaPlaceholder:null,ariaPosInSet:R,ariaPressed:de,ariaReadOnly:de,ariaRelevant:null,ariaRequired:de,ariaRoleDescription:se,ariaRowCount:R,ariaRowIndex:R,ariaRowSpan:R,ariaSelected:de,ariaSetSize:R,ariaSort:null,ariaValueMax:R,ariaValueMin:R,ariaValueNow:R,ariaValueText:null,role:null},transform(e,t){return t==="role"?t:"aria-"+t.slice(4).toLowerCase()}});function Fi(e,t){return t in e?e[t]:t}function vi(e,t){return Fi(e,t.toLowerCase())}const io=Nt({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:kt,acceptCharset:se,accessKey:se,action:null,allow:null,allowFullScreen:X,allowPaymentRequest:X,allowUserMedia:X,alt:null,as:null,async:X,autoCapitalize:null,autoComplete:se,autoFocus:X,autoPlay:X,blocking:se,capture:null,charSet:null,checked:X,cite:null,className:se,cols:R,colSpan:null,content:null,contentEditable:de,controls:X,controlsList:se,coords:R|kt,crossOrigin:null,data:null,dateTime:null,decoding:null,default:X,defer:X,dir:null,dirName:null,disabled:X,download:hr,draggable:de,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:X,formTarget:null,headers:se,height:R,hidden:hr,high:R,href:null,hrefLang:null,htmlFor:se,httpEquiv:se,id:null,imageSizes:null,imageSrcSet:null,inert:X,inputMode:null,integrity:null,is:null,isMap:X,itemId:null,itemProp:se,itemRef:se,itemScope:X,itemType:se,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:X,low:R,manifest:null,max:null,maxLength:R,media:null,method:null,min:null,minLength:R,multiple:X,muted:X,name:null,nonce:null,noModule:X,noValidate:X,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:X,optimum:R,pattern:null,ping:se,placeholder:null,playsInline:X,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:X,referrerPolicy:null,rel:se,required:X,reversed:X,rows:R,rowSpan:R,sandbox:se,scope:null,scoped:X,seamless:X,selected:X,shadowRootClonable:X,shadowRootDelegatesFocus:X,shadowRootMode:null,shape:null,size:R,sizes:null,slot:null,span:R,spellCheck:de,src:null,srcDoc:null,srcLang:null,srcSet:null,start:R,step:null,style:null,tabIndex:R,target:null,title:null,translate:null,type:null,typeMustMatch:X,useMap:null,value:de,width:R,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:se,axis:null,background:null,bgColor:null,border:R,borderColor:null,bottomMargin:R,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:X,declare:X,event:null,face:null,frame:null,frameBorder:null,hSpace:R,leftMargin:R,link:null,longDesc:null,lowSrc:null,marginHeight:R,marginWidth:R,noResize:X,noHref:X,noShade:X,noWrap:X,object:null,profile:null,prompt:null,rev:null,rightMargin:R,rules:null,scheme:null,scrolling:de,standby:null,summary:null,text:null,topMargin:R,valueType:null,version:null,vAlign:null,vLink:null,vSpace:R,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:X,disableRemotePlayback:X,prefix:null,property:null,results:R,security:null,unselectable:null},space:"html",transform:vi}),ao=Nt({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:De,accentHeight:R,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:R,amplitude:R,arabicForm:null,ascent:R,attributeName:null,attributeType:null,azimuth:R,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:R,by:null,calcMode:null,capHeight:R,className:se,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:R,diffuseConstant:R,direction:null,display:null,dur:null,divisor:R,dominantBaseline:null,download:X,dx:null,dy:null,edgeMode:null,editable:null,elevation:R,enableBackground:null,end:null,event:null,exponent:R,externalResourcesRequired:null,fill:null,fillOpacity:R,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:kt,g2:kt,glyphName:kt,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:R,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:R,horizOriginX:R,horizOriginY:R,id:null,ideographic:R,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:R,k:R,k1:R,k2:R,k3:R,k4:R,kernelMatrix:De,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:R,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:R,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:R,overlineThickness:R,paintOrder:null,panose1:null,path:null,pathLength:R,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:se,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:R,pointsAtY:R,pointsAtZ:R,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:De,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:De,rev:De,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:De,requiredFeatures:De,requiredFonts:De,requiredFormats:De,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:R,specularExponent:R,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:R,strikethroughThickness:R,string:null,stroke:null,strokeDashArray:De,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:R,strokeOpacity:R,strokeWidth:null,style:null,surfaceScale:R,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:De,tabIndex:R,tableValues:null,target:null,targetX:R,targetY:R,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:De,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:R,underlineThickness:R,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:R,values:null,vAlphabetic:R,vMathematical:R,vectorEffect:null,vHanging:R,vIdeographic:R,version:null,vertAdvY:R,vertOriginX:R,vertOriginY:R,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:R,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:Fi}),Ui=Nt({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(e,t){return"xlink:"+t.slice(5).toLowerCase()}}),Hi=Nt({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:vi}),zi=Nt({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(e,t){return"xml:"+t.slice(3).toLowerCase()}}),so={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"},oo=/[A-Z]/g,gu=/-[a-z]/g,lo=/^data[-\w.:]+$/i;function co(e,t){const n=fr(t);let r=t,u=Ie;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&n.slice(0,4)==="data"&&lo.test(t)){if(t.charAt(4)==="-"){const a=t.slice(5).replace(gu,ho);r="data"+a.charAt(0).toUpperCase()+a.slice(1)}else{const a=t.slice(4);if(!gu.test(a)){let i=a.replace(oo,fo);i.charAt(0)!=="-"&&(i="-"+i),t="data"+i}}u=Dr}return new u(r,t)}function fo(e){return"-"+e.toLowerCase()}function ho(e){return e.charAt(1).toUpperCase()}const po=Mi([Bi,io,Ui,Hi,zi],"html"),Rr=Mi([Bi,ao,Ui,Hi,zi],"svg");function mo(e){return e.join(" ").trim()}var Tt={},Vn,Tu;function Eo(){if(Tu)return Vn;Tu=1;var e=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,t=/\n/g,n=/^\s*/,r=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,u=/^:\s*/,a=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,i=/^[;\s]*/,o=/^\s+|\s+$/g,l=`
`,c="/",h="*",f="",m="comment",d="declaration";Vn=function(y,M){if(typeof y!="string")throw new TypeError("First argument must be a string");if(!y)return[];M=M||{};var k=1,F=1;function B(H){var v=H.match(t);v&&(k+=v.length);var z=H.lastIndexOf(l);F=~z?H.length-z:F+H.length}function j(){var H={line:k,column:F};return function(v){return v.position=new V(H),ne(),v}}function V(H){this.start=H,this.end={line:k,column:F},this.source=M.source}V.prototype.content=y;function x(H){var v=new Error(M.source+":"+k+":"+F+": "+H);if(v.reason=H,v.filename=M.source,v.line=k,v.column=F,v.source=y,!M.silent)throw v}function Q(H){var v=H.exec(y);if(v){var z=v[0];return B(z),y=y.slice(z.length),v}}function ne(){Q(n)}function ee(H){var v;for(H=H||[];v=A();)v!==!1&&H.push(v);return H}function A(){var H=j();if(!(c!=y.charAt(0)||h!=y.charAt(1))){for(var v=2;f!=y.charAt(v)&&(h!=y.charAt(v)||c!=y.charAt(v+1));)++v;if(v+=2,f===y.charAt(v-1))return x("End of comment missing");var z=y.slice(2,v-2);return F+=2,B(z),y=y.slice(v),F+=2,H({type:m,comment:z})}}function N(){var H=j(),v=Q(r);if(v){if(A(),!Q(u))return x("property missing ':'");var z=Q(a),ae=H({type:d,property:_(v[0].replace(e,f)),value:z?_(z[0].replace(e,f)):f});return Q(i),ae}}function L(){var H=[];ee(H);for(var v;v=N();)v!==!1&&(H.push(v),ee(H));return H}return ne(),L()};function _(y){return y?y.replace(o,f):f}return Vn}var bu;function go(){if(bu)return Tt;bu=1;var e=Tt&&Tt.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Tt,"__esModule",{value:!0}),Tt.default=n;var t=e(Eo());function n(r,u){var a=null;if(!r||typeof r!="string")return a;var i=(0,t.default)(r),o=typeof u=="function";return i.forEach(function(l){if(l.type==="declaration"){var c=l.property,h=l.value;o?u(c,h,l):h&&(a=a||{},a[c]=h)}}),a}return Tt}var Ft={},Au;function To(){if(Au)return Ft;Au=1,Object.defineProperty(Ft,"__esModule",{value:!0}),Ft.camelCase=void 0;var e=/^--[a-zA-Z0-9_-]+$/,t=/-([a-z])/g,n=/^[^-]+$/,r=/^-(webkit|moz|ms|o|khtml)-/,u=/^-(ms)-/,a=function(c){return!c||n.test(c)||e.test(c)},i=function(c,h){return h.toUpperCase()},o=function(c,h){return"".concat(h,"-")},l=function(c,h){return h===void 0&&(h={}),a(c)?c:(c=c.toLowerCase(),h.reactCompat?c=c.replace(u,o):c=c.replace(r,o),c.replace(t,i))};return Ft.camelCase=l,Ft}var vt,_u;function bo(){if(_u)return vt;_u=1;var e=vt&&vt.__importDefault||function(u){return u&&u.__esModule?u:{default:u}},t=e(go()),n=To();function r(u,a){var i={};return!u||typeof u!="string"||(0,t.default)(u,function(o,l){o&&l&&(i[(0,n.camelCase)(o,a)]=l)}),i}return r.default=r,vt=r,vt}var Ao=bo();const _o=wi(Ao),Yi=qi("end"),Pr=qi("start");function qi(e){return t;function t(n){const r=n&&n.position&&n.position[e]||{};if(typeof r.line=="number"&&r.line>0&&typeof r.column=="number"&&r.column>0)return{line:r.line,column:r.column,offset:typeof r.offset=="number"&&r.offset>-1?r.offset:void 0}}}function Co(e){const t=Pr(e),n=Yi(e);if(t&&n)return{start:t,end:n}}function Vt(e){return!e||typeof e!="object"?"":"position"in e||"type"in e?Cu(e.position):"start"in e||"end"in e?Cu(e):"line"in e||"column"in e?pr(e):""}function pr(e){return ku(e&&e.line)+":"+ku(e&&e.column)}function Cu(e){return pr(e&&e.start)+"-"+pr(e&&e.end)}function ku(e){return e&&typeof e=="number"?e:1}class be extends Error{constructor(t,n,r){super(),typeof n=="string"&&(r=n,n=void 0);let u="",a={},i=!1;if(n&&("line"in n&&"column"in n?a={place:n}:"start"in n&&"end"in n?a={place:n}:"type"in n?a={ancestors:[n],place:n.position}:a={...n}),typeof t=="string"?u=t:!a.cause&&t&&(i=!0,u=t.message,a.cause=t),!a.ruleId&&!a.source&&typeof r=="string"){const l=r.indexOf(":");l===-1?a.ruleId=r:(a.source=r.slice(0,l),a.ruleId=r.slice(l+1))}if(!a.place&&a.ancestors&&a.ancestors){const l=a.ancestors[a.ancestors.length-1];l&&(a.place=l.position)}const o=a.place&&"start"in a.place?a.place.start:a.place;this.ancestors=a.ancestors||void 0,this.cause=a.cause||void 0,this.column=o?o.column:void 0,this.fatal=void 0,this.file,this.message=u,this.line=o?o.line:void 0,this.name=Vt(a.place)||"1:1",this.place=a.place||void 0,this.reason=this.message,this.ruleId=a.ruleId||void 0,this.source=a.source||void 0,this.stack=i&&a.cause&&typeof a.cause.stack=="string"?a.cause.stack:"",this.actual,this.expected,this.note,this.url}}be.prototype.file="";be.prototype.name="";be.prototype.reason="";be.prototype.message="";be.prototype.stack="";be.prototype.column=void 0;be.prototype.line=void 0;be.prototype.ancestors=void 0;be.prototype.cause=void 0;be.prototype.fatal=void 0;be.prototype.place=void 0;be.prototype.ruleId=void 0;be.prototype.source=void 0;const wr={}.hasOwnProperty,ko=new Map,So=/[A-Z]/g,yo=new Set(["table","tbody","thead","tfoot","tr"]),Io=new Set(["td","th"]),ji="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function xo(e,t){if(!t||t.Fragment===void 0)throw new TypeError("Expected `Fragment` in options");const n=t.filePath||void 0;let r;if(t.development){if(typeof t.jsxDEV!="function")throw new TypeError("Expected `jsxDEV` in options when `development: true`");r=Mo(n,t.jsxDEV)}else{if(typeof t.jsx!="function")throw new TypeError("Expected `jsx` in production options");if(typeof t.jsxs!="function")throw new TypeError("Expected `jsxs` in production options");r=wo(n,t.jsx,t.jsxs)}const u={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:r,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:n,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:t.passKeys!==!1,passNode:t.passNode||!1,schema:t.space==="svg"?Rr:po,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:t.tableCellAlignToStyle!==!1},a=Vi(u,e,void 0);return a&&typeof a!="string"?a:u.create(e,u.Fragment,{children:a||void 0},void 0)}function Vi(e,t,n){if(t.type==="element")return No(e,t,n);if(t.type==="mdxFlowExpression"||t.type==="mdxTextExpression")return Oo(e,t);if(t.type==="mdxJsxFlowElement"||t.type==="mdxJsxTextElement")return Do(e,t,n);if(t.type==="mdxjsEsm")return Lo(e,t);if(t.type==="root")return Ro(e,t,n);if(t.type==="text")return Po(e,t)}function No(e,t,n){const r=e.schema;let u=r;t.tagName.toLowerCase()==="svg"&&r.space==="html"&&(u=Rr,e.schema=u),e.ancestors.push(t);const a=Wi(e,t.tagName,!1),i=Bo(e,t);let o=Br(e,t);return yo.has(t.tagName)&&(o=o.filter(function(l){return typeof l=="string"?!ro(l):!0})),$i(e,i,a,t),Mr(i,o),e.ancestors.pop(),e.schema=r,e.create(t,a,i,n)}function Oo(e,t){if(t.data&&t.data.estree&&e.evaluater){const r=t.data.estree.body[0];return r.type,e.evaluater.evaluateExpression(r.expression)}en(e,t.position)}function Lo(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);en(e,t.position)}function Do(e,t,n){const r=e.schema;let u=r;t.name==="svg"&&r.space==="html"&&(u=Rr,e.schema=u),e.ancestors.push(t);const a=t.name===null?e.Fragment:Wi(e,t.name,!0),i=Fo(e,t),o=Br(e,t);return $i(e,i,a,t),Mr(i,o),e.ancestors.pop(),e.schema=r,e.create(t,a,i,n)}function Ro(e,t,n){const r={};return Mr(r,Br(e,t)),e.create(t,e.Fragment,r,n)}function Po(e,t){return t.value}function $i(e,t,n,r){typeof n!="string"&&n!==e.Fragment&&e.passNode&&(t.node=r)}function Mr(e,t){if(t.length>0){const n=t.length>1?t:t[0];n&&(e.children=n)}}function wo(e,t,n){return r;function r(u,a,i,o){const c=Array.isArray(i.children)?n:t;return o?c(a,i,o):c(a,i)}}function Mo(e,t){return n;function n(r,u,a,i){const o=Array.isArray(a.children),l=Pr(r);return t(u,a,i,o,{columnNumber:l?l.column-1:void 0,fileName:e,lineNumber:l?l.line:void 0},void 0)}}function Bo(e,t){const n={};let r,u;for(u in t.properties)if(u!=="children"&&wr.call(t.properties,u)){const a=vo(e,u,t.properties[u]);if(a){const[i,o]=a;e.tableCellAlignToStyle&&i==="align"&&typeof o=="string"&&Io.has(t.tagName)?r=o:n[i]=o}}if(r){const a=n.style||(n.style={});a[e.stylePropertyNameCase==="css"?"text-align":"textAlign"]=r}return n}function Fo(e,t){const n={};for(const r of t.attributes)if(r.type==="mdxJsxExpressionAttribute")if(r.data&&r.data.estree&&e.evaluater){const a=r.data.estree.body[0];a.type;const i=a.expression;i.type;const o=i.properties[0];o.type,Object.assign(n,e.evaluater.evaluateExpression(o.argument))}else en(e,t.position);else{const u=r.name;let a;if(r.value&&typeof r.value=="object")if(r.value.data&&r.value.data.estree&&e.evaluater){const o=r.value.data.estree.body[0];o.type,a=e.evaluater.evaluateExpression(o.expression)}else en(e,t.position);else a=r.value===null?!0:r.value;n[u]=a}return n}function Br(e,t){const n=[];let r=-1;const u=e.passKeys?new Map:ko;for(;++r<t.children.length;){const a=t.children[r];let i;if(e.passKeys){const l=a.type==="element"?a.tagName:a.type==="mdxJsxFlowElement"||a.type==="mdxJsxTextElement"?a.name:void 0;if(l){const c=u.get(l)||0;i=l+"-"+c,u.set(l,c+1)}}const o=Vi(e,a,i);o!==void 0&&n.push(o)}return n}function vo(e,t,n){const r=co(e.schema,t);if(!(n==null||typeof n=="number"&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?Js(n):mo(n)),r.property==="style"){let u=typeof n=="object"?n:Uo(e,String(n));return e.stylePropertyNameCase==="css"&&(u=Ho(u)),["style",u]}return[e.elementAttributeNameCase==="react"&&r.space?so[r.property]||r.property:r.attribute,n]}}function Uo(e,t){try{return _o(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};const r=n,u=new be("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:r,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw u.file=e.filePath||void 0,u.url=ji+"#cannot-parse-style-attribute",u}}function Wi(e,t,n){let r;if(!n)r={type:"Literal",value:t};else if(t.includes(".")){const u=t.split(".");let a=-1,i;for(;++a<u.length;){const o=pu(u[a])?{type:"Identifier",name:u[a]}:{type:"Literal",value:u[a]};i=i?{type:"MemberExpression",object:i,property:o,computed:!!(a&&o.type==="Literal"),optional:!1}:o}r=i}else r=pu(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};if(r.type==="Literal"){const u=r.value;return wr.call(e.components,u)?e.components[u]:u}if(e.evaluater)return e.evaluater.evaluateExpression(r);en(e)}function en(e,t){const n=new be("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=ji+"#cannot-handle-mdx-estrees-without-createevaluater",n}function Ho(e){const t={};let n;for(n in e)wr.call(e,n)&&(t[zo(n)]=e[n]);return t}function zo(e){let t=e.replace(So,Yo);return t.slice(0,3)==="ms-"&&(t="-"+t),t}function Yo(e){return"-"+e.toLowerCase()}const $n={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]},qo={};function Fr(e,t){const n=qo,r=typeof n.includeImageAlt=="boolean"?n.includeImageAlt:!0,u=typeof n.includeHtml=="boolean"?n.includeHtml:!0;return Qi(e,r,u)}function Qi(e,t,n){if(jo(e)){if("value"in e)return e.type==="html"&&!n?"":e.value;if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return Su(e.children,t,n)}return Array.isArray(e)?Su(e,t,n):""}function Su(e,t,n){const r=[];let u=-1;for(;++u<e.length;)r[u]=Qi(e[u],t,n);return r.join("")}function jo(e){return!!(e&&typeof e=="object")}const yu=document.createElement("i");function vr(e){const t="&"+e+";";yu.innerHTML=t;const n=yu.textContent;return n.charCodeAt(n.length-1)===59&&e!=="semi"||n===t?!1:n}function we(e,t,n,r){const u=e.length;let a=0,i;if(t<0?t=-t>u?0:u+t:t=t>u?u:t,n=n>0?n:0,r.length<1e4)i=Array.from(r),i.unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);a<r.length;)i=r.slice(a,a+1e4),i.unshift(t,0),e.splice(...i),a+=1e4,t+=1e4}function Be(e,t){return e.length>0?(we(e,e.length,0,t),e):t}const Iu={}.hasOwnProperty;function Xi(e){const t={};let n=-1;for(;++n<e.length;)Vo(t,e[n]);return t}function Vo(e,t){let n;for(n in t){const u=(Iu.call(e,n)?e[n]:void 0)||(e[n]={}),a=t[n];let i;if(a)for(i in a){Iu.call(u,i)||(u[i]=[]);const o=a[i];$o(u[i],Array.isArray(o)?o:o?[o]:[])}}}function $o(e,t){let n=-1;const r=[];for(;++n<t.length;)(t[n].add==="after"?e:r).push(t[n]);we(e,0,0,r)}function Ki(e,t){const n=Number.parseInt(e,t);return n<9||n===11||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(n&65535)===65535||(n&65535)===65534||n>1114111?"�":String.fromCodePoint(n)}function ze(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}const _e=it(/[A-Za-z]/),Te=it(/[\dA-Za-z]/),Wo=it(/[#-'*+\--9=?A-Z^-~]/);function Cn(e){return e!==null&&(e<32||e===127)}const mr=it(/\d/),Qo=it(/[\dA-Fa-f]/),Xo=it(/[!-/:-@[-`{-~]/);function Y(e){return e!==null&&e<-2}function ie(e){return e!==null&&(e<0||e===32)}function J(e){return e===-2||e===-1||e===32}const Ln=it(new RegExp("\\p{P}|\\p{S}","u")),ft=it(/\s/);function it(e){return t;function t(n){return n!==null&&n>-1&&e.test(String.fromCharCode(n))}}function Ot(e){const t=[];let n=-1,r=0,u=0;for(;++n<e.length;){const a=e.charCodeAt(n);let i="";if(a===37&&Te(e.charCodeAt(n+1))&&Te(e.charCodeAt(n+2)))u=2;else if(a<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(a))||(i=String.fromCharCode(a));else if(a>55295&&a<57344){const o=e.charCodeAt(n+1);a<56320&&o>56319&&o<57344?(i=String.fromCharCode(a,o),u=1):i="�"}else i=String.fromCharCode(a);i&&(t.push(e.slice(r,n),encodeURIComponent(i)),r=n+u+1,i=""),u&&(n+=u,u=0)}return t.join("")+e.slice(r)}function te(e,t,n,r){const u=r?r-1:Number.POSITIVE_INFINITY;let a=0;return i;function i(l){return J(l)?(e.enter(n),o(l)):t(l)}function o(l){return J(l)&&a++<u?(e.consume(l),o):(e.exit(n),t(l))}}const Ko={tokenize:Go};function Go(e){const t=e.attempt(this.parser.constructs.contentInitial,r,u);let n;return t;function r(o){if(o===null){e.consume(o);return}return e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),te(e,t,"linePrefix")}function u(o){return e.enter("paragraph"),a(o)}function a(o){const l=e.enter("chunkText",{contentType:"text",previous:n});return n&&(n.next=l),n=l,i(o)}function i(o){if(o===null){e.exit("chunkText"),e.exit("paragraph"),e.consume(o);return}return Y(o)?(e.consume(o),e.exit("chunkText"),a):(e.consume(o),i)}}const Jo={tokenize:Zo},xu={tokenize:el};function Zo(e){const t=this,n=[];let r=0,u,a,i;return o;function o(B){if(r<n.length){const j=n[r];return t.containerState=j[1],e.attempt(j[0].continuation,l,c)(B)}return c(B)}function l(B){if(r++,t.containerState._closeFlow){t.containerState._closeFlow=void 0,u&&F();const j=t.events.length;let V=j,x;for(;V--;)if(t.events[V][0]==="exit"&&t.events[V][1].type==="chunkFlow"){x=t.events[V][1].end;break}k(r);let Q=j;for(;Q<t.events.length;)t.events[Q][1].end={...x},Q++;return we(t.events,V+1,0,t.events.slice(j)),t.events.length=Q,c(B)}return o(B)}function c(B){if(r===n.length){if(!u)return m(B);if(u.currentConstruct&&u.currentConstruct.concrete)return _(B);t.interrupt=!!(u.currentConstruct&&!u._gfmTableDynamicInterruptHack)}return t.containerState={},e.check(xu,h,f)(B)}function h(B){return u&&F(),k(r),m(B)}function f(B){return t.parser.lazy[t.now().line]=r!==n.length,i=t.now().offset,_(B)}function m(B){return t.containerState={},e.attempt(xu,d,_)(B)}function d(B){return r++,n.push([t.currentConstruct,t.containerState]),m(B)}function _(B){if(B===null){u&&F(),k(0),e.consume(B);return}return u=u||t.parser.flow(t.now()),e.enter("chunkFlow",{_tokenizer:u,contentType:"flow",previous:a}),y(B)}function y(B){if(B===null){M(e.exit("chunkFlow"),!0),k(0),e.consume(B);return}return Y(B)?(e.consume(B),M(e.exit("chunkFlow")),r=0,t.interrupt=void 0,o):(e.consume(B),y)}function M(B,j){const V=t.sliceStream(B);if(j&&V.push(null),B.previous=a,a&&(a.next=B),a=B,u.defineSkip(B.start),u.write(V),t.parser.lazy[B.start.line]){let x=u.events.length;for(;x--;)if(u.events[x][1].start.offset<i&&(!u.events[x][1].end||u.events[x][1].end.offset>i))return;const Q=t.events.length;let ne=Q,ee,A;for(;ne--;)if(t.events[ne][0]==="exit"&&t.events[ne][1].type==="chunkFlow"){if(ee){A=t.events[ne][1].end;break}ee=!0}for(k(r),x=Q;x<t.events.length;)t.events[x][1].end={...A},x++;we(t.events,ne+1,0,t.events.slice(Q)),t.events.length=x}}function k(B){let j=n.length;for(;j-- >B;){const V=n[j];t.containerState=V[1],V[0].exit.call(t,e)}n.length=B}function F(){u.write([null]),a=void 0,u=void 0,t.containerState._closeFlow=void 0}}function el(e,t,n){return te(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function It(e){if(e===null||ie(e)||ft(e))return 1;if(Ln(e))return 2}function Dn(e,t,n){const r=[];let u=-1;for(;++u<e.length;){const a=e[u].resolveAll;a&&!r.includes(a)&&(t=a(t,n),r.push(a))}return t}const Er={name:"attention",resolveAll:tl,tokenize:nl};function tl(e,t){let n=-1,r,u,a,i,o,l,c,h;for(;++n<e.length;)if(e[n][0]==="enter"&&e[n][1].type==="attentionSequence"&&e[n][1]._close){for(r=n;r--;)if(e[r][0]==="exit"&&e[r][1].type==="attentionSequence"&&e[r][1]._open&&t.sliceSerialize(e[r][1]).charCodeAt(0)===t.sliceSerialize(e[n][1]).charCodeAt(0)){if((e[r][1]._close||e[n][1]._open)&&(e[n][1].end.offset-e[n][1].start.offset)%3&&!((e[r][1].end.offset-e[r][1].start.offset+e[n][1].end.offset-e[n][1].start.offset)%3))continue;l=e[r][1].end.offset-e[r][1].start.offset>1&&e[n][1].end.offset-e[n][1].start.offset>1?2:1;const f={...e[r][1].end},m={...e[n][1].start};Nu(f,-l),Nu(m,l),i={type:l>1?"strongSequence":"emphasisSequence",start:f,end:{...e[r][1].end}},o={type:l>1?"strongSequence":"emphasisSequence",start:{...e[n][1].start},end:m},a={type:l>1?"strongText":"emphasisText",start:{...e[r][1].end},end:{...e[n][1].start}},u={type:l>1?"strong":"emphasis",start:{...i.start},end:{...o.end}},e[r][1].end={...i.start},e[n][1].start={...o.end},c=[],e[r][1].end.offset-e[r][1].start.offset&&(c=Be(c,[["enter",e[r][1],t],["exit",e[r][1],t]])),c=Be(c,[["enter",u,t],["enter",i,t],["exit",i,t],["enter",a,t]]),c=Be(c,Dn(t.parser.constructs.insideSpan.null,e.slice(r+1,n),t)),c=Be(c,[["exit",a,t],["enter",o,t],["exit",o,t],["exit",u,t]]),e[n][1].end.offset-e[n][1].start.offset?(h=2,c=Be(c,[["enter",e[n][1],t],["exit",e[n][1],t]])):h=0,we(e,r-1,n-r+3,c),n=r+c.length-h-2;break}}for(n=-1;++n<e.length;)e[n][1].type==="attentionSequence"&&(e[n][1].type="data");return e}function nl(e,t){const n=this.parser.constructs.attentionMarkers.null,r=this.previous,u=It(r);let a;return i;function i(l){return a=l,e.enter("attentionSequence"),o(l)}function o(l){if(l===a)return e.consume(l),o;const c=e.exit("attentionSequence"),h=It(l),f=!h||h===2&&u||n.includes(l),m=!u||u===2&&h||n.includes(r);return c._open=!!(a===42?f:f&&(u||!m)),c._close=!!(a===42?m:m&&(h||!f)),t(l)}}function Nu(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}const rl={name:"autolink",tokenize:ul};function ul(e,t,n){let r=0;return u;function u(d){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(d),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),a}function a(d){return _e(d)?(e.consume(d),i):d===64?n(d):c(d)}function i(d){return d===43||d===45||d===46||Te(d)?(r=1,o(d)):c(d)}function o(d){return d===58?(e.consume(d),r=0,l):(d===43||d===45||d===46||Te(d))&&r++<32?(e.consume(d),o):(r=0,c(d))}function l(d){return d===62?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(d),e.exit("autolinkMarker"),e.exit("autolink"),t):d===null||d===32||d===60||Cn(d)?n(d):(e.consume(d),l)}function c(d){return d===64?(e.consume(d),h):Wo(d)?(e.consume(d),c):n(d)}function h(d){return Te(d)?f(d):n(d)}function f(d){return d===46?(e.consume(d),r=0,h):d===62?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(d),e.exit("autolinkMarker"),e.exit("autolink"),t):m(d)}function m(d){if((d===45||Te(d))&&r++<63){const _=d===45?m:f;return e.consume(d),_}return n(d)}}const an={partial:!0,tokenize:il};function il(e,t,n){return r;function r(a){return J(a)?te(e,u,"linePrefix")(a):u(a)}function u(a){return a===null||Y(a)?t(a):n(a)}}const Gi={continuation:{tokenize:sl},exit:ol,name:"blockQuote",tokenize:al};function al(e,t,n){const r=this;return u;function u(i){if(i===62){const o=r.containerState;return o.open||(e.enter("blockQuote",{_container:!0}),o.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(i),e.exit("blockQuoteMarker"),a}return n(i)}function a(i){return J(i)?(e.enter("blockQuotePrefixWhitespace"),e.consume(i),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(i))}}function sl(e,t,n){const r=this;return u;function u(i){return J(i)?te(e,a,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(i):a(i)}function a(i){return e.attempt(Gi,t,n)(i)}}function ol(e){e.exit("blockQuote")}const Ji={name:"characterEscape",tokenize:ll};function ll(e,t,n){return r;function r(a){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(a),e.exit("escapeMarker"),u}function u(a){return Xo(a)?(e.enter("characterEscapeValue"),e.consume(a),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(a)}}const Zi={name:"characterReference",tokenize:cl};function cl(e,t,n){const r=this;let u=0,a,i;return o;function o(f){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(f),e.exit("characterReferenceMarker"),l}function l(f){return f===35?(e.enter("characterReferenceMarkerNumeric"),e.consume(f),e.exit("characterReferenceMarkerNumeric"),c):(e.enter("characterReferenceValue"),a=31,i=Te,h(f))}function c(f){return f===88||f===120?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(f),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),a=6,i=Qo,h):(e.enter("characterReferenceValue"),a=7,i=mr,h(f))}function h(f){if(f===59&&u){const m=e.exit("characterReferenceValue");return i===Te&&!vr(r.sliceSerialize(m))?n(f):(e.enter("characterReferenceMarker"),e.consume(f),e.exit("characterReferenceMarker"),e.exit("characterReference"),t)}return i(f)&&u++<a?(e.consume(f),h):n(f)}}const Ou={partial:!0,tokenize:hl},Lu={concrete:!0,name:"codeFenced",tokenize:fl};function fl(e,t,n){const r=this,u={partial:!0,tokenize:V};let a=0,i=0,o;return l;function l(x){return c(x)}function c(x){const Q=r.events[r.events.length-1];return a=Q&&Q[1].type==="linePrefix"?Q[2].sliceSerialize(Q[1],!0).length:0,o=x,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),h(x)}function h(x){return x===o?(i++,e.consume(x),h):i<3?n(x):(e.exit("codeFencedFenceSequence"),J(x)?te(e,f,"whitespace")(x):f(x))}function f(x){return x===null||Y(x)?(e.exit("codeFencedFence"),r.interrupt?t(x):e.check(Ou,y,j)(x)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),m(x))}function m(x){return x===null||Y(x)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),f(x)):J(x)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),te(e,d,"whitespace")(x)):x===96&&x===o?n(x):(e.consume(x),m)}function d(x){return x===null||Y(x)?f(x):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),_(x))}function _(x){return x===null||Y(x)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),f(x)):x===96&&x===o?n(x):(e.consume(x),_)}function y(x){return e.attempt(u,j,M)(x)}function M(x){return e.enter("lineEnding"),e.consume(x),e.exit("lineEnding"),k}function k(x){return a>0&&J(x)?te(e,F,"linePrefix",a+1)(x):F(x)}function F(x){return x===null||Y(x)?e.check(Ou,y,j)(x):(e.enter("codeFlowValue"),B(x))}function B(x){return x===null||Y(x)?(e.exit("codeFlowValue"),F(x)):(e.consume(x),B)}function j(x){return e.exit("codeFenced"),t(x)}function V(x,Q,ne){let ee=0;return A;function A(z){return x.enter("lineEnding"),x.consume(z),x.exit("lineEnding"),N}function N(z){return x.enter("codeFencedFence"),J(z)?te(x,L,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(z):L(z)}function L(z){return z===o?(x.enter("codeFencedFenceSequence"),H(z)):ne(z)}function H(z){return z===o?(ee++,x.consume(z),H):ee>=i?(x.exit("codeFencedFenceSequence"),J(z)?te(x,v,"whitespace")(z):v(z)):ne(z)}function v(z){return z===null||Y(z)?(x.exit("codeFencedFence"),Q(z)):ne(z)}}}function hl(e,t,n){const r=this;return u;function u(i){return i===null?n(i):(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),a)}function a(i){return r.parser.lazy[r.now().line]?n(i):t(i)}}const Wn={name:"codeIndented",tokenize:pl},dl={partial:!0,tokenize:ml};function pl(e,t,n){const r=this;return u;function u(c){return e.enter("codeIndented"),te(e,a,"linePrefix",5)(c)}function a(c){const h=r.events[r.events.length-1];return h&&h[1].type==="linePrefix"&&h[2].sliceSerialize(h[1],!0).length>=4?i(c):n(c)}function i(c){return c===null?l(c):Y(c)?e.attempt(dl,i,l)(c):(e.enter("codeFlowValue"),o(c))}function o(c){return c===null||Y(c)?(e.exit("codeFlowValue"),i(c)):(e.consume(c),o)}function l(c){return e.exit("codeIndented"),t(c)}}function ml(e,t,n){const r=this;return u;function u(i){return r.parser.lazy[r.now().line]?n(i):Y(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),u):te(e,a,"linePrefix",5)(i)}function a(i){const o=r.events[r.events.length-1];return o&&o[1].type==="linePrefix"&&o[2].sliceSerialize(o[1],!0).length>=4?t(i):Y(i)?u(i):n(i)}}const El={name:"codeText",previous:Tl,resolve:gl,tokenize:bl};function gl(e){let t=e.length-4,n=3,r,u;if((e[n][1].type==="lineEnding"||e[n][1].type==="space")&&(e[t][1].type==="lineEnding"||e[t][1].type==="space")){for(r=n;++r<t;)if(e[r][1].type==="codeTextData"){e[n][1].type="codeTextPadding",e[t][1].type="codeTextPadding",n+=2,t-=2;break}}for(r=n-1,t++;++r<=t;)u===void 0?r!==t&&e[r][1].type!=="lineEnding"&&(u=r):(r===t||e[r][1].type==="lineEnding")&&(e[u][1].type="codeTextData",r!==u+2&&(e[u][1].end=e[r-1][1].end,e.splice(u+2,r-u-2),t-=r-u-2,r=u+2),u=void 0);return e}function Tl(e){return e!==96||this.events[this.events.length-1][1].type==="characterEscape"}function bl(e,t,n){let r=0,u,a;return i;function i(f){return e.enter("codeText"),e.enter("codeTextSequence"),o(f)}function o(f){return f===96?(e.consume(f),r++,o):(e.exit("codeTextSequence"),l(f))}function l(f){return f===null?n(f):f===32?(e.enter("space"),e.consume(f),e.exit("space"),l):f===96?(a=e.enter("codeTextSequence"),u=0,h(f)):Y(f)?(e.enter("lineEnding"),e.consume(f),e.exit("lineEnding"),l):(e.enter("codeTextData"),c(f))}function c(f){return f===null||f===32||f===96||Y(f)?(e.exit("codeTextData"),l(f)):(e.consume(f),c)}function h(f){return f===96?(e.consume(f),u++,h):u===r?(e.exit("codeTextSequence"),e.exit("codeText"),t(f)):(a.type="codeTextData",c(f))}}class Al{constructor(t){this.left=t?[...t]:[],this.right=[]}get(t){if(t<0||t>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+t+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return t<this.left.length?this.left[t]:this.right[this.right.length-t+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(t,n){const r=n??Number.POSITIVE_INFINITY;return r<this.left.length?this.left.slice(t,r):t>this.left.length?this.right.slice(this.right.length-r+this.left.length,this.right.length-t+this.left.length).reverse():this.left.slice(t).concat(this.right.slice(this.right.length-r+this.left.length).reverse())}splice(t,n,r){const u=n||0;this.setCursor(Math.trunc(t));const a=this.right.splice(this.right.length-u,Number.POSITIVE_INFINITY);return r&&Ut(this.left,r),a.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(t){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(t)}pushMany(t){this.setCursor(Number.POSITIVE_INFINITY),Ut(this.left,t)}unshift(t){this.setCursor(0),this.right.push(t)}unshiftMany(t){this.setCursor(0),Ut(this.right,t.reverse())}setCursor(t){if(!(t===this.left.length||t>this.left.length&&this.right.length===0||t<0&&this.left.length===0))if(t<this.left.length){const n=this.left.splice(t,Number.POSITIVE_INFINITY);Ut(this.right,n.reverse())}else{const n=this.right.splice(this.left.length+this.right.length-t,Number.POSITIVE_INFINITY);Ut(this.left,n.reverse())}}}function Ut(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function ea(e){const t={};let n=-1,r,u,a,i,o,l,c;const h=new Al(e);for(;++n<h.length;){for(;n in t;)n=t[n];if(r=h.get(n),n&&r[1].type==="chunkFlow"&&h.get(n-1)[1].type==="listItemPrefix"&&(l=r[1]._tokenizer.events,a=0,a<l.length&&l[a][1].type==="lineEndingBlank"&&(a+=2),a<l.length&&l[a][1].type==="content"))for(;++a<l.length&&l[a][1].type!=="content";)l[a][1].type==="chunkText"&&(l[a][1]._isInFirstContentOfListItem=!0,a++);if(r[0]==="enter")r[1].contentType&&(Object.assign(t,_l(h,n)),n=t[n],c=!0);else if(r[1]._container){for(a=n,u=void 0;a--;)if(i=h.get(a),i[1].type==="lineEnding"||i[1].type==="lineEndingBlank")i[0]==="enter"&&(u&&(h.get(u)[1].type="lineEndingBlank"),i[1].type="lineEnding",u=a);else if(!(i[1].type==="linePrefix"||i[1].type==="listItemIndent"))break;u&&(r[1].end={...h.get(u)[1].start},o=h.slice(u,n),o.unshift(r),h.splice(u,n-u+1,o))}}return we(e,0,Number.POSITIVE_INFINITY,h.slice(0)),!c}function _l(e,t){const n=e.get(t)[1],r=e.get(t)[2];let u=t-1;const a=[];let i=n._tokenizer;i||(i=r.parser[n.contentType](n.start),n._contentTypeTextTrailing&&(i._contentTypeTextTrailing=!0));const o=i.events,l=[],c={};let h,f,m=-1,d=n,_=0,y=0;const M=[y];for(;d;){for(;e.get(++u)[1]!==d;);a.push(u),d._tokenizer||(h=r.sliceStream(d),d.next||h.push(null),f&&i.defineSkip(d.start),d._isInFirstContentOfListItem&&(i._gfmTasklistFirstContentOfListItem=!0),i.write(h),d._isInFirstContentOfListItem&&(i._gfmTasklistFirstContentOfListItem=void 0)),f=d,d=d.next}for(d=n;++m<o.length;)o[m][0]==="exit"&&o[m-1][0]==="enter"&&o[m][1].type===o[m-1][1].type&&o[m][1].start.line!==o[m][1].end.line&&(y=m+1,M.push(y),d._tokenizer=void 0,d.previous=void 0,d=d.next);for(i.events=[],d?(d._tokenizer=void 0,d.previous=void 0):M.pop(),m=M.length;m--;){const k=o.slice(M[m],M[m+1]),F=a.pop();l.push([F,F+k.length-1]),e.splice(F,2,k)}for(l.reverse(),m=-1;++m<l.length;)c[_+l[m][0]]=_+l[m][1],_+=l[m][1]-l[m][0]-1;return c}const Cl={resolve:Sl,tokenize:yl},kl={partial:!0,tokenize:Il};function Sl(e){return ea(e),e}function yl(e,t){let n;return r;function r(o){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),u(o)}function u(o){return o===null?a(o):Y(o)?e.check(kl,i,a)(o):(e.consume(o),u)}function a(o){return e.exit("chunkContent"),e.exit("content"),t(o)}function i(o){return e.consume(o),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,u}}function Il(e,t,n){const r=this;return u;function u(i){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),te(e,a,"linePrefix")}function a(i){if(i===null||Y(i))return n(i);const o=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&o&&o[1].type==="linePrefix"&&o[2].sliceSerialize(o[1],!0).length>=4?t(i):e.interrupt(r.parser.constructs.flow,n,t)(i)}}function ta(e,t,n,r,u,a,i,o,l){const c=l||Number.POSITIVE_INFINITY;let h=0;return f;function f(k){return k===60?(e.enter(r),e.enter(u),e.enter(a),e.consume(k),e.exit(a),m):k===null||k===32||k===41||Cn(k)?n(k):(e.enter(r),e.enter(i),e.enter(o),e.enter("chunkString",{contentType:"string"}),y(k))}function m(k){return k===62?(e.enter(a),e.consume(k),e.exit(a),e.exit(u),e.exit(r),t):(e.enter(o),e.enter("chunkString",{contentType:"string"}),d(k))}function d(k){return k===62?(e.exit("chunkString"),e.exit(o),m(k)):k===null||k===60||Y(k)?n(k):(e.consume(k),k===92?_:d)}function _(k){return k===60||k===62||k===92?(e.consume(k),d):d(k)}function y(k){return!h&&(k===null||k===41||ie(k))?(e.exit("chunkString"),e.exit(o),e.exit(i),e.exit(r),t(k)):h<c&&k===40?(e.consume(k),h++,y):k===41?(e.consume(k),h--,y):k===null||k===32||k===40||Cn(k)?n(k):(e.consume(k),k===92?M:y)}function M(k){return k===40||k===41||k===92?(e.consume(k),y):y(k)}}function na(e,t,n,r,u,a){const i=this;let o=0,l;return c;function c(d){return e.enter(r),e.enter(u),e.consume(d),e.exit(u),e.enter(a),h}function h(d){return o>999||d===null||d===91||d===93&&!l||d===94&&!o&&"_hiddenFootnoteSupport"in i.parser.constructs?n(d):d===93?(e.exit(a),e.enter(u),e.consume(d),e.exit(u),e.exit(r),t):Y(d)?(e.enter("lineEnding"),e.consume(d),e.exit("lineEnding"),h):(e.enter("chunkString",{contentType:"string"}),f(d))}function f(d){return d===null||d===91||d===93||Y(d)||o++>999?(e.exit("chunkString"),h(d)):(e.consume(d),l||(l=!J(d)),d===92?m:f)}function m(d){return d===91||d===92||d===93?(e.consume(d),o++,f):f(d)}}function ra(e,t,n,r,u,a){let i;return o;function o(m){return m===34||m===39||m===40?(e.enter(r),e.enter(u),e.consume(m),e.exit(u),i=m===40?41:m,l):n(m)}function l(m){return m===i?(e.enter(u),e.consume(m),e.exit(u),e.exit(r),t):(e.enter(a),c(m))}function c(m){return m===i?(e.exit(a),l(i)):m===null?n(m):Y(m)?(e.enter("lineEnding"),e.consume(m),e.exit("lineEnding"),te(e,c,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),h(m))}function h(m){return m===i||m===null||Y(m)?(e.exit("chunkString"),c(m)):(e.consume(m),m===92?f:h)}function f(m){return m===i||m===92?(e.consume(m),h):h(m)}}function $t(e,t){let n;return r;function r(u){return Y(u)?(e.enter("lineEnding"),e.consume(u),e.exit("lineEnding"),n=!0,r):J(u)?te(e,r,n?"linePrefix":"lineSuffix")(u):t(u)}}const xl={name:"definition",tokenize:Ol},Nl={partial:!0,tokenize:Ll};function Ol(e,t,n){const r=this;let u;return a;function a(d){return e.enter("definition"),i(d)}function i(d){return na.call(r,e,o,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(d)}function o(d){return u=ze(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)),d===58?(e.enter("definitionMarker"),e.consume(d),e.exit("definitionMarker"),l):n(d)}function l(d){return ie(d)?$t(e,c)(d):c(d)}function c(d){return ta(e,h,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(d)}function h(d){return e.attempt(Nl,f,f)(d)}function f(d){return J(d)?te(e,m,"whitespace")(d):m(d)}function m(d){return d===null||Y(d)?(e.exit("definition"),r.parser.defined.push(u),t(d)):n(d)}}function Ll(e,t,n){return r;function r(o){return ie(o)?$t(e,u)(o):n(o)}function u(o){return ra(e,a,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(o)}function a(o){return J(o)?te(e,i,"whitespace")(o):i(o)}function i(o){return o===null||Y(o)?t(o):n(o)}}const Dl={name:"hardBreakEscape",tokenize:Rl};function Rl(e,t,n){return r;function r(a){return e.enter("hardBreakEscape"),e.consume(a),u}function u(a){return Y(a)?(e.exit("hardBreakEscape"),t(a)):n(a)}}const Pl={name:"headingAtx",resolve:wl,tokenize:Ml};function wl(e,t){let n=e.length-2,r=3,u,a;return e[r][1].type==="whitespace"&&(r+=2),n-2>r&&e[n][1].type==="whitespace"&&(n-=2),e[n][1].type==="atxHeadingSequence"&&(r===n-1||n-4>r&&e[n-2][1].type==="whitespace")&&(n-=r+1===n?2:4),n>r&&(u={type:"atxHeadingText",start:e[r][1].start,end:e[n][1].end},a={type:"chunkText",start:e[r][1].start,end:e[n][1].end,contentType:"text"},we(e,r,n-r+1,[["enter",u,t],["enter",a,t],["exit",a,t],["exit",u,t]])),e}function Ml(e,t,n){let r=0;return u;function u(h){return e.enter("atxHeading"),a(h)}function a(h){return e.enter("atxHeadingSequence"),i(h)}function i(h){return h===35&&r++<6?(e.consume(h),i):h===null||ie(h)?(e.exit("atxHeadingSequence"),o(h)):n(h)}function o(h){return h===35?(e.enter("atxHeadingSequence"),l(h)):h===null||Y(h)?(e.exit("atxHeading"),t(h)):J(h)?te(e,o,"whitespace")(h):(e.enter("atxHeadingText"),c(h))}function l(h){return h===35?(e.consume(h),l):(e.exit("atxHeadingSequence"),o(h))}function c(h){return h===null||h===35||ie(h)?(e.exit("atxHeadingText"),o(h)):(e.consume(h),c)}}const Bl=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],Du=["pre","script","style","textarea"],Fl={concrete:!0,name:"htmlFlow",resolveTo:Hl,tokenize:zl},vl={partial:!0,tokenize:ql},Ul={partial:!0,tokenize:Yl};function Hl(e){let t=e.length;for(;t--&&!(e[t][0]==="enter"&&e[t][1].type==="htmlFlow"););return t>1&&e[t-2][1].type==="linePrefix"&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e}function zl(e,t,n){const r=this;let u,a,i,o,l;return c;function c(b){return h(b)}function h(b){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(b),f}function f(b){return b===33?(e.consume(b),m):b===47?(e.consume(b),a=!0,y):b===63?(e.consume(b),u=3,r.interrupt?t:T):_e(b)?(e.consume(b),i=String.fromCharCode(b),M):n(b)}function m(b){return b===45?(e.consume(b),u=2,d):b===91?(e.consume(b),u=5,o=0,_):_e(b)?(e.consume(b),u=4,r.interrupt?t:T):n(b)}function d(b){return b===45?(e.consume(b),r.interrupt?t:T):n(b)}function _(b){const Oe="CDATA[";return b===Oe.charCodeAt(o++)?(e.consume(b),o===Oe.length?r.interrupt?t:L:_):n(b)}function y(b){return _e(b)?(e.consume(b),i=String.fromCharCode(b),M):n(b)}function M(b){if(b===null||b===47||b===62||ie(b)){const Oe=b===47,We=i.toLowerCase();return!Oe&&!a&&Du.includes(We)?(u=1,r.interrupt?t(b):L(b)):Bl.includes(i.toLowerCase())?(u=6,Oe?(e.consume(b),k):r.interrupt?t(b):L(b)):(u=7,r.interrupt&&!r.parser.lazy[r.now().line]?n(b):a?F(b):B(b))}return b===45||Te(b)?(e.consume(b),i+=String.fromCharCode(b),M):n(b)}function k(b){return b===62?(e.consume(b),r.interrupt?t:L):n(b)}function F(b){return J(b)?(e.consume(b),F):A(b)}function B(b){return b===47?(e.consume(b),A):b===58||b===95||_e(b)?(e.consume(b),j):J(b)?(e.consume(b),B):A(b)}function j(b){return b===45||b===46||b===58||b===95||Te(b)?(e.consume(b),j):V(b)}function V(b){return b===61?(e.consume(b),x):J(b)?(e.consume(b),V):B(b)}function x(b){return b===null||b===60||b===61||b===62||b===96?n(b):b===34||b===39?(e.consume(b),l=b,Q):J(b)?(e.consume(b),x):ne(b)}function Q(b){return b===l?(e.consume(b),l=null,ee):b===null||Y(b)?n(b):(e.consume(b),Q)}function ne(b){return b===null||b===34||b===39||b===47||b===60||b===61||b===62||b===96||ie(b)?V(b):(e.consume(b),ne)}function ee(b){return b===47||b===62||J(b)?B(b):n(b)}function A(b){return b===62?(e.consume(b),N):n(b)}function N(b){return b===null||Y(b)?L(b):J(b)?(e.consume(b),N):n(b)}function L(b){return b===45&&u===2?(e.consume(b),ae):b===60&&u===1?(e.consume(b),he):b===62&&u===4?(e.consume(b),Ne):b===63&&u===3?(e.consume(b),T):b===93&&u===5?(e.consume(b),Me):Y(b)&&(u===6||u===7)?(e.exit("htmlFlowData"),e.check(vl,Ue,H)(b)):b===null||Y(b)?(e.exit("htmlFlowData"),H(b)):(e.consume(b),L)}function H(b){return e.check(Ul,v,Ue)(b)}function v(b){return e.enter("lineEnding"),e.consume(b),e.exit("lineEnding"),z}function z(b){return b===null||Y(b)?H(b):(e.enter("htmlFlowData"),L(b))}function ae(b){return b===45?(e.consume(b),T):L(b)}function he(b){return b===47?(e.consume(b),i="",Ce):L(b)}function Ce(b){if(b===62){const Oe=i.toLowerCase();return Du.includes(Oe)?(e.consume(b),Ne):L(b)}return _e(b)&&i.length<8?(e.consume(b),i+=String.fromCharCode(b),Ce):L(b)}function Me(b){return b===93?(e.consume(b),T):L(b)}function T(b){return b===62?(e.consume(b),Ne):b===45&&u===2?(e.consume(b),T):L(b)}function Ne(b){return b===null||Y(b)?(e.exit("htmlFlowData"),Ue(b)):(e.consume(b),Ne)}function Ue(b){return e.exit("htmlFlow"),t(b)}}function Yl(e,t,n){const r=this;return u;function u(i){return Y(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),a):n(i)}function a(i){return r.parser.lazy[r.now().line]?n(i):t(i)}}function ql(e,t,n){return r;function r(u){return e.enter("lineEnding"),e.consume(u),e.exit("lineEnding"),e.attempt(an,t,n)}}const jl={name:"htmlText",tokenize:Vl};function Vl(e,t,n){const r=this;let u,a,i;return o;function o(T){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(T),l}function l(T){return T===33?(e.consume(T),c):T===47?(e.consume(T),V):T===63?(e.consume(T),B):_e(T)?(e.consume(T),ne):n(T)}function c(T){return T===45?(e.consume(T),h):T===91?(e.consume(T),a=0,_):_e(T)?(e.consume(T),F):n(T)}function h(T){return T===45?(e.consume(T),d):n(T)}function f(T){return T===null?n(T):T===45?(e.consume(T),m):Y(T)?(i=f,he(T)):(e.consume(T),f)}function m(T){return T===45?(e.consume(T),d):f(T)}function d(T){return T===62?ae(T):T===45?m(T):f(T)}function _(T){const Ne="CDATA[";return T===Ne.charCodeAt(a++)?(e.consume(T),a===Ne.length?y:_):n(T)}function y(T){return T===null?n(T):T===93?(e.consume(T),M):Y(T)?(i=y,he(T)):(e.consume(T),y)}function M(T){return T===93?(e.consume(T),k):y(T)}function k(T){return T===62?ae(T):T===93?(e.consume(T),k):y(T)}function F(T){return T===null||T===62?ae(T):Y(T)?(i=F,he(T)):(e.consume(T),F)}function B(T){return T===null?n(T):T===63?(e.consume(T),j):Y(T)?(i=B,he(T)):(e.consume(T),B)}function j(T){return T===62?ae(T):B(T)}function V(T){return _e(T)?(e.consume(T),x):n(T)}function x(T){return T===45||Te(T)?(e.consume(T),x):Q(T)}function Q(T){return Y(T)?(i=Q,he(T)):J(T)?(e.consume(T),Q):ae(T)}function ne(T){return T===45||Te(T)?(e.consume(T),ne):T===47||T===62||ie(T)?ee(T):n(T)}function ee(T){return T===47?(e.consume(T),ae):T===58||T===95||_e(T)?(e.consume(T),A):Y(T)?(i=ee,he(T)):J(T)?(e.consume(T),ee):ae(T)}function A(T){return T===45||T===46||T===58||T===95||Te(T)?(e.consume(T),A):N(T)}function N(T){return T===61?(e.consume(T),L):Y(T)?(i=N,he(T)):J(T)?(e.consume(T),N):ee(T)}function L(T){return T===null||T===60||T===61||T===62||T===96?n(T):T===34||T===39?(e.consume(T),u=T,H):Y(T)?(i=L,he(T)):J(T)?(e.consume(T),L):(e.consume(T),v)}function H(T){return T===u?(e.consume(T),u=void 0,z):T===null?n(T):Y(T)?(i=H,he(T)):(e.consume(T),H)}function v(T){return T===null||T===34||T===39||T===60||T===61||T===96?n(T):T===47||T===62||ie(T)?ee(T):(e.consume(T),v)}function z(T){return T===47||T===62||ie(T)?ee(T):n(T)}function ae(T){return T===62?(e.consume(T),e.exit("htmlTextData"),e.exit("htmlText"),t):n(T)}function he(T){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(T),e.exit("lineEnding"),Ce}function Ce(T){return J(T)?te(e,Me,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(T):Me(T)}function Me(T){return e.enter("htmlTextData"),i(T)}}const Ur={name:"labelEnd",resolveAll:Xl,resolveTo:Kl,tokenize:Gl},$l={tokenize:Jl},Wl={tokenize:Zl},Ql={tokenize:ec};function Xl(e){let t=-1;const n=[];for(;++t<e.length;){const r=e[t][1];if(n.push(e[t]),r.type==="labelImage"||r.type==="labelLink"||r.type==="labelEnd"){const u=r.type==="labelImage"?4:2;r.type="data",t+=u}}return e.length!==n.length&&we(e,0,e.length,n),e}function Kl(e,t){let n=e.length,r=0,u,a,i,o;for(;n--;)if(u=e[n][1],a){if(u.type==="link"||u.type==="labelLink"&&u._inactive)break;e[n][0]==="enter"&&u.type==="labelLink"&&(u._inactive=!0)}else if(i){if(e[n][0]==="enter"&&(u.type==="labelImage"||u.type==="labelLink")&&!u._balanced&&(a=n,u.type!=="labelLink")){r=2;break}}else u.type==="labelEnd"&&(i=n);const l={type:e[a][1].type==="labelLink"?"link":"image",start:{...e[a][1].start},end:{...e[e.length-1][1].end}},c={type:"label",start:{...e[a][1].start},end:{...e[i][1].end}},h={type:"labelText",start:{...e[a+r+2][1].end},end:{...e[i-2][1].start}};return o=[["enter",l,t],["enter",c,t]],o=Be(o,e.slice(a+1,a+r+3)),o=Be(o,[["enter",h,t]]),o=Be(o,Dn(t.parser.constructs.insideSpan.null,e.slice(a+r+4,i-3),t)),o=Be(o,[["exit",h,t],e[i-2],e[i-1],["exit",c,t]]),o=Be(o,e.slice(i+1)),o=Be(o,[["exit",l,t]]),we(e,a,e.length,o),e}function Gl(e,t,n){const r=this;let u=r.events.length,a,i;for(;u--;)if((r.events[u][1].type==="labelImage"||r.events[u][1].type==="labelLink")&&!r.events[u][1]._balanced){a=r.events[u][1];break}return o;function o(m){return a?a._inactive?f(m):(i=r.parser.defined.includes(ze(r.sliceSerialize({start:a.end,end:r.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(m),e.exit("labelMarker"),e.exit("labelEnd"),l):n(m)}function l(m){return m===40?e.attempt($l,h,i?h:f)(m):m===91?e.attempt(Wl,h,i?c:f)(m):i?h(m):f(m)}function c(m){return e.attempt(Ql,h,f)(m)}function h(m){return t(m)}function f(m){return a._balanced=!0,n(m)}}function Jl(e,t,n){return r;function r(f){return e.enter("resource"),e.enter("resourceMarker"),e.consume(f),e.exit("resourceMarker"),u}function u(f){return ie(f)?$t(e,a)(f):a(f)}function a(f){return f===41?h(f):ta(e,i,o,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(f)}function i(f){return ie(f)?$t(e,l)(f):h(f)}function o(f){return n(f)}function l(f){return f===34||f===39||f===40?ra(e,c,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(f):h(f)}function c(f){return ie(f)?$t(e,h)(f):h(f)}function h(f){return f===41?(e.enter("resourceMarker"),e.consume(f),e.exit("resourceMarker"),e.exit("resource"),t):n(f)}}function Zl(e,t,n){const r=this;return u;function u(o){return na.call(r,e,a,i,"reference","referenceMarker","referenceString")(o)}function a(o){return r.parser.defined.includes(ze(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(o):n(o)}function i(o){return n(o)}}function ec(e,t,n){return r;function r(a){return e.enter("reference"),e.enter("referenceMarker"),e.consume(a),e.exit("referenceMarker"),u}function u(a){return a===93?(e.enter("referenceMarker"),e.consume(a),e.exit("referenceMarker"),e.exit("reference"),t):n(a)}}const tc={name:"labelStartImage",resolveAll:Ur.resolveAll,tokenize:nc};function nc(e,t,n){const r=this;return u;function u(o){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(o),e.exit("labelImageMarker"),a}function a(o){return o===91?(e.enter("labelMarker"),e.consume(o),e.exit("labelMarker"),e.exit("labelImage"),i):n(o)}function i(o){return o===94&&"_hiddenFootnoteSupport"in r.parser.constructs?n(o):t(o)}}const rc={name:"labelStartLink",resolveAll:Ur.resolveAll,tokenize:uc};function uc(e,t,n){const r=this;return u;function u(i){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(i),e.exit("labelMarker"),e.exit("labelLink"),a}function a(i){return i===94&&"_hiddenFootnoteSupport"in r.parser.constructs?n(i):t(i)}}const Qn={name:"lineEnding",tokenize:ic};function ic(e,t){return n;function n(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),te(e,t,"linePrefix")}}const bn={name:"thematicBreak",tokenize:ac};function ac(e,t,n){let r=0,u;return a;function a(c){return e.enter("thematicBreak"),i(c)}function i(c){return u=c,o(c)}function o(c){return c===u?(e.enter("thematicBreakSequence"),l(c)):r>=3&&(c===null||Y(c))?(e.exit("thematicBreak"),t(c)):n(c)}function l(c){return c===u?(e.consume(c),r++,l):(e.exit("thematicBreakSequence"),J(c)?te(e,o,"whitespace")(c):o(c))}}const ye={continuation:{tokenize:cc},exit:hc,name:"list",tokenize:lc},sc={partial:!0,tokenize:dc},oc={partial:!0,tokenize:fc};function lc(e,t,n){const r=this,u=r.events[r.events.length-1];let a=u&&u[1].type==="linePrefix"?u[2].sliceSerialize(u[1],!0).length:0,i=0;return o;function o(d){const _=r.containerState.type||(d===42||d===43||d===45?"listUnordered":"listOrdered");if(_==="listUnordered"?!r.containerState.marker||d===r.containerState.marker:mr(d)){if(r.containerState.type||(r.containerState.type=_,e.enter(_,{_container:!0})),_==="listUnordered")return e.enter("listItemPrefix"),d===42||d===45?e.check(bn,n,c)(d):c(d);if(!r.interrupt||d===49)return e.enter("listItemPrefix"),e.enter("listItemValue"),l(d)}return n(d)}function l(d){return mr(d)&&++i<10?(e.consume(d),l):(!r.interrupt||i<2)&&(r.containerState.marker?d===r.containerState.marker:d===41||d===46)?(e.exit("listItemValue"),c(d)):n(d)}function c(d){return e.enter("listItemMarker"),e.consume(d),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||d,e.check(an,r.interrupt?n:h,e.attempt(sc,m,f))}function h(d){return r.containerState.initialBlankLine=!0,a++,m(d)}function f(d){return J(d)?(e.enter("listItemPrefixWhitespace"),e.consume(d),e.exit("listItemPrefixWhitespace"),m):n(d)}function m(d){return r.containerState.size=a+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(d)}}function cc(e,t,n){const r=this;return r.containerState._closeFlow=void 0,e.check(an,u,a);function u(o){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,te(e,t,"listItemIndent",r.containerState.size+1)(o)}function a(o){return r.containerState.furtherBlankLines||!J(o)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,i(o)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(oc,t,i)(o))}function i(o){return r.containerState._closeFlow=!0,r.interrupt=void 0,te(e,e.attempt(ye,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(o)}}function fc(e,t,n){const r=this;return te(e,u,"listItemIndent",r.containerState.size+1);function u(a){const i=r.events[r.events.length-1];return i&&i[1].type==="listItemIndent"&&i[2].sliceSerialize(i[1],!0).length===r.containerState.size?t(a):n(a)}}function hc(e){e.exit(this.containerState.type)}function dc(e,t,n){const r=this;return te(e,u,"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5);function u(a){const i=r.events[r.events.length-1];return!J(a)&&i&&i[1].type==="listItemPrefixWhitespace"?t(a):n(a)}}const Ru={name:"setextUnderline",resolveTo:pc,tokenize:mc};function pc(e,t){let n=e.length,r,u,a;for(;n--;)if(e[n][0]==="enter"){if(e[n][1].type==="content"){r=n;break}e[n][1].type==="paragraph"&&(u=n)}else e[n][1].type==="content"&&e.splice(n,1),!a&&e[n][1].type==="definition"&&(a=n);const i={type:"setextHeading",start:{...e[r][1].start},end:{...e[e.length-1][1].end}};return e[u][1].type="setextHeadingText",a?(e.splice(u,0,["enter",i,t]),e.splice(a+1,0,["exit",e[r][1],t]),e[r][1].end={...e[a][1].end}):e[r][1]=i,e.push(["exit",i,t]),e}function mc(e,t,n){const r=this;let u;return a;function a(c){let h=r.events.length,f;for(;h--;)if(r.events[h][1].type!=="lineEnding"&&r.events[h][1].type!=="linePrefix"&&r.events[h][1].type!=="content"){f=r.events[h][1].type==="paragraph";break}return!r.parser.lazy[r.now().line]&&(r.interrupt||f)?(e.enter("setextHeadingLine"),u=c,i(c)):n(c)}function i(c){return e.enter("setextHeadingLineSequence"),o(c)}function o(c){return c===u?(e.consume(c),o):(e.exit("setextHeadingLineSequence"),J(c)?te(e,l,"lineSuffix")(c):l(c))}function l(c){return c===null||Y(c)?(e.exit("setextHeadingLine"),t(c)):n(c)}}const Ec={tokenize:gc};function gc(e){const t=this,n=e.attempt(an,r,e.attempt(this.parser.constructs.flowInitial,u,te(e,e.attempt(this.parser.constructs.flow,u,e.attempt(Cl,u)),"linePrefix")));return n;function r(a){if(a===null){e.consume(a);return}return e.enter("lineEndingBlank"),e.consume(a),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n}function u(a){if(a===null){e.consume(a);return}return e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),t.currentConstruct=void 0,n}}const Tc={resolveAll:ia()},bc=ua("string"),Ac=ua("text");function ua(e){return{resolveAll:ia(e==="text"?_c:void 0),tokenize:t};function t(n){const r=this,u=this.parser.constructs[e],a=n.attempt(u,i,o);return i;function i(h){return c(h)?a(h):o(h)}function o(h){if(h===null){n.consume(h);return}return n.enter("data"),n.consume(h),l}function l(h){return c(h)?(n.exit("data"),a(h)):(n.consume(h),l)}function c(h){if(h===null)return!0;const f=u[h];let m=-1;if(f)for(;++m<f.length;){const d=f[m];if(!d.previous||d.previous.call(r,r.previous))return!0}return!1}}}function ia(e){return t;function t(n,r){let u=-1,a;for(;++u<=n.length;)a===void 0?n[u]&&n[u][1].type==="data"&&(a=u,u++):(!n[u]||n[u][1].type!=="data")&&(u!==a+2&&(n[a][1].end=n[u-1][1].end,n.splice(a+2,u-a-2),u=a+2),a=void 0);return e?e(n,r):n}}function _c(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||e[n][1].type==="lineEnding")&&e[n-1][1].type==="data"){const r=e[n-1][1],u=t.sliceStream(r);let a=u.length,i=-1,o=0,l;for(;a--;){const c=u[a];if(typeof c=="string"){for(i=c.length;c.charCodeAt(i-1)===32;)o++,i--;if(i)break;i=-1}else if(c===-2)l=!0,o++;else if(c!==-1){a++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(o=0),o){const c={type:n===e.length||l||o<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:a?i:r.start._bufferIndex+i,_index:r.start._index+a,line:r.end.line,column:r.end.column-o,offset:r.end.offset-o},end:{...r.end}};r.end={...c.start},r.start.offset===r.end.offset?Object.assign(r,c):(e.splice(n,0,["enter",c,t],["exit",c,t]),n+=2)}n++}return e}const Cc={42:ye,43:ye,45:ye,48:ye,49:ye,50:ye,51:ye,52:ye,53:ye,54:ye,55:ye,56:ye,57:ye,62:Gi},kc={91:xl},Sc={[-2]:Wn,[-1]:Wn,32:Wn},yc={35:Pl,42:bn,45:[Ru,bn],60:Fl,61:Ru,95:bn,96:Lu,126:Lu},Ic={38:Zi,92:Ji},xc={[-5]:Qn,[-4]:Qn,[-3]:Qn,33:tc,38:Zi,42:Er,60:[rl,jl],91:rc,92:[Dl,Ji],93:Ur,95:Er,96:El},Nc={null:[Er,Tc]},Oc={null:[42,95]},Lc={null:[]},Dc=Object.freeze(Object.defineProperty({__proto__:null,attentionMarkers:Oc,contentInitial:kc,disable:Lc,document:Cc,flow:yc,flowInitial:Sc,insideSpan:Nc,string:Ic,text:xc},Symbol.toStringTag,{value:"Module"}));function Rc(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0};const u={},a=[];let i=[],o=[];const l={attempt:Q(V),check:Q(x),consume:F,enter:B,exit:j,interrupt:Q(x,{interrupt:!0})},c={code:null,containerState:{},defineSkip:y,events:[],now:_,parser:e,previous:null,sliceSerialize:m,sliceStream:d,write:f};let h=t.tokenize.call(c,l);return t.resolveAll&&a.push(t),c;function f(N){return i=Be(i,N),M(),i[i.length-1]!==null?[]:(ne(t,0),c.events=Dn(a,c.events,c),c.events)}function m(N,L){return wc(d(N),L)}function d(N){return Pc(i,N)}function _(){const{_bufferIndex:N,_index:L,line:H,column:v,offset:z}=r;return{_bufferIndex:N,_index:L,line:H,column:v,offset:z}}function y(N){u[N.line]=N.column,A()}function M(){let N;for(;r._index<i.length;){const L=i[r._index];if(typeof L=="string")for(N=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===N&&r._bufferIndex<L.length;)k(L.charCodeAt(r._bufferIndex));else k(L)}}function k(N){h=h(N)}function F(N){Y(N)?(r.line++,r.column=1,r.offset+=N===-3?2:1,A()):N!==-1&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===i[r._index].length&&(r._bufferIndex=-1,r._index++)),c.previous=N}function B(N,L){const H=L||{};return H.type=N,H.start=_(),c.events.push(["enter",H,c]),o.push(H),H}function j(N){const L=o.pop();return L.end=_(),c.events.push(["exit",L,c]),L}function V(N,L){ne(N,L.from)}function x(N,L){L.restore()}function Q(N,L){return H;function H(v,z,ae){let he,Ce,Me,T;return Array.isArray(v)?Ue(v):"tokenize"in v?Ue([v]):Ne(v);function Ne(re){return qe;function qe(ke){const Je=ke!==null&&re[ke],Ze=ke!==null&&re.null,at=[...Array.isArray(Je)?Je:Je?[Je]:[],...Array.isArray(Ze)?Ze:Ze?[Ze]:[]];return Ue(at)(ke)}}function Ue(re){return he=re,Ce=0,re.length===0?ae:b(re[Ce])}function b(re){return qe;function qe(ke){return T=ee(),Me=re,re.partial||(c.currentConstruct=re),re.name&&c.parser.constructs.disable.null.includes(re.name)?We():re.tokenize.call(L?Object.assign(Object.create(c),L):c,l,Oe,We)(ke)}}function Oe(re){return N(Me,T),z}function We(re){return T.restore(),++Ce<he.length?b(he[Ce]):ae}}}function ne(N,L){N.resolveAll&&!a.includes(N)&&a.push(N),N.resolve&&we(c.events,L,c.events.length-L,N.resolve(c.events.slice(L),c)),N.resolveTo&&(c.events=N.resolveTo(c.events,c))}function ee(){const N=_(),L=c.previous,H=c.currentConstruct,v=c.events.length,z=Array.from(o);return{from:v,restore:ae};function ae(){r=N,c.previous=L,c.currentConstruct=H,c.events.length=v,o=z,A()}}function A(){r.line in u&&r.column<2&&(r.column=u[r.line],r.offset+=u[r.line]-1)}}function Pc(e,t){const n=t.start._index,r=t.start._bufferIndex,u=t.end._index,a=t.end._bufferIndex;let i;if(n===u)i=[e[n].slice(r,a)];else{if(i=e.slice(n,u),r>-1){const o=i[0];typeof o=="string"?i[0]=o.slice(r):i.shift()}a>0&&i.push(e[u].slice(0,a))}return i}function wc(e,t){let n=-1;const r=[];let u;for(;++n<e.length;){const a=e[n];let i;if(typeof a=="string")i=a;else switch(a){case-5:{i="\r";break}case-4:{i=`
`;break}case-3:{i=`\r
`;break}case-2:{i=t?" ":"	";break}case-1:{if(!t&&u)continue;i=" ";break}default:i=String.fromCharCode(a)}u=a===-2,r.push(i)}return r.join("")}function Mc(e){const r={constructs:Xi([Dc,...(e||{}).extensions||[]]),content:u(Ko),defined:[],document:u(Jo),flow:u(Ec),lazy:{},string:u(bc),text:u(Ac)};return r;function u(a){return i;function i(o){return Rc(r,a,o)}}}function Bc(e){for(;!ea(e););return e}const Pu=/[\0\t\n\r]/g;function Fc(){let e=1,t="",n=!0,r;return u;function u(a,i,o){const l=[];let c,h,f,m,d;for(a=t+(typeof a=="string"?a.toString():new TextDecoder(i||void 0).decode(a)),f=0,t="",n&&(a.charCodeAt(0)===65279&&f++,n=void 0);f<a.length;){if(Pu.lastIndex=f,c=Pu.exec(a),m=c&&c.index!==void 0?c.index:a.length,d=a.charCodeAt(m),!c){t=a.slice(f);break}if(d===10&&f===m&&r)l.push(-3),r=void 0;else switch(r&&(l.push(-5),r=void 0),f<m&&(l.push(a.slice(f,m)),e+=m-f),d){case 0:{l.push(65533),e++;break}case 9:{for(h=Math.ceil(e/4)*4,l.push(-2);e++<h;)l.push(-1);break}case 10:{l.push(-4),e=1;break}default:r=!0,e=1}f=m+1}return o&&(r&&l.push(-5),t&&l.push(t),l.push(null)),l}}const vc=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function Uc(e){return e.replace(vc,Hc)}function Hc(e,t,n){if(t)return t;if(n.charCodeAt(0)===35){const u=n.charCodeAt(1),a=u===120||u===88;return Ki(n.slice(a?2:1),a?16:10)}return vr(n)||e}const aa={}.hasOwnProperty;function zc(e,t,n){return typeof t!="string"&&(n=t,t=void 0),Yc(n)(Bc(Mc(n).document().write(Fc()(e,t,!0))))}function Yc(e){const t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:a(hu),autolinkProtocol:ee,autolinkEmail:ee,atxHeading:a(lu),blockQuote:a(Ze),characterEscape:ee,characterReference:ee,codeFenced:a(at),codeFencedFenceInfo:i,codeFencedFenceMeta:i,codeIndented:a(at,i),codeText:a(zn,i),codeTextData:ee,data:ee,codeFlowValue:ee,definition:a(hn),definitionDestinationString:i,definitionLabelString:i,definitionTitleString:i,emphasis:a(Vs),hardBreakEscape:a(cu),hardBreakTrailing:a(cu),htmlFlow:a(fu,i),htmlFlowData:ee,htmlText:a(fu,i),htmlTextData:ee,image:a($s),label:i,link:a(hu),listItem:a(Ws),listItemValue:m,listOrdered:a(du,f),listUnordered:a(du),paragraph:a(Qs),reference:b,referenceString:i,resourceDestinationString:i,resourceTitleString:i,setextHeading:a(lu),strong:a(Xs),thematicBreak:a(Gs)},exit:{atxHeading:l(),atxHeadingSequence:V,autolink:l(),autolinkEmail:Je,autolinkProtocol:ke,blockQuote:l(),characterEscapeValue:A,characterReferenceMarkerHexadecimal:We,characterReferenceMarkerNumeric:We,characterReferenceValue:re,characterReference:qe,codeFenced:l(M),codeFencedFence:y,codeFencedFenceInfo:d,codeFencedFenceMeta:_,codeFlowValue:A,codeIndented:l(k),codeText:l(z),codeTextData:A,data:A,definition:l(),definitionDestinationString:j,definitionLabelString:F,definitionTitleString:B,emphasis:l(),hardBreakEscape:l(L),hardBreakTrailing:l(L),htmlFlow:l(H),htmlFlowData:A,htmlText:l(v),htmlTextData:A,image:l(he),label:Me,labelText:Ce,lineEnding:N,link:l(ae),listItem:l(),listOrdered:l(),listUnordered:l(),paragraph:l(),referenceString:Oe,resourceDestinationString:T,resourceTitleString:Ne,resource:Ue,setextHeading:l(ne),setextHeadingLineSequence:Q,setextHeadingText:x,strong:l(),thematicBreak:l()}};sa(t,(e||{}).mdastExtensions||[]);const n={};return r;function r(I){let U={type:"root",children:[]};const $={stack:[U],tokenStack:[],config:t,enter:o,exit:c,buffer:i,resume:h,data:n},Z=[];let ue=-1;for(;++ue<I.length;)if(I[ue][1].type==="listOrdered"||I[ue][1].type==="listUnordered")if(I[ue][0]==="enter")Z.push(ue);else{const He=Z.pop();ue=u(I,He,ue)}for(ue=-1;++ue<I.length;){const He=t[I[ue][0]];aa.call(He,I[ue][1].type)&&He[I[ue][1].type].call(Object.assign({sliceSerialize:I[ue][2].sliceSerialize},$),I[ue][1])}if($.tokenStack.length>0){const He=$.tokenStack[$.tokenStack.length-1];(He[1]||wu).call($,void 0,He[0])}for(U.position={start:tt(I.length>0?I[0][1].start:{line:1,column:1,offset:0}),end:tt(I.length>0?I[I.length-2][1].end:{line:1,column:1,offset:0})},ue=-1;++ue<t.transforms.length;)U=t.transforms[ue](U)||U;return U}function u(I,U,$){let Z=U-1,ue=-1,He=!1,st,Qe,Mt,Bt;for(;++Z<=$;){const Le=I[Z];switch(Le[1].type){case"listUnordered":case"listOrdered":case"blockQuote":{Le[0]==="enter"?ue++:ue--,Bt=void 0;break}case"lineEndingBlank":{Le[0]==="enter"&&(st&&!Bt&&!ue&&!Mt&&(Mt=Z),Bt=void 0);break}case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:Bt=void 0}if(!ue&&Le[0]==="enter"&&Le[1].type==="listItemPrefix"||ue===-1&&Le[0]==="exit"&&(Le[1].type==="listUnordered"||Le[1].type==="listOrdered")){if(st){let gt=Z;for(Qe=void 0;gt--;){const Xe=I[gt];if(Xe[1].type==="lineEnding"||Xe[1].type==="lineEndingBlank"){if(Xe[0]==="exit")continue;Qe&&(I[Qe][1].type="lineEndingBlank",He=!0),Xe[1].type="lineEnding",Qe=gt}else if(!(Xe[1].type==="linePrefix"||Xe[1].type==="blockQuotePrefix"||Xe[1].type==="blockQuotePrefixWhitespace"||Xe[1].type==="blockQuoteMarker"||Xe[1].type==="listItemIndent"))break}Mt&&(!Qe||Mt<Qe)&&(st._spread=!0),st.end=Object.assign({},Qe?I[Qe][1].start:Le[1].end),I.splice(Qe||Z,0,["exit",st,Le[2]]),Z++,$++}if(Le[1].type==="listItemPrefix"){const gt={type:"listItem",_spread:!1,start:Object.assign({},Le[1].start),end:void 0};st=gt,I.splice(Z,0,["enter",gt,Le[2]]),Z++,$++,Mt=void 0,Bt=!0}}}return I[U][1]._spread=He,$}function a(I,U){return $;function $(Z){o.call(this,I(Z),Z),U&&U.call(this,Z)}}function i(){this.stack.push({type:"fragment",children:[]})}function o(I,U,$){this.stack[this.stack.length-1].children.push(I),this.stack.push(I),this.tokenStack.push([U,$||void 0]),I.position={start:tt(U.start),end:void 0}}function l(I){return U;function U($){I&&I.call(this,$),c.call(this,$)}}function c(I,U){const $=this.stack.pop(),Z=this.tokenStack.pop();if(Z)Z[0].type!==I.type&&(U?U.call(this,I,Z[0]):(Z[1]||wu).call(this,I,Z[0]));else throw new Error("Cannot close `"+I.type+"` ("+Vt({start:I.start,end:I.end})+"): it’s not open");$.position.end=tt(I.end)}function h(){return Fr(this.stack.pop())}function f(){this.data.expectingFirstListItemValue=!0}function m(I){if(this.data.expectingFirstListItemValue){const U=this.stack[this.stack.length-2];U.start=Number.parseInt(this.sliceSerialize(I),10),this.data.expectingFirstListItemValue=void 0}}function d(){const I=this.resume(),U=this.stack[this.stack.length-1];U.lang=I}function _(){const I=this.resume(),U=this.stack[this.stack.length-1];U.meta=I}function y(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function M(){const I=this.resume(),U=this.stack[this.stack.length-1];U.value=I.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function k(){const I=this.resume(),U=this.stack[this.stack.length-1];U.value=I.replace(/(\r?\n|\r)$/g,"")}function F(I){const U=this.resume(),$=this.stack[this.stack.length-1];$.label=U,$.identifier=ze(this.sliceSerialize(I)).toLowerCase()}function B(){const I=this.resume(),U=this.stack[this.stack.length-1];U.title=I}function j(){const I=this.resume(),U=this.stack[this.stack.length-1];U.url=I}function V(I){const U=this.stack[this.stack.length-1];if(!U.depth){const $=this.sliceSerialize(I).length;U.depth=$}}function x(){this.data.setextHeadingSlurpLineEnding=!0}function Q(I){const U=this.stack[this.stack.length-1];U.depth=this.sliceSerialize(I).codePointAt(0)===61?1:2}function ne(){this.data.setextHeadingSlurpLineEnding=void 0}function ee(I){const $=this.stack[this.stack.length-1].children;let Z=$[$.length-1];(!Z||Z.type!=="text")&&(Z=Ks(),Z.position={start:tt(I.start),end:void 0},$.push(Z)),this.stack.push(Z)}function A(I){const U=this.stack.pop();U.value+=this.sliceSerialize(I),U.position.end=tt(I.end)}function N(I){const U=this.stack[this.stack.length-1];if(this.data.atHardBreak){const $=U.children[U.children.length-1];$.position.end=tt(I.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(U.type)&&(ee.call(this,I),A.call(this,I))}function L(){this.data.atHardBreak=!0}function H(){const I=this.resume(),U=this.stack[this.stack.length-1];U.value=I}function v(){const I=this.resume(),U=this.stack[this.stack.length-1];U.value=I}function z(){const I=this.resume(),U=this.stack[this.stack.length-1];U.value=I}function ae(){const I=this.stack[this.stack.length-1];if(this.data.inReference){const U=this.data.referenceType||"shortcut";I.type+="Reference",I.referenceType=U,delete I.url,delete I.title}else delete I.identifier,delete I.label;this.data.referenceType=void 0}function he(){const I=this.stack[this.stack.length-1];if(this.data.inReference){const U=this.data.referenceType||"shortcut";I.type+="Reference",I.referenceType=U,delete I.url,delete I.title}else delete I.identifier,delete I.label;this.data.referenceType=void 0}function Ce(I){const U=this.sliceSerialize(I),$=this.stack[this.stack.length-2];$.label=Uc(U),$.identifier=ze(U).toLowerCase()}function Me(){const I=this.stack[this.stack.length-1],U=this.resume(),$=this.stack[this.stack.length-1];if(this.data.inReference=!0,$.type==="link"){const Z=I.children;$.children=Z}else $.alt=U}function T(){const I=this.resume(),U=this.stack[this.stack.length-1];U.url=I}function Ne(){const I=this.resume(),U=this.stack[this.stack.length-1];U.title=I}function Ue(){this.data.inReference=void 0}function b(){this.data.referenceType="collapsed"}function Oe(I){const U=this.resume(),$=this.stack[this.stack.length-1];$.label=U,$.identifier=ze(this.sliceSerialize(I)).toLowerCase(),this.data.referenceType="full"}function We(I){this.data.characterReferenceType=I.type}function re(I){const U=this.sliceSerialize(I),$=this.data.characterReferenceType;let Z;$?(Z=Ki(U,$==="characterReferenceMarkerNumeric"?10:16),this.data.characterReferenceType=void 0):Z=vr(U);const ue=this.stack[this.stack.length-1];ue.value+=Z}function qe(I){const U=this.stack.pop();U.position.end=tt(I.end)}function ke(I){A.call(this,I);const U=this.stack[this.stack.length-1];U.url=this.sliceSerialize(I)}function Je(I){A.call(this,I);const U=this.stack[this.stack.length-1];U.url="mailto:"+this.sliceSerialize(I)}function Ze(){return{type:"blockquote",children:[]}}function at(){return{type:"code",lang:null,meta:null,value:""}}function zn(){return{type:"inlineCode",value:""}}function hn(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function Vs(){return{type:"emphasis",children:[]}}function lu(){return{type:"heading",depth:0,children:[]}}function cu(){return{type:"break"}}function fu(){return{type:"html",value:""}}function $s(){return{type:"image",title:null,url:"",alt:null}}function hu(){return{type:"link",title:null,url:"",children:[]}}function du(I){return{type:"list",ordered:I.type==="listOrdered",start:null,spread:I._spread,children:[]}}function Ws(I){return{type:"listItem",spread:I._spread,checked:null,children:[]}}function Qs(){return{type:"paragraph",children:[]}}function Xs(){return{type:"strong",children:[]}}function Ks(){return{type:"text",value:""}}function Gs(){return{type:"thematicBreak"}}}function tt(e){return{line:e.line,column:e.column,offset:e.offset}}function sa(e,t){let n=-1;for(;++n<t.length;){const r=t[n];Array.isArray(r)?sa(e,r):qc(e,r)}}function qc(e,t){let n;for(n in t)if(aa.call(t,n))switch(n){case"canContainEols":{const r=t[n];r&&e[n].push(...r);break}case"transforms":{const r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{const r=t[n];r&&Object.assign(e[n],r);break}}}function wu(e,t){throw e?new Error("Cannot close `"+e.type+"` ("+Vt({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+Vt({start:t.start,end:t.end})+") is open"):new Error("Cannot close document, a token (`"+t.type+"`, "+Vt({start:t.start,end:t.end})+") is still open")}function jc(e){const t=this;t.parser=n;function n(r){return zc(r,{...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})}}function Vc(e,t){const n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)}function $c(e,t){const n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:`
`}]}function Wc(e,t){const n=t.value?t.value+`
`:"",r={};t.lang&&(r.className=["language-"+t.lang]);let u={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(u.data={meta:t.meta}),e.patch(t,u),u=e.applyData(t,u),u={type:"element",tagName:"pre",properties:{},children:[u]},e.patch(t,u),u}function Qc(e,t){const n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function Xc(e,t){const n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function Kc(e,t){const n=typeof e.options.clobberPrefix=="string"?e.options.clobberPrefix:"user-content-",r=String(t.identifier).toUpperCase(),u=Ot(r.toLowerCase()),a=e.footnoteOrder.indexOf(r);let i,o=e.footnoteCounts.get(r);o===void 0?(o=0,e.footnoteOrder.push(r),i=e.footnoteOrder.length):i=a+1,o+=1,e.footnoteCounts.set(r,o);const l={type:"element",tagName:"a",properties:{href:"#"+n+"fn-"+u,id:n+"fnref-"+u+(o>1?"-"+o:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(i)}]};e.patch(t,l);const c={type:"element",tagName:"sup",properties:{},children:[l]};return e.patch(t,c),e.applyData(t,c)}function Gc(e,t){const n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function Jc(e,t){if(e.options.allowDangerousHtml){const n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}}function oa(e,t){const n=t.referenceType;let r="]";if(n==="collapsed"?r+="[]":n==="full"&&(r+="["+(t.label||t.identifier)+"]"),t.type==="imageReference")return[{type:"text",value:"!["+t.alt+r}];const u=e.all(t),a=u[0];a&&a.type==="text"?a.value="["+a.value:u.unshift({type:"text",value:"["});const i=u[u.length-1];return i&&i.type==="text"?i.value+=r:u.push({type:"text",value:r}),u}function Zc(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return oa(e,t);const u={src:Ot(r.url||""),alt:t.alt};r.title!==null&&r.title!==void 0&&(u.title=r.title);const a={type:"element",tagName:"img",properties:u,children:[]};return e.patch(t,a),e.applyData(t,a)}function ef(e,t){const n={src:Ot(t.url)};t.alt!==null&&t.alt!==void 0&&(n.alt=t.alt),t.title!==null&&t.title!==void 0&&(n.title=t.title);const r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)}function tf(e,t){const n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);const r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)}function nf(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return oa(e,t);const u={href:Ot(r.url||"")};r.title!==null&&r.title!==void 0&&(u.title=r.title);const a={type:"element",tagName:"a",properties:u,children:e.all(t)};return e.patch(t,a),e.applyData(t,a)}function rf(e,t){const n={href:Ot(t.url)};t.title!==null&&t.title!==void 0&&(n.title=t.title);const r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)}function uf(e,t,n){const r=e.all(t),u=n?af(n):la(t),a={},i=[];if(typeof t.checked=="boolean"){const h=r[0];let f;h&&h.type==="element"&&h.tagName==="p"?f=h:(f={type:"element",tagName:"p",properties:{},children:[]},r.unshift(f)),f.children.length>0&&f.children.unshift({type:"text",value:" "}),f.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),a.className=["task-list-item"]}let o=-1;for(;++o<r.length;){const h=r[o];(u||o!==0||h.type!=="element"||h.tagName!=="p")&&i.push({type:"text",value:`
`}),h.type==="element"&&h.tagName==="p"&&!u?i.push(...h.children):i.push(h)}const l=r[r.length-1];l&&(u||l.type!=="element"||l.tagName!=="p")&&i.push({type:"text",value:`
`});const c={type:"element",tagName:"li",properties:a,children:i};return e.patch(t,c),e.applyData(t,c)}function af(e){let t=!1;if(e.type==="list"){t=e.spread||!1;const n=e.children;let r=-1;for(;!t&&++r<n.length;)t=la(n[r])}return t}function la(e){const t=e.spread;return t??e.children.length>1}function sf(e,t){const n={},r=e.all(t);let u=-1;for(typeof t.start=="number"&&t.start!==1&&(n.start=t.start);++u<r.length;){const i=r[u];if(i.type==="element"&&i.tagName==="li"&&i.properties&&Array.isArray(i.properties.className)&&i.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}const a={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,a),e.applyData(t,a)}function of(e,t){const n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function lf(e,t){const n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)}function cf(e,t){const n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function ff(e,t){const n=e.all(t),r=n.shift(),u=[];if(r){const i={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],i),u.push(i)}if(n.length>0){const i={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},o=Pr(t.children[1]),l=Yi(t.children[t.children.length-1]);o&&l&&(i.position={start:o,end:l}),u.push(i)}const a={type:"element",tagName:"table",properties:{},children:e.wrap(u,!0)};return e.patch(t,a),e.applyData(t,a)}function hf(e,t,n){const r=n?n.children:void 0,a=(r?r.indexOf(t):1)===0?"th":"td",i=n&&n.type==="table"?n.align:void 0,o=i?i.length:t.children.length;let l=-1;const c=[];for(;++l<o;){const f=t.children[l],m={},d=i?i[l]:void 0;d&&(m.align=d);let _={type:"element",tagName:a,properties:m,children:[]};f&&(_.children=e.all(f),e.patch(f,_),_=e.applyData(f,_)),c.push(_)}const h={type:"element",tagName:"tr",properties:{},children:e.wrap(c,!0)};return e.patch(t,h),e.applyData(t,h)}function df(e,t){const n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}const Mu=9,Bu=32;function pf(e){const t=String(e),n=/\r?\n|\r/g;let r=n.exec(t),u=0;const a=[];for(;r;)a.push(Fu(t.slice(u,r.index),u>0,!0),r[0]),u=r.index+r[0].length,r=n.exec(t);return a.push(Fu(t.slice(u),u>0,!1)),a.join("")}function Fu(e,t,n){let r=0,u=e.length;if(t){let a=e.codePointAt(r);for(;a===Mu||a===Bu;)r++,a=e.codePointAt(r)}if(n){let a=e.codePointAt(u-1);for(;a===Mu||a===Bu;)u--,a=e.codePointAt(u-1)}return u>r?e.slice(r,u):""}function mf(e,t){const n={type:"text",value:pf(String(t.value))};return e.patch(t,n),e.applyData(t,n)}function Ef(e,t){const n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)}const gf={blockquote:Vc,break:$c,code:Wc,delete:Qc,emphasis:Xc,footnoteReference:Kc,heading:Gc,html:Jc,imageReference:Zc,image:ef,inlineCode:tf,linkReference:nf,link:rf,listItem:uf,list:sf,paragraph:of,root:lf,strong:cf,table:ff,tableCell:df,tableRow:hf,text:mf,thematicBreak:Ef,toml:dn,yaml:dn,definition:dn,footnoteDefinition:dn};function dn(){}const ca=-1,Rn=0,Wt=1,kn=2,Hr=3,zr=4,Yr=5,qr=6,fa=7,ha=8,vu=typeof self=="object"?self:globalThis,Tf=(e,t)=>{const n=(u,a)=>(e.set(a,u),u),r=u=>{if(e.has(u))return e.get(u);const[a,i]=t[u];switch(a){case Rn:case ca:return n(i,u);case Wt:{const o=n([],u);for(const l of i)o.push(r(l));return o}case kn:{const o=n({},u);for(const[l,c]of i)o[r(l)]=r(c);return o}case Hr:return n(new Date(i),u);case zr:{const{source:o,flags:l}=i;return n(new RegExp(o,l),u)}case Yr:{const o=n(new Map,u);for(const[l,c]of i)o.set(r(l),r(c));return o}case qr:{const o=n(new Set,u);for(const l of i)o.add(r(l));return o}case fa:{const{name:o,message:l}=i;return n(new vu[o](l),u)}case ha:return n(BigInt(i),u);case"BigInt":return n(Object(BigInt(i)),u);case"ArrayBuffer":return n(new Uint8Array(i).buffer,i);case"DataView":{const{buffer:o}=new Uint8Array(i);return n(new DataView(o),i)}}return n(new vu[a](i),u)};return r},Uu=e=>Tf(new Map,e)(0),bt="",{toString:bf}={},{keys:Af}=Object,Ht=e=>{const t=typeof e;if(t!=="object"||!e)return[Rn,t];const n=bf.call(e).slice(8,-1);switch(n){case"Array":return[Wt,bt];case"Object":return[kn,bt];case"Date":return[Hr,bt];case"RegExp":return[zr,bt];case"Map":return[Yr,bt];case"Set":return[qr,bt];case"DataView":return[Wt,n]}return n.includes("Array")?[Wt,n]:n.includes("Error")?[fa,n]:[kn,n]},pn=([e,t])=>e===Rn&&(t==="function"||t==="symbol"),_f=(e,t,n,r)=>{const u=(i,o)=>{const l=r.push(i)-1;return n.set(o,l),l},a=i=>{if(n.has(i))return n.get(i);let[o,l]=Ht(i);switch(o){case Rn:{let h=i;switch(l){case"bigint":o=ha,h=i.toString();break;case"function":case"symbol":if(e)throw new TypeError("unable to serialize "+l);h=null;break;case"undefined":return u([ca],i)}return u([o,h],i)}case Wt:{if(l){let m=i;return l==="DataView"?m=new Uint8Array(i.buffer):l==="ArrayBuffer"&&(m=new Uint8Array(i)),u([l,[...m]],i)}const h=[],f=u([o,h],i);for(const m of i)h.push(a(m));return f}case kn:{if(l)switch(l){case"BigInt":return u([l,i.toString()],i);case"Boolean":case"Number":case"String":return u([l,i.valueOf()],i)}if(t&&"toJSON"in i)return a(i.toJSON());const h=[],f=u([o,h],i);for(const m of Af(i))(e||!pn(Ht(i[m])))&&h.push([a(m),a(i[m])]);return f}case Hr:return u([o,i.toISOString()],i);case zr:{const{source:h,flags:f}=i;return u([o,{source:h,flags:f}],i)}case Yr:{const h=[],f=u([o,h],i);for(const[m,d]of i)(e||!(pn(Ht(m))||pn(Ht(d))))&&h.push([a(m),a(d)]);return f}case qr:{const h=[],f=u([o,h],i);for(const m of i)(e||!pn(Ht(m)))&&h.push(a(m));return f}}const{message:c}=i;return u([o,{name:l,message:c}],i)};return a},Hu=(e,{json:t,lossy:n}={})=>{const r=[];return _f(!(t||n),!!t,new Map,r)(e),r},Sn=typeof structuredClone=="function"?(e,t)=>t&&("json"in t||"lossy"in t)?Uu(Hu(e,t)):structuredClone(e):(e,t)=>Uu(Hu(e,t));function Cf(e,t){const n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function kf(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}function Sf(e){const t=typeof e.options.clobberPrefix=="string"?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||Cf,r=e.options.footnoteBackLabel||kf,u=e.options.footnoteLabel||"Footnotes",a=e.options.footnoteLabelTagName||"h2",i=e.options.footnoteLabelProperties||{className:["sr-only"]},o=[];let l=-1;for(;++l<e.footnoteOrder.length;){const c=e.footnoteById.get(e.footnoteOrder[l]);if(!c)continue;const h=e.all(c),f=String(c.identifier).toUpperCase(),m=Ot(f.toLowerCase());let d=0;const _=[],y=e.footnoteCounts.get(f);for(;y!==void 0&&++d<=y;){_.length>0&&_.push({type:"text",value:" "});let F=typeof n=="string"?n:n(l,d);typeof F=="string"&&(F={type:"text",value:F}),_.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+m+(d>1?"-"+d:""),dataFootnoteBackref:"",ariaLabel:typeof r=="string"?r:r(l,d),className:["data-footnote-backref"]},children:Array.isArray(F)?F:[F]})}const M=h[h.length-1];if(M&&M.type==="element"&&M.tagName==="p"){const F=M.children[M.children.length-1];F&&F.type==="text"?F.value+=" ":M.children.push({type:"text",value:" "}),M.children.push(..._)}else h.push(..._);const k={type:"element",tagName:"li",properties:{id:t+"fn-"+m},children:e.wrap(h,!0)};e.patch(c,k),o.push(k)}if(o.length!==0)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:a,properties:{...Sn(i),id:"footnote-label"},children:[{type:"text",value:u}]},{type:"text",value:`
`},{type:"element",tagName:"ol",properties:{},children:e.wrap(o,!0)},{type:"text",value:`
`}]}}const Pn=function(e){if(e==null)return Nf;if(typeof e=="function")return wn(e);if(typeof e=="object")return Array.isArray(e)?yf(e):If(e);if(typeof e=="string")return xf(e);throw new Error("Expected function, string, or object as test")};function yf(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=Pn(e[n]);return wn(r);function r(...u){let a=-1;for(;++a<t.length;)if(t[a].apply(this,u))return!0;return!1}}function If(e){const t=e;return wn(n);function n(r){const u=r;let a;for(a in e)if(u[a]!==t[a])return!1;return!0}}function xf(e){return wn(t);function t(n){return n&&n.type===e}}function wn(e){return t;function t(n,r,u){return!!(Of(n)&&e.call(this,n,typeof r=="number"?r:void 0,u||void 0))}}function Nf(){return!0}function Of(e){return e!==null&&typeof e=="object"&&"type"in e}const da=[],Lf=!0,gr=!1,Df="skip";function pa(e,t,n,r){let u;typeof t=="function"&&typeof n!="function"?(r=n,n=t):u=t;const a=Pn(u),i=r?-1:1;o(e,void 0,[])();function o(l,c,h){const f=l&&typeof l=="object"?l:{};if(typeof f.type=="string"){const d=typeof f.tagName=="string"?f.tagName:typeof f.name=="string"?f.name:void 0;Object.defineProperty(m,"name",{value:"node ("+(l.type+(d?"<"+d+">":""))+")"})}return m;function m(){let d=da,_,y,M;if((!t||a(l,c,h[h.length-1]||void 0))&&(d=Rf(n(l,h)),d[0]===gr))return d;if("children"in l&&l.children){const k=l;if(k.children&&d[0]!==Df)for(y=(r?k.children.length:-1)+i,M=h.concat(k);y>-1&&y<k.children.length;){const F=k.children[y];if(_=o(F,y,M)(),_[0]===gr)return _;y=typeof _[1]=="number"?_[1]:y+i}}return d}}}function Rf(e){return Array.isArray(e)?e:typeof e=="number"?[Lf,e]:e==null?da:[e]}function jr(e,t,n,r){let u,a,i;typeof t=="function"?(a=void 0,i=t,u=n):(a=t,i=n,u=r),pa(e,a,o,u);function o(l,c){const h=c[c.length-1],f=h?h.children.indexOf(l):void 0;return i(l,f,h)}}const Tr={}.hasOwnProperty,Pf={};function wf(e,t){const n=t||Pf,r=new Map,u=new Map,a=new Map,i={...gf,...n.handlers},o={all:c,applyData:Bf,definitionById:r,footnoteById:u,footnoteCounts:a,footnoteOrder:[],handlers:i,one:l,options:n,patch:Mf,wrap:vf};return jr(e,function(h){if(h.type==="definition"||h.type==="footnoteDefinition"){const f=h.type==="definition"?r:u,m=String(h.identifier).toUpperCase();f.has(m)||f.set(m,h)}}),o;function l(h,f){const m=h.type,d=o.handlers[m];if(Tr.call(o.handlers,m)&&d)return d(o,h,f);if(o.options.passThrough&&o.options.passThrough.includes(m)){if("children"in h){const{children:y,...M}=h,k=Sn(M);return k.children=o.all(h),k}return Sn(h)}return(o.options.unknownHandler||Ff)(o,h,f)}function c(h){const f=[];if("children"in h){const m=h.children;let d=-1;for(;++d<m.length;){const _=o.one(m[d],h);if(_){if(d&&m[d-1].type==="break"&&(!Array.isArray(_)&&_.type==="text"&&(_.value=zu(_.value)),!Array.isArray(_)&&_.type==="element")){const y=_.children[0];y&&y.type==="text"&&(y.value=zu(y.value))}Array.isArray(_)?f.push(..._):f.push(_)}}}return f}}function Mf(e,t){e.position&&(t.position=Co(e))}function Bf(e,t){let n=t;if(e&&e.data){const r=e.data.hName,u=e.data.hChildren,a=e.data.hProperties;if(typeof r=="string")if(n.type==="element")n.tagName=r;else{const i="children"in n?n.children:[n];n={type:"element",tagName:r,properties:{},children:i}}n.type==="element"&&a&&Object.assign(n.properties,Sn(a)),"children"in n&&n.children&&u!==null&&u!==void 0&&(n.children=u)}return n}function Ff(e,t){const n=t.data||{},r="value"in t&&!(Tr.call(n,"hProperties")||Tr.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)}function vf(e,t){const n=[];let r=-1;for(t&&n.push({type:"text",value:`
`});++r<e.length;)r&&n.push({type:"text",value:`
`}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:`
`}),n}function zu(e){let t=0,n=e.charCodeAt(t);for(;n===9||n===32;)t++,n=e.charCodeAt(t);return e.slice(t)}function Yu(e,t){const n=wf(e,t),r=n.one(e,void 0),u=Sf(n),a=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return u&&a.children.push({type:"text",value:`
`},u),a}function Uf(e,t){return e&&"run"in e?async function(n,r){const u=Yu(n,{file:r,...t});await e.run(u,r)}:function(n,r){return Yu(n,{file:r,...e||t})}}function qu(e){if(e)throw e}var Xn,ju;function Hf(){if(ju)return Xn;ju=1;var e=Object.prototype.hasOwnProperty,t=Object.prototype.toString,n=Object.defineProperty,r=Object.getOwnPropertyDescriptor,u=function(c){return typeof Array.isArray=="function"?Array.isArray(c):t.call(c)==="[object Array]"},a=function(c){if(!c||t.call(c)!=="[object Object]")return!1;var h=e.call(c,"constructor"),f=c.constructor&&c.constructor.prototype&&e.call(c.constructor.prototype,"isPrototypeOf");if(c.constructor&&!h&&!f)return!1;var m;for(m in c);return typeof m>"u"||e.call(c,m)},i=function(c,h){n&&h.name==="__proto__"?n(c,h.name,{enumerable:!0,configurable:!0,value:h.newValue,writable:!0}):c[h.name]=h.newValue},o=function(c,h){if(h==="__proto__")if(e.call(c,h)){if(r)return r(c,h).value}else return;return c[h]};return Xn=function l(){var c,h,f,m,d,_,y=arguments[0],M=1,k=arguments.length,F=!1;for(typeof y=="boolean"&&(F=y,y=arguments[1]||{},M=2),(y==null||typeof y!="object"&&typeof y!="function")&&(y={});M<k;++M)if(c=arguments[M],c!=null)for(h in c)f=o(y,h),m=o(c,h),y!==m&&(F&&m&&(a(m)||(d=u(m)))?(d?(d=!1,_=f&&u(f)?f:[]):_=f&&a(f)?f:{},i(y,{name:h,newValue:l(F,_,m)})):typeof m<"u"&&i(y,{name:h,newValue:m}));return y},Xn}var zf=Hf();const Kn=wi(zf);function br(e){if(typeof e!="object"||e===null)return!1;const t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function Yf(){const e=[],t={run:n,use:r};return t;function n(...u){let a=-1;const i=u.pop();if(typeof i!="function")throw new TypeError("Expected function as last argument, not "+i);o(null,...u);function o(l,...c){const h=e[++a];let f=-1;if(l){i(l);return}for(;++f<u.length;)(c[f]===null||c[f]===void 0)&&(c[f]=u[f]);u=c,h?qf(h,o)(...c):i(null,...c)}}function r(u){if(typeof u!="function")throw new TypeError("Expected `middelware` to be a function, not "+u);return e.push(u),t}}function qf(e,t){let n;return r;function r(...i){const o=e.length>i.length;let l;o&&i.push(u);try{l=e.apply(this,i)}catch(c){const h=c;if(o&&n)throw h;return u(h)}o||(l&&l.then&&typeof l.then=="function"?l.then(a,u):l instanceof Error?u(l):a(l))}function u(i,...o){n||(n=!0,t(i,...o))}function a(i){u(null,i)}}const je={basename:jf,dirname:Vf,extname:$f,join:Wf,sep:"/"};function jf(e,t){if(t!==void 0&&typeof t!="string")throw new TypeError('"ext" argument must be a string');sn(e);let n=0,r=-1,u=e.length,a;if(t===void 0||t.length===0||t.length>e.length){for(;u--;)if(e.codePointAt(u)===47){if(a){n=u+1;break}}else r<0&&(a=!0,r=u+1);return r<0?"":e.slice(n,r)}if(t===e)return"";let i=-1,o=t.length-1;for(;u--;)if(e.codePointAt(u)===47){if(a){n=u+1;break}}else i<0&&(a=!0,i=u+1),o>-1&&(e.codePointAt(u)===t.codePointAt(o--)?o<0&&(r=u):(o=-1,r=i));return n===r?r=i:r<0&&(r=e.length),e.slice(n,r)}function Vf(e){if(sn(e),e.length===0)return".";let t=-1,n=e.length,r;for(;--n;)if(e.codePointAt(n)===47){if(r){t=n;break}}else r||(r=!0);return t<0?e.codePointAt(0)===47?"/":".":t===1&&e.codePointAt(0)===47?"//":e.slice(0,t)}function $f(e){sn(e);let t=e.length,n=-1,r=0,u=-1,a=0,i;for(;t--;){const o=e.codePointAt(t);if(o===47){if(i){r=t+1;break}continue}n<0&&(i=!0,n=t+1),o===46?u<0?u=t:a!==1&&(a=1):u>-1&&(a=-1)}return u<0||n<0||a===0||a===1&&u===n-1&&u===r+1?"":e.slice(u,n)}function Wf(...e){let t=-1,n;for(;++t<e.length;)sn(e[t]),e[t]&&(n=n===void 0?e[t]:n+"/"+e[t]);return n===void 0?".":Qf(n)}function Qf(e){sn(e);const t=e.codePointAt(0)===47;let n=Xf(e,!t);return n.length===0&&!t&&(n="."),n.length>0&&e.codePointAt(e.length-1)===47&&(n+="/"),t?"/"+n:n}function Xf(e,t){let n="",r=0,u=-1,a=0,i=-1,o,l;for(;++i<=e.length;){if(i<e.length)o=e.codePointAt(i);else{if(o===47)break;o=47}if(o===47){if(!(u===i-1||a===1))if(u!==i-1&&a===2){if(n.length<2||r!==2||n.codePointAt(n.length-1)!==46||n.codePointAt(n.length-2)!==46){if(n.length>2){if(l=n.lastIndexOf("/"),l!==n.length-1){l<0?(n="",r=0):(n=n.slice(0,l),r=n.length-1-n.lastIndexOf("/")),u=i,a=0;continue}}else if(n.length>0){n="",r=0,u=i,a=0;continue}}t&&(n=n.length>0?n+"/..":"..",r=2)}else n.length>0?n+="/"+e.slice(u+1,i):n=e.slice(u+1,i),r=i-u-1;u=i,a=0}else o===46&&a>-1?a++:a=-1}return n}function sn(e){if(typeof e!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}const Kf={cwd:Gf};function Gf(){return"/"}function Ar(e){return!!(e!==null&&typeof e=="object"&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&e.auth===void 0)}function Jf(e){if(typeof e=="string")e=new URL(e);else if(!Ar(e)){const t=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if(e.protocol!=="file:"){const t=new TypeError("The URL must be of scheme file");throw t.code="ERR_INVALID_URL_SCHEME",t}return Zf(e)}function Zf(e){if(e.hostname!==""){const r=new TypeError('File URL host must be "localhost" or empty on darwin');throw r.code="ERR_INVALID_FILE_URL_HOST",r}const t=e.pathname;let n=-1;for(;++n<t.length;)if(t.codePointAt(n)===37&&t.codePointAt(n+1)===50){const r=t.codePointAt(n+2);if(r===70||r===102){const u=new TypeError("File URL path must not include encoded / characters");throw u.code="ERR_INVALID_FILE_URL_PATH",u}}return decodeURIComponent(t)}const Gn=["history","path","basename","stem","extname","dirname"];class ma{constructor(t){let n;t?Ar(t)?n={path:t}:typeof t=="string"||e0(t)?n={value:t}:n=t:n={},this.cwd="cwd"in n?"":Kf.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<Gn.length;){const a=Gn[r];a in n&&n[a]!==void 0&&n[a]!==null&&(this[a]=a==="history"?[...n[a]]:n[a])}let u;for(u in n)Gn.includes(u)||(this[u]=n[u])}get basename(){return typeof this.path=="string"?je.basename(this.path):void 0}set basename(t){Zn(t,"basename"),Jn(t,"basename"),this.path=je.join(this.dirname||"",t)}get dirname(){return typeof this.path=="string"?je.dirname(this.path):void 0}set dirname(t){Vu(this.basename,"dirname"),this.path=je.join(t||"",this.basename)}get extname(){return typeof this.path=="string"?je.extname(this.path):void 0}set extname(t){if(Jn(t,"extname"),Vu(this.dirname,"extname"),t){if(t.codePointAt(0)!==46)throw new Error("`extname` must start with `.`");if(t.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=je.join(this.dirname,this.stem+(t||""))}get path(){return this.history[this.history.length-1]}set path(t){Ar(t)&&(t=Jf(t)),Zn(t,"path"),this.path!==t&&this.history.push(t)}get stem(){return typeof this.path=="string"?je.basename(this.path,this.extname):void 0}set stem(t){Zn(t,"stem"),Jn(t,"stem"),this.path=je.join(this.dirname||"",t+(this.extname||""))}fail(t,n,r){const u=this.message(t,n,r);throw u.fatal=!0,u}info(t,n,r){const u=this.message(t,n,r);return u.fatal=void 0,u}message(t,n,r){const u=new be(t,n,r);return this.path&&(u.name=this.path+":"+u.name,u.file=this.path),u.fatal=!1,this.messages.push(u),u}toString(t){return this.value===void 0?"":typeof this.value=="string"?this.value:new TextDecoder(t||void 0).decode(this.value)}}function Jn(e,t){if(e&&e.includes(je.sep))throw new Error("`"+t+"` cannot be a path: did not expect `"+je.sep+"`")}function Zn(e,t){if(!e)throw new Error("`"+t+"` cannot be empty")}function Vu(e,t){if(!e)throw new Error("Setting `"+t+"` requires `path` to be set too")}function e0(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}const t0=function(e){const r=this.constructor.prototype,u=r[e],a=function(){return u.apply(a,arguments)};return Object.setPrototypeOf(a,r),a},n0={}.hasOwnProperty;class Vr extends t0{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=Yf()}copy(){const t=new Vr;let n=-1;for(;++n<this.attachers.length;){const r=this.attachers[n];t.use(...r)}return t.data(Kn(!0,{},this.namespace)),t}data(t,n){return typeof t=="string"?arguments.length===2?(nr("data",this.frozen),this.namespace[t]=n,this):n0.call(this.namespace,t)&&this.namespace[t]||void 0:t?(nr("data",this.frozen),this.namespace=t,this):this.namespace}freeze(){if(this.frozen)return this;const t=this;for(;++this.freezeIndex<this.attachers.length;){const[n,...r]=this.attachers[this.freezeIndex];if(r[0]===!1)continue;r[0]===!0&&(r[0]=void 0);const u=n.call(t,...r);typeof u=="function"&&this.transformers.use(u)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(t){this.freeze();const n=mn(t),r=this.parser||this.Parser;return er("parse",r),r(String(n),n)}process(t,n){const r=this;return this.freeze(),er("process",this.parser||this.Parser),tr("process",this.compiler||this.Compiler),n?u(void 0,n):new Promise(u);function u(a,i){const o=mn(t),l=r.parse(o);r.run(l,o,function(h,f,m){if(h||!f||!m)return c(h);const d=f,_=r.stringify(d,m);i0(_)?m.value=_:m.result=_,c(h,m)});function c(h,f){h||!f?i(h):a?a(f):n(void 0,f)}}}processSync(t){let n=!1,r;return this.freeze(),er("processSync",this.parser||this.Parser),tr("processSync",this.compiler||this.Compiler),this.process(t,u),Wu("processSync","process",n),r;function u(a,i){n=!0,qu(a),r=i}}run(t,n,r){$u(t),this.freeze();const u=this.transformers;return!r&&typeof n=="function"&&(r=n,n=void 0),r?a(void 0,r):new Promise(a);function a(i,o){const l=mn(n);u.run(t,l,c);function c(h,f,m){const d=f||t;h?o(h):i?i(d):r(void 0,d,m)}}}runSync(t,n){let r=!1,u;return this.run(t,n,a),Wu("runSync","run",r),u;function a(i,o){qu(i),u=o,r=!0}}stringify(t,n){this.freeze();const r=mn(n),u=this.compiler||this.Compiler;return tr("stringify",u),$u(t),u(t,r)}use(t,...n){const r=this.attachers,u=this.namespace;if(nr("use",this.frozen),t!=null)if(typeof t=="function")l(t,n);else if(typeof t=="object")Array.isArray(t)?o(t):i(t);else throw new TypeError("Expected usable value, not `"+t+"`");return this;function a(c){if(typeof c=="function")l(c,[]);else if(typeof c=="object")if(Array.isArray(c)){const[h,...f]=c;l(h,f)}else i(c);else throw new TypeError("Expected usable value, not `"+c+"`")}function i(c){if(!("plugins"in c)&&!("settings"in c))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");o(c.plugins),c.settings&&(u.settings=Kn(!0,u.settings,c.settings))}function o(c){let h=-1;if(c!=null)if(Array.isArray(c))for(;++h<c.length;){const f=c[h];a(f)}else throw new TypeError("Expected a list of plugins, not `"+c+"`")}function l(c,h){let f=-1,m=-1;for(;++f<r.length;)if(r[f][0]===c){m=f;break}if(m===-1)r.push([c,...h]);else if(h.length>0){let[d,..._]=h;const y=r[m][1];br(y)&&br(d)&&(d=Kn(!0,y,d)),r[m]=[c,d,..._]}}}}const r0=new Vr().freeze();function er(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `parser`")}function tr(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `compiler`")}function nr(e,t){if(t)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function $u(e){if(!br(e)||typeof e.type!="string")throw new TypeError("Expected node, got `"+e+"`")}function Wu(e,t,n){if(!n)throw new Error("`"+e+"` finished async. Use `"+t+"` instead")}function mn(e){return u0(e)?e:new ma(e)}function u0(e){return!!(e&&typeof e=="object"&&"message"in e&&"messages"in e)}function i0(e){return typeof e=="string"||a0(e)}function a0(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}const s0="https://github.com/remarkjs/react-markdown/blob/main/changelog.md",Qu=[],Xu={allowDangerousHtml:!0},o0=/^(https?|ircs?|mailto|xmpp)$/i,l0=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function Ku(e){const t=c0(e),n=f0(e);return h0(t.runSync(t.parse(n),n),e)}function c0(e){const t=e.rehypePlugins||Qu,n=e.remarkPlugins||Qu,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...Xu}:Xu;return r0().use(jc).use(n).use(Uf,r).use(t)}function f0(e){const t=e.children||"",n=new ma;return typeof t=="string"&&(n.value=t),n}function h0(e,t){const n=t.allowedElements,r=t.allowElement,u=t.components,a=t.disallowedElements,i=t.skipHtml,o=t.unwrapDisallowed,l=t.urlTransform||d0;for(const h of l0)Object.hasOwn(t,h.from)&&(""+h.from+(h.to?"use `"+h.to+"` instead":"remove it")+s0+h.id,void 0);return jr(e,c),xo(e,{Fragment:q.Fragment,components:u,ignoreInvalidStyle:!0,jsx:q.jsx,jsxs:q.jsxs,passKeys:!0,passNode:!0});function c(h,f,m){if(h.type==="raw"&&m&&typeof f=="number")return i?m.children.splice(f,1):m.children[f]={type:"text",value:h.value},f;if(h.type==="element"){let d;for(d in $n)if(Object.hasOwn($n,d)&&Object.hasOwn(h.properties,d)){const _=h.properties[d],y=$n[d];(y===null||y.includes(h.tagName))&&(h.properties[d]=l(String(_||""),d,h))}}if(h.type==="element"){let d=n?!n.includes(h.tagName):a?a.includes(h.tagName):!1;if(!d&&r&&typeof f=="number"&&(d=!r(h,f,m)),d&&m&&typeof f=="number")return o&&h.children?m.children.splice(f,1,...h.children):m.children.splice(f,1),f}}}function d0(e){const t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),u=e.indexOf("/");return t===-1||u!==-1&&t>u||n!==-1&&t>n||r!==-1&&t>r||o0.test(e.slice(0,t))?e:""}function Gu(e,t){const n=String(e);if(typeof t!="string")throw new TypeError("Expected character");let r=0,u=n.indexOf(t);for(;u!==-1;)r++,u=n.indexOf(t,u+t.length);return r}function p0(e){if(typeof e!="string")throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}function m0(e,t,n){const u=Pn((n||{}).ignore||[]),a=E0(t);let i=-1;for(;++i<a.length;)pa(e,"text",o);function o(c,h){let f=-1,m;for(;++f<h.length;){const d=h[f],_=m?m.children:void 0;if(u(d,_?_.indexOf(d):void 0,m))return;m=d}if(m)return l(c,h)}function l(c,h){const f=h[h.length-1],m=a[i][0],d=a[i][1];let _=0;const M=f.children.indexOf(c);let k=!1,F=[];m.lastIndex=0;let B=m.exec(c.value);for(;B;){const j=B.index,V={index:B.index,input:B.input,stack:[...h,c]};let x=d(...B,V);if(typeof x=="string"&&(x=x.length>0?{type:"text",value:x}:void 0),x===!1?m.lastIndex=j+1:(_!==j&&F.push({type:"text",value:c.value.slice(_,j)}),Array.isArray(x)?F.push(...x):x&&F.push(x),_=j+B[0].length,k=!0),!m.global)break;B=m.exec(c.value)}return k?(_<c.value.length&&F.push({type:"text",value:c.value.slice(_)}),f.children.splice(M,1,...F)):F=[c],M+F.length}}function E0(e){const t=[];if(!Array.isArray(e))throw new TypeError("Expected find and replace tuple or list of tuples");const n=!e[0]||Array.isArray(e[0])?e:[e];let r=-1;for(;++r<n.length;){const u=n[r];t.push([g0(u[0]),T0(u[1])])}return t}function g0(e){return typeof e=="string"?new RegExp(p0(e),"g"):e}function T0(e){return typeof e=="function"?e:function(){return e}}const rr="phrasing",ur=["autolink","link","image","label"];function b0(){return{transforms:[I0],enter:{literalAutolink:_0,literalAutolinkEmail:ir,literalAutolinkHttp:ir,literalAutolinkWww:ir},exit:{literalAutolink:y0,literalAutolinkEmail:S0,literalAutolinkHttp:C0,literalAutolinkWww:k0}}}function A0(){return{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:rr,notInConstruct:ur},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:rr,notInConstruct:ur},{character:":",before:"[ps]",after:"\\/",inConstruct:rr,notInConstruct:ur}]}}function _0(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function ir(e){this.config.enter.autolinkProtocol.call(this,e)}function C0(e){this.config.exit.autolinkProtocol.call(this,e)}function k0(e){this.config.exit.data.call(this,e);const t=this.stack[this.stack.length-1];t.type,t.url="http://"+this.sliceSerialize(e)}function S0(e){this.config.exit.autolinkEmail.call(this,e)}function y0(e){this.exit(e)}function I0(e){m0(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,x0],[new RegExp("(?<=^|\\s|\\p{P}|\\p{S})([-.\\w+]+)@([-\\w]+(?:\\.[-\\w]+)+)","gu"),N0]],{ignore:["link","linkReference"]})}function x0(e,t,n,r,u){let a="";if(!Ea(u)||(/^w/i.test(t)&&(n=t+n,t="",a="http://"),!O0(n)))return!1;const i=L0(n+r);if(!i[0])return!1;const o={type:"link",title:null,url:a+t+i[0],children:[{type:"text",value:t+i[0]}]};return i[1]?[o,{type:"text",value:i[1]}]:o}function N0(e,t,n,r){return!Ea(r,!0)||/[-\d_]$/.test(n)?!1:{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function O0(e){const t=e.split(".");return!(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))}function L0(e){const t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],r=n.indexOf(")");const u=Gu(e,"(");let a=Gu(e,")");for(;r!==-1&&u>a;)e+=n.slice(0,r+1),n=n.slice(r+1),r=n.indexOf(")"),a++;return[e,n]}function Ea(e,t){const n=e.input.charCodeAt(e.index-1);return(e.index===0||ft(n)||Ln(n))&&(!t||n!==47)}ga.peek=U0;function D0(){this.buffer()}function R0(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function P0(){this.buffer()}function w0(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function M0(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=ze(this.sliceSerialize(e)).toLowerCase(),n.label=t}function B0(e){this.exit(e)}function F0(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=ze(this.sliceSerialize(e)).toLowerCase(),n.label=t}function v0(e){this.exit(e)}function U0(){return"["}function ga(e,t,n,r){const u=n.createTracker(r);let a=u.move("[^");const i=n.enter("footnoteReference"),o=n.enter("reference");return a+=u.move(n.safe(n.associationId(e),{after:"]",before:a})),o(),i(),a+=u.move("]"),a}function H0(){return{enter:{gfmFootnoteCallString:D0,gfmFootnoteCall:R0,gfmFootnoteDefinitionLabelString:P0,gfmFootnoteDefinition:w0},exit:{gfmFootnoteCallString:M0,gfmFootnoteCall:B0,gfmFootnoteDefinitionLabelString:F0,gfmFootnoteDefinition:v0}}}function z0(e){let t=!1;return e&&e.firstLineBlank&&(t=!0),{handlers:{footnoteDefinition:n,footnoteReference:ga},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]};function n(r,u,a,i){const o=a.createTracker(i);let l=o.move("[^");const c=a.enter("footnoteDefinition"),h=a.enter("label");return l+=o.move(a.safe(a.associationId(r),{before:l,after:"]"})),h(),l+=o.move("]:"),r.children&&r.children.length>0&&(o.shift(4),l+=o.move((t?`
`:" ")+a.indentLines(a.containerFlow(r,o.current()),t?Ta:Y0))),c(),l}}function Y0(e,t,n){return t===0?e:Ta(e,t,n)}function Ta(e,t,n){return(n?"":"    ")+e}const q0=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];ba.peek=Q0;function j0(){return{canContainEols:["delete"],enter:{strikethrough:$0},exit:{strikethrough:W0}}}function V0(){return{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:q0}],handlers:{delete:ba}}}function $0(e){this.enter({type:"delete",children:[]},e)}function W0(e){this.exit(e)}function ba(e,t,n,r){const u=n.createTracker(r),a=n.enter("strikethrough");let i=u.move("~~");return i+=n.containerPhrasing(e,{...u.current(),before:i,after:"~"}),i+=u.move("~~"),a(),i}function Q0(){return"~"}function X0(e){return e.length}function K0(e,t){const n=t||{},r=(n.align||[]).concat(),u=n.stringLength||X0,a=[],i=[],o=[],l=[];let c=0,h=-1;for(;++h<e.length;){const y=[],M=[];let k=-1;for(e[h].length>c&&(c=e[h].length);++k<e[h].length;){const F=G0(e[h][k]);if(n.alignDelimiters!==!1){const B=u(F);M[k]=B,(l[k]===void 0||B>l[k])&&(l[k]=B)}y.push(F)}i[h]=y,o[h]=M}let f=-1;if(typeof r=="object"&&"length"in r)for(;++f<c;)a[f]=Ju(r[f]);else{const y=Ju(r);for(;++f<c;)a[f]=y}f=-1;const m=[],d=[];for(;++f<c;){const y=a[f];let M="",k="";y===99?(M=":",k=":"):y===108?M=":":y===114&&(k=":");let F=n.alignDelimiters===!1?1:Math.max(1,l[f]-M.length-k.length);const B=M+"-".repeat(F)+k;n.alignDelimiters!==!1&&(F=M.length+F+k.length,F>l[f]&&(l[f]=F),d[f]=F),m[f]=B}i.splice(1,0,m),o.splice(1,0,d),h=-1;const _=[];for(;++h<i.length;){const y=i[h],M=o[h];f=-1;const k=[];for(;++f<c;){const F=y[f]||"";let B="",j="";if(n.alignDelimiters!==!1){const V=l[f]-(M[f]||0),x=a[f];x===114?B=" ".repeat(V):x===99?V%2?(B=" ".repeat(V/2+.5),j=" ".repeat(V/2-.5)):(B=" ".repeat(V/2),j=B):j=" ".repeat(V)}n.delimiterStart!==!1&&!f&&k.push("|"),n.padding!==!1&&!(n.alignDelimiters===!1&&F==="")&&(n.delimiterStart!==!1||f)&&k.push(" "),n.alignDelimiters!==!1&&k.push(B),k.push(F),n.alignDelimiters!==!1&&k.push(j),n.padding!==!1&&k.push(" "),(n.delimiterEnd!==!1||f!==c-1)&&k.push("|")}_.push(n.delimiterEnd===!1?k.join("").replace(/ +$/,""):k.join(""))}return _.join(`
`)}function G0(e){return e==null?"":String(e)}function Ju(e){const t=typeof e=="string"?e.codePointAt(0):0;return t===67||t===99?99:t===76||t===108?108:t===82||t===114?114:0}function J0(e,t,n,r){const u=n.enter("blockquote"),a=n.createTracker(r);a.move("> "),a.shift(2);const i=n.indentLines(n.containerFlow(e,a.current()),Z0);return u(),i}function Z0(e,t,n){return">"+(n?"":" ")+e}function eh(e,t){return Zu(e,t.inConstruct,!0)&&!Zu(e,t.notInConstruct,!1)}function Zu(e,t,n){if(typeof t=="string"&&(t=[t]),!t||t.length===0)return n;let r=-1;for(;++r<t.length;)if(e.includes(t[r]))return!0;return!1}function ei(e,t,n,r){let u=-1;for(;++u<n.unsafe.length;)if(n.unsafe[u].character===`
`&&eh(n.stack,n.unsafe[u]))return/[ \t]/.test(r.before)?"":" ";return`\\
`}function th(e,t){const n=String(e);let r=n.indexOf(t),u=r,a=0,i=0;if(typeof t!="string")throw new TypeError("Expected substring");for(;r!==-1;)r===u?++a>i&&(i=a):a=1,u=r+t.length,r=n.indexOf(t,u);return i}function nh(e,t){return!!(t.options.fences===!1&&e.value&&!e.lang&&/[^ \r\n]/.test(e.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(e.value))}function rh(e){const t=e.options.fence||"`";if(t!=="`"&&t!=="~")throw new Error("Cannot serialize code with `"+t+"` for `options.fence`, expected `` ` `` or `~`");return t}function uh(e,t,n,r){const u=rh(n),a=e.value||"",i=u==="`"?"GraveAccent":"Tilde";if(nh(e,n)){const f=n.enter("codeIndented"),m=n.indentLines(a,ih);return f(),m}const o=n.createTracker(r),l=u.repeat(Math.max(th(a,u)+1,3)),c=n.enter("codeFenced");let h=o.move(l);if(e.lang){const f=n.enter(`codeFencedLang${i}`);h+=o.move(n.safe(e.lang,{before:h,after:" ",encode:["`"],...o.current()})),f()}if(e.lang&&e.meta){const f=n.enter(`codeFencedMeta${i}`);h+=o.move(" "),h+=o.move(n.safe(e.meta,{before:h,after:`
`,encode:["`"],...o.current()})),f()}return h+=o.move(`
`),a&&(h+=o.move(a+`
`)),h+=o.move(l),c(),h}function ih(e,t,n){return(n?"":"    ")+e}function $r(e){const t=e.options.quote||'"';if(t!=='"'&&t!=="'")throw new Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}function ah(e,t,n,r){const u=$r(n),a=u==='"'?"Quote":"Apostrophe",i=n.enter("definition");let o=n.enter("label");const l=n.createTracker(r);let c=l.move("[");return c+=l.move(n.safe(n.associationId(e),{before:c,after:"]",...l.current()})),c+=l.move("]: "),o(),!e.url||/[\0- \u007F]/.test(e.url)?(o=n.enter("destinationLiteral"),c+=l.move("<"),c+=l.move(n.safe(e.url,{before:c,after:">",...l.current()})),c+=l.move(">")):(o=n.enter("destinationRaw"),c+=l.move(n.safe(e.url,{before:c,after:e.title?" ":`
`,...l.current()}))),o(),e.title&&(o=n.enter(`title${a}`),c+=l.move(" "+u),c+=l.move(n.safe(e.title,{before:c,after:u,...l.current()})),c+=l.move(u),o()),i(),c}function sh(e){const t=e.options.emphasis||"*";if(t!=="*"&&t!=="_")throw new Error("Cannot serialize emphasis with `"+t+"` for `options.emphasis`, expected `*`, or `_`");return t}function tn(e){return"&#x"+e.toString(16).toUpperCase()+";"}function yn(e,t,n){const r=It(e),u=It(t);return r===void 0?u===void 0?n==="_"?{inside:!0,outside:!0}:{inside:!1,outside:!1}:u===1?{inside:!0,outside:!0}:{inside:!1,outside:!0}:r===1?u===void 0?{inside:!1,outside:!1}:u===1?{inside:!0,outside:!0}:{inside:!1,outside:!1}:u===void 0?{inside:!1,outside:!1}:u===1?{inside:!0,outside:!1}:{inside:!1,outside:!1}}Aa.peek=oh;function Aa(e,t,n,r){const u=sh(n),a=n.enter("emphasis"),i=n.createTracker(r),o=i.move(u);let l=i.move(n.containerPhrasing(e,{after:u,before:o,...i.current()}));const c=l.charCodeAt(0),h=yn(r.before.charCodeAt(r.before.length-1),c,u);h.inside&&(l=tn(c)+l.slice(1));const f=l.charCodeAt(l.length-1),m=yn(r.after.charCodeAt(0),f,u);m.inside&&(l=l.slice(0,-1)+tn(f));const d=i.move(u);return a(),n.attentionEncodeSurroundingInfo={after:m.outside,before:h.outside},o+l+d}function oh(e,t,n){return n.options.emphasis||"*"}function lh(e,t){let n=!1;return jr(e,function(r){if("value"in r&&/\r?\n|\r/.test(r.value)||r.type==="break")return n=!0,gr}),!!((!e.depth||e.depth<3)&&Fr(e)&&(t.options.setext||n))}function ch(e,t,n,r){const u=Math.max(Math.min(6,e.depth||1),1),a=n.createTracker(r);if(lh(e,n)){const h=n.enter("headingSetext"),f=n.enter("phrasing"),m=n.containerPhrasing(e,{...a.current(),before:`
`,after:`
`});return f(),h(),m+`
`+(u===1?"=":"-").repeat(m.length-(Math.max(m.lastIndexOf("\r"),m.lastIndexOf(`
`))+1))}const i="#".repeat(u),o=n.enter("headingAtx"),l=n.enter("phrasing");a.move(i+" ");let c=n.containerPhrasing(e,{before:"# ",after:`
`,...a.current()});return/^[\t ]/.test(c)&&(c=tn(c.charCodeAt(0))+c.slice(1)),c=c?i+" "+c:i,n.options.closeAtx&&(c+=" "+i),l(),o(),c}_a.peek=fh;function _a(e){return e.value||""}function fh(){return"<"}Ca.peek=hh;function Ca(e,t,n,r){const u=$r(n),a=u==='"'?"Quote":"Apostrophe",i=n.enter("image");let o=n.enter("label");const l=n.createTracker(r);let c=l.move("![");return c+=l.move(n.safe(e.alt,{before:c,after:"]",...l.current()})),c+=l.move("]("),o(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(o=n.enter("destinationLiteral"),c+=l.move("<"),c+=l.move(n.safe(e.url,{before:c,after:">",...l.current()})),c+=l.move(">")):(o=n.enter("destinationRaw"),c+=l.move(n.safe(e.url,{before:c,after:e.title?" ":")",...l.current()}))),o(),e.title&&(o=n.enter(`title${a}`),c+=l.move(" "+u),c+=l.move(n.safe(e.title,{before:c,after:u,...l.current()})),c+=l.move(u),o()),c+=l.move(")"),i(),c}function hh(){return"!"}ka.peek=dh;function ka(e,t,n,r){const u=e.referenceType,a=n.enter("imageReference");let i=n.enter("label");const o=n.createTracker(r);let l=o.move("![");const c=n.safe(e.alt,{before:l,after:"]",...o.current()});l+=o.move(c+"]["),i();const h=n.stack;n.stack=[],i=n.enter("reference");const f=n.safe(n.associationId(e),{before:l,after:"]",...o.current()});return i(),n.stack=h,a(),u==="full"||!c||c!==f?l+=o.move(f+"]"):u==="shortcut"?l=l.slice(0,-1):l+=o.move("]"),l}function dh(){return"!"}Sa.peek=ph;function Sa(e,t,n){let r=e.value||"",u="`",a=-1;for(;new RegExp("(^|[^`])"+u+"([^`]|$)").test(r);)u+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++a<n.unsafe.length;){const i=n.unsafe[a],o=n.compilePattern(i);let l;if(i.atBreak)for(;l=o.exec(r);){let c=l.index;r.charCodeAt(c)===10&&r.charCodeAt(c-1)===13&&c--,r=r.slice(0,c)+" "+r.slice(l.index+1)}}return u+r+u}function ph(){return"`"}function ya(e,t){const n=Fr(e);return!!(!t.options.resourceLink&&e.url&&!e.title&&e.children&&e.children.length===1&&e.children[0].type==="text"&&(n===e.url||"mailto:"+n===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}Ia.peek=mh;function Ia(e,t,n,r){const u=$r(n),a=u==='"'?"Quote":"Apostrophe",i=n.createTracker(r);let o,l;if(ya(e,n)){const h=n.stack;n.stack=[],o=n.enter("autolink");let f=i.move("<");return f+=i.move(n.containerPhrasing(e,{before:f,after:">",...i.current()})),f+=i.move(">"),o(),n.stack=h,f}o=n.enter("link"),l=n.enter("label");let c=i.move("[");return c+=i.move(n.containerPhrasing(e,{before:c,after:"](",...i.current()})),c+=i.move("]("),l(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(l=n.enter("destinationLiteral"),c+=i.move("<"),c+=i.move(n.safe(e.url,{before:c,after:">",...i.current()})),c+=i.move(">")):(l=n.enter("destinationRaw"),c+=i.move(n.safe(e.url,{before:c,after:e.title?" ":")",...i.current()}))),l(),e.title&&(l=n.enter(`title${a}`),c+=i.move(" "+u),c+=i.move(n.safe(e.title,{before:c,after:u,...i.current()})),c+=i.move(u),l()),c+=i.move(")"),o(),c}function mh(e,t,n){return ya(e,n)?"<":"["}xa.peek=Eh;function xa(e,t,n,r){const u=e.referenceType,a=n.enter("linkReference");let i=n.enter("label");const o=n.createTracker(r);let l=o.move("[");const c=n.containerPhrasing(e,{before:l,after:"]",...o.current()});l+=o.move(c+"]["),i();const h=n.stack;n.stack=[],i=n.enter("reference");const f=n.safe(n.associationId(e),{before:l,after:"]",...o.current()});return i(),n.stack=h,a(),u==="full"||!c||c!==f?l+=o.move(f+"]"):u==="shortcut"?l=l.slice(0,-1):l+=o.move("]"),l}function Eh(){return"["}function Wr(e){const t=e.options.bullet||"*";if(t!=="*"&&t!=="+"&&t!=="-")throw new Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}function gh(e){const t=Wr(e),n=e.options.bulletOther;if(!n)return t==="*"?"-":"*";if(n!=="*"&&n!=="+"&&n!=="-")throw new Error("Cannot serialize items with `"+n+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(n===t)throw new Error("Expected `bullet` (`"+t+"`) and `bulletOther` (`"+n+"`) to be different");return n}function Th(e){const t=e.options.bulletOrdered||".";if(t!=="."&&t!==")")throw new Error("Cannot serialize items with `"+t+"` for `options.bulletOrdered`, expected `.` or `)`");return t}function Na(e){const t=e.options.rule||"*";if(t!=="*"&&t!=="-"&&t!=="_")throw new Error("Cannot serialize rules with `"+t+"` for `options.rule`, expected `*`, `-`, or `_`");return t}function bh(e,t,n,r){const u=n.enter("list"),a=n.bulletCurrent;let i=e.ordered?Th(n):Wr(n);const o=e.ordered?i==="."?")":".":gh(n);let l=t&&n.bulletLastUsed?i===n.bulletLastUsed:!1;if(!e.ordered){const h=e.children?e.children[0]:void 0;if((i==="*"||i==="-")&&h&&(!h.children||!h.children[0])&&n.stack[n.stack.length-1]==="list"&&n.stack[n.stack.length-2]==="listItem"&&n.stack[n.stack.length-3]==="list"&&n.stack[n.stack.length-4]==="listItem"&&n.indexStack[n.indexStack.length-1]===0&&n.indexStack[n.indexStack.length-2]===0&&n.indexStack[n.indexStack.length-3]===0&&(l=!0),Na(n)===i&&h){let f=-1;for(;++f<e.children.length;){const m=e.children[f];if(m&&m.type==="listItem"&&m.children&&m.children[0]&&m.children[0].type==="thematicBreak"){l=!0;break}}}}l&&(i=o),n.bulletCurrent=i;const c=n.containerFlow(e,r);return n.bulletLastUsed=i,n.bulletCurrent=a,u(),c}function Ah(e){const t=e.options.listItemIndent||"one";if(t!=="tab"&&t!=="one"&&t!=="mixed")throw new Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}function _h(e,t,n,r){const u=Ah(n);let a=n.bulletCurrent||Wr(n);t&&t.type==="list"&&t.ordered&&(a=(typeof t.start=="number"&&t.start>-1?t.start:1)+(n.options.incrementListMarker===!1?0:t.children.indexOf(e))+a);let i=a.length+1;(u==="tab"||u==="mixed"&&(t&&t.type==="list"&&t.spread||e.spread))&&(i=Math.ceil(i/4)*4);const o=n.createTracker(r);o.move(a+" ".repeat(i-a.length)),o.shift(i);const l=n.enter("listItem"),c=n.indentLines(n.containerFlow(e,o.current()),h);return l(),c;function h(f,m,d){return m?(d?"":" ".repeat(i))+f:(d?a:a+" ".repeat(i-a.length))+f}}function Ch(e,t,n,r){const u=n.enter("paragraph"),a=n.enter("phrasing"),i=n.containerPhrasing(e,r);return a(),u(),i}const kh=Pn(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function Sh(e,t,n,r){return(e.children.some(function(i){return kh(i)})?n.containerPhrasing:n.containerFlow).call(n,e,r)}function yh(e){const t=e.options.strong||"*";if(t!=="*"&&t!=="_")throw new Error("Cannot serialize strong with `"+t+"` for `options.strong`, expected `*`, or `_`");return t}Oa.peek=Ih;function Oa(e,t,n,r){const u=yh(n),a=n.enter("strong"),i=n.createTracker(r),o=i.move(u+u);let l=i.move(n.containerPhrasing(e,{after:u,before:o,...i.current()}));const c=l.charCodeAt(0),h=yn(r.before.charCodeAt(r.before.length-1),c,u);h.inside&&(l=tn(c)+l.slice(1));const f=l.charCodeAt(l.length-1),m=yn(r.after.charCodeAt(0),f,u);m.inside&&(l=l.slice(0,-1)+tn(f));const d=i.move(u+u);return a(),n.attentionEncodeSurroundingInfo={after:m.outside,before:h.outside},o+l+d}function Ih(e,t,n){return n.options.strong||"*"}function xh(e,t,n,r){return n.safe(e.value,r)}function Nh(e){const t=e.options.ruleRepetition||3;if(t<3)throw new Error("Cannot serialize rules with repetition `"+t+"` for `options.ruleRepetition`, expected `3` or more");return t}function Oh(e,t,n){const r=(Na(n)+(n.options.ruleSpaces?" ":"")).repeat(Nh(n));return n.options.ruleSpaces?r.slice(0,-1):r}const La={blockquote:J0,break:ei,code:uh,definition:ah,emphasis:Aa,hardBreak:ei,heading:ch,html:_a,image:Ca,imageReference:ka,inlineCode:Sa,link:Ia,linkReference:xa,list:bh,listItem:_h,paragraph:Ch,root:Sh,strong:Oa,text:xh,thematicBreak:Oh};function Lh(){return{enter:{table:Dh,tableData:ti,tableHeader:ti,tableRow:Ph},exit:{codeText:wh,table:Rh,tableData:ar,tableHeader:ar,tableRow:ar}}}function Dh(e){const t=e._align;this.enter({type:"table",align:t.map(function(n){return n==="none"?null:n}),children:[]},e),this.data.inTable=!0}function Rh(e){this.exit(e),this.data.inTable=void 0}function Ph(e){this.enter({type:"tableRow",children:[]},e)}function ar(e){this.exit(e)}function ti(e){this.enter({type:"tableCell",children:[]},e)}function wh(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,Mh));const n=this.stack[this.stack.length-1];n.type,n.value=t,this.exit(e)}function Mh(e,t){return t==="|"?t:e}function Bh(e){const t=e||{},n=t.tableCellPadding,r=t.tablePipeAlign,u=t.stringLength,a=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:`
`,inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:m,table:i,tableCell:l,tableRow:o}};function i(d,_,y,M){return c(h(d,y,M),d.align)}function o(d,_,y,M){const k=f(d,y,M),F=c([k]);return F.slice(0,F.indexOf(`
`))}function l(d,_,y,M){const k=y.enter("tableCell"),F=y.enter("phrasing"),B=y.containerPhrasing(d,{...M,before:a,after:a});return F(),k(),B}function c(d,_){return K0(d,{align:_,alignDelimiters:r,padding:n,stringLength:u})}function h(d,_,y){const M=d.children;let k=-1;const F=[],B=_.enter("table");for(;++k<M.length;)F[k]=f(M[k],_,y);return B(),F}function f(d,_,y){const M=d.children;let k=-1;const F=[],B=_.enter("tableRow");for(;++k<M.length;)F[k]=l(M[k],d,_,y);return B(),F}function m(d,_,y){let M=La.inlineCode(d,_,y);return y.stack.includes("tableCell")&&(M=M.replace(/\|/g,"\\$&")),M}}function Fh(){return{exit:{taskListCheckValueChecked:ni,taskListCheckValueUnchecked:ni,paragraph:Uh}}}function vh(){return{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:Hh}}}function ni(e){const t=this.stack[this.stack.length-2];t.type,t.checked=e.type==="taskListCheckValueChecked"}function Uh(e){const t=this.stack[this.stack.length-2];if(t&&t.type==="listItem"&&typeof t.checked=="boolean"){const n=this.stack[this.stack.length-1];n.type;const r=n.children[0];if(r&&r.type==="text"){const u=t.children;let a=-1,i;for(;++a<u.length;){const o=u[a];if(o.type==="paragraph"){i=o;break}}i===n&&(r.value=r.value.slice(1),r.value.length===0?n.children.shift():n.position&&r.position&&typeof r.position.start.offset=="number"&&(r.position.start.column++,r.position.start.offset++,n.position.start=Object.assign({},r.position.start)))}}this.exit(e)}function Hh(e,t,n,r){const u=e.children[0],a=typeof e.checked=="boolean"&&u&&u.type==="paragraph",i="["+(e.checked?"x":" ")+"] ",o=n.createTracker(r);a&&o.move(i);let l=La.listItem(e,t,n,{...r,...o.current()});return a&&(l=l.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,c)),l;function c(h){return h+i}}function zh(){return[b0(),H0(),j0(),Lh(),Fh()]}function Yh(e){return{extensions:[A0(),z0(e),V0(),Bh(e),vh()]}}const qh={tokenize:Xh,partial:!0},Da={tokenize:Kh,partial:!0},Ra={tokenize:Gh,partial:!0},Pa={tokenize:Jh,partial:!0},jh={tokenize:Zh,partial:!0},wa={name:"wwwAutolink",tokenize:Wh,previous:Ba},Ma={name:"protocolAutolink",tokenize:Qh,previous:Fa},Ge={name:"emailAutolink",tokenize:$h,previous:va},$e={};function Vh(){return{text:$e}}let ot=48;for(;ot<123;)$e[ot]=Ge,ot++,ot===58?ot=65:ot===91&&(ot=97);$e[43]=Ge;$e[45]=Ge;$e[46]=Ge;$e[95]=Ge;$e[72]=[Ge,Ma];$e[104]=[Ge,Ma];$e[87]=[Ge,wa];$e[119]=[Ge,wa];function $h(e,t,n){const r=this;let u,a;return i;function i(f){return!_r(f)||!va.call(r,r.previous)||Qr(r.events)?n(f):(e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),o(f))}function o(f){return _r(f)?(e.consume(f),o):f===64?(e.consume(f),l):n(f)}function l(f){return f===46?e.check(jh,h,c)(f):f===45||f===95||Te(f)?(a=!0,e.consume(f),l):h(f)}function c(f){return e.consume(f),u=!0,l}function h(f){return a&&u&&_e(r.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(f)):n(f)}}function Wh(e,t,n){const r=this;return u;function u(i){return i!==87&&i!==119||!Ba.call(r,r.previous)||Qr(r.events)?n(i):(e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(qh,e.attempt(Da,e.attempt(Ra,a),n),n)(i))}function a(i){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(i)}}function Qh(e,t,n){const r=this;let u="",a=!1;return i;function i(f){return(f===72||f===104)&&Fa.call(r,r.previous)&&!Qr(r.events)?(e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),u+=String.fromCodePoint(f),e.consume(f),o):n(f)}function o(f){if(_e(f)&&u.length<5)return u+=String.fromCodePoint(f),e.consume(f),o;if(f===58){const m=u.toLowerCase();if(m==="http"||m==="https")return e.consume(f),l}return n(f)}function l(f){return f===47?(e.consume(f),a?c:(a=!0,l)):n(f)}function c(f){return f===null||Cn(f)||ie(f)||ft(f)||Ln(f)?n(f):e.attempt(Da,e.attempt(Ra,h),n)(f)}function h(f){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(f)}}function Xh(e,t,n){let r=0;return u;function u(i){return(i===87||i===119)&&r<3?(r++,e.consume(i),u):i===46&&r===3?(e.consume(i),a):n(i)}function a(i){return i===null?n(i):t(i)}}function Kh(e,t,n){let r,u,a;return i;function i(c){return c===46||c===95?e.check(Pa,l,o)(c):c===null||ie(c)||ft(c)||c!==45&&Ln(c)?l(c):(a=!0,e.consume(c),i)}function o(c){return c===95?r=!0:(u=r,r=void 0),e.consume(c),i}function l(c){return u||r||!a?n(c):t(c)}}function Gh(e,t){let n=0,r=0;return u;function u(i){return i===40?(n++,e.consume(i),u):i===41&&r<n?a(i):i===33||i===34||i===38||i===39||i===41||i===42||i===44||i===46||i===58||i===59||i===60||i===63||i===93||i===95||i===126?e.check(Pa,t,a)(i):i===null||ie(i)||ft(i)?t(i):(e.consume(i),u)}function a(i){return i===41&&r++,e.consume(i),u}}function Jh(e,t,n){return r;function r(o){return o===33||o===34||o===39||o===41||o===42||o===44||o===46||o===58||o===59||o===63||o===95||o===126?(e.consume(o),r):o===38?(e.consume(o),a):o===93?(e.consume(o),u):o===60||o===null||ie(o)||ft(o)?t(o):n(o)}function u(o){return o===null||o===40||o===91||ie(o)||ft(o)?t(o):r(o)}function a(o){return _e(o)?i(o):n(o)}function i(o){return o===59?(e.consume(o),r):_e(o)?(e.consume(o),i):n(o)}}function Zh(e,t,n){return r;function r(a){return e.consume(a),u}function u(a){return Te(a)?n(a):t(a)}}function Ba(e){return e===null||e===40||e===42||e===95||e===91||e===93||e===126||ie(e)}function Fa(e){return!_e(e)}function va(e){return!(e===47||_r(e))}function _r(e){return e===43||e===45||e===46||e===95||Te(e)}function Qr(e){let t=e.length,n=!1;for(;t--;){const r=e[t][1];if((r.type==="labelLink"||r.type==="labelImage")&&!r._balanced){n=!0;break}if(r._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}const ed={tokenize:od,partial:!0};function td(){return{document:{91:{name:"gfmFootnoteDefinition",tokenize:id,continuation:{tokenize:ad},exit:sd}},text:{91:{name:"gfmFootnoteCall",tokenize:ud},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:nd,resolveTo:rd}}}}function nd(e,t,n){const r=this;let u=r.events.length;const a=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let i;for(;u--;){const l=r.events[u][1];if(l.type==="labelImage"){i=l;break}if(l.type==="gfmFootnoteCall"||l.type==="labelLink"||l.type==="label"||l.type==="image"||l.type==="link")break}return o;function o(l){if(!i||!i._balanced)return n(l);const c=ze(r.sliceSerialize({start:i.end,end:r.now()}));return c.codePointAt(0)!==94||!a.includes(c.slice(1))?n(l):(e.enter("gfmFootnoteCallLabelMarker"),e.consume(l),e.exit("gfmFootnoteCallLabelMarker"),t(l))}}function rd(e,t){let n=e.length;for(;n--;)if(e[n][1].type==="labelImage"&&e[n][0]==="enter"){e[n][1];break}e[n+1][1].type="data",e[n+3][1].type="gfmFootnoteCallLabelMarker";const r={type:"gfmFootnoteCall",start:Object.assign({},e[n+3][1].start),end:Object.assign({},e[e.length-1][1].end)},u={type:"gfmFootnoteCallMarker",start:Object.assign({},e[n+3][1].end),end:Object.assign({},e[n+3][1].end)};u.end.column++,u.end.offset++,u.end._bufferIndex++;const a={type:"gfmFootnoteCallString",start:Object.assign({},u.end),end:Object.assign({},e[e.length-1][1].start)},i={type:"chunkString",contentType:"string",start:Object.assign({},a.start),end:Object.assign({},a.end)},o=[e[n+1],e[n+2],["enter",r,t],e[n+3],e[n+4],["enter",u,t],["exit",u,t],["enter",a,t],["enter",i,t],["exit",i,t],["exit",a,t],e[e.length-2],e[e.length-1],["exit",r,t]];return e.splice(n,e.length-n+1,...o),e}function ud(e,t,n){const r=this,u=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let a=0,i;return o;function o(f){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(f),e.exit("gfmFootnoteCallLabelMarker"),l}function l(f){return f!==94?n(f):(e.enter("gfmFootnoteCallMarker"),e.consume(f),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",c)}function c(f){if(a>999||f===93&&!i||f===null||f===91||ie(f))return n(f);if(f===93){e.exit("chunkString");const m=e.exit("gfmFootnoteCallString");return u.includes(ze(r.sliceSerialize(m)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(f),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(f)}return ie(f)||(i=!0),a++,e.consume(f),f===92?h:c}function h(f){return f===91||f===92||f===93?(e.consume(f),a++,c):c(f)}}function id(e,t,n){const r=this,u=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let a,i=0,o;return l;function l(_){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(_),e.exit("gfmFootnoteDefinitionLabelMarker"),c}function c(_){return _===94?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(_),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",h):n(_)}function h(_){if(i>999||_===93&&!o||_===null||_===91||ie(_))return n(_);if(_===93){e.exit("chunkString");const y=e.exit("gfmFootnoteDefinitionLabelString");return a=ze(r.sliceSerialize(y)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(_),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),m}return ie(_)||(o=!0),i++,e.consume(_),_===92?f:h}function f(_){return _===91||_===92||_===93?(e.consume(_),i++,h):h(_)}function m(_){return _===58?(e.enter("definitionMarker"),e.consume(_),e.exit("definitionMarker"),u.includes(a)||u.push(a),te(e,d,"gfmFootnoteDefinitionWhitespace")):n(_)}function d(_){return t(_)}}function ad(e,t,n){return e.check(an,t,e.attempt(ed,t,n))}function sd(e){e.exit("gfmFootnoteDefinition")}function od(e,t,n){const r=this;return te(e,u,"gfmFootnoteDefinitionIndent",5);function u(a){const i=r.events[r.events.length-1];return i&&i[1].type==="gfmFootnoteDefinitionIndent"&&i[2].sliceSerialize(i[1],!0).length===4?t(a):n(a)}}function ld(e){let n=(e||{}).singleTilde;const r={name:"strikethrough",tokenize:a,resolveAll:u};return n==null&&(n=!0),{text:{126:r},insideSpan:{null:[r]},attentionMarkers:{null:[126]}};function u(i,o){let l=-1;for(;++l<i.length;)if(i[l][0]==="enter"&&i[l][1].type==="strikethroughSequenceTemporary"&&i[l][1]._close){let c=l;for(;c--;)if(i[c][0]==="exit"&&i[c][1].type==="strikethroughSequenceTemporary"&&i[c][1]._open&&i[l][1].end.offset-i[l][1].start.offset===i[c][1].end.offset-i[c][1].start.offset){i[l][1].type="strikethroughSequence",i[c][1].type="strikethroughSequence";const h={type:"strikethrough",start:Object.assign({},i[c][1].start),end:Object.assign({},i[l][1].end)},f={type:"strikethroughText",start:Object.assign({},i[c][1].end),end:Object.assign({},i[l][1].start)},m=[["enter",h,o],["enter",i[c][1],o],["exit",i[c][1],o],["enter",f,o]],d=o.parser.constructs.insideSpan.null;d&&we(m,m.length,0,Dn(d,i.slice(c+1,l),o)),we(m,m.length,0,[["exit",f,o],["enter",i[l][1],o],["exit",i[l][1],o],["exit",h,o]]),we(i,c-1,l-c+3,m),l=c+m.length-2;break}}for(l=-1;++l<i.length;)i[l][1].type==="strikethroughSequenceTemporary"&&(i[l][1].type="data");return i}function a(i,o,l){const c=this.previous,h=this.events;let f=0;return m;function m(_){return c===126&&h[h.length-1][1].type!=="characterEscape"?l(_):(i.enter("strikethroughSequenceTemporary"),d(_))}function d(_){const y=It(c);if(_===126)return f>1?l(_):(i.consume(_),f++,d);if(f<2&&!n)return l(_);const M=i.exit("strikethroughSequenceTemporary"),k=It(_);return M._open=!k||k===2&&!!y,M._close=!y||y===2&&!!k,o(_)}}}class cd{constructor(){this.map=[]}add(t,n,r){fd(this,t,n,r)}consume(t){if(this.map.sort(function(a,i){return a[0]-i[0]}),this.map.length===0)return;let n=this.map.length;const r=[];for(;n>0;)n-=1,r.push(t.slice(this.map[n][0]+this.map[n][1]),this.map[n][2]),t.length=this.map[n][0];r.push(t.slice()),t.length=0;let u=r.pop();for(;u;){for(const a of u)t.push(a);u=r.pop()}this.map.length=0}}function fd(e,t,n,r){let u=0;if(!(n===0&&r.length===0)){for(;u<e.map.length;){if(e.map[u][0]===t){e.map[u][1]+=n,e.map[u][2].push(...r);return}u+=1}e.map.push([t,n,r])}}function hd(e,t){let n=!1;const r=[];for(;t<e.length;){const u=e[t];if(n){if(u[0]==="enter")u[1].type==="tableContent"&&r.push(e[t+1][1].type==="tableDelimiterMarker"?"left":"none");else if(u[1].type==="tableContent"){if(e[t-1][1].type==="tableDelimiterMarker"){const a=r.length-1;r[a]=r[a]==="left"?"center":"right"}}else if(u[1].type==="tableDelimiterRow")break}else u[0]==="enter"&&u[1].type==="tableDelimiterRow"&&(n=!0);t+=1}return r}function dd(){return{flow:{null:{name:"table",tokenize:pd,resolveAll:md}}}}function pd(e,t,n){const r=this;let u=0,a=0,i;return o;function o(A){let N=r.events.length-1;for(;N>-1;){const v=r.events[N][1].type;if(v==="lineEnding"||v==="linePrefix")N--;else break}const L=N>-1?r.events[N][1].type:null,H=L==="tableHead"||L==="tableRow"?x:l;return H===x&&r.parser.lazy[r.now().line]?n(A):H(A)}function l(A){return e.enter("tableHead"),e.enter("tableRow"),c(A)}function c(A){return A===124||(i=!0,a+=1),h(A)}function h(A){return A===null?n(A):Y(A)?a>1?(a=0,r.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(A),e.exit("lineEnding"),d):n(A):J(A)?te(e,h,"whitespace")(A):(a+=1,i&&(i=!1,u+=1),A===124?(e.enter("tableCellDivider"),e.consume(A),e.exit("tableCellDivider"),i=!0,h):(e.enter("data"),f(A)))}function f(A){return A===null||A===124||ie(A)?(e.exit("data"),h(A)):(e.consume(A),A===92?m:f)}function m(A){return A===92||A===124?(e.consume(A),f):f(A)}function d(A){return r.interrupt=!1,r.parser.lazy[r.now().line]?n(A):(e.enter("tableDelimiterRow"),i=!1,J(A)?te(e,_,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(A):_(A))}function _(A){return A===45||A===58?M(A):A===124?(i=!0,e.enter("tableCellDivider"),e.consume(A),e.exit("tableCellDivider"),y):V(A)}function y(A){return J(A)?te(e,M,"whitespace")(A):M(A)}function M(A){return A===58?(a+=1,i=!0,e.enter("tableDelimiterMarker"),e.consume(A),e.exit("tableDelimiterMarker"),k):A===45?(a+=1,k(A)):A===null||Y(A)?j(A):V(A)}function k(A){return A===45?(e.enter("tableDelimiterFiller"),F(A)):V(A)}function F(A){return A===45?(e.consume(A),F):A===58?(i=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(A),e.exit("tableDelimiterMarker"),B):(e.exit("tableDelimiterFiller"),B(A))}function B(A){return J(A)?te(e,j,"whitespace")(A):j(A)}function j(A){return A===124?_(A):A===null||Y(A)?!i||u!==a?V(A):(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(A)):V(A)}function V(A){return n(A)}function x(A){return e.enter("tableRow"),Q(A)}function Q(A){return A===124?(e.enter("tableCellDivider"),e.consume(A),e.exit("tableCellDivider"),Q):A===null||Y(A)?(e.exit("tableRow"),t(A)):J(A)?te(e,Q,"whitespace")(A):(e.enter("data"),ne(A))}function ne(A){return A===null||A===124||ie(A)?(e.exit("data"),Q(A)):(e.consume(A),A===92?ee:ne)}function ee(A){return A===92||A===124?(e.consume(A),ne):ne(A)}}function md(e,t){let n=-1,r=!0,u=0,a=[0,0,0,0],i=[0,0,0,0],o=!1,l=0,c,h,f;const m=new cd;for(;++n<e.length;){const d=e[n],_=d[1];d[0]==="enter"?_.type==="tableHead"?(o=!1,l!==0&&(ri(m,t,l,c,h),h=void 0,l=0),c={type:"table",start:Object.assign({},_.start),end:Object.assign({},_.end)},m.add(n,0,[["enter",c,t]])):_.type==="tableRow"||_.type==="tableDelimiterRow"?(r=!0,f=void 0,a=[0,0,0,0],i=[0,n+1,0,0],o&&(o=!1,h={type:"tableBody",start:Object.assign({},_.start),end:Object.assign({},_.end)},m.add(n,0,[["enter",h,t]])),u=_.type==="tableDelimiterRow"?2:h?3:1):u&&(_.type==="data"||_.type==="tableDelimiterMarker"||_.type==="tableDelimiterFiller")?(r=!1,i[2]===0&&(a[1]!==0&&(i[0]=i[1],f=En(m,t,a,u,void 0,f),a=[0,0,0,0]),i[2]=n)):_.type==="tableCellDivider"&&(r?r=!1:(a[1]!==0&&(i[0]=i[1],f=En(m,t,a,u,void 0,f)),a=i,i=[a[1],n,0,0])):_.type==="tableHead"?(o=!0,l=n):_.type==="tableRow"||_.type==="tableDelimiterRow"?(l=n,a[1]!==0?(i[0]=i[1],f=En(m,t,a,u,n,f)):i[1]!==0&&(f=En(m,t,i,u,n,f)),u=0):u&&(_.type==="data"||_.type==="tableDelimiterMarker"||_.type==="tableDelimiterFiller")&&(i[3]=n)}for(l!==0&&ri(m,t,l,c,h),m.consume(t.events),n=-1;++n<t.events.length;){const d=t.events[n];d[0]==="enter"&&d[1].type==="table"&&(d[1]._align=hd(t.events,n))}return e}function En(e,t,n,r,u,a){const i=r===1?"tableHeader":r===2?"tableDelimiter":"tableData",o="tableContent";n[0]!==0&&(a.end=Object.assign({},_t(t.events,n[0])),e.add(n[0],0,[["exit",a,t]]));const l=_t(t.events,n[1]);if(a={type:i,start:Object.assign({},l),end:Object.assign({},l)},e.add(n[1],0,[["enter",a,t]]),n[2]!==0){const c=_t(t.events,n[2]),h=_t(t.events,n[3]),f={type:o,start:Object.assign({},c),end:Object.assign({},h)};if(e.add(n[2],0,[["enter",f,t]]),r!==2){const m=t.events[n[2]],d=t.events[n[3]];if(m[1].end=Object.assign({},d[1].end),m[1].type="chunkText",m[1].contentType="text",n[3]>n[2]+1){const _=n[2]+1,y=n[3]-n[2]-1;e.add(_,y,[])}}e.add(n[3]+1,0,[["exit",f,t]])}return u!==void 0&&(a.end=Object.assign({},_t(t.events,u)),e.add(u,0,[["exit",a,t]]),a=void 0),a}function ri(e,t,n,r,u){const a=[],i=_t(t.events,n);u&&(u.end=Object.assign({},i),a.push(["exit",u,t])),r.end=Object.assign({},i),a.push(["exit",r,t]),e.add(n+1,0,a)}function _t(e,t){const n=e[t],r=n[0]==="enter"?"start":"end";return n[1][r]}const Ed={name:"tasklistCheck",tokenize:Td};function gd(){return{text:{91:Ed}}}function Td(e,t,n){const r=this;return u;function u(l){return r.previous!==null||!r._gfmTasklistFirstContentOfListItem?n(l):(e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(l),e.exit("taskListCheckMarker"),a)}function a(l){return ie(l)?(e.enter("taskListCheckValueUnchecked"),e.consume(l),e.exit("taskListCheckValueUnchecked"),i):l===88||l===120?(e.enter("taskListCheckValueChecked"),e.consume(l),e.exit("taskListCheckValueChecked"),i):n(l)}function i(l){return l===93?(e.enter("taskListCheckMarker"),e.consume(l),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),o):n(l)}function o(l){return Y(l)?t(l):J(l)?e.check({tokenize:bd},t,n)(l):n(l)}}function bd(e,t,n){return te(e,r,"whitespace");function r(u){return u===null?n(u):t(u)}}function Ad(e){return Xi([Vh(),td(),ld(e),dd(),gd()])}const _d={};function ui(e){const t=this,n=e||_d,r=t.data(),u=r.micromarkExtensions||(r.micromarkExtensions=[]),a=r.fromMarkdownExtensions||(r.fromMarkdownExtensions=[]),i=r.toMarkdownExtensions||(r.toMarkdownExtensions=[]);u.push(Ad(n)),a.push(zh()),i.push(Yh(n))}const Ua=-1,Mn=0,Qt=1,In=2,Xr=3,Kr=4,Gr=5,Jr=6,Ha=7,za=8,ii=typeof self=="object"?self:globalThis,Cd=(e,t)=>{const n=(u,a)=>(e.set(a,u),u),r=u=>{if(e.has(u))return e.get(u);const[a,i]=t[u];switch(a){case Mn:case Ua:return n(i,u);case Qt:{const o=n([],u);for(const l of i)o.push(r(l));return o}case In:{const o=n({},u);for(const[l,c]of i)o[r(l)]=r(c);return o}case Xr:return n(new Date(i),u);case Kr:{const{source:o,flags:l}=i;return n(new RegExp(o,l),u)}case Gr:{const o=n(new Map,u);for(const[l,c]of i)o.set(r(l),r(c));return o}case Jr:{const o=n(new Set,u);for(const l of i)o.add(r(l));return o}case Ha:{const{name:o,message:l}=i;return n(new ii[o](l),u)}case za:return n(BigInt(i),u);case"BigInt":return n(Object(BigInt(i)),u);case"ArrayBuffer":return n(new Uint8Array(i).buffer,i);case"DataView":{const{buffer:o}=new Uint8Array(i);return n(new DataView(o),i)}}return n(new ii[a](i),u)};return r},ai=e=>Cd(new Map,e)(0),At="",{toString:kd}={},{keys:Sd}=Object,zt=e=>{const t=typeof e;if(t!=="object"||!e)return[Mn,t];const n=kd.call(e).slice(8,-1);switch(n){case"Array":return[Qt,At];case"Object":return[In,At];case"Date":return[Xr,At];case"RegExp":return[Kr,At];case"Map":return[Gr,At];case"Set":return[Jr,At];case"DataView":return[Qt,n]}return n.includes("Array")?[Qt,n]:n.includes("Error")?[Ha,n]:[In,n]},gn=([e,t])=>e===Mn&&(t==="function"||t==="symbol"),yd=(e,t,n,r)=>{const u=(i,o)=>{const l=r.push(i)-1;return n.set(o,l),l},a=i=>{if(n.has(i))return n.get(i);let[o,l]=zt(i);switch(o){case Mn:{let h=i;switch(l){case"bigint":o=za,h=i.toString();break;case"function":case"symbol":if(e)throw new TypeError("unable to serialize "+l);h=null;break;case"undefined":return u([Ua],i)}return u([o,h],i)}case Qt:{if(l){let m=i;return l==="DataView"?m=new Uint8Array(i.buffer):l==="ArrayBuffer"&&(m=new Uint8Array(i)),u([l,[...m]],i)}const h=[],f=u([o,h],i);for(const m of i)h.push(a(m));return f}case In:{if(l)switch(l){case"BigInt":return u([l,i.toString()],i);case"Boolean":case"Number":case"String":return u([l,i.valueOf()],i)}if(t&&"toJSON"in i)return a(i.toJSON());const h=[],f=u([o,h],i);for(const m of Sd(i))(e||!gn(zt(i[m])))&&h.push([a(m),a(i[m])]);return f}case Xr:return u([o,i.toISOString()],i);case Kr:{const{source:h,flags:f}=i;return u([o,{source:h,flags:f}],i)}case Gr:{const h=[],f=u([o,h],i);for(const[m,d]of i)(e||!(gn(zt(m))||gn(zt(d))))&&h.push([a(m),a(d)]);return f}case Jr:{const h=[],f=u([o,h],i);for(const m of i)(e||!gn(zt(m)))&&h.push(a(m));return f}}const{message:c}=i;return u([o,{name:l,message:c}],i)};return a},si=(e,{json:t,lossy:n}={})=>{const r=[];return yd(!(t||n),!!t,new Map,r)(e),r},oi=typeof structuredClone=="function"?(e,t)=>t&&("json"in t||"lossy"in t)?ai(si(e,t)):structuredClone(e):(e,t)=>ai(si(e,t));let on=class{constructor(t,n,r){this.normal=n,this.property=t,r&&(this.space=r)}};on.prototype.normal={};on.prototype.property={};on.prototype.space=void 0;function Ya(e,t){const n={},r={};for(const u of e)Object.assign(n,u.property),Object.assign(r,u.normal);return new on(n,r,t)}function nn(e){return e.toLowerCase()}let xe=class{constructor(t,n){this.attribute=n,this.property=t}};xe.prototype.attribute="";xe.prototype.booleanish=!1;xe.prototype.boolean=!1;xe.prototype.commaOrSpaceSeparated=!1;xe.prototype.commaSeparated=!1;xe.prototype.defined=!1;xe.prototype.mustUseProperty=!1;xe.prototype.number=!1;xe.prototype.overloadedBoolean=!1;xe.prototype.property="";xe.prototype.spaceSeparated=!1;xe.prototype.space=void 0;let Id=0;const K=dt(),pe=dt(),Cr=dt(),P=dt(),oe=dt(),St=dt(),Re=dt();function dt(){return 2**++Id}const kr=Object.freeze(Object.defineProperty({__proto__:null,boolean:K,booleanish:pe,commaOrSpaceSeparated:Re,commaSeparated:St,number:P,overloadedBoolean:Cr,spaceSeparated:oe},Symbol.toStringTag,{value:"Module"})),sr=Object.keys(kr);let Zr=class extends xe{constructor(t,n,r,u){let a=-1;if(super(t,n),li(this,"space",u),typeof r=="number")for(;++a<sr.length;){const i=sr[a];li(this,sr[a],(r&kr[i])===kr[i])}}};Zr.prototype.defined=!0;function li(e,t,n){n&&(e[t]=n)}function Lt(e){const t={},n={};for(const[r,u]of Object.entries(e.properties)){const a=new Zr(r,e.transform(e.attributes||{},r),u,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(a.mustUseProperty=!0),t[r]=a,n[nn(r)]=r,n[nn(a.attribute)]=r}return new on(t,n,e.space)}const qa=Lt({properties:{ariaActiveDescendant:null,ariaAtomic:pe,ariaAutoComplete:null,ariaBusy:pe,ariaChecked:pe,ariaColCount:P,ariaColIndex:P,ariaColSpan:P,ariaControls:oe,ariaCurrent:null,ariaDescribedBy:oe,ariaDetails:null,ariaDisabled:pe,ariaDropEffect:oe,ariaErrorMessage:null,ariaExpanded:pe,ariaFlowTo:oe,ariaGrabbed:pe,ariaHasPopup:null,ariaHidden:pe,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:oe,ariaLevel:P,ariaLive:null,ariaModal:pe,ariaMultiLine:pe,ariaMultiSelectable:pe,ariaOrientation:null,ariaOwns:oe,ariaPlaceholder:null,ariaPosInSet:P,ariaPressed:pe,ariaReadOnly:pe,ariaRelevant:null,ariaRequired:pe,ariaRoleDescription:oe,ariaRowCount:P,ariaRowIndex:P,ariaRowSpan:P,ariaSelected:pe,ariaSetSize:P,ariaSort:null,ariaValueMax:P,ariaValueMin:P,ariaValueNow:P,ariaValueText:null,role:null},transform(e,t){return t==="role"?t:"aria-"+t.slice(4).toLowerCase()}});function ja(e,t){return t in e?e[t]:t}function Va(e,t){return ja(e,t.toLowerCase())}const xd=Lt({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:St,acceptCharset:oe,accessKey:oe,action:null,allow:null,allowFullScreen:K,allowPaymentRequest:K,allowUserMedia:K,alt:null,as:null,async:K,autoCapitalize:null,autoComplete:oe,autoFocus:K,autoPlay:K,blocking:oe,capture:null,charSet:null,checked:K,cite:null,className:oe,cols:P,colSpan:null,content:null,contentEditable:pe,controls:K,controlsList:oe,coords:P|St,crossOrigin:null,data:null,dateTime:null,decoding:null,default:K,defer:K,dir:null,dirName:null,disabled:K,download:Cr,draggable:pe,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:K,formTarget:null,headers:oe,height:P,hidden:Cr,high:P,href:null,hrefLang:null,htmlFor:oe,httpEquiv:oe,id:null,imageSizes:null,imageSrcSet:null,inert:K,inputMode:null,integrity:null,is:null,isMap:K,itemId:null,itemProp:oe,itemRef:oe,itemScope:K,itemType:oe,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:K,low:P,manifest:null,max:null,maxLength:P,media:null,method:null,min:null,minLength:P,multiple:K,muted:K,name:null,nonce:null,noModule:K,noValidate:K,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:K,optimum:P,pattern:null,ping:oe,placeholder:null,playsInline:K,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:K,referrerPolicy:null,rel:oe,required:K,reversed:K,rows:P,rowSpan:P,sandbox:oe,scope:null,scoped:K,seamless:K,selected:K,shadowRootClonable:K,shadowRootDelegatesFocus:K,shadowRootMode:null,shape:null,size:P,sizes:null,slot:null,span:P,spellCheck:pe,src:null,srcDoc:null,srcLang:null,srcSet:null,start:P,step:null,style:null,tabIndex:P,target:null,title:null,translate:null,type:null,typeMustMatch:K,useMap:null,value:pe,width:P,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:oe,axis:null,background:null,bgColor:null,border:P,borderColor:null,bottomMargin:P,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:K,declare:K,event:null,face:null,frame:null,frameBorder:null,hSpace:P,leftMargin:P,link:null,longDesc:null,lowSrc:null,marginHeight:P,marginWidth:P,noResize:K,noHref:K,noShade:K,noWrap:K,object:null,profile:null,prompt:null,rev:null,rightMargin:P,rules:null,scheme:null,scrolling:pe,standby:null,summary:null,text:null,topMargin:P,valueType:null,version:null,vAlign:null,vLink:null,vSpace:P,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:K,disableRemotePlayback:K,prefix:null,property:null,results:P,security:null,unselectable:null},space:"html",transform:Va}),Nd=Lt({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:Re,accentHeight:P,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:P,amplitude:P,arabicForm:null,ascent:P,attributeName:null,attributeType:null,azimuth:P,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:P,by:null,calcMode:null,capHeight:P,className:oe,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:P,diffuseConstant:P,direction:null,display:null,dur:null,divisor:P,dominantBaseline:null,download:K,dx:null,dy:null,edgeMode:null,editable:null,elevation:P,enableBackground:null,end:null,event:null,exponent:P,externalResourcesRequired:null,fill:null,fillOpacity:P,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:St,g2:St,glyphName:St,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:P,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:P,horizOriginX:P,horizOriginY:P,id:null,ideographic:P,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:P,k:P,k1:P,k2:P,k3:P,k4:P,kernelMatrix:Re,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:P,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:P,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:P,overlineThickness:P,paintOrder:null,panose1:null,path:null,pathLength:P,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:oe,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:P,pointsAtY:P,pointsAtZ:P,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:Re,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:Re,rev:Re,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:Re,requiredFeatures:Re,requiredFonts:Re,requiredFormats:Re,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:P,specularExponent:P,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:P,strikethroughThickness:P,string:null,stroke:null,strokeDashArray:Re,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:P,strokeOpacity:P,strokeWidth:null,style:null,surfaceScale:P,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:Re,tabIndex:P,tableValues:null,target:null,targetX:P,targetY:P,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:Re,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:P,underlineThickness:P,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:P,values:null,vAlphabetic:P,vMathematical:P,vectorEffect:null,vHanging:P,vIdeographic:P,version:null,vertAdvY:P,vertOriginX:P,vertOriginY:P,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:P,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:ja}),$a=Lt({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(e,t){return"xlink:"+t.slice(5).toLowerCase()}}),Wa=Lt({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:Va}),Qa=Lt({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(e,t){return"xml:"+t.slice(3).toLowerCase()}}),Od=/[A-Z]/g,ci=/-[a-z]/g,Ld=/^data[-\w.:]+$/i;function Xa(e,t){const n=nn(t);let r=t,u=xe;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&n.slice(0,4)==="data"&&Ld.test(t)){if(t.charAt(4)==="-"){const a=t.slice(5).replace(ci,Rd);r="data"+a.charAt(0).toUpperCase()+a.slice(1)}else{const a=t.slice(4);if(!ci.test(a)){let i=a.replace(Od,Dd);i.charAt(0)!=="-"&&(i="-"+i),t="data"+i}}u=Zr}return new u(r,t)}function Dd(e){return"-"+e.toLowerCase()}function Rd(e){return e.charAt(1).toUpperCase()}const eu=Ya([qa,xd,$a,Wa,Qa],"html"),tu=Ya([qa,Nd,$a,Wa,Qa],"svg");function fi(e){const t=[],n=String(e||"");let r=n.indexOf(","),u=0,a=!1;for(;!a;){r===-1&&(r=n.length,a=!0);const i=n.slice(u,r).trim();(i||!a)&&t.push(i),u=r+1,r=n.indexOf(",",u)}return t}function Pd(e,t){const n={};return(e[e.length-1]===""?[...e,""]:e).join((n.padRight?" ":"")+","+(n.padLeft===!1?"":" ")).trim()}const hi=/[#.]/g;function wd(e,t){const n=e||"",r={};let u=0,a,i;for(;u<n.length;){hi.lastIndex=u;const o=hi.exec(n),l=n.slice(u,o?o.index:n.length);l&&(a?a==="#"?r.id=l:Array.isArray(r.className)?r.className.push(l):r.className=[l]:i=l,u+=l.length),o&&(a=o[0],u++)}return{type:"element",tagName:i||t||"div",properties:r,children:[]}}function di(e){const t=String(e||"").trim();return t?t.split(/[ \t\n\r\f]+/g):[]}function Md(e){return e.join(" ").trim()}function Ka(e,t,n){const r=n?Ud(n):void 0;function u(a,i,...o){let l;if(a==null){l={type:"root",children:[]};const c=i;o.unshift(c)}else{l=wd(a,t);const c=l.tagName.toLowerCase(),h=r?r.get(c):void 0;if(l.tagName=h||c,Bd(i))o.unshift(i);else for(const[f,m]of Object.entries(i))Fd(e,l.properties,f,m)}for(const c of o)Sr(l.children,c);return l.type==="element"&&l.tagName==="template"&&(l.content={type:"root",children:l.children},l.children=[]),l}return u}function Bd(e){if(e===null||typeof e!="object"||Array.isArray(e))return!0;if(typeof e.type!="string")return!1;const t=e,n=Object.keys(e);for(const r of n){const u=t[r];if(u&&typeof u=="object"){if(!Array.isArray(u))return!0;const a=u;for(const i of a)if(typeof i!="number"&&typeof i!="string")return!0}}return!!("children"in e&&Array.isArray(e.children))}function Fd(e,t,n,r){const u=Xa(e,n);let a;if(r!=null){if(typeof r=="number"){if(Number.isNaN(r))return;a=r}else typeof r=="boolean"?a=r:typeof r=="string"?u.spaceSeparated?a=di(r):u.commaSeparated?a=fi(r):u.commaOrSpaceSeparated?a=di(fi(r).join(" ")):a=pi(u,u.property,r):Array.isArray(r)?a=[...r]:a=u.property==="style"?vd(r):String(r);if(Array.isArray(a)){const i=[];for(const o of a)i.push(pi(u,u.property,o));a=i}u.property==="className"&&Array.isArray(t.className)&&(a=t.className.concat(a)),t[u.property]=a}}function Sr(e,t){if(t!=null)if(typeof t=="number"||typeof t=="string")e.push({type:"text",value:String(t)});else if(Array.isArray(t))for(const n of t)Sr(e,n);else if(typeof t=="object"&&"type"in t)t.type==="root"?Sr(e,t.children):e.push(t);else throw new Error("Expected node, nodes, or string, got `"+t+"`")}function pi(e,t,n){if(typeof n=="string"){if(e.number&&n&&!Number.isNaN(Number(n)))return Number(n);if((e.boolean||e.overloadedBoolean)&&(n===""||nn(n)===nn(t)))return!0}return n}function vd(e){const t=[];for(const[n,r]of Object.entries(e))t.push([n,r].join(": "));return t.join("; ")}function Ud(e){const t=new Map;for(const n of e)t.set(n.toLowerCase(),n);return t}const Hd=["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","solidColor","textArea","textPath"],zd=Ka(eu,"div"),Yd=Ka(tu,"g",Hd);function qd(e){const t=String(e),n=[];return{toOffset:u,toPoint:r};function r(a){if(typeof a=="number"&&a>-1&&a<=t.length){let i=0;for(;;){let o=n[i];if(o===void 0){const l=mi(t,n[i-1]);o=l===-1?t.length+1:l+1,n[i]=o}if(o>a)return{line:i+1,column:a-(i>0?n[i-1]:0)+1,offset:a};i++}}}function u(a){if(a&&typeof a.line=="number"&&typeof a.column=="number"&&!Number.isNaN(a.line)&&!Number.isNaN(a.column)){for(;n.length<a.line;){const o=n[n.length-1],l=mi(t,o),c=l===-1?t.length+1:l+1;if(o===c)break;n.push(c)}const i=(a.line>1?n[a.line-2]:0)+a.column-1;if(i<n[a.line-1])return i}}}function mi(e,t){const n=e.indexOf("\r",t),r=e.indexOf(`
`,t);return r===-1?n:n===-1||n+1===r?r:n<r?n:r}const lt={html:"http://www.w3.org/1999/xhtml",mathml:"http://www.w3.org/1998/Math/MathML",svg:"http://www.w3.org/2000/svg",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"},Ga={}.hasOwnProperty,jd=Object.prototype;function Vd(e,t){const n=t||{};return nu({file:n.file||void 0,location:!1,schema:n.space==="svg"?tu:eu,verbose:n.verbose||!1},e)}function nu(e,t){let n;switch(t.nodeName){case"#comment":{const r=t;return n={type:"comment",value:r.data},An(e,r,n),n}case"#document":case"#document-fragment":{const r=t,u="mode"in r?r.mode==="quirks"||r.mode==="limited-quirks":!1;if(n={type:"root",children:Ja(e,t.childNodes),data:{quirksMode:u}},e.file&&e.location){const a=String(e.file),i=qd(a),o=i.toPoint(0),l=i.toPoint(a.length);n.position={start:o,end:l}}return n}case"#documentType":{const r=t;return n={type:"doctype"},An(e,r,n),n}case"#text":{const r=t;return n={type:"text",value:r.value},An(e,r,n),n}default:return n=$d(e,t),n}}function Ja(e,t){let n=-1;const r=[];for(;++n<t.length;){const u=nu(e,t[n]);r.push(u)}return r}function $d(e,t){const n=e.schema;e.schema=t.namespaceURI===lt.svg?tu:eu;let r=-1;const u={};for(;++r<t.attrs.length;){const o=t.attrs[r],l=(o.prefix?o.prefix+":":"")+o.name;Ga.call(jd,l)||(u[l]=o.value)}const i=(e.schema.space==="svg"?Yd:zd)(t.tagName,u,Ja(e,t.childNodes));if(An(e,t,i),i.tagName==="template"){const o=t,l=o.sourceCodeLocation,c=l&&l.startTag&&Ct(l.startTag),h=l&&l.endTag&&Ct(l.endTag),f=nu(e,o.content);c&&h&&e.file&&(f.position={start:c.end,end:h.start}),i.content=f}return e.schema=n,i}function An(e,t,n){if("sourceCodeLocation"in t&&t.sourceCodeLocation&&e.file){const r=Wd(e,n,t.sourceCodeLocation);r&&(e.location=!0,n.position=r)}}function Wd(e,t,n){const r=Ct(n);if(t.type==="element"){const u=t.children[t.children.length-1];if(r&&!n.endTag&&u&&u.position&&u.position.end&&(r.end=Object.assign({},u.position.end)),e.verbose){const a={};let i;if(n.attrs)for(i in n.attrs)Ga.call(n.attrs,i)&&(a[Xa(e.schema,i).property]=Ct(n.attrs[i]));n.startTag;const o=Ct(n.startTag),l=n.endTag?Ct(n.endTag):void 0,c={opening:o};l&&(c.closing=l),c.properties=a,t.data={position:c}}}return r}function Ct(e){const t=Ei({line:e.startLine,column:e.startCol,offset:e.startOffset}),n=Ei({line:e.endLine,column:e.endCol,offset:e.endOffset});return t||n?{start:t,end:n}:void 0}function Ei(e){return e.line&&e.column?e:void 0}class ln{constructor(t,n,r){this.property=t,this.normal=n,r&&(this.space=r)}}ln.prototype.property={};ln.prototype.normal={};ln.prototype.space=null;function Za(e,t){const n={},r={};let u=-1;for(;++u<e.length;)Object.assign(n,e[u].property),Object.assign(r,e[u].normal);return new ln(n,r,t)}function yr(e){return e.toLowerCase()}class ve{constructor(t,n){this.property=t,this.attribute=n}}ve.prototype.space=null;ve.prototype.boolean=!1;ve.prototype.booleanish=!1;ve.prototype.overloadedBoolean=!1;ve.prototype.number=!1;ve.prototype.commaSeparated=!1;ve.prototype.spaceSeparated=!1;ve.prototype.commaOrSpaceSeparated=!1;ve.prototype.mustUseProperty=!1;ve.prototype.defined=!1;let Qd=0;const W=pt(),me=pt(),es=pt(),w=pt(),le=pt(),yt=pt(),Pe=pt();function pt(){return 2**++Qd}const Ir=Object.freeze(Object.defineProperty({__proto__:null,boolean:W,booleanish:me,commaOrSpaceSeparated:Pe,commaSeparated:yt,number:w,overloadedBoolean:es,spaceSeparated:le},Symbol.toStringTag,{value:"Module"})),or=Object.keys(Ir);class ru extends ve{constructor(t,n,r,u){let a=-1;if(super(t,n),gi(this,"space",u),typeof r=="number")for(;++a<or.length;){const i=or[a];gi(this,or[a],(r&Ir[i])===Ir[i])}}}ru.prototype.defined=!0;function gi(e,t,n){n&&(e[t]=n)}const Xd={}.hasOwnProperty;function Dt(e){const t={},n={};let r;for(r in e.properties)if(Xd.call(e.properties,r)){const u=e.properties[r],a=new ru(r,e.transform(e.attributes||{},r),u,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(a.mustUseProperty=!0),t[r]=a,n[yr(r)]=r,n[yr(a.attribute)]=r}return new ln(t,n,e.space)}const ts=Dt({space:"xlink",transform(e,t){return"xlink:"+t.slice(5).toLowerCase()},properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null}}),ns=Dt({space:"xml",transform(e,t){return"xml:"+t.slice(3).toLowerCase()},properties:{xmlLang:null,xmlBase:null,xmlSpace:null}});function rs(e,t){return t in e?e[t]:t}function us(e,t){return rs(e,t.toLowerCase())}const is=Dt({space:"xmlns",attributes:{xmlnsxlink:"xmlns:xlink"},transform:us,properties:{xmlns:null,xmlnsXLink:null}}),as=Dt({transform(e,t){return t==="role"?t:"aria-"+t.slice(4).toLowerCase()},properties:{ariaActiveDescendant:null,ariaAtomic:me,ariaAutoComplete:null,ariaBusy:me,ariaChecked:me,ariaColCount:w,ariaColIndex:w,ariaColSpan:w,ariaControls:le,ariaCurrent:null,ariaDescribedBy:le,ariaDetails:null,ariaDisabled:me,ariaDropEffect:le,ariaErrorMessage:null,ariaExpanded:me,ariaFlowTo:le,ariaGrabbed:me,ariaHasPopup:null,ariaHidden:me,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:le,ariaLevel:w,ariaLive:null,ariaModal:me,ariaMultiLine:me,ariaMultiSelectable:me,ariaOrientation:null,ariaOwns:le,ariaPlaceholder:null,ariaPosInSet:w,ariaPressed:me,ariaReadOnly:me,ariaRelevant:null,ariaRequired:me,ariaRoleDescription:le,ariaRowCount:w,ariaRowIndex:w,ariaRowSpan:w,ariaSelected:me,ariaSetSize:w,ariaSort:null,ariaValueMax:w,ariaValueMin:w,ariaValueNow:w,ariaValueText:null,role:null}}),Kd=Dt({space:"html",attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},transform:us,mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:yt,acceptCharset:le,accessKey:le,action:null,allow:null,allowFullScreen:W,allowPaymentRequest:W,allowUserMedia:W,alt:null,as:null,async:W,autoCapitalize:null,autoComplete:le,autoFocus:W,autoPlay:W,blocking:le,capture:null,charSet:null,checked:W,cite:null,className:le,cols:w,colSpan:null,content:null,contentEditable:me,controls:W,controlsList:le,coords:w|yt,crossOrigin:null,data:null,dateTime:null,decoding:null,default:W,defer:W,dir:null,dirName:null,disabled:W,download:es,draggable:me,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:W,formTarget:null,headers:le,height:w,hidden:W,high:w,href:null,hrefLang:null,htmlFor:le,httpEquiv:le,id:null,imageSizes:null,imageSrcSet:null,inert:W,inputMode:null,integrity:null,is:null,isMap:W,itemId:null,itemProp:le,itemRef:le,itemScope:W,itemType:le,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:W,low:w,manifest:null,max:null,maxLength:w,media:null,method:null,min:null,minLength:w,multiple:W,muted:W,name:null,nonce:null,noModule:W,noValidate:W,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:W,optimum:w,pattern:null,ping:le,placeholder:null,playsInline:W,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:W,referrerPolicy:null,rel:le,required:W,reversed:W,rows:w,rowSpan:w,sandbox:le,scope:null,scoped:W,seamless:W,selected:W,shadowRootClonable:W,shadowRootDelegatesFocus:W,shadowRootMode:null,shape:null,size:w,sizes:null,slot:null,span:w,spellCheck:me,src:null,srcDoc:null,srcLang:null,srcSet:null,start:w,step:null,style:null,tabIndex:w,target:null,title:null,translate:null,type:null,typeMustMatch:W,useMap:null,value:me,width:w,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:le,axis:null,background:null,bgColor:null,border:w,borderColor:null,bottomMargin:w,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:W,declare:W,event:null,face:null,frame:null,frameBorder:null,hSpace:w,leftMargin:w,link:null,longDesc:null,lowSrc:null,marginHeight:w,marginWidth:w,noResize:W,noHref:W,noShade:W,noWrap:W,object:null,profile:null,prompt:null,rev:null,rightMargin:w,rules:null,scheme:null,scrolling:me,standby:null,summary:null,text:null,topMargin:w,valueType:null,version:null,vAlign:null,vLink:null,vSpace:w,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:W,disableRemotePlayback:W,prefix:null,property:null,results:w,security:null,unselectable:null}}),Gd=Dt({space:"svg",attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},transform:rs,properties:{about:Pe,accentHeight:w,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:w,amplitude:w,arabicForm:null,ascent:w,attributeName:null,attributeType:null,azimuth:w,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:w,by:null,calcMode:null,capHeight:w,className:le,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:w,diffuseConstant:w,direction:null,display:null,dur:null,divisor:w,dominantBaseline:null,download:W,dx:null,dy:null,edgeMode:null,editable:null,elevation:w,enableBackground:null,end:null,event:null,exponent:w,externalResourcesRequired:null,fill:null,fillOpacity:w,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:yt,g2:yt,glyphName:yt,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:w,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:w,horizOriginX:w,horizOriginY:w,id:null,ideographic:w,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:w,k:w,k1:w,k2:w,k3:w,k4:w,kernelMatrix:Pe,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:w,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:w,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:w,overlineThickness:w,paintOrder:null,panose1:null,path:null,pathLength:w,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:le,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:w,pointsAtY:w,pointsAtZ:w,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:Pe,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:Pe,rev:Pe,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:Pe,requiredFeatures:Pe,requiredFonts:Pe,requiredFormats:Pe,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:w,specularExponent:w,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:w,strikethroughThickness:w,string:null,stroke:null,strokeDashArray:Pe,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:w,strokeOpacity:w,strokeWidth:null,style:null,surfaceScale:w,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:Pe,tabIndex:w,tableValues:null,target:null,targetX:w,targetY:w,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:Pe,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:w,underlineThickness:w,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:w,values:null,vAlphabetic:w,vMathematical:w,vectorEffect:null,vHanging:w,vIdeographic:w,version:null,vertAdvY:w,vertOriginX:w,vertOriginY:w,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:w,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null}}),Jd=/^data[-\w.:]+$/i,Ti=/-[a-z]/g,Zd=/[A-Z]/g;function ep(e,t){const n=yr(t);let r=t,u=ve;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&n.slice(0,4)==="data"&&Jd.test(t)){if(t.charAt(4)==="-"){const a=t.slice(5).replace(Ti,np);r="data"+a.charAt(0).toUpperCase()+a.slice(1)}else{const a=t.slice(4);if(!Ti.test(a)){let i=a.replace(Zd,tp);i.charAt(0)!=="-"&&(i="-"+i),t="data"+i}}u=ru}return new u(r,t)}function tp(e){return"-"+e.toLowerCase()}function np(e){return e.charAt(1).toUpperCase()}const rp=Za([ns,ts,is,as,Kd],"html"),ss=Za([ns,ts,is,as,Gd],"svg"),bi={}.hasOwnProperty;function os(e,t){const n=t||{};function r(u,...a){let i=r.invalid;const o=r.handlers;if(u&&bi.call(u,e)){const l=String(u[e]);i=bi.call(o,l)?o[l]:r.unknown}if(i)return i.call(this,u,...a)}return r.handlers=n.handlers||{},r.invalid=n.invalid,r.unknown=n.unknown,r}const up={},ip={}.hasOwnProperty,ls=os("type",{handlers:{root:sp,element:hp,text:cp,comment:fp,doctype:lp}});function ap(e,t){const r=(t||up).space;return ls(e,r==="svg"?ss:rp)}function sp(e,t){const n={nodeName:"#document",mode:(e.data||{}).quirksMode?"quirks":"no-quirks",childNodes:[]};return n.childNodes=uu(e.children,n,t),Rt(e,n),n}function op(e,t){const n={nodeName:"#document-fragment",childNodes:[]};return n.childNodes=uu(e.children,n,t),Rt(e,n),n}function lp(e){const t={nodeName:"#documentType",name:"html",publicId:"",systemId:"",parentNode:null};return Rt(e,t),t}function cp(e){const t={nodeName:"#text",value:e.value,parentNode:null};return Rt(e,t),t}function fp(e){const t={nodeName:"#comment",data:e.value,parentNode:null};return Rt(e,t),t}function hp(e,t){const n=t;let r=n;e.type==="element"&&e.tagName.toLowerCase()==="svg"&&n.space==="html"&&(r=ss);const u=[];let a;if(e.properties){for(a in e.properties)if(a!=="children"&&ip.call(e.properties,a)){const l=dp(r,a,e.properties[a]);l&&u.push(l)}}const i=r.space,o={nodeName:e.tagName,tagName:e.tagName,attrs:u,namespaceURI:lt[i],childNodes:[],parentNode:null};return o.childNodes=uu(e.children,o,r),Rt(e,o),e.tagName==="template"&&e.content&&(o.content=op(e.content,r)),o}function dp(e,t,n){const r=ep(e,t);if(n===!1||n===null||n===void 0||typeof n=="number"&&Number.isNaN(n)||!n&&r.boolean)return;Array.isArray(n)&&(n=r.commaSeparated?Pd(n):Md(n));const u={name:r.attribute,value:n===!0?"":String(n)};if(r.space&&r.space!=="html"&&r.space!=="svg"){const a=u.name.indexOf(":");a<0?u.prefix="":(u.name=u.name.slice(a+1),u.prefix=r.attribute.slice(0,a)),u.namespace=lt[r.space]}return u}function uu(e,t,n){let r=-1;const u=[];if(e)for(;++r<e.length;){const a=ls(e[r],n);a.parentNode=t,u.push(a)}return u}function Rt(e,t){const n=e.position;n&&n.start&&n.end&&(n.start.offset,n.end.offset,t.sourceCodeLocation={startLine:n.start.line,startCol:n.start.column,startOffset:n.start.offset,endLine:n.end.line,endCol:n.end.column,endOffset:n.end.offset})}const pp=["area","base","basefont","bgsound","br","col","command","embed","frame","hr","image","img","input","keygen","link","meta","param","source","track","wbr"],mp=new Set([65534,65535,131070,131071,196606,196607,262142,262143,327678,327679,393214,393215,458750,458751,524286,524287,589822,589823,655358,655359,720894,720895,786430,786431,851966,851967,917502,917503,983038,983039,1048574,1048575,1114110,1114111]),ce="�";var p;(function(e){e[e.EOF=-1]="EOF",e[e.NULL=0]="NULL",e[e.TABULATION=9]="TABULATION",e[e.CARRIAGE_RETURN=13]="CARRIAGE_RETURN",e[e.LINE_FEED=10]="LINE_FEED",e[e.FORM_FEED=12]="FORM_FEED",e[e.SPACE=32]="SPACE",e[e.EXCLAMATION_MARK=33]="EXCLAMATION_MARK",e[e.QUOTATION_MARK=34]="QUOTATION_MARK",e[e.AMPERSAND=38]="AMPERSAND",e[e.APOSTROPHE=39]="APOSTROPHE",e[e.HYPHEN_MINUS=45]="HYPHEN_MINUS",e[e.SOLIDUS=47]="SOLIDUS",e[e.DIGIT_0=48]="DIGIT_0",e[e.DIGIT_9=57]="DIGIT_9",e[e.SEMICOLON=59]="SEMICOLON",e[e.LESS_THAN_SIGN=60]="LESS_THAN_SIGN",e[e.EQUALS_SIGN=61]="EQUALS_SIGN",e[e.GREATER_THAN_SIGN=62]="GREATER_THAN_SIGN",e[e.QUESTION_MARK=63]="QUESTION_MARK",e[e.LATIN_CAPITAL_A=65]="LATIN_CAPITAL_A",e[e.LATIN_CAPITAL_Z=90]="LATIN_CAPITAL_Z",e[e.RIGHT_SQUARE_BRACKET=93]="RIGHT_SQUARE_BRACKET",e[e.GRAVE_ACCENT=96]="GRAVE_ACCENT",e[e.LATIN_SMALL_A=97]="LATIN_SMALL_A",e[e.LATIN_SMALL_Z=122]="LATIN_SMALL_Z"})(p||(p={}));const Se={DASH_DASH:"--",CDATA_START:"[CDATA[",DOCTYPE:"doctype",SCRIPT:"script",PUBLIC:"public",SYSTEM:"system"};function cs(e){return e>=55296&&e<=57343}function Ep(e){return e>=56320&&e<=57343}function gp(e,t){return(e-55296)*1024+9216+t}function fs(e){return e!==32&&e!==10&&e!==13&&e!==9&&e!==12&&e>=1&&e<=31||e>=127&&e<=159}function hs(e){return e>=64976&&e<=65007||mp.has(e)}var S;(function(e){e.controlCharacterInInputStream="control-character-in-input-stream",e.noncharacterInInputStream="noncharacter-in-input-stream",e.surrogateInInputStream="surrogate-in-input-stream",e.nonVoidHtmlElementStartTagWithTrailingSolidus="non-void-html-element-start-tag-with-trailing-solidus",e.endTagWithAttributes="end-tag-with-attributes",e.endTagWithTrailingSolidus="end-tag-with-trailing-solidus",e.unexpectedSolidusInTag="unexpected-solidus-in-tag",e.unexpectedNullCharacter="unexpected-null-character",e.unexpectedQuestionMarkInsteadOfTagName="unexpected-question-mark-instead-of-tag-name",e.invalidFirstCharacterOfTagName="invalid-first-character-of-tag-name",e.unexpectedEqualsSignBeforeAttributeName="unexpected-equals-sign-before-attribute-name",e.missingEndTagName="missing-end-tag-name",e.unexpectedCharacterInAttributeName="unexpected-character-in-attribute-name",e.unknownNamedCharacterReference="unknown-named-character-reference",e.missingSemicolonAfterCharacterReference="missing-semicolon-after-character-reference",e.unexpectedCharacterAfterDoctypeSystemIdentifier="unexpected-character-after-doctype-system-identifier",e.unexpectedCharacterInUnquotedAttributeValue="unexpected-character-in-unquoted-attribute-value",e.eofBeforeTagName="eof-before-tag-name",e.eofInTag="eof-in-tag",e.missingAttributeValue="missing-attribute-value",e.missingWhitespaceBetweenAttributes="missing-whitespace-between-attributes",e.missingWhitespaceAfterDoctypePublicKeyword="missing-whitespace-after-doctype-public-keyword",e.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers="missing-whitespace-between-doctype-public-and-system-identifiers",e.missingWhitespaceAfterDoctypeSystemKeyword="missing-whitespace-after-doctype-system-keyword",e.missingQuoteBeforeDoctypePublicIdentifier="missing-quote-before-doctype-public-identifier",e.missingQuoteBeforeDoctypeSystemIdentifier="missing-quote-before-doctype-system-identifier",e.missingDoctypePublicIdentifier="missing-doctype-public-identifier",e.missingDoctypeSystemIdentifier="missing-doctype-system-identifier",e.abruptDoctypePublicIdentifier="abrupt-doctype-public-identifier",e.abruptDoctypeSystemIdentifier="abrupt-doctype-system-identifier",e.cdataInHtmlContent="cdata-in-html-content",e.incorrectlyOpenedComment="incorrectly-opened-comment",e.eofInScriptHtmlCommentLikeText="eof-in-script-html-comment-like-text",e.eofInDoctype="eof-in-doctype",e.nestedComment="nested-comment",e.abruptClosingOfEmptyComment="abrupt-closing-of-empty-comment",e.eofInComment="eof-in-comment",e.incorrectlyClosedComment="incorrectly-closed-comment",e.eofInCdata="eof-in-cdata",e.absenceOfDigitsInNumericCharacterReference="absence-of-digits-in-numeric-character-reference",e.nullCharacterReference="null-character-reference",e.surrogateCharacterReference="surrogate-character-reference",e.characterReferenceOutsideUnicodeRange="character-reference-outside-unicode-range",e.controlCharacterReference="control-character-reference",e.noncharacterCharacterReference="noncharacter-character-reference",e.missingWhitespaceBeforeDoctypeName="missing-whitespace-before-doctype-name",e.missingDoctypeName="missing-doctype-name",e.invalidCharacterSequenceAfterDoctypeName="invalid-character-sequence-after-doctype-name",e.duplicateAttribute="duplicate-attribute",e.nonConformingDoctype="non-conforming-doctype",e.missingDoctype="missing-doctype",e.misplacedDoctype="misplaced-doctype",e.endTagWithoutMatchingOpenElement="end-tag-without-matching-open-element",e.closingOfElementWithOpenChildElements="closing-of-element-with-open-child-elements",e.disallowedContentInNoscriptInHead="disallowed-content-in-noscript-in-head",e.openElementsLeftAfterEof="open-elements-left-after-eof",e.abandonedHeadElementChild="abandoned-head-element-child",e.misplacedStartTagForHeadElement="misplaced-start-tag-for-head-element",e.nestedNoscriptInHead="nested-noscript-in-head",e.eofInElementThatCanContainOnlyText="eof-in-element-that-can-contain-only-text"})(S||(S={}));const Tp=65536;class bp{constructor(t){this.handler=t,this.html="",this.pos=-1,this.lastGapPos=-2,this.gapStack=[],this.skipNextNewLine=!1,this.lastChunkWritten=!1,this.endOfChunkHit=!1,this.bufferWaterline=Tp,this.isEol=!1,this.lineStartPos=0,this.droppedBufferSize=0,this.line=1,this.lastErrOffset=-1}get col(){return this.pos-this.lineStartPos+ +(this.lastGapPos!==this.pos)}get offset(){return this.droppedBufferSize+this.pos}getError(t,n){const{line:r,col:u,offset:a}=this,i=u+n,o=a+n;return{code:t,startLine:r,endLine:r,startCol:i,endCol:i,startOffset:o,endOffset:o}}_err(t){this.handler.onParseError&&this.lastErrOffset!==this.offset&&(this.lastErrOffset=this.offset,this.handler.onParseError(this.getError(t,0)))}_addGap(){this.gapStack.push(this.lastGapPos),this.lastGapPos=this.pos}_processSurrogate(t){if(this.pos!==this.html.length-1){const n=this.html.charCodeAt(this.pos+1);if(Ep(n))return this.pos++,this._addGap(),gp(t,n)}else if(!this.lastChunkWritten)return this.endOfChunkHit=!0,p.EOF;return this._err(S.surrogateInInputStream),t}willDropParsedChunk(){return this.pos>this.bufferWaterline}dropParsedChunk(){this.willDropParsedChunk()&&(this.html=this.html.substring(this.pos),this.lineStartPos-=this.pos,this.droppedBufferSize+=this.pos,this.pos=0,this.lastGapPos=-2,this.gapStack.length=0)}write(t,n){this.html.length>0?this.html+=t:this.html=t,this.endOfChunkHit=!1,this.lastChunkWritten=n}insertHtmlAtCurrentPos(t){this.html=this.html.substring(0,this.pos+1)+t+this.html.substring(this.pos+1),this.endOfChunkHit=!1}startsWith(t,n){if(this.pos+t.length>this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,!1;if(n)return this.html.startsWith(t,this.pos);for(let r=0;r<t.length;r++)if((this.html.charCodeAt(this.pos+r)|32)!==t.charCodeAt(r))return!1;return!0}peek(t){const n=this.pos+t;if(n>=this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,p.EOF;const r=this.html.charCodeAt(n);return r===p.CARRIAGE_RETURN?p.LINE_FEED:r}advance(){if(this.pos++,this.isEol&&(this.isEol=!1,this.line++,this.lineStartPos=this.pos),this.pos>=this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,p.EOF;let t=this.html.charCodeAt(this.pos);return t===p.CARRIAGE_RETURN?(this.isEol=!0,this.skipNextNewLine=!0,p.LINE_FEED):t===p.LINE_FEED&&(this.isEol=!0,this.skipNextNewLine)?(this.line--,this.skipNextNewLine=!1,this._addGap(),this.advance()):(this.skipNextNewLine=!1,cs(t)&&(t=this._processSurrogate(t)),this.handler.onParseError===null||t>31&&t<127||t===p.LINE_FEED||t===p.CARRIAGE_RETURN||t>159&&t<64976||this._checkForProblematicCharacters(t),t)}_checkForProblematicCharacters(t){fs(t)?this._err(S.controlCharacterInInputStream):hs(t)&&this._err(S.noncharacterInInputStream)}retreat(t){for(this.pos-=t;this.pos<this.lastGapPos;)this.lastGapPos=this.gapStack.pop(),this.pos--;this.isEol=!1}}var G;(function(e){e[e.CHARACTER=0]="CHARACTER",e[e.NULL_CHARACTER=1]="NULL_CHARACTER",e[e.WHITESPACE_CHARACTER=2]="WHITESPACE_CHARACTER",e[e.START_TAG=3]="START_TAG",e[e.END_TAG=4]="END_TAG",e[e.COMMENT=5]="COMMENT",e[e.DOCTYPE=6]="DOCTYPE",e[e.EOF=7]="EOF",e[e.HIBERNATION=8]="HIBERNATION"})(G||(G={}));function ds(e,t){for(let n=e.attrs.length-1;n>=0;n--)if(e.attrs[n].name===t)return e.attrs[n].value;return null}const Ap=new Uint16Array('ᵁ<Õıʊҝջאٵ۞ޢߖࠏ੊ઑඡ๭༉༦჊ረዡᐕᒝᓃᓟᔥ\0\0\0\0\0\0ᕫᛍᦍᰒᷝ὾⁠↰⊍⏀⏻⑂⠤⤒ⴈ⹈⿎〖㊺㘹㞬㣾㨨㩱㫠㬮ࠀEMabcfglmnoprstu\\bfms¦³¹ÈÏlig耻Æ䃆P耻&䀦cute耻Á䃁reve;䄂Āiyx}rc耻Â䃂;䐐r;쀀𝔄rave耻À䃀pha;䎑acr;䄀d;橓Āgp¡on;䄄f;쀀𝔸plyFunction;恡ing耻Å䃅Ācs¾Ãr;쀀𝒜ign;扔ilde耻Ã䃃ml耻Ä䃄ЀaceforsuåûþėĜĢħĪĀcrêòkslash;或Ŷöø;櫧ed;挆y;䐑ƀcrtąċĔause;戵noullis;愬a;䎒r;쀀𝔅pf;쀀𝔹eve;䋘còēmpeq;扎܀HOacdefhilorsuōőŖƀƞƢƵƷƺǜȕɳɸɾcy;䐧PY耻©䂩ƀcpyŝŢźute;䄆Ā;iŧŨ拒talDifferentialD;慅leys;愭ȀaeioƉƎƔƘron;䄌dil耻Ç䃇rc;䄈nint;戰ot;䄊ĀdnƧƭilla;䂸terDot;䂷òſi;䎧rcleȀDMPTǇǋǑǖot;抙inus;抖lus;投imes;抗oĀcsǢǸkwiseContourIntegral;戲eCurlyĀDQȃȏoubleQuote;思uote;怙ȀlnpuȞȨɇɕonĀ;eȥȦ户;橴ƀgitȯȶȺruent;扡nt;戯ourIntegral;戮ĀfrɌɎ;愂oduct;成nterClockwiseContourIntegral;戳oss;樯cr;쀀𝒞pĀ;Cʄʅ拓ap;才րDJSZacefiosʠʬʰʴʸˋ˗ˡ˦̳ҍĀ;oŹʥtrahd;椑cy;䐂cy;䐅cy;䐏ƀgrsʿ˄ˇger;怡r;憡hv;櫤Āayː˕ron;䄎;䐔lĀ;t˝˞戇a;䎔r;쀀𝔇Āaf˫̧Ācm˰̢riticalȀADGT̖̜̀̆cute;䂴oŴ̋̍;䋙bleAcute;䋝rave;䁠ilde;䋜ond;拄ferentialD;慆Ѱ̽\0\0\0͔͂\0Ѕf;쀀𝔻ƀ;DE͈͉͍䂨ot;惜qual;扐blèCDLRUVͣͲ΂ϏϢϸontourIntegraìȹoɴ͹\0\0ͻ»͉nArrow;懓Āeo·ΤftƀARTΐΖΡrrow;懐ightArrow;懔eåˊngĀLRΫτeftĀARγιrrow;柸ightArrow;柺ightArrow;柹ightĀATϘϞrrow;懒ee;抨pɁϩ\0\0ϯrrow;懑ownArrow;懕erticalBar;戥ǹABLRTaВЪаўѿͼrrowƀ;BUНОТ憓ar;椓pArrow;懵reve;䌑eft˒к\0ц\0ѐightVector;楐eeVector;楞ectorĀ;Bљњ憽ar;楖ightǔѧ\0ѱeeVector;楟ectorĀ;BѺѻ懁ar;楗eeĀ;A҆҇护rrow;憧ĀctҒҗr;쀀𝒟rok;䄐ࠀNTacdfglmopqstuxҽӀӄӋӞӢӧӮӵԡԯԶՒ՝ՠեG;䅊H耻Ð䃐cute耻É䃉ƀaiyӒӗӜron;䄚rc耻Ê䃊;䐭ot;䄖r;쀀𝔈rave耻È䃈ement;戈ĀapӺӾcr;䄒tyɓԆ\0\0ԒmallSquare;旻erySmallSquare;斫ĀgpԦԪon;䄘f;쀀𝔼silon;䎕uĀaiԼՉlĀ;TՂՃ橵ilde;扂librium;懌Āci՗՚r;愰m;橳a;䎗ml耻Ë䃋Āipժկsts;戃onentialE;慇ʀcfiosօֈ֍ֲ׌y;䐤r;쀀𝔉lledɓ֗\0\0֣mallSquare;旼erySmallSquare;斪Ͱֺ\0ֿ\0\0ׄf;쀀𝔽All;戀riertrf;愱cò׋؀JTabcdfgorstר׬ׯ׺؀ؒؖ؛؝أ٬ٲcy;䐃耻>䀾mmaĀ;d׷׸䎓;䏜reve;䄞ƀeiy؇،ؐdil;䄢rc;䄜;䐓ot;䄠r;쀀𝔊;拙pf;쀀𝔾eater̀EFGLSTصلَٖٛ٦qualĀ;Lؾؿ扥ess;招ullEqual;执reater;檢ess;扷lantEqual;橾ilde;扳cr;쀀𝒢;扫ЀAacfiosuڅڋږڛڞڪھۊRDcy;䐪Āctڐڔek;䋇;䁞irc;䄤r;愌lbertSpace;愋ǰگ\0ڲf;愍izontalLine;攀Āctۃۅòکrok;䄦mpńېۘownHumðįqual;扏܀EJOacdfgmnostuۺ۾܃܇܎ܚܞܡܨ݄ݸދޏޕcy;䐕lig;䄲cy;䐁cute耻Í䃍Āiyܓܘrc耻Î䃎;䐘ot;䄰r;愑rave耻Ì䃌ƀ;apܠܯܿĀcgܴܷr;䄪inaryI;慈lieóϝǴ݉\0ݢĀ;eݍݎ戬Āgrݓݘral;戫section;拂isibleĀCTݬݲomma;恣imes;恢ƀgptݿރވon;䄮f;쀀𝕀a;䎙cr;愐ilde;䄨ǫޚ\0ޞcy;䐆l耻Ï䃏ʀcfosuެ޷޼߂ߐĀiyޱ޵rc;䄴;䐙r;쀀𝔍pf;쀀𝕁ǣ߇\0ߌr;쀀𝒥rcy;䐈kcy;䐄΀HJacfosߤߨ߽߬߱ࠂࠈcy;䐥cy;䐌ppa;䎚Āey߶߻dil;䄶;䐚r;쀀𝔎pf;쀀𝕂cr;쀀𝒦րJTaceflmostࠥࠩࠬࡐࡣ঳সে্਷ੇcy;䐉耻<䀼ʀcmnpr࠷࠼ࡁࡄࡍute;䄹bda;䎛g;柪lacetrf;愒r;憞ƀaeyࡗ࡜ࡡron;䄽dil;䄻;䐛Āfsࡨ॰tԀACDFRTUVarࡾࢩࢱࣦ࣠ࣼयज़ΐ४Ānrࢃ࢏gleBracket;柨rowƀ;BR࢙࢚࢞憐ar;懤ightArrow;懆eiling;挈oǵࢷ\0ࣃbleBracket;柦nǔࣈ\0࣒eeVector;楡ectorĀ;Bࣛࣜ懃ar;楙loor;挊ightĀAV࣯ࣵrrow;憔ector;楎Āerँगeƀ;AVउऊऐ抣rrow;憤ector;楚iangleƀ;BEतथऩ抲ar;槏qual;抴pƀDTVषूौownVector;楑eeVector;楠ectorĀ;Bॖॗ憿ar;楘ectorĀ;B॥०憼ar;楒ightáΜs̀EFGLSTॾঋকঝঢভqualGreater;拚ullEqual;扦reater;扶ess;檡lantEqual;橽ilde;扲r;쀀𝔏Ā;eঽা拘ftarrow;懚idot;䄿ƀnpw৔ਖਛgȀLRlr৞৷ਂਐeftĀAR০৬rrow;柵ightArrow;柷ightArrow;柶eftĀarγਊightáοightáϊf;쀀𝕃erĀLRਢਬeftArrow;憙ightArrow;憘ƀchtਾੀੂòࡌ;憰rok;䅁;扪Ѐacefiosuਗ਼੝੠੷੼અઋ઎p;椅y;䐜Ādl੥੯iumSpace;恟lintrf;愳r;쀀𝔐nusPlus;戓pf;쀀𝕄cò੶;䎜ҀJacefostuણધભીଔଙඑ඗ඞcy;䐊cute;䅃ƀaey઴હાron;䅇dil;䅅;䐝ƀgswે૰଎ativeƀMTV૓૟૨ediumSpace;怋hiĀcn૦૘ë૙eryThiî૙tedĀGL૸ଆreaterGreateòٳessLesóੈLine;䀊r;쀀𝔑ȀBnptଢନଷ଺reak;恠BreakingSpace;䂠f;愕ڀ;CDEGHLNPRSTV୕ୖ୪୼஡௫ఄ౞಄ದ೘ൡඅ櫬Āou୛୤ngruent;扢pCap;扭oubleVerticalBar;戦ƀlqxஃஊ஛ement;戉ualĀ;Tஒஓ扠ilde;쀀≂̸ists;戄reater΀;EFGLSTஶஷ஽௉௓௘௥扯qual;扱ullEqual;쀀≧̸reater;쀀≫̸ess;批lantEqual;쀀⩾̸ilde;扵umpń௲௽ownHump;쀀≎̸qual;쀀≏̸eĀfsఊధtTriangleƀ;BEచఛడ拪ar;쀀⧏̸qual;括s̀;EGLSTవశ఼ౄోౘ扮qual;扰reater;扸ess;쀀≪̸lantEqual;쀀⩽̸ilde;扴estedĀGL౨౹reaterGreater;쀀⪢̸essLess;쀀⪡̸recedesƀ;ESಒಓಛ技qual;쀀⪯̸lantEqual;拠ĀeiಫಹverseElement;戌ghtTriangleƀ;BEೋೌ೒拫ar;쀀⧐̸qual;拭ĀquೝഌuareSuĀbp೨೹setĀ;E೰ೳ쀀⊏̸qual;拢ersetĀ;Eഃആ쀀⊐̸qual;拣ƀbcpഓതൎsetĀ;Eഛഞ쀀⊂⃒qual;抈ceedsȀ;ESTലള഻െ抁qual;쀀⪰̸lantEqual;拡ilde;쀀≿̸ersetĀ;E൘൛쀀⊃⃒qual;抉ildeȀ;EFT൮൯൵ൿ扁qual;扄ullEqual;扇ilde;扉erticalBar;戤cr;쀀𝒩ilde耻Ñ䃑;䎝܀Eacdfgmoprstuvලෂ෉෕ෛ෠෧෼ขภยา฿ไlig;䅒cute耻Ó䃓Āiy෎ීrc耻Ô䃔;䐞blac;䅐r;쀀𝔒rave耻Ò䃒ƀaei෮ෲ෶cr;䅌ga;䎩cron;䎟pf;쀀𝕆enCurlyĀDQฎบoubleQuote;怜uote;怘;橔Āclวฬr;쀀𝒪ash耻Ø䃘iŬื฼de耻Õ䃕es;樷ml耻Ö䃖erĀBP๋๠Āar๐๓r;怾acĀek๚๜;揞et;掴arenthesis;揜Ҁacfhilors๿ງຊຏຒດຝະ໼rtialD;戂y;䐟r;쀀𝔓i;䎦;䎠usMinus;䂱Āipຢອncareplanåڝf;愙Ȁ;eio຺ູ໠໤檻cedesȀ;EST່້໏໚扺qual;檯lantEqual;扼ilde;找me;怳Ādp໩໮uct;戏ortionĀ;aȥ໹l;戝Āci༁༆r;쀀𝒫;䎨ȀUfos༑༖༛༟OT耻"䀢r;쀀𝔔pf;愚cr;쀀𝒬؀BEacefhiorsu༾གྷཇའཱིྦྷྪྭ႖ႩႴႾarr;椐G耻®䂮ƀcnrཎནབute;䅔g;柫rĀ;tཛྷཝ憠l;椖ƀaeyཧཬཱron;䅘dil;䅖;䐠Ā;vླྀཹ愜erseĀEUྂྙĀlq྇ྎement;戋uilibrium;懋pEquilibrium;楯r»ཹo;䎡ghtЀACDFTUVa࿁࿫࿳ဢဨၛႇϘĀnr࿆࿒gleBracket;柩rowƀ;BL࿜࿝࿡憒ar;懥eftArrow;懄eiling;按oǵ࿹\0စbleBracket;柧nǔည\0နeeVector;楝ectorĀ;Bဝသ懂ar;楕loor;挋Āerိ၃eƀ;AVဵံြ抢rrow;憦ector;楛iangleƀ;BEၐၑၕ抳ar;槐qual;抵pƀDTVၣၮၸownVector;楏eeVector;楜ectorĀ;Bႂႃ憾ar;楔ectorĀ;B႑႒懀ar;楓Āpuႛ႞f;愝ndImplies;楰ightarrow;懛ĀchႹႼr;愛;憱leDelayed;槴ڀHOacfhimoqstuფჱჷჽᄙᄞᅑᅖᅡᅧᆵᆻᆿĀCcჩხHcy;䐩y;䐨FTcy;䐬cute;䅚ʀ;aeiyᄈᄉᄎᄓᄗ檼ron;䅠dil;䅞rc;䅜;䐡r;쀀𝔖ortȀDLRUᄪᄴᄾᅉownArrow»ОeftArrow»࢚ightArrow»࿝pArrow;憑gma;䎣allCircle;战pf;쀀𝕊ɲᅭ\0\0ᅰt;戚areȀ;ISUᅻᅼᆉᆯ斡ntersection;抓uĀbpᆏᆞsetĀ;Eᆗᆘ抏qual;抑ersetĀ;Eᆨᆩ抐qual;抒nion;抔cr;쀀𝒮ar;拆ȀbcmpᇈᇛሉላĀ;sᇍᇎ拐etĀ;Eᇍᇕqual;抆ĀchᇠህeedsȀ;ESTᇭᇮᇴᇿ扻qual;檰lantEqual;扽ilde;承Tháྌ;我ƀ;esሒሓሣ拑rsetĀ;Eሜም抃qual;抇et»ሓրHRSacfhiorsሾቄ቉ቕ቞ቱቶኟዂወዑORN耻Þ䃞ADE;愢ĀHc቎ቒcy;䐋y;䐦Ābuቚቜ;䀉;䎤ƀaeyብቪቯron;䅤dil;䅢;䐢r;쀀𝔗Āeiቻ኉ǲኀ\0ኇefore;戴a;䎘Ācn኎ኘkSpace;쀀  Space;怉ldeȀ;EFTካኬኲኼ戼qual;扃ullEqual;扅ilde;扈pf;쀀𝕋ipleDot;惛Āctዖዛr;쀀𝒯rok;䅦ૡዷጎጚጦ\0ጬጱ\0\0\0\0\0ጸጽ፷ᎅ\0᏿ᐄᐊᐐĀcrዻጁute耻Ú䃚rĀ;oጇገ憟cir;楉rǣጓ\0጖y;䐎ve;䅬Āiyጞጣrc耻Û䃛;䐣blac;䅰r;쀀𝔘rave耻Ù䃙acr;䅪Ādiፁ፩erĀBPፈ፝Āarፍፐr;䁟acĀekፗፙ;揟et;掵arenthesis;揝onĀ;P፰፱拃lus;抎Āgp፻፿on;䅲f;쀀𝕌ЀADETadps᎕ᎮᎸᏄϨᏒᏗᏳrrowƀ;BDᅐᎠᎤar;椒ownArrow;懅ownArrow;憕quilibrium;楮eeĀ;AᏋᏌ报rrow;憥ownáϳerĀLRᏞᏨeftArrow;憖ightArrow;憗iĀ;lᏹᏺ䏒on;䎥ing;䅮cr;쀀𝒰ilde;䅨ml耻Ü䃜ҀDbcdefosvᐧᐬᐰᐳᐾᒅᒊᒐᒖash;披ar;櫫y;䐒ashĀ;lᐻᐼ抩;櫦Āerᑃᑅ;拁ƀbtyᑌᑐᑺar;怖Ā;iᑏᑕcalȀBLSTᑡᑥᑪᑴar;戣ine;䁼eparator;杘ilde;所ThinSpace;怊r;쀀𝔙pf;쀀𝕍cr;쀀𝒱dash;抪ʀcefosᒧᒬᒱᒶᒼirc;䅴dge;拀r;쀀𝔚pf;쀀𝕎cr;쀀𝒲Ȁfiosᓋᓐᓒᓘr;쀀𝔛;䎞pf;쀀𝕏cr;쀀𝒳ҀAIUacfosuᓱᓵᓹᓽᔄᔏᔔᔚᔠcy;䐯cy;䐇cy;䐮cute耻Ý䃝Āiyᔉᔍrc;䅶;䐫r;쀀𝔜pf;쀀𝕐cr;쀀𝒴ml;䅸ЀHacdefosᔵᔹᔿᕋᕏᕝᕠᕤcy;䐖cute;䅹Āayᕄᕉron;䅽;䐗ot;䅻ǲᕔ\0ᕛoWidtè૙a;䎖r;愨pf;愤cr;쀀𝒵௡ᖃᖊᖐ\0ᖰᖶᖿ\0\0\0\0ᗆᗛᗫᙟ᙭\0ᚕ᚛ᚲᚹ\0ᚾcute耻á䃡reve;䄃̀;Ediuyᖜᖝᖡᖣᖨᖭ戾;쀀∾̳;房rc耻â䃢te肻´̆;䐰lig耻æ䃦Ā;r²ᖺ;쀀𝔞rave耻à䃠ĀepᗊᗖĀfpᗏᗔsym;愵èᗓha;䎱ĀapᗟcĀclᗤᗧr;䄁g;樿ɤᗰ\0\0ᘊʀ;adsvᗺᗻᗿᘁᘇ戧nd;橕;橜lope;橘;橚΀;elmrszᘘᘙᘛᘞᘿᙏᙙ戠;榤e»ᘙsdĀ;aᘥᘦ戡ѡᘰᘲᘴᘶᘸᘺᘼᘾ;榨;榩;榪;榫;榬;榭;榮;榯tĀ;vᙅᙆ戟bĀ;dᙌᙍ抾;榝Āptᙔᙗh;戢»¹arr;捼Āgpᙣᙧon;䄅f;쀀𝕒΀;Eaeiop዁ᙻᙽᚂᚄᚇᚊ;橰cir;橯;扊d;手s;䀧roxĀ;e዁ᚒñᚃing耻å䃥ƀctyᚡᚦᚨr;쀀𝒶;䀪mpĀ;e዁ᚯñʈilde耻ã䃣ml耻ä䃤Āciᛂᛈoninôɲnt;樑ࠀNabcdefiklnoprsu᛭ᛱᜰ᜼ᝃᝈ᝸᝽០៦ᠹᡐᜍ᤽᥈ᥰot;櫭Ācrᛶ᜞kȀcepsᜀᜅᜍᜓong;扌psilon;䏶rime;怵imĀ;e᜚᜛戽q;拍Ŷᜢᜦee;抽edĀ;gᜬᜭ挅e»ᜭrkĀ;t፜᜷brk;掶Āoyᜁᝁ;䐱quo;怞ʀcmprtᝓ᝛ᝡᝤᝨausĀ;eĊĉptyv;榰séᜌnoõēƀahwᝯ᝱ᝳ;䎲;愶een;扬r;쀀𝔟g΀costuvwឍឝឳេ៕៛៞ƀaiuបពរðݠrc;旯p»፱ƀdptឤឨឭot;樀lus;樁imes;樂ɱឹ\0\0ើcup;樆ar;昅riangleĀdu៍្own;施p;斳plus;樄eåᑄåᒭarow;植ƀako៭ᠦᠵĀcn៲ᠣkƀlst៺֫᠂ozenge;槫riangleȀ;dlr᠒᠓᠘᠝斴own;斾eft;旂ight;斸k;搣Ʊᠫ\0ᠳƲᠯ\0ᠱ;斒;斑4;斓ck;斈ĀeoᠾᡍĀ;qᡃᡆ쀀=⃥uiv;쀀≡⃥t;挐Ȁptwxᡙᡞᡧᡬf;쀀𝕓Ā;tᏋᡣom»Ꮜtie;拈؀DHUVbdhmptuvᢅᢖᢪᢻᣗᣛᣬ᣿ᤅᤊᤐᤡȀLRlrᢎᢐᢒᢔ;敗;敔;敖;敓ʀ;DUduᢡᢢᢤᢦᢨ敐;敦;敩;敤;敧ȀLRlrᢳᢵᢷᢹ;敝;敚;敜;教΀;HLRhlrᣊᣋᣍᣏᣑᣓᣕ救;敬;散;敠;敫;敢;敟ox;槉ȀLRlrᣤᣦᣨᣪ;敕;敒;攐;攌ʀ;DUduڽ᣷᣹᣻᣽;敥;敨;攬;攴inus;抟lus;択imes;抠ȀLRlrᤙᤛᤝ᤟;敛;敘;攘;攔΀;HLRhlrᤰᤱᤳᤵᤷ᤻᤹攂;敪;敡;敞;攼;攤;攜Āevģ᥂bar耻¦䂦Ȁceioᥑᥖᥚᥠr;쀀𝒷mi;恏mĀ;e᜚᜜lƀ;bhᥨᥩᥫ䁜;槅sub;柈Ŭᥴ᥾lĀ;e᥹᥺怢t»᥺pƀ;Eeįᦅᦇ;檮Ā;qۜۛೡᦧ\0᧨ᨑᨕᨲ\0ᨷᩐ\0\0᪴\0\0᫁\0\0ᬡᬮ᭍᭒\0᯽\0ᰌƀcpr᦭ᦲ᧝ute;䄇̀;abcdsᦿᧀᧄ᧊᧕᧙戩nd;橄rcup;橉Āau᧏᧒p;橋p;橇ot;橀;쀀∩︀Āeo᧢᧥t;恁îړȀaeiu᧰᧻ᨁᨅǰ᧵\0᧸s;橍on;䄍dil耻ç䃧rc;䄉psĀ;sᨌᨍ橌m;橐ot;䄋ƀdmnᨛᨠᨦil肻¸ƭptyv;榲t脀¢;eᨭᨮ䂢räƲr;쀀𝔠ƀceiᨽᩀᩍy;䑇ckĀ;mᩇᩈ朓ark»ᩈ;䏇r΀;Ecefms᩟᩠ᩢᩫ᪤᪪᪮旋;槃ƀ;elᩩᩪᩭ䋆q;扗eɡᩴ\0\0᪈rrowĀlr᩼᪁eft;憺ight;憻ʀRSacd᪒᪔᪖᪚᪟»ཇ;擈st;抛irc;抚ash;抝nint;樐id;櫯cir;槂ubsĀ;u᪻᪼晣it»᪼ˬ᫇᫔᫺\0ᬊonĀ;eᫍᫎ䀺Ā;qÇÆɭ᫙\0\0᫢aĀ;t᫞᫟䀬;䁀ƀ;fl᫨᫩᫫戁îᅠeĀmx᫱᫶ent»᫩eóɍǧ᫾\0ᬇĀ;dኻᬂot;橭nôɆƀfryᬐᬔᬗ;쀀𝕔oäɔ脀©;sŕᬝr;愗Āaoᬥᬩrr;憵ss;朗Ācuᬲᬷr;쀀𝒸Ābpᬼ᭄Ā;eᭁᭂ櫏;櫑Ā;eᭉᭊ櫐;櫒dot;拯΀delprvw᭠᭬᭷ᮂᮬᯔ᯹arrĀlr᭨᭪;椸;椵ɰ᭲\0\0᭵r;拞c;拟arrĀ;p᭿ᮀ憶;椽̀;bcdosᮏᮐᮖᮡᮥᮨ截rcap;橈Āauᮛᮞp;橆p;橊ot;抍r;橅;쀀∪︀Ȁalrv᮵ᮿᯞᯣrrĀ;mᮼᮽ憷;椼yƀevwᯇᯔᯘqɰᯎ\0\0ᯒreã᭳uã᭵ee;拎edge;拏en耻¤䂤earrowĀlrᯮ᯳eft»ᮀight»ᮽeäᯝĀciᰁᰇoninôǷnt;戱lcty;挭ঀAHabcdefhijlorstuwz᰸᰻᰿ᱝᱩᱵᲊᲞᲬᲷ᳻᳿ᴍᵻᶑᶫᶻ᷆᷍rò΁ar;楥Ȁglrs᱈ᱍ᱒᱔ger;怠eth;愸òᄳhĀ;vᱚᱛ怐»ऊūᱡᱧarow;椏aã̕Āayᱮᱳron;䄏;䐴ƀ;ao̲ᱼᲄĀgrʿᲁr;懊tseq;橷ƀglmᲑᲔᲘ耻°䂰ta;䎴ptyv;榱ĀirᲣᲨsht;楿;쀀𝔡arĀlrᲳᲵ»ࣜ»သʀaegsv᳂͸᳖᳜᳠mƀ;oș᳊᳔ndĀ;ș᳑uit;晦amma;䏝in;拲ƀ;io᳧᳨᳸䃷de脀÷;o᳧ᳰntimes;拇nø᳷cy;䑒cɯᴆ\0\0ᴊrn;挞op;挍ʀlptuwᴘᴝᴢᵉᵕlar;䀤f;쀀𝕕ʀ;emps̋ᴭᴷᴽᵂqĀ;d͒ᴳot;扑inus;戸lus;戔quare;抡blebarwedgåúnƀadhᄮᵝᵧownarrowóᲃarpoonĀlrᵲᵶefôᲴighôᲶŢᵿᶅkaro÷གɯᶊ\0\0ᶎrn;挟op;挌ƀcotᶘᶣᶦĀryᶝᶡ;쀀𝒹;䑕l;槶rok;䄑Ādrᶰᶴot;拱iĀ;fᶺ᠖斿Āah᷀᷃ròЩaòྦangle;榦Āci᷒ᷕy;䑟grarr;柿ऀDacdefglmnopqrstuxḁḉḙḸոḼṉṡṾấắẽỡἪἷὄ὎὚ĀDoḆᴴoôᲉĀcsḎḔute耻é䃩ter;橮ȀaioyḢḧḱḶron;䄛rĀ;cḭḮ扖耻ê䃪lon;払;䑍ot;䄗ĀDrṁṅot;扒;쀀𝔢ƀ;rsṐṑṗ檚ave耻è䃨Ā;dṜṝ檖ot;檘Ȁ;ilsṪṫṲṴ檙nters;揧;愓Ā;dṹṺ檕ot;檗ƀapsẅẉẗcr;䄓tyƀ;svẒẓẕ戅et»ẓpĀ1;ẝẤĳạả;怄;怅怃ĀgsẪẬ;䅋p;怂ĀgpẴẸon;䄙f;쀀𝕖ƀalsỄỎỒrĀ;sỊị拕l;槣us;橱iƀ;lvỚớở䎵on»ớ;䏵ȀcsuvỪỳἋἣĀioữḱrc»Ḯɩỹ\0\0ỻíՈantĀglἂἆtr»ṝess»Ṻƀaeiἒ἖Ἒls;䀽st;扟vĀ;DȵἠD;橸parsl;槥ĀDaἯἳot;打rr;楱ƀcdiἾὁỸr;愯oô͒ĀahὉὋ;䎷耻ð䃰Āmrὓὗl耻ë䃫o;悬ƀcipὡὤὧl;䀡sôծĀeoὬὴctatioîՙnentialåչৡᾒ\0ᾞ\0ᾡᾧ\0\0ῆῌ\0ΐ\0ῦῪ \0 ⁚llingdotseñṄy;䑄male;晀ƀilrᾭᾳ῁lig;耀ﬃɩᾹ\0\0᾽g;耀ﬀig;耀ﬄ;쀀𝔣lig;耀ﬁlig;쀀fjƀaltῙ῜ῡt;晭ig;耀ﬂns;斱of;䆒ǰ΅\0ῳf;쀀𝕗ĀakֿῷĀ;vῼ´拔;櫙artint;樍Āao‌⁕Ācs‑⁒α‚‰‸⁅⁈\0⁐β•‥‧‪‬\0‮耻½䂽;慓耻¼䂼;慕;慙;慛Ƴ‴\0‶;慔;慖ʴ‾⁁\0\0⁃耻¾䂾;慗;慜5;慘ƶ⁌\0⁎;慚;慝8;慞l;恄wn;挢cr;쀀𝒻ࢀEabcdefgijlnorstv₂₉₟₥₰₴⃰⃵⃺⃿℃ℒℸ̗ℾ⅒↞Ā;lٍ₇;檌ƀcmpₐₕ₝ute;䇵maĀ;dₜ᳚䎳;檆reve;䄟Āiy₪₮rc;䄝;䐳ot;䄡Ȁ;lqsؾق₽⃉ƀ;qsؾٌ⃄lanô٥Ȁ;cdl٥⃒⃥⃕c;檩otĀ;o⃜⃝檀Ā;l⃢⃣檂;檄Ā;e⃪⃭쀀⋛︀s;檔r;쀀𝔤Ā;gٳ؛mel;愷cy;䑓Ȁ;Eajٚℌℎℐ;檒;檥;檤ȀEaesℛℝ℩ℴ;扩pĀ;p℣ℤ檊rox»ℤĀ;q℮ℯ檈Ā;q℮ℛim;拧pf;쀀𝕘Āci⅃ⅆr;愊mƀ;el٫ⅎ⅐;檎;檐茀>;cdlqr׮ⅠⅪⅮⅳⅹĀciⅥⅧ;檧r;橺ot;拗Par;榕uest;橼ʀadelsↄⅪ←ٖ↛ǰ↉\0↎proø₞r;楸qĀlqؿ↖lesó₈ií٫Āen↣↭rtneqq;쀀≩︀Å↪ԀAabcefkosy⇄⇇⇱⇵⇺∘∝∯≨≽ròΠȀilmr⇐⇔⇗⇛rsðᒄf»․ilôکĀdr⇠⇤cy;䑊ƀ;cwࣴ⇫⇯ir;楈;憭ar;意irc;䄥ƀalr∁∎∓rtsĀ;u∉∊晥it»∊lip;怦con;抹r;쀀𝔥sĀew∣∩arow;椥arow;椦ʀamopr∺∾≃≞≣rr;懿tht;戻kĀlr≉≓eftarrow;憩ightarrow;憪f;쀀𝕙bar;怕ƀclt≯≴≸r;쀀𝒽asè⇴rok;䄧Ābp⊂⊇ull;恃hen»ᱛૡ⊣\0⊪\0⊸⋅⋎\0⋕⋳\0\0⋸⌢⍧⍢⍿\0⎆⎪⎴cute耻í䃭ƀ;iyݱ⊰⊵rc耻î䃮;䐸Ācx⊼⊿y;䐵cl耻¡䂡ĀfrΟ⋉;쀀𝔦rave耻ì䃬Ȁ;inoܾ⋝⋩⋮Āin⋢⋦nt;樌t;戭fin;槜ta;愩lig;䄳ƀaop⋾⌚⌝ƀcgt⌅⌈⌗r;䄫ƀelpܟ⌏⌓inåގarôܠh;䄱f;抷ed;䆵ʀ;cfotӴ⌬⌱⌽⍁are;愅inĀ;t⌸⌹戞ie;槝doô⌙ʀ;celpݗ⍌⍐⍛⍡al;抺Āgr⍕⍙eróᕣã⍍arhk;樗rod;樼Ȁcgpt⍯⍲⍶⍻y;䑑on;䄯f;쀀𝕚a;䎹uest耻¿䂿Āci⎊⎏r;쀀𝒾nʀ;EdsvӴ⎛⎝⎡ӳ;拹ot;拵Ā;v⎦⎧拴;拳Ā;iݷ⎮lde;䄩ǫ⎸\0⎼cy;䑖l耻ï䃯̀cfmosu⏌⏗⏜⏡⏧⏵Āiy⏑⏕rc;䄵;䐹r;쀀𝔧ath;䈷pf;쀀𝕛ǣ⏬\0⏱r;쀀𝒿rcy;䑘kcy;䑔Ѐacfghjos␋␖␢␧␭␱␵␻ppaĀ;v␓␔䎺;䏰Āey␛␠dil;䄷;䐺r;쀀𝔨reen;䄸cy;䑅cy;䑜pf;쀀𝕜cr;쀀𝓀஀ABEHabcdefghjlmnoprstuv⑰⒁⒆⒍⒑┎┽╚▀♎♞♥♹♽⚚⚲⛘❝❨➋⟀⠁⠒ƀart⑷⑺⑼rò৆òΕail;椛arr;椎Ā;gঔ⒋;檋ar;楢ॣ⒥\0⒪\0⒱\0\0\0\0\0⒵Ⓔ\0ⓆⓈⓍ\0⓹ute;䄺mptyv;榴raîࡌbda;䎻gƀ;dlࢎⓁⓃ;榑åࢎ;檅uo耻«䂫rЀ;bfhlpst࢙ⓞⓦⓩ⓫⓮⓱⓵Ā;f࢝ⓣs;椟s;椝ë≒p;憫l;椹im;楳l;憢ƀ;ae⓿─┄檫il;椙Ā;s┉┊檭;쀀⪭︀ƀabr┕┙┝rr;椌rk;杲Āak┢┬cĀek┨┪;䁻;䁛Āes┱┳;榋lĀdu┹┻;榏;榍Ȁaeuy╆╋╖╘ron;䄾Ādi═╔il;䄼ìࢰâ┩;䐻Ȁcqrs╣╦╭╽a;椶uoĀ;rนᝆĀdu╲╷har;楧shar;楋h;憲ʀ;fgqs▋▌উ◳◿扤tʀahlrt▘▤▷◂◨rrowĀ;t࢙□aé⓶arpoonĀdu▯▴own»њp»०eftarrows;懇ightƀahs◍◖◞rrowĀ;sࣴࢧarpoonó྘quigarro÷⇰hreetimes;拋ƀ;qs▋ও◺lanôবʀ;cdgsব☊☍☝☨c;檨otĀ;o☔☕橿Ā;r☚☛檁;檃Ā;e☢☥쀀⋚︀s;檓ʀadegs☳☹☽♉♋pproøⓆot;拖qĀgq♃♅ôউgtò⒌ôছiíলƀilr♕࣡♚sht;楼;쀀𝔩Ā;Eজ♣;檑š♩♶rĀdu▲♮Ā;l॥♳;楪lk;斄cy;䑙ʀ;achtੈ⚈⚋⚑⚖rò◁orneòᴈard;楫ri;旺Āio⚟⚤dot;䅀ustĀ;a⚬⚭掰che»⚭ȀEaes⚻⚽⛉⛔;扨pĀ;p⛃⛄檉rox»⛄Ā;q⛎⛏檇Ā;q⛎⚻im;拦Ѐabnoptwz⛩⛴⛷✚✯❁❇❐Ānr⛮⛱g;柬r;懽rëࣁgƀlmr⛿✍✔eftĀar০✇ightá৲apsto;柼ightá৽parrowĀlr✥✩efô⓭ight;憬ƀafl✶✹✽r;榅;쀀𝕝us;樭imes;樴š❋❏st;戗áፎƀ;ef❗❘᠀旊nge»❘arĀ;l❤❥䀨t;榓ʀachmt❳❶❼➅➇ròࢨorneòᶌarĀ;d྘➃;業;怎ri;抿̀achiqt➘➝ੀ➢➮➻quo;怹r;쀀𝓁mƀ;egল➪➬;檍;檏Ābu┪➳oĀ;rฟ➹;怚rok;䅂萀<;cdhilqrࠫ⟒☹⟜⟠⟥⟪⟰Āci⟗⟙;檦r;橹reå◲mes;拉arr;楶uest;橻ĀPi⟵⟹ar;榖ƀ;ef⠀भ᠛旃rĀdu⠇⠍shar;楊har;楦Āen⠗⠡rtneqq;쀀≨︀Å⠞܀Dacdefhilnopsu⡀⡅⢂⢎⢓⢠⢥⢨⣚⣢⣤ઃ⣳⤂Dot;戺Ȁclpr⡎⡒⡣⡽r耻¯䂯Āet⡗⡙;時Ā;e⡞⡟朠se»⡟Ā;sျ⡨toȀ;dluျ⡳⡷⡻owîҌefôएðᏑker;斮Āoy⢇⢌mma;権;䐼ash;怔asuredangle»ᘦr;쀀𝔪o;愧ƀcdn⢯⢴⣉ro耻µ䂵Ȁ;acdᑤ⢽⣀⣄sôᚧir;櫰ot肻·Ƶusƀ;bd⣒ᤃ⣓戒Ā;uᴼ⣘;横ţ⣞⣡p;櫛ò−ðઁĀdp⣩⣮els;抧f;쀀𝕞Āct⣸⣽r;쀀𝓂pos»ᖝƀ;lm⤉⤊⤍䎼timap;抸ఀGLRVabcdefghijlmoprstuvw⥂⥓⥾⦉⦘⧚⧩⨕⨚⩘⩝⪃⪕⪤⪨⬄⬇⭄⭿⮮ⰴⱧⱼ⳩Āgt⥇⥋;쀀⋙̸Ā;v⥐௏쀀≫⃒ƀelt⥚⥲⥶ftĀar⥡⥧rrow;懍ightarrow;懎;쀀⋘̸Ā;v⥻ే쀀≪⃒ightarrow;懏ĀDd⦎⦓ash;抯ash;抮ʀbcnpt⦣⦧⦬⦱⧌la»˞ute;䅄g;쀀∠⃒ʀ;Eiop඄⦼⧀⧅⧈;쀀⩰̸d;쀀≋̸s;䅉roø඄urĀ;a⧓⧔普lĀ;s⧓ସǳ⧟\0⧣p肻 ଷmpĀ;e௹ఀʀaeouy⧴⧾⨃⨐⨓ǰ⧹\0⧻;橃on;䅈dil;䅆ngĀ;dൾ⨊ot;쀀⩭̸p;橂;䐽ash;怓΀;Aadqsxஒ⨩⨭⨻⩁⩅⩐rr;懗rĀhr⨳⨶k;椤Ā;oᏲᏰot;쀀≐̸uiöୣĀei⩊⩎ar;椨í஘istĀ;s஠டr;쀀𝔫ȀEest௅⩦⩹⩼ƀ;qs஼⩭௡ƀ;qs஼௅⩴lanô௢ií௪Ā;rஶ⪁»ஷƀAap⪊⪍⪑rò⥱rr;憮ar;櫲ƀ;svྍ⪜ྌĀ;d⪡⪢拼;拺cy;䑚΀AEadest⪷⪺⪾⫂⫅⫶⫹rò⥦;쀀≦̸rr;憚r;急Ȁ;fqs఻⫎⫣⫯tĀar⫔⫙rro÷⫁ightarro÷⪐ƀ;qs఻⪺⫪lanôౕĀ;sౕ⫴»శiíౝĀ;rవ⫾iĀ;eచథiäඐĀpt⬌⬑f;쀀𝕟膀¬;in⬙⬚⬶䂬nȀ;Edvஉ⬤⬨⬮;쀀⋹̸ot;쀀⋵̸ǡஉ⬳⬵;拷;拶iĀ;vಸ⬼ǡಸ⭁⭃;拾;拽ƀaor⭋⭣⭩rȀ;ast୻⭕⭚⭟lleì୻l;쀀⫽⃥;쀀∂̸lint;樔ƀ;ceಒ⭰⭳uåಥĀ;cಘ⭸Ā;eಒ⭽ñಘȀAait⮈⮋⮝⮧rò⦈rrƀ;cw⮔⮕⮙憛;쀀⤳̸;쀀↝̸ghtarrow»⮕riĀ;eೋೖ΀chimpqu⮽⯍⯙⬄୸⯤⯯Ȁ;cerല⯆ഷ⯉uå൅;쀀𝓃ortɭ⬅\0\0⯖ará⭖mĀ;e൮⯟Ā;q൴൳suĀbp⯫⯭å೸åഋƀbcp⯶ⰑⰙȀ;Ees⯿ⰀഢⰄ抄;쀀⫅̸etĀ;eഛⰋqĀ;qണⰀcĀ;eലⰗñസȀ;EesⰢⰣൟⰧ抅;쀀⫆̸etĀ;e൘ⰮqĀ;qൠⰣȀgilrⰽⰿⱅⱇìௗlde耻ñ䃱çృiangleĀlrⱒⱜeftĀ;eచⱚñదightĀ;eೋⱥñ೗Ā;mⱬⱭ䎽ƀ;esⱴⱵⱹ䀣ro;愖p;怇ҀDHadgilrsⲏⲔⲙⲞⲣⲰⲶⳓⳣash;抭arr;椄p;쀀≍⃒ash;抬ĀetⲨⲬ;쀀≥⃒;쀀>⃒nfin;槞ƀAetⲽⳁⳅrr;椂;쀀≤⃒Ā;rⳊⳍ쀀<⃒ie;쀀⊴⃒ĀAtⳘⳜrr;椃rie;쀀⊵⃒im;쀀∼⃒ƀAan⳰⳴ⴂrr;懖rĀhr⳺⳽k;椣Ā;oᏧᏥear;椧ቓ᪕\0\0\0\0\0\0\0\0\0\0\0\0\0ⴭ\0ⴸⵈⵠⵥ⵲ⶄᬇ\0\0ⶍⶫ\0ⷈⷎ\0ⷜ⸙⸫⸾⹃Ācsⴱ᪗ute耻ó䃳ĀiyⴼⵅrĀ;c᪞ⵂ耻ô䃴;䐾ʀabios᪠ⵒⵗǈⵚlac;䅑v;樸old;榼lig;䅓Ācr⵩⵭ir;榿;쀀𝔬ͯ⵹\0\0⵼\0ⶂn;䋛ave耻ò䃲;槁Ābmⶈ෴ar;榵Ȁacitⶕ⶘ⶥⶨrò᪀Āir⶝ⶠr;榾oss;榻nå๒;槀ƀaeiⶱⶵⶹcr;䅍ga;䏉ƀcdnⷀⷅǍron;䎿;榶pf;쀀𝕠ƀaelⷔ⷗ǒr;榷rp;榹΀;adiosvⷪⷫⷮ⸈⸍⸐⸖戨rò᪆Ȁ;efmⷷⷸ⸂⸅橝rĀ;oⷾⷿ愴f»ⷿ耻ª䂪耻º䂺gof;抶r;橖lope;橗;橛ƀclo⸟⸡⸧ò⸁ash耻ø䃸l;折iŬⸯ⸴de耻õ䃵esĀ;aǛ⸺s;樶ml耻ö䃶bar;挽ૡ⹞\0⹽\0⺀⺝\0⺢⺹\0\0⻋ຜ\0⼓\0\0⼫⾼\0⿈rȀ;astЃ⹧⹲຅脀¶;l⹭⹮䂶leìЃɩ⹸\0\0⹻m;櫳;櫽y;䐿rʀcimpt⺋⺏⺓ᡥ⺗nt;䀥od;䀮il;怰enk;怱r;쀀𝔭ƀimo⺨⺰⺴Ā;v⺭⺮䏆;䏕maô੶ne;明ƀ;tv⺿⻀⻈䏀chfork»´;䏖Āau⻏⻟nĀck⻕⻝kĀ;h⇴⻛;愎ö⇴sҀ;abcdemst⻳⻴ᤈ⻹⻽⼄⼆⼊⼎䀫cir;樣ir;樢Āouᵀ⼂;樥;橲n肻±ຝim;樦wo;樧ƀipu⼙⼠⼥ntint;樕f;쀀𝕡nd耻£䂣Ԁ;Eaceinosu່⼿⽁⽄⽇⾁⾉⾒⽾⾶;檳p;檷uå໙Ā;c໎⽌̀;acens່⽙⽟⽦⽨⽾pproø⽃urlyeñ໙ñ໎ƀaes⽯⽶⽺pprox;檹qq;檵im;拨iíໟmeĀ;s⾈ຮ怲ƀEas⽸⾐⽺ð⽵ƀdfp໬⾙⾯ƀals⾠⾥⾪lar;挮ine;挒urf;挓Ā;t໻⾴ï໻rel;抰Āci⿀⿅r;쀀𝓅;䏈ncsp;怈̀fiopsu⿚⋢⿟⿥⿫⿱r;쀀𝔮pf;쀀𝕢rime;恗cr;쀀𝓆ƀaeo⿸〉〓tĀei⿾々rnionóڰnt;樖stĀ;e【】䀿ñἙô༔઀ABHabcdefhilmnoprstux぀けさすムㄎㄫㅇㅢㅲㆎ㈆㈕㈤㈩㉘㉮㉲㊐㊰㊷ƀartぇおがròႳòϝail;検aròᱥar;楤΀cdenqrtとふへみわゔヌĀeuねぱ;쀀∽̱te;䅕iãᅮmptyv;榳gȀ;del࿑らるろ;榒;榥å࿑uo耻»䂻rր;abcfhlpstw࿜ガクシスゼゾダッデナp;極Ā;f࿠ゴs;椠;椳s;椞ë≝ð✮l;楅im;楴l;憣;憝Āaiパフil;椚oĀ;nホボ戶aló༞ƀabrョリヮrò៥rk;杳ĀakンヽcĀekヹ・;䁽;䁝Āes㄂㄄;榌lĀduㄊㄌ;榎;榐Ȁaeuyㄗㄜㄧㄩron;䅙Ādiㄡㄥil;䅗ì࿲âヺ;䑀Ȁclqsㄴㄷㄽㅄa;椷dhar;楩uoĀ;rȎȍh;憳ƀacgㅎㅟངlȀ;ipsླྀㅘㅛႜnåႻarôྩt;断ƀilrㅩဣㅮsht;楽;쀀𝔯ĀaoㅷㆆrĀduㅽㅿ»ѻĀ;l႑ㆄ;楬Ā;vㆋㆌ䏁;䏱ƀgns㆕ㇹㇼht̀ahlrstㆤㆰ㇂㇘㇤㇮rrowĀ;t࿜ㆭaéトarpoonĀduㆻㆿowîㅾp»႒eftĀah㇊㇐rrowó࿪arpoonóՑightarrows;應quigarro÷ニhreetimes;拌g;䋚ingdotseñἲƀahm㈍㈐㈓rò࿪aòՑ;怏oustĀ;a㈞㈟掱che»㈟mid;櫮Ȁabpt㈲㈽㉀㉒Ānr㈷㈺g;柭r;懾rëဃƀafl㉇㉊㉎r;榆;쀀𝕣us;樮imes;樵Āap㉝㉧rĀ;g㉣㉤䀩t;榔olint;樒arò㇣Ȁachq㉻㊀Ⴜ㊅quo;怺r;쀀𝓇Ābu・㊊oĀ;rȔȓƀhir㊗㊛㊠reåㇸmes;拊iȀ;efl㊪ၙᠡ㊫方tri;槎luhar;楨;愞ൡ㋕㋛㋟㌬㌸㍱\0㍺㎤\0\0㏬㏰\0㐨㑈㑚㒭㒱㓊㓱\0㘖\0\0㘳cute;䅛quï➺Ԁ;Eaceinpsyᇭ㋳㋵㋿㌂㌋㌏㌟㌦㌩;檴ǰ㋺\0㋼;檸on;䅡uåᇾĀ;dᇳ㌇il;䅟rc;䅝ƀEas㌖㌘㌛;檶p;檺im;择olint;樓iíሄ;䑁otƀ;be㌴ᵇ㌵担;橦΀Aacmstx㍆㍊㍗㍛㍞㍣㍭rr;懘rĀhr㍐㍒ë∨Ā;oਸ਼਴t耻§䂧i;䀻war;椩mĀin㍩ðnuóñt;朶rĀ;o㍶⁕쀀𝔰Ȁacoy㎂㎆㎑㎠rp;景Āhy㎋㎏cy;䑉;䑈rtɭ㎙\0\0㎜iäᑤaraì⹯耻­䂭Āgm㎨㎴maƀ;fv㎱㎲㎲䏃;䏂Ѐ;deglnprካ㏅㏉㏎㏖㏞㏡㏦ot;橪Ā;q኱ኰĀ;E㏓㏔檞;檠Ā;E㏛㏜檝;檟e;扆lus;樤arr;楲aròᄽȀaeit㏸㐈㐏㐗Āls㏽㐄lsetmé㍪hp;樳parsl;槤Ādlᑣ㐔e;挣Ā;e㐜㐝檪Ā;s㐢㐣檬;쀀⪬︀ƀflp㐮㐳㑂tcy;䑌Ā;b㐸㐹䀯Ā;a㐾㐿槄r;挿f;쀀𝕤aĀdr㑍ЂesĀ;u㑔㑕晠it»㑕ƀcsu㑠㑹㒟Āau㑥㑯pĀ;sᆈ㑫;쀀⊓︀pĀ;sᆴ㑵;쀀⊔︀uĀbp㑿㒏ƀ;esᆗᆜ㒆etĀ;eᆗ㒍ñᆝƀ;esᆨᆭ㒖etĀ;eᆨ㒝ñᆮƀ;afᅻ㒦ְrť㒫ֱ»ᅼaròᅈȀcemt㒹㒾㓂㓅r;쀀𝓈tmîñiì㐕aræᆾĀar㓎㓕rĀ;f㓔ឿ昆Āan㓚㓭ightĀep㓣㓪psiloîỠhé⺯s»⡒ʀbcmnp㓻㕞ሉ㖋㖎Ҁ;Edemnprs㔎㔏㔑㔕㔞㔣㔬㔱㔶抂;櫅ot;檽Ā;dᇚ㔚ot;櫃ult;櫁ĀEe㔨㔪;櫋;把lus;檿arr;楹ƀeiu㔽㕒㕕tƀ;en㔎㕅㕋qĀ;qᇚ㔏eqĀ;q㔫㔨m;櫇Ābp㕚㕜;櫕;櫓c̀;acensᇭ㕬㕲㕹㕻㌦pproø㋺urlyeñᇾñᇳƀaes㖂㖈㌛pproø㌚qñ㌗g;晪ڀ123;Edehlmnps㖩㖬㖯ሜ㖲㖴㗀㗉㗕㗚㗟㗨㗭耻¹䂹耻²䂲耻³䂳;櫆Āos㖹㖼t;檾ub;櫘Ā;dሢ㗅ot;櫄sĀou㗏㗒l;柉b;櫗arr;楻ult;櫂ĀEe㗤㗦;櫌;抋lus;櫀ƀeiu㗴㘉㘌tƀ;enሜ㗼㘂qĀ;qሢ㖲eqĀ;q㗧㗤m;櫈Ābp㘑㘓;櫔;櫖ƀAan㘜㘠㘭rr;懙rĀhr㘦㘨ë∮Ā;oਫ਩war;椪lig耻ß䃟௡㙑㙝㙠ዎ㙳㙹\0㙾㛂\0\0\0\0\0㛛㜃\0㜉㝬\0\0\0㞇ɲ㙖\0\0㙛get;挖;䏄rë๟ƀaey㙦㙫㙰ron;䅥dil;䅣;䑂lrec;挕r;쀀𝔱Ȁeiko㚆㚝㚵㚼ǲ㚋\0㚑eĀ4fኄኁaƀ;sv㚘㚙㚛䎸ym;䏑Ācn㚢㚲kĀas㚨㚮pproø዁im»ኬsðኞĀas㚺㚮ð዁rn耻þ䃾Ǭ̟㛆⋧es膀×;bd㛏㛐㛘䃗Ā;aᤏ㛕r;樱;樰ƀeps㛡㛣㜀á⩍Ȁ;bcf҆㛬㛰㛴ot;挶ir;櫱Ā;o㛹㛼쀀𝕥rk;櫚á㍢rime;怴ƀaip㜏㜒㝤dåቈ΀adempst㜡㝍㝀㝑㝗㝜㝟ngleʀ;dlqr㜰㜱㜶㝀㝂斵own»ᶻeftĀ;e⠀㜾ñम;扜ightĀ;e㊪㝋ñၚot;旬inus;樺lus;樹b;槍ime;樻ezium;揢ƀcht㝲㝽㞁Āry㝷㝻;쀀𝓉;䑆cy;䑛rok;䅧Āio㞋㞎xô᝷headĀlr㞗㞠eftarro÷ࡏightarrow»ཝऀAHabcdfghlmoprstuw㟐㟓㟗㟤㟰㟼㠎㠜㠣㠴㡑㡝㡫㢩㣌㣒㣪㣶ròϭar;楣Ācr㟜㟢ute耻ú䃺òᅐrǣ㟪\0㟭y;䑞ve;䅭Āiy㟵㟺rc耻û䃻;䑃ƀabh㠃㠆㠋ròᎭlac;䅱aòᏃĀir㠓㠘sht;楾;쀀𝔲rave耻ù䃹š㠧㠱rĀlr㠬㠮»ॗ»ႃlk;斀Āct㠹㡍ɯ㠿\0\0㡊rnĀ;e㡅㡆挜r»㡆op;挏ri;旸Āal㡖㡚cr;䅫肻¨͉Āgp㡢㡦on;䅳f;쀀𝕦̀adhlsuᅋ㡸㡽፲㢑㢠ownáᎳarpoonĀlr㢈㢌efô㠭ighô㠯iƀ;hl㢙㢚㢜䏅»ᏺon»㢚parrows;懈ƀcit㢰㣄㣈ɯ㢶\0\0㣁rnĀ;e㢼㢽挝r»㢽op;挎ng;䅯ri;旹cr;쀀𝓊ƀdir㣙㣝㣢ot;拰lde;䅩iĀ;f㜰㣨»᠓Āam㣯㣲rò㢨l耻ü䃼angle;榧ހABDacdeflnoprsz㤜㤟㤩㤭㦵㦸㦽㧟㧤㧨㧳㧹㧽㨁㨠ròϷarĀ;v㤦㤧櫨;櫩asèϡĀnr㤲㤷grt;榜΀eknprst㓣㥆㥋㥒㥝㥤㦖appá␕othinçẖƀhir㓫⻈㥙opô⾵Ā;hᎷ㥢ïㆍĀiu㥩㥭gmá㎳Ābp㥲㦄setneqĀ;q㥽㦀쀀⊊︀;쀀⫋︀setneqĀ;q㦏㦒쀀⊋︀;쀀⫌︀Āhr㦛㦟etá㚜iangleĀlr㦪㦯eft»थight»ၑy;䐲ash»ံƀelr㧄㧒㧗ƀ;beⷪ㧋㧏ar;抻q;扚lip;拮Ābt㧜ᑨaòᑩr;쀀𝔳tré㦮suĀbp㧯㧱»ജ»൙pf;쀀𝕧roð໻tré㦴Ācu㨆㨋r;쀀𝓋Ābp㨐㨘nĀEe㦀㨖»㥾nĀEe㦒㨞»㦐igzag;榚΀cefoprs㨶㨻㩖㩛㩔㩡㩪irc;䅵Ādi㩀㩑Ābg㩅㩉ar;機eĀ;qᗺ㩏;扙erp;愘r;쀀𝔴pf;쀀𝕨Ā;eᑹ㩦atèᑹcr;쀀𝓌ૣណ㪇\0㪋\0㪐㪛\0\0㪝㪨㪫㪯\0\0㫃㫎\0㫘ៜ៟tré៑r;쀀𝔵ĀAa㪔㪗ròσrò৶;䎾ĀAa㪡㪤ròθrò৫að✓is;拻ƀdptឤ㪵㪾Āfl㪺ឩ;쀀𝕩imåឲĀAa㫇㫊ròώròਁĀcq㫒ីr;쀀𝓍Āpt៖㫜ré។Ѐacefiosu㫰㫽㬈㬌㬑㬕㬛㬡cĀuy㫶㫻te耻ý䃽;䑏Āiy㬂㬆rc;䅷;䑋n耻¥䂥r;쀀𝔶cy;䑗pf;쀀𝕪cr;쀀𝓎Ācm㬦㬩y;䑎l耻ÿ䃿Ԁacdefhiosw㭂㭈㭔㭘㭤㭩㭭㭴㭺㮀cute;䅺Āay㭍㭒ron;䅾;䐷ot;䅼Āet㭝㭡træᕟa;䎶r;쀀𝔷cy;䐶grarr;懝pf;쀀𝕫cr;쀀𝓏Ājn㮅㮇;怍j;怌'.split("").map(e=>e.charCodeAt(0))),_p=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]);function Cp(e){var t;return e>=55296&&e<=57343||e>1114111?65533:(t=_p.get(e))!==null&&t!==void 0?t:e}var ge;(function(e){e[e.NUM=35]="NUM",e[e.SEMI=59]="SEMI",e[e.EQUALS=61]="EQUALS",e[e.ZERO=48]="ZERO",e[e.NINE=57]="NINE",e[e.LOWER_A=97]="LOWER_A",e[e.LOWER_F=102]="LOWER_F",e[e.LOWER_X=120]="LOWER_X",e[e.LOWER_Z=122]="LOWER_Z",e[e.UPPER_A=65]="UPPER_A",e[e.UPPER_F=70]="UPPER_F",e[e.UPPER_Z=90]="UPPER_Z"})(ge||(ge={}));const kp=32;var ut;(function(e){e[e.VALUE_LENGTH=49152]="VALUE_LENGTH",e[e.BRANCH_LENGTH=16256]="BRANCH_LENGTH",e[e.JUMP_TABLE=127]="JUMP_TABLE"})(ut||(ut={}));function xr(e){return e>=ge.ZERO&&e<=ge.NINE}function Sp(e){return e>=ge.UPPER_A&&e<=ge.UPPER_F||e>=ge.LOWER_A&&e<=ge.LOWER_F}function yp(e){return e>=ge.UPPER_A&&e<=ge.UPPER_Z||e>=ge.LOWER_A&&e<=ge.LOWER_Z||xr(e)}function Ip(e){return e===ge.EQUALS||yp(e)}var Ee;(function(e){e[e.EntityStart=0]="EntityStart",e[e.NumericStart=1]="NumericStart",e[e.NumericDecimal=2]="NumericDecimal",e[e.NumericHex=3]="NumericHex",e[e.NamedEntity=4]="NamedEntity"})(Ee||(Ee={}));var Ke;(function(e){e[e.Legacy=0]="Legacy",e[e.Strict=1]="Strict",e[e.Attribute=2]="Attribute"})(Ke||(Ke={}));class xp{constructor(t,n,r){this.decodeTree=t,this.emitCodePoint=n,this.errors=r,this.state=Ee.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=Ke.Strict}startEntity(t){this.decodeMode=t,this.state=Ee.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(t,n){switch(this.state){case Ee.EntityStart:return t.charCodeAt(n)===ge.NUM?(this.state=Ee.NumericStart,this.consumed+=1,this.stateNumericStart(t,n+1)):(this.state=Ee.NamedEntity,this.stateNamedEntity(t,n));case Ee.NumericStart:return this.stateNumericStart(t,n);case Ee.NumericDecimal:return this.stateNumericDecimal(t,n);case Ee.NumericHex:return this.stateNumericHex(t,n);case Ee.NamedEntity:return this.stateNamedEntity(t,n)}}stateNumericStart(t,n){return n>=t.length?-1:(t.charCodeAt(n)|kp)===ge.LOWER_X?(this.state=Ee.NumericHex,this.consumed+=1,this.stateNumericHex(t,n+1)):(this.state=Ee.NumericDecimal,this.stateNumericDecimal(t,n))}addToNumericResult(t,n,r,u){if(n!==r){const a=r-n;this.result=this.result*Math.pow(u,a)+Number.parseInt(t.substr(n,a),u),this.consumed+=a}}stateNumericHex(t,n){const r=n;for(;n<t.length;){const u=t.charCodeAt(n);if(xr(u)||Sp(u))n+=1;else return this.addToNumericResult(t,r,n,16),this.emitNumericEntity(u,3)}return this.addToNumericResult(t,r,n,16),-1}stateNumericDecimal(t,n){const r=n;for(;n<t.length;){const u=t.charCodeAt(n);if(xr(u))n+=1;else return this.addToNumericResult(t,r,n,10),this.emitNumericEntity(u,2)}return this.addToNumericResult(t,r,n,10),-1}emitNumericEntity(t,n){var r;if(this.consumed<=n)return(r=this.errors)===null||r===void 0||r.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(t===ge.SEMI)this.consumed+=1;else if(this.decodeMode===Ke.Strict)return 0;return this.emitCodePoint(Cp(this.result),this.consumed),this.errors&&(t!==ge.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(t,n){const{decodeTree:r}=this;let u=r[this.treeIndex],a=(u&ut.VALUE_LENGTH)>>14;for(;n<t.length;n++,this.excess++){const i=t.charCodeAt(n);if(this.treeIndex=Np(r,u,this.treeIndex+Math.max(1,a),i),this.treeIndex<0)return this.result===0||this.decodeMode===Ke.Attribute&&(a===0||Ip(i))?0:this.emitNotTerminatedNamedEntity();if(u=r[this.treeIndex],a=(u&ut.VALUE_LENGTH)>>14,a!==0){if(i===ge.SEMI)return this.emitNamedEntityData(this.treeIndex,a,this.consumed+this.excess);this.decodeMode!==Ke.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return-1}emitNotTerminatedNamedEntity(){var t;const{result:n,decodeTree:r}=this,u=(r[n]&ut.VALUE_LENGTH)>>14;return this.emitNamedEntityData(n,u,this.consumed),(t=this.errors)===null||t===void 0||t.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(t,n,r){const{decodeTree:u}=this;return this.emitCodePoint(n===1?u[t]&~ut.VALUE_LENGTH:u[t+1],r),n===3&&this.emitCodePoint(u[t+2],r),r}end(){var t;switch(this.state){case Ee.NamedEntity:return this.result!==0&&(this.decodeMode!==Ke.Attribute||this.result===this.treeIndex)?this.emitNotTerminatedNamedEntity():0;case Ee.NumericDecimal:return this.emitNumericEntity(0,2);case Ee.NumericHex:return this.emitNumericEntity(0,3);case Ee.NumericStart:return(t=this.errors)===null||t===void 0||t.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case Ee.EntityStart:return 0}}}function Np(e,t,n,r){const u=(t&ut.BRANCH_LENGTH)>>7,a=t&ut.JUMP_TABLE;if(u===0)return a!==0&&r===a?n:-1;if(a){const l=r-a;return l<0||l>=u?-1:e[n+l]-1}let i=n,o=i+u-1;for(;i<=o;){const l=i+o>>>1,c=e[l];if(c<r)i=l+1;else if(c>r)o=l-1;else return e[l+u]}return-1}var O;(function(e){e.HTML="http://www.w3.org/1999/xhtml",e.MATHML="http://www.w3.org/1998/Math/MathML",e.SVG="http://www.w3.org/2000/svg",e.XLINK="http://www.w3.org/1999/xlink",e.XML="http://www.w3.org/XML/1998/namespace",e.XMLNS="http://www.w3.org/2000/xmlns/"})(O||(O={}));var ct;(function(e){e.TYPE="type",e.ACTION="action",e.ENCODING="encoding",e.PROMPT="prompt",e.NAME="name",e.COLOR="color",e.FACE="face",e.SIZE="size"})(ct||(ct={}));var Fe;(function(e){e.NO_QUIRKS="no-quirks",e.QUIRKS="quirks",e.LIMITED_QUIRKS="limited-quirks"})(Fe||(Fe={}));var C;(function(e){e.A="a",e.ADDRESS="address",e.ANNOTATION_XML="annotation-xml",e.APPLET="applet",e.AREA="area",e.ARTICLE="article",e.ASIDE="aside",e.B="b",e.BASE="base",e.BASEFONT="basefont",e.BGSOUND="bgsound",e.BIG="big",e.BLOCKQUOTE="blockquote",e.BODY="body",e.BR="br",e.BUTTON="button",e.CAPTION="caption",e.CENTER="center",e.CODE="code",e.COL="col",e.COLGROUP="colgroup",e.DD="dd",e.DESC="desc",e.DETAILS="details",e.DIALOG="dialog",e.DIR="dir",e.DIV="div",e.DL="dl",e.DT="dt",e.EM="em",e.EMBED="embed",e.FIELDSET="fieldset",e.FIGCAPTION="figcaption",e.FIGURE="figure",e.FONT="font",e.FOOTER="footer",e.FOREIGN_OBJECT="foreignObject",e.FORM="form",e.FRAME="frame",e.FRAMESET="frameset",e.H1="h1",e.H2="h2",e.H3="h3",e.H4="h4",e.H5="h5",e.H6="h6",e.HEAD="head",e.HEADER="header",e.HGROUP="hgroup",e.HR="hr",e.HTML="html",e.I="i",e.IMG="img",e.IMAGE="image",e.INPUT="input",e.IFRAME="iframe",e.KEYGEN="keygen",e.LABEL="label",e.LI="li",e.LINK="link",e.LISTING="listing",e.MAIN="main",e.MALIGNMARK="malignmark",e.MARQUEE="marquee",e.MATH="math",e.MENU="menu",e.META="meta",e.MGLYPH="mglyph",e.MI="mi",e.MO="mo",e.MN="mn",e.MS="ms",e.MTEXT="mtext",e.NAV="nav",e.NOBR="nobr",e.NOFRAMES="noframes",e.NOEMBED="noembed",e.NOSCRIPT="noscript",e.OBJECT="object",e.OL="ol",e.OPTGROUP="optgroup",e.OPTION="option",e.P="p",e.PARAM="param",e.PLAINTEXT="plaintext",e.PRE="pre",e.RB="rb",e.RP="rp",e.RT="rt",e.RTC="rtc",e.RUBY="ruby",e.S="s",e.SCRIPT="script",e.SEARCH="search",e.SECTION="section",e.SELECT="select",e.SOURCE="source",e.SMALL="small",e.SPAN="span",e.STRIKE="strike",e.STRONG="strong",e.STYLE="style",e.SUB="sub",e.SUMMARY="summary",e.SUP="sup",e.TABLE="table",e.TBODY="tbody",e.TEMPLATE="template",e.TEXTAREA="textarea",e.TFOOT="tfoot",e.TD="td",e.TH="th",e.THEAD="thead",e.TITLE="title",e.TR="tr",e.TRACK="track",e.TT="tt",e.U="u",e.UL="ul",e.SVG="svg",e.VAR="var",e.WBR="wbr",e.XMP="xmp"})(C||(C={}));var s;(function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.A=1]="A",e[e.ADDRESS=2]="ADDRESS",e[e.ANNOTATION_XML=3]="ANNOTATION_XML",e[e.APPLET=4]="APPLET",e[e.AREA=5]="AREA",e[e.ARTICLE=6]="ARTICLE",e[e.ASIDE=7]="ASIDE",e[e.B=8]="B",e[e.BASE=9]="BASE",e[e.BASEFONT=10]="BASEFONT",e[e.BGSOUND=11]="BGSOUND",e[e.BIG=12]="BIG",e[e.BLOCKQUOTE=13]="BLOCKQUOTE",e[e.BODY=14]="BODY",e[e.BR=15]="BR",e[e.BUTTON=16]="BUTTON",e[e.CAPTION=17]="CAPTION",e[e.CENTER=18]="CENTER",e[e.CODE=19]="CODE",e[e.COL=20]="COL",e[e.COLGROUP=21]="COLGROUP",e[e.DD=22]="DD",e[e.DESC=23]="DESC",e[e.DETAILS=24]="DETAILS",e[e.DIALOG=25]="DIALOG",e[e.DIR=26]="DIR",e[e.DIV=27]="DIV",e[e.DL=28]="DL",e[e.DT=29]="DT",e[e.EM=30]="EM",e[e.EMBED=31]="EMBED",e[e.FIELDSET=32]="FIELDSET",e[e.FIGCAPTION=33]="FIGCAPTION",e[e.FIGURE=34]="FIGURE",e[e.FONT=35]="FONT",e[e.FOOTER=36]="FOOTER",e[e.FOREIGN_OBJECT=37]="FOREIGN_OBJECT",e[e.FORM=38]="FORM",e[e.FRAME=39]="FRAME",e[e.FRAMESET=40]="FRAMESET",e[e.H1=41]="H1",e[e.H2=42]="H2",e[e.H3=43]="H3",e[e.H4=44]="H4",e[e.H5=45]="H5",e[e.H6=46]="H6",e[e.HEAD=47]="HEAD",e[e.HEADER=48]="HEADER",e[e.HGROUP=49]="HGROUP",e[e.HR=50]="HR",e[e.HTML=51]="HTML",e[e.I=52]="I",e[e.IMG=53]="IMG",e[e.IMAGE=54]="IMAGE",e[e.INPUT=55]="INPUT",e[e.IFRAME=56]="IFRAME",e[e.KEYGEN=57]="KEYGEN",e[e.LABEL=58]="LABEL",e[e.LI=59]="LI",e[e.LINK=60]="LINK",e[e.LISTING=61]="LISTING",e[e.MAIN=62]="MAIN",e[e.MALIGNMARK=63]="MALIGNMARK",e[e.MARQUEE=64]="MARQUEE",e[e.MATH=65]="MATH",e[e.MENU=66]="MENU",e[e.META=67]="META",e[e.MGLYPH=68]="MGLYPH",e[e.MI=69]="MI",e[e.MO=70]="MO",e[e.MN=71]="MN",e[e.MS=72]="MS",e[e.MTEXT=73]="MTEXT",e[e.NAV=74]="NAV",e[e.NOBR=75]="NOBR",e[e.NOFRAMES=76]="NOFRAMES",e[e.NOEMBED=77]="NOEMBED",e[e.NOSCRIPT=78]="NOSCRIPT",e[e.OBJECT=79]="OBJECT",e[e.OL=80]="OL",e[e.OPTGROUP=81]="OPTGROUP",e[e.OPTION=82]="OPTION",e[e.P=83]="P",e[e.PARAM=84]="PARAM",e[e.PLAINTEXT=85]="PLAINTEXT",e[e.PRE=86]="PRE",e[e.RB=87]="RB",e[e.RP=88]="RP",e[e.RT=89]="RT",e[e.RTC=90]="RTC",e[e.RUBY=91]="RUBY",e[e.S=92]="S",e[e.SCRIPT=93]="SCRIPT",e[e.SEARCH=94]="SEARCH",e[e.SECTION=95]="SECTION",e[e.SELECT=96]="SELECT",e[e.SOURCE=97]="SOURCE",e[e.SMALL=98]="SMALL",e[e.SPAN=99]="SPAN",e[e.STRIKE=100]="STRIKE",e[e.STRONG=101]="STRONG",e[e.STYLE=102]="STYLE",e[e.SUB=103]="SUB",e[e.SUMMARY=104]="SUMMARY",e[e.SUP=105]="SUP",e[e.TABLE=106]="TABLE",e[e.TBODY=107]="TBODY",e[e.TEMPLATE=108]="TEMPLATE",e[e.TEXTAREA=109]="TEXTAREA",e[e.TFOOT=110]="TFOOT",e[e.TD=111]="TD",e[e.TH=112]="TH",e[e.THEAD=113]="THEAD",e[e.TITLE=114]="TITLE",e[e.TR=115]="TR",e[e.TRACK=116]="TRACK",e[e.TT=117]="TT",e[e.U=118]="U",e[e.UL=119]="UL",e[e.SVG=120]="SVG",e[e.VAR=121]="VAR",e[e.WBR=122]="WBR",e[e.XMP=123]="XMP"})(s||(s={}));const Op=new Map([[C.A,s.A],[C.ADDRESS,s.ADDRESS],[C.ANNOTATION_XML,s.ANNOTATION_XML],[C.APPLET,s.APPLET],[C.AREA,s.AREA],[C.ARTICLE,s.ARTICLE],[C.ASIDE,s.ASIDE],[C.B,s.B],[C.BASE,s.BASE],[C.BASEFONT,s.BASEFONT],[C.BGSOUND,s.BGSOUND],[C.BIG,s.BIG],[C.BLOCKQUOTE,s.BLOCKQUOTE],[C.BODY,s.BODY],[C.BR,s.BR],[C.BUTTON,s.BUTTON],[C.CAPTION,s.CAPTION],[C.CENTER,s.CENTER],[C.CODE,s.CODE],[C.COL,s.COL],[C.COLGROUP,s.COLGROUP],[C.DD,s.DD],[C.DESC,s.DESC],[C.DETAILS,s.DETAILS],[C.DIALOG,s.DIALOG],[C.DIR,s.DIR],[C.DIV,s.DIV],[C.DL,s.DL],[C.DT,s.DT],[C.EM,s.EM],[C.EMBED,s.EMBED],[C.FIELDSET,s.FIELDSET],[C.FIGCAPTION,s.FIGCAPTION],[C.FIGURE,s.FIGURE],[C.FONT,s.FONT],[C.FOOTER,s.FOOTER],[C.FOREIGN_OBJECT,s.FOREIGN_OBJECT],[C.FORM,s.FORM],[C.FRAME,s.FRAME],[C.FRAMESET,s.FRAMESET],[C.H1,s.H1],[C.H2,s.H2],[C.H3,s.H3],[C.H4,s.H4],[C.H5,s.H5],[C.H6,s.H6],[C.HEAD,s.HEAD],[C.HEADER,s.HEADER],[C.HGROUP,s.HGROUP],[C.HR,s.HR],[C.HTML,s.HTML],[C.I,s.I],[C.IMG,s.IMG],[C.IMAGE,s.IMAGE],[C.INPUT,s.INPUT],[C.IFRAME,s.IFRAME],[C.KEYGEN,s.KEYGEN],[C.LABEL,s.LABEL],[C.LI,s.LI],[C.LINK,s.LINK],[C.LISTING,s.LISTING],[C.MAIN,s.MAIN],[C.MALIGNMARK,s.MALIGNMARK],[C.MARQUEE,s.MARQUEE],[C.MATH,s.MATH],[C.MENU,s.MENU],[C.META,s.META],[C.MGLYPH,s.MGLYPH],[C.MI,s.MI],[C.MO,s.MO],[C.MN,s.MN],[C.MS,s.MS],[C.MTEXT,s.MTEXT],[C.NAV,s.NAV],[C.NOBR,s.NOBR],[C.NOFRAMES,s.NOFRAMES],[C.NOEMBED,s.NOEMBED],[C.NOSCRIPT,s.NOSCRIPT],[C.OBJECT,s.OBJECT],[C.OL,s.OL],[C.OPTGROUP,s.OPTGROUP],[C.OPTION,s.OPTION],[C.P,s.P],[C.PARAM,s.PARAM],[C.PLAINTEXT,s.PLAINTEXT],[C.PRE,s.PRE],[C.RB,s.RB],[C.RP,s.RP],[C.RT,s.RT],[C.RTC,s.RTC],[C.RUBY,s.RUBY],[C.S,s.S],[C.SCRIPT,s.SCRIPT],[C.SEARCH,s.SEARCH],[C.SECTION,s.SECTION],[C.SELECT,s.SELECT],[C.SOURCE,s.SOURCE],[C.SMALL,s.SMALL],[C.SPAN,s.SPAN],[C.STRIKE,s.STRIKE],[C.STRONG,s.STRONG],[C.STYLE,s.STYLE],[C.SUB,s.SUB],[C.SUMMARY,s.SUMMARY],[C.SUP,s.SUP],[C.TABLE,s.TABLE],[C.TBODY,s.TBODY],[C.TEMPLATE,s.TEMPLATE],[C.TEXTAREA,s.TEXTAREA],[C.TFOOT,s.TFOOT],[C.TD,s.TD],[C.TH,s.TH],[C.THEAD,s.THEAD],[C.TITLE,s.TITLE],[C.TR,s.TR],[C.TRACK,s.TRACK],[C.TT,s.TT],[C.U,s.U],[C.UL,s.UL],[C.SVG,s.SVG],[C.VAR,s.VAR],[C.WBR,s.WBR],[C.XMP,s.XMP]]);function Pt(e){var t;return(t=Op.get(e))!==null&&t!==void 0?t:s.UNKNOWN}const D=s,Lp={[O.HTML]:new Set([D.ADDRESS,D.APPLET,D.AREA,D.ARTICLE,D.ASIDE,D.BASE,D.BASEFONT,D.BGSOUND,D.BLOCKQUOTE,D.BODY,D.BR,D.BUTTON,D.CAPTION,D.CENTER,D.COL,D.COLGROUP,D.DD,D.DETAILS,D.DIR,D.DIV,D.DL,D.DT,D.EMBED,D.FIELDSET,D.FIGCAPTION,D.FIGURE,D.FOOTER,D.FORM,D.FRAME,D.FRAMESET,D.H1,D.H2,D.H3,D.H4,D.H5,D.H6,D.HEAD,D.HEADER,D.HGROUP,D.HR,D.HTML,D.IFRAME,D.IMG,D.INPUT,D.LI,D.LINK,D.LISTING,D.MAIN,D.MARQUEE,D.MENU,D.META,D.NAV,D.NOEMBED,D.NOFRAMES,D.NOSCRIPT,D.OBJECT,D.OL,D.P,D.PARAM,D.PLAINTEXT,D.PRE,D.SCRIPT,D.SECTION,D.SELECT,D.SOURCE,D.STYLE,D.SUMMARY,D.TABLE,D.TBODY,D.TD,D.TEMPLATE,D.TEXTAREA,D.TFOOT,D.TH,D.THEAD,D.TITLE,D.TR,D.TRACK,D.UL,D.WBR,D.XMP]),[O.MATHML]:new Set([D.MI,D.MO,D.MN,D.MS,D.MTEXT,D.ANNOTATION_XML]),[O.SVG]:new Set([D.TITLE,D.FOREIGN_OBJECT,D.DESC]),[O.XLINK]:new Set,[O.XML]:new Set,[O.XMLNS]:new Set},Nr=new Set([D.H1,D.H2,D.H3,D.H4,D.H5,D.H6]);C.STYLE,C.SCRIPT,C.XMP,C.IFRAME,C.NOEMBED,C.NOFRAMES,C.PLAINTEXT;var E;(function(e){e[e.DATA=0]="DATA",e[e.RCDATA=1]="RCDATA",e[e.RAWTEXT=2]="RAWTEXT",e[e.SCRIPT_DATA=3]="SCRIPT_DATA",e[e.PLAINTEXT=4]="PLAINTEXT",e[e.TAG_OPEN=5]="TAG_OPEN",e[e.END_TAG_OPEN=6]="END_TAG_OPEN",e[e.TAG_NAME=7]="TAG_NAME",e[e.RCDATA_LESS_THAN_SIGN=8]="RCDATA_LESS_THAN_SIGN",e[e.RCDATA_END_TAG_OPEN=9]="RCDATA_END_TAG_OPEN",e[e.RCDATA_END_TAG_NAME=10]="RCDATA_END_TAG_NAME",e[e.RAWTEXT_LESS_THAN_SIGN=11]="RAWTEXT_LESS_THAN_SIGN",e[e.RAWTEXT_END_TAG_OPEN=12]="RAWTEXT_END_TAG_OPEN",e[e.RAWTEXT_END_TAG_NAME=13]="RAWTEXT_END_TAG_NAME",e[e.SCRIPT_DATA_LESS_THAN_SIGN=14]="SCRIPT_DATA_LESS_THAN_SIGN",e[e.SCRIPT_DATA_END_TAG_OPEN=15]="SCRIPT_DATA_END_TAG_OPEN",e[e.SCRIPT_DATA_END_TAG_NAME=16]="SCRIPT_DATA_END_TAG_NAME",e[e.SCRIPT_DATA_ESCAPE_START=17]="SCRIPT_DATA_ESCAPE_START",e[e.SCRIPT_DATA_ESCAPE_START_DASH=18]="SCRIPT_DATA_ESCAPE_START_DASH",e[e.SCRIPT_DATA_ESCAPED=19]="SCRIPT_DATA_ESCAPED",e[e.SCRIPT_DATA_ESCAPED_DASH=20]="SCRIPT_DATA_ESCAPED_DASH",e[e.SCRIPT_DATA_ESCAPED_DASH_DASH=21]="SCRIPT_DATA_ESCAPED_DASH_DASH",e[e.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN=22]="SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN",e[e.SCRIPT_DATA_ESCAPED_END_TAG_OPEN=23]="SCRIPT_DATA_ESCAPED_END_TAG_OPEN",e[e.SCRIPT_DATA_ESCAPED_END_TAG_NAME=24]="SCRIPT_DATA_ESCAPED_END_TAG_NAME",e[e.SCRIPT_DATA_DOUBLE_ESCAPE_START=25]="SCRIPT_DATA_DOUBLE_ESCAPE_START",e[e.SCRIPT_DATA_DOUBLE_ESCAPED=26]="SCRIPT_DATA_DOUBLE_ESCAPED",e[e.SCRIPT_DATA_DOUBLE_ESCAPED_DASH=27]="SCRIPT_DATA_DOUBLE_ESCAPED_DASH",e[e.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH=28]="SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH",e[e.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN=29]="SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN",e[e.SCRIPT_DATA_DOUBLE_ESCAPE_END=30]="SCRIPT_DATA_DOUBLE_ESCAPE_END",e[e.BEFORE_ATTRIBUTE_NAME=31]="BEFORE_ATTRIBUTE_NAME",e[e.ATTRIBUTE_NAME=32]="ATTRIBUTE_NAME",e[e.AFTER_ATTRIBUTE_NAME=33]="AFTER_ATTRIBUTE_NAME",e[e.BEFORE_ATTRIBUTE_VALUE=34]="BEFORE_ATTRIBUTE_VALUE",e[e.ATTRIBUTE_VALUE_DOUBLE_QUOTED=35]="ATTRIBUTE_VALUE_DOUBLE_QUOTED",e[e.ATTRIBUTE_VALUE_SINGLE_QUOTED=36]="ATTRIBUTE_VALUE_SINGLE_QUOTED",e[e.ATTRIBUTE_VALUE_UNQUOTED=37]="ATTRIBUTE_VALUE_UNQUOTED",e[e.AFTER_ATTRIBUTE_VALUE_QUOTED=38]="AFTER_ATTRIBUTE_VALUE_QUOTED",e[e.SELF_CLOSING_START_TAG=39]="SELF_CLOSING_START_TAG",e[e.BOGUS_COMMENT=40]="BOGUS_COMMENT",e[e.MARKUP_DECLARATION_OPEN=41]="MARKUP_DECLARATION_OPEN",e[e.COMMENT_START=42]="COMMENT_START",e[e.COMMENT_START_DASH=43]="COMMENT_START_DASH",e[e.COMMENT=44]="COMMENT",e[e.COMMENT_LESS_THAN_SIGN=45]="COMMENT_LESS_THAN_SIGN",e[e.COMMENT_LESS_THAN_SIGN_BANG=46]="COMMENT_LESS_THAN_SIGN_BANG",e[e.COMMENT_LESS_THAN_SIGN_BANG_DASH=47]="COMMENT_LESS_THAN_SIGN_BANG_DASH",e[e.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH=48]="COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH",e[e.COMMENT_END_DASH=49]="COMMENT_END_DASH",e[e.COMMENT_END=50]="COMMENT_END",e[e.COMMENT_END_BANG=51]="COMMENT_END_BANG",e[e.DOCTYPE=52]="DOCTYPE",e[e.BEFORE_DOCTYPE_NAME=53]="BEFORE_DOCTYPE_NAME",e[e.DOCTYPE_NAME=54]="DOCTYPE_NAME",e[e.AFTER_DOCTYPE_NAME=55]="AFTER_DOCTYPE_NAME",e[e.AFTER_DOCTYPE_PUBLIC_KEYWORD=56]="AFTER_DOCTYPE_PUBLIC_KEYWORD",e[e.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER=57]="BEFORE_DOCTYPE_PUBLIC_IDENTIFIER",e[e.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED=58]="DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED",e[e.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED=59]="DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED",e[e.AFTER_DOCTYPE_PUBLIC_IDENTIFIER=60]="AFTER_DOCTYPE_PUBLIC_IDENTIFIER",e[e.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS=61]="BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS",e[e.AFTER_DOCTYPE_SYSTEM_KEYWORD=62]="AFTER_DOCTYPE_SYSTEM_KEYWORD",e[e.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER=63]="BEFORE_DOCTYPE_SYSTEM_IDENTIFIER",e[e.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED=64]="DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED",e[e.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED=65]="DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED",e[e.AFTER_DOCTYPE_SYSTEM_IDENTIFIER=66]="AFTER_DOCTYPE_SYSTEM_IDENTIFIER",e[e.BOGUS_DOCTYPE=67]="BOGUS_DOCTYPE",e[e.CDATA_SECTION=68]="CDATA_SECTION",e[e.CDATA_SECTION_BRACKET=69]="CDATA_SECTION_BRACKET",e[e.CDATA_SECTION_END=70]="CDATA_SECTION_END",e[e.CHARACTER_REFERENCE=71]="CHARACTER_REFERENCE",e[e.AMBIGUOUS_AMPERSAND=72]="AMBIGUOUS_AMPERSAND"})(E||(E={}));const fe={DATA:E.DATA,RCDATA:E.RCDATA,RAWTEXT:E.RAWTEXT,SCRIPT_DATA:E.SCRIPT_DATA,PLAINTEXT:E.PLAINTEXT,CDATA_SECTION:E.CDATA_SECTION};function Dp(e){return e>=p.DIGIT_0&&e<=p.DIGIT_9}function jt(e){return e>=p.LATIN_CAPITAL_A&&e<=p.LATIN_CAPITAL_Z}function Rp(e){return e>=p.LATIN_SMALL_A&&e<=p.LATIN_SMALL_Z}function nt(e){return Rp(e)||jt(e)}function Ai(e){return nt(e)||Dp(e)}function Tn(e){return e+32}function ps(e){return e===p.SPACE||e===p.LINE_FEED||e===p.TABULATION||e===p.FORM_FEED}function _i(e){return ps(e)||e===p.SOLIDUS||e===p.GREATER_THAN_SIGN}function Pp(e){return e===p.NULL?S.nullCharacterReference:e>1114111?S.characterReferenceOutsideUnicodeRange:cs(e)?S.surrogateCharacterReference:hs(e)?S.noncharacterCharacterReference:fs(e)||e===p.CARRIAGE_RETURN?S.controlCharacterReference:null}class wp{constructor(t,n){this.options=t,this.handler=n,this.paused=!1,this.inLoop=!1,this.inForeignNode=!1,this.lastStartTagName="",this.active=!1,this.state=E.DATA,this.returnState=E.DATA,this.entityStartPos=0,this.consumedAfterSnapshot=-1,this.currentCharacterToken=null,this.currentToken=null,this.currentAttr={name:"",value:""},this.preprocessor=new bp(n),this.currentLocation=this.getCurrentLocation(-1),this.entityDecoder=new xp(Ap,(r,u)=>{this.preprocessor.pos=this.entityStartPos+u-1,this._flushCodePointConsumedAsCharacterReference(r)},n.onParseError?{missingSemicolonAfterCharacterReference:()=>{this._err(S.missingSemicolonAfterCharacterReference,1)},absenceOfDigitsInNumericCharacterReference:r=>{this._err(S.absenceOfDigitsInNumericCharacterReference,this.entityStartPos-this.preprocessor.pos+r)},validateNumericCharacterReference:r=>{const u=Pp(r);u&&this._err(u,1)}}:void 0)}_err(t,n=0){var r,u;(u=(r=this.handler).onParseError)===null||u===void 0||u.call(r,this.preprocessor.getError(t,n))}getCurrentLocation(t){return this.options.sourceCodeLocationInfo?{startLine:this.preprocessor.line,startCol:this.preprocessor.col-t,startOffset:this.preprocessor.offset-t,endLine:-1,endCol:-1,endOffset:-1}:null}_runParsingLoop(){if(!this.inLoop){for(this.inLoop=!0;this.active&&!this.paused;){this.consumedAfterSnapshot=0;const t=this._consume();this._ensureHibernation()||this._callState(t)}this.inLoop=!1}}pause(){this.paused=!0}resume(t){if(!this.paused)throw new Error("Parser was already resumed");this.paused=!1,!this.inLoop&&(this._runParsingLoop(),this.paused||t==null||t())}write(t,n,r){this.active=!0,this.preprocessor.write(t,n),this._runParsingLoop(),this.paused||r==null||r()}insertHtmlAtCurrentPos(t){this.active=!0,this.preprocessor.insertHtmlAtCurrentPos(t),this._runParsingLoop()}_ensureHibernation(){return this.preprocessor.endOfChunkHit?(this.preprocessor.retreat(this.consumedAfterSnapshot),this.consumedAfterSnapshot=0,this.active=!1,!0):!1}_consume(){return this.consumedAfterSnapshot++,this.preprocessor.advance()}_advanceBy(t){this.consumedAfterSnapshot+=t;for(let n=0;n<t;n++)this.preprocessor.advance()}_consumeSequenceIfMatch(t,n){return this.preprocessor.startsWith(t,n)?(this._advanceBy(t.length-1),!0):!1}_createStartTagToken(){this.currentToken={type:G.START_TAG,tagName:"",tagID:s.UNKNOWN,selfClosing:!1,ackSelfClosing:!1,attrs:[],location:this.getCurrentLocation(1)}}_createEndTagToken(){this.currentToken={type:G.END_TAG,tagName:"",tagID:s.UNKNOWN,selfClosing:!1,ackSelfClosing:!1,attrs:[],location:this.getCurrentLocation(2)}}_createCommentToken(t){this.currentToken={type:G.COMMENT,data:"",location:this.getCurrentLocation(t)}}_createDoctypeToken(t){this.currentToken={type:G.DOCTYPE,name:t,forceQuirks:!1,publicId:null,systemId:null,location:this.currentLocation}}_createCharacterToken(t,n){this.currentCharacterToken={type:t,chars:n,location:this.currentLocation}}_createAttr(t){this.currentAttr={name:t,value:""},this.currentLocation=this.getCurrentLocation(0)}_leaveAttrName(){var t,n;const r=this.currentToken;if(ds(r,this.currentAttr.name)===null){if(r.attrs.push(this.currentAttr),r.location&&this.currentLocation){const u=(t=(n=r.location).attrs)!==null&&t!==void 0?t:n.attrs=Object.create(null);u[this.currentAttr.name]=this.currentLocation,this._leaveAttrValue()}}else this._err(S.duplicateAttribute)}_leaveAttrValue(){this.currentLocation&&(this.currentLocation.endLine=this.preprocessor.line,this.currentLocation.endCol=this.preprocessor.col,this.currentLocation.endOffset=this.preprocessor.offset)}prepareToken(t){this._emitCurrentCharacterToken(t.location),this.currentToken=null,t.location&&(t.location.endLine=this.preprocessor.line,t.location.endCol=this.preprocessor.col+1,t.location.endOffset=this.preprocessor.offset+1),this.currentLocation=this.getCurrentLocation(-1)}emitCurrentTagToken(){const t=this.currentToken;this.prepareToken(t),t.tagID=Pt(t.tagName),t.type===G.START_TAG?(this.lastStartTagName=t.tagName,this.handler.onStartTag(t)):(t.attrs.length>0&&this._err(S.endTagWithAttributes),t.selfClosing&&this._err(S.endTagWithTrailingSolidus),this.handler.onEndTag(t)),this.preprocessor.dropParsedChunk()}emitCurrentComment(t){this.prepareToken(t),this.handler.onComment(t),this.preprocessor.dropParsedChunk()}emitCurrentDoctype(t){this.prepareToken(t),this.handler.onDoctype(t),this.preprocessor.dropParsedChunk()}_emitCurrentCharacterToken(t){if(this.currentCharacterToken){switch(t&&this.currentCharacterToken.location&&(this.currentCharacterToken.location.endLine=t.startLine,this.currentCharacterToken.location.endCol=t.startCol,this.currentCharacterToken.location.endOffset=t.startOffset),this.currentCharacterToken.type){case G.CHARACTER:{this.handler.onCharacter(this.currentCharacterToken);break}case G.NULL_CHARACTER:{this.handler.onNullCharacter(this.currentCharacterToken);break}case G.WHITESPACE_CHARACTER:{this.handler.onWhitespaceCharacter(this.currentCharacterToken);break}}this.currentCharacterToken=null}}_emitEOFToken(){const t=this.getCurrentLocation(0);t&&(t.endLine=t.startLine,t.endCol=t.startCol,t.endOffset=t.startOffset),this._emitCurrentCharacterToken(t),this.handler.onEof({type:G.EOF,location:t}),this.active=!1}_appendCharToCurrentCharacterToken(t,n){if(this.currentCharacterToken)if(this.currentCharacterToken.type===t){this.currentCharacterToken.chars+=n;return}else this.currentLocation=this.getCurrentLocation(0),this._emitCurrentCharacterToken(this.currentLocation),this.preprocessor.dropParsedChunk();this._createCharacterToken(t,n)}_emitCodePoint(t){const n=ps(t)?G.WHITESPACE_CHARACTER:t===p.NULL?G.NULL_CHARACTER:G.CHARACTER;this._appendCharToCurrentCharacterToken(n,String.fromCodePoint(t))}_emitChars(t){this._appendCharToCurrentCharacterToken(G.CHARACTER,t)}_startCharacterReference(){this.returnState=this.state,this.state=E.CHARACTER_REFERENCE,this.entityStartPos=this.preprocessor.pos,this.entityDecoder.startEntity(this._isCharacterReferenceInAttribute()?Ke.Attribute:Ke.Legacy)}_isCharacterReferenceInAttribute(){return this.returnState===E.ATTRIBUTE_VALUE_DOUBLE_QUOTED||this.returnState===E.ATTRIBUTE_VALUE_SINGLE_QUOTED||this.returnState===E.ATTRIBUTE_VALUE_UNQUOTED}_flushCodePointConsumedAsCharacterReference(t){this._isCharacterReferenceInAttribute()?this.currentAttr.value+=String.fromCodePoint(t):this._emitCodePoint(t)}_callState(t){switch(this.state){case E.DATA:{this._stateData(t);break}case E.RCDATA:{this._stateRcdata(t);break}case E.RAWTEXT:{this._stateRawtext(t);break}case E.SCRIPT_DATA:{this._stateScriptData(t);break}case E.PLAINTEXT:{this._statePlaintext(t);break}case E.TAG_OPEN:{this._stateTagOpen(t);break}case E.END_TAG_OPEN:{this._stateEndTagOpen(t);break}case E.TAG_NAME:{this._stateTagName(t);break}case E.RCDATA_LESS_THAN_SIGN:{this._stateRcdataLessThanSign(t);break}case E.RCDATA_END_TAG_OPEN:{this._stateRcdataEndTagOpen(t);break}case E.RCDATA_END_TAG_NAME:{this._stateRcdataEndTagName(t);break}case E.RAWTEXT_LESS_THAN_SIGN:{this._stateRawtextLessThanSign(t);break}case E.RAWTEXT_END_TAG_OPEN:{this._stateRawtextEndTagOpen(t);break}case E.RAWTEXT_END_TAG_NAME:{this._stateRawtextEndTagName(t);break}case E.SCRIPT_DATA_LESS_THAN_SIGN:{this._stateScriptDataLessThanSign(t);break}case E.SCRIPT_DATA_END_TAG_OPEN:{this._stateScriptDataEndTagOpen(t);break}case E.SCRIPT_DATA_END_TAG_NAME:{this._stateScriptDataEndTagName(t);break}case E.SCRIPT_DATA_ESCAPE_START:{this._stateScriptDataEscapeStart(t);break}case E.SCRIPT_DATA_ESCAPE_START_DASH:{this._stateScriptDataEscapeStartDash(t);break}case E.SCRIPT_DATA_ESCAPED:{this._stateScriptDataEscaped(t);break}case E.SCRIPT_DATA_ESCAPED_DASH:{this._stateScriptDataEscapedDash(t);break}case E.SCRIPT_DATA_ESCAPED_DASH_DASH:{this._stateScriptDataEscapedDashDash(t);break}case E.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN:{this._stateScriptDataEscapedLessThanSign(t);break}case E.SCRIPT_DATA_ESCAPED_END_TAG_OPEN:{this._stateScriptDataEscapedEndTagOpen(t);break}case E.SCRIPT_DATA_ESCAPED_END_TAG_NAME:{this._stateScriptDataEscapedEndTagName(t);break}case E.SCRIPT_DATA_DOUBLE_ESCAPE_START:{this._stateScriptDataDoubleEscapeStart(t);break}case E.SCRIPT_DATA_DOUBLE_ESCAPED:{this._stateScriptDataDoubleEscaped(t);break}case E.SCRIPT_DATA_DOUBLE_ESCAPED_DASH:{this._stateScriptDataDoubleEscapedDash(t);break}case E.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH:{this._stateScriptDataDoubleEscapedDashDash(t);break}case E.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN:{this._stateScriptDataDoubleEscapedLessThanSign(t);break}case E.SCRIPT_DATA_DOUBLE_ESCAPE_END:{this._stateScriptDataDoubleEscapeEnd(t);break}case E.BEFORE_ATTRIBUTE_NAME:{this._stateBeforeAttributeName(t);break}case E.ATTRIBUTE_NAME:{this._stateAttributeName(t);break}case E.AFTER_ATTRIBUTE_NAME:{this._stateAfterAttributeName(t);break}case E.BEFORE_ATTRIBUTE_VALUE:{this._stateBeforeAttributeValue(t);break}case E.ATTRIBUTE_VALUE_DOUBLE_QUOTED:{this._stateAttributeValueDoubleQuoted(t);break}case E.ATTRIBUTE_VALUE_SINGLE_QUOTED:{this._stateAttributeValueSingleQuoted(t);break}case E.ATTRIBUTE_VALUE_UNQUOTED:{this._stateAttributeValueUnquoted(t);break}case E.AFTER_ATTRIBUTE_VALUE_QUOTED:{this._stateAfterAttributeValueQuoted(t);break}case E.SELF_CLOSING_START_TAG:{this._stateSelfClosingStartTag(t);break}case E.BOGUS_COMMENT:{this._stateBogusComment(t);break}case E.MARKUP_DECLARATION_OPEN:{this._stateMarkupDeclarationOpen(t);break}case E.COMMENT_START:{this._stateCommentStart(t);break}case E.COMMENT_START_DASH:{this._stateCommentStartDash(t);break}case E.COMMENT:{this._stateComment(t);break}case E.COMMENT_LESS_THAN_SIGN:{this._stateCommentLessThanSign(t);break}case E.COMMENT_LESS_THAN_SIGN_BANG:{this._stateCommentLessThanSignBang(t);break}case E.COMMENT_LESS_THAN_SIGN_BANG_DASH:{this._stateCommentLessThanSignBangDash(t);break}case E.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH:{this._stateCommentLessThanSignBangDashDash(t);break}case E.COMMENT_END_DASH:{this._stateCommentEndDash(t);break}case E.COMMENT_END:{this._stateCommentEnd(t);break}case E.COMMENT_END_BANG:{this._stateCommentEndBang(t);break}case E.DOCTYPE:{this._stateDoctype(t);break}case E.BEFORE_DOCTYPE_NAME:{this._stateBeforeDoctypeName(t);break}case E.DOCTYPE_NAME:{this._stateDoctypeName(t);break}case E.AFTER_DOCTYPE_NAME:{this._stateAfterDoctypeName(t);break}case E.AFTER_DOCTYPE_PUBLIC_KEYWORD:{this._stateAfterDoctypePublicKeyword(t);break}case E.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER:{this._stateBeforeDoctypePublicIdentifier(t);break}case E.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED:{this._stateDoctypePublicIdentifierDoubleQuoted(t);break}case E.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED:{this._stateDoctypePublicIdentifierSingleQuoted(t);break}case E.AFTER_DOCTYPE_PUBLIC_IDENTIFIER:{this._stateAfterDoctypePublicIdentifier(t);break}case E.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS:{this._stateBetweenDoctypePublicAndSystemIdentifiers(t);break}case E.AFTER_DOCTYPE_SYSTEM_KEYWORD:{this._stateAfterDoctypeSystemKeyword(t);break}case E.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER:{this._stateBeforeDoctypeSystemIdentifier(t);break}case E.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED:{this._stateDoctypeSystemIdentifierDoubleQuoted(t);break}case E.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED:{this._stateDoctypeSystemIdentifierSingleQuoted(t);break}case E.AFTER_DOCTYPE_SYSTEM_IDENTIFIER:{this._stateAfterDoctypeSystemIdentifier(t);break}case E.BOGUS_DOCTYPE:{this._stateBogusDoctype(t);break}case E.CDATA_SECTION:{this._stateCdataSection(t);break}case E.CDATA_SECTION_BRACKET:{this._stateCdataSectionBracket(t);break}case E.CDATA_SECTION_END:{this._stateCdataSectionEnd(t);break}case E.CHARACTER_REFERENCE:{this._stateCharacterReference();break}case E.AMBIGUOUS_AMPERSAND:{this._stateAmbiguousAmpersand(t);break}default:throw new Error("Unknown state")}}_stateData(t){switch(t){case p.LESS_THAN_SIGN:{this.state=E.TAG_OPEN;break}case p.AMPERSAND:{this._startCharacterReference();break}case p.NULL:{this._err(S.unexpectedNullCharacter),this._emitCodePoint(t);break}case p.EOF:{this._emitEOFToken();break}default:this._emitCodePoint(t)}}_stateRcdata(t){switch(t){case p.AMPERSAND:{this._startCharacterReference();break}case p.LESS_THAN_SIGN:{this.state=E.RCDATA_LESS_THAN_SIGN;break}case p.NULL:{this._err(S.unexpectedNullCharacter),this._emitChars(ce);break}case p.EOF:{this._emitEOFToken();break}default:this._emitCodePoint(t)}}_stateRawtext(t){switch(t){case p.LESS_THAN_SIGN:{this.state=E.RAWTEXT_LESS_THAN_SIGN;break}case p.NULL:{this._err(S.unexpectedNullCharacter),this._emitChars(ce);break}case p.EOF:{this._emitEOFToken();break}default:this._emitCodePoint(t)}}_stateScriptData(t){switch(t){case p.LESS_THAN_SIGN:{this.state=E.SCRIPT_DATA_LESS_THAN_SIGN;break}case p.NULL:{this._err(S.unexpectedNullCharacter),this._emitChars(ce);break}case p.EOF:{this._emitEOFToken();break}default:this._emitCodePoint(t)}}_statePlaintext(t){switch(t){case p.NULL:{this._err(S.unexpectedNullCharacter),this._emitChars(ce);break}case p.EOF:{this._emitEOFToken();break}default:this._emitCodePoint(t)}}_stateTagOpen(t){if(nt(t))this._createStartTagToken(),this.state=E.TAG_NAME,this._stateTagName(t);else switch(t){case p.EXCLAMATION_MARK:{this.state=E.MARKUP_DECLARATION_OPEN;break}case p.SOLIDUS:{this.state=E.END_TAG_OPEN;break}case p.QUESTION_MARK:{this._err(S.unexpectedQuestionMarkInsteadOfTagName),this._createCommentToken(1),this.state=E.BOGUS_COMMENT,this._stateBogusComment(t);break}case p.EOF:{this._err(S.eofBeforeTagName),this._emitChars("<"),this._emitEOFToken();break}default:this._err(S.invalidFirstCharacterOfTagName),this._emitChars("<"),this.state=E.DATA,this._stateData(t)}}_stateEndTagOpen(t){if(nt(t))this._createEndTagToken(),this.state=E.TAG_NAME,this._stateTagName(t);else switch(t){case p.GREATER_THAN_SIGN:{this._err(S.missingEndTagName),this.state=E.DATA;break}case p.EOF:{this._err(S.eofBeforeTagName),this._emitChars("</"),this._emitEOFToken();break}default:this._err(S.invalidFirstCharacterOfTagName),this._createCommentToken(2),this.state=E.BOGUS_COMMENT,this._stateBogusComment(t)}}_stateTagName(t){const n=this.currentToken;switch(t){case p.SPACE:case p.LINE_FEED:case p.TABULATION:case p.FORM_FEED:{this.state=E.BEFORE_ATTRIBUTE_NAME;break}case p.SOLIDUS:{this.state=E.SELF_CLOSING_START_TAG;break}case p.GREATER_THAN_SIGN:{this.state=E.DATA,this.emitCurrentTagToken();break}case p.NULL:{this._err(S.unexpectedNullCharacter),n.tagName+=ce;break}case p.EOF:{this._err(S.eofInTag),this._emitEOFToken();break}default:n.tagName+=String.fromCodePoint(jt(t)?Tn(t):t)}}_stateRcdataLessThanSign(t){t===p.SOLIDUS?this.state=E.RCDATA_END_TAG_OPEN:(this._emitChars("<"),this.state=E.RCDATA,this._stateRcdata(t))}_stateRcdataEndTagOpen(t){nt(t)?(this.state=E.RCDATA_END_TAG_NAME,this._stateRcdataEndTagName(t)):(this._emitChars("</"),this.state=E.RCDATA,this._stateRcdata(t))}handleSpecialEndTag(t){if(!this.preprocessor.startsWith(this.lastStartTagName,!1))return!this._ensureHibernation();this._createEndTagToken();const n=this.currentToken;switch(n.tagName=this.lastStartTagName,this.preprocessor.peek(this.lastStartTagName.length)){case p.SPACE:case p.LINE_FEED:case p.TABULATION:case p.FORM_FEED:return this._advanceBy(this.lastStartTagName.length),this.state=E.BEFORE_ATTRIBUTE_NAME,!1;case p.SOLIDUS:return this._advanceBy(this.lastStartTagName.length),this.state=E.SELF_CLOSING_START_TAG,!1;case p.GREATER_THAN_SIGN:return this._advanceBy(this.lastStartTagName.length),this.emitCurrentTagToken(),this.state=E.DATA,!1;default:return!this._ensureHibernation()}}_stateRcdataEndTagName(t){this.handleSpecialEndTag(t)&&(this._emitChars("</"),this.state=E.RCDATA,this._stateRcdata(t))}_stateRawtextLessThanSign(t){t===p.SOLIDUS?this.state=E.RAWTEXT_END_TAG_OPEN:(this._emitChars("<"),this.state=E.RAWTEXT,this._stateRawtext(t))}_stateRawtextEndTagOpen(t){nt(t)?(this.state=E.RAWTEXT_END_TAG_NAME,this._stateRawtextEndTagName(t)):(this._emitChars("</"),this.state=E.RAWTEXT,this._stateRawtext(t))}_stateRawtextEndTagName(t){this.handleSpecialEndTag(t)&&(this._emitChars("</"),this.state=E.RAWTEXT,this._stateRawtext(t))}_stateScriptDataLessThanSign(t){switch(t){case p.SOLIDUS:{this.state=E.SCRIPT_DATA_END_TAG_OPEN;break}case p.EXCLAMATION_MARK:{this.state=E.SCRIPT_DATA_ESCAPE_START,this._emitChars("<!");break}default:this._emitChars("<"),this.state=E.SCRIPT_DATA,this._stateScriptData(t)}}_stateScriptDataEndTagOpen(t){nt(t)?(this.state=E.SCRIPT_DATA_END_TAG_NAME,this._stateScriptDataEndTagName(t)):(this._emitChars("</"),this.state=E.SCRIPT_DATA,this._stateScriptData(t))}_stateScriptDataEndTagName(t){this.handleSpecialEndTag(t)&&(this._emitChars("</"),this.state=E.SCRIPT_DATA,this._stateScriptData(t))}_stateScriptDataEscapeStart(t){t===p.HYPHEN_MINUS?(this.state=E.SCRIPT_DATA_ESCAPE_START_DASH,this._emitChars("-")):(this.state=E.SCRIPT_DATA,this._stateScriptData(t))}_stateScriptDataEscapeStartDash(t){t===p.HYPHEN_MINUS?(this.state=E.SCRIPT_DATA_ESCAPED_DASH_DASH,this._emitChars("-")):(this.state=E.SCRIPT_DATA,this._stateScriptData(t))}_stateScriptDataEscaped(t){switch(t){case p.HYPHEN_MINUS:{this.state=E.SCRIPT_DATA_ESCAPED_DASH,this._emitChars("-");break}case p.LESS_THAN_SIGN:{this.state=E.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break}case p.NULL:{this._err(S.unexpectedNullCharacter),this._emitChars(ce);break}case p.EOF:{this._err(S.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break}default:this._emitCodePoint(t)}}_stateScriptDataEscapedDash(t){switch(t){case p.HYPHEN_MINUS:{this.state=E.SCRIPT_DATA_ESCAPED_DASH_DASH,this._emitChars("-");break}case p.LESS_THAN_SIGN:{this.state=E.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break}case p.NULL:{this._err(S.unexpectedNullCharacter),this.state=E.SCRIPT_DATA_ESCAPED,this._emitChars(ce);break}case p.EOF:{this._err(S.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break}default:this.state=E.SCRIPT_DATA_ESCAPED,this._emitCodePoint(t)}}_stateScriptDataEscapedDashDash(t){switch(t){case p.HYPHEN_MINUS:{this._emitChars("-");break}case p.LESS_THAN_SIGN:{this.state=E.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break}case p.GREATER_THAN_SIGN:{this.state=E.SCRIPT_DATA,this._emitChars(">");break}case p.NULL:{this._err(S.unexpectedNullCharacter),this.state=E.SCRIPT_DATA_ESCAPED,this._emitChars(ce);break}case p.EOF:{this._err(S.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break}default:this.state=E.SCRIPT_DATA_ESCAPED,this._emitCodePoint(t)}}_stateScriptDataEscapedLessThanSign(t){t===p.SOLIDUS?this.state=E.SCRIPT_DATA_ESCAPED_END_TAG_OPEN:nt(t)?(this._emitChars("<"),this.state=E.SCRIPT_DATA_DOUBLE_ESCAPE_START,this._stateScriptDataDoubleEscapeStart(t)):(this._emitChars("<"),this.state=E.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(t))}_stateScriptDataEscapedEndTagOpen(t){nt(t)?(this.state=E.SCRIPT_DATA_ESCAPED_END_TAG_NAME,this._stateScriptDataEscapedEndTagName(t)):(this._emitChars("</"),this.state=E.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(t))}_stateScriptDataEscapedEndTagName(t){this.handleSpecialEndTag(t)&&(this._emitChars("</"),this.state=E.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(t))}_stateScriptDataDoubleEscapeStart(t){if(this.preprocessor.startsWith(Se.SCRIPT,!1)&&_i(this.preprocessor.peek(Se.SCRIPT.length))){this._emitCodePoint(t);for(let n=0;n<Se.SCRIPT.length;n++)this._emitCodePoint(this._consume());this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED}else this._ensureHibernation()||(this.state=E.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(t))}_stateScriptDataDoubleEscaped(t){switch(t){case p.HYPHEN_MINUS:{this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED_DASH,this._emitChars("-");break}case p.LESS_THAN_SIGN:{this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break}case p.NULL:{this._err(S.unexpectedNullCharacter),this._emitChars(ce);break}case p.EOF:{this._err(S.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break}default:this._emitCodePoint(t)}}_stateScriptDataDoubleEscapedDash(t){switch(t){case p.HYPHEN_MINUS:{this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH,this._emitChars("-");break}case p.LESS_THAN_SIGN:{this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break}case p.NULL:{this._err(S.unexpectedNullCharacter),this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitChars(ce);break}case p.EOF:{this._err(S.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break}default:this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitCodePoint(t)}}_stateScriptDataDoubleEscapedDashDash(t){switch(t){case p.HYPHEN_MINUS:{this._emitChars("-");break}case p.LESS_THAN_SIGN:{this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break}case p.GREATER_THAN_SIGN:{this.state=E.SCRIPT_DATA,this._emitChars(">");break}case p.NULL:{this._err(S.unexpectedNullCharacter),this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitChars(ce);break}case p.EOF:{this._err(S.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break}default:this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitCodePoint(t)}}_stateScriptDataDoubleEscapedLessThanSign(t){t===p.SOLIDUS?(this.state=E.SCRIPT_DATA_DOUBLE_ESCAPE_END,this._emitChars("/")):(this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED,this._stateScriptDataDoubleEscaped(t))}_stateScriptDataDoubleEscapeEnd(t){if(this.preprocessor.startsWith(Se.SCRIPT,!1)&&_i(this.preprocessor.peek(Se.SCRIPT.length))){this._emitCodePoint(t);for(let n=0;n<Se.SCRIPT.length;n++)this._emitCodePoint(this._consume());this.state=E.SCRIPT_DATA_ESCAPED}else this._ensureHibernation()||(this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED,this._stateScriptDataDoubleEscaped(t))}_stateBeforeAttributeName(t){switch(t){case p.SPACE:case p.LINE_FEED:case p.TABULATION:case p.FORM_FEED:break;case p.SOLIDUS:case p.GREATER_THAN_SIGN:case p.EOF:{this.state=E.AFTER_ATTRIBUTE_NAME,this._stateAfterAttributeName(t);break}case p.EQUALS_SIGN:{this._err(S.unexpectedEqualsSignBeforeAttributeName),this._createAttr("="),this.state=E.ATTRIBUTE_NAME;break}default:this._createAttr(""),this.state=E.ATTRIBUTE_NAME,this._stateAttributeName(t)}}_stateAttributeName(t){switch(t){case p.SPACE:case p.LINE_FEED:case p.TABULATION:case p.FORM_FEED:case p.SOLIDUS:case p.GREATER_THAN_SIGN:case p.EOF:{this._leaveAttrName(),this.state=E.AFTER_ATTRIBUTE_NAME,this._stateAfterAttributeName(t);break}case p.EQUALS_SIGN:{this._leaveAttrName(),this.state=E.BEFORE_ATTRIBUTE_VALUE;break}case p.QUOTATION_MARK:case p.APOSTROPHE:case p.LESS_THAN_SIGN:{this._err(S.unexpectedCharacterInAttributeName),this.currentAttr.name+=String.fromCodePoint(t);break}case p.NULL:{this._err(S.unexpectedNullCharacter),this.currentAttr.name+=ce;break}default:this.currentAttr.name+=String.fromCodePoint(jt(t)?Tn(t):t)}}_stateAfterAttributeName(t){switch(t){case p.SPACE:case p.LINE_FEED:case p.TABULATION:case p.FORM_FEED:break;case p.SOLIDUS:{this.state=E.SELF_CLOSING_START_TAG;break}case p.EQUALS_SIGN:{this.state=E.BEFORE_ATTRIBUTE_VALUE;break}case p.GREATER_THAN_SIGN:{this.state=E.DATA,this.emitCurrentTagToken();break}case p.EOF:{this._err(S.eofInTag),this._emitEOFToken();break}default:this._createAttr(""),this.state=E.ATTRIBUTE_NAME,this._stateAttributeName(t)}}_stateBeforeAttributeValue(t){switch(t){case p.SPACE:case p.LINE_FEED:case p.TABULATION:case p.FORM_FEED:break;case p.QUOTATION_MARK:{this.state=E.ATTRIBUTE_VALUE_DOUBLE_QUOTED;break}case p.APOSTROPHE:{this.state=E.ATTRIBUTE_VALUE_SINGLE_QUOTED;break}case p.GREATER_THAN_SIGN:{this._err(S.missingAttributeValue),this.state=E.DATA,this.emitCurrentTagToken();break}default:this.state=E.ATTRIBUTE_VALUE_UNQUOTED,this._stateAttributeValueUnquoted(t)}}_stateAttributeValueDoubleQuoted(t){switch(t){case p.QUOTATION_MARK:{this.state=E.AFTER_ATTRIBUTE_VALUE_QUOTED;break}case p.AMPERSAND:{this._startCharacterReference();break}case p.NULL:{this._err(S.unexpectedNullCharacter),this.currentAttr.value+=ce;break}case p.EOF:{this._err(S.eofInTag),this._emitEOFToken();break}default:this.currentAttr.value+=String.fromCodePoint(t)}}_stateAttributeValueSingleQuoted(t){switch(t){case p.APOSTROPHE:{this.state=E.AFTER_ATTRIBUTE_VALUE_QUOTED;break}case p.AMPERSAND:{this._startCharacterReference();break}case p.NULL:{this._err(S.unexpectedNullCharacter),this.currentAttr.value+=ce;break}case p.EOF:{this._err(S.eofInTag),this._emitEOFToken();break}default:this.currentAttr.value+=String.fromCodePoint(t)}}_stateAttributeValueUnquoted(t){switch(t){case p.SPACE:case p.LINE_FEED:case p.TABULATION:case p.FORM_FEED:{this._leaveAttrValue(),this.state=E.BEFORE_ATTRIBUTE_NAME;break}case p.AMPERSAND:{this._startCharacterReference();break}case p.GREATER_THAN_SIGN:{this._leaveAttrValue(),this.state=E.DATA,this.emitCurrentTagToken();break}case p.NULL:{this._err(S.unexpectedNullCharacter),this.currentAttr.value+=ce;break}case p.QUOTATION_MARK:case p.APOSTROPHE:case p.LESS_THAN_SIGN:case p.EQUALS_SIGN:case p.GRAVE_ACCENT:{this._err(S.unexpectedCharacterInUnquotedAttributeValue),this.currentAttr.value+=String.fromCodePoint(t);break}case p.EOF:{this._err(S.eofInTag),this._emitEOFToken();break}default:this.currentAttr.value+=String.fromCodePoint(t)}}_stateAfterAttributeValueQuoted(t){switch(t){case p.SPACE:case p.LINE_FEED:case p.TABULATION:case p.FORM_FEED:{this._leaveAttrValue(),this.state=E.BEFORE_ATTRIBUTE_NAME;break}case p.SOLIDUS:{this._leaveAttrValue(),this.state=E.SELF_CLOSING_START_TAG;break}case p.GREATER_THAN_SIGN:{this._leaveAttrValue(),this.state=E.DATA,this.emitCurrentTagToken();break}case p.EOF:{this._err(S.eofInTag),this._emitEOFToken();break}default:this._err(S.missingWhitespaceBetweenAttributes),this.state=E.BEFORE_ATTRIBUTE_NAME,this._stateBeforeAttributeName(t)}}_stateSelfClosingStartTag(t){switch(t){case p.GREATER_THAN_SIGN:{const n=this.currentToken;n.selfClosing=!0,this.state=E.DATA,this.emitCurrentTagToken();break}case p.EOF:{this._err(S.eofInTag),this._emitEOFToken();break}default:this._err(S.unexpectedSolidusInTag),this.state=E.BEFORE_ATTRIBUTE_NAME,this._stateBeforeAttributeName(t)}}_stateBogusComment(t){const n=this.currentToken;switch(t){case p.GREATER_THAN_SIGN:{this.state=E.DATA,this.emitCurrentComment(n);break}case p.EOF:{this.emitCurrentComment(n),this._emitEOFToken();break}case p.NULL:{this._err(S.unexpectedNullCharacter),n.data+=ce;break}default:n.data+=String.fromCodePoint(t)}}_stateMarkupDeclarationOpen(t){this._consumeSequenceIfMatch(Se.DASH_DASH,!0)?(this._createCommentToken(Se.DASH_DASH.length+1),this.state=E.COMMENT_START):this._consumeSequenceIfMatch(Se.DOCTYPE,!1)?(this.currentLocation=this.getCurrentLocation(Se.DOCTYPE.length+1),this.state=E.DOCTYPE):this._consumeSequenceIfMatch(Se.CDATA_START,!0)?this.inForeignNode?this.state=E.CDATA_SECTION:(this._err(S.cdataInHtmlContent),this._createCommentToken(Se.CDATA_START.length+1),this.currentToken.data="[CDATA[",this.state=E.BOGUS_COMMENT):this._ensureHibernation()||(this._err(S.incorrectlyOpenedComment),this._createCommentToken(2),this.state=E.BOGUS_COMMENT,this._stateBogusComment(t))}_stateCommentStart(t){switch(t){case p.HYPHEN_MINUS:{this.state=E.COMMENT_START_DASH;break}case p.GREATER_THAN_SIGN:{this._err(S.abruptClosingOfEmptyComment),this.state=E.DATA;const n=this.currentToken;this.emitCurrentComment(n);break}default:this.state=E.COMMENT,this._stateComment(t)}}_stateCommentStartDash(t){const n=this.currentToken;switch(t){case p.HYPHEN_MINUS:{this.state=E.COMMENT_END;break}case p.GREATER_THAN_SIGN:{this._err(S.abruptClosingOfEmptyComment),this.state=E.DATA,this.emitCurrentComment(n);break}case p.EOF:{this._err(S.eofInComment),this.emitCurrentComment(n),this._emitEOFToken();break}default:n.data+="-",this.state=E.COMMENT,this._stateComment(t)}}_stateComment(t){const n=this.currentToken;switch(t){case p.HYPHEN_MINUS:{this.state=E.COMMENT_END_DASH;break}case p.LESS_THAN_SIGN:{n.data+="<",this.state=E.COMMENT_LESS_THAN_SIGN;break}case p.NULL:{this._err(S.unexpectedNullCharacter),n.data+=ce;break}case p.EOF:{this._err(S.eofInComment),this.emitCurrentComment(n),this._emitEOFToken();break}default:n.data+=String.fromCodePoint(t)}}_stateCommentLessThanSign(t){const n=this.currentToken;switch(t){case p.EXCLAMATION_MARK:{n.data+="!",this.state=E.COMMENT_LESS_THAN_SIGN_BANG;break}case p.LESS_THAN_SIGN:{n.data+="<";break}default:this.state=E.COMMENT,this._stateComment(t)}}_stateCommentLessThanSignBang(t){t===p.HYPHEN_MINUS?this.state=E.COMMENT_LESS_THAN_SIGN_BANG_DASH:(this.state=E.COMMENT,this._stateComment(t))}_stateCommentLessThanSignBangDash(t){t===p.HYPHEN_MINUS?this.state=E.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH:(this.state=E.COMMENT_END_DASH,this._stateCommentEndDash(t))}_stateCommentLessThanSignBangDashDash(t){t!==p.GREATER_THAN_SIGN&&t!==p.EOF&&this._err(S.nestedComment),this.state=E.COMMENT_END,this._stateCommentEnd(t)}_stateCommentEndDash(t){const n=this.currentToken;switch(t){case p.HYPHEN_MINUS:{this.state=E.COMMENT_END;break}case p.EOF:{this._err(S.eofInComment),this.emitCurrentComment(n),this._emitEOFToken();break}default:n.data+="-",this.state=E.COMMENT,this._stateComment(t)}}_stateCommentEnd(t){const n=this.currentToken;switch(t){case p.GREATER_THAN_SIGN:{this.state=E.DATA,this.emitCurrentComment(n);break}case p.EXCLAMATION_MARK:{this.state=E.COMMENT_END_BANG;break}case p.HYPHEN_MINUS:{n.data+="-";break}case p.EOF:{this._err(S.eofInComment),this.emitCurrentComment(n),this._emitEOFToken();break}default:n.data+="--",this.state=E.COMMENT,this._stateComment(t)}}_stateCommentEndBang(t){const n=this.currentToken;switch(t){case p.HYPHEN_MINUS:{n.data+="--!",this.state=E.COMMENT_END_DASH;break}case p.GREATER_THAN_SIGN:{this._err(S.incorrectlyClosedComment),this.state=E.DATA,this.emitCurrentComment(n);break}case p.EOF:{this._err(S.eofInComment),this.emitCurrentComment(n),this._emitEOFToken();break}default:n.data+="--!",this.state=E.COMMENT,this._stateComment(t)}}_stateDoctype(t){switch(t){case p.SPACE:case p.LINE_FEED:case p.TABULATION:case p.FORM_FEED:{this.state=E.BEFORE_DOCTYPE_NAME;break}case p.GREATER_THAN_SIGN:{this.state=E.BEFORE_DOCTYPE_NAME,this._stateBeforeDoctypeName(t);break}case p.EOF:{this._err(S.eofInDoctype),this._createDoctypeToken(null);const n=this.currentToken;n.forceQuirks=!0,this.emitCurrentDoctype(n),this._emitEOFToken();break}default:this._err(S.missingWhitespaceBeforeDoctypeName),this.state=E.BEFORE_DOCTYPE_NAME,this._stateBeforeDoctypeName(t)}}_stateBeforeDoctypeName(t){if(jt(t))this._createDoctypeToken(String.fromCharCode(Tn(t))),this.state=E.DOCTYPE_NAME;else switch(t){case p.SPACE:case p.LINE_FEED:case p.TABULATION:case p.FORM_FEED:break;case p.NULL:{this._err(S.unexpectedNullCharacter),this._createDoctypeToken(ce),this.state=E.DOCTYPE_NAME;break}case p.GREATER_THAN_SIGN:{this._err(S.missingDoctypeName),this._createDoctypeToken(null);const n=this.currentToken;n.forceQuirks=!0,this.emitCurrentDoctype(n),this.state=E.DATA;break}case p.EOF:{this._err(S.eofInDoctype),this._createDoctypeToken(null);const n=this.currentToken;n.forceQuirks=!0,this.emitCurrentDoctype(n),this._emitEOFToken();break}default:this._createDoctypeToken(String.fromCodePoint(t)),this.state=E.DOCTYPE_NAME}}_stateDoctypeName(t){const n=this.currentToken;switch(t){case p.SPACE:case p.LINE_FEED:case p.TABULATION:case p.FORM_FEED:{this.state=E.AFTER_DOCTYPE_NAME;break}case p.GREATER_THAN_SIGN:{this.state=E.DATA,this.emitCurrentDoctype(n);break}case p.NULL:{this._err(S.unexpectedNullCharacter),n.name+=ce;break}case p.EOF:{this._err(S.eofInDoctype),n.forceQuirks=!0,this.emitCurrentDoctype(n),this._emitEOFToken();break}default:n.name+=String.fromCodePoint(jt(t)?Tn(t):t)}}_stateAfterDoctypeName(t){const n=this.currentToken;switch(t){case p.SPACE:case p.LINE_FEED:case p.TABULATION:case p.FORM_FEED:break;case p.GREATER_THAN_SIGN:{this.state=E.DATA,this.emitCurrentDoctype(n);break}case p.EOF:{this._err(S.eofInDoctype),n.forceQuirks=!0,this.emitCurrentDoctype(n),this._emitEOFToken();break}default:this._consumeSequenceIfMatch(Se.PUBLIC,!1)?this.state=E.AFTER_DOCTYPE_PUBLIC_KEYWORD:this._consumeSequenceIfMatch(Se.SYSTEM,!1)?this.state=E.AFTER_DOCTYPE_SYSTEM_KEYWORD:this._ensureHibernation()||(this._err(S.invalidCharacterSequenceAfterDoctypeName),n.forceQuirks=!0,this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(t))}}_stateAfterDoctypePublicKeyword(t){const n=this.currentToken;switch(t){case p.SPACE:case p.LINE_FEED:case p.TABULATION:case p.FORM_FEED:{this.state=E.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER;break}case p.QUOTATION_MARK:{this._err(S.missingWhitespaceAfterDoctypePublicKeyword),n.publicId="",this.state=E.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED;break}case p.APOSTROPHE:{this._err(S.missingWhitespaceAfterDoctypePublicKeyword),n.publicId="",this.state=E.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED;break}case p.GREATER_THAN_SIGN:{this._err(S.missingDoctypePublicIdentifier),n.forceQuirks=!0,this.state=E.DATA,this.emitCurrentDoctype(n);break}case p.EOF:{this._err(S.eofInDoctype),n.forceQuirks=!0,this.emitCurrentDoctype(n),this._emitEOFToken();break}default:this._err(S.missingQuoteBeforeDoctypePublicIdentifier),n.forceQuirks=!0,this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(t)}}_stateBeforeDoctypePublicIdentifier(t){const n=this.currentToken;switch(t){case p.SPACE:case p.LINE_FEED:case p.TABULATION:case p.FORM_FEED:break;case p.QUOTATION_MARK:{n.publicId="",this.state=E.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED;break}case p.APOSTROPHE:{n.publicId="",this.state=E.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED;break}case p.GREATER_THAN_SIGN:{this._err(S.missingDoctypePublicIdentifier),n.forceQuirks=!0,this.state=E.DATA,this.emitCurrentDoctype(n);break}case p.EOF:{this._err(S.eofInDoctype),n.forceQuirks=!0,this.emitCurrentDoctype(n),this._emitEOFToken();break}default:this._err(S.missingQuoteBeforeDoctypePublicIdentifier),n.forceQuirks=!0,this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(t)}}_stateDoctypePublicIdentifierDoubleQuoted(t){const n=this.currentToken;switch(t){case p.QUOTATION_MARK:{this.state=E.AFTER_DOCTYPE_PUBLIC_IDENTIFIER;break}case p.NULL:{this._err(S.unexpectedNullCharacter),n.publicId+=ce;break}case p.GREATER_THAN_SIGN:{this._err(S.abruptDoctypePublicIdentifier),n.forceQuirks=!0,this.emitCurrentDoctype(n),this.state=E.DATA;break}case p.EOF:{this._err(S.eofInDoctype),n.forceQuirks=!0,this.emitCurrentDoctype(n),this._emitEOFToken();break}default:n.publicId+=String.fromCodePoint(t)}}_stateDoctypePublicIdentifierSingleQuoted(t){const n=this.currentToken;switch(t){case p.APOSTROPHE:{this.state=E.AFTER_DOCTYPE_PUBLIC_IDENTIFIER;break}case p.NULL:{this._err(S.unexpectedNullCharacter),n.publicId+=ce;break}case p.GREATER_THAN_SIGN:{this._err(S.abruptDoctypePublicIdentifier),n.forceQuirks=!0,this.emitCurrentDoctype(n),this.state=E.DATA;break}case p.EOF:{this._err(S.eofInDoctype),n.forceQuirks=!0,this.emitCurrentDoctype(n),this._emitEOFToken();break}default:n.publicId+=String.fromCodePoint(t)}}_stateAfterDoctypePublicIdentifier(t){const n=this.currentToken;switch(t){case p.SPACE:case p.LINE_FEED:case p.TABULATION:case p.FORM_FEED:{this.state=E.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS;break}case p.GREATER_THAN_SIGN:{this.state=E.DATA,this.emitCurrentDoctype(n);break}case p.QUOTATION_MARK:{this._err(S.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers),n.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break}case p.APOSTROPHE:{this._err(S.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers),n.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break}case p.EOF:{this._err(S.eofInDoctype),n.forceQuirks=!0,this.emitCurrentDoctype(n),this._emitEOFToken();break}default:this._err(S.missingQuoteBeforeDoctypeSystemIdentifier),n.forceQuirks=!0,this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(t)}}_stateBetweenDoctypePublicAndSystemIdentifiers(t){const n=this.currentToken;switch(t){case p.SPACE:case p.LINE_FEED:case p.TABULATION:case p.FORM_FEED:break;case p.GREATER_THAN_SIGN:{this.emitCurrentDoctype(n),this.state=E.DATA;break}case p.QUOTATION_MARK:{n.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break}case p.APOSTROPHE:{n.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break}case p.EOF:{this._err(S.eofInDoctype),n.forceQuirks=!0,this.emitCurrentDoctype(n),this._emitEOFToken();break}default:this._err(S.missingQuoteBeforeDoctypeSystemIdentifier),n.forceQuirks=!0,this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(t)}}_stateAfterDoctypeSystemKeyword(t){const n=this.currentToken;switch(t){case p.SPACE:case p.LINE_FEED:case p.TABULATION:case p.FORM_FEED:{this.state=E.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER;break}case p.QUOTATION_MARK:{this._err(S.missingWhitespaceAfterDoctypeSystemKeyword),n.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break}case p.APOSTROPHE:{this._err(S.missingWhitespaceAfterDoctypeSystemKeyword),n.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break}case p.GREATER_THAN_SIGN:{this._err(S.missingDoctypeSystemIdentifier),n.forceQuirks=!0,this.state=E.DATA,this.emitCurrentDoctype(n);break}case p.EOF:{this._err(S.eofInDoctype),n.forceQuirks=!0,this.emitCurrentDoctype(n),this._emitEOFToken();break}default:this._err(S.missingQuoteBeforeDoctypeSystemIdentifier),n.forceQuirks=!0,this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(t)}}_stateBeforeDoctypeSystemIdentifier(t){const n=this.currentToken;switch(t){case p.SPACE:case p.LINE_FEED:case p.TABULATION:case p.FORM_FEED:break;case p.QUOTATION_MARK:{n.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break}case p.APOSTROPHE:{n.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break}case p.GREATER_THAN_SIGN:{this._err(S.missingDoctypeSystemIdentifier),n.forceQuirks=!0,this.state=E.DATA,this.emitCurrentDoctype(n);break}case p.EOF:{this._err(S.eofInDoctype),n.forceQuirks=!0,this.emitCurrentDoctype(n),this._emitEOFToken();break}default:this._err(S.missingQuoteBeforeDoctypeSystemIdentifier),n.forceQuirks=!0,this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(t)}}_stateDoctypeSystemIdentifierDoubleQuoted(t){const n=this.currentToken;switch(t){case p.QUOTATION_MARK:{this.state=E.AFTER_DOCTYPE_SYSTEM_IDENTIFIER;break}case p.NULL:{this._err(S.unexpectedNullCharacter),n.systemId+=ce;break}case p.GREATER_THAN_SIGN:{this._err(S.abruptDoctypeSystemIdentifier),n.forceQuirks=!0,this.emitCurrentDoctype(n),this.state=E.DATA;break}case p.EOF:{this._err(S.eofInDoctype),n.forceQuirks=!0,this.emitCurrentDoctype(n),this._emitEOFToken();break}default:n.systemId+=String.fromCodePoint(t)}}_stateDoctypeSystemIdentifierSingleQuoted(t){const n=this.currentToken;switch(t){case p.APOSTROPHE:{this.state=E.AFTER_DOCTYPE_SYSTEM_IDENTIFIER;break}case p.NULL:{this._err(S.unexpectedNullCharacter),n.systemId+=ce;break}case p.GREATER_THAN_SIGN:{this._err(S.abruptDoctypeSystemIdentifier),n.forceQuirks=!0,this.emitCurrentDoctype(n),this.state=E.DATA;break}case p.EOF:{this._err(S.eofInDoctype),n.forceQuirks=!0,this.emitCurrentDoctype(n),this._emitEOFToken();break}default:n.systemId+=String.fromCodePoint(t)}}_stateAfterDoctypeSystemIdentifier(t){const n=this.currentToken;switch(t){case p.SPACE:case p.LINE_FEED:case p.TABULATION:case p.FORM_FEED:break;case p.GREATER_THAN_SIGN:{this.emitCurrentDoctype(n),this.state=E.DATA;break}case p.EOF:{this._err(S.eofInDoctype),n.forceQuirks=!0,this.emitCurrentDoctype(n),this._emitEOFToken();break}default:this._err(S.unexpectedCharacterAfterDoctypeSystemIdentifier),this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(t)}}_stateBogusDoctype(t){const n=this.currentToken;switch(t){case p.GREATER_THAN_SIGN:{this.emitCurrentDoctype(n),this.state=E.DATA;break}case p.NULL:{this._err(S.unexpectedNullCharacter);break}case p.EOF:{this.emitCurrentDoctype(n),this._emitEOFToken();break}}}_stateCdataSection(t){switch(t){case p.RIGHT_SQUARE_BRACKET:{this.state=E.CDATA_SECTION_BRACKET;break}case p.EOF:{this._err(S.eofInCdata),this._emitEOFToken();break}default:this._emitCodePoint(t)}}_stateCdataSectionBracket(t){t===p.RIGHT_SQUARE_BRACKET?this.state=E.CDATA_SECTION_END:(this._emitChars("]"),this.state=E.CDATA_SECTION,this._stateCdataSection(t))}_stateCdataSectionEnd(t){switch(t){case p.GREATER_THAN_SIGN:{this.state=E.DATA;break}case p.RIGHT_SQUARE_BRACKET:{this._emitChars("]");break}default:this._emitChars("]]"),this.state=E.CDATA_SECTION,this._stateCdataSection(t)}}_stateCharacterReference(){let t=this.entityDecoder.write(this.preprocessor.html,this.preprocessor.pos);if(t<0)if(this.preprocessor.lastChunkWritten)t=this.entityDecoder.end();else{this.active=!1,this.preprocessor.pos=this.preprocessor.html.length-1,this.consumedAfterSnapshot=0,this.preprocessor.endOfChunkHit=!0;return}t===0?(this.preprocessor.pos=this.entityStartPos,this._flushCodePointConsumedAsCharacterReference(p.AMPERSAND),this.state=!this._isCharacterReferenceInAttribute()&&Ai(this.preprocessor.peek(1))?E.AMBIGUOUS_AMPERSAND:this.returnState):this.state=this.returnState}_stateAmbiguousAmpersand(t){Ai(t)?this._flushCodePointConsumedAsCharacterReference(t):(t===p.SEMICOLON&&this._err(S.unknownNamedCharacterReference),this.state=this.returnState,this._callState(t))}}const ms=new Set([s.DD,s.DT,s.LI,s.OPTGROUP,s.OPTION,s.P,s.RB,s.RP,s.RT,s.RTC]),Ci=new Set([...ms,s.CAPTION,s.COLGROUP,s.TBODY,s.TD,s.TFOOT,s.TH,s.THEAD,s.TR]),xn=new Set([s.APPLET,s.CAPTION,s.HTML,s.MARQUEE,s.OBJECT,s.TABLE,s.TD,s.TEMPLATE,s.TH]),Mp=new Set([...xn,s.OL,s.UL]),Bp=new Set([...xn,s.BUTTON]),ki=new Set([s.ANNOTATION_XML,s.MI,s.MN,s.MO,s.MS,s.MTEXT]),Si=new Set([s.DESC,s.FOREIGN_OBJECT,s.TITLE]),Fp=new Set([s.TR,s.TEMPLATE,s.HTML]),vp=new Set([s.TBODY,s.TFOOT,s.THEAD,s.TEMPLATE,s.HTML]),Up=new Set([s.TABLE,s.TEMPLATE,s.HTML]),Hp=new Set([s.TD,s.TH]);class zp{get currentTmplContentOrNode(){return this._isInTemplate()?this.treeAdapter.getTemplateContent(this.current):this.current}constructor(t,n,r){this.treeAdapter=n,this.handler=r,this.items=[],this.tagIDs=[],this.stackTop=-1,this.tmplCount=0,this.currentTagId=s.UNKNOWN,this.current=t}_indexOf(t){return this.items.lastIndexOf(t,this.stackTop)}_isInTemplate(){return this.currentTagId===s.TEMPLATE&&this.treeAdapter.getNamespaceURI(this.current)===O.HTML}_updateCurrentElement(){this.current=this.items[this.stackTop],this.currentTagId=this.tagIDs[this.stackTop]}push(t,n){this.stackTop++,this.items[this.stackTop]=t,this.current=t,this.tagIDs[this.stackTop]=n,this.currentTagId=n,this._isInTemplate()&&this.tmplCount++,this.handler.onItemPush(t,n,!0)}pop(){const t=this.current;this.tmplCount>0&&this._isInTemplate()&&this.tmplCount--,this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(t,!0)}replace(t,n){const r=this._indexOf(t);this.items[r]=n,r===this.stackTop&&(this.current=n)}insertAfter(t,n,r){const u=this._indexOf(t)+1;this.items.splice(u,0,n),this.tagIDs.splice(u,0,r),this.stackTop++,u===this.stackTop&&this._updateCurrentElement(),this.current&&this.currentTagId!==void 0&&this.handler.onItemPush(this.current,this.currentTagId,u===this.stackTop)}popUntilTagNamePopped(t){let n=this.stackTop+1;do n=this.tagIDs.lastIndexOf(t,n-1);while(n>0&&this.treeAdapter.getNamespaceURI(this.items[n])!==O.HTML);this.shortenToLength(Math.max(n,0))}shortenToLength(t){for(;this.stackTop>=t;){const n=this.current;this.tmplCount>0&&this._isInTemplate()&&(this.tmplCount-=1),this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(n,this.stackTop<t)}}popUntilElementPopped(t){const n=this._indexOf(t);this.shortenToLength(Math.max(n,0))}popUntilPopped(t,n){const r=this._indexOfTagNames(t,n);this.shortenToLength(Math.max(r,0))}popUntilNumberedHeaderPopped(){this.popUntilPopped(Nr,O.HTML)}popUntilTableCellPopped(){this.popUntilPopped(Hp,O.HTML)}popAllUpToHtmlElement(){this.tmplCount=0,this.shortenToLength(1)}_indexOfTagNames(t,n){for(let r=this.stackTop;r>=0;r--)if(t.has(this.tagIDs[r])&&this.treeAdapter.getNamespaceURI(this.items[r])===n)return r;return-1}clearBackTo(t,n){const r=this._indexOfTagNames(t,n);this.shortenToLength(r+1)}clearBackToTableContext(){this.clearBackTo(Up,O.HTML)}clearBackToTableBodyContext(){this.clearBackTo(vp,O.HTML)}clearBackToTableRowContext(){this.clearBackTo(Fp,O.HTML)}remove(t){const n=this._indexOf(t);n>=0&&(n===this.stackTop?this.pop():(this.items.splice(n,1),this.tagIDs.splice(n,1),this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(t,!1)))}tryPeekProperlyNestedBodyElement(){return this.stackTop>=1&&this.tagIDs[1]===s.BODY?this.items[1]:null}contains(t){return this._indexOf(t)>-1}getCommonAncestor(t){const n=this._indexOf(t)-1;return n>=0?this.items[n]:null}isRootHtmlElementCurrent(){return this.stackTop===0&&this.tagIDs[0]===s.HTML}hasInDynamicScope(t,n){for(let r=this.stackTop;r>=0;r--){const u=this.tagIDs[r];switch(this.treeAdapter.getNamespaceURI(this.items[r])){case O.HTML:{if(u===t)return!0;if(n.has(u))return!1;break}case O.SVG:{if(Si.has(u))return!1;break}case O.MATHML:{if(ki.has(u))return!1;break}}}return!0}hasInScope(t){return this.hasInDynamicScope(t,xn)}hasInListItemScope(t){return this.hasInDynamicScope(t,Mp)}hasInButtonScope(t){return this.hasInDynamicScope(t,Bp)}hasNumberedHeaderInScope(){for(let t=this.stackTop;t>=0;t--){const n=this.tagIDs[t];switch(this.treeAdapter.getNamespaceURI(this.items[t])){case O.HTML:{if(Nr.has(n))return!0;if(xn.has(n))return!1;break}case O.SVG:{if(Si.has(n))return!1;break}case O.MATHML:{if(ki.has(n))return!1;break}}}return!0}hasInTableScope(t){for(let n=this.stackTop;n>=0;n--)if(this.treeAdapter.getNamespaceURI(this.items[n])===O.HTML)switch(this.tagIDs[n]){case t:return!0;case s.TABLE:case s.HTML:return!1}return!0}hasTableBodyContextInTableScope(){for(let t=this.stackTop;t>=0;t--)if(this.treeAdapter.getNamespaceURI(this.items[t])===O.HTML)switch(this.tagIDs[t]){case s.TBODY:case s.THEAD:case s.TFOOT:return!0;case s.TABLE:case s.HTML:return!1}return!0}hasInSelectScope(t){for(let n=this.stackTop;n>=0;n--)if(this.treeAdapter.getNamespaceURI(this.items[n])===O.HTML)switch(this.tagIDs[n]){case t:return!0;case s.OPTION:case s.OPTGROUP:break;default:return!1}return!0}generateImpliedEndTags(){for(;this.currentTagId!==void 0&&ms.has(this.currentTagId);)this.pop()}generateImpliedEndTagsThoroughly(){for(;this.currentTagId!==void 0&&Ci.has(this.currentTagId);)this.pop()}generateImpliedEndTagsWithExclusion(t){for(;this.currentTagId!==void 0&&this.currentTagId!==t&&Ci.has(this.currentTagId);)this.pop()}}const lr=3;var Ve;(function(e){e[e.Marker=0]="Marker",e[e.Element=1]="Element"})(Ve||(Ve={}));const yi={type:Ve.Marker};class Yp{constructor(t){this.treeAdapter=t,this.entries=[],this.bookmark=null}_getNoahArkConditionCandidates(t,n){const r=[],u=n.length,a=this.treeAdapter.getTagName(t),i=this.treeAdapter.getNamespaceURI(t);for(let o=0;o<this.entries.length;o++){const l=this.entries[o];if(l.type===Ve.Marker)break;const{element:c}=l;if(this.treeAdapter.getTagName(c)===a&&this.treeAdapter.getNamespaceURI(c)===i){const h=this.treeAdapter.getAttrList(c);h.length===u&&r.push({idx:o,attrs:h})}}return r}_ensureNoahArkCondition(t){if(this.entries.length<lr)return;const n=this.treeAdapter.getAttrList(t),r=this._getNoahArkConditionCandidates(t,n);if(r.length<lr)return;const u=new Map(n.map(i=>[i.name,i.value]));let a=0;for(let i=0;i<r.length;i++){const o=r[i];o.attrs.every(l=>u.get(l.name)===l.value)&&(a+=1,a>=lr&&this.entries.splice(o.idx,1))}}insertMarker(){this.entries.unshift(yi)}pushElement(t,n){this._ensureNoahArkCondition(t),this.entries.unshift({type:Ve.Element,element:t,token:n})}insertElementAfterBookmark(t,n){const r=this.entries.indexOf(this.bookmark);this.entries.splice(r,0,{type:Ve.Element,element:t,token:n})}removeEntry(t){const n=this.entries.indexOf(t);n!==-1&&this.entries.splice(n,1)}clearToLastMarker(){const t=this.entries.indexOf(yi);t===-1?this.entries.length=0:this.entries.splice(0,t+1)}getElementEntryInScopeWithTagName(t){const n=this.entries.find(r=>r.type===Ve.Marker||this.treeAdapter.getTagName(r.element)===t);return n&&n.type===Ve.Element?n:null}getElementEntry(t){return this.entries.find(n=>n.type===Ve.Element&&n.element===t)}}const rt={createDocument(){return{nodeName:"#document",mode:Fe.NO_QUIRKS,childNodes:[]}},createDocumentFragment(){return{nodeName:"#document-fragment",childNodes:[]}},createElement(e,t,n){return{nodeName:e,tagName:e,attrs:n,namespaceURI:t,childNodes:[],parentNode:null}},createCommentNode(e){return{nodeName:"#comment",data:e,parentNode:null}},createTextNode(e){return{nodeName:"#text",value:e,parentNode:null}},appendChild(e,t){e.childNodes.push(t),t.parentNode=e},insertBefore(e,t,n){const r=e.childNodes.indexOf(n);e.childNodes.splice(r,0,t),t.parentNode=e},setTemplateContent(e,t){e.content=t},getTemplateContent(e){return e.content},setDocumentType(e,t,n,r){const u=e.childNodes.find(a=>a.nodeName==="#documentType");if(u)u.name=t,u.publicId=n,u.systemId=r;else{const a={nodeName:"#documentType",name:t,publicId:n,systemId:r,parentNode:null};rt.appendChild(e,a)}},setDocumentMode(e,t){e.mode=t},getDocumentMode(e){return e.mode},detachNode(e){if(e.parentNode){const t=e.parentNode.childNodes.indexOf(e);e.parentNode.childNodes.splice(t,1),e.parentNode=null}},insertText(e,t){if(e.childNodes.length>0){const n=e.childNodes[e.childNodes.length-1];if(rt.isTextNode(n)){n.value+=t;return}}rt.appendChild(e,rt.createTextNode(t))},insertTextBefore(e,t,n){const r=e.childNodes[e.childNodes.indexOf(n)-1];r&&rt.isTextNode(r)?r.value+=t:rt.insertBefore(e,rt.createTextNode(t),n)},adoptAttributes(e,t){const n=new Set(e.attrs.map(r=>r.name));for(let r=0;r<t.length;r++)n.has(t[r].name)||e.attrs.push(t[r])},getFirstChild(e){return e.childNodes[0]},getChildNodes(e){return e.childNodes},getParentNode(e){return e.parentNode},getAttrList(e){return e.attrs},getTagName(e){return e.tagName},getNamespaceURI(e){return e.namespaceURI},getTextNodeContent(e){return e.value},getCommentNodeContent(e){return e.data},getDocumentTypeNodeName(e){return e.name},getDocumentTypeNodePublicId(e){return e.publicId},getDocumentTypeNodeSystemId(e){return e.systemId},isTextNode(e){return e.nodeName==="#text"},isCommentNode(e){return e.nodeName==="#comment"},isDocumentTypeNode(e){return e.nodeName==="#documentType"},isElementNode(e){return Object.prototype.hasOwnProperty.call(e,"tagName")},setNodeSourceCodeLocation(e,t){e.sourceCodeLocation=t},getNodeSourceCodeLocation(e){return e.sourceCodeLocation},updateNodeSourceCodeLocation(e,t){e.sourceCodeLocation={...e.sourceCodeLocation,...t}}},Es="html",qp="about:legacy-compat",jp="http://www.ibm.com/data/dtd/v11/ibmxhtml1-transitional.dtd",gs=["+//silmaril//dtd html pro v0r11 19970101//","-//as//dtd html 3.0 aswedit + extensions//","-//advasoft ltd//dtd html 3.0 aswedit + extensions//","-//ietf//dtd html 2.0 level 1//","-//ietf//dtd html 2.0 level 2//","-//ietf//dtd html 2.0 strict level 1//","-//ietf//dtd html 2.0 strict level 2//","-//ietf//dtd html 2.0 strict//","-//ietf//dtd html 2.0//","-//ietf//dtd html 2.1e//","-//ietf//dtd html 3.0//","-//ietf//dtd html 3.2 final//","-//ietf//dtd html 3.2//","-//ietf//dtd html 3//","-//ietf//dtd html level 0//","-//ietf//dtd html level 1//","-//ietf//dtd html level 2//","-//ietf//dtd html level 3//","-//ietf//dtd html strict level 0//","-//ietf//dtd html strict level 1//","-//ietf//dtd html strict level 2//","-//ietf//dtd html strict level 3//","-//ietf//dtd html strict//","-//ietf//dtd html//","-//metrius//dtd metrius presentational//","-//microsoft//dtd internet explorer 2.0 html strict//","-//microsoft//dtd internet explorer 2.0 html//","-//microsoft//dtd internet explorer 2.0 tables//","-//microsoft//dtd internet explorer 3.0 html strict//","-//microsoft//dtd internet explorer 3.0 html//","-//microsoft//dtd internet explorer 3.0 tables//","-//netscape comm. corp.//dtd html//","-//netscape comm. corp.//dtd strict html//","-//o'reilly and associates//dtd html 2.0//","-//o'reilly and associates//dtd html extended 1.0//","-//o'reilly and associates//dtd html extended relaxed 1.0//","-//sq//dtd html 2.0 hotmetal + extensions//","-//softquad software//dtd hotmetal pro 6.0::19990601::extensions to html 4.0//","-//softquad//dtd hotmetal pro 4.0::19971010::extensions to html 4.0//","-//spyglass//dtd html 2.0 extended//","-//sun microsystems corp.//dtd hotjava html//","-//sun microsystems corp.//dtd hotjava strict html//","-//w3c//dtd html 3 1995-03-24//","-//w3c//dtd html 3.2 draft//","-//w3c//dtd html 3.2 final//","-//w3c//dtd html 3.2//","-//w3c//dtd html 3.2s draft//","-//w3c//dtd html 4.0 frameset//","-//w3c//dtd html 4.0 transitional//","-//w3c//dtd html experimental 19960712//","-//w3c//dtd html experimental 970421//","-//w3c//dtd w3 html//","-//w3o//dtd w3 html 3.0//","-//webtechs//dtd mozilla html 2.0//","-//webtechs//dtd mozilla html//"],Vp=[...gs,"-//w3c//dtd html 4.01 frameset//","-//w3c//dtd html 4.01 transitional//"],$p=new Set(["-//w3o//dtd w3 html strict 3.0//en//","-/w3c/dtd html 4.0 transitional/en","html"]),Ts=["-//w3c//dtd xhtml 1.0 frameset//","-//w3c//dtd xhtml 1.0 transitional//"],Wp=[...Ts,"-//w3c//dtd html 4.01 frameset//","-//w3c//dtd html 4.01 transitional//"];function Ii(e,t){return t.some(n=>e.startsWith(n))}function Qp(e){return e.name===Es&&e.publicId===null&&(e.systemId===null||e.systemId===qp)}function Xp(e){if(e.name!==Es)return Fe.QUIRKS;const{systemId:t}=e;if(t&&t.toLowerCase()===jp)return Fe.QUIRKS;let{publicId:n}=e;if(n!==null){if(n=n.toLowerCase(),$p.has(n))return Fe.QUIRKS;let r=t===null?Vp:gs;if(Ii(n,r))return Fe.QUIRKS;if(r=t===null?Ts:Wp,Ii(n,r))return Fe.LIMITED_QUIRKS}return Fe.NO_QUIRKS}const xi={TEXT_HTML:"text/html",APPLICATION_XML:"application/xhtml+xml"},Kp="definitionurl",Gp="definitionURL",Jp=new Map(["attributeName","attributeType","baseFrequency","baseProfile","calcMode","clipPathUnits","diffuseConstant","edgeMode","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector","zoomAndPan"].map(e=>[e.toLowerCase(),e])),Zp=new Map([["xlink:actuate",{prefix:"xlink",name:"actuate",namespace:O.XLINK}],["xlink:arcrole",{prefix:"xlink",name:"arcrole",namespace:O.XLINK}],["xlink:href",{prefix:"xlink",name:"href",namespace:O.XLINK}],["xlink:role",{prefix:"xlink",name:"role",namespace:O.XLINK}],["xlink:show",{prefix:"xlink",name:"show",namespace:O.XLINK}],["xlink:title",{prefix:"xlink",name:"title",namespace:O.XLINK}],["xlink:type",{prefix:"xlink",name:"type",namespace:O.XLINK}],["xml:lang",{prefix:"xml",name:"lang",namespace:O.XML}],["xml:space",{prefix:"xml",name:"space",namespace:O.XML}],["xmlns",{prefix:"",name:"xmlns",namespace:O.XMLNS}],["xmlns:xlink",{prefix:"xmlns",name:"xlink",namespace:O.XMLNS}]]),em=new Map(["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","textPath"].map(e=>[e.toLowerCase(),e])),tm=new Set([s.B,s.BIG,s.BLOCKQUOTE,s.BODY,s.BR,s.CENTER,s.CODE,s.DD,s.DIV,s.DL,s.DT,s.EM,s.EMBED,s.H1,s.H2,s.H3,s.H4,s.H5,s.H6,s.HEAD,s.HR,s.I,s.IMG,s.LI,s.LISTING,s.MENU,s.META,s.NOBR,s.OL,s.P,s.PRE,s.RUBY,s.S,s.SMALL,s.SPAN,s.STRONG,s.STRIKE,s.SUB,s.SUP,s.TABLE,s.TT,s.U,s.UL,s.VAR]);function nm(e){const t=e.tagID;return t===s.FONT&&e.attrs.some(({name:r})=>r===ct.COLOR||r===ct.SIZE||r===ct.FACE)||tm.has(t)}function bs(e){for(let t=0;t<e.attrs.length;t++)if(e.attrs[t].name===Kp){e.attrs[t].name=Gp;break}}function As(e){for(let t=0;t<e.attrs.length;t++){const n=Jp.get(e.attrs[t].name);n!=null&&(e.attrs[t].name=n)}}function iu(e){for(let t=0;t<e.attrs.length;t++){const n=Zp.get(e.attrs[t].name);n&&(e.attrs[t].prefix=n.prefix,e.attrs[t].name=n.name,e.attrs[t].namespace=n.namespace)}}function rm(e){const t=em.get(e.tagName);t!=null&&(e.tagName=t,e.tagID=Pt(e.tagName))}function um(e,t){return t===O.MATHML&&(e===s.MI||e===s.MO||e===s.MN||e===s.MS||e===s.MTEXT)}function im(e,t,n){if(t===O.MATHML&&e===s.ANNOTATION_XML){for(let r=0;r<n.length;r++)if(n[r].name===ct.ENCODING){const u=n[r].value.toLowerCase();return u===xi.TEXT_HTML||u===xi.APPLICATION_XML}}return t===O.SVG&&(e===s.FOREIGN_OBJECT||e===s.DESC||e===s.TITLE)}function am(e,t,n,r){return(!r||r===O.HTML)&&im(e,t,n)||(!r||r===O.MATHML)&&um(e,t)}const sm="hidden",om=8,lm=3;var g;(function(e){e[e.INITIAL=0]="INITIAL",e[e.BEFORE_HTML=1]="BEFORE_HTML",e[e.BEFORE_HEAD=2]="BEFORE_HEAD",e[e.IN_HEAD=3]="IN_HEAD",e[e.IN_HEAD_NO_SCRIPT=4]="IN_HEAD_NO_SCRIPT",e[e.AFTER_HEAD=5]="AFTER_HEAD",e[e.IN_BODY=6]="IN_BODY",e[e.TEXT=7]="TEXT",e[e.IN_TABLE=8]="IN_TABLE",e[e.IN_TABLE_TEXT=9]="IN_TABLE_TEXT",e[e.IN_CAPTION=10]="IN_CAPTION",e[e.IN_COLUMN_GROUP=11]="IN_COLUMN_GROUP",e[e.IN_TABLE_BODY=12]="IN_TABLE_BODY",e[e.IN_ROW=13]="IN_ROW",e[e.IN_CELL=14]="IN_CELL",e[e.IN_SELECT=15]="IN_SELECT",e[e.IN_SELECT_IN_TABLE=16]="IN_SELECT_IN_TABLE",e[e.IN_TEMPLATE=17]="IN_TEMPLATE",e[e.AFTER_BODY=18]="AFTER_BODY",e[e.IN_FRAMESET=19]="IN_FRAMESET",e[e.AFTER_FRAMESET=20]="AFTER_FRAMESET",e[e.AFTER_AFTER_BODY=21]="AFTER_AFTER_BODY",e[e.AFTER_AFTER_FRAMESET=22]="AFTER_AFTER_FRAMESET"})(g||(g={}));const cm={startLine:-1,startCol:-1,startOffset:-1,endLine:-1,endCol:-1,endOffset:-1},_s=new Set([s.TABLE,s.TBODY,s.TFOOT,s.THEAD,s.TR]),Ni={scriptingEnabled:!0,sourceCodeLocationInfo:!1,treeAdapter:rt,onParseError:null};class Oi{constructor(t,n,r=null,u=null){this.fragmentContext=r,this.scriptHandler=u,this.currentToken=null,this.stopped=!1,this.insertionMode=g.INITIAL,this.originalInsertionMode=g.INITIAL,this.headElement=null,this.formElement=null,this.currentNotInHTML=!1,this.tmplInsertionModeStack=[],this.pendingCharacterTokens=[],this.hasNonWhitespacePendingCharacterToken=!1,this.framesetOk=!0,this.skipNextNewLine=!1,this.fosterParentingEnabled=!1,this.options={...Ni,...t},this.treeAdapter=this.options.treeAdapter,this.onParseError=this.options.onParseError,this.onParseError&&(this.options.sourceCodeLocationInfo=!0),this.document=n??this.treeAdapter.createDocument(),this.tokenizer=new wp(this.options,this),this.activeFormattingElements=new Yp(this.treeAdapter),this.fragmentContextID=r?Pt(this.treeAdapter.getTagName(r)):s.UNKNOWN,this._setContextModes(r??this.document,this.fragmentContextID),this.openElements=new zp(this.document,this.treeAdapter,this)}static parse(t,n){const r=new this(n);return r.tokenizer.write(t,!0),r.document}static getFragmentParser(t,n){const r={...Ni,...n};t??(t=r.treeAdapter.createElement(C.TEMPLATE,O.HTML,[]));const u=r.treeAdapter.createElement("documentmock",O.HTML,[]),a=new this(r,u,t);return a.fragmentContextID===s.TEMPLATE&&a.tmplInsertionModeStack.unshift(g.IN_TEMPLATE),a._initTokenizerForFragmentParsing(),a._insertFakeRootElement(),a._resetInsertionMode(),a._findFormInFragmentContext(),a}getFragment(){const t=this.treeAdapter.getFirstChild(this.document),n=this.treeAdapter.createDocumentFragment();return this._adoptNodes(t,n),n}_err(t,n,r){var u;if(!this.onParseError)return;const a=(u=t.location)!==null&&u!==void 0?u:cm,i={code:n,startLine:a.startLine,startCol:a.startCol,startOffset:a.startOffset,endLine:r?a.startLine:a.endLine,endCol:r?a.startCol:a.endCol,endOffset:r?a.startOffset:a.endOffset};this.onParseError(i)}onItemPush(t,n,r){var u,a;(a=(u=this.treeAdapter).onItemPush)===null||a===void 0||a.call(u,t),r&&this.openElements.stackTop>0&&this._setContextModes(t,n)}onItemPop(t,n){var r,u;if(this.options.sourceCodeLocationInfo&&this._setEndLocation(t,this.currentToken),(u=(r=this.treeAdapter).onItemPop)===null||u===void 0||u.call(r,t,this.openElements.current),n){let a,i;this.openElements.stackTop===0&&this.fragmentContext?(a=this.fragmentContext,i=this.fragmentContextID):{current:a,currentTagId:i}=this.openElements,this._setContextModes(a,i)}}_setContextModes(t,n){const r=t===this.document||t&&this.treeAdapter.getNamespaceURI(t)===O.HTML;this.currentNotInHTML=!r,this.tokenizer.inForeignNode=!r&&t!==void 0&&n!==void 0&&!this._isIntegrationPoint(n,t)}_switchToTextParsing(t,n){this._insertElement(t,O.HTML),this.tokenizer.state=n,this.originalInsertionMode=this.insertionMode,this.insertionMode=g.TEXT}switchToPlaintextParsing(){this.insertionMode=g.TEXT,this.originalInsertionMode=g.IN_BODY,this.tokenizer.state=fe.PLAINTEXT}_getAdjustedCurrentElement(){return this.openElements.stackTop===0&&this.fragmentContext?this.fragmentContext:this.openElements.current}_findFormInFragmentContext(){let t=this.fragmentContext;for(;t;){if(this.treeAdapter.getTagName(t)===C.FORM){this.formElement=t;break}t=this.treeAdapter.getParentNode(t)}}_initTokenizerForFragmentParsing(){if(!(!this.fragmentContext||this.treeAdapter.getNamespaceURI(this.fragmentContext)!==O.HTML))switch(this.fragmentContextID){case s.TITLE:case s.TEXTAREA:{this.tokenizer.state=fe.RCDATA;break}case s.STYLE:case s.XMP:case s.IFRAME:case s.NOEMBED:case s.NOFRAMES:case s.NOSCRIPT:{this.tokenizer.state=fe.RAWTEXT;break}case s.SCRIPT:{this.tokenizer.state=fe.SCRIPT_DATA;break}case s.PLAINTEXT:{this.tokenizer.state=fe.PLAINTEXT;break}}}_setDocumentType(t){const n=t.name||"",r=t.publicId||"",u=t.systemId||"";if(this.treeAdapter.setDocumentType(this.document,n,r,u),t.location){const i=this.treeAdapter.getChildNodes(this.document).find(o=>this.treeAdapter.isDocumentTypeNode(o));i&&this.treeAdapter.setNodeSourceCodeLocation(i,t.location)}}_attachElementToTree(t,n){if(this.options.sourceCodeLocationInfo){const r=n&&{...n,startTag:n};this.treeAdapter.setNodeSourceCodeLocation(t,r)}if(this._shouldFosterParentOnInsertion())this._fosterParentElement(t);else{const r=this.openElements.currentTmplContentOrNode;this.treeAdapter.appendChild(r??this.document,t)}}_appendElement(t,n){const r=this.treeAdapter.createElement(t.tagName,n,t.attrs);this._attachElementToTree(r,t.location)}_insertElement(t,n){const r=this.treeAdapter.createElement(t.tagName,n,t.attrs);this._attachElementToTree(r,t.location),this.openElements.push(r,t.tagID)}_insertFakeElement(t,n){const r=this.treeAdapter.createElement(t,O.HTML,[]);this._attachElementToTree(r,null),this.openElements.push(r,n)}_insertTemplate(t){const n=this.treeAdapter.createElement(t.tagName,O.HTML,t.attrs),r=this.treeAdapter.createDocumentFragment();this.treeAdapter.setTemplateContent(n,r),this._attachElementToTree(n,t.location),this.openElements.push(n,t.tagID),this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(r,null)}_insertFakeRootElement(){const t=this.treeAdapter.createElement(C.HTML,O.HTML,[]);this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(t,null),this.treeAdapter.appendChild(this.openElements.current,t),this.openElements.push(t,s.HTML)}_appendCommentNode(t,n){const r=this.treeAdapter.createCommentNode(t.data);this.treeAdapter.appendChild(n,r),this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(r,t.location)}_insertCharacters(t){let n,r;if(this._shouldFosterParentOnInsertion()?({parent:n,beforeElement:r}=this._findFosterParentingLocation(),r?this.treeAdapter.insertTextBefore(n,t.chars,r):this.treeAdapter.insertText(n,t.chars)):(n=this.openElements.currentTmplContentOrNode,this.treeAdapter.insertText(n,t.chars)),!t.location)return;const u=this.treeAdapter.getChildNodes(n),a=r?u.lastIndexOf(r):u.length,i=u[a-1];if(this.treeAdapter.getNodeSourceCodeLocation(i)){const{endLine:l,endCol:c,endOffset:h}=t.location;this.treeAdapter.updateNodeSourceCodeLocation(i,{endLine:l,endCol:c,endOffset:h})}else this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(i,t.location)}_adoptNodes(t,n){for(let r=this.treeAdapter.getFirstChild(t);r;r=this.treeAdapter.getFirstChild(t))this.treeAdapter.detachNode(r),this.treeAdapter.appendChild(n,r)}_setEndLocation(t,n){if(this.treeAdapter.getNodeSourceCodeLocation(t)&&n.location){const r=n.location,u=this.treeAdapter.getTagName(t),a=n.type===G.END_TAG&&u===n.tagName?{endTag:{...r},endLine:r.endLine,endCol:r.endCol,endOffset:r.endOffset}:{endLine:r.startLine,endCol:r.startCol,endOffset:r.startOffset};this.treeAdapter.updateNodeSourceCodeLocation(t,a)}}shouldProcessStartTagTokenInForeignContent(t){if(!this.currentNotInHTML)return!1;let n,r;return this.openElements.stackTop===0&&this.fragmentContext?(n=this.fragmentContext,r=this.fragmentContextID):{current:n,currentTagId:r}=this.openElements,t.tagID===s.SVG&&this.treeAdapter.getTagName(n)===C.ANNOTATION_XML&&this.treeAdapter.getNamespaceURI(n)===O.MATHML?!1:this.tokenizer.inForeignNode||(t.tagID===s.MGLYPH||t.tagID===s.MALIGNMARK)&&r!==void 0&&!this._isIntegrationPoint(r,n,O.HTML)}_processToken(t){switch(t.type){case G.CHARACTER:{this.onCharacter(t);break}case G.NULL_CHARACTER:{this.onNullCharacter(t);break}case G.COMMENT:{this.onComment(t);break}case G.DOCTYPE:{this.onDoctype(t);break}case G.START_TAG:{this._processStartTag(t);break}case G.END_TAG:{this.onEndTag(t);break}case G.EOF:{this.onEof(t);break}case G.WHITESPACE_CHARACTER:{this.onWhitespaceCharacter(t);break}}}_isIntegrationPoint(t,n,r){const u=this.treeAdapter.getNamespaceURI(n),a=this.treeAdapter.getAttrList(n);return am(t,u,a,r)}_reconstructActiveFormattingElements(){const t=this.activeFormattingElements.entries.length;if(t){const n=this.activeFormattingElements.entries.findIndex(u=>u.type===Ve.Marker||this.openElements.contains(u.element)),r=n===-1?t-1:n-1;for(let u=r;u>=0;u--){const a=this.activeFormattingElements.entries[u];this._insertElement(a.token,this.treeAdapter.getNamespaceURI(a.element)),a.element=this.openElements.current}}}_closeTableCell(){this.openElements.generateImpliedEndTags(),this.openElements.popUntilTableCellPopped(),this.activeFormattingElements.clearToLastMarker(),this.insertionMode=g.IN_ROW}_closePElement(){this.openElements.generateImpliedEndTagsWithExclusion(s.P),this.openElements.popUntilTagNamePopped(s.P)}_resetInsertionMode(){for(let t=this.openElements.stackTop;t>=0;t--)switch(t===0&&this.fragmentContext?this.fragmentContextID:this.openElements.tagIDs[t]){case s.TR:{this.insertionMode=g.IN_ROW;return}case s.TBODY:case s.THEAD:case s.TFOOT:{this.insertionMode=g.IN_TABLE_BODY;return}case s.CAPTION:{this.insertionMode=g.IN_CAPTION;return}case s.COLGROUP:{this.insertionMode=g.IN_COLUMN_GROUP;return}case s.TABLE:{this.insertionMode=g.IN_TABLE;return}case s.BODY:{this.insertionMode=g.IN_BODY;return}case s.FRAMESET:{this.insertionMode=g.IN_FRAMESET;return}case s.SELECT:{this._resetInsertionModeForSelect(t);return}case s.TEMPLATE:{this.insertionMode=this.tmplInsertionModeStack[0];return}case s.HTML:{this.insertionMode=this.headElement?g.AFTER_HEAD:g.BEFORE_HEAD;return}case s.TD:case s.TH:{if(t>0){this.insertionMode=g.IN_CELL;return}break}case s.HEAD:{if(t>0){this.insertionMode=g.IN_HEAD;return}break}}this.insertionMode=g.IN_BODY}_resetInsertionModeForSelect(t){if(t>0)for(let n=t-1;n>0;n--){const r=this.openElements.tagIDs[n];if(r===s.TEMPLATE)break;if(r===s.TABLE){this.insertionMode=g.IN_SELECT_IN_TABLE;return}}this.insertionMode=g.IN_SELECT}_isElementCausesFosterParenting(t){return _s.has(t)}_shouldFosterParentOnInsertion(){return this.fosterParentingEnabled&&this.openElements.currentTagId!==void 0&&this._isElementCausesFosterParenting(this.openElements.currentTagId)}_findFosterParentingLocation(){for(let t=this.openElements.stackTop;t>=0;t--){const n=this.openElements.items[t];switch(this.openElements.tagIDs[t]){case s.TEMPLATE:{if(this.treeAdapter.getNamespaceURI(n)===O.HTML)return{parent:this.treeAdapter.getTemplateContent(n),beforeElement:null};break}case s.TABLE:{const r=this.treeAdapter.getParentNode(n);return r?{parent:r,beforeElement:n}:{parent:this.openElements.items[t-1],beforeElement:null}}}}return{parent:this.openElements.items[0],beforeElement:null}}_fosterParentElement(t){const n=this._findFosterParentingLocation();n.beforeElement?this.treeAdapter.insertBefore(n.parent,t,n.beforeElement):this.treeAdapter.appendChild(n.parent,t)}_isSpecialElement(t,n){const r=this.treeAdapter.getNamespaceURI(t);return Lp[r].has(n)}onCharacter(t){if(this.skipNextNewLine=!1,this.tokenizer.inForeignNode){H1(this,t);return}switch(this.insertionMode){case g.INITIAL:{Yt(this,t);break}case g.BEFORE_HTML:{Xt(this,t);break}case g.BEFORE_HEAD:{Kt(this,t);break}case g.IN_HEAD:{Gt(this,t);break}case g.IN_HEAD_NO_SCRIPT:{Jt(this,t);break}case g.AFTER_HEAD:{Zt(this,t);break}case g.IN_BODY:case g.IN_CAPTION:case g.IN_CELL:case g.IN_TEMPLATE:{ks(this,t);break}case g.TEXT:case g.IN_SELECT:case g.IN_SELECT_IN_TABLE:{this._insertCharacters(t);break}case g.IN_TABLE:case g.IN_TABLE_BODY:case g.IN_ROW:{cr(this,t);break}case g.IN_TABLE_TEXT:{Os(this,t);break}case g.IN_COLUMN_GROUP:{Nn(this,t);break}case g.AFTER_BODY:{On(this,t);break}case g.AFTER_AFTER_BODY:{_n(this,t);break}}}onNullCharacter(t){if(this.skipNextNewLine=!1,this.tokenizer.inForeignNode){U1(this,t);return}switch(this.insertionMode){case g.INITIAL:{Yt(this,t);break}case g.BEFORE_HTML:{Xt(this,t);break}case g.BEFORE_HEAD:{Kt(this,t);break}case g.IN_HEAD:{Gt(this,t);break}case g.IN_HEAD_NO_SCRIPT:{Jt(this,t);break}case g.AFTER_HEAD:{Zt(this,t);break}case g.TEXT:{this._insertCharacters(t);break}case g.IN_TABLE:case g.IN_TABLE_BODY:case g.IN_ROW:{cr(this,t);break}case g.IN_COLUMN_GROUP:{Nn(this,t);break}case g.AFTER_BODY:{On(this,t);break}case g.AFTER_AFTER_BODY:{_n(this,t);break}}}onComment(t){if(this.skipNextNewLine=!1,this.currentNotInHTML){Or(this,t);return}switch(this.insertionMode){case g.INITIAL:case g.BEFORE_HTML:case g.BEFORE_HEAD:case g.IN_HEAD:case g.IN_HEAD_NO_SCRIPT:case g.AFTER_HEAD:case g.IN_BODY:case g.IN_TABLE:case g.IN_CAPTION:case g.IN_COLUMN_GROUP:case g.IN_TABLE_BODY:case g.IN_ROW:case g.IN_CELL:case g.IN_SELECT:case g.IN_SELECT_IN_TABLE:case g.IN_TEMPLATE:case g.IN_FRAMESET:case g.AFTER_FRAMESET:{Or(this,t);break}case g.IN_TABLE_TEXT:{qt(this,t);break}case g.AFTER_BODY:{gm(this,t);break}case g.AFTER_AFTER_BODY:case g.AFTER_AFTER_FRAMESET:{Tm(this,t);break}}}onDoctype(t){switch(this.skipNextNewLine=!1,this.insertionMode){case g.INITIAL:{bm(this,t);break}case g.BEFORE_HEAD:case g.IN_HEAD:case g.IN_HEAD_NO_SCRIPT:case g.AFTER_HEAD:{this._err(t,S.misplacedDoctype);break}case g.IN_TABLE_TEXT:{qt(this,t);break}}}onStartTag(t){this.skipNextNewLine=!1,this.currentToken=t,this._processStartTag(t),t.selfClosing&&!t.ackSelfClosing&&this._err(t,S.nonVoidHtmlElementStartTagWithTrailingSolidus)}_processStartTag(t){this.shouldProcessStartTagTokenInForeignContent(t)?z1(this,t):this._startTagOutsideForeignContent(t)}_startTagOutsideForeignContent(t){switch(this.insertionMode){case g.INITIAL:{Yt(this,t);break}case g.BEFORE_HTML:{Am(this,t);break}case g.BEFORE_HEAD:{Cm(this,t);break}case g.IN_HEAD:{Ye(this,t);break}case g.IN_HEAD_NO_SCRIPT:{ym(this,t);break}case g.AFTER_HEAD:{xm(this,t);break}case g.IN_BODY:{Ae(this,t);break}case g.IN_TABLE:{xt(this,t);break}case g.IN_TABLE_TEXT:{qt(this,t);break}case g.IN_CAPTION:{k1(this,t);break}case g.IN_COLUMN_GROUP:{ou(this,t);break}case g.IN_TABLE_BODY:{vn(this,t);break}case g.IN_ROW:{Un(this,t);break}case g.IN_CELL:{I1(this,t);break}case g.IN_SELECT:{Rs(this,t);break}case g.IN_SELECT_IN_TABLE:{N1(this,t);break}case g.IN_TEMPLATE:{L1(this,t);break}case g.AFTER_BODY:{R1(this,t);break}case g.IN_FRAMESET:{P1(this,t);break}case g.AFTER_FRAMESET:{M1(this,t);break}case g.AFTER_AFTER_BODY:{F1(this,t);break}case g.AFTER_AFTER_FRAMESET:{v1(this,t);break}}}onEndTag(t){this.skipNextNewLine=!1,this.currentToken=t,this.currentNotInHTML?Y1(this,t):this._endTagOutsideForeignContent(t)}_endTagOutsideForeignContent(t){switch(this.insertionMode){case g.INITIAL:{Yt(this,t);break}case g.BEFORE_HTML:{_m(this,t);break}case g.BEFORE_HEAD:{km(this,t);break}case g.IN_HEAD:{Sm(this,t);break}case g.IN_HEAD_NO_SCRIPT:{Im(this,t);break}case g.AFTER_HEAD:{Nm(this,t);break}case g.IN_BODY:{Fn(this,t);break}case g.TEXT:{d1(this,t);break}case g.IN_TABLE:{rn(this,t);break}case g.IN_TABLE_TEXT:{qt(this,t);break}case g.IN_CAPTION:{S1(this,t);break}case g.IN_COLUMN_GROUP:{y1(this,t);break}case g.IN_TABLE_BODY:{Lr(this,t);break}case g.IN_ROW:{Ds(this,t);break}case g.IN_CELL:{x1(this,t);break}case g.IN_SELECT:{Ps(this,t);break}case g.IN_SELECT_IN_TABLE:{O1(this,t);break}case g.IN_TEMPLATE:{D1(this,t);break}case g.AFTER_BODY:{Ms(this,t);break}case g.IN_FRAMESET:{w1(this,t);break}case g.AFTER_FRAMESET:{B1(this,t);break}case g.AFTER_AFTER_BODY:{_n(this,t);break}}}onEof(t){switch(this.insertionMode){case g.INITIAL:{Yt(this,t);break}case g.BEFORE_HTML:{Xt(this,t);break}case g.BEFORE_HEAD:{Kt(this,t);break}case g.IN_HEAD:{Gt(this,t);break}case g.IN_HEAD_NO_SCRIPT:{Jt(this,t);break}case g.AFTER_HEAD:{Zt(this,t);break}case g.IN_BODY:case g.IN_TABLE:case g.IN_CAPTION:case g.IN_COLUMN_GROUP:case g.IN_TABLE_BODY:case g.IN_ROW:case g.IN_CELL:case g.IN_SELECT:case g.IN_SELECT_IN_TABLE:{xs(this,t);break}case g.TEXT:{p1(this,t);break}case g.IN_TABLE_TEXT:{qt(this,t);break}case g.IN_TEMPLATE:{ws(this,t);break}case g.AFTER_BODY:case g.IN_FRAMESET:case g.AFTER_FRAMESET:case g.AFTER_AFTER_BODY:case g.AFTER_AFTER_FRAMESET:{su(this,t);break}}}onWhitespaceCharacter(t){if(this.skipNextNewLine&&(this.skipNextNewLine=!1,t.chars.charCodeAt(0)===p.LINE_FEED)){if(t.chars.length===1)return;t.chars=t.chars.substr(1)}if(this.tokenizer.inForeignNode){this._insertCharacters(t);return}switch(this.insertionMode){case g.IN_HEAD:case g.IN_HEAD_NO_SCRIPT:case g.AFTER_HEAD:case g.TEXT:case g.IN_COLUMN_GROUP:case g.IN_SELECT:case g.IN_SELECT_IN_TABLE:case g.IN_FRAMESET:case g.AFTER_FRAMESET:{this._insertCharacters(t);break}case g.IN_BODY:case g.IN_CAPTION:case g.IN_CELL:case g.IN_TEMPLATE:case g.AFTER_BODY:case g.AFTER_AFTER_BODY:case g.AFTER_AFTER_FRAMESET:{Cs(this,t);break}case g.IN_TABLE:case g.IN_TABLE_BODY:case g.IN_ROW:{cr(this,t);break}case g.IN_TABLE_TEXT:{Ns(this,t);break}}}}function fm(e,t){let n=e.activeFormattingElements.getElementEntryInScopeWithTagName(t.tagName);return n?e.openElements.contains(n.element)?e.openElements.hasInScope(t.tagID)||(n=null):(e.activeFormattingElements.removeEntry(n),n=null):Is(e,t),n}function hm(e,t){let n=null,r=e.openElements.stackTop;for(;r>=0;r--){const u=e.openElements.items[r];if(u===t.element)break;e._isSpecialElement(u,e.openElements.tagIDs[r])&&(n=u)}return n||(e.openElements.shortenToLength(Math.max(r,0)),e.activeFormattingElements.removeEntry(t)),n}function dm(e,t,n){let r=t,u=e.openElements.getCommonAncestor(t);for(let a=0,i=u;i!==n;a++,i=u){u=e.openElements.getCommonAncestor(i);const o=e.activeFormattingElements.getElementEntry(i),l=o&&a>=lm;!o||l?(l&&e.activeFormattingElements.removeEntry(o),e.openElements.remove(i)):(i=pm(e,o),r===t&&(e.activeFormattingElements.bookmark=o),e.treeAdapter.detachNode(r),e.treeAdapter.appendChild(i,r),r=i)}return r}function pm(e,t){const n=e.treeAdapter.getNamespaceURI(t.element),r=e.treeAdapter.createElement(t.token.tagName,n,t.token.attrs);return e.openElements.replace(t.element,r),t.element=r,r}function mm(e,t,n){const r=e.treeAdapter.getTagName(t),u=Pt(r);if(e._isElementCausesFosterParenting(u))e._fosterParentElement(n);else{const a=e.treeAdapter.getNamespaceURI(t);u===s.TEMPLATE&&a===O.HTML&&(t=e.treeAdapter.getTemplateContent(t)),e.treeAdapter.appendChild(t,n)}}function Em(e,t,n){const r=e.treeAdapter.getNamespaceURI(n.element),{token:u}=n,a=e.treeAdapter.createElement(u.tagName,r,u.attrs);e._adoptNodes(t,a),e.treeAdapter.appendChild(t,a),e.activeFormattingElements.insertElementAfterBookmark(a,u),e.activeFormattingElements.removeEntry(n),e.openElements.remove(n.element),e.openElements.insertAfter(t,a,u.tagID)}function au(e,t){for(let n=0;n<om;n++){const r=fm(e,t);if(!r)break;const u=hm(e,r);if(!u)break;e.activeFormattingElements.bookmark=r;const a=dm(e,u,r.element),i=e.openElements.getCommonAncestor(r.element);e.treeAdapter.detachNode(a),i&&mm(e,i,a),Em(e,u,r)}}function Or(e,t){e._appendCommentNode(t,e.openElements.currentTmplContentOrNode)}function gm(e,t){e._appendCommentNode(t,e.openElements.items[0])}function Tm(e,t){e._appendCommentNode(t,e.document)}function su(e,t){if(e.stopped=!0,t.location){const n=e.fragmentContext?0:2;for(let r=e.openElements.stackTop;r>=n;r--)e._setEndLocation(e.openElements.items[r],t);if(!e.fragmentContext&&e.openElements.stackTop>=0){const r=e.openElements.items[0],u=e.treeAdapter.getNodeSourceCodeLocation(r);if(u&&!u.endTag&&(e._setEndLocation(r,t),e.openElements.stackTop>=1)){const a=e.openElements.items[1],i=e.treeAdapter.getNodeSourceCodeLocation(a);i&&!i.endTag&&e._setEndLocation(a,t)}}}}function bm(e,t){e._setDocumentType(t);const n=t.forceQuirks?Fe.QUIRKS:Xp(t);Qp(t)||e._err(t,S.nonConformingDoctype),e.treeAdapter.setDocumentMode(e.document,n),e.insertionMode=g.BEFORE_HTML}function Yt(e,t){e._err(t,S.missingDoctype,!0),e.treeAdapter.setDocumentMode(e.document,Fe.QUIRKS),e.insertionMode=g.BEFORE_HTML,e._processToken(t)}function Am(e,t){t.tagID===s.HTML?(e._insertElement(t,O.HTML),e.insertionMode=g.BEFORE_HEAD):Xt(e,t)}function _m(e,t){const n=t.tagID;(n===s.HTML||n===s.HEAD||n===s.BODY||n===s.BR)&&Xt(e,t)}function Xt(e,t){e._insertFakeRootElement(),e.insertionMode=g.BEFORE_HEAD,e._processToken(t)}function Cm(e,t){switch(t.tagID){case s.HTML:{Ae(e,t);break}case s.HEAD:{e._insertElement(t,O.HTML),e.headElement=e.openElements.current,e.insertionMode=g.IN_HEAD;break}default:Kt(e,t)}}function km(e,t){const n=t.tagID;n===s.HEAD||n===s.BODY||n===s.HTML||n===s.BR?Kt(e,t):e._err(t,S.endTagWithoutMatchingOpenElement)}function Kt(e,t){e._insertFakeElement(C.HEAD,s.HEAD),e.headElement=e.openElements.current,e.insertionMode=g.IN_HEAD,e._processToken(t)}function Ye(e,t){switch(t.tagID){case s.HTML:{Ae(e,t);break}case s.BASE:case s.BASEFONT:case s.BGSOUND:case s.LINK:case s.META:{e._appendElement(t,O.HTML),t.ackSelfClosing=!0;break}case s.TITLE:{e._switchToTextParsing(t,fe.RCDATA);break}case s.NOSCRIPT:{e.options.scriptingEnabled?e._switchToTextParsing(t,fe.RAWTEXT):(e._insertElement(t,O.HTML),e.insertionMode=g.IN_HEAD_NO_SCRIPT);break}case s.NOFRAMES:case s.STYLE:{e._switchToTextParsing(t,fe.RAWTEXT);break}case s.SCRIPT:{e._switchToTextParsing(t,fe.SCRIPT_DATA);break}case s.TEMPLATE:{e._insertTemplate(t),e.activeFormattingElements.insertMarker(),e.framesetOk=!1,e.insertionMode=g.IN_TEMPLATE,e.tmplInsertionModeStack.unshift(g.IN_TEMPLATE);break}case s.HEAD:{e._err(t,S.misplacedStartTagForHeadElement);break}default:Gt(e,t)}}function Sm(e,t){switch(t.tagID){case s.HEAD:{e.openElements.pop(),e.insertionMode=g.AFTER_HEAD;break}case s.BODY:case s.BR:case s.HTML:{Gt(e,t);break}case s.TEMPLATE:{mt(e,t);break}default:e._err(t,S.endTagWithoutMatchingOpenElement)}}function mt(e,t){e.openElements.tmplCount>0?(e.openElements.generateImpliedEndTagsThoroughly(),e.openElements.currentTagId!==s.TEMPLATE&&e._err(t,S.closingOfElementWithOpenChildElements),e.openElements.popUntilTagNamePopped(s.TEMPLATE),e.activeFormattingElements.clearToLastMarker(),e.tmplInsertionModeStack.shift(),e._resetInsertionMode()):e._err(t,S.endTagWithoutMatchingOpenElement)}function Gt(e,t){e.openElements.pop(),e.insertionMode=g.AFTER_HEAD,e._processToken(t)}function ym(e,t){switch(t.tagID){case s.HTML:{Ae(e,t);break}case s.BASEFONT:case s.BGSOUND:case s.HEAD:case s.LINK:case s.META:case s.NOFRAMES:case s.STYLE:{Ye(e,t);break}case s.NOSCRIPT:{e._err(t,S.nestedNoscriptInHead);break}default:Jt(e,t)}}function Im(e,t){switch(t.tagID){case s.NOSCRIPT:{e.openElements.pop(),e.insertionMode=g.IN_HEAD;break}case s.BR:{Jt(e,t);break}default:e._err(t,S.endTagWithoutMatchingOpenElement)}}function Jt(e,t){const n=t.type===G.EOF?S.openElementsLeftAfterEof:S.disallowedContentInNoscriptInHead;e._err(t,n),e.openElements.pop(),e.insertionMode=g.IN_HEAD,e._processToken(t)}function xm(e,t){switch(t.tagID){case s.HTML:{Ae(e,t);break}case s.BODY:{e._insertElement(t,O.HTML),e.framesetOk=!1,e.insertionMode=g.IN_BODY;break}case s.FRAMESET:{e._insertElement(t,O.HTML),e.insertionMode=g.IN_FRAMESET;break}case s.BASE:case s.BASEFONT:case s.BGSOUND:case s.LINK:case s.META:case s.NOFRAMES:case s.SCRIPT:case s.STYLE:case s.TEMPLATE:case s.TITLE:{e._err(t,S.abandonedHeadElementChild),e.openElements.push(e.headElement,s.HEAD),Ye(e,t),e.openElements.remove(e.headElement);break}case s.HEAD:{e._err(t,S.misplacedStartTagForHeadElement);break}default:Zt(e,t)}}function Nm(e,t){switch(t.tagID){case s.BODY:case s.HTML:case s.BR:{Zt(e,t);break}case s.TEMPLATE:{mt(e,t);break}default:e._err(t,S.endTagWithoutMatchingOpenElement)}}function Zt(e,t){e._insertFakeElement(C.BODY,s.BODY),e.insertionMode=g.IN_BODY,Bn(e,t)}function Bn(e,t){switch(t.type){case G.CHARACTER:{ks(e,t);break}case G.WHITESPACE_CHARACTER:{Cs(e,t);break}case G.COMMENT:{Or(e,t);break}case G.START_TAG:{Ae(e,t);break}case G.END_TAG:{Fn(e,t);break}case G.EOF:{xs(e,t);break}}}function Cs(e,t){e._reconstructActiveFormattingElements(),e._insertCharacters(t)}function ks(e,t){e._reconstructActiveFormattingElements(),e._insertCharacters(t),e.framesetOk=!1}function Om(e,t){e.openElements.tmplCount===0&&e.treeAdapter.adoptAttributes(e.openElements.items[0],t.attrs)}function Lm(e,t){const n=e.openElements.tryPeekProperlyNestedBodyElement();n&&e.openElements.tmplCount===0&&(e.framesetOk=!1,e.treeAdapter.adoptAttributes(n,t.attrs))}function Dm(e,t){const n=e.openElements.tryPeekProperlyNestedBodyElement();e.framesetOk&&n&&(e.treeAdapter.detachNode(n),e.openElements.popAllUpToHtmlElement(),e._insertElement(t,O.HTML),e.insertionMode=g.IN_FRAMESET)}function Rm(e,t){e.openElements.hasInButtonScope(s.P)&&e._closePElement(),e._insertElement(t,O.HTML)}function Pm(e,t){e.openElements.hasInButtonScope(s.P)&&e._closePElement(),e.openElements.currentTagId!==void 0&&Nr.has(e.openElements.currentTagId)&&e.openElements.pop(),e._insertElement(t,O.HTML)}function wm(e,t){e.openElements.hasInButtonScope(s.P)&&e._closePElement(),e._insertElement(t,O.HTML),e.skipNextNewLine=!0,e.framesetOk=!1}function Mm(e,t){const n=e.openElements.tmplCount>0;(!e.formElement||n)&&(e.openElements.hasInButtonScope(s.P)&&e._closePElement(),e._insertElement(t,O.HTML),n||(e.formElement=e.openElements.current))}function Bm(e,t){e.framesetOk=!1;const n=t.tagID;for(let r=e.openElements.stackTop;r>=0;r--){const u=e.openElements.tagIDs[r];if(n===s.LI&&u===s.LI||(n===s.DD||n===s.DT)&&(u===s.DD||u===s.DT)){e.openElements.generateImpliedEndTagsWithExclusion(u),e.openElements.popUntilTagNamePopped(u);break}if(u!==s.ADDRESS&&u!==s.DIV&&u!==s.P&&e._isSpecialElement(e.openElements.items[r],u))break}e.openElements.hasInButtonScope(s.P)&&e._closePElement(),e._insertElement(t,O.HTML)}function Fm(e,t){e.openElements.hasInButtonScope(s.P)&&e._closePElement(),e._insertElement(t,O.HTML),e.tokenizer.state=fe.PLAINTEXT}function vm(e,t){e.openElements.hasInScope(s.BUTTON)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(s.BUTTON)),e._reconstructActiveFormattingElements(),e._insertElement(t,O.HTML),e.framesetOk=!1}function Um(e,t){const n=e.activeFormattingElements.getElementEntryInScopeWithTagName(C.A);n&&(au(e,t),e.openElements.remove(n.element),e.activeFormattingElements.removeEntry(n)),e._reconstructActiveFormattingElements(),e._insertElement(t,O.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t)}function Hm(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,O.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t)}function zm(e,t){e._reconstructActiveFormattingElements(),e.openElements.hasInScope(s.NOBR)&&(au(e,t),e._reconstructActiveFormattingElements()),e._insertElement(t,O.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t)}function Ym(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,O.HTML),e.activeFormattingElements.insertMarker(),e.framesetOk=!1}function qm(e,t){e.treeAdapter.getDocumentMode(e.document)!==Fe.QUIRKS&&e.openElements.hasInButtonScope(s.P)&&e._closePElement(),e._insertElement(t,O.HTML),e.framesetOk=!1,e.insertionMode=g.IN_TABLE}function Ss(e,t){e._reconstructActiveFormattingElements(),e._appendElement(t,O.HTML),e.framesetOk=!1,t.ackSelfClosing=!0}function ys(e){const t=ds(e,ct.TYPE);return t!=null&&t.toLowerCase()===sm}function jm(e,t){e._reconstructActiveFormattingElements(),e._appendElement(t,O.HTML),ys(t)||(e.framesetOk=!1),t.ackSelfClosing=!0}function Vm(e,t){e._appendElement(t,O.HTML),t.ackSelfClosing=!0}function $m(e,t){e.openElements.hasInButtonScope(s.P)&&e._closePElement(),e._appendElement(t,O.HTML),e.framesetOk=!1,t.ackSelfClosing=!0}function Wm(e,t){t.tagName=C.IMG,t.tagID=s.IMG,Ss(e,t)}function Qm(e,t){e._insertElement(t,O.HTML),e.skipNextNewLine=!0,e.tokenizer.state=fe.RCDATA,e.originalInsertionMode=e.insertionMode,e.framesetOk=!1,e.insertionMode=g.TEXT}function Xm(e,t){e.openElements.hasInButtonScope(s.P)&&e._closePElement(),e._reconstructActiveFormattingElements(),e.framesetOk=!1,e._switchToTextParsing(t,fe.RAWTEXT)}function Km(e,t){e.framesetOk=!1,e._switchToTextParsing(t,fe.RAWTEXT)}function Li(e,t){e._switchToTextParsing(t,fe.RAWTEXT)}function Gm(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,O.HTML),e.framesetOk=!1,e.insertionMode=e.insertionMode===g.IN_TABLE||e.insertionMode===g.IN_CAPTION||e.insertionMode===g.IN_TABLE_BODY||e.insertionMode===g.IN_ROW||e.insertionMode===g.IN_CELL?g.IN_SELECT_IN_TABLE:g.IN_SELECT}function Jm(e,t){e.openElements.currentTagId===s.OPTION&&e.openElements.pop(),e._reconstructActiveFormattingElements(),e._insertElement(t,O.HTML)}function Zm(e,t){e.openElements.hasInScope(s.RUBY)&&e.openElements.generateImpliedEndTags(),e._insertElement(t,O.HTML)}function e1(e,t){e.openElements.hasInScope(s.RUBY)&&e.openElements.generateImpliedEndTagsWithExclusion(s.RTC),e._insertElement(t,O.HTML)}function t1(e,t){e._reconstructActiveFormattingElements(),bs(t),iu(t),t.selfClosing?e._appendElement(t,O.MATHML):e._insertElement(t,O.MATHML),t.ackSelfClosing=!0}function n1(e,t){e._reconstructActiveFormattingElements(),As(t),iu(t),t.selfClosing?e._appendElement(t,O.SVG):e._insertElement(t,O.SVG),t.ackSelfClosing=!0}function Di(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,O.HTML)}function Ae(e,t){switch(t.tagID){case s.I:case s.S:case s.B:case s.U:case s.EM:case s.TT:case s.BIG:case s.CODE:case s.FONT:case s.SMALL:case s.STRIKE:case s.STRONG:{Hm(e,t);break}case s.A:{Um(e,t);break}case s.H1:case s.H2:case s.H3:case s.H4:case s.H5:case s.H6:{Pm(e,t);break}case s.P:case s.DL:case s.OL:case s.UL:case s.DIV:case s.DIR:case s.NAV:case s.MAIN:case s.MENU:case s.ASIDE:case s.CENTER:case s.FIGURE:case s.FOOTER:case s.HEADER:case s.HGROUP:case s.DIALOG:case s.DETAILS:case s.ADDRESS:case s.ARTICLE:case s.SEARCH:case s.SECTION:case s.SUMMARY:case s.FIELDSET:case s.BLOCKQUOTE:case s.FIGCAPTION:{Rm(e,t);break}case s.LI:case s.DD:case s.DT:{Bm(e,t);break}case s.BR:case s.IMG:case s.WBR:case s.AREA:case s.EMBED:case s.KEYGEN:{Ss(e,t);break}case s.HR:{$m(e,t);break}case s.RB:case s.RTC:{Zm(e,t);break}case s.RT:case s.RP:{e1(e,t);break}case s.PRE:case s.LISTING:{wm(e,t);break}case s.XMP:{Xm(e,t);break}case s.SVG:{n1(e,t);break}case s.HTML:{Om(e,t);break}case s.BASE:case s.LINK:case s.META:case s.STYLE:case s.TITLE:case s.SCRIPT:case s.BGSOUND:case s.BASEFONT:case s.TEMPLATE:{Ye(e,t);break}case s.BODY:{Lm(e,t);break}case s.FORM:{Mm(e,t);break}case s.NOBR:{zm(e,t);break}case s.MATH:{t1(e,t);break}case s.TABLE:{qm(e,t);break}case s.INPUT:{jm(e,t);break}case s.PARAM:case s.TRACK:case s.SOURCE:{Vm(e,t);break}case s.IMAGE:{Wm(e,t);break}case s.BUTTON:{vm(e,t);break}case s.APPLET:case s.OBJECT:case s.MARQUEE:{Ym(e,t);break}case s.IFRAME:{Km(e,t);break}case s.SELECT:{Gm(e,t);break}case s.OPTION:case s.OPTGROUP:{Jm(e,t);break}case s.NOEMBED:case s.NOFRAMES:{Li(e,t);break}case s.FRAMESET:{Dm(e,t);break}case s.TEXTAREA:{Qm(e,t);break}case s.NOSCRIPT:{e.options.scriptingEnabled?Li(e,t):Di(e,t);break}case s.PLAINTEXT:{Fm(e,t);break}case s.COL:case s.TH:case s.TD:case s.TR:case s.HEAD:case s.FRAME:case s.TBODY:case s.TFOOT:case s.THEAD:case s.CAPTION:case s.COLGROUP:break;default:Di(e,t)}}function r1(e,t){if(e.openElements.hasInScope(s.BODY)&&(e.insertionMode=g.AFTER_BODY,e.options.sourceCodeLocationInfo)){const n=e.openElements.tryPeekProperlyNestedBodyElement();n&&e._setEndLocation(n,t)}}function u1(e,t){e.openElements.hasInScope(s.BODY)&&(e.insertionMode=g.AFTER_BODY,Ms(e,t))}function i1(e,t){const n=t.tagID;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n))}function a1(e){const t=e.openElements.tmplCount>0,{formElement:n}=e;t||(e.formElement=null),(n||t)&&e.openElements.hasInScope(s.FORM)&&(e.openElements.generateImpliedEndTags(),t?e.openElements.popUntilTagNamePopped(s.FORM):n&&e.openElements.remove(n))}function s1(e){e.openElements.hasInButtonScope(s.P)||e._insertFakeElement(C.P,s.P),e._closePElement()}function o1(e){e.openElements.hasInListItemScope(s.LI)&&(e.openElements.generateImpliedEndTagsWithExclusion(s.LI),e.openElements.popUntilTagNamePopped(s.LI))}function l1(e,t){const n=t.tagID;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTagsWithExclusion(n),e.openElements.popUntilTagNamePopped(n))}function c1(e){e.openElements.hasNumberedHeaderInScope()&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilNumberedHeaderPopped())}function f1(e,t){const n=t.tagID;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n),e.activeFormattingElements.clearToLastMarker())}function h1(e){e._reconstructActiveFormattingElements(),e._insertFakeElement(C.BR,s.BR),e.openElements.pop(),e.framesetOk=!1}function Is(e,t){const n=t.tagName,r=t.tagID;for(let u=e.openElements.stackTop;u>0;u--){const a=e.openElements.items[u],i=e.openElements.tagIDs[u];if(r===i&&(r!==s.UNKNOWN||e.treeAdapter.getTagName(a)===n)){e.openElements.generateImpliedEndTagsWithExclusion(r),e.openElements.stackTop>=u&&e.openElements.shortenToLength(u);break}if(e._isSpecialElement(a,i))break}}function Fn(e,t){switch(t.tagID){case s.A:case s.B:case s.I:case s.S:case s.U:case s.EM:case s.TT:case s.BIG:case s.CODE:case s.FONT:case s.NOBR:case s.SMALL:case s.STRIKE:case s.STRONG:{au(e,t);break}case s.P:{s1(e);break}case s.DL:case s.UL:case s.OL:case s.DIR:case s.DIV:case s.NAV:case s.PRE:case s.MAIN:case s.MENU:case s.ASIDE:case s.BUTTON:case s.CENTER:case s.FIGURE:case s.FOOTER:case s.HEADER:case s.HGROUP:case s.DIALOG:case s.ADDRESS:case s.ARTICLE:case s.DETAILS:case s.SEARCH:case s.SECTION:case s.SUMMARY:case s.LISTING:case s.FIELDSET:case s.BLOCKQUOTE:case s.FIGCAPTION:{i1(e,t);break}case s.LI:{o1(e);break}case s.DD:case s.DT:{l1(e,t);break}case s.H1:case s.H2:case s.H3:case s.H4:case s.H5:case s.H6:{c1(e);break}case s.BR:{h1(e);break}case s.BODY:{r1(e,t);break}case s.HTML:{u1(e,t);break}case s.FORM:{a1(e);break}case s.APPLET:case s.OBJECT:case s.MARQUEE:{f1(e,t);break}case s.TEMPLATE:{mt(e,t);break}default:Is(e,t)}}function xs(e,t){e.tmplInsertionModeStack.length>0?ws(e,t):su(e,t)}function d1(e,t){var n;t.tagID===s.SCRIPT&&((n=e.scriptHandler)===null||n===void 0||n.call(e,e.openElements.current)),e.openElements.pop(),e.insertionMode=e.originalInsertionMode}function p1(e,t){e._err(t,S.eofInElementThatCanContainOnlyText),e.openElements.pop(),e.insertionMode=e.originalInsertionMode,e.onEof(t)}function cr(e,t){if(e.openElements.currentTagId!==void 0&&_s.has(e.openElements.currentTagId))switch(e.pendingCharacterTokens.length=0,e.hasNonWhitespacePendingCharacterToken=!1,e.originalInsertionMode=e.insertionMode,e.insertionMode=g.IN_TABLE_TEXT,t.type){case G.CHARACTER:{Os(e,t);break}case G.WHITESPACE_CHARACTER:{Ns(e,t);break}}else cn(e,t)}function m1(e,t){e.openElements.clearBackToTableContext(),e.activeFormattingElements.insertMarker(),e._insertElement(t,O.HTML),e.insertionMode=g.IN_CAPTION}function E1(e,t){e.openElements.clearBackToTableContext(),e._insertElement(t,O.HTML),e.insertionMode=g.IN_COLUMN_GROUP}function g1(e,t){e.openElements.clearBackToTableContext(),e._insertFakeElement(C.COLGROUP,s.COLGROUP),e.insertionMode=g.IN_COLUMN_GROUP,ou(e,t)}function T1(e,t){e.openElements.clearBackToTableContext(),e._insertElement(t,O.HTML),e.insertionMode=g.IN_TABLE_BODY}function b1(e,t){e.openElements.clearBackToTableContext(),e._insertFakeElement(C.TBODY,s.TBODY),e.insertionMode=g.IN_TABLE_BODY,vn(e,t)}function A1(e,t){e.openElements.hasInTableScope(s.TABLE)&&(e.openElements.popUntilTagNamePopped(s.TABLE),e._resetInsertionMode(),e._processStartTag(t))}function _1(e,t){ys(t)?e._appendElement(t,O.HTML):cn(e,t),t.ackSelfClosing=!0}function C1(e,t){!e.formElement&&e.openElements.tmplCount===0&&(e._insertElement(t,O.HTML),e.formElement=e.openElements.current,e.openElements.pop())}function xt(e,t){switch(t.tagID){case s.TD:case s.TH:case s.TR:{b1(e,t);break}case s.STYLE:case s.SCRIPT:case s.TEMPLATE:{Ye(e,t);break}case s.COL:{g1(e,t);break}case s.FORM:{C1(e,t);break}case s.TABLE:{A1(e,t);break}case s.TBODY:case s.TFOOT:case s.THEAD:{T1(e,t);break}case s.INPUT:{_1(e,t);break}case s.CAPTION:{m1(e,t);break}case s.COLGROUP:{E1(e,t);break}default:cn(e,t)}}function rn(e,t){switch(t.tagID){case s.TABLE:{e.openElements.hasInTableScope(s.TABLE)&&(e.openElements.popUntilTagNamePopped(s.TABLE),e._resetInsertionMode());break}case s.TEMPLATE:{mt(e,t);break}case s.BODY:case s.CAPTION:case s.COL:case s.COLGROUP:case s.HTML:case s.TBODY:case s.TD:case s.TFOOT:case s.TH:case s.THEAD:case s.TR:break;default:cn(e,t)}}function cn(e,t){const n=e.fosterParentingEnabled;e.fosterParentingEnabled=!0,Bn(e,t),e.fosterParentingEnabled=n}function Ns(e,t){e.pendingCharacterTokens.push(t)}function Os(e,t){e.pendingCharacterTokens.push(t),e.hasNonWhitespacePendingCharacterToken=!0}function qt(e,t){let n=0;if(e.hasNonWhitespacePendingCharacterToken)for(;n<e.pendingCharacterTokens.length;n++)cn(e,e.pendingCharacterTokens[n]);else for(;n<e.pendingCharacterTokens.length;n++)e._insertCharacters(e.pendingCharacterTokens[n]);e.insertionMode=e.originalInsertionMode,e._processToken(t)}const Ls=new Set([s.CAPTION,s.COL,s.COLGROUP,s.TBODY,s.TD,s.TFOOT,s.TH,s.THEAD,s.TR]);function k1(e,t){const n=t.tagID;Ls.has(n)?e.openElements.hasInTableScope(s.CAPTION)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(s.CAPTION),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=g.IN_TABLE,xt(e,t)):Ae(e,t)}function S1(e,t){const n=t.tagID;switch(n){case s.CAPTION:case s.TABLE:{e.openElements.hasInTableScope(s.CAPTION)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(s.CAPTION),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=g.IN_TABLE,n===s.TABLE&&rn(e,t));break}case s.BODY:case s.COL:case s.COLGROUP:case s.HTML:case s.TBODY:case s.TD:case s.TFOOT:case s.TH:case s.THEAD:case s.TR:break;default:Fn(e,t)}}function ou(e,t){switch(t.tagID){case s.HTML:{Ae(e,t);break}case s.COL:{e._appendElement(t,O.HTML),t.ackSelfClosing=!0;break}case s.TEMPLATE:{Ye(e,t);break}default:Nn(e,t)}}function y1(e,t){switch(t.tagID){case s.COLGROUP:{e.openElements.currentTagId===s.COLGROUP&&(e.openElements.pop(),e.insertionMode=g.IN_TABLE);break}case s.TEMPLATE:{mt(e,t);break}case s.COL:break;default:Nn(e,t)}}function Nn(e,t){e.openElements.currentTagId===s.COLGROUP&&(e.openElements.pop(),e.insertionMode=g.IN_TABLE,e._processToken(t))}function vn(e,t){switch(t.tagID){case s.TR:{e.openElements.clearBackToTableBodyContext(),e._insertElement(t,O.HTML),e.insertionMode=g.IN_ROW;break}case s.TH:case s.TD:{e.openElements.clearBackToTableBodyContext(),e._insertFakeElement(C.TR,s.TR),e.insertionMode=g.IN_ROW,Un(e,t);break}case s.CAPTION:case s.COL:case s.COLGROUP:case s.TBODY:case s.TFOOT:case s.THEAD:{e.openElements.hasTableBodyContextInTableScope()&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=g.IN_TABLE,xt(e,t));break}default:xt(e,t)}}function Lr(e,t){const n=t.tagID;switch(t.tagID){case s.TBODY:case s.TFOOT:case s.THEAD:{e.openElements.hasInTableScope(n)&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=g.IN_TABLE);break}case s.TABLE:{e.openElements.hasTableBodyContextInTableScope()&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=g.IN_TABLE,rn(e,t));break}case s.BODY:case s.CAPTION:case s.COL:case s.COLGROUP:case s.HTML:case s.TD:case s.TH:case s.TR:break;default:rn(e,t)}}function Un(e,t){switch(t.tagID){case s.TH:case s.TD:{e.openElements.clearBackToTableRowContext(),e._insertElement(t,O.HTML),e.insertionMode=g.IN_CELL,e.activeFormattingElements.insertMarker();break}case s.CAPTION:case s.COL:case s.COLGROUP:case s.TBODY:case s.TFOOT:case s.THEAD:case s.TR:{e.openElements.hasInTableScope(s.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=g.IN_TABLE_BODY,vn(e,t));break}default:xt(e,t)}}function Ds(e,t){switch(t.tagID){case s.TR:{e.openElements.hasInTableScope(s.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=g.IN_TABLE_BODY);break}case s.TABLE:{e.openElements.hasInTableScope(s.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=g.IN_TABLE_BODY,Lr(e,t));break}case s.TBODY:case s.TFOOT:case s.THEAD:{(e.openElements.hasInTableScope(t.tagID)||e.openElements.hasInTableScope(s.TR))&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=g.IN_TABLE_BODY,Lr(e,t));break}case s.BODY:case s.CAPTION:case s.COL:case s.COLGROUP:case s.HTML:case s.TD:case s.TH:break;default:rn(e,t)}}function I1(e,t){const n=t.tagID;Ls.has(n)?(e.openElements.hasInTableScope(s.TD)||e.openElements.hasInTableScope(s.TH))&&(e._closeTableCell(),Un(e,t)):Ae(e,t)}function x1(e,t){const n=t.tagID;switch(n){case s.TD:case s.TH:{e.openElements.hasInTableScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=g.IN_ROW);break}case s.TABLE:case s.TBODY:case s.TFOOT:case s.THEAD:case s.TR:{e.openElements.hasInTableScope(n)&&(e._closeTableCell(),Ds(e,t));break}case s.BODY:case s.CAPTION:case s.COL:case s.COLGROUP:case s.HTML:break;default:Fn(e,t)}}function Rs(e,t){switch(t.tagID){case s.HTML:{Ae(e,t);break}case s.OPTION:{e.openElements.currentTagId===s.OPTION&&e.openElements.pop(),e._insertElement(t,O.HTML);break}case s.OPTGROUP:{e.openElements.currentTagId===s.OPTION&&e.openElements.pop(),e.openElements.currentTagId===s.OPTGROUP&&e.openElements.pop(),e._insertElement(t,O.HTML);break}case s.HR:{e.openElements.currentTagId===s.OPTION&&e.openElements.pop(),e.openElements.currentTagId===s.OPTGROUP&&e.openElements.pop(),e._appendElement(t,O.HTML),t.ackSelfClosing=!0;break}case s.INPUT:case s.KEYGEN:case s.TEXTAREA:case s.SELECT:{e.openElements.hasInSelectScope(s.SELECT)&&(e.openElements.popUntilTagNamePopped(s.SELECT),e._resetInsertionMode(),t.tagID!==s.SELECT&&e._processStartTag(t));break}case s.SCRIPT:case s.TEMPLATE:{Ye(e,t);break}}}function Ps(e,t){switch(t.tagID){case s.OPTGROUP:{e.openElements.stackTop>0&&e.openElements.currentTagId===s.OPTION&&e.openElements.tagIDs[e.openElements.stackTop-1]===s.OPTGROUP&&e.openElements.pop(),e.openElements.currentTagId===s.OPTGROUP&&e.openElements.pop();break}case s.OPTION:{e.openElements.currentTagId===s.OPTION&&e.openElements.pop();break}case s.SELECT:{e.openElements.hasInSelectScope(s.SELECT)&&(e.openElements.popUntilTagNamePopped(s.SELECT),e._resetInsertionMode());break}case s.TEMPLATE:{mt(e,t);break}}}function N1(e,t){const n=t.tagID;n===s.CAPTION||n===s.TABLE||n===s.TBODY||n===s.TFOOT||n===s.THEAD||n===s.TR||n===s.TD||n===s.TH?(e.openElements.popUntilTagNamePopped(s.SELECT),e._resetInsertionMode(),e._processStartTag(t)):Rs(e,t)}function O1(e,t){const n=t.tagID;n===s.CAPTION||n===s.TABLE||n===s.TBODY||n===s.TFOOT||n===s.THEAD||n===s.TR||n===s.TD||n===s.TH?e.openElements.hasInTableScope(n)&&(e.openElements.popUntilTagNamePopped(s.SELECT),e._resetInsertionMode(),e.onEndTag(t)):Ps(e,t)}function L1(e,t){switch(t.tagID){case s.BASE:case s.BASEFONT:case s.BGSOUND:case s.LINK:case s.META:case s.NOFRAMES:case s.SCRIPT:case s.STYLE:case s.TEMPLATE:case s.TITLE:{Ye(e,t);break}case s.CAPTION:case s.COLGROUP:case s.TBODY:case s.TFOOT:case s.THEAD:{e.tmplInsertionModeStack[0]=g.IN_TABLE,e.insertionMode=g.IN_TABLE,xt(e,t);break}case s.COL:{e.tmplInsertionModeStack[0]=g.IN_COLUMN_GROUP,e.insertionMode=g.IN_COLUMN_GROUP,ou(e,t);break}case s.TR:{e.tmplInsertionModeStack[0]=g.IN_TABLE_BODY,e.insertionMode=g.IN_TABLE_BODY,vn(e,t);break}case s.TD:case s.TH:{e.tmplInsertionModeStack[0]=g.IN_ROW,e.insertionMode=g.IN_ROW,Un(e,t);break}default:e.tmplInsertionModeStack[0]=g.IN_BODY,e.insertionMode=g.IN_BODY,Ae(e,t)}}function D1(e,t){t.tagID===s.TEMPLATE&&mt(e,t)}function ws(e,t){e.openElements.tmplCount>0?(e.openElements.popUntilTagNamePopped(s.TEMPLATE),e.activeFormattingElements.clearToLastMarker(),e.tmplInsertionModeStack.shift(),e._resetInsertionMode(),e.onEof(t)):su(e,t)}function R1(e,t){t.tagID===s.HTML?Ae(e,t):On(e,t)}function Ms(e,t){var n;if(t.tagID===s.HTML){if(e.fragmentContext||(e.insertionMode=g.AFTER_AFTER_BODY),e.options.sourceCodeLocationInfo&&e.openElements.tagIDs[0]===s.HTML){e._setEndLocation(e.openElements.items[0],t);const r=e.openElements.items[1];r&&!(!((n=e.treeAdapter.getNodeSourceCodeLocation(r))===null||n===void 0)&&n.endTag)&&e._setEndLocation(r,t)}}else On(e,t)}function On(e,t){e.insertionMode=g.IN_BODY,Bn(e,t)}function P1(e,t){switch(t.tagID){case s.HTML:{Ae(e,t);break}case s.FRAMESET:{e._insertElement(t,O.HTML);break}case s.FRAME:{e._appendElement(t,O.HTML),t.ackSelfClosing=!0;break}case s.NOFRAMES:{Ye(e,t);break}}}function w1(e,t){t.tagID===s.FRAMESET&&!e.openElements.isRootHtmlElementCurrent()&&(e.openElements.pop(),!e.fragmentContext&&e.openElements.currentTagId!==s.FRAMESET&&(e.insertionMode=g.AFTER_FRAMESET))}function M1(e,t){switch(t.tagID){case s.HTML:{Ae(e,t);break}case s.NOFRAMES:{Ye(e,t);break}}}function B1(e,t){t.tagID===s.HTML&&(e.insertionMode=g.AFTER_AFTER_FRAMESET)}function F1(e,t){t.tagID===s.HTML?Ae(e,t):_n(e,t)}function _n(e,t){e.insertionMode=g.IN_BODY,Bn(e,t)}function v1(e,t){switch(t.tagID){case s.HTML:{Ae(e,t);break}case s.NOFRAMES:{Ye(e,t);break}}}function U1(e,t){t.chars=ce,e._insertCharacters(t)}function H1(e,t){e._insertCharacters(t),e.framesetOk=!1}function Bs(e){for(;e.treeAdapter.getNamespaceURI(e.openElements.current)!==O.HTML&&e.openElements.currentTagId!==void 0&&!e._isIntegrationPoint(e.openElements.currentTagId,e.openElements.current);)e.openElements.pop()}function z1(e,t){if(nm(t))Bs(e),e._startTagOutsideForeignContent(t);else{const n=e._getAdjustedCurrentElement(),r=e.treeAdapter.getNamespaceURI(n);r===O.MATHML?bs(t):r===O.SVG&&(rm(t),As(t)),iu(t),t.selfClosing?e._appendElement(t,r):e._insertElement(t,r),t.ackSelfClosing=!0}}function Y1(e,t){if(t.tagID===s.P||t.tagID===s.BR){Bs(e),e._endTagOutsideForeignContent(t);return}for(let n=e.openElements.stackTop;n>0;n--){const r=e.openElements.items[n];if(e.treeAdapter.getNamespaceURI(r)===O.HTML){e._endTagOutsideForeignContent(t);break}const u=e.treeAdapter.getTagName(r);if(u.toLowerCase()===t.tagName){t.tagName=u,e.openElements.shortenToLength(n);break}}}C.AREA,C.BASE,C.BASEFONT,C.BGSOUND,C.BR,C.COL,C.EMBED,C.FRAME,C.HR,C.IMG,C.INPUT,C.KEYGEN,C.LINK,C.META,C.PARAM,C.SOURCE,C.TRACK,C.WBR;const Fs=vs("end"),Et=vs("start");function vs(e){return t;function t(n){const r=n&&n.position&&n.position[e]||{};if(typeof r.line=="number"&&r.line>0&&typeof r.column=="number"&&r.column>0)return{line:r.line,column:r.column,offset:typeof r.offset=="number"&&r.offset>-1?r.offset:void 0}}}const Us=function(e){if(e==null)return $1;if(typeof e=="function")return Hn(e);if(typeof e=="object")return Array.isArray(e)?q1(e):j1(e);if(typeof e=="string")return V1(e);throw new Error("Expected function, string, or object as test")};function q1(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=Us(e[n]);return Hn(r);function r(...u){let a=-1;for(;++a<t.length;)if(t[a].apply(this,u))return!0;return!1}}function j1(e){const t=e;return Hn(n);function n(r){const u=r;let a;for(a in e)if(u[a]!==t[a])return!1;return!0}}function V1(e){return Hn(t);function t(n){return n&&n.type===e}}function Hn(e){return t;function t(n,r,u){return!!(W1(n)&&e.call(this,n,typeof r=="number"?r:void 0,u||void 0))}}function $1(){return!0}function W1(e){return e!==null&&typeof e=="object"&&"type"in e}const Hs=[],Q1=!0,Ri=!1,X1="skip";function K1(e,t,n,r){let u;typeof t=="function"&&typeof n!="function"?(r=n,n=t):u=t;const a=Us(u),i=r?-1:1;o(e,void 0,[])();function o(l,c,h){const f=l&&typeof l=="object"?l:{};if(typeof f.type=="string"){const d=typeof f.tagName=="string"?f.tagName:typeof f.name=="string"?f.name:void 0;Object.defineProperty(m,"name",{value:"node ("+(l.type+(d?"<"+d+">":""))+")"})}return m;function m(){let d=Hs,_,y,M;if((!t||a(l,c,h[h.length-1]||void 0))&&(d=G1(n(l,h)),d[0]===Ri))return d;if("children"in l&&l.children){const k=l;if(k.children&&d[0]!==X1)for(y=(r?k.children.length:-1)+i,M=h.concat(k);y>-1&&y<k.children.length;){const F=k.children[y];if(_=o(F,y,M)(),_[0]===Ri)return _;y=typeof _[1]=="number"?_[1]:y+i}}return d}}}function G1(e){return Array.isArray(e)?e:typeof e=="number"?[Q1,e]:e==null?Hs:[e]}function J1(e,t,n,r){let u,a,i;a=t,i=n,u=r,K1(e,a,o,u);function o(l,c){const h=c[c.length-1],f=h?h.children.indexOf(l):void 0;return i(l,f,h)}}const Z1=/<(\/?)(iframe|noembed|noframes|plaintext|script|style|textarea|title|xmp)(?=[\t\n\f\r />])/gi,eE=new Set(["mdxFlowExpression","mdxJsxFlowElement","mdxJsxTextElement","mdxTextExpression","mdxjsEsm"]),Pi={sourceCodeLocationInfo:!0,scriptingEnabled:!1};function zs(e,t){const n=cE(e),r=os("type",{handlers:{root:tE,element:nE,text:rE,comment:qs,doctype:uE,raw:aE},unknown:sE}),u={parser:n?new Oi(Pi):Oi.getFragmentParser(void 0,Pi),handle(o){r(o,u)},stitches:!1,options:t||{}};r(e,u),wt(u,Et());const a=n?u.parser.document:u.parser.getFragment(),i=Vd(a,{file:u.options.file});return u.stitches&&J1(i,"comment",function(o,l,c){const h=o;if(h.value.stitch&&c&&l!==void 0){const f=c.children;return f[l]=h.value.stitch,l}}),i.type==="root"&&i.children.length===1&&i.children[0].type===e.type?i.children[0]:i}function Ys(e,t){let n=-1;if(e)for(;++n<e.length;)t.handle(e[n])}function tE(e,t){Ys(e.children,t)}function nE(e,t){oE(e,t),Ys(e.children,t),lE(e,t)}function rE(e,t){t.parser.tokenizer.state>4&&(t.parser.tokenizer.state=0);const n={type:G.CHARACTER,chars:e.value,location:fn(e)};wt(t,Et(e)),t.parser.currentToken=n,t.parser._processToken(t.parser.currentToken)}function uE(e,t){const n={type:G.DOCTYPE,name:"html",forceQuirks:!1,publicId:"",systemId:"",location:fn(e)};wt(t,Et(e)),t.parser.currentToken=n,t.parser._processToken(t.parser.currentToken)}function iE(e,t){t.stitches=!0;const n=fE(e);if("children"in e&&"children"in n){const r=zs({type:"root",children:e.children},t.options);n.children=r.children}qs({type:"comment",value:{stitch:n}},t)}function qs(e,t){const n=e.value,r={type:G.COMMENT,data:n,location:fn(e)};wt(t,Et(e)),t.parser.currentToken=r,t.parser._processToken(t.parser.currentToken)}function aE(e,t){if(t.parser.tokenizer.preprocessor.html="",t.parser.tokenizer.preprocessor.pos=-1,t.parser.tokenizer.preprocessor.lastGapPos=-2,t.parser.tokenizer.preprocessor.gapStack=[],t.parser.tokenizer.preprocessor.skipNextNewLine=!1,t.parser.tokenizer.preprocessor.lastChunkWritten=!1,t.parser.tokenizer.preprocessor.endOfChunkHit=!1,t.parser.tokenizer.preprocessor.isEol=!1,js(t,Et(e)),t.parser.tokenizer.write(t.options.tagfilter?e.value.replace(Z1,"&lt;$1$2"):e.value,!1),t.parser.tokenizer._runParsingLoop(),t.parser.tokenizer.state===72||t.parser.tokenizer.state===78){t.parser.tokenizer.preprocessor.lastChunkWritten=!0;const n=t.parser.tokenizer._consume();t.parser.tokenizer._callState(n)}}function sE(e,t){const n=e;if(t.options.passThrough&&t.options.passThrough.includes(n.type))iE(n,t);else{let r="";throw eE.has(n.type)&&(r=". It looks like you are using MDX nodes with `hast-util-raw` (or `rehype-raw`). If you use this because you are using remark or rehype plugins that inject `'html'` nodes, then please raise an issue with that plugin, as its a bad and slow idea. If you use this because you are using markdown syntax, then you have to configure this utility (or plugin) to pass through these nodes (see `passThrough` in docs), but you can also migrate to use the MDX syntax"),new Error("Cannot compile `"+n.type+"` node"+r)}}function wt(e,t){js(e,t);const n=e.parser.tokenizer.currentCharacterToken;n&&n.location&&(n.location.endLine=e.parser.tokenizer.preprocessor.line,n.location.endCol=e.parser.tokenizer.preprocessor.col+1,n.location.endOffset=e.parser.tokenizer.preprocessor.offset+1,e.parser.currentToken=n,e.parser._processToken(e.parser.currentToken)),e.parser.tokenizer.paused=!1,e.parser.tokenizer.inLoop=!1,e.parser.tokenizer.active=!1,e.parser.tokenizer.returnState=fe.DATA,e.parser.tokenizer.charRefCode=-1,e.parser.tokenizer.consumedAfterSnapshot=-1,e.parser.tokenizer.currentLocation=null,e.parser.tokenizer.currentCharacterToken=null,e.parser.tokenizer.currentToken=null,e.parser.tokenizer.currentAttr={name:"",value:""}}function js(e,t){if(t&&t.offset!==void 0){const n={startLine:t.line,startCol:t.column,startOffset:t.offset,endLine:-1,endCol:-1,endOffset:-1};e.parser.tokenizer.preprocessor.lineStartPos=-t.column+1,e.parser.tokenizer.preprocessor.droppedBufferSize=t.offset,e.parser.tokenizer.preprocessor.line=t.line,e.parser.tokenizer.currentLocation=n}}function oE(e,t){const n=e.tagName.toLowerCase();if(t.parser.tokenizer.state===fe.PLAINTEXT)return;wt(t,Et(e));const r=t.parser.openElements.current;let u="namespaceURI"in r?r.namespaceURI:lt.html;u===lt.html&&n==="svg"&&(u=lt.svg);const a=ap({...e,children:[]},{space:u===lt.svg?"svg":"html"}),i={type:G.START_TAG,tagName:n,tagID:Pt(n),selfClosing:!1,ackSelfClosing:!1,attrs:"attrs"in a?a.attrs:[],location:fn(e)};t.parser.currentToken=i,t.parser._processToken(t.parser.currentToken),t.parser.tokenizer.lastStartTagName=n}function lE(e,t){const n=e.tagName.toLowerCase();if(!t.parser.tokenizer.inForeignNode&&pp.includes(n)||t.parser.tokenizer.state===fe.PLAINTEXT)return;wt(t,Fs(e));const r={type:G.END_TAG,tagName:n,tagID:Pt(n),selfClosing:!1,ackSelfClosing:!1,attrs:[],location:fn(e)};t.parser.currentToken=r,t.parser._processToken(t.parser.currentToken),n===t.parser.tokenizer.lastStartTagName&&(t.parser.tokenizer.state===fe.RCDATA||t.parser.tokenizer.state===fe.RAWTEXT||t.parser.tokenizer.state===fe.SCRIPT_DATA)&&(t.parser.tokenizer.state=fe.DATA)}function cE(e){const t=e.type==="root"?e.children[0]:e;return!!(t&&(t.type==="doctype"||t.type==="element"&&t.tagName.toLowerCase()==="html"))}function fn(e){const t=Et(e)||{line:void 0,column:void 0,offset:void 0},n=Fs(e)||{line:void 0,column:void 0,offset:void 0};return{startLine:t.line,startCol:t.column,startOffset:t.offset,endLine:n.line,endCol:n.column,endOffset:n.offset}}function fE(e){return"children"in e?oi({...e,children:[]}):oi(e)}function hE(e){return function(t,n){return zs(t,{...e,file:n})}}const AE=e=>{const{content:t,isVisible:n,onClose:r,sheetId:u,showHeader:a=!0,onContentChange:i}=e,[o,l]=et.useState([]),[c,h]=et.useState(""),[f,m]=et.useState(""),d=et.useRef(null),_=(t==null?void 0:t.text)||"Exploration",y=(t==null?void 0:t.full_text)||"",M=["#FFEBEE","#E8F5E8","#E3F2FD","#FFFDE7","#F3E5F5","#FFF3E0","#E0F2F1","#FAFAFA","#FCE4EC","#F1F8E9"],k=A=>M[A%M.length],F=()=>{var N,L;return u?`chatfork_sheet_${u}`:`chatfork_content_${((L=(N=t==null?void 0:t.text)==null?void 0:N.substring(0,100))==null?void 0:L.replace(/[^a-zA-Z0-9]/g,""))||"default"}`},B=(A,N)=>{const L=F(),H={markdownText:A,selections:N,lastUpdated:new Date().toISOString()};try{if(localStorage.setItem(L,JSON.stringify(H)),i){const v={...t,full_text:A};i(v,N)}console.log("ChatFork state saved:",L)}catch(v){console.error("Failed to save ChatFork state:",v)}},j=()=>{const A=F();try{const N=localStorage.getItem(A);if(N){const L=JSON.parse(N);console.log("Loading ChatFork state:",A,L),h(L.markdownText||y);const H=(L.selections||[]).map(v=>({...v,created:new Date(v.created)}));return l(H),!0}}catch(N){console.error("Failed to load ChatFork state:",N)}return h(y),l([]),!1};et.useEffect(()=>{j()||console.log("No saved state found, using original content")},[y]),et.useEffect(()=>{const A=L=>{const v=L.target.closest("[data-selection]");if(v){const z=v.getAttribute("data-selection");z&&(m(z),console.log("Selected tab from text click:",z),L.preventDefault(),L.stopPropagation())}},N=d.current;if(N)return N.addEventListener("click",A),()=>N.removeEventListener("click",A)},[]),et.useEffect(()=>{const A=d.current;if(!A)return;if(A.querySelectorAll("[data-selection]").forEach(L=>{L.style.outline="",L.style.outlineOffset=""}),f){const L=A.querySelector(`[data-selection="${f}"]`);if(L){const H=L.getAttribute("data-color")||"#333";L.style.outline=`2px solid ${H}`,L.style.outlineOffset="1px"}}},[f,c]),et.useEffect(()=>{const A=N=>{var L,H;if(N.shiftKey&&N.key==="Tab"){const v=window.getSelection();if(!v||v.isCollapsed)return;const z=v.toString(),ae=v.anchorNode;if(!ae||ae.nodeType!==Node.TEXT_NODE)return;const he=ae.textContent||"",Ce=v.anchorOffset,Me=v.focusOffset,T=Math.min(Ce,Me),Ne=Math.max(Ce,Me),Ue=he.slice(0,T),b=he.slice(Ne),Oe=((L=Ue.match(/\b\w+$/))==null?void 0:L[0])||"",We=((H=b.match(/^\w+\b/))==null?void 0:H[0])||"",re=Oe+z+We,qe=`sel_${Date.now()}`,ke=c.indexOf(re);if(ke===-1)return;const Je=c.slice(0,ke),Ze=c.slice(ke+re.length),at=`${Je}[[mark:id=${qe}]]${re}[[/mark]]${Ze}`,zn={id:qe,text:re,startIndex:ke,endIndex:ke+re.length,created:new Date,userInput:"",llmResponse:""},hn=[...o,zn];h(at),l(hn),m(qe),B(at,hn),console.log("Created fork from selection:",re.substring(0,50)),Yn.registerEvent(qn.CHATFORK_TEXT_SELECTED,{text:re.substring(0,50)+(re.length>50?"...":""),length:re.length,sheetId:e.sheetId||null,method:"sticky"}),Yn.registerEvent(qn.CHATFORK_FORK_CREATED,{topic:re.substring(0,50)+(re.length>50?"...":""),text:re,method:"shift_tab_selection",sheetId:e.sheetId||null,forkId:qe,timestamp:new Date().toISOString()}),N.preventDefault()}};return window.addEventListener("keydown",A),()=>window.removeEventListener("keydown",A)},[c,e.sheetId]);const V=A=>A.replace(/\[\[mark:id=(.*?)\]\](.*?)\[\[\/mark\]\]/gs,(N,L,H)=>{const v=o.findIndex(ae=>ae.id===L),z=v>=0?k(v):"#FFF5F5";return`<mark data-selection="${L}" data-color="${z}" class="sticky-highlight" style="background-color: ${z}; border: 1px solid ${z}; cursor: pointer;">${H}</mark>`}),x=A=>{let N;try{if(A instanceof Date?N=A:N=new Date(A),isNaN(N.getTime()))return console.warn("Invalid date provided to formatTimestamp:",A),"Invalid Date";const L=N.getFullYear(),H=String(N.getMonth()+1).padStart(2,"0"),v=String(N.getDate()).padStart(2,"0"),z=String(N.getHours()).padStart(2,"0"),ae=String(N.getMinutes()).padStart(2,"0");return`${L}.${H}.${v} ${z}:${ae}`}catch(L){return console.error("Error formatting timestamp:",L,A),"Error"}},Q=A=>{const N=o.find(z=>z.id===A),L=o.filter(z=>z.id!==A),H=new RegExp(`\\[\\[mark:id=${A}\\]\\](.*?)\\[\\[/mark\\]\\]`,"g"),v=c.replace(H,"$1");f===A&&m(""),l(L),h(v),N&&Yn.registerEvent(qn.CHATFORK_FORK_CREATED,{topic:`Removed: ${N.text.substring(0,50)+(N.text.length>50?"...":"")}`,text:N.text,method:"remove_fork_button",action:"removed",sheetId:e.sheetId||null,forkId:A,timestamp:new Date().toISOString()}),B(v,L)},ne=(A,N)=>{const L=o.map(H=>H.id===A?{...H,userInput:N}:H);l(L),B(c,L)},ee=!!e.sheetId;return q.jsxs("div",{className:`chatfork-canvas-container ${ee?"in-mindsheet":""}`,ref:d,children:[a&&q.jsx("div",{className:"chatfork-canvas-header",children:q.jsx("h3",{children:_})}),q.jsxs("div",{className:"chatfork-layout",children:[q.jsxs("div",{className:"chatfork-main-content",children:[q.jsx("div",{className:"chatfork-main-header",children:q.jsx("h4",{children:"Initial Exploration"})}),q.jsx("div",{className:"chatfork-main-text",onClick:A=>A.stopPropagation(),children:c?q.jsx("div",{className:"chatfork-content-text",children:q.jsx(Ku,{remarkPlugins:[ui],rehypePlugins:[hE],children:V(c)})}):q.jsx("p",{className:"chatfork-empty-message",children:"No content available. This appears to be an empty ChatFork."})}),q.jsx("div",{className:"selection-instruction",children:q.jsxs("small",{children:["Select text and press ",q.jsx("kbd",{children:"Shift"}),"+",q.jsx("kbd",{children:"Tab"})," to create a fork chat"]})})]}),o.length>0&&q.jsx("div",{className:"chatfork-vertical-tabs",children:o.map((A,N)=>q.jsx("button",{className:`chatfork-vertical-tab ${f===A.id?"active":""}`,style:{backgroundColor:k(N),borderColor:k(N)},onClick:()=>m(A.id),title:`Fork ${N+1}: ${A.text.substring(0,50)}...`,children:N+1},A.id))}),q.jsxs("div",{className:"chatfork-forks-area",children:[q.jsxs("div",{className:"chatfork-forks-header",children:[q.jsx("h4",{children:"FORKED CHATS LEVEL 1"}),q.jsx("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:q.jsxs("span",{className:"chatfork-forks-count",children:[o.length," active"]})})]}),q.jsx("div",{className:"chatfork-forks-content",children:o.length>0?f?(()=>{const A=o.find(L=>L.id===f);if(!A)return q.jsx("div",{children:"Fork not found"});const N=o.findIndex(L=>L.id===f);return q.jsxs("div",{className:"fork-chat-item",children:[q.jsx("div",{className:"fork-selected-text",children:q.jsxs("div",{className:"fork-text-preview",style:{backgroundColor:k(N),borderColor:k(N),fontFamily:"Arial, sans-serif",fontWeight:"bold",display:"flex",justifyContent:"space-between",alignItems:"flex-start",gap:"10px"},children:[q.jsxs("span",{style:{flex:"1"},children:[A.text," ",q.jsxs("span",{style:{fontWeight:"normal"},children:["[1.",N+1,"]"]})]}),q.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px",fontSize:"12px",fontWeight:"normal",whiteSpace:"nowrap"},children:[q.jsxs("small",{children:["[",x(A.created)," ",A.id,"]"]}),q.jsx("button",{onClick:()=>Q(A.id),className:"fork-remove-btn",title:"Remove fork chat",style:{marginLeft:"4px"},children:"✕"})]})]})}),q.jsxs("div",{className:"fork-chat-content",children:[q.jsx("div",{className:"fork-user-input",children:q.jsx("textarea",{placeholder:"Ask about this selection...",value:A.userInput||"",onChange:L=>ne(A.id,L.target.value),className:"fork-input-field"})}),q.jsx("div",{className:"fork-llm-response",children:A.llmResponse?q.jsx("div",{className:"fork-response-content",children:q.jsx(Ku,{remarkPlugins:[ui],children:A.llmResponse})}):q.jsx("div",{className:"fork-response-placeholder",children:"LLM response will appear here..."})})]})]},A.id)})():q.jsx("div",{className:"chatfork-forks-placeholder",children:q.jsx("p",{children:"Click a tab to view fork details."})}):q.jsx("div",{className:"chatfork-forks-placeholder",children:q.jsxs("p",{children:["Select text and press ",q.jsx("kbd",{children:"Shift"}),"+",q.jsx("kbd",{children:"Tab"})," to create fork chats."]})})})]})]})]})};export{AE as default};
