import { Node as KonvaNode } from 'konva/lib/Node';
import { Connection } from '../core/models/Connection';
import { Node } from '../core/models/Node';

/**
 * Layout strategy names
 */
export type LayoutStrategyType = 'leftToRight' | 'topDown' | 'radial' | 'bottomUp' | 'compactLeftToRight';

/**
 * Configuration options for layout calculations
 */
export interface LayoutConfig {
  nodeWidth: number;
  nodeHeight: number;
  horizontalSpacing: number;
  verticalSpacing: number;
  levelSpacing: number;
  // Compaction parameters
  siblingCompactionFactor?: number; // Factor to reduce spacing between siblings (0-1)
  levelCompactionFactor?: number;   // Factor to reduce spacing between levels (0-1)
  adaptiveSpacing?: boolean;        // Whether to use adaptive spacing based on node size
  minimumHorizontalSpacing?: number; // Minimum horizontal spacing regardless of compaction
  minimumVerticalSpacing?: number;   // Minimum vertical spacing regardless of compaction
}

/**
 * Default layout configuration
 */
export const DEFAULT_LAYOUT_CONFIG: LayoutConfig = {
  nodeWidth: 200,
  nodeHeight: 100,
  horizontalSpacing: 100,
  verticalSpacing: 80,
  levelSpacing: 250,
  // Default compaction settings
  siblingCompactionFactor: 0.6,
  levelCompactionFactor: 0.7,
  adaptiveSpacing: true,
  minimumHorizontalSpacing: 30,
  minimumVerticalSpacing: 25,
};

/**
 * Represents a node in a leveled tree structure
 */
export interface TreeLevel {
  level: number;
  nodes: string[];
}

/**
 * Interface for layout strategy implementations
 */
export interface LayoutStrategy {
  /**
   * Calculate positions for all nodes
   */
  calculateLayout(
    nodes: Record<string, Node>,
    connections: Connection[],
    rootId: string,
    config?: LayoutConfig
  ): Record<string, Node>;
  
  /**
   * Name of the strategy
   */
  readonly name: LayoutStrategyType;
}

/**
 * Interface for layout manager
 */
export interface LayoutManager {
  /**
   * Apply a layout strategy to nodes
   */
  applyLayout(
    nodes: Record<string, Node>,
    connections: Connection[],
    rootId: string,
    strategyType: LayoutStrategyType,
    config?: LayoutConfig
  ): Record<string, Node>;
  
  /**
   * Register a new layout strategy
   */
  registerStrategy(strategy: LayoutStrategy): void;
  
  /**
   * Get available strategy types
   */
  getAvailableStrategies(): LayoutStrategyType[];
} 