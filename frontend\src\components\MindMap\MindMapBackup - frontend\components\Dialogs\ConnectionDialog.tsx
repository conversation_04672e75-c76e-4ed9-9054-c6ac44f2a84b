/**
 * ConnectionDialog Component
 * Dialog for editing connection properties
 */

import React, { useState } from 'react';
import { Connection, ConnectionType, LineStyleType } from '../../core/models/Connection';
import { useMindMapStore } from '../../core/state/MindMapStore';

interface ConnectionDialogProps {
  connection: Connection | null;
  onClose: () => void;
}

/**
 * ConnectionDialog Component
 */
const ConnectionDialog: React.FC<ConnectionDialogProps> = ({ connection, onClose }) => {
  const { updateConnection } = useMindMapStore();
  
  // States
  const [label, setLabel] = useState(connection?.label || '');
  const [color, setColor] = useState(connection?.color || '#9ca3af');
  const [thickness, setThickness] = useState(connection?.thickness || 1);
  const [type, setType] = useState<ConnectionType>(connection?.type || 'solid');
  const [lineStyle, setLineStyle] = useState<LineStyleType>(connection?.lineStyle || 'angled');
  const [showArrow, setShowArrow] = useState(connection?.showArrow === true); // Default to false
  const [activeTab, setActiveTab] = useState('info');
  
  // Handle dialog close
  const handleClose = () => {
    onClose();
  };
  
  // Handle save
  const handleSave = () => {
    if (!connection) return;
    
    updateConnection(connection.id, {
      label,
      color,
      thickness,
      type,
      lineStyle,
      showArrow,
      updatedAt: Date.now()
    });
    
    onClose();
  };
  
  // Handle tab switch
  const handleTabSwitch = (tab: string) => {
    setActiveTab(tab);
  };
  
  if (!connection) return null;
  
  return (
    <div className="connection-dialog" style={{
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      width: '400px',
      background: 'white',
      borderRadius: '8px',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
      zIndex: 1002
    }}>
      <div style={{ borderBottom: '1px solid #e5e7eb', padding: '12px 16px' }}>
        <h3 style={{ margin: 0, fontSize: '16px' }}>Edit Connection</h3>
      </div>
      
      {/* Tab Navigation */}
      <div style={{ 
        display: 'flex', 
        borderBottom: '1px solid #e5e7eb'
      }}>
        <TabButton 
          label="Information" 
          isActive={activeTab === 'info'} 
          onClick={() => handleTabSwitch('info')} 
        />
        <TabButton 
          label="Design" 
          isActive={activeTab === 'design'} 
          onClick={() => handleTabSwitch('design')} 
        />
      </div>
      
      {/* Tab Content */}
      <div style={{ padding: '16px' }}>
        {/* Info Tab */}
        {activeTab === 'info' && (
          <div>
            <div style={{ marginBottom: '16px' }}>
              <div style={{ marginBottom: '8px', fontSize: '12px', color: '#6b7280' }}>
                Connection ID: {connection.id.substring(0, 8)}
              </div>
              <div style={{ marginBottom: '8px', fontSize: '12px', color: '#6b7280' }}>
                From Node: {connection.from.substring(0, 8)}
              </div>
              <div style={{ marginBottom: '8px', fontSize: '12px', color: '#6b7280' }}>
                To Node: {connection.to.substring(0, 8)}
              </div>
            </div>
            
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px' }}>
                Label/Caption:
              </label>
              <input 
                type="text" 
                value={label}
                onChange={(e) => setLabel(e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px',
                  borderRadius: '4px',
                  border: '1px solid #e5e7eb',
                  fontSize: '14px'
                }}
                placeholder="Enter connection label"
              />
            </div>
          </div>
        )}
        
        {/* Design Tab */}
        {activeTab === 'design' && (
          <div>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px' }}>
                Line Style:
              </label>
              <select
                value={lineStyle}
                onChange={(e) => setLineStyle(e.target.value as LineStyleType)}
                style={{
                  width: '100%',
                  padding: '8px',
                  borderRadius: '4px',
                  border: '1px solid #e5e7eb',
                  fontSize: '14px'
                }}
              >
                <option value="angled">Angled (Default)</option>
                <option value="straight">Straight Line</option>
                <option value="curved">Curved Line</option>
              </select>
            </div>
            
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px' }}>
                Line Pattern:
              </label>
              <select
                value={type}
                onChange={(e) => setType(e.target.value as ConnectionType)}
                style={{
                  width: '100%',
                  padding: '8px',
                  borderRadius: '4px',
                  border: '1px solid #e5e7eb',
                  fontSize: '14px'
                }}
              >
                <option value="solid">Solid Line</option>
                <option value="dashed">Dashed Line</option>
                <option value="dotted">Dotted Line</option>
              </select>
            </div>
            
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px' }}>
                Show Arrow:
              </label>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <input
                  type="checkbox"
                  checked={showArrow}
                  onChange={(e) => setShowArrow(e.target.checked)}
                  style={{ marginRight: '8px' }}
                />
                <span>Display arrow at the end of the connection</span>
              </div>
            </div>
            
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px' }}>
                Color:
              </label>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <input 
                  type="color" 
                  value={color}
                  onChange={(e) => setColor(e.target.value)}
                  style={{
                    width: '40px',
                    height: '40px',
                    border: 'none',
                    padding: 0,
                    borderRadius: '4px'
                  }}
                />
                <input 
                  type="text" 
                  value={color}
                  onChange={(e) => setColor(e.target.value)}
                  style={{
                    flex: 1,
                    padding: '8px',
                    borderRadius: '4px',
                    border: '1px solid #e5e7eb',
                    fontSize: '14px'
                  }}
                />
              </div>
            </div>
            
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px' }}>
                Thickness:
              </label>
              <select
                value={thickness}
                onChange={(e) => setThickness(Number(e.target.value))}
                style={{
                  width: '100%',
                  padding: '8px',
                  borderRadius: '4px',
                  border: '1px solid #e5e7eb',
                  fontSize: '14px'
                }}
              >
                <option value="1">Thin (1px)</option>
                <option value="2">Medium (2px)</option>
                <option value="3">Thick (3px)</option>
              </select>
            </div>
            
            {/* Preview */}
            <div style={{ 
              marginTop: '16px', 
              padding: '12px',
              border: '1px solid #e5e7eb',
              borderRadius: '4px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100px',
              backgroundColor: '#f9fafb'
            }}>
              <div style={{ width: '200px', position: 'relative', height: '60px' }}>
                <svg width="100%" height="100%">
                  {/* Preview based on line style */}
                  {lineStyle === 'straight' && (
                    <path
                      d={`M 20,30 L 180,30`}
                      stroke={color}
                      strokeWidth={thickness}
                      strokeDasharray={type === 'dashed' ? '5,5' : type === 'dotted' ? '2,2' : 'none'}
                      fill="none"
                      markerEnd={showArrow ? "url(#preview-arrow)" : ''}
                    />
                  )}
                  
                  {lineStyle === 'angled' && (
                    <path
                      d={`M 20,30 L 100,30 L 100,30 L 180,30`}
                      stroke={color}
                      strokeWidth={thickness}
                      strokeDasharray={type === 'dashed' ? '5,5' : type === 'dotted' ? '2,2' : 'none'}
                      fill="none"
                      markerEnd={showArrow ? "url(#preview-arrow)" : ''}
                    />
                  )}
                  
                  {lineStyle === 'curved' && (
                    <path
                      d={`M 20,30 Q 100,0 180,30`}
                      stroke={color}
                      strokeWidth={thickness}
                      strokeDasharray={type === 'dashed' ? '5,5' : type === 'dotted' ? '2,2' : 'none'}
                      fill="none"
                      markerEnd={showArrow ? "url(#preview-arrow)" : ''}
                    />
                  )}
                  
                  {/* Arrow marker for preview */}
                  <defs>
                    <marker
                      id="preview-arrow"
                      markerWidth="10"
                      markerHeight="10"
                      refX="9"
                      refY="5"
                      orient="auto"
                      markerUnits="strokeWidth"
                    >
                      <path d="M0,0 L0,10 L10,5 L0,0" fill={color} />
                    </marker>
                  </defs>
                </svg>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* Actions */}
      <div style={{ 
        borderTop: '1px solid #e5e7eb', 
        padding: '12px 16px',
        display: 'flex',
        justifyContent: 'flex-end',
        gap: '8px'
      }}>
        <button
          onClick={handleClose}
          style={{
            padding: '6px 12px',
            background: 'white',
            border: '1px solid #e5e7eb',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Cancel
        </button>
        <button
          onClick={handleSave}
          style={{
            padding: '6px 12px',
            background: '#3b82f6',
            color: 'white',
            border: '1px solid #3b82f6',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Save
        </button>
      </div>
    </div>
  );
};

interface TabButtonProps {
  label: string;
  isActive: boolean;
  onClick: () => void;
}

const TabButton: React.FC<TabButtonProps> = ({ label, isActive, onClick }) => {
  return (
    <button
      onClick={onClick}
      style={{
        padding: '8px 16px',
        background: isActive ? '#f3f4f6' : 'transparent',
        border: 'none',
        borderBottom: isActive ? '2px solid #3b82f6' : '2px solid transparent',
        cursor: 'pointer',
        fontWeight: isActive ? 500 : 400,
        color: isActive ? '#3b82f6' : '#6b7280'
      }}
    >
      {label}
    </button>
  );
};

export default ConnectionDialog; 