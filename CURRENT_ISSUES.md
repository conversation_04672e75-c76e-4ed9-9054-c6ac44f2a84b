# MindMap Issues Tracking

## 🔥 CRITICAL RULE: NO REWRITES OR <PERSON><PERSON><PERSON>ECTURAL CHANGES
**Only fix specific bugs and add small features. Do not change core architecture.**

## Current Status: FUNCTIONAL ✅
- Automatic mindmap creation works
- Manual node creation (Tab key) works  
- Node editing works
- Multiple sheets work
- Canvas rendering works

## Issues to Fix (One at a time, test before moving to next)

### 1. Visual Distinction for Manual vs Auto Nodes ⏳
**Problem**: Can't tell which nodes were added manually vs automatically
**Solution**: Add visual styling only, no logic changes
**Files to touch**: 
- `frontend/src/features/mindmap/components/Canvas/NodeComponent.tsx` (add styling)
**Test**: Create auto mindmap, add manual node, verify different border colors
**Status**: Not started

### 2. Layout Preservation for Manual Nodes ⏳  
**Problem**: Auto-layout overwrites manual node positions
**Solution**: Check for manual flag before repositioning in layout
**Files to touch**:
- Layout algorithm (preserve positions with `isManuallyAdded: true`)
**Test**: Move node manually, trigger layout, verify position kept
**Status**: Not started

### 3. Manager-Canvas Store Sync ⏳
**Problem**: Manager and canvas sometimes use different store instances
**Solution**: Ensure both use same store reference
**Files to touch**:
- `EnhancedMindMapManager.tsx` (store reference fix)
**Test**: Open manager, make changes, verify canvas updates
**Status**: Not started

### 4. Remove LLM Route Duplication ⏳
**Problem**: `llm.py` and `llm_fixed.py` both exist
**Solution**: Choose one, delete the other
**Files to touch**: Backend route files
**Test**: Verify mindmap creation still works
**Status**: Not started

## COMPLETED ✅
*Nothing yet - track completed fixes here*

## BLOCKED/POSTPONED 🚫
*Major changes that would break things - avoid these*

## Rules for Making Changes

### ✅ ALLOWED:
- Add CSS styling
- Add small visual indicators  
- Fix specific bugs in existing functions
- Add simple conditional logic
- Remove duplicate files (carefully)

### 🚫 FORBIDDEN:
- Change store architecture
- Rewrite components from scratch
- Change how stores are accessed
- Modify core rendering logic
- Change the MBCP processing flow
- Alter the sheet system

### Before Making ANY Change:
1. Create a backup of the file
2. Make the smallest possible change
3. Test immediately 
4. If it breaks anything, revert immediately
5. Only then move to next issue

## Daily Workflow
1. Pick ONE issue from the list above
2. Make the minimal change needed
3. Test thoroughly
4. Mark as completed
5. Commit the change
6. Move to next issue tomorrow

## Emergency Recovery
If anything breaks:
1. `git stash` (save current changes)
2. `git reset --hard HEAD~1` (go back one commit)
3. Test that it works again
4. If still broken, go back further until it works 