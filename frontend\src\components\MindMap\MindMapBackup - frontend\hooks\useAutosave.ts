import { useEffect } from 'react';
import { useMindMapStore } from '../core/state/MindMapStore';

/**
 * Hook to implement automatic saving of the MindMap state
 * 
 * Features:
 * 1. Periodic autosave at specified intervals
 * 2. Save on window blur (when user switches to another window/tab)
 * 3. Save on visibility change (when page becomes hidden)
 * 
 * @param autosaveInterval Interval in milliseconds between autosaves (default: 30000ms = 30s)
 */
export const useAutosave = (autosaveInterval = 30000) => {
  useEffect(() => {
    console.log('Initializing autosave functionality');
    
    // Get the saveProject function from the store
    const saveProject = useMindMapStore.getState().saveProject;
    
    // Function to perform the save operation
    const performSave = () => {
      console.log('Autosave: Saving current project state');
      const result = saveProject();
      if (result) {
        console.log('Autosave: Project saved successfully at', new Date().toLocaleTimeString());
      }
    };
    
    // Set up periodic autosave
    const intervalId = setInterval(performSave, autosaveInterval);
    console.log(`Autosave: Set up automatic saving every ${autosaveInterval / 1000} seconds`);
    
    // Save when window loses focus
    const handleBlur = () => {
      console.log('Autosave: Window lost focus, saving project');
      performSave();
    };
    
    // Save when page visibility changes to hidden
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        console.log('Autosave: Page visibility changed to hidden, saving project');
        performSave();
      }
    };
    
    // Add event listeners
    window.addEventListener('blur', handleBlur);
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Clean up on unmount
    return () => {
      console.log('Autosave: Cleaning up autosave functionality');
      clearInterval(intervalId);
      window.removeEventListener('blur', handleBlur);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      
      // Final save on unmount
      performSave();
    };
  }, [autosaveInterval]);
}; 