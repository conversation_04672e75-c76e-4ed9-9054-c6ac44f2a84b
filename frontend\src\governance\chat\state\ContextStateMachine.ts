/**
 * ContextStateMachine.ts
 * 
 * A professional state machine implementation for managing context transitions
 * in the governance chat component. This prevents infinite loops and ensures
 * proper state transitions with circuit breaker protection.
 */

import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';

// Define the possible context states
export enum ContextState {
  IDLE = 'idle',
  TELEOLOGICAL = 'teleological',
  MINDMAP = 'mindmap',
  CHATFORK = 'chatfork',
  INSTANTANIOUS = 'instantanious',
  TEMPLATE = 'template',
  TRANSITIONING = 'transitioning',
  ERROR = 'error'
}

// Define the possible transition events
export enum TransitionEvent {
  SELECT_TELEOLOGICAL = 'select_teleological',
  SELECT_MINDMAP = 'select_mindmap',
  SELECT_CHATFORK = 'select_chatfork',
  SELECT_INSTANTANIOUS = 'select_instantanious',
  SELECT_TEMPLATE = 'select_template',
  TRANSITION_COMPLETE = 'transition_complete',
  TRANSITION_ERROR = 'transition_error',
  RESET = 'reset'
}

// Define the transition result
export interface TransitionResult {
  success: boolean;
  previousState: ContextState;
  currentState: ContextState;
  error?: Error;
  transitionId: string;
}

// Circuit breaker configuration
interface CircuitBreakerConfig {
  maxTransitions: number;
  timeWindowMs: number;
  cooldownMs: number;
}

// Default circuit breaker settings
const DEFAULT_CIRCUIT_BREAKER: CircuitBreakerConfig = {
  maxTransitions: 5,
  timeWindowMs: 2000,
  cooldownMs: 5000
};

/**
 * Context State Machine
 * 
 * Manages transitions between different context states with circuit breaker
 * protection to prevent infinite loops.
 */
export class ContextStateMachine extends EventEmitter {
  private currentState: ContextState = ContextState.IDLE;
  private transitionCount: number = 0;
  private transitionTimes: number[] = [];
  private circuitOpen: boolean = false;
  private lastTransitionId: string | null = null;
  private circuitBreakerConfig: CircuitBreakerConfig;
  private transitionInProgress: boolean = false;
  private templateValue: string | null = null;

  constructor(config: Partial<CircuitBreakerConfig> = {}) {
    super();
    this.circuitBreakerConfig = {
      ...DEFAULT_CIRCUIT_BREAKER,
      ...config
    };
  }

  /**
   * Get the current state
   */
  public getState(): ContextState {
    return this.currentState;
  }

  /**
   * Get the current template value (if in template state)
   */
  public getTemplateValue(): string | null {
    return this.templateValue;
  }

  /**
   * Check if a transition is in progress
   */
  public isTransitioning(): boolean {
    return this.transitionInProgress;
  }

  /**
   * Check if the circuit breaker is open
   */
  public isCircuitOpen(): boolean {
    return this.circuitOpen;
  }

  /**
   * Reset the state machine to idle
   */
  public reset(): void {
    this.currentState = ContextState.IDLE;
    this.transitionCount = 0;
    this.transitionTimes = [];
    this.circuitOpen = false;
    this.lastTransitionId = null;
    this.transitionInProgress = false;
    this.templateValue = null;
    this.emit('reset');
  }

  /**
   * Trigger a state transition
   */
  public transition(event: TransitionEvent, data?: any): TransitionResult {
    // Generate a unique ID for this transition
    const transitionId = uuidv4();

    // Check if a transition is already in progress
    if (this.transitionInProgress) {
      const error = new Error('Transition already in progress');
      return {
        success: false,
        previousState: this.currentState,
        currentState: this.currentState,
        error,
        transitionId
      };
    }

    // Check if circuit breaker is open
    if (this.circuitOpen) {
      const error = new Error('Circuit breaker open - too many transitions');
      return {
        success: false,
        previousState: this.currentState,
        currentState: this.currentState,
        error,
        transitionId
      };
    }

    // Update circuit breaker state
    this.updateCircuitBreaker();

    // Set transition in progress
    this.transitionInProgress = true;
    this.lastTransitionId = transitionId;

    // Store the previous state
    const previousState = this.currentState;

    try {
      // Handle the transition based on current state and event
      switch (event) {
        case TransitionEvent.SELECT_TELEOLOGICAL:
          this.currentState = ContextState.TRANSITIONING;
          this.emit('transitioning', { from: previousState, to: ContextState.TELEOLOGICAL, transitionId });
          this.currentState = ContextState.TELEOLOGICAL;
          break;

        case TransitionEvent.SELECT_MINDMAP:
          this.currentState = ContextState.TRANSITIONING;
          this.emit('transitioning', { from: previousState, to: ContextState.MINDMAP, transitionId });
          this.currentState = ContextState.MINDMAP;
          break;

        case TransitionEvent.SELECT_CHATFORK:
          this.currentState = ContextState.TRANSITIONING;
          this.emit('transitioning', { from: previousState, to: ContextState.CHATFORK, transitionId });
          this.currentState = ContextState.CHATFORK;
          break;

        case TransitionEvent.SELECT_INSTANTANIOUS:
          this.currentState = ContextState.TRANSITIONING;
          this.emit('transitioning', { from: previousState, to: ContextState.INSTANTANIOUS, transitionId });
          this.currentState = ContextState.INSTANTANIOUS;
          break;

        case TransitionEvent.SELECT_TEMPLATE:
          this.currentState = ContextState.TRANSITIONING;
          this.templateValue = data?.value || null;
          this.emit('transitioning', { from: previousState, to: ContextState.TEMPLATE, transitionId, templateValue: this.templateValue });
          this.currentState = ContextState.TEMPLATE;
          break;

        case TransitionEvent.TRANSITION_ERROR:
          this.currentState = ContextState.ERROR;
          break;

        case TransitionEvent.RESET:
          this.reset();
          break;

        default:
          throw new Error(`Unsupported event: ${event}`);
      }

      // Emit the state change event
      this.emit('stateChanged', {
        from: previousState,
        to: this.currentState,
        transitionId
      });

      return {
        success: true,
        previousState,
        currentState: this.currentState,
        transitionId
      };
    } catch (error) {
      // Handle transition error
      this.currentState = ContextState.ERROR;
      
      return {
        success: false,
        previousState,
        currentState: this.currentState,
        error: error instanceof Error ? error : new Error(String(error)),
        transitionId
      };
    } finally {
      // Release the transition lock after a short delay
      setTimeout(() => {
        this.transitionInProgress = false;
        this.emit('transitionComplete', {
          from: previousState,
          to: this.currentState,
          transitionId
        });
      }, 300);
    }
  }

  /**
   * Update the circuit breaker state
   */
  private updateCircuitBreaker(): void {
    const now = Date.now();
    
    // Add the current time to the transition times
    this.transitionTimes.push(now);
    
    // Remove transitions outside the time window
    this.transitionTimes = this.transitionTimes.filter(
      time => now - time < this.circuitBreakerConfig.timeWindowMs
    );
    
    // Check if we've exceeded the maximum number of transitions
    if (this.transitionTimes.length > this.circuitBreakerConfig.maxTransitions) {
      this.circuitOpen = true;
      this.emit('circuitOpen');
      
      // Reset the circuit breaker after the cooldown period
      setTimeout(() => {
        this.circuitOpen = false;
        this.transitionTimes = [];
        this.emit('circuitClosed');
      }, this.circuitBreakerConfig.cooldownMs);
    }
  }
}

// Create a singleton instance
export const contextStateMachine = new ContextStateMachine();

// Export the singleton
export default contextStateMachine;
