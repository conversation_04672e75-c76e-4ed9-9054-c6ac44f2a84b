import React, { useState, useEffect, useRef, useCallback, lazy, Suspense } from 'react';
import ProjectDialog from '../features/mindmap/components/Dialogs/ProjectDialog';
import { useMindMapStore } from '../core/state/MindMapStore';
import { useMindBookStore } from '../core/state/MindBookStore';
import { ChatResponse, ResponseTypeEnum } from '../services/api/GovernanceLLM';
import { createMindmapFromMBCP } from './MindMap/utils/MBCPProcessor';
import './OptimizedMindMap.css';
import { Card, Dialog, DialogActions, DialogContent, DialogTitle, Button } from '@mui/material';
import MindMapCanvasWrapper from '../features/mindmap/components/Canvas/MindMapCanvasWrapper';
import { EnhancedMindMapManager } from './MindMap/components/Manager';
import { GovernanceChatDialog } from '../governance/chat';
import MindSheetTabs from '../features/mindsheet/MindSheetTabs';

// Main component that composes everything together
interface OptimizedMindMap_ModularProps {
  initialAction?: any;
}

const OptimizedMindMap_Modular: React.FC<OptimizedMindMap_ModularProps> = ({ initialAction }) => {
  // Removed governance chat state variables as they're handled by InitialView
  // We still need these for backward compatibility with legacy code
  const [chatForkContainerVisible, setChatForkContainerVisible] = useState(false);
  const [chatForkContent, setChatForkContent] = useState<ChatResponse | null>(null);

  // Get selected node and editing state from store
  const {
    selectedNodeId,
    nodes,
    connections,
    // isEditing has been removed
    updateNode,
    setShowMindMapManager,
    showMindMapManager,
    showProjectDialog,
    setShowProjectDialog
  } = useMindMapStore(state => ({
    selectedNodeId: state.selectedNodeId,
    nodes: state.nodes,
    connections: state.connections,
    // isEditing has been removed
    updateNode: state.updateNode,
    setShowMindMapManager: state.setShowMindMapManager,
    showMindMapManager: state.showMindMapManager,
    showProjectDialog: state.showProjectDialog,
    setShowProjectDialog: state.setShowProjectDialog
  }));

  // Get the active sheet ID from MindBookStore
  const activeSheetId = useMindBookStore(state => state.activeSheetId);

  // Create a mindMapState object to pass to GovernanceChatDialog
  const mindMapState = {
    nodes,
    connections,
    selectedNodeId
  };

  // ChatFork state should only be accessed when needed
  // const chatForkVisible = useChatForkStore(state => state.isVisible);

  // Get the selected node if one is selected
  const selectedNode = selectedNodeId ? nodes[selectedNodeId] : null;

  // Handle node updates
  const handleNodeSave = (updatedNode) => {
    if (updatedNode && updatedNode.id) {
      updateNode(updatedNode.id, updatedNode);
    }
  };

  // Handle opening the MindMap Manager
  const handleOpenMindMapManager = () => {
    console.log('Opening MindMap Manager, current state:', showMindMapManager);
    setShowMindMapManager(true);
    console.log('MindMap Manager state set to true');
  };

  // Add a handler for ChatFork actions
  const handleChatForkAction = useCallback((action: any) => {
    console.log('[OptimizedMindMap] handleChatForkAction', action);

    // Handle create_mindmap action type
    if (action?.type === 'create_mindmap') {
      console.log('[OptimizedMindMap] Processing create_mindmap action:', action);

      // Extract the MBCP data from the action - use the correct path based on how it's structured
      // The MBCP data is in action.data.mbcpData for teleological intent
      const mbcpData = action.data?.mbcpData || action.data?.content || action.data;
      console.log('[OptimizedMindMap] MBCP data for mindmap:', JSON.stringify(mbcpData, null, 2));
      console.log('[OptimizedMindMap] Action structure:', JSON.stringify(action, null, 2));

      // Check if this is an incomplete action (flagged by useChat.ts)
      if (action.data?.incomplete) {
        console.warn('[OptimizedMindMap] Received incomplete MBCP data');
        console.warn('[OptimizedMindMap] This indicates a backend issue - the backend should provide a complete MBCP structure with children');
      }

      if (mbcpData) {
        // Process the MBCP data to create a mindmap
        try {
          console.log('[OptimizedMindMap] Creating mindmap from MBCP data');
          // Use the createMindmapFromMBCP utility to process the MBCP data
          // This will generate default children if none are provided
          const success = createMindmapFromMBCP(mbcpData);

          if (success) {
            console.log('[OptimizedMindMap] Mindmap created successfully');
          } else {
            console.log('[OptimizedMindMap] Could not create mindmap with the provided data');
            // Continue without showing an error message
            return false;
          }

          // No need to minimize governance chat here as it's handled by InitialView
          return true;
        } catch (error) {
          console.error('[OptimizedMindMap] Error creating mindmap:', error);
        }
      } else {
        console.error('[OptimizedMindMap] No MBCP data found in create_mindmap action');
      }
    }

    // Handle show_chatfork action type
    else if (action?.type === 'show_chatfork') {
      // Get the action data - this may come in different formats
      const actionData = action.data || {};
      console.log('[OptimizedMindMap] ChatFork action data:', actionData);

      // Extract intent from multiple possible locations for robustness
      const intent = actionData.intent ||
                    actionData.responseType?.type ||
                    actionData.content?.intent;

      console.log('[OptimizedMindMap] Detected intent:', intent);

      // Close only the ChatFork components, not GovernanceChat
      setChatForkContainerVisible(false);

      // ChatFork handling should be implemented in a way that doesn't require loading at startup
      // This code has been commented out to prevent loading ChatFork components at startup

      // // Check if the action contains the required fields for ChatFork
      // const hasChatForkData = action.data?.full_text ||
      //                        action.data?.content?.full_text ||
      //                        action.data?.templateOutput?.full_text;
      //
      // // Check if the action contains UI labels for ChatFork
      // const hasChatForkUI = action.data?.ui_labels?.chatfork_button ||
      //                      action.data?.content?.ui_labels?.chatfork_button;
      //
      // // Determine if this should be handled by ChatForkAdapter based on data structure
      // if (hasChatForkData || hasChatForkUI) {
      //   console.log('[OptimizedMindMap] Using ChatForkView based on data structure');
      //
      //   // Let the adapter handle it - this affects only ChatForkView, not GovernanceChat
      //   const handled = ChatForkAdapter.handleChatForkAction(action);
      //
      //   if (handled) {
      //     console.log('[OptimizedMindMap] Action handled by ChatForkAdapter');
      //     // Don't hide GovernanceChat - allow them to coexist
      //     return true;
      //   }
      // }

      console.log('[OptimizedMindMap] ChatFork handling is disabled to prevent loading at startup');

      // Create a ChatResponse object for backward compatibility
      const chatResponse: ChatResponse = {
        text: action.data.title || action.data.text || 'ChatFork content',
        responseType: {
          type: action.data.intent || 'default',
          requiresMindmap: false,
          requiresChatFork: true
        },
        suggestedActions: [],
        templateOutput: action.data.templateOutput || action.data.content || action.data.mindmap || action.data
      };

      // Show the legacy ChatForkContainer
      setChatForkContainerVisible(true);
      setChatForkContent(chatResponse);

      // This is the key change: DO NOT close the governance chat
      // Allow both to be visible simultaneously
      return true;
    }

    return false;
  }, [setChatForkContent]);

  // Removed governance chat functions as they're handled by InitialView

  // Handle initial action if provided
  useEffect(() => {
    if (initialAction) {
      console.log('[OptimizedMindMap] Processing initial action:', initialAction);

      // Check if this is a direct mindmap action from IntentSelector
      if (initialAction.sheetId) {
        console.log('[OptimizedMindMap] Direct mindmap action with sheetId detected:', initialAction.sheetId);

        // Defer the state update to avoid React warnings about updating during render
        setTimeout(() => {
          // Get the MindBookStore
          const mindBookStore = useMindBookStore.getState();

          // Set the active sheet
          mindBookStore.setActiveSheet(initialAction.sheetId);
          console.log('[OptimizedMindMap] Set active sheet to:', initialAction.sheetId);

          // Get the MindMapStore
          const mindMapStore = useMindMapStore.getState();

          // Process the MBCP data to create a mindmap
          try {
            console.log('[OptimizedMindMap] Creating mindmap from MBCP data');
            // Use the createMindmapFromMBCP utility to process the MBCP data
            const success = createMindmapFromMBCP(initialAction.data);

            if (success) {
              console.log('[OptimizedMindMap] Mindmap created successfully');

              // Update the layout
              setTimeout(() => {
                console.log('[OptimizedMindMap] Updating layout');
                mindMapStore.updateLayout('tree');

                // Select the root node
                const rootNodeId = mindMapStore.rootNodeId;
                if (rootNodeId) {
                  console.log('[OptimizedMindMap] Selecting root node:', rootNodeId);
                  mindMapStore.selectNode(rootNodeId);
                }
              }, 500);
            } else {
              console.error('[OptimizedMindMap] Could not create mindmap with the provided data');
            }
          } catch (error) {
            console.error('[OptimizedMindMap] Error creating mindmap:', error);
          }
        }, 0); // Defer to next tick
      } else {
        // Handle as a normal action
        handleChatForkAction(initialAction);
      }
    } else {
      console.log('[OptimizedMindMap] No initialAction provided');
    }
  }, [initialAction, handleChatForkAction]);

  // Removed governance chat event listeners as they're handled by InitialView

  // Get window dimensions for the canvas
  const [windowDimensions, setWindowDimensions] = useState({
    width: window.innerWidth,
    height: window.innerHeight - 40 // Subtract header height
  });

  // Update dimensions on window resize
  useEffect(() => {
    const handleResize = () => {
      setWindowDimensions({
        width: window.innerWidth,
        height: window.innerHeight - 40
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Load the last active project on initialization
  useEffect(() => {
    const { loadLastActiveProject, nodes } = useMindMapStore.getState();

    // Only load if there are no nodes already loaded
    if (!nodes || Object.keys(nodes).length === 0) {
      console.log('[OptimizedMindMap] No nodes found, loading last active project');
      const success = loadLastActiveProject();
      console.log('[OptimizedMindMap] Loaded last active project:', success);

      // If no last active project, create a new default project
      if (!success) {
        console.log('[OptimizedMindMap] No last active project found, creating default project');
        const { createNewProject } = useMindMapStore.getState();
        createNewProject('Untitled Mindmap');
      }
    } else {
      console.log('[OptimizedMindMap] Nodes already loaded, skipping load of last active project');
    }
  }, []);

  // Find the root node to get its text for the sheet tab
  const getRootNodeText = () => {
    try {
      // Check if nodes and edges are valid objects before using Object.values
      if (!nodes || typeof nodes !== 'object') {
        console.warn('Nodes is not a valid object:', nodes);
        return 'Untitled Mindmap';
      }

      if (!connections || typeof connections !== 'object') {
        console.warn('Connections is not a valid object:', connections);
        // Still try to get a root node without checking connections
        const rootNode = Object.values(nodes).find(node => node.id === 'root');
        return rootNode?.text || 'Untitled Mindmap';
      }

      // Find a node without incoming connections or with id='root'
      const rootNode = Object.values(nodes).find(node =>
        node.id === 'root' || !Object.values(connections).some(conn => conn.to === node.id)
      );

      return rootNode?.text || 'Untitled Mindmap';
    } catch (error) {
      console.error('Error in getRootNodeText:', error);
      return 'Untitled Mindmap';
    }
  };

  // Debug output
  console.log('OptimizedMindMap_Modular rendering with nodes:', nodes && typeof nodes === 'object' ? Object.keys(nodes).length : 0);

  // Remove the forceRender state as it's causing the jumping issue
  // We don't need to force re-renders as React will handle this automatically

  // Log when showMindMapManager changes
  useEffect(() => {
    console.log('showMindMapManager changed:', showMindMapManager);
  }, [showMindMapManager]);

  // Control visibility of the governance chat
  const [showGovernanceChat, setShowGovernanceChat] = useState(false);
  const [isGovernanceChatCollapsed, setIsGovernanceChatCollapsed] = useState(false);

  // Handler for governance chat actions
  const handleGovernanceAction = useCallback((action: any) => {
    console.log('[OptimizedMindMap] Received governance action:', action);

    // Pass action to the ChatFork handler if it's a create_mindmap action
    if (action.type === 'create_mindmap' && handleChatForkAction) {
      handleChatForkAction(action);
    }
  }, [handleChatForkAction]);

  // Auto-layout optimization - triggered when nodes change
  useEffect(() => {
    // REMOVED: Automatic layout update that bypassed governance
    // The previous code had:
    // mindMapStore.updateLayout('tree');
    // This violated governance rules and caused layout jumping on every node change
    
    // Layout should only be updated when explicitly requested by the user
    // through the MindMapManager or other user interactions
    console.log('[OptimizedMindMap] Nodes changed, but not triggering automatic layout (governance compliance)');
  }, [nodes]);

  return (
    <div className="optimized-mind-map">
      {/* Project Management Dialog */}
      {showProjectDialog && (
        <ProjectDialog
          isOpen={showProjectDialog}
          onClose={() => setShowProjectDialog(false)}
        />
      )}

      {/* MindMap Canvas - This is the main component that renders the mindmap */}
      <div
        className="mind-map-container"
        style={{
          display: (nodes && typeof nodes === 'object' && Object.keys(nodes).length > 0) ? 'block' : 'none',
          position: 'absolute',
          top: '40px',
          left: 0,
          right: 0,
          bottom: '70px', // Adjusted to account for the mindsheet tabs (30px) and footer (40px)
          zIndex: 10,
          backgroundColor: '#f8fafc'
        }}
        key="mindmap-container"
      >
        {nodes && typeof nodes === 'object' && Object.keys(nodes).length > 0 && (
          <MindMapCanvasWrapper
            width={windowDimensions.width}
            height={windowDimensions.height - 110} // Adjusted for header (40px), mindsheet tabs (30px), and footer (40px)
            sheetId={activeSheetId || 'default-sheet'}
            key="mindmap-canvas"
          />
        )}
      </div>

      {/* Controls moved to MindSheetTabs component */}

      {/* MindMap Manager Dialog */}
      <EnhancedMindMapManager
        open={showMindMapManager}
        onClose={() => setShowMindMapManager(false)}
      />

      {/* Governance Agent */}
      {showGovernanceChat && (
        <GovernanceChatDialog
          isOpen={showGovernanceChat}
          isCollapsed={isGovernanceChatCollapsed}
          onClose={() => setShowGovernanceChat(false)}
          onCollapse={() => setIsGovernanceChatCollapsed(!isGovernanceChatCollapsed)}
          onAction={handleGovernanceAction}
          mindMapState={{}}
        />
      )}

      {/* NodeBox Component - Removed duplicate NodeBox */}
    </div>
  );
};

export default OptimizedMindMap_Modular;