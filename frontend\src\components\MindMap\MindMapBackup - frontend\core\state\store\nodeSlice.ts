import { StateCreator } from 'zustand';
import { MindMapStore, Node, Position } from './types';

export interface NodeSlice {
  nodes: Record<string, Node>;
  selectedNodeId: string | null;
  addNode: (node: Omit<Node, 'id'>) => string;
  updateNode: (id: string, updates: Partial<Node>) => void;
  deleteNode: (id: string) => void;
  selectNode: (id: string | null) => void;
  updateNodePosition: (id: string, position: Position) => void;
}

export const createNodeSlice: StateCreator<MindMapStore, [], [], NodeSlice> = (set, get) => ({
  nodes: {},
  selectedNodeId: null,

  addNode: (node) => {
    const id = crypto.randomUUID();
    set((state) => ({
      nodes: {
        ...state.nodes,
        [id]: { ...node, id }
      }
    }));
    return id;
  },

  updateNode: (id, updates) => {
    set((state) => ({
      nodes: {
        ...state.nodes,
        [id]: { ...state.nodes[id], ...updates }
      }
    }));
  },

  deleteNode: (id) => {
    set((state) => {
      const { [id]: deletedNode, ...remainingNodes } = state.nodes;
      return { nodes: remainingNodes };
    });
  },

  selectNode: (id) => {
    set({ selectedNodeId: id });
  },

  updateNodePosition: (id, position) => {
    set((state) => ({
      nodes: {
        ...state.nodes,
        [id]: { ...state.nodes[id], position }
      }
    }));
  }
}); 