# Prompt: Detect and Recommend Codebase Improvements for Vibe Architect Using mvcd.yaml

You are acting as a system improvement agent for the Vibe Architect project.

DISREGARD ALL OTHER CONTENT AND FOCUS ON THIS TASK

---

## Objective

Your task is to analyze the current state of the Vibe Architect project and identify meaningful improvement opportunities in terms of:
1. Refactoring or removing duplicate code: Identifying and consolidating identical or very similar code snippets.
2. Modularization: Breaking down large or complex functions/components into smaller, more manageable pieces, thereby concidering the trade-off between complexity and readability.
3. Improving clarity and readability: Making code easier to understand (e.g., better variable names, comments, simpler logic).
4. Removing unused code: Deleting functions, variables, or files that are no longer needed.
5. Improving performance or efficiency: Optimizing code to run faster or use fewer resources.

---

## Input

1. Use the following file to obtain an overview of the codebase structure:

   `.VibeArch/Directory/mvcd.yaml`

   Each entry includes:
   - file: Relative path to the file
   - element: Name of the function, class, or component
   - type: One of [Component, Hook, Utility, Store, Context, Type, Other]
   - description: Description (may be missing or vague)
   - dependencies, loc, last_modified

2. Based on this metadata and your own analysis of the codebase, inspect:
   - Files with unclear or missing descriptions
   - Files with similar or duplicated functionality
   - Files with no inbound references (i.e., potentially unused code)

---

## Goal

For each problem found, return a structured improvement recommendation.

You must:
1. Analyze the actual code behind each mvcd entry
2. Identify issues in:
   - Code clarity
   - Architectural structure or component boundaries
   - Performance inefficiencies
   - Security risks
   - Redundant or duplicate functionality
   - Unused or orphaned components

3. For each issue:
   - Create a unique `issue_id` (e.g. IMP001, IMP002, etc.)
   - Specify the exact `file` path relative to project root
   - Specify the `element` name (function, class, component, variable, etc.)
   - Include the `line` number where the issue occurs (if applicable)
   - Tag it with a `category` (choose one):
     - clarity: Code readability, naming, comments
     - structure: Architecture, component boundaries, organization
     - performance: Speed, efficiency, resource usage
     - security: Security vulnerabilities or risks
     - redundancy: Duplicate or similar code
     - semantics: Logic, correctness, behavior
     - unused: Dead code, unused variables/functions
   - Write a short description of the `issue` (what's wrong)
   - Provide a precise improvement `suggestion` (how to fix it)
   - Assign a `confidence` score (0–100%)
   - Set an `impact_level`: low | medium | high

---

## Output

📍 Save your output as a valid YAML list in the following file:

Name: [DATE]_improvement_suggestion.yaml
in path: `.VibeArch/Improvement/`

Format:
```yaml
- issue_id: IMP001
  file: backend/app/api/endpoints/mvcd.py
  element: get_mvcd_status
  line: 264
  category: clarity
  issue: "Function name 'get_mvcd_status' could be more descriptive about what status information it returns."
  suggestion: "Consider renaming to 'get_mvcd_metrics_and_status' or add detailed docstring explaining the returned status fields."
  confidence: 75
  impact_level: low

- issue_id: IMP002
  file: frontend-vite/src/components/MVCD.jsx
  element: fetchMvcdData
  line: 319
  category: structure
  issue: "Function is too long (35+ lines) and handles multiple responsibilities including error handling, data parsing, and state management."
  suggestion: "Break down into smaller functions: parseYamlData(), buildDirectoryStructure(), and handleMvcdError()."
  confidence: 85
  impact_level: medium
```

## Constraints

- Do not fabricate purpose or hallucinate functionality
- Do not reformat or reorder any part of the file
-

## Final Instruction

Return only [DATE]_improvement_suggestion.yaml
