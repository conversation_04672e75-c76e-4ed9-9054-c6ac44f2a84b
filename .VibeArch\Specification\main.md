# MindBack_V1 Specification

## Overview

Teleological Mindmap Workflow
- there are two workflows: an automatic detection of the teleological intent and generation of a mindmap structure and a manual creation of a main node by selecting teleologcal in the intention selectionbox
- a unique mindsheet is created on which the mindmap is created
- both workflows uses the same mechanisms and same codebase for creating the mindmap
- there is a data set describing each mindmap on each unique mindsheet
- there is a mindmap manager loading the mindmap data from the active mindsheet giving the user the posibility to manage the desigen of the mindmap. 
- when switching to another mindsheet with another mindmap the data of thei mindmap is loaded
- 1. we have a data set for each mindmap, 2. we have a mindmap manager with which the user can alter the data set in terms of restoring, scaling and centering, beside the posibility for the user to mode the mindmap and each node individually
