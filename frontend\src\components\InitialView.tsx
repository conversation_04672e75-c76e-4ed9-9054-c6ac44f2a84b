import React, { useState, useEffect, lazy, Suspense } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMindBookStore } from '../core/state/MindBookStore';

// Lazy load the OptimizedMindMap_Modular component to improve initial load time
const OptimizedMindMap_Modular = lazy(() => import('./OptimizedMindMap_Modular'));

/**
 * InitialView Component
 *
 * This is the initial view of the application, showing only the governance box.
 * MindMap and ChatFork components are only loaded when needed based on user intent.
 * 
 * NOTE: GovernanceBox is now rendered via AppRefactored -> MindBook, not here.
 * This component now only handles legacy mindmap operations.
 */
interface InitialViewProps {
  isContextPanelOpen?: boolean;
  showGovernanceChat?: boolean;
  isGovernanceChatMinimized?: boolean;
  isGovernanceChatFullyCollapsed?: boolean;
  setShowGovernanceChat?: (show: boolean) => void;
  setIsGovernanceChatMinimized?: (minimized: boolean) => void;
  setIsGovernanceChatFullyCollapsed?: (collapsed: boolean) => void;
}

const InitialView: React.FC<InitialViewProps> = ({
  isContextPanelOpen = false,
  // These props are now ignored since governance is handled by AppRefactored
  showGovernanceChat: propShowGovernanceChat,
  isGovernanceChatMinimized: propIsGovernanceChatMinimized,
  isGovernanceChatFullyCollapsed: propIsGovernanceChatFullyCollapsed,
  setShowGovernanceChat: propSetShowGovernanceChat,
  setIsGovernanceChatMinimized: propSetIsGovernanceChatMinimized,
  setIsGovernanceChatFullyCollapsed: propSetIsGovernanceChatFullyCollapsed
}) => {
  // State for mindmap (legacy support)
  const [showMindMap, setShowMindMap] = useState(false);
  const [mindMapAction, setMindMapAction] = useState<any>(null);

  // Listen for custom mindmap creation events
  useEffect(() => {
    const handleCreateMindmapEvent = (event: any) => {
      console.log('InitialView: Received mindback:create_mindmap event', event.detail);
      handleChatAction(event.detail);
    };

    const handleShowMindmapEvent = (event: any) => {
      console.log('InitialView: Received mindback:show_mindmap event', event.detail);

      // Check if this is a duplicate event
      if (mindMapAction &&
          mindMapAction.sheetId === event.detail.sheetId) {
        console.log('InitialView: Duplicate mindmap event detected, ignoring');
        return;
      }

      // Force a complete reset of the mindmap component
      setShowMindMap(false);

      // Clear any existing action
      setMindMapAction(null);

      // Wait a bit to ensure the component is fully unmounted
      setTimeout(() => {
        // Set the new action
        setMindMapAction(event.detail);
        console.log('InitialView: Set mindMapAction state to:', JSON.stringify(event.detail, null, 2));

        // Show the mindmap component
        setShowMindMap(true);
        console.log('InitialView: Set showMindMap state to true');
      }, 100);
    };

    // Add event listeners
    document.addEventListener('mindback:create_mindmap', handleCreateMindmapEvent);
    document.addEventListener('mindback:show_mindmap', handleShowMindmapEvent);

    // Clean up event listeners
    return () => {
      document.removeEventListener('mindback:create_mindmap', handleCreateMindmapEvent);
      document.removeEventListener('mindback:show_mindmap', handleShowMindmapEvent);
    };
  }, [mindMapAction]);

  // Handle chat actions (for legacy support)
  const handleChatAction = (action: any) => {
    console.log('InitialView: Received action from chat:', action);

    if (action.type === 'create_mindmap' || action.type === 'SHOW_MINDMAP') {
      console.log('Showing mindmap with data:', action.data || action.payload);

      // Clear any existing mindmap first
      setShowMindMap(false);
      setMindMapAction(null);

      // Wait a bit to ensure cleanup, then show new mindmap
      setTimeout(() => {
        setMindMapAction(action);
        setShowMindMap(true);
      }, 100);

    } else if (action.type === 'show_chatfork' || action.type === 'SHOW_CHATFORK') {
      console.log('Showing chatfork with data:', action.data || action.payload);

      // Import the ChatFork store and show the ChatFork view
      import('../components/ChatFork/ChatForkStore').then(module => {
        const { useChatForkStore } = module;
        console.log('InitialView: Showing ChatFork with data:', action.data || action.payload);

        // Show the ChatFork view with the data
        const chatForkStore = useChatForkStore.getState();
        chatForkStore.showChatFork(action.data || action.payload);

        // Force a re-render to ensure the ChatFork is displayed
        setTimeout(() => {
          console.log('InitialView: Forcing re-check of ChatFork visibility');
          // Re-show the ChatFork to ensure it's visible
          chatForkStore.showChatFork(action.data || action.payload);
        }, 100);
      });
    } else {
      console.log('Unknown action type:', action.type);
    }
  };

  return (
    <div className="initial-view">
      {/* Governance box is now rendered by AppRefactored -> MindBook */}
      {/* No longer rendering GovernanceBoxPositioned here to avoid conflicts */}

      {/* MindMap Component - DEPRECATED: All mindmap operations now use MindSheet framework */}
      {showMindMap && (() => {
        // CRITICAL: All mindmap operations should now go through MindSheet framework
        // OptimizedMindMap_Modular is deprecated and causes positioning conflicts
        console.log('InitialView: Mindmap request detected, but all mindmap operations should use MindSheet framework now');
        console.log('InitialView: Hiding legacy OptimizedMindMap_Modular to prevent conflicts');
        
        // Reset the showMindMap state to prevent future renders
        setTimeout(() => {
          setShowMindMap(false);
          setMindMapAction(null);
        }, 100);
        
        return (
          <div style={{
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: '#fff3cd',
            border: '1px solid #ffeaa7',
            borderRadius: '8px',
            padding: '12px',
            zIndex: 1000,
            maxWidth: '300px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
          }}>
            <strong>Note:</strong> All mindmap operations now use the MindSheet framework. 
            The legacy OptimizedMindMap_Modular has been disabled to prevent rendering conflicts.
          </div>
        );
      })()}

      {/* Controls Bar - Empty since governance is handled elsewhere */}
      <div className="control-buttons-bar governance-controls">
        {/* The governance button that was here has been removed */}
      </div>
    </div>
  );
};

export default InitialView;
