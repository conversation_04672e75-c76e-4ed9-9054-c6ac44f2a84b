# Frontend Dependency Notes

## Node.js Version Requirements

- **Node.js: 20.x LTS (recommended)**
  - Provides best compatibility with our dependencies
  - Avoid Node.js 22+ which may cause unexpected issues
  - Node.js 18.x LTS is also supported

- **npm: 10.x+**
  - Comes with Node.js 20 LTS
  - Required for proper dependency resolution

## React Version Requirements

- **react: ^18.2.0**
  - Required for react-dom/client API used in main.tsx
  - Previous version (17.0.2) doesn't support the createRoot API

- **react-dom: ^18.2.0**
  - Required for client API (createRoot method)
  - Enables React 18 features including concurrent rendering

- **@types/react: ^18.2.0**
  - TypeScript definitions must match React version

- **@types/react-dom: ^18.2.0**
  - TypeScript definitions must match React DOM version

- **react-router-dom: ^6.20.0**
  - Compatible version with React 18

## Other Critical Dependencies

- **react-konva: 17.0.2-6**
  - This version has a dependency on react-reconciler@0.26.2 which requires React 17
  - Currently using React 18 with resolutions field to override this dependency
  - Consider upgrading to a newer version of react-konva that supports React 18
  - Warning messages about this conflict appear during npm install but application works

- **openai: 1.6.1**
  - This specific version required by langchain-openai>=0.0.2.post1
  - Changing this version may cause dependency conflicts

## Resolutions

The `resolutions` field in package.json enforces React 18 for all sub-dependencies, ensuring consistent React version usage throughout the dependency tree.

## Troubleshooting

If you encounter the error "Failed to resolve import 'react-dom/client'", ensure:
1. You're using React 18 or higher
2. The node_modules directory is properly installed
3. TypeScript types are correctly updated to match React version