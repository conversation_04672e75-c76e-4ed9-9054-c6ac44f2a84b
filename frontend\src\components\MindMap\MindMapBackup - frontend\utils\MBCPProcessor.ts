/**
 * MBCPProcessor.ts
 *
 * This file contains utilities for processing MBCP (MindBack Communication Protocol) data
 * from LLM responses and creating mindmap nodes, properly preserving all metadata.
 *
 * This is the recommended approach for mindmap generation, replacing the deprecated
 * semi-automatic workflow in ManualJsonProcessor.ts.
 */

import { useMindMapStore } from '../core/state/MindMapStore';
import { createNode, Node, Connection } from '../core/models/Node';

interface MBCPNode {
  text: string;
  description?: string;
  children?: MBCPNode[];
  metadata?: {
    intent?: string;
    tags?: string[];
    nodePath?: string;
    action?: any;
    agent?: any;
    [key: string]: any;
  };
}

/**
 * Create a mindmap from MBCP data using the fully automatic workflow
 *
 * @param mbcpData The MBCP data from the LLM response
 * @returns The ID of the root node or null if creation failed
 */
export const createMindmapFromMBCP = (mbcpData: any): string | null => {
  console.log('Processing MBCP data with automatic workflow:', mbcpData);

  try {
    // Get the MindMapStore
    const store = useMindMapStore.getState();

    // Extract mindmap-specific data if available
    const mindmapData = mbcpData.mindmap || mbcpData;

    // Create a new project with a descriptive name
    const projectName = mbcpData.title || mbcpData.text || mindmapData.root?.text || 'New Mindmap';
    console.log('Creating new project with name:', projectName);

    try {
      // Create new project and get the store again to ensure we have the updated state
      // First clear the existing state
      store.clearAll();

      // Set the project name
      store.setProjectName(projectName);

      // Create a root node manually instead of using createNewProject
      const rootNode = createNode(projectName, 0, 0, {
        width: 200,
        height: 80,
        color: '#ffffff',
        borderColor: '#3b82f6',
        shape: 'rectangle',
        description: mbcpData.description || ''
      });

      // Add the root node to the store
      const rootNodeId = store.addNode(0, 0, {
        isRoot: true,
        text: projectName
      });

      // Set the root node ID explicitly
      store.setRootNode(rootNodeId);
    } catch (error) {
      console.error('Error creating new project:', error);
      throw error;
    }

    // Get the updated store state after project creation
    const updatedStore = useMindMapStore.getState();
    console.log('Store state after project creation:', {
      rootNodeId: updatedStore.rootNodeId,
      nodeCount: Object.keys(updatedStore.nodes).length
    });

    // Look for a root node ID - first try the store property
    let rootNodeId = updatedStore.rootNodeId;
    console.log('Root node ID after project creation:', rootNodeId);

    // If rootNodeId is null, but we have nodes, use the first one
    if (!rootNodeId && Object.keys(updatedStore.nodes).length > 0) {
      rootNodeId = Object.keys(updatedStore.nodes)[0];
      console.log('Found node in store, using as root:', rootNodeId);

      // Update rootNodeId in the store
      updatedStore.setRootNode(rootNodeId);
      console.log('Updated root node ID in store:', rootNodeId);
    }

    // If we still don't have a root node, create one without requiring a parent
    if (!rootNodeId) {
      console.log('No root node found, creating one directly');

      // Create a root node at position 0,0
      const newNode = {
        id: `root_${Date.now()}`,
        text: projectName,
        description: mbcpData.description || '',
        x: 0,
        y: 0,
        width: 200,
        height: 100,
        color: '#ffffff',
        borderColor: '#2c3e50',
        shape: 'rectangle',
        metadata: {
          intent: mbcpData.intent || 'teleological',
          nodePath: '1.0'
        },
        hatContributions: {
          blue: false,
          white: false,
          red: false,
          black: false,
          yellow: false,
          green: false
        }
      };

      // Add the node directly to the store
      updatedStore.setNodes({ ...updatedStore.nodes, [newNode.id]: newNode });
      rootNodeId = newNode.id;
      updatedStore.setRootNode(rootNodeId);
      console.log('Created and set new root node:', rootNodeId);
    }

    // Final check to ensure we have a root node ID
    if (!rootNodeId) {
      console.error('Failed to get or create root node ID - this should never happen');
      console.log('Current store state:', updatedStore);

      // Force a refresh of the store state
      useMindMapStore.setState({});
      return null;
    }

    console.log('Using root node ID for MBCP data:', rootNodeId);

    // If we have the complete MBCP data with mindmap structure
    // Check for both mindmapData.root and mindmapData.structure formats
    if (mindmapData && (mindmapData.root || mindmapData.structure || mindmapData.mindmap_structure)) {
      console.log('Processing complete MBCP data with mindmap structure');

      // Update root node with data from MBCP - using correct property names
      try {
        // Get the fresh state again to ensure we have the latest
        const freshStore = useMindMapStore.getState();

        // First verify the node exists
        if (freshStore.nodes && freshStore.nodes[rootNodeId]) {
          console.log('Found root node in store, updating with MBCP data');

          // Determine the root node data from various possible structures
          const rootData = mindmapData.root ||
                          mindmapData.structure?.root ||
                          mindmapData.mindmap_structure?.root ||
                          { text: projectName, description: mindmapData.description || '' };

          console.log('Root data extracted:', rootData);

          // Update with both text and title properties to ensure compatibility
          freshStore.updateNode(rootNodeId, {
            text: rootData.text,
            title: rootData.text, // Add title property too
            description: rootData.description || '',
            metadata: rootData.metadata || { intent: 'teleological' }
          });
          console.log('Updated root node via updateNode method');

          // Debug the node after update
          console.log('Root node after update:', freshStore.nodes[rootNodeId]);
        } else {
          console.warn('Root node not found in fresh store state, using direct assignment');

          // Force direct property assignment
          const nodes = { ...freshStore.nodes };
          nodes[rootNodeId] = {
            ...nodes[rootNodeId] || {},
            id: rootNodeId,
            text: mindmapData.root.text,
            title: mindmapData.root.text,
            description: mindmapData.root.description || '',
            metadata: mindmapData.root.metadata || { intent: 'teleological' }
          };
          freshStore.setNodes(nodes);
          console.log('Updated node directly via setNodes');
        }
      } catch (updateError) {
        console.error('Error updating root node:', updateError);
      }

      // Process children if they exist
      // Determine the children from various possible structures
      const rootData = mindmapData.root ||
                      mindmapData.structure?.root ||
                      mindmapData.mindmap_structure?.root ||
                      {};

      // Look for children in various possible locations
      const children = rootData.children ||
                      mindmapData.children ||
                      mindmapData.structure?.children ||
                      mindmapData.mindmap_structure?.children ||
                      [];

      if (Array.isArray(children) && children.length > 0) {
        console.log(`Processing ${children.length} children`);

        // Get fresh store again
        const freshStore = useMindMapStore.getState();

        children.forEach((child: any, index: number) => {
          // Create child node with current rootNodeId as parent
          try {
            // Ensure rootNodeId exists before attempting to create a child
            if (!rootNodeId) {
              console.error('Cannot create child node: rootNodeId is undefined');
              return;
            }

            // Make sure the child object has all needed properties to prevent undefined access
            if (!child || typeof child !== 'object') {
              console.error(`Child node ${index} is not a valid object:`, child);
              child = { text: `Child ${index + 1}` }; // Create a default object to prevent further errors
            }

            // Ensure child.text is a string (this is what's likely causing the toString error)
            const childText = child.text || `Child ${index + 1}`;

            // Create child node with consistent parameter types
            // Important: Use a string for direction and ensure all parameters are valid
            console.log(`Creating child node ${index} with parent ${rootNodeId}`, child);

            // Create the position variables for the node
            const posX = 200 + (index * 20); // Add some offset for each node
            const posY = (index * 100) - ((mindmapData.root.children.length - 1) * 50); // Distribute vertically

            console.log(`Positioning child at: x=${posX}, y=${posY}`);

            // Use the x, y parameters as the first ones since that's the expected signature
            const childId = freshStore.addNode(posX, posY, {
              parentId: rootNodeId,
              direction: 'right', // Always use string to avoid toString issues
              text: childText, // Always provide text to avoid undefined.toString()
              metadata: {
                ...(child.metadata || {}),
                nodePath: `1.${index + 1}`,
                isManuallyAdded: false,
                creationSource: 'auto'
              }
            });

            console.log(`Created child node ${index} with ID:`, childId);

            if (childId) {
              // Ensure there's a connection created between parent and child
              // This is critical since some implementations expect connections to exist
              try {
                // Check if connections is an array (original format) or record (new format)
                // and add connection accordingly
                console.log('Creating connection between parent and child');

                // Create connection directly if the implementation requires it
                if (Array.isArray(freshStore.connections)) {
                  // Old format
                  console.log('Using array-based connection format');

                  // Create a unique connection ID
                  const connectionId = `conn_${rootNodeId}_${childId}_${Date.now()}`;

                  // Add connection directly to state
                  const newConnection = {
                    id: connectionId,
                    from: rootNodeId,
                    to: childId,
                    type: 'solid',
                    color: '#9ca3af', // Standard gray color for auto-created connections
                    thickness: 2,
                    showArrow: false,
                    lineStyle: 'angled',
                    path: [],
                    createdAt: Date.now()
                  };

                  // Add to connections array
                  freshStore.connections.push(newConnection);

                  console.log(`Added connection to array: ${rootNodeId} -> ${childId} (${connectionId})`);
                } else {
                  // Create connection via method if possible
                  console.log('Using method-based connection creation');
                  if (typeof freshStore.addConnection === 'function') {
                    try {
                      // Create full connection object to avoid issues with partial parameters
                      const connectionParams = {
                        from: rootNodeId,
                        to: childId,
                        type: 'solid',
                        color: '#9ca3af',
                        thickness: 2,
                        showArrow: false,
                        lineStyle: 'angled',
                        path: []
                      };

                      freshStore.addConnection(connectionParams);
                      console.log(`Added connection via method: ${rootNodeId} -> ${childId}`);
                    } catch (connMethodError) {
                      console.error('Error using addConnection method:', connMethodError);

                      // Direct state update as fallback
                      useMindMapStore.setState(state => ({
                        ...state,
                        connections: [...state.connections, {
                          id: `fallback_conn_${Date.now()}`,
                          from: rootNodeId,
                          to: childId,
                          type: 'solid',
                          color: '#9ca3af',
                          thickness: 2,
                          showArrow: false,
                          lineStyle: 'angled',
                          path: []
                        }]
                      }));
                    }
                  } else {
                    console.warn('No addConnection method found, using direct state update');
                    // Force direct state update
                    useMindMapStore.setState(state => ({
                      ...state,
                      connections: [...state.connections, {
                        id: `direct_conn_${Date.now()}`,
                        from: rootNodeId,
                        to: childId,
                        type: 'solid',
                        color: '#9ca3af',
                        thickness: 2,
                        showArrow: false,
                        lineStyle: 'angled',
                        path: []
                      }]
                    }));
                  }
                }

                // Verify that the connection was added
                setTimeout(() => {
                  const currentState = useMindMapStore.getState();
                  console.log('Connections after adding:', currentState.connections);
                  const connectionExists = Array.isArray(currentState.connections) &&
                    currentState.connections.some(c => c.from === rootNodeId && c.to === childId);

                  if (!connectionExists) {
                    console.warn('Connection may not have been created properly, attempting fallback method');
                    // Force a connection update through the store
                    useMindMapStore.setState(state => ({
                      ...state,
                      connections: [...state.connections, {
                        id: `fallback_conn_${Date.now()}`,
                        from: rootNodeId,
                        to: childId,
                        type: 'solid',
                        color: '#9ca3af',
                        thickness: 2,
                        showArrow: false,
                        lineStyle: 'angled',
                        path: []
                      }]
                    }));
                  }
                }, 50);

                console.log(`Connection created between ${rootNodeId} and ${childId}`);
              } catch (connectionError) {
                console.warn('Error creating connection but continuing with node update:', connectionError);
              }

              // Update with child data - use both text and title
              freshStore.updateNode(childId, {
                text: childText,
                title: childText, // Add title property too
                description: child.description || '',
                metadata: {
                  ...(child.metadata || {}),
                  nodePath: `1.${index + 1}`,
                  isManuallyAdded: false,
                  creationSource: 'auto'
                }
              });

              // Debug the child node update
              console.log(`Updated child node ${index}:`, freshStore.nodes[childId]);

              // Process grandchildren if they exist (recursive)
              if (child.children && Array.isArray(child.children) && child.children.length > 0) {
                processChildNodes(child.children, childId, freshStore, `1.${index + 1}`);
              }
            } else {
              console.error(`Failed to create child node ${index} - addNode returned empty ID`);
            }
          } catch (error) {
            console.error(`Error creating child node ${index}:`, error);
          }
        });
      }
    } else {
      // Create a basic mindmap structure
      console.log('No proper MBCP structure found, creating basic mindmap from available data');
      console.log('MBCP data received:', mbcpData);

      // Get fresh store
      const freshStore = useMindMapStore.getState();

      // Update root node with basic data
      try {
        freshStore.updateNode(rootNodeId, {
          text: projectName,
          title: projectName, // Add title property too
          description: mbcpData.description || mbcpData.content || '',
          metadata: { intent: mbcpData.intent || 'teleological' }
        });
        console.log('Updated root node with basic data');
      } catch (updateError) {
        console.error('Error updating root node with basic data:', updateError);
      }

      // Try to extract children from various properties
      const possibleChildren = extractPossibleChildren(mbcpData);
      console.log('Extracted possible children:', possibleChildren);

      if (possibleChildren.length > 0) {
        console.log(`Processing ${possibleChildren.length} extracted children`);

        possibleChildren.forEach((child: any, index: number) => {
          // Create child node with proper error handling
          try {
            // Ensure rootNodeId exists before attempting to create a child
            if (!rootNodeId) {
              console.error('Cannot create child node: rootNodeId is undefined');
              return;
            }

            // Make sure the child object has all needed properties to prevent undefined access
            if (!child || typeof child !== 'object') {
              console.error(`Extracted child node ${index} is not a valid object:`, child);
              child = { title: `Point ${index + 1}` }; // Create a default object
            }

            // Ensure we have valid string values for title and text
            const childTitle = child.title || child.text || `Point ${index + 1}`;
            const childText = child.text || child.title || `Point ${index + 1}`;

            const childId = freshStore.addNode(0, 0, {
              parentId: rootNodeId,
              direction: 'right', // Use string direction name instead of angle for clarity
              text: childText,
              metadata: {
                nodePath: `1.${index + 1}`
              }
            });

            if (childId) {
              // Update with child data
              freshStore.updateNode(childId, {
                title: childTitle,
                text: childText,
                description: child.content || child.description || '',
                metadata: {
                  nodePath: `1.${index + 1}`
                }
              });
            } else {
              console.error(`Failed to create child node for point ${index + 1} - addNode returned empty ID`);
            }
          } catch (error) {
            console.error(`Error creating child node for point ${index + 1}:`, error);
          }
        });
      }
    }

    // Apply auto layout to position the nodes
    setTimeout(() => {
      try {
        // Get fresh store for layout
        const freshStore = useMindMapStore.getState();
        console.log('Running auto-layout with node count:', Object.keys(freshStore.nodes).length);

        // Get the current node positions for debugging
        const nodePositions = Object.entries(freshStore.nodes).map(([id, node]) => ({
          id, text: node.text, x: node.x, y: node.y
        }));
        console.log('Node positions before layout:', nodePositions);

        // Apply auto-layout
        if (typeof freshStore.updateLayout === 'function') {
          freshStore.updateLayout('tree');
          console.log('Auto layout applied to mindmap');
        } else {
          console.warn('updateLayout function not available in store');
        }

        // Verify node positions after layout
        const afterLayout = useMindMapStore.getState();
        const newNodePositions = Object.entries(afterLayout.nodes).map(([id, node]) => ({
          id, text: node.text, x: node.x, y: node.y
        }));
        console.log('Node positions after layout:', newNodePositions);

        // Force a store update to ensure UI reflects changes
        useMindMapStore.setState({});
      } catch (layoutError) {
        console.error('Error applying auto layout:', layoutError);
      }
    }, 300); // Increase timeout to ensure all nodes are created first

    return rootNodeId;
  } catch (error) {
    console.error('Error creating mindmap from MBCP data:', error);
    return null;
  }
};

/**
 * Process child nodes recursively
 *
 * @param children The child nodes to process
 * @param parentId The ID of the parent node
 * @param store The MindMapStore
 * @param parentPath The path of the parent node
 */
const processChildNodes = (children: any[], parentId: string, store: any, parentPath: string) => {
  children.forEach((child: any, index: number) => {
    try {
      // Validate inputs
      if (!parentId) {
        console.error('Cannot create child node: parentId is undefined');
        return;
      }

      // Make sure the child object has all needed properties to prevent undefined access
      if (!child || typeof child !== 'object') {
        console.error(`Child node at ${parentPath}.${index + 1} is not a valid object:`, child);
        child = { text: `Node ${parentPath}.${index + 1}` }; // Create a default object
      }

      // Ensure child.text is a string (to prevent toString errors)
      const childText = child.text || `Node ${parentPath}.${index + 1}`;

      // Get parent node to calculate position
      const parentNode = store.nodes[parentId];
      if (!parentNode) {
        console.error(`Parent node ${parentId} not found in store`);
        return;
      }

      // Calculate position based on parent
      const posX = parentNode.x + 200 + (index * 20); // Offset from parent with additional spacing
      const posY = parentNode.y + (index * 80) - ((children.length - 1) * 40); // Distribute vertically

      console.log(`Positioning nested child at: x=${posX}, y=${posY}, parentPos: x=${parentNode.x}, y=${parentNode.y}`);

      // Create child node with consistent parameter types
      // Important: Pass calculated positions as first parameters to match the signature
      console.log(`Creating nested child node at path ${parentPath}.${index + 1}`, child);
      const childId = store.addNode(posX, posY, {
        parentId: parentId,
        direction: 'right', // Always use string to avoid toString issues
        text: childText, // Use verified text value
        metadata: {
          ...(child.metadata || {}),
          nodePath: `${parentPath}.${index + 1}`,
          isManuallyAdded: false,
          creationSource: 'auto'
        }
      });

      if (childId) {
        // Ensure there's a connection created between parent and child
        try {
          console.log('Creating connection for nested node');

          // Support both array and record-based connection formats
          if (Array.isArray(store.connections)) {
            // Old format
            console.log('Using array-based connection format for nested node');

            // Create a unique connection ID
            const connectionId = `conn_${parentId}_${childId}_${Date.now()}`;

            // Add connection directly to state
            const newConnection = {
              id: connectionId,
              from: parentId,
              to: childId,
              type: 'solid',
              color: '#9ca3af', // Standard gray color for auto-created connections
              thickness: 2,
              showArrow: false,
              lineStyle: 'angled',
              path: [],
              createdAt: Date.now()
            };

            // Add to connections array
            store.connections.push(newConnection);

            console.log(`Added nested connection to array: ${parentId} -> ${childId} (${connectionId})`);
          } else {
            // Create connection via method if possible
            console.log('Using method-based connection creation for nested node');
            if (typeof store.addConnection === 'function') {
              try {
                // Create full connection object to avoid issues with partial parameters
                const connectionParams = {
                  from: parentId,
                  to: childId,
                  type: 'solid',
                  color: '#9ca3af',
                  thickness: 2,
                  showArrow: false,
                  lineStyle: 'angled',
                  path: []
                };

                store.addConnection(connectionParams);
                console.log(`Added nested connection via method: ${parentId} -> ${childId}`);
              } catch (connMethodError) {
                console.error('Error using addConnection method for nested node:', connMethodError);

                // Direct state update as fallback
                useMindMapStore.setState(state => ({
                  ...state,
                  connections: [...state.connections, {
                    id: `fallback_nested_conn_${Date.now()}`,
                    from: parentId,
                    to: childId,
                    type: 'solid',
                    color: '#9ca3af',
                    thickness: 2,
                    showArrow: false,
                    lineStyle: 'angled',
                    path: []
                  }]
                }));
              }
            } else {
              console.warn('No addConnection method found for nested node, using direct state update');
              // Force direct state update
              useMindMapStore.setState(state => ({
                ...state,
                connections: [...state.connections, {
                  id: `direct_nested_conn_${Date.now()}`,
                  from: parentId,
                  to: childId,
                  type: 'solid',
                  color: '#9ca3af',
                  thickness: 2,
                  showArrow: false,
                  lineStyle: 'angled',
                  path: []
                }]
              }));
            }
          }

          // Verify connection was added
          setTimeout(() => {
            const currentState = useMindMapStore.getState();
            const connectionExists = Array.isArray(currentState.connections) &&
              currentState.connections.some(c => c.from === parentId && c.to === childId);

            if (!connectionExists) {
              console.warn('Nested connection may not have been created properly, attempting fallback');
              useMindMapStore.setState(state => ({
                ...state,
                connections: [...state.connections, {
                  id: `fallback_nested_conn_${Date.now()}`,
                  from: parentId,
                  to: childId,
                  type: 'solid',
                  color: '#9ca3af',
                  thickness: 2,
                  showArrow: false,
                  lineStyle: 'angled',
                  path: []
                }]
              }));
            }
          }, 50);

          console.log(`Connection created between ${parentId} and ${childId}`);
        } catch (connectionError) {
          console.warn('Error creating connection for nested node but continuing:', connectionError);
        }

        // Current node path
        const nodePath = `${parentPath}.${index + 1}`;

        // Update with child data - use both text and title
        store.updateNode(childId, {
          text: childText,
          title: childText,
          description: child.description || '',
          metadata: {
            ...(child.metadata || {}),
            nodePath: nodePath,
            isManuallyAdded: false,
            creationSource: 'auto'
          }
        });

        // Log success
        console.log(`Successfully updated nested child node at path ${nodePath}`);

        // Process grandchildren if they exist (recursive)
        if (child.children && Array.isArray(child.children) && child.children.length > 0) {
          processChildNodes(child.children, childId, store, nodePath);
        }
      } else {
        console.error(`Failed to create child node for path ${parentPath}.${index + 1}`);
      }
    } catch (error) {
      console.error(`Error in processChildNodes for path ${parentPath}.${index + 1}:`, error);
    }
  });
};

/**
 * Extract possible children from various properties in the data
 *
 * @param data The data to extract children from
 * @returns An array of potential child nodes
 */
const extractPossibleChildren = (data: any): any[] => {
  // First check if we have a mindmap structure with children
  if (data.mindmap && data.mindmap.root && Array.isArray(data.mindmap.root.children)) {
    console.log('Found children in mindmap.root.children');
    return data.mindmap.root.children;
  }

  // Check for structure.root.children
  if (data.structure && data.structure.root && Array.isArray(data.structure.root.children)) {
    console.log('Found children in structure.root.children');
    return data.structure.root.children;
  }

  // Check for mindmap_structure.root.children
  if (data.mindmap_structure && data.mindmap_structure.root && Array.isArray(data.mindmap_structure.root.children)) {
    console.log('Found children in mindmap_structure.root.children');
    return data.mindmap_structure.root.children;
  }

  // Check for direct children array
  if (Array.isArray(data.children) && data.children.length > 0) {
    console.log('Found direct children array');
    return data.children;
  }

  // Check various properties that might contain children
  for (const prop of ['keyPoints', 'mainPoints', 'branches', 'topics', 'concepts', 'points', 'sections']) {
    if (data[prop] && Array.isArray(data[prop]) && data[prop].length > 0) {
      console.log(`Found children in ${prop} property`);
      return data[prop].map((item: any, index: number) => {
        if (typeof item === 'string') {
          return {
            title: item.split(':')[0] || item.substring(0, 30),
            content: item,
            text: item.split(':')[0] || item.substring(0, 30),
            description: item
          };
        } else if (typeof item === 'object') {
          return {
            title: item.title || item.text || `Point ${index + 1}`,
            content: item.content || item.description || JSON.stringify(item),
            text: item.title || item.text || `Point ${index + 1}`,
            description: item.content || item.description || JSON.stringify(item)
          };
        }
        return null;
      }).filter(Boolean);
    }
  }

  // If no direct children found, try to extract from structure
  if (data.structure && typeof data.structure === 'object') {
    return extractPossibleChildren(data.structure);
  }

  // Try to extract from content if it's an object
  if (data.content && typeof data.content === 'object') {
    return extractPossibleChildren(data.content);
  }

  // Try to extract from response if it's an object
  if (data.response && typeof data.response === 'object') {
    return extractPossibleChildren(data.response);
  }

  // Try to extract from full_text if it contains numbered points
  if (data.full_text && typeof data.full_text === 'string') {
    console.log('Attempting to extract children from full_text');

    // Look for numbered points in the full_text
    const lines = data.full_text.split('\n');
    const numberedPoints = [];
    let currentPoint = null;
    let currentDescription = '';

    for (const line of lines) {
      // Check for numbered points like "1. Point" or "1) Point"
      const numberedMatch = line.match(/^\s*(\d+)[.):] (.+)/);

      if (numberedMatch) {
        // If we already have a point, save it before starting a new one
        if (currentPoint) {
          numberedPoints.push({
            text: currentPoint,
            description: currentDescription.trim()
          });
        }

        // Start a new point
        currentPoint = numberedMatch[2].trim();
        currentDescription = '';
      } else if (currentPoint && line.trim()) {
        // Add to the description of the current point
        currentDescription += ' ' + line.trim();
      }
    }

    // Add the last point if there is one
    if (currentPoint) {
      numberedPoints.push({
        text: currentPoint,
        description: currentDescription.trim()
      });
    }

    if (numberedPoints.length > 0) {
      console.log(`Extracted ${numberedPoints.length} points from full_text`);
      return numberedPoints;
    }
  }

  return [];
};