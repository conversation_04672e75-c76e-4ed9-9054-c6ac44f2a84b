/**
 * MindMapToolbar.css
 * 
 * Styles for the MindMapToolbar component.
 */

.mindmap-toolbar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50px;
  background-color: #ffffff;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  z-index: 10;
}

.toolbar-group {
  display: flex;
  gap: 10px;
}

.toolbar-button {
  width: 36px;
  height: 36px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  background-color: #ffffff;
  color: #4a5568;
  font-size: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toolbar-button:hover {
  background-color: #f7fafc;
  border-color: #cbd5e0;
}

.toolbar-button:active {
  background-color: #edf2f7;
  transform: translateY(1px);
}
