
### Describing the workflow

as we will allow the user to ask simple questuins in the dialogue we need to have two prompts: 1. handling simple and sophisticated prompts, when sophisticated ask the user if we shall create a mindmap structure (this was all implemented but got wreckt) , if yes, then using the second prompt to build the mindmap, by a. deciding on an appropriate project naming to go into the title of the main node dialogbox, b. put the initial prompt of the user into description of the main node dialogbox, c. have the llm generate the rest of the nodes, each with their title and description to go into the respective title and description of the sub nodes... this was implemented and this i want to have installed again. help modify the yaml file initiate_mindmap accordingly and to adjust the code to follow this workflow


ok the lates reply is better, however here is a snippet of the reply: "That's great to hear! Entering the Swedish market for road maintenance can be a strategic move. Here are some steps you can consider to enter the market:

1. Research the Market: Understand the current road maintenance industry in Sweden, including key players, market size, growth trends, and regulatory environment.

2. Identify Opportunities: Explore specific opportunities within the road maintenance sector in Sweden, such as innovative technologies, sustainable practices, or unmet needs...." the prompt structure is setup in such a way (line 36-41), that key considerations are separately listed. These are: 1. Research the Market, 2. Identify Opportunities, ... and shall be listed in the reply to the user together with the prompt in line 44 and the green button (must be somewhere in the code already). Upon clicking the green button, the dialoguebox shall reduce in hight (300) and the bottom line shall align with the bottom line of the screen making place for the mindmapping. The mindmapping structure, inclusive numbering is provided in the completion of the llm in json, where the first part of the structure is the topic itself to represent the main node

once a mindmap is created there shall be a dropselectionbox in GovernanceChatDialog right next to the selection box of llm. this box shall include all nodes in the mindmap (index), when a new node is added the index of the new node shall be added to the selectionbox. When double clicking on a node the selectionbox shall automatically select the index of the node, also when selecting a node index in the selectionbox the node dialogbox shall open. further in the dialog field a user post shall be inserted "user select node: [index]". Be sure to consider the event that the user is loading a saved mindmap aswell. default in the new box shall be "Gov" and when closing the node "Gov" shall be selected. Further the box shall be leftalignd next to the llm model 

ChatFork: mindmap is one of several mindback tools. The next MB-Tool is the ChatFork. The Chatfork is used by the governance agent when a complex theme is explained. Like "what is the history of the internet" being explained over 10 points each containing several context subpoints. Each point and subpoint are organized in text fields under eachother. The user is enabled to address each point and subsection and fork comments, utterances and questions to each part and thereby initiate a subchat, which in turn can be forked in to another subchat, and so on. Based on the input from the user the initiation_Agent workflow selects the tool ChatForkTool.yaml and have the llm return the completion as described in the yaml file. mindback build the completion in the ui. The user interact with the displayed structur by clicking parts of the structure, pressing tab fork a dialog textfield for the user to follow a tread with statements or questions

well 1. agentic selection of tool, 2. prompting the llm to get the content for the selected tool, e.g. if the user states "we want to make 20% of our revenue in sweden in three years" the agents shall detect this as a purpose that require breakdown of the task and therefore selects the mindmap tool. based on this selection the governing agent or whoever shall make a prompt to the llm to receive back in the completion issues to be considered for the purpose in a format which allows mindback to build the mindmap structure. In the same token  for the initiation prompt "What is democracy", the agentic selection is tool for a verbose explanation of the topic, which allow the user to interact at each level of the returned text with statements and questions, therefore the agentic selection is  the ChatForktool and the government agent has to prompt the llm to give, e.g. a verbose completion with 10 points covering subtopics of democracy in such a structure that it enables mindback to display the completion in the ChatFork tool for further interaction with the user in terms of enabling the user to fork a dialog at any line of the description of the topic.