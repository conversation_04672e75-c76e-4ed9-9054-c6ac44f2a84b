/**
 * Connection Model
 * Represents a connection between two nodes in the mind map
 */

import { v4 as uuidv4 } from 'uuid';

/**
 * Connection style options
 */
export type ConnectionStyle = 
  | 'straight'   // Direct line between nodes
  | 'curved'     // Curved line between nodes
  | 'orthogonal' // Right-angled line between nodes
  | 'bezier';    // Bezier curve between nodes

/**
 * Connection type options
 */
export type ConnectionType = 'solid' | 'dashed';

/**
 * Line style types
 */
export type LineStyle = 'angled' | 'straight' | 'curved';

/**
 * Line thickness types
 */
export type LineThickness = 1 | 2 | 3;

/**
 * Connection kind options
 */
export type ConnectionKind = 'tree' | 'bird';

/**
 * Connection interface
 */
export interface Connection {
  id: string;
  from: string;  // Source node ID
  to: string;    // Target node ID
  label?: string;
  type: ConnectionType;
  lineStyle: LineStyle;
  thickness: LineThickness;
  color?: string;
  showArrow: boolean;
  kind: ConnectionKind;  // Determines if it's a tree (parent-child) or bird (lateral) connection
  // Metadata
  createdAt: number;
  updatedAt: number;
}

/**
 * Input type for creating connections
 */
export interface CreateConnectionInput {
  from: string;
  to: string;
  label?: string;
  type?: ConnectionType;
  lineStyle?: LineStyle;
  thickness?: LineThickness;
  color?: string;
  showArrow?: boolean;
  kind?: ConnectionKind;
}

/**
 * Connection default values
 */
export const DEFAULT_CONNECTION_COLOR = '#9ca3af';
export const DEFAULT_CONNECTION_THICKNESS: LineThickness = 2;
export const DEFAULT_CONNECTION_STYLE: LineStyle = 'angled';
export const DEFAULT_CONNECTION_TYPE: ConnectionType = 'solid';

/**
 * Default connection object
 */
export const DEFAULT_CONNECTION: Required<Omit<Connection, 'id' | 'from' | 'to' | 'label'>> = {
  type: 'solid',
  lineStyle: 'angled',
  thickness: 2,
  color: '#9ca3af',
  showArrow: false,
  kind: 'tree',
  createdAt: Date.now(),
  updatedAt: Date.now()
};

/**
 * Creates a new connection with the given properties
 */
export function createConnection(input: CreateConnectionInput): Connection {
  if (!input.from || !input.to) {
    throw new Error('Connection must have both from and to nodes specified');
  }

  return {
    id: uuidv4(),
    ...DEFAULT_CONNECTION,
    ...input,
    createdAt: Date.now(),
    updatedAt: Date.now()
  };
}

/**
 * Updates an existing connection with new properties
 */
export function updateConnection(connection: Connection, updates: Partial<Connection>): Connection {
  return {
    ...connection,
    ...updates,
    updatedAt: Date.now()
  };
}

/**
 * Creates a new tree connection (parent-child relationship)
 */
export function createTreeConnection(params: {
  parent: string;
  child: string;
  lineStyle?: LineStyle;
  color?: string;
  thickness?: LineThickness;
}): Connection {
  return createConnection({
    from: params.parent,
    to: params.child,
    kind: 'tree',
    lineStyle: params.lineStyle,
    color: params.color,
    thickness: params.thickness,
    type: 'solid'
  });
}

/**
 * Creates a new bird connection (lateral relationship)
 */
export function createBirdConnection(params: {
  source: string;
  target: string;
  label?: string;
  lineStyle?: LineStyle;
  color?: string;
  thickness?: LineThickness;
}): Connection {
  return createConnection({
    from: params.source,
    to: params.target,
    kind: 'bird',
    label: params.label,
    lineStyle: params.lineStyle || 'curved',
    color: params.color || '#6b7280',
    thickness: params.thickness || 1,
    type: 'solid'
  });
}

export default Connection;
