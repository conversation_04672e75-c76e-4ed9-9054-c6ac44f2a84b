import React, { useState, useRef, useEffect } from "react";
import Dialog from "@mui/material/Dialog";
import Paper, { PaperProps } from "@mui/material/Paper";
import Draggable from "react-draggable";
import { Node } from "../../../core/models/Node";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Box from "@mui/material/Box";
import "./NewNodeDialog.css";

// Debug utilities with unique namespace
const debugLog = (message: string, ...args: any[]) => {
  console.log(`[NewNodeDialog] ${message}`, ...args);
};

// Interface for the dialog props
interface NewNodeDialogProps {
  open: boolean;
  node: Node;
  onClose: () => void;
  onSave: (node: Node) => void;
}

// Draggable Paper component with unique ref
function NodeDraggablePaper(props: PaperProps) {
  const nodeRef = useRef(null);

  return (
    <Draggable
      handle=".node-editor-header"
      bounds="body"
      nodeRef={nodeRef}
      defaultPosition={{ x: 223, y: 100 }}
      cancel=".node-editor-header-buttons, .node-editor-close-button, .node-editor-header-button"
      enableUserSelectHack={true}
      onStart={(e) => {
        e.stopPropagation();
        debugLog("Starting drag operation");
        document.body.classList.add("node-dragging-active");
      }}
      onDrag={() => {
        debugLog("Drag in progress");
      }}
      onStop={() => {
        debugLog("Drag operation completed");
        document.body.classList.remove("node-dragging-active");
      }}
    >
      <Paper {...props} ref={nodeRef} className="node-editor-paper" />
    </Draggable>
  );
}

export const NewNodeDialog: React.FC<NewNodeDialogProps> = ({
  open,
  node,
  onClose,
  onSave,
}) => {
  // State for dialog size
  const [size, setSize] = useState({ width: 1375, height: 750 });
  const isResizing = useRef(false);
  const resizeStartPos = useRef({ x: 0, y: 0 });
  const startSize = useRef({ width: 1375, height: 750 });
  const dialogRef = useRef<HTMLDivElement>(null);

  // State for color agent tabs
  const [activeTab, setActiveTab] = useState(0);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Handle reset position
  const handleResetPosition = () => {
    if (dialogRef.current) {
      const draggableNode = dialogRef.current.closest(".react-draggable");
      if (draggableNode) {
        (draggableNode as HTMLElement).style.transform =
          "translate(223px, 100px)";
        debugLog("Reset position");
      }
    }
  };

  // Handle reset size
  const handleResetSize = () => {
    setSize({ width: 1375, height: 750 });
    debugLog("Reset size");
  };

  // Handle resize start
  const handleResizeStart = (e: React.MouseEvent) => {
    debugLog("Starting resize operation");
    e.preventDefault();
    e.stopPropagation();

    isResizing.current = true;
    resizeStartPos.current = { x: e.clientX, y: e.clientY };
    startSize.current = { ...size };

    // Add the transparent selection class to the body
    document.body.classList.add("node-dragging-active");

    const handleMouseMove = (e: MouseEvent) => {
      if (isResizing.current) {
        const deltaX = e.clientX - resizeStartPos.current.x;
        const deltaY = e.clientY - resizeStartPos.current.y;

        debugLog("Resizing - delta:", deltaX, deltaY);

        setSize({
          width: Math.max(800, startSize.current.width + deltaX),
          height: Math.max(450, startSize.current.height + deltaY),
        });
      }
    };

    const handleMouseUp = () => {
      debugLog("Resize operation completed");
      isResizing.current = false;
      // Remove the transparent selection class from the body
      document.body.classList.remove("node-dragging-active");
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };

    // Use capture to ensure we get all events
    document.addEventListener("mousemove", handleMouseMove, { capture: true });
    document.addEventListener("mouseup", handleMouseUp, { capture: true });
  };

  // Fix position after dialog is mounted
  useEffect(() => {
    if (open && dialogRef.current) {
      debugLog("Dialog opened, fixing position");

      // Wait a short time for the dialog to be fully rendered
      setTimeout(() => {
        const draggableNode = dialogRef.current?.closest(".react-draggable");
        if (draggableNode) {
          try {
            (draggableNode as HTMLElement).style.transform =
              "translate(223px, 100px)";
            debugLog("Set initial position");
          } catch (error) {
            debugLog("Failed to set initial position:", error);
          }
        }
      }, 100);
    }
  }, [open]);

  // Only render the dialog when open is true
  if (!open) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      PaperComponent={NodeDraggablePaper}
      PaperProps={{
        style: {
          width: `${size.width}px`,
          height: `${size.height}px`,
          maxWidth: "none",
          maxHeight: "none",
          margin: 0,
          position: "absolute",
          pointerEvents: "auto",
        },
        elevation: 24,
      }}
      hideBackdrop
      disableEnforceFocus
      aria-labelledby="node-editor-title"
      className="node-editor-dialog-root"
      sx={{
        zIndex: 2000,
        "& .MuiDialog-container": {
          position: "fixed",
          top: 0,
          left: 0,
          height: "100%",
          width: "100%",
          alignItems: "flex-start",
          justifyContent: "flex-start",
          pointerEvents: "none",
          transform: "none",
        },
        "& .MuiPaper-root": {
          pointerEvents: "auto",
          position: "absolute",
          overflow: "visible",
        },
        "& .MuiBackdrop-root": {
          backgroundColor: "transparent",
        },
      }}
    >
      <div
        className="node-editor-dialog"
        ref={dialogRef}
        data-testid="node-editor-dialog"
      >
        {/* Node Editor Header - Updated layout */}
        <div className="node-editor-header">
          <div className="node-editor-header-title">
            <img
              src="./Public/Logo/MB_logo.jpg"
              alt="MindBack Logo"
              className="node-editor-header-logo"
              width="24"
              height="24"
              draggable="false"
              onError={(e) => {
                console.error('Failed to load NewNodeDialog logo');
                e.currentTarget.src = "./Public/Logo/mindback_logo.jpg";
              }}
            />
            <span className="node-editor-header-path">
              {node.metadata?.nodePath || '1.0'}
            </span>
            <span className="node-editor-header-text">{node.text}</span>
          </div>
          <div className="node-editor-header-buttons">
            <span className="node-editor-node-id">ID: {node.id}</span>
            <button
              onClick={handleResetPosition}
              className="node-editor-header-button"
              title="Reset Position"
            >
              ↖
            </button>
            <button
              onClick={handleResetSize}
              className="node-editor-header-button"
              title="Reset Size"
            >
              ⊡
            </button>
            <button
              onClick={onClose}
              className="node-editor-close-button"
              title="Close"
            >
              ×
            </button>
          </div>
        </div>

        <div className="node-editor-content">
          {/* Node Info Section - Compact layout */}
          <div className="node-editor-info-section">
            {/* Title input with inline label */}
            <div className="node-editor-title-container">
              <label className="node-editor-inline-label">Node Title:</label>
              <input
                type="text"
                value={node.text}
                onChange={(e) => onSave({ ...node, text: e.target.value })}
                placeholder="Enter title"
                className="node-editor-title-input"
              />
            </div>

            <label className="node-editor-input-label">Node Description</label>
            <textarea
              value={node.description || ""}
              onChange={(e) => onSave({ ...node, description: e.target.value })}
              placeholder="Add a description for this node..."
              className="node-editor-description-textarea"
              rows={6}
            />
          </div>

          {/* Color Agent Tabs */}
          <div className="node-editor-agent-tabs">
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              aria-label="color agent tabs"
              className="color-agent-tabs"
            >
              <Tab label="White Agent" className="color-tab white-hat" />
              <Tab label="Red Agent" className="color-tab red-hat" />
              <Tab label="Black Agent" className="color-tab black-hat" />
              <Tab label="Yellow Agent" className="color-tab yellow-hat" />
              <Tab label="Green Agent" className="color-tab green-hat" />
            </Tabs>

            {/* Tab Content */}
            <div className="tab-content-container">
              {activeTab === 0 && (
                <div className="agent-tab-content white-hat-content">
                  <p>
                    White Agent (Facts): Information and data known or needed.
                  </p>
                </div>
              )}
              {activeTab === 1 && (
                <div className="agent-tab-content red-hat-content">
                  <p>
                    Red Agent (Emotions): Intuitions, feelings, and emotional
                    reactions.
                  </p>
                </div>
              )}
              {activeTab === 2 && (
                <div className="agent-tab-content black-hat-content">
                  <p>
                    Black Agent (Caution): Potential problems, risks, and
                    difficulties.
                  </p>
                </div>
              )}
              {activeTab === 3 && (
                <div className="agent-tab-content yellow-hat-content">
                  <p>
                    Yellow Agent (Benefits): Positives, values, and benefits.
                  </p>
                </div>
              )}
              {activeTab === 4 && (
                <div className="agent-tab-content green-hat-content">
                  <p>
                    Green Agent (Creativity): New ideas, possibilities, and
                    alternatives.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Resize handle */}
        <div
          className="node-editor-resize-handle"
          onMouseDown={handleResizeStart}
        />
      </div>
    </Dialog>
  );
};
