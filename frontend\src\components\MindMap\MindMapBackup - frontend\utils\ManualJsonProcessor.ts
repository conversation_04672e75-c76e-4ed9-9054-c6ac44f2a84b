/**
 * Utility for processing mindmap JSON data using a manual node-by-node approach
 * @deprecated This entire module is being phased out in favor of the fully automatic workflow.
 * New code should not use these functions. Use the automated MBCP workflow instead.
 */

// Define custom store interface with just the methods we need
interface MindMapStoreAdapter {
  addNode: (parentId: string, direction: number) => string | null;
  updateNode: (id: string, updates: any) => void;
  addConnection: (input: any) => void;
  autoLayout: () => void;
}

interface NodeData {
  title: string;
  content?: string;
  children?: NodeData[];
  metadata?: Record<string, any>;
}

/**
 * Debug function to log the structure of a JSON object for mindmap data
 */
const debugJsonStructure = (data: any, level = 0): void => {
  const indent = '  '.repeat(level);
  
  if (!data) {
    console.log(`${indent}NULL or UNDEFINED`);
    return;
  }
  
  if (typeof data !== 'object') {
    console.log(`${indent}${typeof data}: ${JSON.stringify(data)}`);
    return;
  }
  
  if (Array.isArray(data)) {
    console.log(`${indent}ARRAY[${data.length}]:`);
    data.forEach((item, index) => {
      console.log(`${indent}  [${index}]:`);
      debugJsonStructure(item, level + 2);
    });
    return;
  }
  
  console.log(`${indent}OBJECT:`);
  Object.entries(data).forEach(([key, value]) => {
    console.log(`${indent}  "${key}":`);
    debugJsonStructure(value, level + 2);
  });
};

/**
 * Creates a store adapter that works with our required interface
 */
const createStoreAdapter = (store: any): MindMapStoreAdapter => {
  console.log('Creating store adapter with store:', store);
  
  if (!store) {
    console.error('Invalid store provided to adapter: null or undefined');
    throw new Error('Invalid store: null or undefined');
  }
  
  // Check if this is a store instance or a Zustand hook result
  let storeObj = store;
  
  // If it has getState, it's likely the Zustand store itself
  if (typeof store.getState === 'function') {
    storeObj = store.getState();
    console.log('Using getState() to get store object:', storeObj);
  }
  
  // If it has setState, it's likely the Zustand hook result
  if (typeof store.setState === 'function' && typeof storeObj.addNode !== 'function') {
    console.log('Store appears to be a Zustand hook result with setState');
    
    // Get the actual methods
    if (typeof store.addNode === 'function') {
      storeObj = store;
      console.log('Using the store hook directly');
    }
  }
  
  // Final validation to ensure we have the necessary methods
  if (typeof storeObj.addNode !== 'function') {
    console.error('Invalid store: missing addNode method', storeObj);
    throw new Error('Invalid store: missing addNode method');
  }
  
  // Verify all required methods exist
  const requiredMethods = ['addNode', 'updateNode', 'addConnection', 'autoLayout'];
  const missingMethods = requiredMethods.filter(method => typeof storeObj[method] !== 'function');
  
  if (missingMethods.length > 0) {
    console.error('Store missing required methods:', missingMethods);
    throw new Error(`Store missing required methods: ${missingMethods.join(', ')}`);
  }
  
  console.log('Final store object for adapter:', {
    hasAddNode: typeof storeObj.addNode === 'function',
    hasUpdateNode: typeof storeObj.updateNode === 'function',
    hasAddConnection: typeof storeObj.addConnection === 'function',
    hasAutoLayout: typeof storeObj.autoLayout === 'function'
  });
  
  return {
    addNode: (parentId: string, direction: number = 90) => {
      console.log('Adding node with parentId:', parentId, 'direction:', direction);
      try {
        // Use the store's addNode method directly
        const result = storeObj.addNode(parentId, direction);
        console.log('Node added, result:', result);
        return result;
      } catch (error) {
        console.error('Error in addNode:', error);
        return null;
      }
    },
    updateNode: (id: string, updates: any) => {
      console.log('Updating node:', id, 'with updates:', updates);
      try {
        // Update node with content
        storeObj.updateNode(id, updates);
        console.log('Node updated successfully');
      } catch (error) {
        console.error('Error in updateNode:', error);
      }
    },
    addConnection: (input: any) => {
      console.log('Adding connection:', input);
      try {
        // Add connection between nodes
        storeObj.addConnection(input);
        console.log('Connection added successfully');
      } catch (error) {
        console.error('Error in addConnection:', error);
      }
    },
    autoLayout: () => {
      console.log('Running auto layout');
      try {
        // Run auto layout
        storeObj.autoLayout();
        console.log('Auto layout completed');
      } catch (error) {
        console.error('Error in autoLayout:', error);
      }
    }
  };
};

/**
 * Creates a mindmap from JSON data using manual node creation.
 * This ensures all nodes are created through the same code path.
 * 
 * @deprecated - Use automatic MBCP workflow instead. This function is part of the semi-automatic workflow being phased out.
 * Future mindmap generation should use the direct MBCP data processing in OptimizedMindMap_Modular.tsx.
 */
export const createMindMapFromJsonManual = (
  jsonData: any,
  store: any,
  parentId?: string,
  parentPath?: string,
  childIndex?: number
): string | null => {
  console.warn('Semi-automatic workflow (createMindMapFromJsonManual) is deprecated. Use automatic workflow instead.');
  
  try {
    // For debugging - show the exact structure of the data at each level
    if (parentId) {
      console.log(`CHILD NODE DATA (index ${childIndex}, parent ${parentId}):`);
      console.log('Title:', jsonData.title);
      console.log('Content:', jsonData.content);
      console.log('Has children:', jsonData.children && jsonData.children.length > 0);
    } else {
      console.log('Starting to create mindmap manually from JSON:');
      debugJsonStructure(jsonData);
      console.log('Child count in input data:', 
        jsonData.children ? jsonData.children.length : 
        (jsonData.keyPoints ? jsonData.keyPoints.length : 0));
    }
    
    // Extra validation for the store
    if (!store) {
      console.error('No store provided to createMindMapFromJsonManual');
      return null;
    }
    
    // Get Zustand store state if needed (it may be the store hook)
    const actualStore = typeof store.getState === 'function' ? store : store;
    
    // Create an adapter from the store
    const adapter = createStoreAdapter(actualStore);
    
    // IMPORTANT: Preserve the original data passed in for child nodes
    // This ensures we don't lose the specific title/content for recursive child calls
    let nodeData: NodeData;
    
    // For child nodes, use the jsonData directly - this preserves titles!
    if (parentId) {
      nodeData = {
        title: jsonData.title || `Child ${childIndex !== undefined ? childIndex + 1 : ''}`,
        content: jsonData.content || '',
        children: jsonData.children || [],
        metadata: jsonData.metadata || {}
      };
      console.log(`CHILD NODE PRESERVED TITLE: "${nodeData.title}"`);
    } 
    // For root nodes, process the data more thoroughly
    else {
      // Check for properly structured mindmap data (from backend)
      if (jsonData.mindmap && jsonData.mindmap.root) {
        console.log('Found mindmap.root structure - using backend format');
        return createMindMapFromBackendFormat(jsonData.mindmap.root, store);
      }
      
      if (jsonData.mindmap) {
        nodeData = jsonData.mindmap;
      } else if (jsonData.nodes) {
        nodeData = jsonData;
      } else {
        // Basic data with no explicit children
        nodeData = { 
          title: jsonData.title || jsonData.text || 'Root Node', 
          content: jsonData.content || jsonData.description || '', 
          children: jsonData.children || [] 
        };
        
        // If we have keyPoints but no children, convert keyPoints to children
        if (jsonData.keyPoints && Array.isArray(jsonData.keyPoints) && 
            (!nodeData.children || !nodeData.children.length)) {
          console.log('Converting keyPoints to children:', jsonData.keyPoints);
          nodeData.children = jsonData.keyPoints.map((point: string) => ({
            title: point.split(':')[0] || point.substring(0, 30),
            content: point
          }));
        }
        
        // If we have directData, use that directly (from manual method)
        if (jsonData.directData && typeof jsonData.directData === 'object') {
          console.log('Using directData property found in input');
          nodeData = jsonData.directData;
        }
        
        // If we have structure info but no children
        if (jsonData.structure && 
            (!nodeData.children || !nodeData.children.length)) {
          console.log('Trying to extract children from structure');
          // Try to find child nodes in structure property (common in LLM responses)
          if (typeof jsonData.structure === 'object') {
            const possibleChildren = [];
            
            // Check for mainPoints, keyPoints, branches, or topics
            for (const key of ['mainPoints', 'keyPoints', 'branches', 'topics', 'points', 'concepts']) {
              if (jsonData.structure[key] && Array.isArray(jsonData.structure[key])) {
                console.log(`Found children in structure.${key}:`, jsonData.structure[key]);
                nodeData.children = jsonData.structure[key].map((item: any) => {
                  if (typeof item === 'string') {
                    return {
                      title: item.split(':')[0] || item.substring(0, 30),
                      content: item
                    };
                  } else if (typeof item === 'object') {
                    return {
                      title: item.title || item.name || item.topic || 'Untitled',
                      content: item.content || item.description || item.details || '',
                      children: item.children || item.subtopics || item.points || []
                    };
                  }
                  return item;
                });
                break;
              }
            }
          }
        }
      }
    }
    
    // Debug the processed node data
    console.log(`Processed node data for ${parentId ? 'child' : 'root'} node:`, 
      { title: nodeData.title, content: nodeData.content?.substring(0, 30) + '...' });
      
    console.log('Does node have children:', nodeData.children && nodeData.children.length > 0, 
      'Count:', nodeData.children?.length || 0);
    
    // Calculate node path for proper hierarchical numbering
    let nodePath = '1.0'; // Default for root
    
    if (parentPath && childIndex !== undefined) {
      // If parent is root (1.0), children should be 1.1, 1.2, etc.
      if (parentPath === '1.0') {
        nodePath = `1.${childIndex + 1}`;
      } else {
        // For non-root parents, append the child number to the parent path
        nodePath = `${parentPath}.${childIndex + 1}`;
      }
    }
    
    // Get the title for this node - SIMPLIFIED to just use nodeData.title directly
    // This ensures we're using the title we preserved from the input data
    const nodeTitle = nodeData.title || (parentId 
      ? `Child ${childIndex !== undefined ? childIndex + 1 : ''}`
      : 'Root Node');
      
    console.log(`FINAL Node title for ${parentId ? 'child' : 'root'} node: "${nodeTitle}"`);
    
    // Create the node
    const rootId = parentId || 'root';
    const nodeId = adapter.addNode(rootId, 90);
    console.log('Created node with ID:', nodeId, 'Path:', nodePath);
    
    if (!nodeId) {
      console.error('Failed to create node:', nodeData);
      return null;
    }
    
    // Update the node with actual data
    adapter.updateNode(nodeId, {
      title: nodeTitle,
      text: nodeTitle,
      content: nodeData.content || '',
      metadata: {
        ...(nodeData.metadata || {}),
        nodePath: nodePath // Add proper hierarchical path
      }
    });
    
    // Create connection if this is a child node
    if (parentId) {
      try {
        // Add validation to ensure both source and target exist
        if (!parentId || !nodeId) {
          console.error('Cannot create connection: Missing source or target node ID', {
            source: parentId,
            target: nodeId
          });
        } else {
          adapter.addConnection({
            from: parentId,
            to: nodeId
          });
          console.log(`Created connection from ${parentId} to ${nodeId}`);
        }
      } catch (error) {
        console.error('Error creating connection:', error);
      }
    }
    
    // Process children recursively if they exist
    if (nodeData.children && Array.isArray(nodeData.children) && nodeData.children.length > 0) {
      const children = nodeData.children; // Store reference to avoid linter errors
      console.log('Processing', children.length, 'children for node', nodeId, 'with path', nodePath);
      
      // Log child titles for debugging
      children.forEach((child, index) => {
        console.log(`Child ${index} title: "${child.title || 'Untitled'}"`);
      });
      
      // Process each child
      children.forEach((child, index) => {
        console.log(`Creating child node ${index + 1} of ${children.length}`);
        try {
          // IMPORTANT: Pass each child object directly to preserve its structure
          // This ensures title, content, etc. are preserved
          const childId = createMindMapFromJsonManual(child, actualStore, nodeId, nodePath, index);
          console.log(`Child node created with ID: ${childId}`);
        } catch (childError) {
          console.error(`Error creating child ${index}:`, childError);
        }
      });
    } else {
      console.log('No children to process for node:', nodeId);
    }
    
    // Auto layout after all nodes are created if this is the root call
    if (!parentId) {
      console.log('Root node creation complete, scheduling auto layout');
      setTimeout(() => {
        try {
          adapter.autoLayout();
        } catch (layoutError) {
          console.error('Error during auto layout:', layoutError);
        }
      }, 100);
    }
    
    return nodeId;
  } catch (error) {
    console.error('Error creating mindmap from JSON manually:', error);
    return null;
  }
};

/**
 * Processes backend formatted mindmap data 
 * (matching the format used in GovernanceLLM.processMindmapData)
 */
const createMindMapFromBackendFormat = (rootNodeData: any, store: any): string | null => {
  try {
    console.log('Processing backend format with root node:', rootNodeData);
    
    // Extra validation for the store
    if (!store) {
      console.error('No store provided');
      return null;
    }
    
    // Get the store reference properly
    const actualStore = typeof store.getState === 'function' ? store : store;
    
    // Create an adapter from the store
    const adapter = createStoreAdapter(actualStore);
    
    // Create the root node
    const rootId = adapter.addNode('root', 90);
    
    if (!rootId) {
      console.error('Failed to create root node');
      return null;
    }
    
    // Update the root node with data
    adapter.updateNode(rootId, {
      title: rootNodeData.text || 'Root Node',
      text: rootNodeData.text || 'Root Node',
      content: rootNodeData.description || '',
      metadata: {
        nodePath: '1.0' // Root node path
      }
    });
    
    // Process child nodes recursively
    if (rootNodeData.children && Array.isArray(rootNodeData.children)) {
      processChildNodesBackendFormat(rootNodeData.children, rootId, actualStore);
    }
    
    // Auto layout after all nodes are created
    setTimeout(() => {
      try {
        adapter.autoLayout();
        console.log('Auto layout completed');
      } catch (error) {
        console.error('Error during auto layout:', error);
      }
    }, 100);
    
    return rootId;
  } catch (error) {
    console.error('Error creating mindmap from backend format:', error);
    return null;
  }
};

/**
 * Processes child nodes from backend formatted data
 */
const processChildNodesBackendFormat = (
  children: any[], 
  parentId: string, 
  store: any
) => {
  try {
    // Get the store reference properly
    const actualStore = typeof store.getState === 'function' ? store : store;
    
    // Create an adapter from the store
    const adapter = createStoreAdapter(actualStore);
    
    // Calculate parent path from parent node
    let parentPath = '1.0'; // Default for root
    if (actualStore.nodes && actualStore.nodes[parentId] && 
        actualStore.nodes[parentId].metadata && 
        actualStore.nodes[parentId].metadata.nodePath) {
      parentPath = actualStore.nodes[parentId].metadata.nodePath;
    }
    
    // Process each child node
    children.forEach((child, index) => {
      try {
        console.log(`Processing backend child ${index}:`, child);
        
        // Create child node
        const childId = adapter.addNode(parentId, 90);
        if (!childId) {
          console.error('Failed to create child node in processChildNodesBackendFormat');
          return;
        }
        
        // Calculate child path
        let childPath = `${parentPath}.${index + 1}`;
        // Special case for root's children
        if (parentPath === '1.0') {
          childPath = `1.${index + 1}`;
        }
        
        // Update child node
        adapter.updateNode(childId, {
          title: child.text || `Child ${index + 1}`,
          text: child.text || `Child ${index + 1}`,
          content: child.description || '',
          metadata: {
            nodePath: childPath
          }
        });
        
        // Create connection
        adapter.addConnection({
          from: parentId,
          to: childId
        });
        
        // Process grandchildren recursively
        if (child.children && Array.isArray(child.children) && child.children.length > 0) {
          processChildNodesBackendFormat(child.children, childId, actualStore);
        }
      } catch (error) {
        console.error(`Error processing backend child ${index}:`, error);
      }
    });
  } catch (error) {
    console.error('Error in processChildNodesBackendFormat:', error);
  }
};

/**
 * Safely parses JSON strings that might come from LLM responses
 * Handles various JSON formatting issues
 */
export const safeParseJson = (jsonString: string): any | null => {
  try {
    console.log('Attempting to parse JSON string');
    
    // First attempt: direct parsing
    try {
      return JSON.parse(jsonString);
    } catch (directError) {
      console.log('Direct JSON parsing failed, trying alternative approaches');
    }
    
    // Second attempt: Clean up the string first
    let cleanedString = jsonString;
    
    // Remove trailing commas which are invalid in JSON
    cleanedString = cleanedString.replace(/,\s*}/g, '}').replace(/,\s*\]/g, ']');
    
    // Add any missing quotes around property names
    cleanedString = cleanedString.replace(/([{,]\s*)([a-zA-Z0-9_]+)(\s*:)/g, '$1"$2"$3');
    
    try {
      return JSON.parse(cleanedString);
    } catch (cleaningError) {
      console.log('Cleaned JSON parsing failed, trying with regex extraction');
    }
    
    // Third attempt: Try to extract JSON with regex
    const jsonRegex = /{[\s\S]*}/;
    const matches = jsonString.match(jsonRegex);
    
    if (matches && matches[0]) {
      try {
        return JSON.parse(matches[0]);
      } catch (regexError) {
        console.error('Regex extraction failed:', regexError);
      }
    }
    
    console.error('All JSON parsing attempts failed');
    return null;
  } catch (error) {
    console.error('Error in safeParseJson:', error);
    return null;
  }
};

/**
 * A simplified direct approach to build a mindmap tree.
 * This function focuses just on creating the node structure correctly.
 */
export const buildMindMapTreeDirectly = (store: any, data: any): string | null => {
  try {
    console.log('=========== BUILDING MINDMAP TREE DIRECTLY ===========');
    console.log('Building mindmap tree with data:', JSON.stringify(data, null, 2));
    
    if (!store || !data) {
      console.error('Invalid store or data');
      return null;
    }
    
    // Create a store adapter to interact with the store
    const adapter = createStoreAdapter(store);
    
    // Create the root node
    console.log('Creating root node with title:', data.title);
    const rootId = adapter.addNode('root', 90);
    
    if (!rootId) {
      console.error('Failed to create root node');
      return null;
    }
    
    // Update the root node with data
    adapter.updateNode(rootId, {
      title: data.title || 'Root Node',
      text: data.title || 'Root Node',
      content: data.content || '',
      metadata: {
        nodePath: '1.0' // Root node path
      }
    });
    
    // More detailed logging for children analysis
    console.log('Analyzing children data structure:');
    console.log('data.children exists:', !!data.children);
    console.log('data.children is array:', Array.isArray(data.children));
    console.log('data.children length:', data.children?.length || 0);
    
    // Process children with better error handling
    if (data.children && Array.isArray(data.children) && data.children.length > 0) {
      console.log(`Processing ${data.children.length} first-level children`);
      
      // Process each first-level child
      data.children.forEach((child: any, index: number) => {
        try {
          console.log(`Creating first-level child ${index + 1} with title:`, child.title || 'Untitled Child');
          
          // Generate child path (root is 1.0, first level is 1.1, 1.2, etc.)
          const childPath = `1.${index + 1}`;
          
          // Create child node
          const childId = adapter.addNode(rootId, 90);
          
          if (!childId) {
            console.error('Failed to create child node:', index);
            return; // Skip this child
          }
          
          // Update child node with data
          adapter.updateNode(childId, {
            title: child.title || `Child ${index + 1}`,
            text: child.title || `Child ${index + 1}`,
            content: child.content || '',
            metadata: {
              nodePath: childPath
            }
          });
          
          // Create connection from root to child
          adapter.addConnection({
            from: rootId,
            to: childId
          });
          
          // Process second-level children (grandchildren)
          if (child.children && Array.isArray(child.children) && child.children.length > 0) {
            console.log(`Processing ${child.children.length} second-level children for child ${index + 1}`);
            
            child.children.forEach((grandChild: any, grandChildIndex: number) => {
              try {
                console.log(`Creating second-level child ${grandChildIndex + 1} for parent ${index + 1}`);
                
                // Generate grandchild path (e.g., 1.1.1, 1.1.2, etc.)
                const grandChildPath = `${childPath}.${grandChildIndex + 1}`;
                
                // Create grandchild node
                const grandChildId = adapter.addNode(childId, 90);
                
                if (!grandChildId) {
                  console.error('Failed to create grandchild node:', grandChildIndex);
                  return; // Skip this grandchild
                }
                
                // Update grandchild node with data
                adapter.updateNode(grandChildId, {
                  title: grandChild.title || `Subpoint ${grandChildIndex + 1}`,
                  text: grandChild.title || `Subpoint ${grandChildIndex + 1}`,
                  content: grandChild.content || '',
                  metadata: {
                    nodePath: grandChildPath
                  }
                });
                
                // Create connection from child to grandchild
                adapter.addConnection({
                  from: childId,
                  to: grandChildId
                });
                
              } catch (grandChildError) {
                console.error(`Error processing second-level child ${grandChildIndex + 1}:`, grandChildError);
              }
            });
          }
          
        } catch (childError) {
          console.error(`Error processing first-level child ${index + 1}:`, childError);
        }
      });
    } else {
      console.log('No children to process for root node.');
    }
    
    // Run auto layout to position nodes properly
    console.log('Running auto layout...');
    setTimeout(() => {
      try {
        adapter.autoLayout();
        console.log('Auto layout completed');
      } catch (error) {
        console.error('Error in auto layout:', error);
      }
    }, 100);
    
    return rootId;
  } catch (error) {
    console.error('Error building mindmap tree directly:', error);
    return null;
  }
};

// Export this function for direct use in browser console
export const testManualMethod = (store: any, jsonData: any) => {
  console.log('Manual method test started with data:', jsonData);
  
  // First attempt: use the existing store
  try {
    console.log('Testing with existing store reference...');
    const result = createMindMapFromJsonManual(jsonData, store);
    console.log('Result:', result);
    return result;
  } catch (error) {
    console.error('Error with direct store reference:', error);
  }
  
  // If the first attempt fails, try extracting a clean store reference
  if (store && store.getState) {
    try {
      console.log('Testing with store.getState()...');
      const stateStore = store.getState();
      const result2 = createMindMapFromJsonManual(jsonData, stateStore);
      console.log('Result with state store:', result2);
      return result2;
    } catch (error) {
      console.error('Error with state store reference:', error);
    }
  }
  
  console.error('All manual method tests failed');
  return null;
};

// For global usage in browser console
if (typeof window !== 'undefined') {
  (window as any).testManualMindmapMethod = testManualMethod;
} 