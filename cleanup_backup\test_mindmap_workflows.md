# Testing Mindmap Creation Workflows

This document outlines the test procedures for verifying that the React Hooks violations in the mindmap creation workflows have been fixed.

## Test Setup

1. Start the application using the `run_setup.ps1` script
2. Open the browser to `http://localhost:5173`
3. Open the browser console to monitor for React Hooks violations

## Test Cases

### Test Case 1: Manual Workflow (Build Mind Map Button)

1. Enter a prompt that will trigger teleological intent (e.g., "Create a strategic plan for launching a new product")
2. Wait for the response to appear
3. Click the "Build Mind Map" button
4. Verify that a new mindsheet is created with a mindmap
5. Check the console for any React Hooks violations

**Expected Result:**
- A new mindsheet should be created with a mindmap
- The mindmap should be properly initialized with a root node
- No React Hooks violations should appear in the console

### Test Case 2: Automatic Workflow (Teleological Intent)

1. Select "Teleological" from the intent dropdown
2. Enter a prompt (e.g., "Strategic planning for a new product")
3. Wait for the response to appear
4. Verify that a new mindsheet is created with a mindmap
5. Check the console for any React Hooks violations

**Expected Result:**
- A new mindsheet should be created with a mindmap
- The mindmap should be properly initialized with a root node
- No React Hooks violations should appear in the console

### Test Case 3: Multiple Mindmaps

1. Create a mindmap using the manual workflow
2. Create another mindmap using the automatic workflow
3. Switch between the mindsheets
4. Verify that each mindsheet maintains its own state
5. Check the console for any React Hooks violations

**Expected Result:**
- Each mindsheet should maintain its own state
- Switching between mindsheets should not cause any React Hooks violations
- The content of each mindmap should be preserved when switching

## Verification Checklist

For each test case, verify the following:

- [ ] No React Hooks violations in the console
- [ ] Mindmap is properly initialized
- [ ] Mindsheet is created and activated
- [ ] Mindmap state is preserved when switching between sheets
- [ ] No duplicate initialization messages in the console

## Troubleshooting

If any issues are encountered during testing, check the following:

1. Verify that the MindSheet component is using hooks consistently
2. Verify that the MindMapAdapter is using the sheet-specific store
3. Verify that the createMindmapFromResponse function is using the sheet-specific store
4. Check for any conditional hook calls in the MindSheet component
