/**
 * MessageInput.tsx
 *
 * Component for inputting messages in the governance chat.
 */

import React, { KeyboardEvent } from 'react';
import './MessageInput.css';

interface MessageInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: () => void;
  disabled?: boolean;
  messages?: any[]; // For debug purposes
}

const MessageInput: React.FC<MessageInputProps> = ({
  value,
  onChange,
  onSubmit,
  disabled = false,
  messages = []
}) => {
  // Handle key press
  const handleKeyPress = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSubmit();
    }
  };

  // Debug function to log messages and MBCP data
  const handleDebug = () => {
    console.log('Debug: Current messages:', messages);

    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      console.log('Debug: Last message:', lastMessage);

      if (lastMessage?.mbcpData) {
        console.log('Debug: MBCP data:', JSON.stringify(lastMessage.mbcpData, null, 2));
      } else {
        console.log('Debug: No MBCP data in last message');
      }
    } else {
      console.log('Debug: No messages');
    }
  };

  return (
    <div className="message-input-container">
      <input
        type="text"
        className="message-input"
        placeholder="Type your message..."
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onKeyPress={handleKeyPress}
        disabled={disabled}
      />
      <button
        className="send-button"
        onClick={onSubmit}
        disabled={disabled || !value.trim()}
      >
        Send
      </button>

      {/* Debug button - only visible in development */}
      {process.env.NODE_ENV !== 'production' && (
        <button
          className="debug-button"
          onClick={handleDebug}
          type="button"
        >
          Debug
        </button>
      )}
    </div>
  );
};

export default MessageInput;
