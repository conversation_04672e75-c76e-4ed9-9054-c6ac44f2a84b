import { useCallback, useRef, useEffect } from 'react';
import { Node } from '../../../core/models/Node';
import { Connection } from '../../../core/models/Connection';
import { CanvasRenderingContext } from '../types/canvas.types';

export const useCanvasRendering = (
  canvasRef: React.RefObject<HTMLCanvasElement>,
  nodes: Node[],
  connections: Connection[],
  zoom: number,
  position: { x: number; y: number },
  selectedId: string | null,
  draggedNodeId: string | null
) => {
  const requestRef = useRef<number>();

  const drawNode = useCallback((
    ctx: CanvasRenderingContext2D,
    node: Node,
    isSelected: boolean
  ) => {
    const x = (node.x + position.x) * zoom;
    const y = (node.y + position.y) * zoom;
    const width = 100 * zoom;
    const height = 50 * zoom;

    // Draw node background
    ctx.fillStyle = isSelected ? '#e2e8f0' : '#f8fafc';
    ctx.strokeStyle = isSelected ? '#64748b' : '#94a3b8';
    ctx.lineWidth = isSelected ? 2 : 1;
    
    ctx.beginPath();
    ctx.roundRect(x - width/2, y - height/2, width, height, 8);
    ctx.fill();
    ctx.stroke();

    // Draw node text
    ctx.fillStyle = '#1e293b';
    ctx.font = `${12 * zoom}px system-ui`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(node.text, x, y);
  }, [zoom, position]);

  const drawConnection = useCallback((
    ctx: CanvasRenderingContext2D,
    connection: Connection
  ) => {
    const fromNode = nodes.find(n => n.id === connection.from);
    const toNode = nodes.find(n => n.id === connection.to);
    
    if (!fromNode || !toNode) return;

    const fromX = (fromNode.x + position.x) * zoom;
    const fromY = (fromNode.y + position.y) * zoom;
    const toX = (toNode.x + position.x) * zoom;
    const toY = (toNode.y + position.y) * zoom;

    ctx.beginPath();
    ctx.moveTo(fromX, fromY);
    ctx.lineTo(toX, toY);
    ctx.strokeStyle = '#cbd5e1';
    ctx.lineWidth = 1;
    ctx.stroke();
  }, [nodes, zoom, position]);

  const render = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw connections
    connections.forEach(connection => drawConnection(ctx, connection));

    // Draw nodes
    nodes.forEach(node => {
      const isSelected = node.id === selectedId;
      drawNode(ctx, node, isSelected);
    });

    requestRef.current = requestAnimationFrame(render);
  }, [canvasRef, nodes, connections, drawNode, drawConnection, selectedId]);

  useEffect(() => {
    requestRef.current = requestAnimationFrame(render);
    return () => {
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, [render]);

  return { render };
}; 