import create from 'zustand';
import { ChatResponse } from '../../../../services/api/GovernanceLLM';
import { useMindMapStore } from './MindMapStore';

// Helper function to generate unique IDs for nodes
const generateId = () => `${Date.now()}-${Math.floor(Math.random() * 10000)}`;

interface ChatForkState {
  // State
  content: ChatResponse | null;
  isVisible: boolean;
  isMinimized: boolean;
  selectedText: string | null;
  selectionContext: string | null;
  position: { x: number, y: number };

  // Actions
  showChatFork: (content: ChatResponse) => void;
  hideChatFork: () => void;
  minimizeChatFork: () => void;
  maximizeChatFork: () => void;
  toggleMinimized: () => void;
  setPosition: (x: number, y: number) => void;
  setSelectedText: (text: string | null, context: string | null) => void;
  clearSelection: () => void;
  createForkFromSelection: () => string | null;

  // Debug helpers
  _debugState: () => void;
}

export const useChatForkStore = create<ChatForkState>((set, get) => ({
  // Initial state
  content: null,
  isVisible: false,
  isMinimized: false,
  selectedText: null,
  selectionContext: null,
  position: { x: window.innerWidth - 420, y: 80 },

  // Actions
  showChatFork: (content) => {
    console.log('ChatForkStore: Showing ChatFork with content:', content);
    set({
      content,
      isVisible: true,
      // Reset selection when showing new content
      selectedText: null,
      selectionContext: null
    });

    // Debug log after state update
    setTimeout(() => get()._debugState(), 100);
  },

  hideChatFork: () => {
    console.log('ChatForkStore: Hiding ChatFork');
    set({
      isVisible: false,
      isMinimized: false,
      // Optionally clear content when hiding
      // content: null
    });
  },

  minimizeChatFork: () => {
    console.log('ChatForkStore: Minimizing ChatFork');
    set({ isMinimized: true });
  },

  maximizeChatFork: () => {
    console.log('ChatForkStore: Maximizing ChatFork');
    set({ isMinimized: false });
  },

  toggleMinimized: () => {
    const { isMinimized } = get();
    console.log(`ChatForkStore: Toggling minimized state from ${isMinimized} to ${!isMinimized}`);
    set({ isMinimized: !isMinimized });
  },

  setPosition: (x, y) => {
    set({ position: { x, y } });
  },

  setSelectedText: (text, context) => {
    console.log('ChatForkStore: Setting selected text:', { text, context });
    set({ selectedText: text, selectionContext: context });

    // Debug log after state update
    setTimeout(() => get()._debugState(), 100);
  },

  clearSelection: () => {
    console.log('ChatForkStore: Clearing selection');
    set({ selectedText: null, selectionContext: null });
  },

  createForkFromSelection: () => {
    const { selectedText, selectionContext, content } = get();

    if (!selectedText || !content) {
      console.warn('ChatForkStore: Cannot create fork, missing selection or content');
      return null;
    }

    // Get the MindMap store to create nodes
    const mindMapStore = useMindMapStore.getState();

    // Create parent node for the fork if needed
    let parentId = '';
    const rootId = mindMapStore.rootNodeId;

    if (!rootId) {
      console.error('ChatForkStore: Cannot create fork, no root node exists');
      return null;
    }

    // Use the root node as the parent for now
    parentId = rootId;

    // Create a title from the selection (limited to 50 chars)
    const title = selectedText.length > 50
      ? selectedText.substring(0, 47) + '...'
      : selectedText;

    // Create the node with the selection as description
    const newNodeId = mindMapStore.addChildNode(parentId, {
      text: title,
      description: selectedText,
      metadata: {
        source: 'chatfork',
        context: selectionContext || 'No context',
        originalText: content.text
      }
    });

    console.log('ChatForkStore: Created fork node:', {
      id: newNodeId,
      text: title,
      parentId
    });

    // Clear the selection after forking
    get().clearSelection();

    return newNodeId;
  },

  // Debug helper to log current state
  _debugState: () => {
    const state = get();
    console.log('ChatForkStore - Current State:', {
      isVisible: state.isVisible,
      hasContent: !!state.content,
      contentTitle: state.content?.text,
      selectedText: state.selectedText
    });
  }
}));