import React from 'react';

interface FooterContextButtonProps {
  isOpen: boolean;
  onClick: () => void;
}

const FooterContextButton: React.FC<FooterContextButtonProps> = ({ isOpen, onClick }) => {
  return (
    <button
      onClick={onClick}
      title="Context Settings"
      style={{
        background: 'none',
        border: 'none',
        color: '#ffffff',
        fontSize: '16px',
        cursor: 'pointer',
        padding: '4px 8px',
        display: 'flex',
        alignItems: 'center',
        transition: 'opacity 0.2s'
      }}
    >
      {isOpen ? '<' : '>'}
    </button>
  );
};

export default FooterContextButton;
