import React, { useState, useEffect } from 'react';
import ChatFork from './index.tsx';
import { ChatResponse, ChatService } from '../../services/api/GovernanceLLM';
import './ChatFork.css';

interface ChatForkContainerProps {
  initialQuestion?: string;
  isOpen?: boolean;
  onClose: () => void;
  existingResponse?: ChatResponse;
  content?: ChatResponse | null;
}

const ChatForkContainer: React.FC<ChatForkContainerProps> = ({
  initialQuestion = '',
  isOpen = true,
  onClose,
  existingResponse,
  content
}) => {
  console.log('ChatForkContainer rendered with:', {
    isOpen,
    hasExistingResponse: !!existingResponse,
    hasContent: !!content,
    responseType: (existingResponse || content)?.responseType,
    templateOutput: (existingResponse || content)?.templateOutput ? 'Present' : 'Missing'
  });

  // Add detailed logging for template output structure
  const responseToUse = content || existingResponse;
  if (responseToUse?.templateOutput) {
    console.log('ChatFork templateOutput structure:', {
      hasChatfork: !!responseToUse.templateOutput.chatfork,
      hasRoot: !!(responseToUse.templateOutput.root ||
                 (responseToUse.templateOutput.chatfork &&
                  responseToUse.templateOutput.chatfork.root)),
      rootObject: responseToUse.templateOutput.root ||
                 (responseToUse.templateOutput.chatfork &&
                  responseToUse.templateOutput.chatfork.root) ||
                 'Not found',
      dataFormat: responseToUse.templateOutput.chatfork ?
                 'Nested MBCP' :
                 (responseToUse.templateOutput.root ?
                  'Direct MBCP' :
                  (responseToUse.templateOutput.sections ?
                   'Legacy format' : 'Unknown format'))
    });
  }

  const [chatResponse, setChatResponse] = useState<ChatResponse | null>(content || existingResponse || null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showChatFork, setShowChatFork] = useState(!!existingResponse || !!content);
  const [governanceBoxHeight, setGovernanceBoxHeight] = useState('300px');

  // Check if the response should use ChatFork visualization
  const shouldUseChatFork = (response: ChatResponse | null) => {
    if (!response) return false;

    // Check responseType field
    if (response.responseType) {
      // If requiresChatFork is explicitly set to false, don't use ChatFork
      if (response.responseType.requiresChatFork === false) {
        console.log('ChatFork explicitly disabled by responseType.requiresChatFork === false');
        return false;
      }

      // If type is factual, don't use ChatFork
      if (response.responseType.type === 'factual') {
        console.log('ChatFork disabled for factual response');
        return false;
      }
    }

    // Check intent field
    if (response.intent === 'factual') {
      console.log('ChatFork disabled for factual intent');
      return false;
    }

    // Default to using ChatFork
    return true;
  };

  // Fetch content when initial question is provided and no existing response or content
  useEffect(() => {
    if (content) {
      // If content prop is provided, use it directly
      console.log('Using provided content for ChatFork:', content);
      setChatResponse(content);

      // Only show ChatFork if appropriate for this response type
      const useChatFork = shouldUseChatFork(content);
      setShowChatFork(useChatFork);
      setGovernanceBoxHeight(useChatFork ? '100px' : '300px');
    } else if (initialQuestion && isOpen && !existingResponse) {
      console.log('Fetching content for initial question:', initialQuestion);
      setIsLoading(true);
      setError(null);

      const chatService = ChatService.getInstance();

      chatService.sendMessage(initialQuestion, true)
        .then(response => {
          console.log('Received response for ChatFork:', response);
          setChatResponse(response);
          setIsLoading(false);

          // Check if we should use ChatFork for this response
          const useChatFork = shouldUseChatFork(response);

          // After receiving response, show the ChatFork if appropriate
          setTimeout(() => {
            setShowChatFork(useChatFork);
            setGovernanceBoxHeight(useChatFork ? '100px' : '300px');
          }, 500);
        })
        .catch(err => {
          console.error('Error fetching ChatFork data:', err);
          setError(`Error: ${err.message}`);
          setIsLoading(false);
        });
    } else if (existingResponse) {
      // If we already have a response, check if we should show the ChatFork
      console.log('Using existing response for ChatFork:', existingResponse);
      setChatResponse(existingResponse);

      // Only show ChatFork if appropriate for this response type
      const useChatFork = shouldUseChatFork(existingResponse);
      setShowChatFork(useChatFork);
      setGovernanceBoxHeight(useChatFork ? '100px' : '300px');
    }
  }, [initialQuestion, isOpen, existingResponse, content]);

  const handleNodeClick = (nodeId: string) => {
    console.log(`Node clicked: ${nodeId}`);
    // You can expand this to handle specific node click behavior
  };

  const handleNodeFork = (nodeId: string, parentId: string, type: 'question' | 'discussion') => {
    console.log(`Creating fork from node ${parentId} to ${nodeId} of type ${type}`);

    // Here you would typically send a request to generate content for the new fork
    const chatService = ChatService.getInstance();

    // Get the parent node's content to provide context
    const parentNode = chatResponse?.templateOutput?.sections?.find(s => s.id === parentId);

    if (parentNode) {
      const context = {
        parentContent: parentNode.content,
        forkType: type,
        nodeId: nodeId,
        parentId: parentId
      };

      console.log('Fork context:', context);
      // Future implementation: Send context to backend for processing
    }
  };

  // DEPRECATED: This is a duplicate of the function defined above
  // It's kept here temporarily for backward compatibility
  // TODO: Remove this duplicate function and update all references to use the one defined above
  // This is part of the MainRouter implementation plan (see docs/frontend_MainRouter_implementation.md)
  const shouldUseChatFork2 = (response: ChatResponse | null) => {
    if (!response) return false;

    // Check responseType field
    if (response.responseType) {
      // If requiresChatFork is explicitly set to false, don't use ChatFork
      if (response.responseType.requiresChatFork === false) {
        console.log('ChatFork explicitly disabled by responseType.requiresChatFork === false');
        return false;
      }

      // If type is factual, don't use ChatFork
      if (response.responseType.type === 'factual') {
        console.log('ChatFork disabled for factual response');
        return false;
      }
    }

    // Check intent field
    if (response.intent === 'factual') {
      console.log('ChatFork disabled for factual intent');
      return false;
    }

    // Default to using ChatFork if it has valid data
    return true;
  };

  // Check if we have valid template output for ChatFork
  const hasChatForkData = chatResponse && chatResponse.templateOutput && (
    // Check for MBCP format
    (chatResponse.templateOutput.root && chatResponse.templateOutput.root.children) ||
    // Check for MBCP with chatfork wrapper
    (chatResponse.templateOutput.chatfork && chatResponse.templateOutput.chatfork.root) ||
    // Check for legacy format
    chatResponse.templateOutput.sections ||
    (chatResponse.templateOutput.content && chatResponse.templateOutput.content.sections)
  );

  // Determine if we should show the ChatFork visualization
  // This combines both the data check and the intent/type check
  // This is part of the MainRouter implementation plan (see docs/frontend_MainRouter_implementation.md)
  const shouldShowChatFork = hasChatForkData && shouldUseChatFork(chatResponse);

  // Create a fallback structure if needed
  const createFallbackStructure = () => {
    if (!chatResponse) return null;

    console.log('Creating fallback structure for ChatFork');

    try {
      // If templateOutput exists but structure doesn't match expectations
      if (chatResponse.templateOutput) {
        // Basic MBCP structure
        const fallbackMBCP = {
          chatfork: {
            root: {
              id: "root",
              text: chatResponse.text.split('\n')[0] || "Topic", // Use first line as title
              description: chatResponse.text,
              intent: "exploratory",
              agent: "white_hat",
              children: []
            }
          }
        };

        // Set the fallback structure
        setChatResponse({
          ...chatResponse,
          templateOutput: fallbackMBCP
        });

        return fallbackMBCP;
      }
    } catch (error) {
      console.error('Error creating fallback structure:', error);
    }

    return null;
  };

  // Attempt to create fallback if needed
  // Only create fallback for non-factual responses that should use ChatFork
  useEffect(() => {
    if (chatResponse && !hasChatForkData && showChatFork && shouldUseChatFork(chatResponse)) {
      console.log('Creating fallback structure for non-factual response');
      createFallbackStructure();
    }
  }, [chatResponse, hasChatForkData, showChatFork]);

  // Update showChatFork state based on response type
  // This is part of the MainRouter implementation plan (see docs/frontend_MainRouter_implementation.md)
  useEffect(() => {
    if (chatResponse) {
      const useChatFork = shouldUseChatFork(chatResponse);
      console.log(`Response type check: shouldUseChatFork = ${useChatFork}`);

      if (!useChatFork && showChatFork) {
        // If this is a factual response but ChatFork is showing, update the state
        console.log('Factual response detected, disabling ChatFork visualization');
        setShowChatFork(false);
        setGovernanceBoxHeight('300px'); // Expand governance box for factual responses
      }
    }
  }, [chatResponse, showChatFork]);

  return (
    <div className={`chat-fork-container-wrapper ${isOpen ? 'open' : 'closed'}`}>
      {/* Governance Agent Box */}
      <div
        className="governance-agent-box"
        style={{
          height: governanceBoxHeight,
          transition: 'height 0.5s ease'
        }}
      >
        <div className="governance-header">
          <h3>Mindback Governance Agent</h3>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="governance-content">
          {isLoading ? (
            <div className="loading">Processing your question...</div>
          ) : error ? (
            <div className="error">{error}</div>
          ) : chatResponse ? (
            <div className="response">
              {!showChatFork && (
                <>
                  <p>{chatResponse.text}</p>
                  {shouldUseChatFork(chatResponse) ? (
                    <p className="transition-message">
                      Creating interactive ChatFork visualization...
                    </p>
                  ) : (
                    <p className="factual-message">
                      {/* No transition message for factual responses */}
                    </p>
                  )}
                </>
              )}
            </div>
          ) : (
            <div className="initial-state">
              <p>Hello! I'm your AI assistant. How can I help you today?</p>
            </div>
          )}
        </div>
      </div>

      {/* ChatFork Visualization - only show for non-factual responses */}
      {/* This is part of the MainRouter implementation plan (see docs/frontend_MainRouter_implementation.md) */}
      {showChatFork && shouldShowChatFork && (
        <div className="chat-fork-visualization">
          <ChatFork
            chatResponse={chatResponse}
            isVisible={showChatFork}
            onNodeClick={handleNodeClick}
            onNodeFork={handleNodeFork}
          />
        </div>
      )}

      {/* Error message when ChatFork data is missing but should be shown */}
      {showChatFork && !shouldShowChatFork && !shouldUseChatFork(chatResponse) && chatResponse && (
        <div className="chat-fork-info">
          <p>This is a factual response and does not require a ChatFork visualization.</p>
        </div>
      )}

      {/* Error message when ChatFork data is invalid */}
      {showChatFork && !hasChatForkData && shouldUseChatFork(chatResponse) && chatResponse && (
        <div className="chat-fork-error">
          <p>Could not display ChatFork visualization: Missing or invalid template data.</p>
          <pre>{JSON.stringify(chatResponse.templateOutput || {}, null, 2)}</pre>
        </div>
      )}
    </div>
  );
};

export default ChatForkContainer;