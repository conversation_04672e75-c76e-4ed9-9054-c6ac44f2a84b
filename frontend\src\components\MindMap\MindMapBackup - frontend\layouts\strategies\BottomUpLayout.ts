import { Connection } from '../../core/models/Connection';
import { Node } from '../../core/models/Node';
import { DEFAULT_LAYOUT_CONFIG, LayoutConfig, LayoutStrategy, LayoutStrategyType } from '../types';
import { adjustOverlappingNodes, buildNodeLevels } from '../utils';

/**
 * Bottom-up layout strategy for hierarchical trees
 * Places nodes in a bottom-up tree structure with proper parent-child relationships
 */
export class BottomUpLayout implements LayoutStrategy {
  readonly name: LayoutStrategyType = 'bottomUp';

  calculateLayout(
    nodes: Record<string, Node>,
    connections: Connection[],
    rootId: string,
    config: LayoutConfig = DEFAULT_LAYOUT_CONFIG
  ): Record<string, Node> {
    if (!nodes[rootId]) {
      console.warn('Root node not found:', rootId);
      return nodes;
    }

    // Build levels through BFS
    const levels = buildNodeLevels(nodes, connections, rootId);
    
    // Copy nodes to avoid mutating the original
    const updatedNodes = { ...nodes };
    
    // Reverse the level order to build from bottom up
    const maxLevel = levels.length - 1;
    
    // Calculate positions level by level, but from bottom up
    levels.forEach((level, levelIndex) => {
      const levelNodes = level.nodes;
      const totalWidth = levelNodes.length * (config.nodeWidth + config.horizontalSpacing);
      const startX = -totalWidth / 2;
      
      // Calculate Y position from bottom up (highest level index = closest to bottom)
      // For bottom-up layout, we invert the Y axis
      const y = -(maxLevel - levelIndex) * config.levelSpacing; 
      
      // Position nodes in this level
      levelNodes.forEach((nodeId, nodeIndex) => {
        const node = updatedNodes[nodeId];
        if (!node) return;
        
        // Calculate base position
        const x = startX + nodeIndex * (config.nodeWidth + config.horizontalSpacing);
        
        // Update node position
        updatedNodes[nodeId] = {
          ...node,
          x,
          y
        };
      });
    });

    // Adjust for overlapping nodes while maintaining the hierarchy
    return adjustOverlappingNodes(updatedNodes, connections, config, true);
  }
} 