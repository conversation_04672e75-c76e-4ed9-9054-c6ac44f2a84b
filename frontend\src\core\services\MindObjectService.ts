/**
 * MindObjectService.ts
 *
 * Service layer for MindObject operations.
 * This service provides methods for interacting with objects within sheets,
 * such as nodes and connections in mindmaps.
 *
 * The service layer pattern helps break circular dependencies by providing
 * a single point of access for operations without direct imports.
 */

import { v4 as uuidv4 } from 'uuid';
import RegistrationManager, { EventType } from './RegistrationManager';
import { mindSheetService } from './MindSheetService';
import { applicationService } from './ApplicationService';

// Define object types
export enum MindObjectType {
  NODE = 'node',
  CONNECTION = 'connection',
  ANNOTATION = 'annotation',
  ATTACHMENT = 'attachment'
}

// Define node types
export enum NodeType {
  STANDARD = 'standard',
  DECISION = 'decision',
  ACTION = 'action',
  QUESTION = 'question',
  IDEA = 'idea',
  CONCEPT = 'concept',
  CUSTOM = 'custom'
}

// Define connection types
export enum ConnectionType {
  STANDARD = 'standard',
  CAUSAL = 'causal',
  SEQUENTIAL = 'sequential',
  HIERARCHICAL = 'hierarchical',
  ASSOCIATIVE = 'associative',
  CUSTOM = 'custom'
}

// Define node metadata
export interface NodeMetadata {
  type?: NodeType;
  tags?: string[];
  agent?: {
    id: string;
    name: string;
    role: string;
  };
  action?: {
    type: string;
    status: 'pending' | 'in_progress' | 'completed' | 'failed';
    data: any;
  };
  custom?: Record<string, any>;
}

// Define connection metadata
export interface ConnectionMetadata {
  type?: ConnectionType;
  label?: string;
  weight?: number;
  bidirectional?: boolean;
  custom?: Record<string, any>;
}

// Define node creation options
export interface NodeCreationOptions {
  text: string;
  parentId?: string;
  position?: { x: number, y: number };
  metadata?: NodeMetadata;
  index?: number;
}

// Define connection creation options
export interface ConnectionCreationOptions {
  fromId: string;
  toId: string;
  metadata?: ConnectionMetadata;
  style?: {
    color?: string;
    width?: number;
    style?: 'solid' | 'dashed' | 'dotted';
  };
}

/**
 * MindObjectService class
 *
 * Singleton service for MindObject operations.
 */
class MindObjectService {
  private static instance: MindObjectService;

  private constructor() {
    // Private constructor to prevent direct instantiation
    console.log('MindObjectService: Initialized');
  }

  /**
   * Get the singleton instance of the MindObjectService
   */
  public static getInstance(): MindObjectService {
    if (!MindObjectService.instance) {
      MindObjectService.instance = new MindObjectService();
    }
    return MindObjectService.instance;
  }

  /**
   * Create a node in a sheet
   */
  public createNode(sheetId: string, options: NodeCreationOptions): string | null {
    try {
      // Get the sheet-specific store
      const store = mindSheetService.getMindMapSheetStore(sheetId);
      if (!store) {
        console.error('MindObjectService: Cannot create node in non-existent sheet:', sheetId);
        return null;
      }

      // Get the store state
      const storeState = store.getState();

      // Generate a unique ID if not provided
      const nodeId = uuidv4();

      // Create the node
      const node = {
        id: nodeId,
        text: options.text,
        parent: options.parentId || null,
        position: options.position || { x: 0, y: 0 },
        metadata: options.metadata || {},
        index: options.index !== undefined ? options.index : Object.keys(storeState.nodes).length
      };

      // Add the node to the store
      storeState.addNode(node);

      // Log the node creation
      RegistrationManager.registerEvent(EventType.NODE_CREATED, {
        id: nodeId,
        sheetId,
        text: options.text,
        parentId: options.parentId
      });

      console.log('MindObjectService: Node created', nodeId, options.text);

      return nodeId;
    } catch (error) {
      console.error('MindObjectService: Error creating node:', error);

      // Log the error
      RegistrationManager.registerEvent(EventType.ERROR_OCCURRED, {
        component: 'MindObjectService',
        method: 'createNode',
        message: error.message,
        stack: error.stack
      });

      return null;
    }
  }

  /**
   * Create a child node
   */
  public createChildNode(sheetId: string, parentId: string, text: string, metadata?: NodeMetadata): string | null {
    return this.createNode(sheetId, {
      text,
      parentId,
      metadata
    });
  }

  /**
   * Create a sibling node
   */
  public createSiblingNode(sheetId: string, siblingId: string, text: string, metadata?: NodeMetadata): string | null {
    try {
      // Get the sheet-specific store
      const store = mindSheetService.getMindMapSheetStore(sheetId);
      if (!store) {
        console.error('MindObjectService: Cannot create sibling node in non-existent sheet:', sheetId);
        return null;
      }

      // Get the store state
      const storeState = store.getState();

      // Get the sibling node
      const siblingNode = storeState.nodes[siblingId];
      if (!siblingNode) {
        console.error('MindObjectService: Cannot create sibling for non-existent node:', siblingId);
        return null;
      }

      // Create a node with the same parent
      return this.createNode(sheetId, {
        text,
        parentId: siblingNode.parent,
        metadata
      });
    } catch (error) {
      console.error('MindObjectService: Error creating sibling node:', error);

      // Log the error
      RegistrationManager.registerEvent(EventType.ERROR_OCCURRED, {
        component: 'MindObjectService',
        method: 'createSiblingNode',
        message: error.message,
        stack: error.stack
      });

      return null;
    }
  }

  /**
   * Update a node
   */
  public updateNode(sheetId: string, nodeId: string, updates: Partial<NodeCreationOptions>): boolean {
    try {
      // Get the sheet-specific store
      const store = mindSheetService.getMindMapSheetStore(sheetId);
      if (!store) {
        console.error('MindObjectService: Cannot update node in non-existent sheet:', sheetId);
        return false;
      }

      // Get the store state
      const storeState = store.getState();

      // Get the node
      const node = storeState.nodes[nodeId];
      if (!node) {
        console.error('MindObjectService: Cannot update non-existent node:', nodeId);
        return false;
      }

      // Create the updated node
      const updatedNode = {
        ...node,
        text: updates.text !== undefined ? updates.text : node.text,
        parent: updates.parentId !== undefined ? updates.parentId : node.parent,
        position: updates.position !== undefined ? updates.position : node.position,
        metadata: updates.metadata !== undefined ? { ...node.metadata, ...updates.metadata } : node.metadata
      };

      // Update the node in the store
      storeState.updateNode(nodeId, updatedNode);

      // Log the node update
      RegistrationManager.registerEvent(EventType.NODE_EDITED, {
        id: nodeId,
        sheetId,
        updates
      });

      console.log('MindObjectService: Node updated', nodeId);

      return true;
    } catch (error) {
      console.error('MindObjectService: Error updating node:', error);

      // Log the error
      RegistrationManager.registerEvent(EventType.ERROR_OCCURRED, {
        component: 'MindObjectService',
        method: 'updateNode',
        message: error.message,
        stack: error.stack
      });

      return false;
    }
  }

  /**
   * Delete a node
   */
  public deleteNode(sheetId: string, nodeId: string): boolean {
    try {
      // Get the sheet-specific store
      const store = mindSheetService.getMindMapSheetStore(sheetId);
      if (!store) {
        console.error('MindObjectService: Cannot delete node in non-existent sheet:', sheetId);
        return false;
      }

      // Get the store state
      const storeState = store.getState();

      // Get the node
      const node = storeState.nodes[nodeId];
      if (!node) {
        console.error('MindObjectService: Cannot delete non-existent node:', nodeId);
        return false;
      }

      // Delete the node from the store
      storeState.deleteNode(nodeId);

      // Log the node deletion
      RegistrationManager.registerEvent(EventType.NODE_DELETED, {
        id: nodeId,
        sheetId
      });

      console.log('MindObjectService: Node deleted', nodeId);

      return true;
    } catch (error) {
      console.error('MindObjectService: Error deleting node:', error);

      // Log the error
      RegistrationManager.registerEvent(EventType.ERROR_OCCURRED, {
        component: 'MindObjectService',
        method: 'deleteNode',
        message: error.message,
        stack: error.stack
      });

      return false;
    }
  }

  /**
   * Get a node
   */
  public getNode(sheetId: string, nodeId: string): any {
    try {
      // Get the sheet-specific store
      const store = mindSheetService.getMindMapSheetStore(sheetId);
      if (!store) {
        console.error('MindObjectService: Cannot get node from non-existent sheet:', sheetId);
        return null;
      }

      // Get the store state
      const storeState = store.getState();

      // Get the node
      const node = storeState.nodes[nodeId];
      if (!node) {
        console.error('MindObjectService: Cannot get non-existent node:', nodeId);
        return null;
      }

      return node;
    } catch (error) {
      console.error('MindObjectService: Error getting node:', error);

      // Log the error
      RegistrationManager.registerEvent(EventType.ERROR_OCCURRED, {
        component: 'MindObjectService',
        method: 'getNode',
        message: error.message,
        stack: error.stack
      });

      return null;
    }
  }

  /**
   * Select a node
   */
  public selectNode(sheetId: string, nodeId: string): boolean {
    try {
      // Get the sheet-specific store
      const store = mindSheetService.getMindMapSheetStore(sheetId);
      if (!store) {
        console.error('MindObjectService: Cannot select node in non-existent sheet:', sheetId);
        return false;
      }

      // Get the store state
      const storeState = store.getState();

      // Get the node
      const node = storeState.nodes[nodeId];
      if (!node) {
        console.error('MindObjectService: Cannot select non-existent node:', nodeId);
        return false;
      }

      // Select the node in the store
      storeState.selectNode(nodeId);

      // Log the node selection
      RegistrationManager.registerEvent(EventType.NODE_SELECTED, {
        id: nodeId,
        sheetId
      });

      console.log('MindObjectService: Node selected', nodeId);

      return true;
    } catch (error) {
      console.error('MindObjectService: Error selecting node:', error);

      // Log the error
      RegistrationManager.registerEvent(EventType.ERROR_OCCURRED, {
        component: 'MindObjectService',
        method: 'selectNode',
        message: error.message,
        stack: error.stack
      });

      return false;
    }
  }

  /**
   * Create a connection between nodes
   */
  public createConnection(sheetId: string, options: ConnectionCreationOptions): string | null {
    try {
      // Get the sheet-specific store
      const store = mindSheetService.getMindMapSheetStore(sheetId);
      if (!store) {
        console.error('MindObjectService: Cannot create connection in non-existent sheet:', sheetId);
        return null;
      }

      // Get the store state
      const storeState = store.getState();

      // Check if the nodes exist
      if (!storeState.nodes[options.fromId]) {
        console.error('MindObjectService: Cannot create connection from non-existent node:', options.fromId);
        return null;
      }

      if (!storeState.nodes[options.toId]) {
        console.error('MindObjectService: Cannot create connection to non-existent node:', options.toId);
        return null;
      }

      // Create the connection
      const connectionId = storeState.addConnection(
        options.fromId,
        options.toId,
        options.style || {}
      );

      // Add metadata if provided
      if (options.metadata) {
        // Store metadata in the connection
        // This requires extending the MindMapStore to support connection metadata
        // For now, we'll just log it
        console.log('MindObjectService: Connection metadata:', options.metadata);
      }

      // Log the connection creation
      RegistrationManager.registerEvent(EventType.CONNECTION_CREATED, {
        id: connectionId,
        sheetId,
        fromId: options.fromId,
        toId: options.toId
      });

      console.log('MindObjectService: Connection created', connectionId, options.fromId, options.toId);

      return connectionId;
    } catch (error) {
      console.error('MindObjectService: Error creating connection:', error);

      // Log the error
      RegistrationManager.registerEvent(EventType.ERROR_OCCURRED, {
        component: 'MindObjectService',
        method: 'createConnection',
        message: error.message,
        stack: error.stack
      });

      return null;
    }
  }

  /**
   * Delete a connection
   */
  public deleteConnection(sheetId: string, connectionId: string): boolean {
    try {
      // Get the sheet-specific store
      const store = mindSheetService.getMindMapSheetStore(sheetId);
      if (!store) {
        console.error('MindObjectService: Cannot delete connection in non-existent sheet:', sheetId);
        return false;
      }

      // Get the store state
      const storeState = store.getState();

      // Get the connection
      const connection = storeState.connections.find(conn => conn.id === connectionId);
      if (!connection) {
        console.error('MindObjectService: Cannot delete non-existent connection:', connectionId);
        return false;
      }

      // Delete the connection from the store
      storeState.deleteConnection(connectionId);

      // Log the connection deletion
      RegistrationManager.registerEvent(EventType.CONNECTION_DELETED, {
        id: connectionId,
        sheetId,
        fromId: connection.from,
        toId: connection.to
      });

      console.log('MindObjectService: Connection deleted', connectionId);

      return true;
    } catch (error) {
      console.error('MindObjectService: Error deleting connection:', error);

      // Log the error
      RegistrationManager.registerEvent(EventType.ERROR_OCCURRED, {
        component: 'MindObjectService',
        method: 'deleteConnection',
        message: error.message,
        stack: error.stack
      });

      return false;
    }
  }
}

// Export the singleton instance
export const mindObjectService = MindObjectService.getInstance();

// Export convenience functions
export const createNode = (sheetId: string, options: NodeCreationOptions) =>
  mindObjectService.createNode(sheetId, options);
export const createChildNode = (sheetId: string, parentId: string, text: string, metadata?: NodeMetadata) =>
  mindObjectService.createChildNode(sheetId, parentId, text, metadata);
export const createSiblingNode = (sheetId: string, siblingId: string, text: string, metadata?: NodeMetadata) =>
  mindObjectService.createSiblingNode(sheetId, siblingId, text, metadata);
export const updateNode = (sheetId: string, nodeId: string, updates: Partial<NodeCreationOptions>) =>
  mindObjectService.updateNode(sheetId, nodeId, updates);
export const deleteNode = (sheetId: string, nodeId: string) =>
  mindObjectService.deleteNode(sheetId, nodeId);
export const getNode = (sheetId: string, nodeId: string) =>
  mindObjectService.getNode(sheetId, nodeId);
export const selectNode = (sheetId: string, nodeId: string) =>
  mindObjectService.selectNode(sheetId, nodeId);
export const createConnection = (sheetId: string, options: ConnectionCreationOptions) =>
  mindObjectService.createConnection(sheetId, options);
export const deleteConnection = (sheetId: string, connectionId: string) =>
  mindObjectService.deleteConnection(sheetId, connectionId);
