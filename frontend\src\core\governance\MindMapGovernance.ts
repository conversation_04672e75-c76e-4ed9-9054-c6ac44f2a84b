/**
 * MindMapGovernance.ts
 * 
 * SINGLE SOURCE OF TRUTH for all mindmap UI/UX behavior
 * Consolidates all layout, positioning, and interaction rules
 */

export interface MindMapGovernanceConfig {
  // Layout Strategy Rules
  layout: {
    defaultStrategy: 'leftToRight' | 'topDown' | 'radial' | 'compactLeftToRight';
    allowUserOverride: boolean;
    preventAutoSwitching: boolean; // Stop automatic layout switching
    preserveUserChoice: boolean; // Remember user's layout preference per sheet
  };

  // Node Behavior Rules
  nodes: {
    // Dragging behavior
    dragging: {
      enabled: boolean;
      constrainToCanvas: boolean;
      preventWidthResize: boolean; // Fix: Prevent terrible width dragging
      snapToGrid: boolean;
      gridSize: number;
    };
    
    // Default node properties
    defaults: {
      width: number;
      height: number;
      color: string;
      borderColor: string;
      shape: 'rectangle' | 'ellipse' | 'diamond' | 'hexagon';
    };
    
    // Selection behavior
    selection: {
      multiSelectEnabled: boolean;
      clickToSelect: boolean;
      preventJumpOnSelect: boolean; // Fix: Prevent mindmap jumping when clicking
    };
  };

  // Positioning Rules
  positioning: {
    // Initial positioning when creating new mindmaps
    initial: {
      centerOnScreen: boolean;
      rootNodePosition: { x: number; y: number };
      useViewportCenter: boolean;
    };
    
    // Viewport behavior
    viewport: {
      defaultScale: number;
      minScale: number;
      maxScale: number;
      centerOnNodeSelect: boolean; // Fix: Control auto-centering
      preventJumpOnClick: boolean; // Fix: Stop viewport jumping on click
    };
    
    // Auto-layout behavior
    autoLayout: {
      triggerOnNodeAdd: boolean;
      triggerOnNodeMove: boolean;
      preserveManualPositions: boolean;
    };
  };

  // Manager Dialog Rules
  manager: {
    // Which manager should be active
    activeManager: 'enhanced' | 'legacy' | 'none';
    
    // Manager behavior
    behavior: {
      singleInstanceOnly: boolean; // Only one manager per sheet
      inheritSheetState: boolean; // Manager reflects active sheet state
      persistPosition: boolean;
    };
    
    // Manager controls
    controls: {
      enableLayoutSwitching: boolean;
      enableZoomControls: boolean;
      enablePositionReset: boolean;
      enableScaleReset: boolean;
    };
  };

  // Z-Index Management
  zIndex: {
    canvas: number;
    nodes: number;
    connections: number;
    manager: number;
    governance: number;
    nodeBox: number;
  };
}

// Default governance configuration - FIX ALL CURRENT ISSUES
export const DEFAULT_MINDMAP_GOVERNANCE: MindMapGovernanceConfig = {
  layout: {
    defaultStrategy: 'leftToRight', // Consistent default
    allowUserOverride: true,
    preventAutoSwitching: true, // FIX: Stop automatic layout jumping
    preserveUserChoice: true // Remember user preference
  },

  nodes: {
    dragging: {
      enabled: true,
      constrainToCanvas: true,
      preventWidthResize: true, // FIX: Prevent width dragging
      snapToGrid: false,
      gridSize: 20
    },
    
    defaults: {
      width: 180, // Consistent with current store
      height: 70,
      color: '#ffffff',
      borderColor: '#2c3e50',
      shape: 'rectangle'
    },
    
    selection: {
      multiSelectEnabled: false,
      clickToSelect: true,
      preventJumpOnSelect: true // FIX: No jumping when selecting nodes
    }
  },

  positioning: {
    initial: {
      centerOnScreen: true,
      rootNodePosition: { x: 0, y: 0 }, // Use mindmap coordinates
      useViewportCenter: true
    },
    
    viewport: {
      defaultScale: 1.0,
      minScale: 0.3,
      maxScale: 2.0,
      centerOnNodeSelect: false, // FIX: Don't auto-center on select
      preventJumpOnClick: true // FIX: No viewport jumping
    },
    
    autoLayout: {
      triggerOnNodeAdd: true,
      triggerOnNodeMove: false, // Don't re-layout on manual moves
      preserveManualPositions: true
    }
  },

  manager: {
    activeManager: 'enhanced',
    
    behavior: {
      singleInstanceOnly: true, // FIX: Only one manager per sheet
      inheritSheetState: true, // FIX: Manager reflects sheet state
      persistPosition: true
    },
    
    controls: {
      enableLayoutSwitching: true,
      enableZoomControls: true,
      enablePositionReset: true,
      enableScaleReset: true
    }
  },

  zIndex: {
    canvas: 100,
    nodes: 200,
    connections: 150,
    manager: 2000,
    governance: 3000,
    nodeBox: 2100
  }
};

/**
 * MindMap Governance Manager
 * Enforces all governance rules across the application
 */
export class MindMapGovernanceManager {
  private config: MindMapGovernanceConfig;
  private sheetPreferences: Map<string, Partial<MindMapGovernanceConfig>> = new Map();

  constructor(config: MindMapGovernanceConfig = DEFAULT_MINDMAP_GOVERNANCE) {
    this.config = { ...config };
  }

  /**
   * Get governance config for a specific sheet
   */
  getSheetConfig(sheetId: string): MindMapGovernanceConfig {
    const sheetPrefs = this.sheetPreferences.get(sheetId) || {};
    return this.mergeConfigs(this.config, sheetPrefs);
  }

  /**
   * Update preferences for a specific sheet
   */
  setSheetPreference(sheetId: string, preferences: Partial<MindMapGovernanceConfig>): void {
    this.sheetPreferences.set(sheetId, preferences);
  }

  /**
   * Validate and fix a layout strategy change
   */
  validateLayoutChange(sheetId: string, newStrategy: string): boolean {
    const config = this.getSheetConfig(sheetId);
    
    if (!config.layout.allowUserOverride) {
      console.warn('MindMapGovernance: Layout changes not allowed by governance');
      return false;
    }

    if (config.layout.preventAutoSwitching) {
      // Only allow intentional user changes, not automatic switches
      console.log('MindMapGovernance: Layout change validated for sheet:', sheetId);
      return true;
    }

    return true;
  }

  /**
   * Validate node dragging behavior
   */
  validateNodeDrag(sheetId: string, nodeId: string, newPosition: { x: number; y: number }): { x: number; y: number } {
    const config = this.getSheetConfig(sheetId);
    
    if (!config.nodes.dragging.enabled) {
      console.warn('MindMapGovernance: Node dragging disabled by governance');
      return newPosition; // Return original position
    }

    // Prevent width-only dragging by ensuring both x and y change reasonably
    if (config.nodes.dragging.preventWidthResize) {
      // This is handled at the component level by proper drag handlers
    }

    return newPosition;
  }

  /**
   * Validate viewport changes
   */
  validateViewportChange(sheetId: string, change: { position?: { x: number; y: number }; scale?: number }): boolean {
    const config = this.getSheetConfig(sheetId);
    
    if (change.scale !== undefined) {
      const { minScale, maxScale } = config.positioning.viewport;
      if (change.scale < minScale || change.scale > maxScale) {
        console.warn('MindMapGovernance: Scale change outside allowed range');
        return false;
      }
    }

    return true;
  }

  /**
   * Get the correct manager configuration for a sheet
   */
  getManagerConfig(sheetId: string): { show: boolean; type: string } {
    const config = this.getSheetConfig(sheetId);
    
    return {
      show: config.manager.activeManager !== 'none',
      type: config.manager.activeManager
    };
  }

  /**
   * Merge two configuration objects
   */
  private mergeConfigs(base: MindMapGovernanceConfig, override: Partial<MindMapGovernanceConfig>): MindMapGovernanceConfig {
    return {
      layout: { ...base.layout, ...override.layout },
      nodes: {
        dragging: { ...base.nodes.dragging, ...override.nodes?.dragging },
        defaults: { ...base.nodes.defaults, ...override.nodes?.defaults },
        selection: { ...base.nodes.selection, ...override.nodes?.selection }
      },
      positioning: {
        initial: { ...base.positioning.initial, ...override.positioning?.initial },
        viewport: { ...base.positioning.viewport, ...override.positioning?.viewport },
        autoLayout: { ...base.positioning.autoLayout, ...override.positioning?.autoLayout }
      },
      manager: {
        ...base.manager,
        behavior: { ...base.manager.behavior, ...override.manager?.behavior },
        controls: { ...base.manager.controls, ...override.manager?.controls }
      },
      zIndex: { ...base.zIndex, ...override.zIndex }
    };
  }
}

// Global governance manager instance
export const mindMapGovernance = new MindMapGovernanceManager();

// Export types for use in other files
export type LayoutStrategy = MindMapGovernanceConfig['layout']['defaultStrategy'];
export type NodeDefaults = MindMapGovernanceConfig['nodes']['defaults'];
export type PositioningRules = MindMapGovernanceConfig['positioning'];
export type ManagerRules = MindMapGovernanceConfig['manager']; 