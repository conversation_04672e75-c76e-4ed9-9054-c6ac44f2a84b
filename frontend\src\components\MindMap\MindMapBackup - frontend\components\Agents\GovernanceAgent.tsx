// Placeholder file for: frontend\src\components\MindMap\components\Agents\GovernanceAgent.tsx

import React, { useState } from 'react';
import { Dialog, DialogContent, DialogTitle, IconButton, TextField, Button, Switch, FormControlLabel } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import SendIcon from '@mui/icons-material/Send';
import SettingsIcon from '@mui/icons-material/Settings';
import '../../../ChatFork/ChatFork.css';
import ChatForkContainer from '../../../ChatFork/ChatForkContainer';
import { ChatService } from '../../../../services/api/GovernanceLLM';

interface GovernanceAgentProps {
  isOpen: boolean;
  onClose: () => void;
}

const GovernanceAgent: React.FC<GovernanceAgentProps> = ({ isOpen, onClose }) => {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<{text: string; sender: string; timestamp: Date}[]>([]);
  const [useCrewAI, setUseCrewAI] = useState(true);
  const [currentQuestion, setCurrentQuestion] = useState<string>('');
  const [showChatFork, setShowChatFork] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [model, setModel] = useState('GPT-3.5 Turbo');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [latestChatResponse, setLatestChatResponse] = useState<any>(null);
  const [showMindmapButton, setShowMindmapButton] = useState(false);

  const handleSendMessage = async () => {
    if (!message.trim()) return;
    
    // Add user message to history
    const newMessage = {
      text: message,
      sender: 'user',
      timestamp: new Date()
    };
    
    setMessages([...messages, newMessage]);
    
    // Save current question for ChatFork
    setCurrentQuestion(message);
    
    // Clear input field and set loading state
    setMessage('');
    setIsLoading(true);
    setError(null);
    
    try {
      // Send message to ChatService
      const chatService = ChatService.getInstance();
      const response = await chatService.sendMessage(message, useCrewAI);
      
      // Save the response for passing to ChatForkContainer
      setLatestChatResponse(response);
      
      // Handle response actions and types
      if (useCrewAI) {
        console.log('CrewAI response:', response);
        
        // Extract the actual template selection directly from the raw response
        let templateId = '';
        
        // Try to get template_id from different places in the response
        if (response.templateOutput && response.templateOutput.template_id) {
          templateId = response.templateOutput.template_id;
        } else if (response.text && response.text.includes('Selected template:')) {
          // Try to extract from the text if it's not in the output
          const match = response.text.match(/Selected template: (\w+)/);
          if (match) {
            templateId = match[1].toLowerCase();
          }
        }
        
        console.log('Detected template ID:', templateId);
        
        // Give priority to the explicit template selection over content-based detection
        const isChatForkTemplate =
          templateId === 'chatfork' ||
          response.responseType?.requiresChatFork;
        
        const isMindmapTemplate =
          templateId === 'mindmap' ||
          response.responseType?.requiresMindmap;
        
        if (isChatForkTemplate) {
          console.log('ChatFork template selected, showing ChatFork view');
          // Format a simplified message instead of showing raw JSON
          const assistantMessage = {
            text: "Interactive explanation prepared. Opening ChatFork view...",
            sender: 'assistant',
            timestamp: new Date()
          };
          
          setMessages(prev => [...prev, assistantMessage]);
          setShowChatFork(true);
          setShowMindmapButton(false); // Ensure the mindmap button is hidden
          setIsLoading(false);
          return; // Exit early to avoid showing both the raw JSON and ChatFork
        }
        
        // Set mindmap button visibility based on template selection
        setShowMindmapButton(isMindmapTemplate);
        
        // For non-ChatFork responses, add the response text to messages
        setMessages(prev => [
          ...prev, 
          {
            text: response.text,
            sender: 'assistant',
            timestamp: new Date()
          }
        ]);
      } else {
        // Use more sophisticated pattern matching for non-CrewAI responses
        const chatService = ChatService.getInstance();
        
        // Check if it's a simple factual query using our local helper method
        const isSimpleFactual = isSimpleFactualQuery(message);
        // Use a similar pattern logic but implemented locally
        const isComplexEducationalQuery = 
          !isSimpleFactual && 
          (message.toLowerCase().includes('what is') || 
           message.toLowerCase().includes('explain') || 
           message.toLowerCase().includes('how does') ||
           message.toLowerCase().includes('who is'));
        
        if (isComplexEducationalQuery) {
          // For complex educational queries, use ChatFork
          setShowChatFork(true);
          setShowMindmapButton(false);
          
          // Add a simplified message
          setMessages(prev => [
            ...prev, 
            {
              text: "Interactive explanation prepared. Opening ChatFork view...",
              sender: 'assistant',
              timestamp: new Date()
            }
          ]);
        } else {
          // For non-educational content, show the response as usual
          setShowMindmapButton(false);
          setMessages(prev => [
            ...prev, 
            {
              text: response.text,
              sender: 'assistant',
              timestamp: new Date()
            }
          ]);
        }
      }
      
      setIsLoading(false);
    } catch (err) {
      console.error('Error sending message:', err);
      setError(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTimestamp = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Helper to identify simple factual queries
  const isSimpleFactualQuery = (query: string): boolean => {
    // Check if the query is relatively short (likely a simple question)
    if (query.trim().split(/\s+/).length <= 10) {
      // Check if it matches simple factual patterns
      return !!query.match(/^what is the|^who is the|^where is|^when was|^how many|^which|^is the/i);
    }
    return false;
  };

  return (
    <>
      {/* Main Dialog */}
      <Dialog 
        open={isOpen && !showChatFork} 
        onClose={onClose}
        fullWidth
        maxWidth="sm"
        className="governance-agent-dialog"
      >
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <span>mindback Governance Agent</span>
            <span style={{ fontSize: '0.8rem', marginLeft: '8px', color: '#666' }}>
              {formatTimestamp(new Date())}
            </span>
          </div>
          <div>
            <IconButton size="small" onClick={() => setShowSettings(!showSettings)}>
              <SettingsIcon />
            </IconButton>
            <IconButton size="small" onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </div>
        </DialogTitle>
        
        <DialogContent sx={{ height: '400px', display: 'flex', flexDirection: 'column' }}>
          {/* Settings Panel */}
          {showSettings && (
            <div className="settings-panel">
              <FormControlLabel
                control={
                  <Switch
                    checked={useCrewAI}
                    onChange={(e) => setUseCrewAI(e.target.checked)}
                  />
                }
                label="Use CrewAI"
              />
              <div className="model-selector">
                <label>Model:</label>
                <select 
                  value={model} 
                  onChange={(e) => setModel(e.target.value)}
                  disabled={!useCrewAI}
                >
                  <option value="GPT-3.5 Turbo">GPT-3.5 Turbo</option>
                  <option value="GPT-4">GPT-4</option>
                </select>
              </div>
            </div>
          )}
          
          {/* Message History */}
          <div className="message-history" style={{ flexGrow: 1, overflowY: 'auto', marginBottom: '10px' }}>
            {messages.length === 0 ? (
              <div className="welcome-message">
                <p>Hello! I'm your AI assistant. How can I help you today?</p>
              </div>
            ) : (
              messages.map((msg, index) => (
                <div 
                  key={index} 
                  className={`message ${msg.sender === 'user' ? 'user-message' : 'assistant-message'}`}
                >
                  <div className="message-sender">{msg.sender === 'user' ? 'You' : 'AI'}</div>
                  <div className="message-text">{msg.text}</div>
                  <div className="message-timestamp">{formatTimestamp(msg.timestamp)}</div>
                  {/* Show Build Mindmap button only for the last assistant message when appropriate */}
                  {msg.sender === 'assistant' && 
                   index === messages.length - 1 && 
                   showMindmapButton && 
                   !showChatFork && ( // Critical condition to ensure mutual exclusivity
                    <Button 
                      variant="contained" 
                      color="primary" 
                      className="build-mindmap-button"
                      size="small"
                      style={{ marginTop: '10px' }}
                      onClick={() => {
                        // Handle mindmap creation using latestChatResponse
                        console.log('Creating mindmap with data:', latestChatResponse);
                      }}
                    >
                      Build Mindmap
                    </Button>
                  )}
                </div>
              ))
            )}
            
            {/* Loading indicator */}
            {isLoading && (
              <div className="loading-indicator">
                <p>Processing your request...</p>
              </div>
            )}
            
            {/* Error message */}
            {error && (
              <div className="error-message">
                <p>{error}</p>
              </div>
            )}
          </div>
          
          {/* Input Area */}
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <TextField
              fullWidth
              placeholder="Type your message..."
              variant="outlined"
              size="small"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              sx={{ mr: 1 }}
            />
            <Button 
              variant="contained" 
              color="primary" 
              onClick={handleSendMessage}
              endIcon={<SendIcon />}
              disabled={!message.trim()}
            >
              Send
            </Button>
          </div>
          
          {/* CrewAI Status */}
          <div className="crew-ai-status">
            <FormControlLabel
              control={
                <Switch
                  checked={useCrewAI}
                  onChange={(e) => setUseCrewAI(e.target.checked)}
                  size="small"
                />
              }
              label={
                <span style={{ fontSize: '0.75rem' }}>
                  {useCrewAI ? 'Use CrewAI' : 'Live LLM'}
                </span>
              }
            />
          </div>
        </DialogContent>
      </Dialog>
      
      {/* ChatFork Container */}
      {showChatFork && (
        <ChatForkContainer
          initialQuestion={currentQuestion}
          isOpen={showChatFork}
          onClose={() => {
            setShowChatFork(false);
            setCurrentQuestion('');
          }}
          existingResponse={latestChatResponse}
        />
      )}
    </>
  );
};

export default GovernanceAgent;
