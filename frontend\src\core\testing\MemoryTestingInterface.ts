/**
 * MemoryTestingInterface.ts
 * 
 * Testing interface for MBCP-compatible memory system.
 * Implements Phase 1.1.5 of the Development Plan.
 */

import { ChatMemoryService, StructuredMessage, ConversationThread, MemoryBlock } from '../../services/ChatMemoryService';
import { collectMBCPSnapshot, exportMBCPSnapshot, createConversationThread, addMessageToThread } from '../services/MemorySnapshotService';
import { useMindBookStore } from '../state/MindBookStore';

interface TestResult {
  testName: string;
  passed: boolean;
  message: string;
  data?: any;
}

interface TestSuite {
  suiteName: string;
  tests: TestResult[];
  passed: boolean;
  summary: string;
}

export class MemoryTestingInterface {
  private static instance: MemoryTestingInterface;
  private chatMemoryService: ChatMemoryService;
  private testResults: TestSuite[] = [];

  private constructor() {
    this.chatMemoryService = ChatMemoryService.getInstance();
  }

  static getInstance(): MemoryTestingInterface {
    if (!MemoryTestingInterface.instance) {
      MemoryTestingInterface.instance = new MemoryTestingInterface();
    }
    return MemoryTestingInterface.instance;
  }

  /**
   * Run all memory tests
   */
  async runAllTests(): Promise<TestSuite[]> {
    console.log('🧪 Starting MBCP Memory System Tests...');
    
    this.testResults = [];
    
    // Test conversation threading
    await this.testConversationThreading();
    
    // Test memory block generation
    await this.testMemoryBlockGeneration();
    
    // Test MBCP snapshot format
    await this.testMBCPSnapshotFormat();
    
    // Test ChatFork workflow integration
    await this.testChatForkWorkflow();
    
    // Test Base64 encoding/decoding
    await this.testBase64EncodingDecoding();
    
    console.log('🧪 Memory tests completed:', this.testResults);
    return this.testResults;
  }

  /**
   * Test conversation threading capabilities
   */
  private async testConversationThreading(): Promise<void> {
    const suite: TestSuite = {
      suiteName: 'Conversation Threading',
      tests: [],
      passed: false,
      summary: ''
    };

    try {
      // Test 1: Create conversation thread
      const sheetId = 'test_sheet_001';
      const threadId = this.chatMemoryService.createConversationThread(sheetId);
      
      suite.tests.push({
        testName: 'Create Conversation Thread',
        passed: !!threadId && threadId.includes(sheetId),
        message: threadId ? `Created thread: ${threadId}` : 'Failed to create thread'
      });

      // Test 2: Add messages to thread (ensure we're on the parent thread)
      this.chatMemoryService.switchToThread(threadId); // Ensure we're on parent thread
      this.chatMemoryService.addStructuredMessage('Hello, this is a test message', 'user');
      this.chatMemoryService.addStructuredMessage('This is a response', 'llm');

      const thread = this.chatMemoryService.getConversationThread(threadId);
      const hasMessages = thread && thread.messages.length === 2;

      suite.tests.push({
        testName: 'Add Messages to Thread',
        passed: hasMessages,
        message: hasMessages ? `Thread has ${thread!.messages.length} messages` : 'Failed to add messages to thread'
      });

      // Test 3: Create child thread (fork)
      const childThreadId = this.chatMemoryService.createConversationThread(sheetId, threadId);
      const childThread = this.chatMemoryService.getConversationThread(childThreadId);
      const hasParent = childThread && childThread.parentThreadId === threadId;

      suite.tests.push({
        testName: 'Create Child Thread (Fork)',
        passed: hasParent,
        message: hasParent ? `Child thread created with parent: ${threadId}` : 'Failed to create child thread'
      });

      // Test 4: Get parent context (verify parent thread has messages first)
      const parentThread = this.chatMemoryService.getConversationThread(threadId);
      const parentHasMessages = parentThread && parentThread.messages.length > 0;

      if (parentHasMessages) {
        const parentContext = this.chatMemoryService.getParentContext(childThreadId);
        const hasParentContext = parentContext.parentMessages.length > 0;

        suite.tests.push({
          testName: 'Retrieve Parent Context',
          passed: hasParentContext,
          message: hasParentContext ? `Retrieved ${parentContext.parentMessages.length} parent messages` : 'No parent context found'
        });
      } else {
        suite.tests.push({
          testName: 'Retrieve Parent Context',
          passed: false,
          message: `Parent thread has no messages (${parentThread?.messages.length || 0} messages)`
        });
      }

    } catch (error) {
      suite.tests.push({
        testName: 'Conversation Threading Error',
        passed: false,
        message: `Error: ${error instanceof Error ? error.message : String(error)}`
      });
    }

    suite.passed = suite.tests.every(test => test.passed);
    suite.summary = `${suite.tests.filter(t => t.passed).length}/${suite.tests.length} tests passed`;
    this.testResults.push(suite);
  }

  /**
   * Test memory block generation
   */
  private async testMemoryBlockGeneration(): Promise<void> {
    const suite: TestSuite = {
      suiteName: 'Memory Block Generation',
      tests: [],
      passed: false,
      summary: ''
    };

    try {
      // Test CTX block generation
      const ctxBlock = this.chatMemoryService.prepareMemoryBlock('CTX');
      const isValidCTX = ctxBlock.type === 'CTX' && typeof ctxBlock.content === 'string';
      
      suite.tests.push({
        testName: 'Generate CTX Block',
        passed: isValidCTX,
        message: isValidCTX ? `CTX block: ${ctxBlock.content}` : 'Invalid CTX block format',
        data: ctxBlock
      });

      // Test MEM block generation
      const memBlock = this.chatMemoryService.prepareMemoryBlock('MEM');
      const isValidMEM = memBlock.type === 'MEM' && typeof memBlock.content === 'string';
      
      suite.tests.push({
        testName: 'Generate MEM Block',
        passed: isValidMEM,
        message: isValidMEM ? `MEM block generated (${memBlock.content.length} chars)` : 'Invalid MEM block format',
        data: memBlock
      });

      // Test ZIPPED block generation
      const zippedBlock = this.chatMemoryService.prepareMemoryBlock('ZIPPED');
      const isValidZIPPED = zippedBlock.type === 'ZIPPED' && zippedBlock.content.includes('[LARGE_CONTEXT]');
      
      suite.tests.push({
        testName: 'Generate ZIPPED Block',
        passed: isValidZIPPED,
        message: isValidZIPPED ? `ZIPPED block generated (${zippedBlock.content.length} chars)` : 'Invalid ZIPPED block format',
        data: zippedBlock
      });

    } catch (error) {
      suite.tests.push({
        testName: 'Memory Block Generation Error',
        passed: false,
        message: `Error: ${error instanceof Error ? error.message : String(error)}`
      });
    }

    suite.passed = suite.tests.every(test => test.passed);
    suite.summary = `${suite.tests.filter(t => t.passed).length}/${suite.tests.length} tests passed`;
    this.testResults.push(suite);
  }

  /**
   * Test MBCP snapshot format consistency
   */
  private async testMBCPSnapshotFormat(): Promise<void> {
    const suite: TestSuite = {
      suiteName: 'MBCP Snapshot Format',
      tests: [],
      passed: false,
      summary: ''
    };

    try {
      // Test snapshot collection
      const snapshot = await collectMBCPSnapshot();
      const hasRequiredFields = snapshot.id && snapshot.format === 'MBCP' && snapshot.version;
      
      suite.tests.push({
        testName: 'Collect MBCP Snapshot',
        passed: hasRequiredFields,
        message: hasRequiredFields ? `Snapshot collected: ${snapshot.id}` : 'Missing required snapshot fields'
      });

      // Test snapshot structure
      const hasCorrectStructure = snapshot.mindBook && snapshot.conversationThreads && snapshot.memoryEntries;
      
      suite.tests.push({
        testName: 'Validate Snapshot Structure',
        passed: hasCorrectStructure,
        message: hasCorrectStructure ? 'Snapshot has correct MBCP structure' : 'Invalid snapshot structure'
      });

      // Test metadata
      const hasMetadata = snapshot.metadata && typeof snapshot.metadata.totalSheets === 'number';
      
      suite.tests.push({
        testName: 'Validate Metadata',
        passed: hasMetadata,
        message: hasMetadata ? `Metadata: ${snapshot.metadata.totalSheets} sheets, ${snapshot.metadata.totalMessages} messages` : 'Invalid metadata'
      });

    } catch (error) {
      suite.tests.push({
        testName: 'MBCP Snapshot Error',
        passed: false,
        message: `Error: ${error instanceof Error ? error.message : String(error)}`
      });
    }

    suite.passed = suite.tests.every(test => test.passed);
    suite.summary = `${suite.tests.filter(t => t.passed).length}/${suite.tests.length} tests passed`;
    this.testResults.push(suite);
  }

  /**
   * Test ChatFork workflow integration
   */
  private async testChatForkWorkflow(): Promise<void> {
    const suite: TestSuite = {
      suiteName: 'ChatFork Workflow Integration',
      tests: [],
      passed: false,
      summary: ''
    };

    try {
      // Use a test sheet ID instead of relying on store state
      const currentSheetId = 'test_sheet_chatfork_' + Date.now();

      // Test 1: Create parent conversation
      const parentThreadId = this.chatMemoryService.createConversationThread(currentSheetId);
      const parentCreated = !!parentThreadId;

      suite.tests.push({
        testName: 'Create Parent Thread',
        passed: parentCreated,
        message: parentCreated ? `Parent thread created: ${parentThreadId}` : 'Failed to create parent thread'
      });

      if (parentCreated) {
        // Add messages to parent thread (ensure we're on the parent thread)
        this.chatMemoryService.switchToThread(parentThreadId); // Ensure we're on parent thread
        this.chatMemoryService.addStructuredMessage('Original conversation message', 'user');
        this.chatMemoryService.addStructuredMessage('LLM response to original', 'llm');

        // Verify parent thread has messages before creating fork
        const parentThread = this.chatMemoryService.getConversationThread(parentThreadId);
        const parentHasMessages = parentThread && parentThread.messages.length === 2;

        // Test 2: Create ChatFork (child thread)
        const forkThreadId = this.chatMemoryService.createConversationThread(currentSheetId, parentThreadId);
        const forkCreated = !!forkThreadId && forkThreadId !== parentThreadId;

        // Debug logging
        console.log('ChatFork Test Debug:', {
          parentThreadId,
          forkThreadId,
          forkCreated,
          areEqual: forkThreadId === parentThreadId,
          forkExists: !!forkThreadId
        });

        suite.tests.push({
          testName: 'Create ChatFork Thread',
          passed: forkCreated,
          message: forkCreated ? `Fork created: ${forkThreadId}` : `Failed to create fork thread. Parent: ${parentThreadId}, Fork: ${forkThreadId}, Equal: ${forkThreadId === parentThreadId}`
        });

        if (forkCreated && parentHasMessages) {
          // Test 3: Verify parent context in fork
          this.chatMemoryService.switchToThread(forkThreadId);
          const parentContext = this.chatMemoryService.getParentContext();
          const hasParentContext = parentContext.parentMessages.length > 0;

          suite.tests.push({
            testName: 'Fork Has Parent Context',
            passed: hasParentContext,
            message: hasParentContext ? `Fork has ${parentContext.parentMessages.length} parent messages` : 'Cannot test parent context - fork creation failed'
          });
        } else {
          suite.tests.push({
            testName: 'Fork Has Parent Context',
            passed: false,
            message: !forkCreated ? 'Cannot test parent context - fork creation failed' : `Parent thread has no messages (${parentThread?.messages.length || 0} messages)`
          });
        }
      } else {
        // If parent creation failed, mark remaining tests as failed
        suite.tests.push({
          testName: 'Create ChatFork Thread',
          passed: false,
          message: 'Cannot create fork - parent thread creation failed'
        });

        suite.tests.push({
          testName: 'Fork Has Parent Context',
          passed: false,
          message: 'Cannot test parent context - parent thread creation failed'
        });
      }

    } catch (error) {
      // Add error details to help with debugging
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : 'No stack trace';

      suite.tests.push({
        testName: 'ChatFork Workflow Error',
        passed: false,
        message: `Error: ${errorMessage}\nStack: ${errorStack?.substring(0, 200)}...`
      });
    }

    suite.passed = suite.tests.every(test => test.passed);
    suite.summary = `${suite.tests.filter(t => t.passed).length}/${suite.tests.length} tests passed`;
    this.testResults.push(suite);
  }

  /**
   * Test Base64 encoding/decoding for large contexts
   */
  private async testBase64EncodingDecoding(): Promise<void> {
    const suite: TestSuite = {
      suiteName: 'Base64 Encoding/Decoding',
      tests: [],
      passed: false,
      summary: ''
    };

    try {
      // Test data
      const testData = {
        mindbook: { sheets: [{ id: 'test', title: 'Test Sheet' }] },
        messages: [{ id: 'msg1', content: 'Test message', sender: 'user' }],
        context: { level: 'foundational', settings: { key: 'value' } }
      };

      // Test encoding
      const jsonString = JSON.stringify(testData);
      const base64Encoded = btoa(jsonString);
      const encodingWorked = base64Encoded.length > 0;
      
      suite.tests.push({
        testName: 'Base64 Encoding',
        passed: encodingWorked,
        message: encodingWorked ? `Encoded ${jsonString.length} chars to ${base64Encoded.length} chars` : 'Encoding failed'
      });

      // Test decoding
      const decodedString = atob(base64Encoded);
      const decodedData = JSON.parse(decodedString);
      const decodingWorked = JSON.stringify(decodedData) === jsonString;
      
      suite.tests.push({
        testName: 'Base64 Decoding',
        passed: decodingWorked,
        message: decodingWorked ? 'Successfully decoded and verified data' : 'Decoding failed or data corrupted'
      });

      // Test with ZIPPED block format
      const zippedBlock = this.chatMemoryService.prepareMemoryBlock('ZIPPED');
      const hasZippedFormat = zippedBlock.content.includes('[LARGE_CONTEXT]') && zippedBlock.content.includes('[/LARGE_CONTEXT]');
      
      suite.tests.push({
        testName: 'ZIPPED Block Format',
        passed: hasZippedFormat,
        message: hasZippedFormat ? 'ZIPPED block has correct format markers' : 'ZIPPED block format invalid'
      });

    } catch (error) {
      suite.tests.push({
        testName: 'Base64 Encoding/Decoding Error',
        passed: false,
        message: `Error: ${error instanceof Error ? error.message : String(error)}`
      });
    }

    suite.passed = suite.tests.every(test => test.passed);
    suite.summary = `${suite.tests.filter(t => t.passed).length}/${suite.tests.length} tests passed`;
    this.testResults.push(suite);
  }

  /**
   * Get test results summary
   */
  getTestSummary(): { totalSuites: number; passedSuites: number; totalTests: number; passedTests: number } {
    const totalSuites = this.testResults.length;
    const passedSuites = this.testResults.filter(suite => suite.passed).length;
    const totalTests = this.testResults.reduce((sum, suite) => sum + suite.tests.length, 0);
    const passedTests = this.testResults.reduce((sum, suite) => sum + suite.tests.filter(test => test.passed).length, 0);

    return { totalSuites, passedSuites, totalTests, passedTests };
  }

  /**
   * Clear test results
   */
  clearResults(): void {
    this.testResults = [];
  }
}

// Export singleton instance and convenience functions
export const memoryTestingInterface = MemoryTestingInterface.getInstance();
export const runMemoryTests = () => memoryTestingInterface.runAllTests();
export const getMemoryTestSummary = () => memoryTestingInterface.getTestSummary();
export const clearMemoryTestResults = () => memoryTestingInterface.clearResults();

// Export types
export type { TestResult, TestSuite };
