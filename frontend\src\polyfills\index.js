// polyfills/index.js - Entry point for all polyfills
import eventEmitter, { EventEmitter } from './events';

// Initialize all polyfills
export function initPolyfills() {
  // Make EventEmitter available globally if needed
  if (typeof window !== 'undefined') {
    window.EventEmitter = EventEmitter;
    window.events = eventEmitter;
  }
  
  console.log('Polyfills initialized successfully');
}

// Export all polyfills
export { eventEmitter, EventEmitter };
