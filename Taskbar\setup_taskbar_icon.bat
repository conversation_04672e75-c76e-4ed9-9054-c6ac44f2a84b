@echo off
echo Setting up MindBack Taskbar Icon...
echo.

REM Change to the Taskbar directory
cd /d "C:\Users\<USER>\Documents\VSCode\MindBack_Backup\MindBack_V1\Taskbar"

echo Step 1: Converting JPG logo to ICO format...
python convert_logo_to_ico.py

echo.
echo Step 2: Creating desktop shortcut with icon...
powershell -ExecutionPolicy Bypass -File "create_shortcut.ps1"

echo.
echo ✅ Setup complete!
echo.
echo Next steps:
echo 1. Check your Desktop for "Start MindBack" shortcut
echo 2. Right-click it and select "Pin to taskbar"
echo 3. Test the taskbar button to start MindBack
echo.
pause 