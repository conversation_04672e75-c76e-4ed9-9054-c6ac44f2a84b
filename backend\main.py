from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import openai
import os
import logging
from logging.handlers import RotatingFileHandler
from datetime import datetime
import os.path
from pydantic import BaseModel
from dotenv import load_dotenv
from pathlib import Path
import json
import sys
from api.config.settings import get_settings

# Add backend directory to the path to ensure imports work correctly
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up logging
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
print(f"Attempting to create logs directory at absolute path: {os.path.abspath(log_dir)}")

if not os.path.exists(log_dir):
    try:
        os.makedirs(log_dir)
        print(f"SUCCESS: Created logs directory at: {log_dir}")
    except Exception as e:
        print(f"ERROR: Failed to create logs directory at {log_dir}: {str(e)}")
        # Create alternative log directory in the current working directory
        print(f"Current working directory: {os.getcwd()}")
        log_dir = os.path.join(os.getcwd(), "logs")
        print(f"Trying alternative logs directory at: {log_dir}")
        try:
            os.makedirs(log_dir, exist_ok=True)
            print(f"SUCCESS: Created alternative logs directory at: {log_dir}")
        except Exception as alt_e:
            print(f"CRITICAL ERROR: Could not create any logs directory: {str(alt_e)}")
            print("Will attempt to log to current directory")
            log_dir = os.getcwd()  # Default to current directory
else:
    print(f"Logs directory already exists at: {log_dir}")

# Set up logging with timestamps
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_file = os.path.join(log_dir, f"mindback_{timestamp}.log")

# Print file path for debugging
print(f"Log file will be created at absolute path: {os.path.abspath(log_file)}")

# Configure the root logger
try:
    print("Configuring logging...")
    logging.basicConfig(
        level=logging.DEBUG,  # Changed to DEBUG for more verbose logging
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),  # Console handler
            RotatingFileHandler(
                log_file,
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5
            )
        ]
    )
    print(f"SUCCESS: Logging configured to: {log_file}")
except Exception as e:
    print(f"ERROR: Failed to configure logging: {str(e)}")
    # Setup basic console logging as fallback
    logging.basicConfig(
        level=logging.DEBUG,  # Changed to DEBUG for more verbose logging
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    print("Logging to console only")

logger = logging.getLogger(__name__)
logger.info(f"Starting MindBack API - Logs will be written to {log_file}")
logger.debug("This is a debug message to test logging level")

# Initialize settings - this will load the API key from root .env file
# and validate that it exists (will raise error if missing)
try:
    settings = get_settings()
    OPENAI_API_KEY = settings.openai_api_key
    logger.info(f"✅ OpenAI API key loaded successfully (length: {len(OPENAI_API_KEY)})")
    logger.info(f"First 4 characters: {OPENAI_API_KEY[:4]}")

    # Set the API key for the openai module
    openai.api_key = OPENAI_API_KEY

except Exception as e:
    logger.error(f"❌ CRITICAL: Failed to load OpenAI API key from root .env file: {e}")
    logger.error("Please ensure OPENAI_API_KEY is set in the root .env file")
    raise SystemExit("Cannot start server without valid OpenAI API key")

# Import the routers
from app.routers import mindmap
# Import LLM router from the newer implementation
from api.routes import llm
# CrewAI has been removed

app = FastAPI(title="MindBack API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",  # Vite dev server
        "http://127.0.0.1:5173",  # Vite dev server alternative URL
        "http://localhost:3000",  # In case React runs on port 3000
        "http://127.0.0.1:3000",  # Alternative for port 3000
        "http://localhost:8080",  # Another common dev port
        "http://127.0.0.1:8080",  # Alternative for port 8080
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["Content-Type", "Content-Length", "Authorization"],
)

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_message(self, message: str):
        for connection in self.active_connections:
            await connection.send_text(message)

manager = ConnectionManager()

# WebSocket for AI Chat
@app.websocket("/ws/chat")
async def websocket_chat(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            # No fallback behavior - API key is required and validated at startup

            try:
                # Parse the incoming message as JSON to get both text and model
                message_data = json.loads(data)
                user_message = message_data.get('message', '')
                model = message_data.get('model', 'gpt-3.5-turbo')  # Default to gpt-3.5-turbo if not specified

                response = openai.ChatCompletion.create(
                    model=model,
                    messages=[{"role": "user", "content": user_message}],
                    api_key=OPENAI_API_KEY
                )
                reply = response.choices[0].message.content
                await manager.send_message(reply)
            except json.JSONDecodeError:
                await manager.send_message("Error: Invalid message format. Expected JSON with 'message' and 'model' fields.")
            except Exception as e:
                await manager.send_message(f"Error: {str(e)}")
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {str(e)}")
        await websocket.close()

# Data model for mind map nodes
class MindMapNode(BaseModel):
    id: str
    text: str
    parent_id: str = None

# In-memory storage (for testing)
mind_map_storage = {}

# Save mind map (REST API)
@app.post("/mindmap/save")
async def save_mind_map(nodes: list[MindMapNode]):
    mind_map_storage["latest"] = nodes
    return JSONResponse(content={"message": "Mind map saved successfully"}, status_code=200)

# Load mind map (REST API)
@app.get("/mindmap/load")
async def load_mind_map():
    return JSONResponse(content={"nodes": mind_map_storage.get("latest", [])}, status_code=200)

@app.get("/")
async def root():
    return {"message": "MindBack API is running!"}

# Add health check endpoint
@app.get("/api/health")
async def health_check():
    """
    Simple health check endpoint to verify API connectivity
    """
    return {
        "status": "ok",
        "version": "1.0.0",
        "openai_api": "configured"  # API key is validated at startup
    }

# Include routers
app.include_router(mindmap.router)
app.include_router(llm.router)
# CrewAI router has been removed
