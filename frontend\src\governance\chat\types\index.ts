import { StructuredMessage } from '../../../services/ChatMemoryService';
import { MessageStatus, MessageStatusValues } from '../../../types/MessageStatus';

export interface Position {
  x: number;
  y: number;
}

export interface Dimensions {
  width: number;
  height: number;
  prevHeight?: number;
}

export interface DragOffset {
  x: number;
  y: number;
}

export interface SuggestedAction {
  type: string;
  label: string;
  data?: any;
}

export interface ChatMessageWithActions {
  id: string;
  text: string;
  timestamp: Date;
  sender: 'user' | 'llm';
  status?: MessageStatus;
  suggestedActions?: SuggestedAction[];
}

export interface DialogHeaderProps {
  onClose: () => void;
  onMouseDown: (e: React.MouseEvent) => void;
  onMinimize: () => void;
  onResetPosition: () => void;
  onResetSize: () => void;
  isCollapsed: boolean;
  useLiveLLM: boolean;
  onLiveLLMToggle: () => void;
  isMindmapMode?: boolean;
  onExitMindmapMode?: () => void;
}

export interface MessageInputProps {
  value: string;
  onChange: (value: string) => void;
  onSendMessage: () => Promise<void>;
  disabled?: boolean;
}

export interface MessageListProps {
  messages: ChatMessageWithActions[];
  isLoading: boolean;
  onActionButton: (action: SuggestedAction) => Promise<void>;
  messagesEndRef: React.RefObject<HTMLDivElement>;
}

export interface ModelSelectorProps {
  selectedModel: string;
  onModelChange: (model: string) => void;
  useLiveLLM: boolean;
  onLiveLLMToggle: () => void;
}

export interface GovernanceChatDialogProps {
  isOpen: boolean;
  onClose: () => void;
  hasMindMap?: boolean;
} 