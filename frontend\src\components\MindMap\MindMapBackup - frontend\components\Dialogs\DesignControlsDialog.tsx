import React from 'react';
import { useMindMap } from '../../context/MindMapContext';
import { Direction } from '../../types';

export const DesignControlsDialog: React.FC = () => {
  const { 
    showDesignControls, 
    setShowDesignControls,
    nodeColor,
    setNodeColor,
    direction,
    setDirection,
    projectName,
    setProjectName
  } = useMindMap();
  
  if (!showDesignControls) return null;
  
  // Function to handle reorganizing nodes after direction change
  const handleDirectionChange = (newDirection: Direction) => {
    setDirection(newDirection);
    
    // Ask if user wants to reorganize
    if (window.confirm('Do you want to reorganize the mind map?')) {
      // TODO: Implement reorganize nodes functionality
      console.log('Reorganizing nodes with direction:', newDirection);
    }
  };
  
  return (
    <div className="design-controls-dialog">
      <div className="dialog-header">
        <h3>Design Controls</h3>
        <button className="close-button" onClick={() => setShowDesignControls(false)}>×</button>
      </div>
      <div className="dialog-content">
        <div className="form-group">
          <label>Default Node Color:</label>
          <div className="color-options">
            {['#4dabf7', '#51cf66', '#ff6b6b', '#fcc419', '#da77f2'].map(color => (
              <div
                key={color}
                className={`color-option ${color === nodeColor ? 'selected' : ''}`}
                style={{ backgroundColor: color }}
                onClick={() => setNodeColor(color)}
              />
            ))}
          </div>
        </div>
        <div className="form-group">
          <label>Growth Direction:</label>
          <div className="direction-buttons">
            {['right', 'down', 'left', 'up'].map(dir => (
              <button
                key={dir}
                className={`direction-button ${direction === dir ? 'selected' : ''}`}
                onClick={() => handleDirectionChange(dir as Direction)}
              >
                {dir.charAt(0).toUpperCase() + dir.slice(1)}
              </button>
            ))}
          </div>
        </div>
        <div className="form-group">
          <label>Mind Map Name:</label>
          <input
            type="text"
            value={projectName}
            onChange={(e) => setProjectName(e.target.value)}
            className="text-input"
          />
        </div>
      </div>
    </div>
  );
};

export default DesignControlsDialog; 