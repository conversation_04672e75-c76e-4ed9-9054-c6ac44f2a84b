/**
 * API configuration and utilities
 */

// Default API configuration
const API_CONFIG = {
  baseUrl: import.meta.env.VITE_APP_API_URL || 'http://localhost:8000/api',
  timeout: 30000, // 30 seconds
  retryAttempts: 3,
  retryDelay: 1000, // 1 second
};

/**
 * Gets the base URL for API calls
 * @returns The configured API base URL
 */
export function getApiBaseUrl(): string {
  return API_CONFIG.baseUrl;
}

/**
 * Gets the API timeout value in milliseconds
 * @returns The configured API timeout
 */
export function getApiTimeout(): number {
  return API_CONFIG.timeout;
}

/**
 * Gets the number of retry attempts for failed API calls
 * @returns The configured number of retry attempts
 */
export function getRetryAttempts(): number {
  return API_CONFIG.retryAttempts;
}

/**
 * Gets the delay between retry attempts in milliseconds
 * @returns The configured retry delay
 */
export function getRetryDelay(): number {
  return API_CONFIG.retryDelay;
} 