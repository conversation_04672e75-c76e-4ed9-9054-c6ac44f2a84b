/**
 * keyboardHandler.ts
 *
 * Global keyboard handler for the application.
 * This utility provides a centralized way to handle keyboard shortcuts.
 */

import { useMindMapStore } from '../core/state/MindMapStore';
import RegistrationManager, { EventType } from '../core/services/RegistrationManager';

// Initialize the keyboard handler
export const initKeyboardHandler = () => {
  console.log('Initializing global keyboard handler');

  // Handle keydown events
  const handleKeyDown = (e: KeyboardEvent) => {
    // Skip if focus is in an input or textarea
    if (
      e.target instanceof HTMLInputElement ||
      e.target instanceof HTMLTextAreaElement
    ) {
      return;
    }

    console.log('Global key handler:', e.key, 'Shift:', e.shiftKey);

    const { selectedNodeId, nodes, addNode } = useMindMapStore.getState();

    // Handle Tab key for adding child nodes
    if (e.key === 'Tab' && selectedNodeId) {
      // Prevent default tab behavior
      e.preventDefault();
      e.stopPropagation();

      console.log('Global handler: Tab key pressed with node selected:', selectedNodeId);

      // Get the selected node
      const selectedNode = nodes[selectedNodeId];
      if (!selectedNode) {
        console.log('Global handler: Selected node not found');
        return;
      }

      // Calculate position for the new node
      const x = selectedNode.x + 200; // Offset to the right
      const y = selectedNode.y + 50;  // Offset down

      // Add the child node
      const childId = addNode(
        selectedNodeId,
        'New Node',
        x,
        y,
        { metadata: { isManuallyAdded: true } }
      );

      // FIXED: Get store reference outside of event handler to avoid hook violations
      const mindMapStore = useMindMapStore.getState();

      // Force update the node text to ensure it's properly set
      if (childId) {
        // Get the current node to ensure we have the latest data
        const currentNode = mindMapStore.nodes[childId];
        console.log('Global handler: New node created:', currentNode);

        // Update the node text again to ensure it's properly set
        mindMapStore.updateNode(childId, {
          text: currentNode.text || 'New Node'
        });
      }

      console.log('Global handler: Added child node with ID:', childId);

      // Register the node creation event
      if (childId) {
        RegistrationManager.registerEvent(EventType.NODE_CREATED, { id: childId });

        // If not Shift+Tab, select the new node and open NodeBox
        if (!e.shiftKey) {
          // First select the node
          useMindMapStore.getState().selectNode(childId);

          // Then open the NodeBox by setting the isEditing flag
          useMindMapStore.getState().updateNode(childId, {
            metadata: {
              ...useMindMapStore.getState().nodes[childId].metadata,
              isEditing: true // This flag will be used by NodeBox to determine if it should open
            }
          });

          // Register the node opening event
          RegistrationManager.registerEvent(EventType.NODE_OPENED, { id: childId });

          console.log('Global handler: Opening NodeBox for new node:', childId);

          // After a short delay, focus and select the title text
          setTimeout(() => {
            const titleInput = document.querySelector('.nodebox-title-input') as HTMLInputElement;
            if (titleInput) {
              titleInput.focus();
              titleInput.select();
              console.log('Global handler: Selected title text in NodeBox');
            } else {
              console.log('Global handler: Could not find title input field');
            }
          }, 300);
        }
      }
    }
  };

  // Add the event listener
  window.addEventListener('keydown', handleKeyDown, true);

  // Return a cleanup function
  return () => {
    window.removeEventListener('keydown', handleKeyDown, true);
  };
};
