# MindBack Logging System

## Overview

MindBack includes an optional logging server that can capture frontend logs for debugging purposes. This document explains how to use it.

## Logger Server

The logger server is implemented in `backend/logger_server.py` and runs on port 9000 by default. It provides an endpoint for receiving logs from the frontend.

### Starting the Logger Server

To start the logger server manually:

```bash
cd backend
python -m uvicorn logger_server:app --reload --host 0.0.0.0 --port 9000
```

## Frontend Logging

The frontend logging functionality has been removed from the main application to prevent connection errors when the logger server is not running.

## Implementing Frontend Logging (Optional)

If you need to enable frontend logging in the future, you can:

1. Create a utility file (e.g., `frontend/src/utils/logger.ts`) with logging functionality
2. Initialize the logger in your application entry point
3. Make sure to handle cases when the logger server is not available

Example implementation:

```typescript
// logger.ts
export const initLogger = (enabled: boolean) => {
  if (!enabled) return;
  
  // Store original console methods
  const originalConsoleLog = console.log;
  
  // Override console.log
  console.log = (...args) => {
    // Call original method
    originalConsoleLog.apply(console, args);
    
    // Send to logger server with error handling
    try {
      fetch('http://localhost:9000/log', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          timestamp: new Date().toISOString(),
          type: 'log',
          message: args.join(' ')
        })
      }).catch(() => {
        // Silently fail if server is not available
      });
    } catch (e) {
      // Ignore errors
    }
  };
}
```

## Best Practices

1. Always make logging optional
2. Handle connection errors gracefully
3. Provide a way to enable/disable logging at runtime
4. Consider performance implications of logging in production
