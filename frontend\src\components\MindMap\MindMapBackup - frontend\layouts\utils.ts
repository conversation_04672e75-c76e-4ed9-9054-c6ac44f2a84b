import { Connection } from '../core/models/Connection';
import { Node } from '../core/models/Node';
import { LayoutConfig, TreeLevel } from './types';

/**
 * Builds node levels for hierarchical layout using breadth-first search
 */
export function buildNodeLevels(
  nodes: Record<string, Node>,
  connections: Connection[],
  rootId: string
): TreeLevel[] {
  const levels: TreeLevel[] = [];
  const processed = new Set<string>();

  // Queue for BFS
  const queue: { nodeId: string; level: number }[] = [{ nodeId: rootId, level: 0 }];
  processed.add(rootId);

  while (queue.length > 0) {
    const { nodeId, level } = queue.shift()!;

    // Ensure level array exists
    if (!levels[level]) {
      levels[level] = { level, nodes: [] };
    }
    levels[level].nodes.push(nodeId);

    // Find children (sort by nodePath if available for consistent ordering)
    const children = connections
      .filter(conn => conn.from === nodeId && !processed.has(conn.to))
      .map(conn => ({ 
        id: conn.to, 
        path: nodes[conn.to]?.metadata?.nodePath || '' 
      }))
      .sort((a, b) => {
        // Sort by nodePath if available
        if (a.path && b.path) {
          return a.path.localeCompare(b.path);
        }
        return 0;
      })
      .map(child => child.id);

    // Add children to queue
    children.forEach(childId => {
      if (!processed.has(childId)) {
        processed.add(childId);
        queue.push({ nodeId: childId, level: level + 1 });
      }
    });
  }

  return levels;
}

/**
 * Checks if two nodes overlap
 */
export function checkNodeOverlap(nodeA: Node, nodeB: Node, config: LayoutConfig): boolean {
  // Use adaptive spacing if enabled
  if (config.adaptiveSpacing) {
    // Calculate adaptive sizes based on content
    const calculateAdaptiveSize = (node: Node) => {
      const contentLength = node.metadata?.content?.length || 0;
      const titleLength = node.text?.length || 0;
      
      const contentFactor = Math.min(contentLength / 200, 1.5);
      const titleFactor = Math.min(titleLength / 30, 1.3);
      
      const widthAdjust = Math.max(contentFactor, titleFactor);
      const heightAdjust = contentFactor;
      
      return {
        width: config.nodeWidth * (1 + (widthAdjust * 0.2)),
        height: config.nodeHeight * (1 + (heightAdjust * 0.15))
      };
    };
    
    const sizeA = calculateAdaptiveSize(nodeA);
    const sizeB = calculateAdaptiveSize(nodeB);
    
    // Calculate minimum spacing requirements
    const requiredHSpacing = Math.max(
      config.minimumHorizontalSpacing || 0, 
      config.horizontalSpacing * (config.siblingCompactionFactor || 0.6)
    );
    
    const requiredVSpacing = Math.max(
      config.minimumVerticalSpacing || 0, 
      config.verticalSpacing * (config.siblingCompactionFactor || 0.6)
    );
    
    // Check for overlap using adaptive sizes
    const xOverlap = Math.abs(nodeA.x - nodeB.x) < (sizeA.width/2 + sizeB.width/2 + requiredHSpacing);
    const yOverlap = Math.abs(nodeA.y - nodeB.y) < (sizeA.height/2 + sizeB.height/2 + requiredVSpacing);
    
    return xOverlap && yOverlap;
  } else {
    // Standard overlap check
    const xOverlap = Math.abs(nodeA.x - nodeB.x) < config.nodeWidth + config.horizontalSpacing;
    const yOverlap = Math.abs(nodeA.y - nodeB.y) < config.nodeHeight + config.verticalSpacing;
    return xOverlap && yOverlap;
  }
}

/**
 * Adjusts node positions to prevent overlap while maintaining hierarchy
 */
export function adjustOverlappingNodes(
  nodes: Record<string, Node>,
  connections: Connection[],
  config: LayoutConfig,
  maintainHierarchy: boolean = true
): Record<string, Node> {
  const nodeList = Object.values(nodes);
  const updatedNodes = { ...nodes };
  let iterations = 0;
  const maxIterations = 10;
  let hasOverlap = true;

  // Build parent-child relationships for maintaining hierarchy
  const childrenOf = new Map<string, string[]>();
  const parentOf = new Map<string, string>();
  
  if (maintainHierarchy) {
    connections.forEach(conn => {
      // Add to children map
      if (!childrenOf.has(conn.from)) {
        childrenOf.set(conn.from, []);
      }
      childrenOf.get(conn.from)!.push(conn.to);
      
      // Add to parent map
      parentOf.set(conn.to, conn.from);
    });
  }

  // Calculate adaptive spacing based on content size if enabled
  const adaptiveNodeSizes = new Map<string, { width: number, height: number }>();
  
  if (config.adaptiveSpacing) {
    nodeList.forEach(node => {
      // Get content length (if any) to adjust node size
      const contentLength = node.metadata?.content?.length || 0;
      const titleLength = node.text?.length || 0;
      
      // Calculate size adjustment factor based on content
      const contentFactor = Math.min(contentLength / 200, 1.5); // Cap at 1.5x
      const titleFactor = Math.min(titleLength / 30, 1.3); // Cap at 1.3x
      
      // Apply size adjustment
      const widthAdjust = Math.max(contentFactor, titleFactor);
      const heightAdjust = contentFactor;
      
      adaptiveNodeSizes.set(node.id, {
        width: config.nodeWidth * (1 + (widthAdjust * 0.2)),
        height: config.nodeHeight * (1 + (heightAdjust * 0.15))
      });
    });
  }

  while (hasOverlap && iterations < maxIterations) {
    hasOverlap = false;
    iterations++;

    for (let i = 0; i < nodeList.length; i++) {
      for (let j = i + 1; j < nodeList.length; j++) {
        const nodeA = updatedNodes[nodeList[i].id];
        const nodeB = updatedNodes[nodeList[j].id];
        
        // Use adaptive sizes if enabled, otherwise use config defaults
        const nodeASize = config.adaptiveSpacing 
          ? adaptiveNodeSizes.get(nodeA.id) 
          : { width: config.nodeWidth, height: config.nodeHeight };
          
        const nodeBSize = config.adaptiveSpacing 
          ? adaptiveNodeSizes.get(nodeB.id) 
          : { width: config.nodeWidth, height: config.nodeHeight };
        
        // Calculate minimum spacing requirements based on node sizes
        const requiredHSpacing = config.adaptiveSpacing
          ? Math.max(config.minimumHorizontalSpacing || 0, config.horizontalSpacing * config.siblingCompactionFactor!)
          : config.horizontalSpacing;
          
        const requiredVSpacing = config.adaptiveSpacing
          ? Math.max(config.minimumVerticalSpacing || 0, config.verticalSpacing * config.siblingCompactionFactor!)
          : config.verticalSpacing;
        
        // Check for overlap using adaptive sizes
        const xOverlap = Math.abs(nodeA.x - nodeB.x) < (nodeASize!.width/2 + nodeBSize!.width/2 + requiredHSpacing);
        const yOverlap = Math.abs(nodeA.y - nodeB.y) < (nodeASize!.height/2 + nodeBSize!.height/2 + requiredVSpacing);
        
        if (xOverlap && yOverlap) {
          hasOverlap = true;
          
          // Calculate repulsion force
          const dx = nodeB.x - nodeA.x;
          const dy = nodeB.y - nodeA.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          const minDistance = Math.sqrt(
            Math.pow(config.nodeWidth + config.horizontalSpacing, 2) +
            Math.pow(config.nodeHeight + config.verticalSpacing, 2)
          );
          
          if (distance < minDistance) {
            // Calculate base force
            const force = (minDistance - distance) / distance;
            let moveAX = -dx * force / 2;
            let moveAY = -dy * force / 2;
            let moveBX = dx * force / 2;
            let moveBY = dy * force / 2;
            
            // Adjust movement to maintain hierarchy if needed
            if (maintainHierarchy) {
              // Parent-child relationship check
              const aIsParentOfB = childrenOf.get(nodeA.id)?.includes(nodeB.id) || false;
              const bIsParentOfA = childrenOf.get(nodeB.id)?.includes(nodeA.id) || false;
              
              if (aIsParentOfB) {
                // A is parent of B, so B should only move down
                moveAX = 0; // Don't move parent horizontally
                moveBX = Math.abs(moveBX); // Child moves only right
              } else if (bIsParentOfA) {
                // B is parent of A, so A should only move down
                moveBX = 0; // Don't move parent horizontally
                moveAX = -Math.abs(moveAX); // Child moves only right
              }
            }
            
            // Apply movement
            updatedNodes[nodeA.id] = {
              ...nodeA,
              x: nodeA.x + moveAX,
              y: nodeA.y + moveAY
            };
            
            updatedNodes[nodeB.id] = {
              ...nodeB,
              x: nodeB.x + moveBX,
              y: nodeB.y + moveBY
            };
          }
        }
      }
    }
  }

  return updatedNodes;
}

/**
 * Gets all child nodes for a given parent
 */
export function getChildNodes(parentId: string, connections: Connection[]): string[] {
  return connections
    .filter(conn => conn.from === parentId)
    .map(conn => conn.to);
}

/**
 * Gets deepest level in the tree
 */
export function getTreeDepth(levels: TreeLevel[]): number {
  return levels.length;
}

/**
 * Gets maximum width of any level in the tree
 */
export function getMaxLevelWidth(levels: TreeLevel[]): number {
  return Math.max(...levels.map(level => level.nodes.length));
} 