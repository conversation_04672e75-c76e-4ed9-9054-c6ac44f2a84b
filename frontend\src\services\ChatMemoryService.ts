import { MessageStatus } from '../types/MessageStatus'; // Correct relative path
import { ResponseType, SuggestedAction } from './api/GovernanceLLM'; // This path is correct

export interface StructuredMessage {
  id: string;
  content: string;
  sender: 'user' | 'llm';
  timestamp: Date;
  parentId?: string;
  tags?: string[];
  status?: MessageStatus;
  responseType?: ResponseType;
  suggestedActions?: SuggestedAction[];
}

export interface ChatContext {
  recentMessages: StructuredMessage[];
  relevantNodes: string[];
  activeHat?: string;
  threadId?: string;
  parentThreadId?: string;
  sheetId?: string;
}

export interface ConversationThread {
  threadId: string;
  parentThreadId?: string;
  sheetId: string;
  messages: StructuredMessage[];
  createdAt: Date;
  lastUpdated: Date;
  contextSummary?: string;
}

export interface MemoryBlock {
  type: 'CTX' | 'MEM' | 'ZIPPED';
  content: string;
  metadata: Record<string, any>;
}

export class ChatMemoryService {
  private static instance: ChatMemoryService;
  private currentContext: ChatContext;
  private readonly maxRecentMessages: number = 10;
  private conversationThreads: Map<string, ConversationThread> = new Map();
  private currentThreadId?: string;

  private constructor() {
    this.currentContext = {
      recentMessages: [],
      relevantNodes: [],
    };
  }

  static getInstance(): ChatMemoryService {
    if (!ChatMemoryService.instance) {
      ChatMemoryService.instance = new ChatMemoryService();
    }
    return ChatMemoryService.instance;
  }

  getCurrentContext(): ChatContext {
    return this.currentContext;
  }

  clearMemory(): void {
    this.currentContext = {
      recentMessages: [],
      relevantNodes: [],
    };
  }

  addStructuredMessage(
    content: string,
    sender: 'user' | 'llm',
    responseType?: ResponseType,
    suggestedActions?: SuggestedAction[],
    parentId?: string,
    tags?: string[],
    status?: MessageStatus
  ): void {
    const message: StructuredMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content,
      sender,
      timestamp: new Date(),
      parentId,
      tags,
      status,
      responseType,
      suggestedActions
    };

    this.currentContext.recentMessages.push(message);
    
    if (this.currentContext.recentMessages.length > this.maxRecentMessages) {
      this.currentContext.recentMessages.shift();
    }
  }

  // Legacy method for backward compatibility
  addMessage(
    content: string,
    sender: 'user' | 'llm',
    parentId?: string,
    tags?: string[],
    status?: MessageStatus
  ): void {
    this.addStructuredMessage(content, sender, undefined, undefined, parentId, tags, status);
  }

  setActiveHat(hat: string | undefined): void {
    this.currentContext.activeHat = hat;
  }

  addRelevantNode(nodeId: string): void {
    if (!this.currentContext.relevantNodes.includes(nodeId)) {
      this.currentContext.relevantNodes.push(nodeId);
    }
  }

  removeRelevantNode(nodeId: string): void {
    this.currentContext.relevantNodes = this.currentContext.relevantNodes.filter(
      id => id !== nodeId
    );
  }

  getRelevantMessages(hat?: string): StructuredMessage[] {
    let relevantMessages = [...this.currentContext.recentMessages];

    if (hat) {
      relevantMessages = relevantMessages.filter(msg =>
        msg.tags?.includes(hat) || !msg.tags?.length
      );
    }

    return relevantMessages;
  }

  getMessagesByResponseType(type: 'factual' | 'strategic' | 'learning' | 'reflective'): StructuredMessage[] {
    return this.currentContext.recentMessages.filter(
      msg => msg.responseType?.type === type
    );
  }

  getMessagesRequiringMindmap(): StructuredMessage[] {
    return this.currentContext.recentMessages.filter(
      msg => msg.responseType?.requiresMindmap
    );
  }

  // ===== PHASE 1.1.4: Conversation Threading Capabilities =====

  /**
   * Create a new conversation thread for ChatFork lineage
   */
  createConversationThread(sheetId: string, parentThreadId?: string): string {
    // Generate unique thread ID with timestamp + random component to prevent collisions
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substr(2, 9);
    const threadId = `thread_${timestamp}_${randomSuffix}_${sheetId}`;

    const thread: ConversationThread = {
      threadId,
      parentThreadId,
      sheetId,
      messages: [],
      createdAt: new Date(),
      lastUpdated: new Date()
    };

    this.conversationThreads.set(threadId, thread);
    this.currentThreadId = threadId;

    // Update current context
    this.currentContext.threadId = threadId;
    this.currentContext.parentThreadId = parentThreadId;
    this.currentContext.sheetId = sheetId;

    console.log('ChatMemoryService: Created conversation thread:', threadId);
    return threadId;
  }

  /**
   * Switch to an existing conversation thread
   */
  switchToThread(threadId: string): boolean {
    const thread = this.conversationThreads.get(threadId);
    if (!thread) {
      console.warn('ChatMemoryService: Thread not found:', threadId);
      return false;
    }

    this.currentThreadId = threadId;
    this.currentContext.threadId = threadId;
    this.currentContext.parentThreadId = thread.parentThreadId;
    this.currentContext.sheetId = thread.sheetId;

    // Load thread messages into current context
    this.currentContext.recentMessages = [...thread.messages];

    console.log('ChatMemoryService: Switched to thread:', threadId);
    return true;
  }

  /**
   * Get parent context for forked chats
   */
  getParentContext(threadId?: string): {
    parentMessages: StructuredMessage[];
    contextSummary?: string;
    threadLineage: string[];
  } {
    const targetThreadId = threadId || this.currentThreadId;
    if (!targetThreadId) {
      return { parentMessages: [], threadLineage: [] };
    }

    const thread = this.conversationThreads.get(targetThreadId);
    if (!thread || !thread.parentThreadId) {
      return { parentMessages: [], threadLineage: [] };
    }

    const parentThread = this.conversationThreads.get(thread.parentThreadId);
    if (!parentThread) {
      return { parentMessages: [], threadLineage: [thread.parentThreadId] };
    }

    // Build thread lineage
    const lineage: string[] = [];
    let currentThread = parentThread;
    let depth = 0;

    while (currentThread && depth < 5) { // Limit depth to prevent infinite loops
      lineage.push(currentThread.threadId);
      if (currentThread.parentThreadId) {
        currentThread = this.conversationThreads.get(currentThread.parentThreadId);
      } else {
        break;
      }
      depth++;
    }

    return {
      parentMessages: parentThread.messages.slice(-5), // Last 5 messages from parent
      contextSummary: parentThread.contextSummary,
      threadLineage: lineage
    };
  }

  /**
   * Prepare memory block for API calls
   */
  prepareMemoryBlock(type: 'CTX' | 'MEM' | 'ZIPPED' = 'MEM'): MemoryBlock {
    const metadata = {
      threadId: this.currentThreadId,
      sheetId: this.currentContext.sheetId,
      generatedAt: new Date().toISOString(),
      messageCount: this.currentContext.recentMessages.length
    };

    switch (type) {
      case 'CTX':
        // Generate compressed contextual metadata
        const ctxParts = [];
        if (this.currentContext.sheetId) ctxParts.push(`sheet=${this.currentContext.sheetId}`);
        if (this.currentContext.activeHat) ctxParts.push(`hat=${this.currentContext.activeHat}`);
        if (this.currentContext.relevantNodes.length > 0) {
          ctxParts.push(`nodes=${this.currentContext.relevantNodes.slice(0, 3).join(',')}`);
        }

        return {
          type: 'CTX',
          content: ctxParts.join('/'),
          metadata
        };

      case 'MEM':
        // Generate structured conversation history
        const memParts = [];

        // THREAD: Parent conversation summary
        if (this.currentThreadId) {
          const parentContext = this.getParentContext();
          if (parentContext.parentMessages.length > 0) {
            const summary = this.summarizeMessages(parentContext.parentMessages);
            memParts.push(`THREAD: ${summary}`);
          }
        }

        // CONTEXT: Recent conversation
        if (this.currentContext.recentMessages.length > 0) {
          const recentSummary = this.summarizeMessages(this.currentContext.recentMessages.slice(-3));
          memParts.push(`CONTEXT: ${recentSummary}`);
        }

        // NODES: Relevant nodes
        if (this.currentContext.relevantNodes.length > 0) {
          memParts.push(`NODES: ${this.currentContext.relevantNodes.join(', ')}`);
        }

        return {
          type: 'MEM',
          content: memParts.join('\n') || 'No memory context available',
          metadata
        };

      case 'ZIPPED':
        // For large data - compress current context
        const largeData = {
          currentContext: this.currentContext,
          threads: Array.from(this.conversationThreads.values())
        };

        return {
          type: 'ZIPPED',
          content: `[LARGE_CONTEXT]\n${JSON.stringify(largeData)}\n[/LARGE_CONTEXT]`,
          metadata: {
            ...metadata,
            dataSize: JSON.stringify(largeData).length
          }
        };

      default:
        return {
          type: 'MEM',
          content: 'No memory context available',
          metadata
        };
    }
  }

  /**
   * Update context summary for current thread
   */
  updateContextSummary(summary: string): void {
    if (!this.currentThreadId) return;

    const thread = this.conversationThreads.get(this.currentThreadId);
    if (thread) {
      thread.contextSummary = summary;
      thread.lastUpdated = new Date();
    }
  }

  /**
   * Get conversation thread by ID
   */
  getConversationThread(threadId: string): ConversationThread | undefined {
    return this.conversationThreads.get(threadId);
  }

  /**
   * Get all conversation threads for a sheet
   */
  getThreadsBySheet(sheetId: string): ConversationThread[] {
    return Array.from(this.conversationThreads.values())
      .filter(thread => thread.sheetId === sheetId)
      .sort((a, b) => b.lastUpdated.getTime() - a.lastUpdated.getTime());
  }

  /**
   * Get current thread ID
   */
  getCurrentThreadId(): string | undefined {
    return this.currentThreadId;
  }

  /**
   * Summarize messages for context
   */
  private summarizeMessages(messages: StructuredMessage[]): string {
    if (messages.length === 0) return 'No messages';

    if (messages.length === 1) {
      return `${messages[0].sender}: ${messages[0].content.substring(0, 50)}...`;
    }

    const userMsgs = messages.filter(m => m.sender === 'user').length;
    const llmMsgs = messages.filter(m => m.sender === 'llm').length;
    const lastMsg = messages[messages.length - 1];

    return `${userMsgs} user, ${llmMsgs} LLM messages. Last: ${lastMsg.content.substring(0, 30)}...`;
  }

  // ===== Override addStructuredMessage to support threading =====

  addStructuredMessage(
    content: string,
    sender: 'user' | 'llm',
    responseType?: ResponseType,
    suggestedActions?: SuggestedAction[],
    parentId?: string,
    tags?: string[],
    status?: MessageStatus
  ): void {
    const message: StructuredMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content,
      sender,
      timestamp: new Date(),
      parentId,
      tags,
      status,
      responseType,
      suggestedActions
    };

    // Add to current context
    this.currentContext.recentMessages.push(message);

    if (this.currentContext.recentMessages.length > this.maxRecentMessages) {
      this.currentContext.recentMessages.shift();
    }

    // Add to current thread if exists
    if (this.currentThreadId) {
      const thread = this.conversationThreads.get(this.currentThreadId);
      if (thread) {
        thread.messages.push(message);
        thread.lastUpdated = new Date();
      }
    }
  }
}