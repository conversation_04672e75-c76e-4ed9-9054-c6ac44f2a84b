"""
Prompt Service Module
Handles loading YAML prompt templates and formatting them for the LLM API.
"""
import os
import yaml
import logging
import re
from typing import Dict, Any, List
from pathlib import Path

# Get the existing logger instead of creating a new one
logger = logging.getLogger(__name__)

# Set the path to the prompt library
PROMPT_LIBRARY_PATH = os.environ.get(
    "PROMPT_LIBRARY_PATH",
    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "Prompt_library")
)

def load_yaml_prompt(prompt_type: str) -> Dict[str, Any]:
    """
    Load a YAML prompt by prompt type from the prompt library.
    Supports both system_*.yaml and direct *.yaml files.
    """
    logger.info(f"Loading prompt template: {prompt_type}")

    # Try all possible extensions and prefixes
    for ext in [".yaml", ".yml"]:
        # Try direct file name
        file_path = os.path.join(PROMPT_LIBRARY_PATH, f"{prompt_type}{ext}")
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
                data = yaml.safe_load(content)
                logger.info(f"Loaded prompt from {file_path}")
                logger.debug(f"Prompt content keys: {list(data.keys()) if data else 'None'}")
                return data

        # Try with system_ prefix
        system_path = os.path.join(PROMPT_LIBRARY_PATH, f"system_{prompt_type}{ext}")
        if os.path.exists(system_path):
            with open(system_path, 'r', encoding='utf-8') as file:
                content = file.read()
                data = yaml.safe_load(content)
                logger.info(f"Loaded system prompt from {system_path}")
                logger.debug(f"Prompt content keys: {list(data.keys()) if data else 'None'}")
                return data

    # If no file found, log error and return empty dict
    logger.error(f"No prompt template found for type: {prompt_type}")
    return {}

def format_prompt_with_values(prompt_data: Dict[str, Any], values: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format a prompt template by replacing placeholders with actual values.
    Processes all fields in the prompt data recursively.
    """
    if not prompt_data:
        logger.warning("Empty prompt data provided for formatting")
        return {}

    def replace_placeholders(text, values):
        if not isinstance(text, str):
            return text

        # First, replace placeholders in {value} format (single braces)
        for key, value in values.items():
            text = text.replace(f'{{{key}}}', str(value))

        # Then replace placeholders in {{value}} format (double braces)
        pattern = r'\{\{([^}]+)\}\}'

        def replace_match(match):
            key = match.group(1).strip()
            if key in values:
                return str(values[key])
            # If key not found, leave placeholder untouched
            return match.group(0)

        return re.sub(pattern, replace_match, text)

    def process_item(item, values):
        if isinstance(item, dict):
            return {k: process_item(v, values) for k, v in item.items()}
        elif isinstance(item, list):
            return [process_item(i, values) for i in item]
        elif isinstance(item, str):
            return replace_placeholders(item, values)
        else:
            return item

    # Process the entire prompt data
    formatted_data = process_item(prompt_data, values)

    # Log detailed information about the replacement
    logger.info("=== PROMPT FORMATTING DETAILS ===")
    logger.info(f"Input values: {values}")
    if isinstance(prompt_data, dict):
        if 'system_role' in prompt_data:
            logger.info(f"Original system_role: {prompt_data['system_role'][:100]}...")
            logger.info(f"Formatted system_role: {formatted_data['system_role'][:100]}...")
        if 'content' in prompt_data:
            logger.info(f"Original content: {prompt_data['content'][:100]}...")
            logger.info(f"Formatted content: {formatted_data['content'][:100]}...")
    logger.info("==================================")

    logger.info("Formatted prompt with values")
    return formatted_data

def prepare_messages(system_prompt: str, user_prompt: str, formatted_prompt: Dict[str, Any]) -> Dict[str, Any]:
    """
    Prepare messages for the LLM API based on the formatted prompt.
    Supports multiple processing levels based on prompt complexity.
    """
    result = {
        "system_prompt": system_prompt,
        "content_prompt": user_prompt
    }

    if not formatted_prompt:
        logger.info("No formatted prompt data - using basic processing")
        return result

    # Determine processing level based on prompt structure
    has_analysis_approach = 'analysis_approach' in formatted_prompt
    has_extended_sections = any(key in formatted_prompt for key in ['guidelines', 'result_format', 'example_result'])
    
    if has_analysis_approach:
        # Level 2: Extended Processing (for complex analytical prompts like exploratory)
        logger.info("Using extended processing (Level 2) - detected analysis_approach")
        system_parts = []
        
        # Core system role
        if 'system_role' in formatted_prompt:
            system_parts.append(formatted_prompt['system_role'])
        
        # Extended sections
        if 'guidelines' in formatted_prompt:
            guidelines = formatted_prompt['guidelines']
            if isinstance(guidelines, list):
                guidelines_text = "\n".join(f"- {guideline}" for guideline in guidelines)
            else:
                guidelines_text = str(guidelines)
            system_parts.append(f"\n**Guidelines:**\n{guidelines_text}")
        
        if 'analysis_approach' in formatted_prompt:
            system_parts.append(f"\n**Analysis Approach:**\n{formatted_prompt['analysis_approach']}")
        
        if 'result_format' in formatted_prompt:
            system_parts.append(f"\n**Result Format:**\n{formatted_prompt['result_format']}")
        
        if 'example_result' in formatted_prompt:
            system_parts.append(f"\n**Example:**\n{formatted_prompt['example_result']}")
        
        if system_parts:
            result["system_prompt"] = "\n".join(system_parts)
            
    elif has_extended_sections:
        # Level 1.5: Basic+ Processing (simple prompts with some extended sections)
        logger.info("Using basic+ processing (Level 1.5) - detected extended sections")
        system_parts = []
        
        if 'system_role' in formatted_prompt:
            system_parts.append(formatted_prompt['system_role'])
            
        if 'guidelines' in formatted_prompt:
            guidelines = formatted_prompt['guidelines']
            if isinstance(guidelines, list):
                guidelines_text = "\n".join(f"- {guideline}" for guideline in guidelines)
            else:
                guidelines_text = str(guidelines)
            system_parts.append(f"\n**Guidelines:**\n{guidelines_text}")
            
        if system_parts:
            result["system_prompt"] = "\n".join(system_parts)
    else:
        # Level 1: Basic Processing (original behavior for simple prompts)
        logger.info("Using basic processing (Level 1) - simple prompt structure")
        if 'system_role' in formatted_prompt:
            result["system_prompt"] = formatted_prompt["system_role"]

    # Handle content prompt (consistent across all levels)
    if 'content' in formatted_prompt:
        result["content_prompt"] = formatted_prompt["content"]

    logger.info(f"Prepared messages using processing level based on prompt structure")
    return result