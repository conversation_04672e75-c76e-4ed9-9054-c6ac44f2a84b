# this file describe the 5M tool, its parameters and the expected outcome structure
tool_id: five_m
name: "5M Analysis (Ishikawa)"
purpose: "Identify root causes of problems using the 5M framework (Man, Machine, Material, Method, Measurement)."
used_for:
  - Exploratory / Diagnostic
input_type: "Problem statement"
output_type: "Cause-and-effect hierarchy"
return_structure:
  format: "json"
  schema:
    five_m:
      root:
        id: "root"
        text: "{problem}"
        children:
          - id: "man"
            text: "Man"
            description: "Human-related factors"
            children: []
          - id: "machine"
            text: "Machine"
            description: "Equipment-related factors"
            children: []
          - id: "material"
            text: "Material"
            description: "Supply and resource issues"
            children: []
          - id: "method"
            text: "Method"
            description: "Process-related causes"
            children: []
          - id: "measurement"
            text: "Measurement"
            description: "Data-related accuracy problems"
            children: []
  validation: "Must be a valid JSON object with a root cause and five primary branches."

