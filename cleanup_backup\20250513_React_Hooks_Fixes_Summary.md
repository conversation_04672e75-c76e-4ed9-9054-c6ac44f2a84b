# React Hooks Fixes in MindMap Workflow

**Date: May 13, 2025**

## Executive Summary

This document summarizes the changes made to fix the React Hooks violations in the MindMap workflow of the MindBack application. The fixes focused on ensuring proper hook usage, separating concerns, and eliminating duplicate initialization logic.

## Issues Fixed

1. **Hooks Order Violation**: The order of hooks was changing between renders in the MindSheet component.
2. **Hooks Inside Hooks**: Hooks were being called inside other hooks (specifically inside useEffect and useMemo).
3. **Conditional Hook Calls**: Hooks were being called conditionally, violating the Rules of Hooks.
4. **Duplicate Initialization**: The mindmap was being initialized twice - once in MindMapAdapter.ts and once in MindSheet.tsx.
5. **Store Management Issues**: There was confusion between global MindMapStore and sheet-specific stores from MindMapStoreFactory.
6. **Zustand Store Hooks**: Zustand store hooks were being called inside other hooks, causing violations.
7. **EnhancedMindMapManager Hooks Violations**: Similar hooks violations were occurring in the EnhancedMindMapManager component.
8. **MindMapCanvas Rendering Issues**: The MindMapCanvas component was not properly rendering nodes due to store access issues.

## Changes Made

### 1. Created MindSheetWrapper Component

- Created a wrapper component that handles all store access
- Moved Zustand store hooks to the wrapper component
- Passed down store references as props to MindSheet

### 2. Created EnhancedMindMapManagerWrapper Component

- Created a wrapper component that handles all store access for EnhancedMindMapManager
- Moved Zustand store hooks to the wrapper component
- Passed down store references as props to EnhancedMindMapManager

### 3. Created MindMapCanvasWrapper Component

- Created a wrapper component that handles all store access for MindMapCanvas
- Moved Zustand store hooks to the wrapper component
- Passed down store references as props to MindMapCanvas

### 4. Fixed MindSheet Component

- Removed direct Zustand store hooks
- Used store props passed from wrapper instead
- Separated useEffect hooks to avoid conditional hook calls
- Ensured all hooks are called at the top level in a consistent order
- Used refs to store props that might change between renders
- Memoized event handlers and complex rendering logic
- Added proper dependencies to useMemo hooks
- Updated to use EnhancedMindMapManagerWrapper instead of EnhancedMindMapManager
- Updated to use MindMapCanvasWrapper instead of MindMapCanvas

### 5. Fixed EnhancedMindMapManager Component

- Removed direct Zustand store hooks
- Used store props passed from wrapper instead
- Used refs to store props that might change between renders
- Removed useEffect for store updates
- Used direct store.getState() calls instead of store selectors

### 6. Fixed MindMapCanvas Component

- Removed direct Zustand store hooks
- Used store props passed from wrapper instead
- Used refs to store props that might change between renders
- Used direct store.getState() calls instead of store selectors
- Added null checks to avoid errors when store is not available

```typescript
// Before
useEffect(() => {
  // Initialize store
  const store = getMindMapStore(id);
  setSheetStore(store);

  // Conditional initialization
  if (contentType === MindSheetContentType.MINDMAP) {
    // Initialize mindmap
  }
}, [id, contentType]);

// After
useEffect(() => {
  // Initialize store - always runs
  const store = getMindMapStore(id);
  setSheetStore(store);
}, [id, contentType]);

useEffect(() => {
  // Separate effect for mindmap initialization
  if (contentType === MindSheetContentType.MINDMAP && sheetStore) {
    // Initialize mindmap
  }
}, [id, contentType, sheetStore]);

// Before
const contentElement = useMemo(() => {
  // Render logic
}, [contentType, sheetStore, isActive, id, openMindMapManager, handleMindMapError, handleChatForkError, handleChatForkClose]);

// After
const contentElement = useMemo(() => {
  // Create local references to avoid closure issues
  const currentContentType = contentType;
  const currentSheetStore = sheetStore;

  // Render logic using currentContentType and currentSheetStore
}, []);
```

### 2. MindMapAdapter

- Made initializeMindMap a wrapper that defers actual initialization to MindSheet
- Made processChildNodes a pure function that takes a store state instead of calling getMindMapStore
- Removed duplicate initialization logic

```typescript
// Before
export const initializeMindMap = (mbcpData: any): boolean => {
  // Get the sheet-specific store
  const sheetStore = getMindMapStore(activeSheet.id);

  // Initialize the mindmap
  sheetStore.getState().initialize(window.innerWidth, window.innerHeight);

  // Create a new project
  const rootNodeId = storeState.createNewProject(projectName);

  // Process child nodes
  processChildNodes(activeSheet.id, rootNodeId, mbcpData.mindmap.root.children);
};

// After
export const initializeMindMap = (mbcpData: any): boolean => {
  // Update the sheet content with the MBCP data
  mindBookStore.updateSheetContent(activeSheet.id, mbcpData);

  // The actual initialization will happen in the MindSheet component
  console.log('MindMapAdapter: Initialization will be handled by MindSheet component');
};
```

### 3. processChildNodes Function

- Made processChildNodes a pure function that takes a store state instead of calling getMindMapStore
- Exported the function for use in MindSheet

```typescript
// Before
const processChildNodes = (sheetId: string, parentId: string, children: MBCPNode[]): void => {
  // Get the sheet-specific store
  const store = getMindMapStore(sheetId);
  const storeState = store.getState();

  // Process each child
  // ...
};

// After
export const processChildNodes = (
  storeState: any,
  parentId: string,
  children: MBCPNode[]
): void => {
  // Process each child
  // ...
};
```

## Testing

To verify that our changes have fixed the React Hooks violations, we should test both the manual and automatic workflows for building mindmaps:

1. **Manual Workflow**: Click the "Build Mind Map" button after receiving a teleological response
2. **Automatic Workflow**: Select "Teleological" from the intent dropdown and enter a prompt

Both workflows should now work without any React Hooks violations in the console.

## Conclusion

The root cause of the React Hooks violations was that we were calling hooks inside other hooks and not maintaining a consistent order of hooks between renders. We've addressed these issues through several key changes:

1. **Component Separation**: We created wrapper components that handle all store access, moving Zustand store hooks out of the MindSheet, EnhancedMindMapManager, and MindMapCanvas components.
2. **Props vs. Hooks**: We replaced direct hook calls with props passed down from the wrapper components.
3. **Consistent Hook Order**: We ensured hooks are always called in the same order by removing conditional hook calls and separating effects.
4. **Pure Functions**: We refactored helper functions to be pure functions that don't use hooks or call functions that use hooks.
5. **Proper Dependencies**: We added proper dependencies to useMemo hooks to ensure they're correctly memoized.
6. **Single Source of Truth**: We consolidated initialization logic to avoid duplication and ensure proper state management.
7. **Direct State Access**: We used direct store.getState() calls instead of store selectors to avoid hooks violations.
8. **Null Checks**: We added null checks to avoid errors when store references are not available.

These changes, combined with the upgrade to react-konva 18.2.10, should resolve the dependency conflicts and React Hooks violations in the mindmap creation workflows. The key insight was that React Hooks must be called in exactly the same order on every render, which we've now ensured through our component separation and refactoring.

This approach follows React's best practices for complex components:
1. Separate concerns into different components
2. Use props to pass data down instead of accessing global state directly
3. Keep hooks at the top level of components
4. Ensure consistent hook order across renders
5. Use refs to store values that shouldn't trigger re-renders
6. Avoid calling hooks inside other hooks or conditionally
7. Use wrapper components to isolate store access
8. Add proper null checks to handle edge cases
