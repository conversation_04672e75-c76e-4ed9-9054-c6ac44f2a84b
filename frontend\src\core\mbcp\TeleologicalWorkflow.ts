/**
 * Teleological Workflow Manager
 * 
 * Handles both automatic and manual creation of teleological mindmaps.
 * Supports the MindBack Context Protocol for goal-oriented planning.
 * 
 * Workflows:
 * 1. Automatic: LLM detects teleological intent → creates mindmap
 * 2. Manual: User selects teleological → guided input → creates mindmap
 */

import { processMBCPData, MBCPData, MBCPNode } from './MBCPProcessor';
import { useMindBookStore } from '../state/MindBookStore';
import { v4 as uuidv4 } from 'uuid';

// Interface for manual teleological input
export interface ManualTeleologicalInput {
  goal: string;                    // Primary objective to achieve
  timeframe?: string;              // Timeline for goal completion
  currentSituation?: string;       // Starting point/current state
  constraints?: string[];          // Known limitations or obstacles
  resources?: string[];            // Available resources/assets
  stakeholders?: string[];         // People/entities involved
  successMetrics?: string[];       // How success will be measured
}

// Teleological mindmap template structure
export interface TeleologicalTemplate {
  templateId: string;
  name: string;
  description: string;
  rootStructure: MBCPNode;
}

// Result of teleological workflow processing
export interface TeleologicalWorkflowResult {
  success: boolean;
  mindsheetId?: string;
  rootNodeId?: string;
  error?: string;
  workflowType: 'automatic' | 'manual';
}

/**
 * Teleological Workflow Manager
 */
export class TeleologicalWorkflowManager {
  private static instance: TeleologicalWorkflowManager;
  
  public static getInstance(): TeleologicalWorkflowManager {
    if (!TeleologicalWorkflowManager.instance) {
      TeleologicalWorkflowManager.instance = new TeleologicalWorkflowManager();
    }
    return TeleologicalWorkflowManager.instance;
  }

  /**
   * Process automatic teleological workflow from LLM response
   */
  public processAutomaticTeleological(llmResponse: any): TeleologicalWorkflowResult {
    try {
      console.log('TeleologicalWorkflow: Processing automatic teleological from LLM');

      // Validate LLM response contains teleological intent
      if (!this.validateTeleologicalIntent(llmResponse)) {
        return {
          success: false,
          error: 'LLM response does not contain valid teleological intent',
          workflowType: 'automatic'
        };
      }

      // Extract MBCP data from LLM response
      const mbcpData = this.extractMBCPFromLLMResponse(llmResponse);
      
      // Create mindsheet and process MBCP data
      return this.createTeleologicalMindsheet(mbcpData, 'automatic');

    } catch (error) {
      console.error('TeleologicalWorkflow: Error in automatic workflow:', error);
      return {
        success: false,
        error: `Automatic workflow error: ${error}`,
        workflowType: 'automatic'
      };
    }
  }

  /**
   * Process manual teleological workflow from user input
   */
  public processManualTeleological(input: ManualTeleologicalInput): TeleologicalWorkflowResult {
    try {
      console.log('TeleologicalWorkflow: Processing manual teleological creation');

      // Validate user input
      if (!input.goal || input.goal.trim().length === 0) {
        return {
          success: false,
          error: 'Goal is required for teleological mindmap creation',
          workflowType: 'manual'
        };
      }

      // Generate MBCP data from user input
      const mbcpData = this.generateMBCPFromUserInput(input);
      
      // Create mindsheet and process MBCP data
      return this.createTeleologicalMindsheet(mbcpData, 'manual');

    } catch (error) {
      console.error('TeleologicalWorkflow: Error in manual workflow:', error);
      return {
        success: false,
        error: `Manual workflow error: ${error}`,
        workflowType: 'manual'
      };
    }
  }

  /**
   * Get available teleological templates
   */
  public getTeleologicalTemplates(): TeleologicalTemplate[] {
    return [
      {
        templateId: 'goal_achievement',
        name: 'Goal Achievement Plan',
        description: 'Structured approach to achieving specific objectives',
        rootStructure: this.createGoalAchievementTemplate()
      },
      {
        templateId: 'project_planning',
        name: 'Project Planning',
        description: 'Comprehensive project planning with milestones',
        rootStructure: this.createProjectPlanningTemplate()
      },
      {
        templateId: 'problem_solving',
        name: 'Problem Solving Strategy',
        description: 'Systematic approach to solving complex problems',
        rootStructure: this.createProblemSolvingTemplate()
      },
      {
        templateId: 'strategic_planning',
        name: 'Strategic Planning',
        description: 'Long-term strategic planning and execution',
        rootStructure: this.createStrategicPlanningTemplate()
      }
    ];
  }

  /**
   * Validate if LLM response contains teleological intent
   */
  private validateTeleologicalIntent(llmResponse: any): boolean {
    // Check various possible locations for intent
    const intent = llmResponse.intent || 
                  llmResponse.content?.intent || 
                  llmResponse.mbcpData?.intent ||
                  llmResponse.data?.intent;

    return intent === 'teleological';
  }

  /**
   * Extract MBCP data from LLM response
   */
  private extractMBCPFromLLMResponse(llmResponse: any): MBCPData {
    // Try different possible locations for MBCP data
    let mbcpData = llmResponse.mbcpData || 
                   llmResponse.content?.mbcpData ||
                   llmResponse.data?.mbcpData ||
                   llmResponse.content ||
                   llmResponse;

    // Ensure the data has the correct teleological structure
    if (!mbcpData.mindmap && !mbcpData.root) {
      mbcpData = {
        mindmap: {
          root: {
            text: mbcpData.text || 'Teleological Goal',
            description: mbcpData.description || 'Automatically generated teleological mindmap',
            metadata: {
              intent: 'teleological',
              creationSource: 'automatic',
              timestamp: new Date().toISOString()
            },
            children: mbcpData.children || []
          }
        }
      };
    }

    return mbcpData;
  }

  /**
   * Generate MBCP data from user input
   */
  private generateMBCPFromUserInput(input: ManualTeleologicalInput): MBCPData {
    const template = this.createGoalAchievementTemplate();
    
    // Customize template with user input
    template.text = input.goal;
    template.description = `Strategic plan to achieve: ${input.goal}`;
    
    if (input.timeframe) {
      template.metadata!.timeframe = input.timeframe;
    }

    // Add user-specific context to children
    if (template.children) {
      template.children.forEach((child, index) => {
        switch (index) {
          case 0: // Current State Analysis
            if (input.currentSituation) {
              child.description = input.currentSituation;
            }
            break;
          case 3: // Risk Assessment & Mitigation
            if (input.constraints && input.constraints.length > 0) {
              child.description = `Known constraints: ${input.constraints.join(', ')}`;
            }
            break;
          case 4: // Resource Requirements
            if (input.resources && input.resources.length > 0) {
              child.description = `Available resources: ${input.resources.join(', ')}`;
            }
            break;
          case 5: // Stakeholder Considerations
            if (input.stakeholders && input.stakeholders.length > 0) {
              child.description = `Key stakeholders: ${input.stakeholders.join(', ')}`;
            }
            break;
        }
      });
    }

    return {
      mindmap: {
        root: template
      },
      text: input.goal,
      description: `Manual teleological planning for: ${input.goal}`,
      intent: 'teleological'
    };
  }

  /**
   * Create mindsheet and process MBCP data
   */
  private createTeleologicalMindsheet(
    mbcpData: MBCPData, 
    workflowType: 'automatic' | 'manual'
  ): TeleologicalWorkflowResult {
    try {
      // Get MindBook store
      const mindBookStore = useMindBookStore.getState();

      // Extract goal from MBCP data for sheet title
      const goal = mbcpData.text || 
                   mbcpData.mindmap?.root?.text || 
                   mbcpData.root?.text || 
                   'Teleological Planning';

      // Create new mindsheet
      const sheetId = mindBookStore.createMindMapSheet(
        `${goal}`, 
        mbcpData
      );

      console.log(`TeleologicalWorkflow: Created mindsheet ${sheetId} for ${workflowType} workflow`);

      // Process MBCP data to create mindmap
      const processingResult = processMBCPData(mbcpData);

      if (!processingResult.success) {
        return {
          success: false,
          error: `Failed to process MBCP data: ${processingResult.error}`,
          workflowType
        };
      }

      // Mark the sheet with teleological metadata
      mindBookStore.updateSheetMetadata(sheetId, {
        intent: 'teleological',
        workflowType,
        createdAt: new Date().toISOString(),
        goal: goal
      });

      return {
        success: true,
        mindsheetId: sheetId,
        rootNodeId: processingResult.rootNodeId,
        workflowType
      };

    } catch (error) {
      console.error('TeleologicalWorkflow: Error creating mindsheet:', error);
      return {
        success: false,
        error: `Failed to create mindsheet: ${error}`,
        workflowType
      };
    }
  }

  /**
   * Create Goal Achievement template structure
   */
  private createGoalAchievementTemplate(): MBCPNode {
    return {
      id: uuidv4(),
      text: "Goal Achievement Plan",
      description: "Strategic framework for achieving specific objectives",
      metadata: {
        intent: 'teleological',
        templateType: 'goal_achievement',
        agent: 'blue_hat'
      },
      children: [
        {
          id: uuidv4(),
          text: "Current State Analysis",
          description: "Assess where we are now and what resources we have",
          metadata: { intent: 'analytical', agent: 'white_hat', tags: ['analysis', 'baseline'] }
        },
        {
          id: uuidv4(),
          text: "Goal Definition & Success Metrics",
          description: "Clearly define the goal and how success will be measured",
          metadata: { intent: 'teleological', agent: 'blue_hat', tags: ['goals', 'metrics'] }
        },
        {
          id: uuidv4(),
          text: "Strategy & Action Plans",
          description: "Develop concrete strategies and actionable steps",
          metadata: { intent: 'constructive', agent: 'green_hat', tags: ['strategy', 'actions'] }
        },
        {
          id: uuidv4(),
          text: "Risk Assessment & Mitigation",
          description: "Identify potential obstacles and mitigation strategies",
          metadata: { intent: 'critical', agent: 'black_hat', tags: ['risks', 'mitigation'] }
        },
        {
          id: uuidv4(),
          text: "Resource Requirements",
          description: "Determine what resources, time, and budget are needed",
          metadata: { intent: 'practical', agent: 'yellow_hat', tags: ['resources', 'budget'] }
        },
        {
          id: uuidv4(),
          text: "Stakeholder & Emotional Considerations",
          description: "Consider people involved and emotional aspects",
          metadata: { intent: 'empathetic', agent: 'red_hat', tags: ['stakeholders', 'emotions'] }
        }
      ]
    };
  }

  /**
   * Create Project Planning template structure
   */
  private createProjectPlanningTemplate(): MBCPNode {
    return {
      id: uuidv4(),
      text: "Project Planning Framework",
      description: "Comprehensive project planning with phases and milestones",
      metadata: {
        intent: 'teleological',
        templateType: 'project_planning',
        agent: 'blue_hat'
      },
      children: [
        {
          id: uuidv4(),
          text: "Project Scope & Objectives",
          description: "Define project boundaries and key objectives",
          metadata: { intent: 'teleological', agent: 'blue_hat', tags: ['scope', 'objectives'] }
        },
        {
          id: uuidv4(),
          text: "Phase Planning & Milestones",
          description: "Break down project into manageable phases",
          metadata: { intent: 'constructive', agent: 'green_hat', tags: ['phases', 'milestones'] }
        },
        {
          id: uuidv4(),
          text: "Resource Allocation",
          description: "Assign team members, budget, and tools",
          metadata: { intent: 'practical', agent: 'yellow_hat', tags: ['resources', 'allocation'] }
        },
        {
          id: uuidv4(),
          text: "Risk Management",
          description: "Identify and plan for potential project risks",
          metadata: { intent: 'critical', agent: 'black_hat', tags: ['risks', 'contingency'] }
        }
      ]
    };
  }

  /**
   * Create Problem Solving template structure
   */
  private createProblemSolvingTemplate(): MBCPNode {
    return {
      id: uuidv4(),
      text: "Problem Solving Strategy",
      description: "Systematic approach to understanding and solving problems",
      metadata: {
        intent: 'teleological',
        templateType: 'problem_solving',
        agent: 'blue_hat'
      },
      children: [
        {
          id: uuidv4(),
          text: "Problem Definition",
          description: "Clearly articulate what problem needs to be solved",
          metadata: { intent: 'analytical', agent: 'white_hat', tags: ['problem', 'definition'] }
        },
        {
          id: uuidv4(),
          text: "Root Cause Analysis",
          description: "Identify underlying causes of the problem",
          metadata: { intent: 'analytical', agent: 'white_hat', tags: ['analysis', 'causes'] }
        },
        {
          id: uuidv4(),
          text: "Solution Generation",
          description: "Brainstorm and develop potential solutions",
          metadata: { intent: 'constructive', agent: 'green_hat', tags: ['solutions', 'brainstorm'] }
        },
        {
          id: uuidv4(),
          text: "Solution Evaluation",
          description: "Assess feasibility and effectiveness of solutions",
          metadata: { intent: 'critical', agent: 'black_hat', tags: ['evaluation', 'feasibility'] }
        },
        {
          id: uuidv4(),
          text: "Implementation Plan",
          description: "Create action plan for implementing chosen solution",
          metadata: { intent: 'practical', agent: 'yellow_hat', tags: ['implementation', 'plan'] }
        }
      ]
    };
  }

  /**
   * Create Strategic Planning template structure
   */
  private createStrategicPlanningTemplate(): MBCPNode {
    return {
      id: uuidv4(),
      text: "Strategic Planning Framework",
      description: "Long-term strategic planning and execution roadmap",
      metadata: {
        intent: 'teleological',
        templateType: 'strategic_planning',
        agent: 'blue_hat'
      },
      children: [
        {
          id: uuidv4(),
          text: "Vision & Mission",
          description: "Define long-term vision and core mission",
          metadata: { intent: 'teleological', agent: 'blue_hat', tags: ['vision', 'mission'] }
        },
        {
          id: uuidv4(),
          text: "SWOT Analysis",
          description: "Assess strengths, weaknesses, opportunities, threats",
          metadata: { intent: 'analytical', agent: 'white_hat', tags: ['swot', 'analysis'] }
        },
        {
          id: uuidv4(),
          text: "Strategic Initiatives",
          description: "Identify key strategic initiatives and priorities",
          metadata: { intent: 'constructive', agent: 'green_hat', tags: ['initiatives', 'strategy'] }
        },
        {
          id: uuidv4(),
          text: "Implementation Roadmap",
          description: "Create timeline and roadmap for execution",
          metadata: { intent: 'practical', agent: 'yellow_hat', tags: ['roadmap', 'timeline'] }
        },
        {
          id: uuidv4(),
          text: "Performance Metrics",
          description: "Define KPIs and success measurements",
          metadata: { intent: 'analytical', agent: 'white_hat', tags: ['metrics', 'kpis'] }
        }
      ]
    };
  }
}

// Export singleton instance and convenience functions
export const teleologicalWorkflow = TeleologicalWorkflowManager.getInstance();

/**
 * Process automatic teleological workflow from LLM
 */
export const processAutomaticTeleological = (llmResponse: any): TeleologicalWorkflowResult => {
  return teleologicalWorkflow.processAutomaticTeleological(llmResponse);
};

/**
 * Process manual teleological workflow from user input
 */
export const processManualTeleological = (input: ManualTeleologicalInput): TeleologicalWorkflowResult => {
  return teleologicalWorkflow.processManualTeleological(input);
};

/**
 * Get available teleological templates
 */
export const getTeleologicalTemplates = (): TeleologicalTemplate[] => {
  return teleologicalWorkflow.getTeleologicalTemplates();
}; 