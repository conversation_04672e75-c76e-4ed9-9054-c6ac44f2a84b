/**
 * MindMapCanvas.css
 *
 * Styles for the MindMapCanvas component.
 */

/* Canvas container */
.konvajs-content {
  background-color: #f8fafc;
  z-index: 1500 !important; /* Higher z-index to ensure visibility */
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

/* Make the stage focusable but hide the outline */
.konvajs-content canvas {
  outline: none;
  z-index: 1500 !important; /* Higher z-index to ensure visibility */
  width: 100% !important;
  height: 100% !important;
}

/* Custom focus indicator for debugging */
.konvajs-content canvas:focus {
  /* Subtle indicator that doesn't interfere with the UI */
  box-shadow: inset 0 0 0 2px rgba(52, 152, 219, 0.3);
}

/* Container for the canvas */
.mindmap-canvas-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 1500;
}

/* Loading state */
.loading-canvas {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f8fafc;
}

.initializing-mindmap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3498db;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
