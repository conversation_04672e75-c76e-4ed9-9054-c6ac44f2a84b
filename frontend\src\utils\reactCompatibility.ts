/**
 * React Compatibility Utilities
 *
 * This file provides compatibility functions for React internal APIs
 * that may have changed between versions.
 */

// Simple implementation of useInternalStore
const simpleUseInternalStore = (subscribe: any, getSnapshot: any) => {
  // Just return the snapshot directly without any subscription
  // This is a simplified version that doesn't handle updates
  return getSnapshot();
};

// Create a safe React object with all necessary internals
const createSafeReact = () => {
  // Start with an empty object
  const safeReact: any = {};

  // Add basic React API methods
  safeReact.createElement = () => ({});
  safeReact.Fragment = Symbol('Fragment');
  safeReact.StrictMode = Symbol('StrictMode');

  // Add React hooks
  safeReact.useState = (initialState: any) => [
    typeof initialState === 'function' ? initialState() : initialState,
    () => {}
  ];
  safeReact.useEffect = () => {};
  safeReact.useContext = () => ({});
  safeReact.useReducer = (reducer: any, initialState: any) => [initialState, () => {}];
  safeReact.useCallback = (callback: any) => callback;
  safeReact.useMemo = (factory: any) => factory();
  safeReact.useRef = (initialValue: any) => ({ current: initialValue });

  // Create the internal structure
  safeReact.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = {
    ReactCurrentDispatcher: {
      current: {
        // Add all hooks to the dispatcher
        useState: safeReact.useState,
        useEffect: safeReact.useEffect,
        useContext: safeReact.useContext,
        useReducer: safeReact.useReducer,
        useCallback: safeReact.useCallback,
        useMemo: safeReact.useMemo,
        useRef: safeReact.useRef,
        useLayoutEffect: () => {},
        useImperativeHandle: () => {},
        useDebugValue: () => {},
        useDeferredValue: (value: any) => value,
        useTransition: () => [false, () => {}],
        useId: () => 'id-' + Math.random().toString(36).substring(2, 9),
        useSyncExternalStore: simpleUseInternalStore,
        useInternalStore: simpleUseInternalStore
      }
    }
  };

  return safeReact;
};

// Polyfill for useInternalStore if it's missing
export const createInternalStoreCompat = () => {
  try {
    console.log('Setting up React compatibility layer...');

    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      console.log('Not in browser environment, skipping compatibility setup');
      return {};
    }

    // Get or create React
    let React;

    if (window.React) {
      console.log('Using existing window.React');
      React = window.React;
    } else {
      try {
        console.log('Trying to import React');
        React = require('react');
        window.React = React;
        console.log('Successfully imported React and set on window');
      } catch (importError) {
        console.warn('Failed to import React, creating safe React object', importError);
        React = createSafeReact();
        window.React = React;
        console.log('Created and set safe React object on window');
      }
    }

    // Ensure internals exist
    if (!React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) {
      console.log('Creating React internals');
      React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = {
        ReactCurrentDispatcher: {
          current: {}
        }
      };
    }

    const internals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;

    // Ensure ReactCurrentDispatcher exists
    if (!internals.ReactCurrentDispatcher) {
      console.log('Creating ReactCurrentDispatcher');
      internals.ReactCurrentDispatcher = {
        current: {}
      };
    }

    // Ensure current exists
    if (!internals.ReactCurrentDispatcher.current) {
      console.log('Creating current dispatcher');
      internals.ReactCurrentDispatcher.current = {};
    }

    const dispatcher = internals.ReactCurrentDispatcher.current;

    // Add useInternalStore if it doesn't exist
    if (!dispatcher.useInternalStore) {
      console.log('Adding useInternalStore to dispatcher');
      dispatcher.useInternalStore = simpleUseInternalStore;
    }

    console.log('React compatibility layer setup complete');
    return { dispatcher };
  } catch (error) {
    console.error('Error in React compatibility setup:', error);
    return {};
  }
};

// Call this function before rendering any components that might use StageWrapper
export const initReactCompatibility = () => {
  try {
    console.log('Initializing React compatibility...');
    createInternalStoreCompat();
    console.log('React compatibility initialized successfully');
    return true;
  } catch (error) {
    console.error('Failed to initialize React compatibility:', error);
    return false;
  }
};
