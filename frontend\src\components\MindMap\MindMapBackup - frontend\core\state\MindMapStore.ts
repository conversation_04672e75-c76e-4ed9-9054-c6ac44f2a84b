import { create } from 'zustand';
import { createProjectSlice } from './store/projectSlice';
import { createNodeSlice } from './store/nodeSlice';
import { createConnectionSlice } from './store/connectionSlice';
import { createUndoRedoSlice } from './store/undoRedoSlice';
import { MindMapState } from './store/types';
import { createNode, defaultNodeValues, Node, Connection } from '../models/Node';
import { v4 as uuidv4 } from 'uuid';
import { LayoutManager } from '../../../../core/layout/LayoutManager';

export const useMindMapStore = create<MindMapState>()((set, get, ...args) => ({
  ...createProjectSlice(set, get),
  ...createNodeSlice(set, get),
  ...createConnectionSlice(set, get),
  ...createUndoRedoSlice(set, get),

  // LLM settings
  llmModel: 'gpt-3.5-turbo',
  setLlmModel: (model) => set({ llmModel: model }),

  // UI state
  isEditing: false,
  rootNodeId: null,
  connections: [],
  nodes: {},
  selectedNodeId: null,
  selectedConnectionId: null,
  projectName: 'Untitled Project',
  scale: 1.0,
  position: { x: 0, y: 0 },

  // Initialize mindmap with window dimensions
  initialize: (windowWidth: number, windowHeight: number) => {
    console.log('Initializing MindMap store with window dimensions:', { windowWidth, windowHeight });

    // Try to load last active project first
    const activeProjectLoaded = get().loadLastActiveProject();

    if (activeProjectLoaded) {
      console.log('Loaded last active project');
      return;
    }

    // If no active project, create a fresh one
    console.log('No active project found, creating a new one');

    // Clear existing state
    set({
      nodes: {},
      connections: [],
      selectedNodeId: null,
      position: { x: windowWidth / 2, y: windowHeight / 2 },
      scale: 1.0,
      rootNodeId: null,
      isEditing: false
    });

    // Create root node
    const rootNode = createNode(
      'Central Idea',
      0,
      0,
      {
        width: defaultNodeValues.width,
        height: defaultNodeValues.height,
        color: '#ffffff',
        borderColor: '#2c3e50',
        shape: 'rectangle',
        description: 'Click to edit',
        hatContributions: {
          blue: false,
          white: false,
          red: false,
          black: false,
          yellow: false,
          green: false
        },
        metadata: {
          nodePath: '1.0'
        }
      }
    );

    // Update state with root node
    set(state => ({
      ...state,
      nodes: { ...state.nodes, [rootNode.id]: rootNode },
      rootNodeId: rootNode.id,
      selectedNodeId: rootNode.id,
      projectName: 'Untitled Project'
    }));

    console.log('Store initialized with root node:', {
      rootNodeId: get().rootNodeId,
      position: get().position,
      scale: get().scale,
      nodesCount: Object.keys(get().nodes).length
    });
  },

  // Load the last active project
  loadLastActiveProject: () => {
    try {
      // Check for last active project in localStorage
      const lastActiveProject = localStorage.getItem('mindback_last_active_project');

      if (lastActiveProject) {
        console.log('[MindMapStore] Found last active project:', lastActiveProject);
        // Try to load the project
        return get().loadProject(lastActiveProject);
      }

      // If no last active project is saved, try to load most recent project
      const projects = get().getProjectsList();

      if (projects.length > 0) {
        // Get the most recently saved project
        const mostRecentProject = projects[0].name;
        console.log('[MindMapStore] Loading most recent project:', mostRecentProject);
        return get().loadProject(mostRecentProject);
      }

      return false;
    } catch (error) {
      console.error('[MindMapStore] Error loading last active project:', error);
      return false;
    }
  },

  // Override saveProject to also save the active project name
  saveProject: () => {
    const state = get();
    const { projectName, nodes, connections, position, scale } = state;

    const dataToSave = {
      projectName,
      nodes,
      connections,
      position,
      scale,
      savedAt: Date.now()
    };

    try {
      localStorage.setItem(`mindmap_${projectName}`, JSON.stringify(dataToSave));

      // Also save this as the last active project
      localStorage.setItem('mindback_last_active_project', projectName);

      console.log('[MindMapStore] Saved project:', projectName);
      return dataToSave;
    } catch (error) {
      console.error('[MindMapStore] Error saving project:', error);
      return null;
    }
  },

  // Set the project name
  setProjectName: (name: string) => {
    set({ projectName: name });
  },

  // Add a new node
  addNode: (x = 0, y = 0, options?: { isRoot?: boolean, parentId?: string, direction?: string, text?: string, metadata?: any }): string => {
    // Get the current state
    const state = get();
    const { nodes } = state;
    let { nextNodeId } = state;

    // Guard against invalid nextNodeId
    if (nextNodeId === undefined || nextNodeId === null) {
      console.error('DEBUG: nextNodeId is undefined or null, using fallback ID');
      // Use a fallback ID based on timestamp
      const fallbackId = `fallback_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

      // Create the node first with our fallback ID
      const nodeWithFallbackId = createNode({
        id: fallbackId,
        text: options?.text || 'New Node',
        x,
        y,
        parentId: options?.parentId,
        metadata: {
          ...options?.metadata,
          isManuallyAdded: options?.metadata?.isManuallyAdded ?? false,
          creationSource: options?.metadata?.creationSource || 'auto'
        }
      });

      // Set a valid nextNodeId for future use and add the node with fallback ID
      set({
        nextNodeId: 1,
        nodes: {
          ...nodes,
          [fallbackId]: nodeWithFallbackId
        }
      });

      console.log('DEBUG: Created node with fallback ID:', fallbackId);
      return fallbackId;
    }

    // Determine if this is a root node
    const isRoot = options?.isRoot || Object.keys(nodes).length === 0;

    // If no nodes exist, create a root node
    if (isRoot) {
      console.log('DEBUG: Creating root node');

      // Safely create ID string
      const nodeId = `${nextNodeId}`;

      // Create the node at the specified position
      const rootNode = createNode({
        id: nodeId,
        text: options?.text || 'Central Idea',
        x,
        y,
        metadata: {
          ...options?.metadata,
          // If we have explicit metadata about manual creation, use it
          isManuallyAdded: options?.metadata?.isManuallyAdded ?? false,
          creationSource: options?.metadata?.creationSource || 'auto'
        }
      });

      // Update the state with the new root node
      set(state => ({
        ...state,
        nodes: {
          ...state.nodes,
          [rootNode.id]: rootNode
        },
        rootNodeId: rootNode.id,
        nextNodeId: state.nextNodeId + 1
      }));

      console.log('DEBUG: Root node created:', {
        rootNode,
        rootNodeId: rootNode.id,
        nodesCount: Object.keys({...nodes, [rootNode.id]: rootNode}).length
      });

      return rootNode.id;
    }

    // If we're adding a child node and parentId is provided
    if (options?.parentId) {
      console.log('DEBUG: Creating child node with parent:', options.parentId);

      // Special handling for 'root' parentId - convert to actual rootNodeId
      let actualParentId = options.parentId;
      if (actualParentId === 'root') {
        actualParentId = state.rootNodeId || '';
        console.log('DEBUG: Converting "root" parentId to actual rootNodeId:', actualParentId);
      }

      // Verify we have a valid parent ID
      if (!actualParentId) {
        console.error('DEBUG: Invalid parent ID after processing:', options.parentId, '→', actualParentId);
        return '';
      }

      const parentNode = nodes[actualParentId];

      if (!parentNode) {
        console.error('Parent node not found:', actualParentId);
        return '';
      }

      // Calculate position if not provided
      let nodeX = x;
      let nodeY = y;

      // If position not specified, calculate based on parent
      if (x === 0 && y === 0) {
        // Get the direction with proper fallback - first ensure direction exists
        let directionStr = 'right'; // Default direction

        // Safely handle different direction formats
        if (options?.direction !== undefined) {
          try {
            // If it's a number or string, convert to string safely
            directionStr = String(options.direction).toLowerCase();
          } catch (e) {
            console.error('Error converting direction to string:', e);
            // Keep the default 'right' in case of error
          }
        }

        console.log('DEBUG: Using direction for positioning:', directionStr);

        // Calculate position based on direction
        switch (directionStr) {
          case 'right':
          case '90':
          case '0':
            nodeX = parentNode.x + 200;
            nodeY = parentNode.y;
            break;
          case 'left':
          case '270':
          case '180':
            nodeX = parentNode.x - 200;
            nodeY = parentNode.y;
            break;
          case 'up':
          case 'top':
            nodeX = parentNode.x;
            nodeY = parentNode.y - 150;
            break;
          case 'down':
          case 'bottom':
            nodeX = parentNode.x;
            nodeY = parentNode.y + 150;
            break;
          default:
            console.log(`DEBUG: Unrecognized direction "${directionStr}", defaulting to "right"`);
            nodeX = parentNode.x + 200;
            nodeY = parentNode.y;
        }

        console.log('DEBUG: Calculated position for child node:', { nodeX, nodeY, direction: directionStr });
      }

      // Safely create ID string
      const nodeId = `${nextNodeId}`;

      // Create the child node
      const childNode = createNode({
        id: nodeId,
        text: options?.text || 'New Node',
        x: nodeX,
        y: nodeY,
        parentId: parentNode.id,
        metadata: {
          ...options?.metadata,
          // If we have explicit metadata about manual creation, use it
          isManuallyAdded: options?.metadata?.isManuallyAdded ?? false,
          creationSource: options?.metadata?.creationSource || 'auto'
        }
      });

      // Create connection between parent and child node
      const connection: Connection = {
        id: `${parentNode.id}-${childNode.id}`,
        from: parentNode.id,
        to: childNode.id,
        type: 'solid',
        color: options?.metadata?.isManuallyAdded ? '#2ecc71' : '#9ca3af', // Green color for manual nodes
        thickness: 2,
        showArrow: false,
        lineStyle: 'angled',
        path: []
      };

      // Update the state with the new node and connection
      set(state => ({
        ...state,
        nodes: {
          ...state.nodes,
          [childNode.id]: childNode
        },
        connections: [
          ...state.connections,
          connection
        ],
        nextNodeId: state.nextNodeId + 1
      }));

      console.log('DEBUG: Child node created:', {
        childNode,
        connection,
        nodesCount: Object.keys({...nodes, [childNode.id]: childNode}).length
      });

      return childNode.id;
    }

    // Create a standalone node if no parent specified
    // Safely create ID string
    const nodeId = `${nextNodeId}`;

    const newNode = createNode({
      id: nodeId,
      text: options?.text || 'New Node',
      x,
      y,
      metadata: {
        ...options?.metadata,
        // If we have explicit metadata about manual creation, use it
        isManuallyAdded: options?.metadata?.isManuallyAdded ?? false,
        creationSource: options?.metadata?.creationSource || 'auto'
      }
    });

    // Update the state with the new node
    set(state => ({
      ...state,
      nodes: {
        ...state.nodes,
        [newNode.id]: newNode
      },
      nextNodeId: state.nextNodeId + 1
    }));

    console.log('DEBUG: Standalone node created:', {
      newNode,
      nodesCount: Object.keys({...nodes, [newNode.id]: newNode}).length
    });

    return newNode.id;
  },

  // Update an existing node
  updateNode: (id, updates) => set((state) => {
    if (!id) {
      console.error('Cannot update node: id is undefined or null');
      return state;
    }

    // Check if the node exists
    if (!state.nodes[id]) {
      console.warn(`Node with ID ${id} does not exist in state. Creating it first.`);
      // Create a basic node with the given ID
      const newNode = {
        id,
        text: updates.text || updates.title || 'New Node',
        x: updates.x || 0,
        y: updates.y || 0,
        width: updates.width || 200,
        height: updates.height || 100,
        color: updates.color || '#ffffff',
        borderColor: updates.borderColor || '#2c3e50',
        shape: updates.shape || 'rectangle',
        description: updates.description || '',
        metadata: updates.metadata || {},
        ...updates  // Apply all other updates
      };

      return {
        ...state,
        nodes: {
          ...state.nodes,
          [id]: newNode
        }
      };
    }

    // Normal update if node exists
    return {
      ...state,
      nodes: {
        ...state.nodes,
        [id]: { ...state.nodes[id], ...updates }
      }
    };
  }),

  // Delete a node
  deleteNode: (id) => set((state) => {
    const { [id]: deletedNode, ...remainingNodes } = state.nodes;

    // Also delete any connections to/from this node
    const filteredConnections = state.connections.filter(conn => conn.from !== id && conn.to !== id);

    return {
      nodes: remainingNodes,
      connections: filteredConnections,
      selectedNodeId: state.selectedNodeId === id ? null : state.selectedNodeId
    };
  }),

  // Move a node
  moveNode: (id, x, y) => set((state) => ({
    nodes: {
      ...state.nodes,
      [id]: { ...state.nodes[id], x, y }
    }
  })),

  // Update node position (alias for moveNode for compatibility)
  updateNodePosition: (id, x, y) => set((state) => {
    if (!state.nodes[id]) return state;
    return {
      nodes: {
        ...state.nodes,
        [id]: { ...state.nodes[id], x, y }
      }
    };
  }),

  // Select a node
  selectNode: (id, isEditing = false) => {
    // Update the selected node ID and isEditing flag
    console.log('[DEBUG] MindMapStore: Selecting node:', id, 'isEditing:', isEditing);
    console.log('[DEBUG] MindMapStore: Previous state:', {
      selectedNodeId: get().selectedNodeId
    });

    // Check if the node exists in the store
    const nodeExists = id ? !!get().nodes[id] : false;
    console.log('[DEBUG] MindMapStore: Node exists in store:', nodeExists);

    if (id && !nodeExists) {
      console.error('[DEBUG] MindMapStore: Attempted to select non-existent node:', id);
      // Don't update the state if the node doesn't exist
      return;
    }

    // Set the new state with both selectedNodeId and isEditing
    set({ selectedNodeId: id, isEditing: isEditing });

    // Verify the update immediately
    console.log('[DEBUG] MindMapStore: State immediately after update:', {
      selectedNodeId: get().selectedNodeId,
      nodeExists: id ? !!get().nodes[id] : false
    });

    // Also check after a small delay to ensure state propagation
    setTimeout(() => {
      const state = get();
      console.log('[DEBUG] MindMapStore: State after delay:', {
        selectedNodeId: state.selectedNodeId,
        nodeExists: id ? !!state.nodes[id] : false
      });
    }, 50);
  },

  // Add a new connection
  addConnection: (input: CreateConnectionInput) => {
    const connection = createConnection(input);
    set(state => ({
      ...state,
      connections: [...state.connections, connection]
    }));
  },

  // Update an existing connection
  updateConnection: (id: string, updates: Partial<Connection>) => {
    set(state => {
      const connection = state.connections.find(c => c.id === id);
      if (!connection) return state;

      const updatedConnection = {
        ...connection,
        ...updates,
        updatedAt: Date.now()
      };

      return {
        ...state,
        connections: state.connections.map(c =>
          c.id === id ? updatedConnection : c
        )
      };
    });
  },

  // Delete a connection
  deleteConnection: (id: string) => set((state) => {
    const filteredConnections = state.connections.filter(conn => conn.id !== id);
    return {
      connections: filteredConnections,
      selectedConnectionId: state.selectedConnectionId === id ? null : state.selectedConnectionId
    };
  }),

  // Select a connection
  selectConnection: (id: string | null) => set({ selectedConnectionId: id, selectedNodeId: null }),

  // Load a project from local storage
  loadProject: (name) => {
    try {
      const projectName = name || get().projectName;
      const savedData = localStorage.getItem(`mindmap_${projectName}`);

      if (!savedData) {
        console.log('[MindMapStore] No saved project found:', projectName);
        return false;
      }

      const parsedData = JSON.parse(savedData);

      set({
        ...parsedData,
        projectName,
        selectedNodeId: null,
        selectedConnectionId: null
      });

      // Save this as the last active project
      localStorage.setItem('mindback_last_active_project', projectName);

      console.log('[MindMapStore] Loaded project:', projectName);
      return true;
    } catch (error) {
      console.error('[MindMapStore] Error loading project:', error);
      return false;
    }
  },

  // Clear all nodes and connections
  clearAll: () => {
    set(state => ({
      ...state,
      nodes: {},
      connections: [],
      selectedNodeId: null,
      selectedConnectionId: null
    }));

    console.log('[MindMapStore] Cleared all nodes and connections');
  },

  // Reset the view position and scale
  resetView: () => {
    set({
      scale: 1.0,
      position: { x: 0, y: 0 }
    });
  },

  // Add a hat to a node
  addHatToNode: (nodeId, hat) => {
    const state = get();
    const node = state.nodes[nodeId];
    if (!node) return;

    set(state => ({
      nodes: {
        ...state.nodes,
        [nodeId]: {
          ...node,
          hatContributions: {
            ...node.hatContributions,
            [hat]: true
          }
        }
      }
    }));
  },

  // Remove a hat from a node
  removeHatFromNode: (nodeId, hat) => {
    const state = get();
    const node = state.nodes[nodeId];
    if (!node) return;

    set(state => ({
      nodes: {
        ...state.nodes,
        [nodeId]: {
          ...node,
          hatContributions: {
            ...node.hatContributions,
            [hat]: false
          }
        }
      }
    }));
  },

  // Add a child node to the specified node
  addChildNode: (parentId: string, customData?: Partial<Node>) => {
    const state = get();

    // Check if parent exists
    if (!state.nodes[parentId]) {
      console.error(`Parent node ${parentId} not found`);
      return null;
    }

    // Generate new node ID
    const childId = generateId();

    // Create child path based on parent's path
    const parentPath = state.nodes[parentId].path || '';
    const childPath = parentPath ? `${parentPath}.${childId}` : childId;

    // Create the new node
    const newNode: Node = {
      id: childId,
      text: customData?.text || 'New Node',
      description: customData?.description || '',
      x: state.nodes[parentId].x + 200, // Offset from parent
      y: state.nodes[parentId].y + 50,
      width: 150,
      height: 80,
      path: childPath,
      color: customData?.color || '#4299e1',
      borderColor: customData?.borderColor || '#4299e1',
      textColor: customData?.textColor || '#ffffff',
      fontSize: customData?.fontSize || 14,
      fontFamily: customData?.fontFamily || 'Arial',
      metadata: {
        ...customData?.metadata,
        // Add any default metadata here
      }
    };

    // Create connection
    const newConnection: Connection = {
      id: generateId(),
      from: parentId,
      to: childId,
      type: 'straight',
      color: '#64748b',
      thickness: 2
    };

    // Update the store
    set({
      nodes: {
        ...state.nodes,
        [childId]: newNode
      },
      connections: [...state.connections, newConnection],
      selectedNodeId: childId
    });

    return childId;
  },

  // Set the root node
  setRootNode: (id: string) => set({ rootNodeId: id }),

  // Clear all selections
  clearSelection: () => set({ selectedNodeId: null, selectedConnectionId: null }),

  // Set the layout direction
  setDirection: (direction: Direction) => set({ direction }),

  // Set node description
  setNodeDescription: (description: string) => {
    set(state => ({
      nodes: {
        ...state.nodes,
        root: {
          ...state.nodes.root,
          description
        }
      }
    }));
  },

  // Set nodes
  setNodes: (nodes: { [key: string]: Node }) => set({ nodes }),

  // Set project dialog visibility
  setShowProjectDialog: (show: boolean) => set({ showProjectDialog: show }),

  // Show/hide MindMap Manager
  setShowMindMapManager: (show: boolean) => set({ showMindMapManager: show }),

  // Update node positions based on modern layout system
  updateLayout: (layoutType: string = 'tree') => {
    const state = get();
    if (!state.rootNodeId) return;

    // Clear the manually positioned flag on all nodes
    // so they can be repositioned by the layout algorithm
    const nodesWithResetFlags = Object.fromEntries(
      Object.entries(state.nodes).map(([id, node]) => {
        if (node.metadata?.manuallyPositioned) {
          return [id, {
            ...node,
            metadata: {
              ...node.metadata,
              manuallyPositioned: false
            }
          }];
        }
        return [id, node];
      })
    );

    try {
      // Create a layout manager with the current state
      const layoutManager = new LayoutManager(
        nodesWithResetFlags,
        state.connections,
        state.rootNodeId,
        layoutType as any
      );

      // Apply the layout
      const updatedNodes = layoutManager.applyLayout();

      // Update the state with the new node positions
      set(state => ({
        ...state,
        nodes: updatedNodes
      }));

      return true;
    } catch (error) {
      console.error('[MindMapStore] Error updating layout:', error);
      return false;
    }
  },

  // Update node positions based on tree layout
  updateNodePosition: (nodeId: string, x: number, y: number) => {
    set(state => ({
      ...state,
      nodes: {
        ...state.nodes,
        [nodeId]: state.nodes[nodeId] ? { ...state.nodes[nodeId], x, y } : state.nodes[nodeId]
      }
    }));
  },

  // Create a new project
  createNewProject: (name: string) => {
    if (!name) return false;

    console.log('[MindMapStore] Creating new project:', name);

    // Clear existing state
    set({
      nodes: {},
      connections: [],
      position: { x: 0, y: 0 },
      scale: 1.0,
      projectName: name,
      selectedNodeId: null,
      selectedConnectionId: null,
      rootNodeId: null
    });

    // Create a root node - use the project name as the node text
    const rootNode = createNode(
      name, // Use the project name instead of hardcoded 'Central Idea'
      0,
      0,
      {
        width: defaultNodeValues.width,
        height: defaultNodeValues.height,
        color: '#ffffff',
        borderColor: '#2c3e50',
        shape: 'rectangle',
        description: 'Click to edit',
        hatContributions: {
          blue: false,
          white: false,
          red: false,
          black: false,
          yellow: false,
          green: false
        },
        metadata: {
          nodePath: '1.0'
        }
      }
    );

    console.log('[MindMapStore] Created root node with ID:', rootNode.id);

    // Update state with root node - CRITICAL to update atomically
    const nodesWithRoot = { [rootNode.id]: rootNode };
    set({
      nodes: nodesWithRoot,
      rootNodeId: rootNode.id,
      selectedNodeId: rootNode.id
    });

    // Verify root node was set
    const stateAfterUpdate = get();
    console.log('[MindMapStore] Verified root node ID:', stateAfterUpdate.rootNodeId);
    console.log('[MindMapStore] Verified node count:', Object.keys(stateAfterUpdate.nodes).length);

    // Double-check and ensure root node is set
    if (!stateAfterUpdate.rootNodeId) {
      console.warn('[MindMapStore] Root node ID not set, forcing update');

      // Force an explicit update of rootNodeId
      set((state) => ({
        ...state,
        rootNodeId: rootNode.id,
        nodes: {
          ...state.nodes,
          [rootNode.id]: rootNode
        }
      }));

      // Verify again
      const finalState = get();
      console.log('[MindMapStore] After forced update - rootNodeId:', finalState.rootNodeId);
      console.log('[MindMapStore] After forced update - nodes:', Object.keys(finalState.nodes));
    }

    // Save the new project
    const saveResult = get().saveProject();
    console.log('[MindMapStore] Project creation complete:', name);

    // Return the root node ID as well to allow immediate use
    return !!saveResult;
  },

  // Delete a project by name
  deleteProject: (name: string) => {
    try {
      localStorage.removeItem(`mindmap_${name}`);
      console.log('[MindMapStore] Deleted project:', name);
      return true;
    } catch (error) {
      console.error('[MindMapStore] Error deleting project:', error);
      return false;
    }
  },

  // Rename a project
  renameProject: (oldName: string, newName: string) => {
    if (!oldName || !newName || oldName === newName) return false;

    try {
      // Get the project data
      const projectData = localStorage.getItem(`mindmap_${oldName}`);
      if (!projectData) return false;

      // Store with new name
      localStorage.setItem(`mindmap_${newName}`, projectData);

      // Remove old entry
      localStorage.removeItem(`mindmap_${oldName}`);

      // Update current project name if it was the one renamed
      if (get().projectName === oldName) {
        set({ projectName: newName });
      }

      console.log('[MindMapStore] Renamed project:', oldName, 'to', newName);
      return true;
    } catch (error) {
      console.error('[MindMapStore] Error renaming project:', error);
      return false;
    }
  },

  // Duplicate a project
  duplicateProject: (name: string, newName: string) => {
    if (!name || !newName) return false;

    try {
      // Get the project data
      const projectData = localStorage.getItem(`mindmap_${name}`);
      if (!projectData) return false;

      // Store with new name
      localStorage.setItem(`mindmap_${newName}`, projectData);

      console.log('[MindMapStore] Duplicated project:', name, 'to', newName);
      return true;
    } catch (error) {
      console.error('[MindMapStore] Error duplicating project:', error);
      return false;
    }
  },

  // Get list of all saved projects
  getProjectsList: () => {
    try {
      const projects: Array<{ name: string; savedAt: number }> = [];

      // Find all keys in localStorage that start with "mindmap_"
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('mindmap_')) {
          const projectName = key.substring(8); // Remove "mindmap_" prefix

          try {
            // Get saved date if available
            const data = JSON.parse(localStorage.getItem(key) || '{}');
            projects.push({
              name: projectName,
              savedAt: data.savedAt || Date.now()
            });
          } catch (e) {
            // If parsing fails, just add the name
            projects.push({
              name: projectName,
              savedAt: Date.now()
            });
          }
        }
      }

      // Sort by most recently saved
      return projects.sort((a, b) => b.savedAt - a.savedAt);
    } catch (error) {
      console.error('[MindMapStore] Error getting projects list:', error);
      return [];
    }
  },

  // Export a project to a JSON file
  exportProjectToFile: (name: string) => {
    try {
      const projectData = localStorage.getItem(`mindmap_${name}`);
      if (!projectData) {
        console.error('[MindMapStore] No project data found for export:', name);
        return;
      }

      // Parse the data to add metadata for import
      const parsedData = JSON.parse(projectData);
      const exportData = {
        ...parsedData,
        exportVersion: '1.0',
        exportDate: new Date().toISOString(),
        projectName: name
      };

      // Convert to JSON string
      const dataStr = JSON.stringify(exportData, null, 2);

      // Create a blob and download link
      const blob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      // Create a download link and trigger it
      const downloadLink = document.createElement('a');
      downloadLink.href = url;
      downloadLink.download = `mindback_${name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(downloadLink);
      downloadLink.click();

      // Clean up
      URL.revokeObjectURL(url);
      document.body.removeChild(downloadLink);

      console.log('[MindMapStore] Project exported successfully:', name);
    } catch (error) {
      console.error('[MindMapStore] Error exporting project:', error);
    }
  },

  // Import a project from a JSON file
  importProjectFromFile: async (file: File) => {
    try {
      return new Promise<boolean>((resolve) => {
        const reader = new FileReader();

        reader.onload = (event) => {
          try {
            const fileContent = event.target?.result as string;
            const importedData = JSON.parse(fileContent);

            // Validate the imported data
            if (!importedData.nodes || !importedData.connections) {
              console.error('[MindMapStore] Invalid project file format');
              resolve(false);
              return;
            }

            // Get the project name
            let projectName = importedData.projectName || 'Imported Project';

            // Check if a project with this name already exists
            const existingProjects = get().getProjectsList();
            if (existingProjects.some(p => p.name === projectName)) {
              projectName = `${projectName} (Imported ${new Date().toLocaleTimeString()})`;
            }

            // Save the imported project
            localStorage.setItem(`mindmap_${projectName}`, JSON.stringify({
              nodes: importedData.nodes,
              connections: importedData.connections,
              scale: importedData.scale || 1,
              position: importedData.position || { x: 0, y: 0 },
              savedAt: Date.now()
            }));

            console.log('[MindMapStore] Project imported successfully:', projectName);
            resolve(true);
          } catch (error) {
            console.error('[MindMapStore] Error parsing imported file:', error);
            resolve(false);
          }
        };

        reader.onerror = () => {
          console.error('[MindMapStore] Error reading file');
          resolve(false);
        };

        reader.readAsText(file);
      });
    } catch (error) {
      console.error('[MindMapStore] Error importing project:', error);
      return false;
    }
  },

  // Auto-layout the tree (using the modern layout system)
  autoLayout: () => set((state) => {
    console.log('[DEBUG] autoLayout called, using modern layout system');

    if (!state.rootNodeId) {
      console.log('[DEBUG] autoLayout: No rootNodeId, returning without changes');
      return state;
    }

    // Verify root node exists in state
    if (!state.nodes[state.rootNodeId]) {
      console.error('[DEBUG] autoLayout: Root node ID exists but node not found in state');
      return state;
    }

    try {
      // Check for empty node map
      if (Object.keys(state.nodes).length === 0) {
        console.warn('[DEBUG] autoLayout: No nodes to layout');
        return state;
      }

      // Filter out any invalid nodes and fix node positions if needed
      const validNodes = Object.fromEntries(
        Object.entries(state.nodes)
          .filter(([id, node]) => {
            if (!node) {
              console.warn(`[DEBUG] autoLayout: Removing undefined node with ID ${id}`);
              return false;
            }

            // Fix common position issues
            if (node.x === undefined || node.y === undefined ||
                isNaN(node.x) || isNaN(node.y)) {
              console.warn(`[DEBUG] autoLayout: Node ${id} has invalid position, fixing`);
              node.x = node.x || 0;
              node.y = node.y || 0;
            }

            return true;
          })
          .map(([id, node]) => {
            // Make sure all nodes have basic minimum properties
            const updatedNode = {
              ...node,
              x: node.x || 0,
              y: node.y || 0,
              width: node.width || 200,
              height: node.height || 100,
              metadata: node.metadata || {}
            };

            return [id, updatedNode];
          })
      );

      // Check if we have nodes after filtering
      if (Object.keys(validNodes).length === 0) {
        console.warn('[DEBUG] autoLayout: No valid nodes to layout after filtering');
        return state;
      }

      // Reset manually positioned flags
      const nodesWithResetFlags = Object.fromEntries(
        Object.entries(validNodes).map(([id, node]) => {
          if (node.metadata?.manuallyPositioned) {
            return [id, {
              ...node,
              metadata: {
                ...node.metadata,
                manuallyPositioned: false
              }
            }];
          }
          return [id, node];
        })
      );

      // Make sure the root node exists in our valid nodes
      if (!nodesWithResetFlags[state.rootNodeId]) {
        console.error('[DEBUG] autoLayout: Root node not found in valid nodes');
        return state;
      }

      try {
        // Create a layout manager with the current state
        const layoutManager = new LayoutManager(
          nodesWithResetFlags,
          state.connections,
          state.rootNodeId,
          'tree'
        );

        // Apply the layout
        const updatedNodes = layoutManager.applyLayout();

        console.log('[DEBUG] autoLayout: Applied layout successfully');

        return {
          ...state,
          nodes: updatedNodes
        };
      } catch (error) {
        console.error('[DEBUG] autoLayout: Error with LayoutManager:', error);
        return state;
      }
    } catch (error) {
      console.error('[DEBUG] autoLayout: Error during layout calculation:', error);

      // Return state unchanged to avoid breaking the UI
      return state;
    }
  })
}));

// Helper function to check for cycles in tree connections
function checkForCycles(
  connections: Connection[],
  newConnection: CreateConnectionInput
): boolean {
  const graph = new Map<string, Set<string>>();

  // Build adjacency list from existing connections
  connections.forEach(conn => {
    if (!graph.has(conn.from)) {
      graph.set(conn.from, new Set());
    }
    graph.get(conn.from)!.add(conn.to);
  });

  // Add new connection to graph
  if (!graph.has(newConnection.from)) {
    graph.set(newConnection.from, new Set());
  }
  graph.get(newConnection.from)!.add(newConnection.to);

  // Helper function to check for cycles using DFS
  function hasCycle(node: string, visited: Set<string> = new Set()): boolean {
    if (visited.has(node)) {
      return true;
    }

    visited.add(node);
    const neighbors = graph.get(node);
    if (neighbors) {
      for (const neighbor of neighbors) {
        if (hasCycle(neighbor, visited)) {
          return true;
        }
      }
    }
    visited.delete(node);
    return false;
  }

  // Check for cycles starting from each node
  for (const node of graph.keys()) {
    if (hasCycle(node)) {
      return true;
    }
  }

  return false;
}

export default useMindMapStore;

