"""
Intent Configuration Module
Loads and provides access to intent types configuration
"""
import os
import yaml
import logging
from typing import Dict, List, Any, Optional

# Get the existing logger
logger = logging.getLogger(__name__)

# Path to the intent types configuration file
INTENT_CONFIG_PATH = os.path.join(os.path.dirname(__file__), "intent_types.yaml")

# Cache for the loaded configuration
_intent_config = None

def load_intent_config() -> Dict[str, Any]:
    """
    Load the intent types configuration from the YAML file
    """
    global _intent_config
    
    # Return cached config if available
    if _intent_config is not None:
        return _intent_config
    
    try:
        # Load the configuration file
        with open(INTENT_CONFIG_PATH, 'r') as f:
            _intent_config = yaml.safe_load(f)
        
        logger.info(f"Loaded intent configuration from {INTENT_CONFIG_PATH}")
        logger.debug(f"Intent types: {list(_intent_config.get('intents', {}).keys())}")
        
        return _intent_config
    except Exception as e:
        logger.error(f"Error loading intent configuration: {str(e)}")
        # Return a default configuration if loading fails
        return {
            "intents": {
                "factual": {"description": "Factual question"},
                "exploratory": {"description": "Exploratory question"},
                "teleological": {"description": "Goal-oriented planning"},
                "instantiation": {"description": "Template population"},
                "miscellaneous": {"description": "Other prompts"}
            }
        }

def get_intent_types() -> List[str]:
    """
    Get a list of all valid intent types
    """
    config = load_intent_config()
    return list(config.get("intents", {}).keys())

def get_intent_display_names() -> Dict[str, str]:
    """
    Get a dictionary mapping intent types to their display names
    """
    config = load_intent_config()
    return {
        intent_type: intent_data.get("display_name", intent_type.capitalize())
        for intent_type, intent_data in config.get("intents", {}).items()
    }

def get_intent_metadata(intent_type: str) -> Optional[Dict[str, Any]]:
    """
    Get metadata for a specific intent type
    """
    config = load_intent_config()
    return config.get("intents", {}).get(intent_type)

def is_valid_intent(intent_type: str) -> bool:
    """
    Check if an intent type is valid
    """
    return intent_type in get_intent_types()

# Initialize the configuration when the module is imported
load_intent_config()
