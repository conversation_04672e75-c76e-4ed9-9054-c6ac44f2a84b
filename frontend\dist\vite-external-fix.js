// Vite external fix for events module
(function() {
  // Create a simple EventEmitter implementation
  class EventEmitter {
    constructor() {
      this._events = {};
    }
    
    on(event, listener) {
      if (!this._events[event]) this._events[event] = [];
      this._events[event].push(listener);
      return this;
    }
    
    off(event, listener) {
      if (!this._events[event]) return this;
      this._events[event] = this._events[event].filter(l => l !== listener);
      return this;
    }
    
    emit(event, ...args) {
      if (!this._events[event]) return false;
      this._events[event].forEach(listener => listener.apply(this, args));
      return true;
    }
    
    once(event, listener) {
      const onceWrapper = (...args) => {
        this.off(event, onceWrapper);
        listener.apply(this, args);
      };
      this.on(event, onceWrapper);
      return this;
    }
  }
  
  // Directly patch the Vite browser external
  try {
    // This is a direct fix for the specific error in the console
    window.__vite_browser_external_events_EventEmitter = EventEmitter;
    
    // Also patch the module.exports for CommonJS compatibility
    if (!window.module) window.module = {};
    if (!window.module.exports) window.module.exports = {};
    window.module.exports.EventEmitter = EventEmitter;
    
    console.log('Vite browser external fix for events module applied');
  } catch (e) {
    console.error('Failed to apply Vite browser external fix:', e);
  }
})();
