# MindBack Prompt Convention v1.0

## Overview
This document defines the standard structure and processing rules for YAML prompt files in the MindBack system to ensure consistent behavior across all intent types.

## Prompt Structure Convention

### **Standard YAML Structure**
All prompt files MUST follow this structure:

```yaml
# === CORE SECTIONS (ALWAYS PROCESSED) ===
system_role: >
  The primary role definition and instructions for the LLM.
  This section is ALWAYS included in the system message.

content: >
  The main content template sent as the user message.
  Supports placeholder variables like {g-llm_dialogue}, {topic}, {intent}.

# === EXTENDED SECTIONS (CONDITIONALLY PROCESSED) ===
guidelines:
  # List format (recommended)
  - Guideline 1
  - Guideline 2
  # OR string format
  # String with guidelines

analysis_approach: >
  Detailed analytical framework or methodology instructions.
  Used for complex prompts requiring structured thinking.

result_format: >
  Specification of the expected output format.
  Typically JSON schema or structure description.

example_result: >
  Complete example of expected output.
  Helps LLM understand the desired response format.

# === METADATA SECTIONS (OPTIONAL) ===
name: prompt_name
description: >
  Human-readable description of the prompt's purpose.
version: 1.0
author: Team Name
```

### **Processing Rules**

#### **Level 1: Basic Processing** (Current behavior)
- Processes: `system_role`, `content`
- Used for: Simple prompts that don't need extended instructions
- Prompt types: `initiate_situational2`, `initiate_miscellaneous2`

#### **Level 2: Extended Processing** (New behavior)
- Processes: `system_role`, `content`, `guidelines`, `analysis_approach`, `result_format`, `example_result`
- Used for: Complex prompts requiring detailed instructions
- Prompt types: `initiate_chatfork2`, future complex prompts
- Triggered by: Presence of `analysis_approach` field

#### **Level 3: Custom Processing** (Existing behavior)
- Processes: Custom logic per prompt type
- Used for: Specialized prompts with unique requirements
- Prompt types: `initiate_mindmap2` (teleological)

### **Prompt Type Categories**

#### **Category A: Simple Prompts**
```yaml
system_role: >
  [Complete instructions]
content: >
  {g-llm_dialogue}
guidelines:
  - [Simple list]
```
Examples: situational, miscellaneous, factual

#### **Category B: Analytical Prompts** 
```yaml
system_role: >
  [Role definition]
content: >
  {g-llm_dialogue}
guidelines:
  - [Detailed guidelines]
analysis_approach: >
  [Structured framework] # Triggers extended processing
result_format: >
  [Output specification]
example_result: >
  [Complete example]
```
Examples: exploratory, complex instantiation

#### **Category C: Specialized Prompts**
```yaml
system_role: >
  [Complete specialized instructions including all requirements]
content: >
  [Detailed content template]
```
Examples: teleological (mindmap generation)

### **Implementation Strategy**

1. **Backward Compatibility**: Existing prompts continue to work unchanged
2. **Progressive Enhancement**: New prompts can use extended sections
3. **Auto-Detection**: Processing level determined by prompt content
4. **Safe Fallback**: If extended processing fails, fall back to basic processing

### **Migration Guidelines**

#### **For Existing Prompts:**
- No changes required - they continue to work
- Can be gradually enhanced with additional sections

#### **For New Prompts:**
- Use Category B structure for complex analytical tasks
- Use Category A structure for simple tasks
- Use Category C structure for highly specialized tasks

#### **For the Exploratory Prompt Fix:**
- Keep current structure
- Enable extended processing by detecting `analysis_approach`
- All sections will be combined into comprehensive system message

### **Variable Replacement Rules**

All sections support these placeholder variables:
- `{g-llm_dialogue}`: User's input prompt
- `{topic}`: Extracted or provided topic
- `{intent}`: Detected or provided intent

Processing order:
1. Load YAML file
2. Replace variables in ALL sections
3. Combine sections based on processing level
4. Send to LLM

### **Quality Assurance**

#### **Required Testing:**
- Test all existing prompt types after changes
- Verify teleological prompts still work perfectly
- Confirm exploratory prompts show improvement
- Validate backward compatibility

#### **Monitoring:**
- Log processing level used for each prompt
- Monitor response quality metrics
- Track any processing failures or fallbacks 