# Testing Procedures

## Unit Testing

- Use pytest for Python
- Use Jest for JavaScript/TypeScript


## Test Structure

# Testing Architecture

## Unit Tests
/src
  /components
    /__tests__
      Component.spec.tsx
  /features
    /__tests__
      Feature.spec.tsx
  /core
    /__tests__
      Core.spec.tsx

## Integration Tests
/tests
  /integration
    /features
      Feature.test.tsx

## E2E Tests
/cypress
  /e2e
    /features
      Feature.cy.tsx