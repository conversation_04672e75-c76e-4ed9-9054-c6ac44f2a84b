instantiation_template_router = {
  "business_model_canvas": {
    "prompt_template": "initiate_mindmap2.yaml",
    "button_label": "Build Business Model Canvas",
    "renderer": "MindMap",
    "workflow": "mindmap_workflow"
  },
  "swot_analysis": {
    "prompt_template": "initiate_mindmap2.yaml",
    "button_label": "Build SWOT",
    "renderer": "MindMap",
    "workflow": "mindmap_workflow"
  },
  "triz_method": {
    "prompt_template": "initiate_mindmap2.yaml",
    "button_label": "Build TRIZ Breakdown",
    "renderer": "MindMap",
    "workflow": "mindmap_workflow"
  },
  "pestel_analysis": {
    "prompt_template": "initiate_mindmap2.yaml",
    "button_label": "Build PESTEL Analysis",
    "renderer": "MindMap",
    "workflow": "mindmap_workflow"
  },
  "empathy_map": {
    "prompt_template": "initiate_mindmap2.yaml",
    "button_label": "Build Empathy Map",
    "renderer": "MindMap",
    "workflow": "mindmap_workflow"
  },
  "customer_journey_map": {
    "prompt_template": "initiate_mindmap2.yaml",
    "button_label": "Build Customer Journey Map",
    "renderer": "MindMap",
    "workflow": "mindmap_workflow"
  },
  "balanced_scorecard": {
    "prompt_template": "initiate_mindmap2.yaml",
    "button_label": "Build Balanced Scorecard",
    "renderer": "MindMap",
    "workflow": "mindmap_workflow"
  },
  "okr_structure": {
    "prompt_template": "initiate_mindmap2.yaml",
    "button_label": "Build OKR Structure",
    "renderer": "MindMap",
    "workflow": "mindmap_workflow"
  },
  "raci_matrix": {
    "prompt_template": "initiate_mindmap2.yaml",
    "button_label": "Build RACI Matrix",
    "renderer": "MindMap",
    "workflow": "mindmap_workflow"
  },
  "porter_five_forces": {
    "prompt_template": "initiate_mindmap2.yaml",
    "button_label": "Build Porter's Five Forces",
    "renderer": "MindMap",
    "workflow": "mindmap_workflow"
  },
  "mckinsey_7s": {
    "prompt_template": "initiate_mindmap2.yaml",
    "button_label": "Build McKinsey 7S Framework",
    "renderer": "MindMap",
    "workflow": "mindmap_workflow"
  },
  "5s_framework": {
    "prompt_template": "initiate_mindmap2.yaml",
    "button_label": "Build 5S Framework",
    "renderer": "MindMap",
    "workflow": "mindmap_workflow"
  },
  "6m_method": {
    "prompt_template": "initiate_mindmap2.yaml",
    "button_label": "Build 6M Root Cause Map",
    "renderer": "MindMap",
    "workflow": "mindmap_workflow"
  },
  "lean_waste_classification": {
    "prompt_template": "initiate_mindmap2.yaml",
    "button_label": "Build Lean Waste Classification",
    "renderer": "MindMap",
    "workflow": "mindmap_workflow"
  }
}