"""
Configuration settings for the MindBack API.
Handles loading settings from environment variables.
Single source of truth: root .env file only.
"""

from pydantic_settings import BaseSettings
import os
from typing import Optional
from functools import lru_cache
from dotenv import load_dotenv
from pathlib import Path

# Load environment variables from root .env file only
# Determine the root directory (3 levels up from this file)
root_dir = Path(__file__).resolve().parent.parent.parent
env_path = root_dir / '.env'

# Load from the specific root .env file
load_dotenv(dotenv_path=env_path)

class Settings(BaseSettings):
    """
    Application settings loaded from environment variables.
    Single source of truth: root .env file.
    No fallbacks, no defaults for critical settings.
    """
    # API keys - NO FALLBACKS, MUST BE SET IN ROOT .ENV
    openai_api_key: str

    # API settings
    debug_mode: bool = False

    # Path configurations
    prompt_library_path: str = os.getenv(
        "PROMPT_LIBRARY_PATH",
        str(root_dir / "backend" / "Prompt_library")
    )

    # Model configurations - with reasonable defaults
    default_model: str = "gpt-4o-mini"
    default_temperature: float = 0.7

    class Config:
        # Point to the root .env file explicitly
        env_file = str(env_path)
        env_file_encoding = "utf-8"
        extra = "ignore"

@lru_cache()
def get_settings() -> Settings:
    """
    Get cached settings instance.
    Will raise validation error if OPENAI_API_KEY is not set.
    """
    return Settings()