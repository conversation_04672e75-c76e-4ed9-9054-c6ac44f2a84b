<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindBack Backend Visualization</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #2980b9;
            margin-top: 30px;
        }
        .section {
            margin-bottom: 40px;
            padding: 20px;
            border-radius: 5px;
            background-color: #f8f9fa;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .note {
            background-color: #fff8dc;
            border-left: 4px solid #ffd700;
            padding: 10px 15px;
            margin: 20px 0;
        }
        .section-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .dot {
            height: 12px;
            width: 12px;
            background-color: #3498db;
            border-radius: 50%;
            display: inline-block;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
        });
    </script>
</head>
<body>
    <h1>MindBack Backend Visualization</h1>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>Directory Structure</h2>
        </div>
        <div class="mermaid">
        flowchart LR
            Backend("Backend") --> Main("main.py")
            Backend --> ReqTxt("requirements.txt")
            Backend --> ApiDir("api/")
            Backend --> AppDir("app/")
            Backend --> LogsDir("logs/")
            Backend --> PromptLib("Prompt_library/")
            Backend --> Data("data/")

            ApiDir --> ApiConfig("config/")
            ApiDir --> ApiModels("models/")
            ApiDir --> ApiRoutes("routes/")
            ApiDir --> ApiSchemas("schemas/")
            ApiDir --> ApiServices("services/")

            AppDir --> AppMain("main.py")
            AppDir --> AppRouters("routers/")
            AppDir --> AppServices("services/")

            ApiConfig --> Settings("settings.py")

            ApiRoutes --> LlmRoutes("llm.py")

            ApiServices --> OpenAI("openai_service.py")
            ApiServices --> Gemini("gemini_service.py")
            ApiServices --> Anthropic("anthropic_service.py")
            ApiServices --> Prompt("prompt_service.py")
            ApiServices --> Processor("response_processor.py")

            AppRouters --> MindmapRouter("mindmap.py")
            AppRouters --> OldLlmRouter("llm.py")

            PromptLib --> Templates("YAML templates")

            Data --> Mindmaps("mindmaps/")

            classDef main fill:#f9a825,stroke:#f57f17,stroke-width:2px
            classDef directory fill:#4caf50,stroke:#388e3c,stroke-width:1px,color:white
            classDef file fill:#bbdefb,stroke:#64b5f6,stroke-width:1px
            classDef service fill:#ce93d8,stroke:#ab47bc,stroke-width:1px
            classDef router fill:#ffcc80,stroke:#ff9800,stroke-width:1px

            class Main,AppMain main
            class ApiDir,AppDir,LogsDir,PromptLib,Data directory
            class ReqTxt,Settings,ApiModels,ApiSchemas file
            class OpenAI,Gemini,Anthropic,Prompt,Processor service
            class LlmRoutes,MindmapRouter,OldLlmRouter router
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>API Initialization Flow</h2>
        </div>
        <div class="mermaid">
        sequenceDiagram
            participant Script as run_setup.ps1
            participant Venv as Python venv
            participant Main as backend/main.py
            participant Middleware as CORS Middleware
            participant Settings as api/config/settings.py
            participant Routers as Imported Routers

            Script->>Venv: Activate virtual environment
            Script->>Venv: Install dependencies
            Script->>Main: Start uvicorn (main:app)
            Main->>Settings: Load .env & API keys
            Main->>Middleware: Configure CORS
            Main->>Routers: Import and register routers
            Routers->>Main: Include routes in FastAPI app
            Main-->>Script: Server running
            Note over Main,Script: Backend available at http://127.0.0.1:8000
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>LLM Request Flow</h2>
        </div>
        <div class="mermaid">
        flowchart TD
            Client([Frontend Client]) -- "POST /api/llm/chat" --> Router[LLM Router]

            Router --> RequestValidation[Validate LLMChatRequest]
            RequestValidation --> PromptService[Prompt Service]

            subgraph LLM_Processing [LLM Processing Chain]
                PromptService --> LoadPrompt[Load YAML Prompt]
                LoadPrompt --> FormatPrompt[Format with Values]
                FormatPrompt --> PrepareMessages[Prepare Messages]
                PrepareMessages --> ModelSelection{Select LLM Model}

                ModelSelection -- OpenAI --> OpenAIClient[OpenAI Service]
                ModelSelection -- Gemini --> GeminiClient[Gemini Service]
                ModelSelection -- Claude --> AnthropicClient[Anthropic Service]

                OpenAIClient --> FunctionCalling[Apply Function Calling]
                GeminiClient --> FunctionCalling
                AnthropicClient --> FunctionCalling

                FunctionCalling --> Response[Get LLM Response]
                Response --> ProcessResponse[Process Response]
            end

            ProcessResponse --> Validation[Validate MBCP Structure]
            Validation --> TransformResponse[Transform to Frontend Format]
            TransformResponse --> Client

            classDef service fill:#ce93d8,stroke:#ab47bc,stroke-width:1px;
            classDef client fill:#81c784,stroke:#4caf50,stroke-width:1px;
            classDef router fill:#ffcc80,stroke:#ff9800,stroke-width:1px;
            classDef process fill:#90caf9,stroke:#42a5f5,stroke-width:1px;
            classDef decision fill:#fff59d,stroke:#ffee58,stroke-width:1px;

            class Client client;
            class Router router;
            class ModelSelection decision;
            class OpenAIClient,GeminiClient,AnthropicClient service;
            class RequestValidation,PromptService,LoadPrompt,FormatPrompt,PrepareMessages,FunctionCalling,Response,ProcessResponse,Validation,TransformResponse process;
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>Module Dependency Graph</h2>
        </div>
        <div class="mermaid">
        graph TD
            MainEntry["main.py (Entry)"] --> AppRouters["app.routers"]
            MainEntry --> ApiRoutes["api.routes"]
            MainEntry --> CORSMiddleware["CORS Middleware"]
            MainEntry --> Logging["Logging Setup"]
            MainEntry --> DotEnv["dotenv.load_dotenv"]

            ApiRoutes --> LlmRouter["llm.py router"]
            AppRouters --> MindMapRouter["mindmap.py router"]

            LlmRouter --> OpenAIService["openai_service.py"]
            LlmRouter --> GeminiService["gemini_service.py"]
            LlmRouter --> AnthropicService["anthropic_service.py"]
            LlmRouter --> PromptService["prompt_service.py"]
            LlmRouter --> ResponseProcessor["response_processor.py"]
            LlmRouter --> MbcpSchemas["mbcp_schemas.py"]
            LlmRouter --> MbcpModels["mbcp_models.py"]
            LlmRouter --> Settings["settings.py"]

            OpenAIService --> Settings
            GeminiService --> Settings
            AnthropicService --> Settings

            MindMapRouter --> JSONLib["json"]
            MindMapRouter --> OSLib["os"]

            PromptService --> YAMLLib["yaml"]

            Settings --> DotEnv

            classDef entry fill:#f9a825,stroke:#f57f17,stroke-width:2px;
            classDef router fill:#ffcc80,stroke:#ff9800,stroke-width:1px;
            classDef service fill:#ce93d8,stroke:#ab47bc,stroke-width:1px;
            classDef library fill:#a5d6a7,stroke:#66bb6a,stroke-width:1px;
            classDef config fill:#90caf9,stroke:#42a5f5,stroke-width:1px;

            class MainEntry entry;
            class LlmRouter,MindMapRouter router;
            class OpenAIService,GeminiService,AnthropicService,PromptService,ResponseProcessor service;
            class JSONLib,OSLib,YAMLLib,DotEnv,CORSMiddleware,Logging library;
            class Settings,MbcpModels,MbcpSchemas config;
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>MindMap Data Flow</h2>
        </div>
        <div class="mermaid">
        flowchart TD
            ClientReq([Client Request]) --> MindmapSave["POST /save (MindMap Router)"]
            MindmapSave --> Validation[Validate Project Data]
            Validation --> CreateFile[Create JSON File]
            CreateFile --> FilePath["DATA_DIR/project_id.json"]
            FilePath --> Response[Return Success Response]
            Response --> Client([Client Response])

            LoadReq([Load Request]) --> MindmapLoad["GET /load/{project_id} (MindMap Router)"]
            MindmapLoad --> CheckFile[Check If File Exists]
            CheckFile --> ReadFile[Read JSON File]
            ReadFile --> ParseJson[Parse JSON Data]
            ParseJson --> ReturnData[Return Project Data]
            ReturnData --> Client

            classDef client fill:#81c784,stroke:#4caf50,stroke-width:1px;
            classDef endpoint fill:#ffcc80,stroke:#ff9800,stroke-width:1px;
            classDef process fill:#90caf9,stroke:#42a5f5,stroke-width:1px;
            classDef file fill:#ce93d8,stroke:#ab47bc,stroke-width:1px;

            class ClientReq,LoadReq,Client client;
            class MindmapSave,MindmapLoad endpoint;
            class Validation,CreateFile,CheckFile,ReadFile,ParseJson,ReturnData,Response process;
            class FilePath file;
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>Potential Issue Areas</h2>
        </div>
        <div class="mermaid">
        graph TD
            Backend[Backend Issues] --> EnvVars["Environment Variables"]
            Backend --> ModuleStructure["Module Structure"]
            Backend --> PathConfig["Path Configuration"]
            Backend --> DuplicateRouters["Duplicate Routers"]
            Backend --> LlmIntegration["LLM Integration"]

            EnvVars --> MissingKeys["Missing API Keys"]
            EnvVars --> WrongPaths[".env in Wrong Location"]

            ModuleStructure --> AppApiConfusion["app/ vs api/ Confusion"]
            ModuleStructure --> ImportErrors["Import Errors"]

            PathConfig --> ComplexPaths["Complex Path Manipulations"]
            PathConfig --> DirStructureChange["Directory Structure Changes"]

            DuplicateRouters --> EndpointConflicts["Endpoint Conflicts"]
            DuplicateRouters --> ImportConfusion["Module Import Confusion"]

            LlmIntegration --> ProviderFailure["LLM Provider Failure"]
            LlmIntegration --> SchemaValidation["Response Schema Validation"]

            classDef issue fill:#ef9a9a,stroke:#e57373,stroke-width:1px;
            classDef mainIssue fill:#f44336,stroke:#d32f2f,stroke-width:2px,color:white;

            class Backend mainIssue;
            class EnvVars,ModuleStructure,PathConfig,DuplicateRouters,LlmIntegration issue;
            class MissingKeys,WrongPaths,AppApiConfusion,ImportErrors,ComplexPaths,DirStructureChange,EndpointConflicts,ImportConfusion,ProviderFailure,SchemaValidation issue;
        </div>
    </div>

    <div class="note">
        <p><strong>Note:</strong> This visualization represents the current state of the backend based on code analysis.
        The structure reveals potential issues with duplicate routers and confusing module organization. The most critical
        components are the LLM service integrations and the environment variable configuration.</p>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>Two-Step YAML Prompting Process</h2>
        </div>
        <div class="mermaid">
        flowchart TD
            ClientReq("Client Request") --> LLMRouter("LLM Router")
            LLMRouter --> RequestValidation("Validate Request")
            RequestValidation --> PromptType

            subgraph YAMLFlow["YAML Prompt Processing"]
                PromptType{{"prompt_type?"}}

                %% First branch - No prompt_type
                PromptType -- "No" --> DefaultPrompt("Use initiation_prompt2.yaml")
                DefaultPrompt --> LoadIntentPrompt("Load YAML prompt")
                LoadIntentPrompt --> FormatIntent("Format with values")
                FormatIntent --> PrepMessages1("Prepare messages")
                PrepMessages1 --> CallLLM1("Call OpenAI API")
                CallLLM1 --> ProcessResp1("Process response")

                %% Second branch - With prompt_type
                PromptType -- "Yes" --> ProvidedPrompt("Use specified prompt_type")
                ProvidedPrompt --> LoadDirectPrompt("Load specified YAML")
                LoadDirectPrompt --> FormatDirect("Format with values")
                FormatDirect --> PrepMessages2("Prepare messages")
                PrepMessages2 --> CallLLM2("Call OpenAI API")
                CallLLM2 --> ProcessResp2("Process response")

                %% Intent check and routing
                ProcessResp1 --> IntentCheck{{"What is the intent?"}}
                ProcessResp2 --> IntentCheck

                %% Factual or miscellaneous intent path
                IntentCheck -- "factual/miscellaneous" --> ReturnDirect("Return direct response")

                %% Teleological intent path
                IntentCheck -- "teleological" --> TeleologicalCall("Make teleological LLM call")
                TeleologicalCall --> LoadMindmapPrompt("Load initiate_mindmap2.yaml")
                LoadMindmapPrompt --> AddMindmapValues("Replace placeholders")
                AddMindmapValues --> FormatMindmap("Format mindmap template")
                FormatMindmap --> CallMindmapLLM("Call OpenAI with mindmap")
                CallMindmapLLM --> ProcessMindmapResponse("Process mindmap response")
                ProcessMindmapResponse --> ReturnMindmap("Return mindmap MBCP")

                %% Exploratory intent path
                IntentCheck -- "exploratory" --> ExploratoryCall("Make exploratory LLM call")
                ExploratoryCall --> LoadChatforkPrompt("Load initiate_chatfork2.yaml")
                LoadChatforkPrompt --> AddChatforkValues("Replace placeholders")
                AddChatforkValues --> FormatChatfork("Format chatfork template")
                FormatChatfork --> CallChatforkLLM("Call OpenAI with chatfork")
                CallChatforkLLM --> ProcessChatforkResponse("Process chatfork response")
                ProcessChatforkResponse --> ReturnChatfork("Return chatfork MBCP")

                %% Instantiation intent path
                IntentCheck -- "instantiation" --> CheckTemplateType{{"Which template type?"}}
                CheckTemplateType --> LoadTemplateRouter("Load instantiation_template_router.py")
                LoadTemplateRouter --> SelectTemplate("Select appropriate template")
                SelectTemplate --> LoadSelectedPrompt("Load selected prompt template")
                LoadSelectedPrompt --> AddTemplateValues("Replace placeholders")
                AddTemplateValues --> FormatTemplate("Format template")
                FormatTemplate --> CallTemplateLLM("Call OpenAI with template")
                CallTemplateLLM --> ProcessTemplateResponse("Process template response")
                ProcessTemplateResponse --> ReturnTemplate("Return template MBCP")
            end

            ReturnDirect --> Client("Client")
            ReturnMindmap --> Client
            ReturnChatfork --> Client
            ReturnTemplate --> Client

            classDef problem fill:#e57373,stroke:#c62828,stroke-width:2px
            classDef process fill:#90caf9,stroke:#42a5f5,stroke-width:1px
            classDef decision fill:#fff59d,stroke:#ffee58,stroke-width:1px
            classDef client fill:#81c784,stroke:#4caf50,stroke-width:1px

            class ClientReq,Client client
            class PromptType,IntentCheck,CheckTemplateType decision
            class AddMindmapValues,AddChatforkValues,AddTemplateValues problem
            class LLMRouter,RequestValidation,DefaultPrompt,ProvidedPrompt,LoadIntentPrompt,LoadDirectPrompt,FormatIntent,FormatDirect,PrepMessages1,PrepMessages2,CallLLM1,CallLLM2,ProcessResp1,ProcessResp2 process
            class TeleologicalCall,LoadMindmapPrompt,FormatMindmap,CallMindmapLLM,ProcessMindmapResponse,ReturnMindmap process
            class ExploratoryCall,LoadChatforkPrompt,FormatChatfork,CallChatforkLLM,ProcessChatforkResponse,ReturnChatfork process
            class LoadTemplateRouter,SelectTemplate,LoadSelectedPrompt,FormatTemplate,CallTemplateLLM,ProcessTemplateResponse,ReturnTemplate,ReturnDirect process
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>YAML Prompting Logic Details</h2>
        </div>
        <div class="mermaid">
        flowchart LR
            subgraph FormatFunction["Format Function"]
                FormatProc("format_prompt_with_values()")
                CheckEmpty{{"Empty data?"}}
                ReturnEmptyDict("Return {}")
                ReplaceProcess("Process recursively")
                ProcessDict("Process dictionary")
                ProcessList("Process list items")
                ProcessStr("Process strings")
                SingleBrace("Replace {value}")
                DoubleBrace("Replace {{value}}")
                CompleteReplace("Format data")
                LogDetails("Log details")
                ReturnFormatted("Return formatted")

                FormatProc --> CheckEmpty
                CheckEmpty -- "Yes" --> ReturnEmptyDict
                CheckEmpty -- "No" --> ReplaceProcess
                ReplaceProcess --> ProcessDict
                ReplaceProcess --> ProcessList
                ReplaceProcess --> ProcessStr
                ProcessStr --> SingleBrace
                ProcessStr --> DoubleBrace
                SingleBrace --> CompleteReplace
                DoubleBrace --> CompleteReplace
                CompleteReplace --> LogDetails
                LogDetails --> ReturnFormatted
            end

            subgraph PrepareFunction["Prepare Function"]
                PrepareMsg("prepare_messages()")
                InitResult("Initialize result")
                CheckContentPrompt{{"content_prompt?"}}
                UseFormatted("Use formatted")
                UseUserPrompt("Use original")
                CheckSystemRole{{"system_role?"}}
                OverrideSystem("Override system")
                UseOrigSystem("Use original")
                ReturnPrepared("Return messages")

                PrepareMsg --> InitResult
                InitResult --> CheckContentPrompt
                CheckContentPrompt -- "Yes" --> UseFormatted
                CheckContentPrompt -- "No" --> UseUserPrompt
                UseFormatted --> CheckSystemRole
                UseUserPrompt --> CheckSystemRole
                CheckSystemRole -- "Yes" --> OverrideSystem
                CheckSystemRole -- "No" --> UseOrigSystem
                OverrideSystem --> ReturnPrepared
                UseOrigSystem --> ReturnPrepared
            end

            classDef function fill:#ce93d8,stroke:#ab47bc,stroke-width:1px
            classDef process fill:#90caf9,stroke:#42a5f5,stroke-width:1px
            classDef decision fill:#fff59d,stroke:#ffee58,stroke-width:1px
            classDef problem fill:#e57373,stroke:#c62828,stroke-width:2px

            class FormatProc,PrepareMsg function
            class CheckEmpty,CheckContentPrompt,CheckSystemRole decision
            class ReturnEmptyDict,SingleBrace,DoubleBrace problem
            class ReplaceProcess,ProcessDict,ProcessList,ProcessStr,CompleteReplace,LogDetails,ReturnFormatted,InitResult,UseFormatted,UseUserPrompt,OverrideSystem,UseOrigSystem,ReturnPrepared process
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>Potential Issues with YAML Prompting</h2>
        </div>
        <div class="mermaid">
        flowchart LR
            YAMLIssues("YAML Issues")
            PathIssues("Path Issues")
            YAMLStructure("YAML Structure")
            VariableReplace("Variable Replacement")
            PromptChain("Prompt Chaining")
            LLMIntegration("LLM Integration")

            WrongPath("Wrong Path")
            MissingFiles("Missing Files")
            MalformedYAML("Invalid Syntax")
            MissingFields("Missing Fields")
            InconsistentFormat("Inconsistent Format")
            UnreplacedVars("Unreplaced Variables")
            MissingValues("Missing Values")
            FormatMismatch("Format Mismatch")
            IntentFail("Intent Classification Fails")
            ValuePropagation("Value Propagation Issues")
            TemplateIssues("Template Issues")
            FunctionIssue("Function Schema Issues")
            MessageIssue("Message Structure")
            ResponseHandling("Response Processing")

            YAMLIssues --> PathIssues
            YAMLIssues --> YAMLStructure
            YAMLIssues --> VariableReplace
            YAMLIssues --> PromptChain
            YAMLIssues --> LLMIntegration

            PathIssues --> WrongPath
            PathIssues --> MissingFiles

            YAMLStructure --> MalformedYAML
            YAMLStructure --> MissingFields
            YAMLStructure --> InconsistentFormat

            VariableReplace --> UnreplacedVars
            VariableReplace --> MissingValues
            VariableReplace --> FormatMismatch

            PromptChain --> IntentFail
            PromptChain --> ValuePropagation
            PromptChain --> TemplateIssues

            LLMIntegration --> FunctionIssue
            LLMIntegration --> MessageIssue
            LLMIntegration --> ResponseHandling

            classDef issue fill:#ef9a9a,stroke:#e57373,stroke-width:1px
            classDef mainIssue fill:#f44336,stroke:#d32f2f,stroke-width:2px,color:white
            classDef mostLikely fill:#d50000,stroke:#b71c1c,stroke-width:3px,color:white

            class YAMLIssues mainIssue
            class PathIssues,YAMLStructure,VariableReplace,PromptChain,LLMIntegration issue
            class ValuePropagation,UnreplacedVars,IntentFail mostLikely
        </div>
    </div>

    <div class="note">
        <p><strong>Note on Current Two-Step YAML Prompting:</strong> The current implementation includes an explicit two-step process in the LLM router. First, it processes the user query either with the specified prompt_type or defaults to "initiation_prompt2.yaml" for intent classification. Then, for teleological intents, it makes a second LLM call with "initiate_mindmap2.yaml" to generate a structured mindmap. The most likely issues are with variable replacement (the system logs show careful attention to this) and value propagation between the two steps. The system explicitly replaces placeholders with the actual topic and user query, logs the replacements, and then formats both the system prompt and content prompt before making the second LLM call.</p>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>Current Implementation vs. Ideal Workflow</h2>
        </div>
        <div class="mermaid">
        flowchart TD
            ClientReq("Client Request") --> LLMRouter("LLM Router")
            LLMRouter --> RequestValidation("Validate Request")
            RequestValidation --> PromptType

            subgraph CurrentImplementation["Current Implementation (Actual Code)"]
                PromptType{{"prompt_type?"}}

                %% First branch - No prompt_type
                PromptType -- "No" --> DefaultPrompt("Use initiation_prompt2.yaml")
                DefaultPrompt --> LoadIntentPrompt("Load YAML prompt")
                LoadIntentPrompt --> FormatIntent("Format with values")
                FormatIntent --> PrepMessages1("Prepare messages")
                PrepMessages1 --> CallLLM1("Call OpenAI API")
                CallLLM1 --> ProcessResp1("Process response")

                %% Second branch - With prompt_type
                PromptType -- "Yes" --> ProvidedPrompt("Use specified prompt_type")
                ProvidedPrompt --> LoadDirectPrompt("Load specified YAML")
                LoadDirectPrompt --> FormatDirect("Format with values")
                FormatDirect --> PrepMessages2("Prepare messages")
                PrepMessages2 --> CallLLM2("Call OpenAI API")
                CallLLM2 --> ProcessResp2("Process response")

                %% Intent check and routing
                ProcessResp1 --> IntentCheck{{"What is the intent?"}}
                ProcessResp2 --> IntentCheck

                %% Non-teleological intent path (direct return)
                IntentCheck -- "factual/exploratory/instantiation/misc" --> ReturnDirect("Return direct response")

                %% Teleological intent path (implemented)
                IntentCheck -- "teleological" --> TeleologicalCall("Make teleological LLM call")
                TeleologicalCall --> LoadMindmapPrompt("Load initiate_mindmap2.yaml")
                LoadMindmapPrompt --> AddMindmapValues("Replace placeholders")
                AddMindmapValues --> FormatMindmap("Format mindmap template")
                FormatMindmap --> CallMindmapLLM("Call OpenAI with mindmap")
                CallMindmapLLM --> ProcessMindmapResponse("Process mindmap response")
                ProcessMindmapResponse --> ReturnMindmap("Return mindmap MBCP")
            end

            subgraph MissingImplementation["Missing Implementation (Gaps)"]
                %% Exploratory intent path (not implemented)
                MissingExploratory["❌ No second call for exploratory intent"] -.- LoadChatforkPrompt["Should load initiate_chatfork2.yaml"]
                LoadChatforkPrompt -.- AddChatforkValues["Should replace placeholders"]
                AddChatforkValues -.- FormatChatfork["Should format chatfork template"]
                FormatChatfork -.- CallChatforkLLM["Should call OpenAI with chatfork"]
                CallChatforkLLM -.- ProcessChatforkResponse["Should process chatfork response"]
                ProcessChatforkResponse -.- ReturnChatfork["Should return chatfork MBCP"]

                %% Instantiation intent path (not implemented)
                MissingInstantiation["❌ No second call for instantiation intent"] -.- LoadTemplateRouter["Should load instantiation_template_router.py"]
                LoadTemplateRouter -.- SelectTemplate["Should select appropriate template"]
                SelectTemplate -.- LoadSelectedPrompt["Should load selected prompt template"]
                LoadSelectedPrompt -.- AddTemplateValues["Should replace placeholders"]
                AddTemplateValues -.- FormatTemplate["Should format template"]
                FormatTemplate -.- CallTemplateLLM["Should call OpenAI with template"]
                CallTemplateLLM -.- ProcessTemplateResponse["Should process template response"]
                ProcessTemplateResponse -.- ReturnTemplate["Should return template MBCP"]
            end

            %% Connect the current implementation to the client
            ReturnDirect --> Client("Client")
            ReturnMindmap --> Client

            %% Style definitions
            classDef problem fill:#e57373,stroke:#c62828,stroke-width:2px
            classDef process fill:#90caf9,stroke:#42a5f5,stroke-width:1px
            classDef decision fill:#fff59d,stroke:#ffee58,stroke-width:1px
            classDef client fill:#81c784,stroke:#4caf50,stroke-width:1px
            classDef missing fill:#e0e0e0,stroke:#9e9e9e,stroke-width:1px,stroke-dasharray: 5 5
            classDef missingMain fill:#ffab91,stroke:#ff5722,stroke-width:2px

            class ClientReq,Client client
            class PromptType,IntentCheck decision
            class AddMindmapValues problem
            class LLMRouter,RequestValidation,DefaultPrompt,ProvidedPrompt,LoadIntentPrompt,LoadDirectPrompt,FormatIntent,FormatDirect,PrepMessages1,PrepMessages2,CallLLM1,CallLLM2,ProcessResp1,ProcessResp2 process
            class TeleologicalCall,LoadMindmapPrompt,FormatMindmap,CallMindmapLLM,ProcessMindmapResponse,ReturnMindmap,ReturnDirect process
            class LoadChatforkPrompt,AddChatforkValues,FormatChatfork,CallChatforkLLM,ProcessChatforkResponse,ReturnChatfork missing
            class LoadTemplateRouter,SelectTemplate,LoadSelectedPrompt,AddTemplateValues,FormatTemplate,CallTemplateLLM,ProcessTemplateResponse,ReturnTemplate missing
            class MissingExploratory,MissingInstantiation missingMain
        </div>
    </div>

</body>
</html>