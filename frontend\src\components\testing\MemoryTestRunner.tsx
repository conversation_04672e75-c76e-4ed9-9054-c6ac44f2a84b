/**
 * MemoryTestRunner.tsx
 * 
 * React component for running and displaying MBCP memory system tests.
 * Provides UI for Phase 1.1 validation.
 */

import React, { useState, useCallback } from 'react';
import { memoryTestingInterface, TestResult, TestSuite } from '../../core/testing/MemoryTestingInterface';

interface MemoryTestRunnerProps {
  onClose?: () => void;
}

export const MemoryTestRunner: React.FC<MemoryTestRunnerProps> = ({ onClose }) => {
  const [testResults, setTestResults] = useState<TestSuite[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [showDetails, setShowDetails] = useState<{ [suiteName: string]: boolean }>({});

  const runTests = useCallback(async () => {
    setIsRunning(true);
    setTestResults([]);
    
    try {
      console.log('🧪 Starting MBCP Memory Tests...');
      const results = await memoryTestingInterface.runAllTests();
      setTestResults(results);
      
      const summary = memoryTestingInterface.getTestSummary();
      console.log('🧪 Test Summary:', summary);
      
    } catch (error) {
      console.error('❌ Test execution failed:', error);
    } finally {
      setIsRunning(false);
    }
  }, []);

  const toggleDetails = (suiteName: string) => {
    setShowDetails(prev => ({
      ...prev,
      [suiteName]: !prev[suiteName]
    }));
  };

  const clearResults = () => {
    setTestResults([]);
    memoryTestingInterface.clearResults();
  };

  const getStatusIcon = (passed: boolean) => {
    return passed ? '✅' : '❌';
  };

  const getStatusColor = (passed: boolean) => {
    return passed ? '#4CAF50' : '#F44336';
  };

  const summary = testResults.length > 0 ? memoryTestingInterface.getTestSummary() : null;

  return (
    <div style={{
      position: 'fixed',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      width: '800px',
      maxHeight: '80vh',
      backgroundColor: 'white',
      border: '2px solid #333',
      borderRadius: '8px',
      padding: '20px',
      zIndex: 10000,
      overflow: 'auto',
      fontFamily: 'Arial, sans-serif'
    }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '20px',
        borderBottom: '1px solid #ccc',
        paddingBottom: '10px'
      }}>
        <h2 style={{ margin: 0, color: '#333' }}>
          🧪 MBCP Memory System Tests
        </h2>
        {onClose && (
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '20px',
              cursor: 'pointer',
              color: '#666'
            }}
          >
            ✕
          </button>
        )}
      </div>

      {/* Controls */}
      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={runTests}
          disabled={isRunning}
          style={{
            backgroundColor: isRunning ? '#ccc' : '#2196F3',
            color: 'white',
            border: 'none',
            padding: '10px 20px',
            borderRadius: '4px',
            cursor: isRunning ? 'not-allowed' : 'pointer',
            marginRight: '10px'
          }}
        >
          {isRunning ? '🔄 Running Tests...' : '▶️ Run All Tests'}
        </button>
        
        {testResults.length > 0 && (
          <button
            onClick={clearResults}
            style={{
              backgroundColor: '#FF9800',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            🗑️ Clear Results
          </button>
        )}
      </div>

      {/* Summary */}
      {summary && (
        <div style={{
          backgroundColor: '#f5f5f5',
          padding: '15px',
          borderRadius: '4px',
          marginBottom: '20px',
          border: `2px solid ${summary.passedSuites === summary.totalSuites ? '#4CAF50' : '#FF9800'}`
        }}>
          <h3 style={{ margin: '0 0 10px 0', color: '#333' }}>📊 Test Summary</h3>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '10px' }}>
            <div>
              <strong>Test Suites:</strong> {summary.passedSuites}/{summary.totalSuites} passed
            </div>
            <div>
              <strong>Individual Tests:</strong> {summary.passedTests}/{summary.totalTests} passed
            </div>
          </div>
          <div style={{ 
            marginTop: '10px', 
            fontSize: '18px',
            color: summary.passedSuites === summary.totalSuites ? '#4CAF50' : '#FF9800'
          }}>
            <strong>
              {summary.passedSuites === summary.totalSuites ? 
                '🎉 All test suites passed!' : 
                '⚠️ Some tests failed - check details below'
              }
            </strong>
          </div>
        </div>
      )}

      {/* Test Results */}
      {testResults.length > 0 && (
        <div>
          <h3 style={{ color: '#333', marginBottom: '15px' }}>📋 Test Results</h3>
          
          {testResults.map((suite, index) => (
            <div
              key={index}
              style={{
                border: `2px solid ${getStatusColor(suite.passed)}`,
                borderRadius: '4px',
                marginBottom: '15px',
                overflow: 'hidden'
              }}
            >
              {/* Suite Header */}
              <div
                onClick={() => toggleDetails(suite.suiteName)}
                style={{
                  backgroundColor: suite.passed ? '#E8F5E8' : '#FFEBEE',
                  padding: '15px',
                  cursor: 'pointer',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  borderBottom: showDetails[suite.suiteName] ? '1px solid #ccc' : 'none'
                }}
              >
                <div>
                  <span style={{ fontSize: '18px', marginRight: '10px' }}>
                    {getStatusIcon(suite.passed)}
                  </span>
                  <strong>{suite.suiteName}</strong>
                  <span style={{ marginLeft: '10px', color: '#666' }}>
                    ({suite.summary})
                  </span>
                </div>
                <span style={{ fontSize: '12px', color: '#666' }}>
                  {showDetails[suite.suiteName] ? '▼' : '▶'}
                </span>
              </div>

              {/* Suite Details */}
              {showDetails[suite.suiteName] && (
                <div style={{ padding: '15px', backgroundColor: '#fafafa' }}>
                  {suite.tests.map((test, testIndex) => (
                    <div
                      key={testIndex}
                      style={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        marginBottom: '10px',
                        padding: '8px',
                        backgroundColor: 'white',
                        borderRadius: '4px',
                        border: `1px solid ${getStatusColor(test.passed)}`
                      }}
                    >
                      <span style={{ marginRight: '10px', fontSize: '16px' }}>
                        {getStatusIcon(test.passed)}
                      </span>
                      <div style={{ flex: 1 }}>
                        <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                          {test.testName}
                        </div>
                        <div style={{ 
                          color: test.passed ? '#4CAF50' : '#F44336',
                          fontSize: '14px'
                        }}>
                          {test.message}
                        </div>
                        {test.data && (
                          <details style={{ marginTop: '8px' }}>
                            <summary style={{ 
                              cursor: 'pointer', 
                              color: '#666',
                              fontSize: '12px'
                            }}>
                              View Test Data
                            </summary>
                            <pre style={{
                              backgroundColor: '#f0f0f0',
                              padding: '8px',
                              borderRadius: '4px',
                              fontSize: '11px',
                              overflow: 'auto',
                              maxHeight: '200px',
                              marginTop: '4px'
                            }}>
                              {JSON.stringify(test.data, null, 2)}
                            </pre>
                          </details>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Loading State */}
      {isRunning && (
        <div style={{
          textAlign: 'center',
          padding: '40px',
          color: '#666'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '10px' }}>🔄</div>
          <div>Running MBCP Memory Tests...</div>
          <div style={{ fontSize: '12px', marginTop: '5px' }}>
            This may take a few seconds
          </div>
        </div>
      )}

      {/* Empty State */}
      {!isRunning && testResults.length === 0 && (
        <div style={{
          textAlign: 'center',
          padding: '40px',
          color: '#666'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '15px' }}>🧪</div>
          <div style={{ fontSize: '18px', marginBottom: '10px' }}>
            Ready to test MBCP Memory System
          </div>
          <div style={{ fontSize: '14px' }}>
            Click "Run All Tests" to validate Phase 1.1 implementation
          </div>
        </div>
      )}
    </div>
  );
};

export default MemoryTestRunner;
