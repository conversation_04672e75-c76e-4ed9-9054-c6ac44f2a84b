import { useState, useCallback, useEffect } from 'react';
import { Direction } from '../types';
import { useMindMapStore } from '../core/state/MindMapStore';
import { CreateConnectionInput, Connection } from '../core/models/Connection';
import { Node } from '../core/models/Node';

interface UseMindMapCoreOptions {
  initialDirection?: Direction;
  onNodeSelect?: (nodeId: string | null) => void;
  onNodeUpdate?: (node: Node) => void;
  onConnectionUpdate?: (connection: Connection) => void;
}

export function useMindMapCore(options: UseMindMapCoreOptions = {}) {
  // Use selectors to get state and actions
  const [direction, setDirection] = useState<Direction>(options.initialDirection || 'right');

  // Get nodes and connections from store using selectors
  const nodes = useMindMapStore(state => Object.values(state.nodes));
  const connections = useMindMapStore(state => state.connections);
  const selectedNodeId = useMindMapStore(state => state.selectedNodeId);

  // Handle node selection
  const handleNodeSelect = useCallback((nodeId: string | null) => {
    const selectNode = useMindMapStore.getState().selectNode;
    selectNode(nodeId);
    if (options.onNodeSelect) {
      options.onNodeSelect(nodeId);
    }
  }, [options]);

  // Add a new node
  const addNode = useCallback((parentId: string, direction: number) => {
    console.log('[DEBUG] useMindMapCore - Adding node to parent:', parentId, 'with direction:', direction);
    const addNodeFn = useMindMapStore.getState().addNode;
    return addNodeFn(parentId, direction);
  }, []);

  // Update a node
  const updateNode = useCallback((id: string, updates: Partial<Node>) => {
    console.log('[DEBUG] useMindMapCore - Updating node:', id);
    const updateNodeFn = useMindMapStore.getState().updateNode;
    updateNodeFn(id, updates);
    if (options.onNodeUpdate) {
      const nodes = useMindMapStore.getState().nodes;
      const node = nodes[id];
      if (node) {
        options.onNodeUpdate(node);
      }
    }
  }, [options]);

  // Add a connection
  const addConnection = useCallback((connectionInput: CreateConnectionInput) => {
    console.log('[DEBUG] useMindMapCore - Adding connection from', connectionInput.from, 'to', connectionInput.to);
    const addConnectionFn = useMindMapStore.getState().addConnection;
    addConnectionFn(connectionInput);
  }, []);

  // Update a connection
  const updateConnection = useCallback((id: string, updates: Partial<Connection>) => {
    console.log('[DEBUG] useMindMapCore - Updating connection:', id);
    const updateConnectionFn = useMindMapStore.getState().updateConnection;
    updateConnectionFn(id, updates);
    if (options.onConnectionUpdate) {
      const connections = useMindMapStore.getState().connections;
      const connection = connections.find(c => c.id === id);
      if (connection) {
        options.onConnectionUpdate(connection);
      }
    }
  }, [options]);

  // Delete a node
  const deleteNode = useCallback((nodeId: string) => {
    console.log('[DEBUG] useMindMapCore - Deleting node:', nodeId);
    const deleteNodeFn = useMindMapStore.getState().deleteNode;
    deleteNodeFn(nodeId);
  }, []);

  // Handle direction change
  const changeDirection = useCallback((newDirection: Direction) => {
    console.log('[DEBUG] useMindMapCore - Changing direction to:', newDirection);
    setDirection(newDirection);
    // If there's an auto-layout function, call it here
    const autoLayoutFn = useMindMapStore.getState().autoLayout;
    autoLayoutFn();
  }, []);

  // Auto-layout the mindmap
  const autoLayout = useCallback(() => {
    console.log('[DEBUG] useMindMapCore - Auto-layouting mindmap');
    const autoLayoutFn = useMindMapStore.getState().autoLayout;
    autoLayoutFn();
  }, []);

  // Initialize with root node if needed
  useEffect(() => {
    // Only perform auto-layout if nodes already exist
    const nodes = useMindMapStore.getState().nodes;
    if (Object.keys(nodes).length > 0) {
      autoLayout();
    }
  }, [autoLayout]);

  return {
    nodes,
    connections,
    selectedNodeId,
    direction,
    addNode,
    updateNode,
    deleteNode,
    addConnection,
    updateConnection,
    handleNodeSelect,
    changeDirection,
    autoLayout,
  };
}