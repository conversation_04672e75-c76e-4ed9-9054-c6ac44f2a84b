// Placeholder file for: frontend\src\components\MindMap\core\operations\NodeOperations.ts

import { useMindMapStore } from '../state/MindMapStore';
import { Node } from '../models/Node';

export const defaultNodeValues = {
  width: 100,
  height: 50,
  fontSize: 12,
  backgroundColor: '#f8fafc',
  borderColor: '#94a3b8',
  textColor: '#1e293b'
};

export const handleTabKeyPress = (nodeId: string, shiftKey: boolean): void => {
  const store = useMindMapStore.getState();
  
  if (shiftKey) {
    // Delete node on Shift+Tab
    store.deleteNode(nodeId);
  } else {
    // Add child node on Tab
    store.addChildNode(nodeId, false);
  }
};

export const calculateNodePosition = (
  parentNode: Node,
  direction: number,
  spacing: number = 150
): { x: number; y: number } => {
  const angle = (direction * Math.PI) / 180;
  return {
    x: parentNode.x + Math.cos(angle) * spacing,
    y: parentNode.y + Math.sin(angle) * spacing
  };
};
