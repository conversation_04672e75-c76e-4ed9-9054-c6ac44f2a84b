# this file describe the chatfork tool, its parameters and the expected outcome structure
tool_id: chatfork
name: "ChatFork"
purpose: "Break down complex explanations into multiple structured points with interactive discussion capabilities."
used_for:
  - Conceptual Inquiry
  - Educational Explanations
  - Topic Breakdowns
input_type: "Topic requiring structured breakdown"
output_type: "Interactive forkable discussion structure"
llm_prompt: |
  You are an expert educational content creator. Given the topic: "{user_input}",
  create a comprehensive explanation structured as follows:
  
  1. A detailed title and main explanation of the topic
  2. Between 4-6 key sections, each containing:
     - An informative heading
     - A detailed explanation (3-5 sentences minimum)
     - 2-3 relevant subpoints with their own content
     - 1-2 discussion points or questions that prompt deeper exploration
  
  YOU MUST FORMAT YOUR RESPONSE AS VALID JSON with this exact structure:
  ```json
  {
    "title": "Topic title",
    "main_explanation": "Overview explanation (3-5 sentences)",
    "sections": [
      {
        "id": "section_1",
        "heading": "Section heading",
        "content": "Detailed explanation (3-5 sentences)",
        "subpoints": [
          {
            "id": "section_1.1",
            "title": "Subpoint title",
            "content": "Subpoint explanation (2-3 sentences)"
          }
        ],
        "discussion_points": [
          {
            "question": "Follow-up question?",
            "context": "Additional context about why this question matters"
          }
        ]
      }
    ]
  }
  ```
  
  IMPORTANT REQUIREMENTS:
  - Do NOT include any markdown backticks around the JSON
  - Include ALL fields exactly as shown with NO placeholders
  - Ensure ALL sections have subpoints and discussion_points
  - Make sure each id follows the pattern shown (e.g., "section_1", "section_1.1")
  - Write clear, informative content for each field

return_structure:
  format: "json"
  schema:
    chatfork:
      title: string  # Topic title
      main_explanation: string  # Overview explanation
      sections:
        - id: string  # Unique identifier (e.g., "section_1")
          heading: string  # Section heading
          content: string  # Detailed explanation
          subpoints:
            - id: string  # Unique identifier (e.g., "section_1.1")
              title: string  # Subpoint title
              content: string  # Subpoint explanation
          discussion_points:
            - question: string  # Follow-up question
              context: string  # Additional context for discussion
  validation: "Must be a valid JSON object with complete section structure and discussion points."
