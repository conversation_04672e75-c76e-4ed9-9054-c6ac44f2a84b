/**
 * MindSheetTabs Component Styles
 */

.mindsheet-tabs {
  display: flex;
  overflow-x: auto;
  white-space: nowrap;
  z-index: var(--z-index-mindsheet-tabs); /* Using CSS variable for z-index */
  position: fixed; /* Fixed position to ensure it stays in place */
  bottom: 40px; /* Position above the footer */
  left: 0;
  right: 0;
  height: 30px;
  background-color: #f0f0f0;
  border-top: 1px solid #ddd;
  pointer-events: auto; /* Ensure clicks are captured */
  justify-content: flex-start; /* Align tabs to the left */
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1); /* Add subtle shadow for better visibility */
}

.mindsheet-tab {
  padding: 5px 16px;
  border-right: 1px solid #ddd;
  cursor: pointer;
  user-select: none;
  display: inline-flex;
  align-items: center;
  min-width: 100px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 13px;
  color: #666;
  transition: all 0.2s ease;
}

.mindsheet-tab:hover {
  background-color: #f8f8f8;
}

.mindsheet-tab.active {
  background-color: #fff;
  border-bottom: 2px solid #3498db;
  font-weight: bold;
  color: #333;
}

/* Content type specific styling - icons removed */

/* Subtle animation for tab activation */
@keyframes tabActivate {
  0% { border-bottom-color: transparent; }
  100% { border-bottom-color: #3498db; }
}

.mindsheet-tab.active {
  animation: tabActivate 0.3s ease;
}

/* Control buttons in the tabs row */
.mindsheet-controls {
  display: flex;
  margin-left: auto; /* Push to the right */
  gap: 8px;
  padding-right: 10px;
}

.mindsheet-control-button {
  padding: 5px 12px;
  background-color: #f1f1f1;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 13px;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s;
  height: 26px;
  margin: 2px 0;
  display: flex;
  align-items: center;
}

.mindsheet-control-button:hover {
  background-color: #e0e0e0;
}
