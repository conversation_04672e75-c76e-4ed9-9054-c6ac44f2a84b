<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindBack Memory Architecture</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #333;
        }
        .architecture-diagram {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin: 30px 0;
        }
        .layer {
            display: flex;
            justify-content: space-between;
            gap: 20px;
        }
        .component {
            flex: 1;
            padding: 15px;
            border-radius: 6px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            position: relative;
        }
        .component h3 {
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
        }
        .component ul {
            padding-left: 20px;
        }
        .component li {
            margin-bottom: 5px;
        }
        .frontend {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
        }
        .backend {
            background-color: #e8f5e9;
            border: 1px solid #c8e6c9;
        }
        .storage {
            background-color: #fff3e0;
            border: 1px solid #ffe0b2;
        }
        .arrow {
            position: absolute;
            width: 20px;
            height: 20px;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            font-size: 20px;
            color: #757575;
        }
        .data-flow {
            margin-top: 40px;
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 6px;
            border: 1px dashed #ccc;
        }
        .data-flow h3 {
            margin-top: 0;
        }
        .flow-diagram {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            position: relative;
        }
        .flow-step {
            width: 150px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 10px;
            border-radius: 6px;
            position: relative;
            z-index: 1;
        }
        .flow-arrow {
            position: absolute;
            top: 40px;
            left: 0;
            right: 0;
            height: 2px;
            background-color: #757575;
            z-index: 0;
        }
        .flow-arrow::after {
            content: ">";
            position: absolute;
            right: -5px;
            top: -10px;
            font-size: 20px;
            color: #757575;
        }
        .user-action {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
        }
        .state-update {
            background-color: #e8f5e9;
            border: 1px solid #c8e6c9;
        }
        .persistence {
            background-color: #fff3e0;
            border: 1px solid #ffe0b2;
        }
        .restoration {
            background-color: #f3e5f5;
            border: 1px solid #e1bee7;
        }
        .code-example {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            margin: 20px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MindBack Memory Architecture</h1>
        <p>This diagram illustrates how memory is structured in the MindBack application and how it interoperates with the Project Manager.</p>

        <div class="architecture-diagram">
            <div class="layer">
                <div class="component frontend">
                    <h3>Frontend State Management</h3>
                    <ul>
                        <li><strong>MindMapStore (Zustand)</strong>: Global state for nodes, connections, and UI</li>
                        <li><strong>ChatMemoryService</strong>: Manages chat history and context</li>
                        <li><strong>RegistrationManager</strong>: Tracks user and system events</li>
                    </ul>
                    <div class="arrow">↓</div>
                </div>
            </div>

            <div class="layer">
                <div class="component storage">
                    <h3>Browser Storage</h3>
                    <ul>
                        <li><strong>localStorage</strong>: Persists projects across page refreshes</li>
                        <li>Project data stored with key: <code>mindmap_${projectName}</code></li>
                        <li>Last active project tracked for session restoration</li>
                    </ul>
                    <div class="arrow">↕</div>
                </div>
            </div>

            <div class="layer">
                <div class="component backend">
                    <h3>Backend Storage</h3>
                    <ul>
                        <li><strong>In-memory storage</strong>: Temporary storage for testing</li>
                        <li><strong>REST API</strong>: Endpoints for saving and loading mind maps</li>
                        <li>Future: Persistent database storage</li>
                    </ul>
                </div>
            </div>
        </div>

        <h2>Project Manager Integration</h2>
        <p>The Project Manager is primarily implemented through the MindMapStore, which handles project lifecycle operations:</p>

        <div class="code-example">
            <pre>// Project creation
createNewProject: (name) => {
  // Initialize project with default settings
  // Create root node
  // Register creation events
}

// Project saving
saveProject: () => {
  // Serialize current state
  // Store in localStorage
  // Update "last active project" reference
}

// Project loading
loadProject: (name) => {
  // Retrieve project data from localStorage
  // Deserialize and update global state
  // Reset selection state
}

// Session persistence
loadLastActiveProject: () => {
  // Load the last active project on startup
}</pre>
        </div>

        <div class="data-flow">
            <h3>Memory Flow During User Session</h3>
            <div class="flow-diagram">
                <div class="flow-step user-action">User Interaction</div>
                <div class="flow-step state-update">State Update</div>
                <div class="flow-step persistence">Persistence</div>
                <div class="flow-step restoration">Restoration</div>
                <div class="flow-arrow"></div>
            </div>
            <ul style="margin-top: 40px;">
                <li><strong>User Interaction</strong>: User creates/edits nodes or sends messages</li>
                <li><strong>State Update</strong>: MindMapStore and ChatMemoryService update in-memory state</li>
                <li><strong>Persistence</strong>: Project data saved to localStorage automatically or manually</li>
                <li><strong>Restoration</strong>: On page refresh, state is restored from localStorage</li>
            </ul>
        </div>

        <h2>Key Memory Components</h2>

        <h3>1. ChatMemoryService</h3>
        <p>A singleton service that manages chat history and context:</p>
        <ul>
            <li>Stores structured messages with metadata</li>
            <li>Maintains a limited history of recent messages (max 10)</li>
            <li>Tracks relevant nodes and active "hat" (thinking mode)</li>
            <li>Provides methods for message categorization and retrieval</li>
        </ul>

        <h3>2. MindMapStore</h3>
        <p>A Zustand-based global state store:</p>
        <ul>
            <li>Manages nodes, connections, and selection state</li>
            <li>Handles project saving, loading, and exporting</li>
            <li>Maintains UI state like scale and position</li>
            <li>Provides methods for node and connection operations</li>
        </ul>

        <h3>3. RegistrationManager</h3>
        <p>A centralized service for tracking user and system events:</p>
        <ul>
            <li>Records actions like node creation, selection, and editing</li>
            <li>Maintains an audit trail of user interactions</li>
            <li>Currently disabled in production but logs events to console</li>
        </ul>

        <h3>4. LocalStorage Persistence</h3>
        <p>Browser-based storage for project data:</p>
        <ul>
            <li>Saves projects with unique keys (<code>mindmap_${projectName}</code>)</li>
            <li>Tracks last active project for session restoration</li>
            <li>Stores complete project state including nodes, connections, and metadata</li>
        </ul>

        <h3>5. Backend Storage</h3>
        <p>Server-side storage for mind maps:</p>
        <ul>
            <li>Simple in-memory storage for testing purposes</li>
            <li>REST API endpoints for saving and loading mind maps</li>
            <li>Future potential for database integration</li>
        </ul>
    </div>
</body>
</html>
