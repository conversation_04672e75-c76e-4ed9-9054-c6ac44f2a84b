// <PERSON>ript to check if polyfills are working
(function() {
  console.log('Checking polyfills...');
  
  // Check EventEmitter
  try {
    const EventEmitter = window.EventEmitter;
    if (EventEmitter) {
      const emitter = new EventEmitter();
      emitter.on('test', () => console.log('EventEmitter works!'));
      emitter.emit('test');
      console.log('✅ EventEmitter polyfill is working');
    } else {
      console.error('❌ EventEmitter polyfill is not available');
    }
  } catch (err) {
    console.error('❌ Error testing EventEmitter:', err);
  }
  
  // Check events module
  try {
    const events = window.eventsModule;
    if (events && events.EventEmitter) {
      const emitter = new events.EventEmitter();
      emitter.on('test', () => console.log('events.EventEmitter works!'));
      emitter.emit('test');
      console.log('✅ events module polyfill is working');
    } else {
      console.error('❌ events module polyfill is not available');
    }
  } catch (err) {
    console.error('❌ Error testing events module:', err);
  }
  
  // Check require('events')
  try {
    if (window.require) {
      const eventsModule = window.require('events');
      if (eventsModule && eventsModule.EventEmitter) {
        const emitter = new eventsModule.EventEmitter();
        emitter.on('test', () => console.log('require("events") works!'));
        emitter.emit('test');
        console.log('✅ require("events") is working');
      } else {
        console.error('❌ require("events") is not working correctly');
      }
    } else {
      console.warn('⚠️ window.require is not available');
    }
  } catch (err) {
    console.error('❌ Error testing require("events"):', err);
  }
  
  console.log('Polyfill check complete');
})();
