/**
 * LayoutService.ts
 * 
 * Service layer for layout operations.
 * This service provides methods for arranging objects within sheets,
 * such as positioning nodes in mindmaps.
 * 
 * The service layer pattern helps break circular dependencies by providing
 * a single point of access for operations without direct imports.
 */

import RegistrationManager, { EventType } from './RegistrationManager';
import { mindSheetService } from './MindSheetService';

// Define layout types
export enum LayoutType {
  TREE = 'tree',
  RADIAL = 'radial',
  FORCE = 'force',
  GRID = 'grid',
  HORIZONTAL = 'horizontal',
  VERTICAL = 'vertical',
  CUSTOM = 'custom'
}

// Define layout options
export interface LayoutOptions {
  type: LayoutType;
  nodeSpacing?: number;
  levelSpacing?: number;
  direction?: 'LR' | 'RL' | 'TB' | 'BT';
  centerX?: number;
  centerY?: number;
  rootNodeId?: string;
}

/**
 * LayoutService class
 * 
 * Singleton service for layout operations.
 */
class LayoutService {
  private static instance: LayoutService;

  private constructor() {
    // Private constructor to prevent direct instantiation
    console.log('LayoutService: Initialized');
  }

  /**
   * Get the singleton instance of the LayoutService
   */
  public static getInstance(): LayoutService {
    if (!LayoutService.instance) {
      LayoutService.instance = new LayoutService();
    }
    return LayoutService.instance;
  }

  /**
   * Apply a layout to a sheet
   */
  public applyLayout(sheetId: string, options: LayoutOptions): boolean {
    try {
      // Get the sheet-specific store
      const store = mindSheetService.getMindMapSheetStore(sheetId);
      if (!store) {
        console.error('LayoutService: Cannot apply layout to non-existent sheet:', sheetId);
        return false;
      }

      // Get the store state
      const storeState = store.getState();

      // Apply the layout
      console.log('LayoutService: Applying layout to sheet:', sheetId, options);
      
      // Different layout types have different implementations
      switch (options.type) {
        case LayoutType.TREE:
          this.applyTreeLayout(storeState, options);
          break;
        case LayoutType.RADIAL:
          this.applyRadialLayout(storeState, options);
          break;
        case LayoutType.FORCE:
          this.applyForceLayout(storeState, options);
          break;
        case LayoutType.GRID:
          this.applyGridLayout(storeState, options);
          break;
        case LayoutType.HORIZONTAL:
          this.applyHorizontalLayout(storeState, options);
          break;
        case LayoutType.VERTICAL:
          this.applyVerticalLayout(storeState, options);
          break;
        case LayoutType.CUSTOM:
          // Custom layouts are handled by the caller
          console.log('LayoutService: Custom layout requested, no automatic layout applied');
          break;
        default:
          console.error('LayoutService: Unknown layout type:', options.type);
          return false;
      }

      // Log the layout application
      RegistrationManager.registerEvent(EventType.LAYOUT_APPLIED, {
        sheetId,
        layoutType: options.type
      });

      console.log('LayoutService: Layout applied', sheetId, options.type);

      return true;
    } catch (error) {
      console.error('LayoutService: Error applying layout:', error);

      // Log the error
      RegistrationManager.registerEvent(EventType.ERROR_OCCURRED, {
        component: 'LayoutService',
        method: 'applyLayout',
        message: error.message,
        stack: error.stack
      });

      return false;
    }
  }

  /**
   * Apply a tree layout to a sheet
   */
  private applyTreeLayout(storeState: any, options: LayoutOptions): void {
    // Get the root node ID
    const rootNodeId = options.rootNodeId || storeState.rootNodeId;
    if (!rootNodeId) {
      console.error('LayoutService: Cannot apply tree layout without a root node');
      return;
    }

    // Get the root node
    const rootNode = storeState.nodes[rootNodeId];
    if (!rootNode) {
      console.error('LayoutService: Cannot apply tree layout with non-existent root node:', rootNodeId);
      return;
    }

    // Set default options
    const nodeSpacing = options.nodeSpacing || 100;
    const levelSpacing = options.levelSpacing || 150;
    const direction = options.direction || 'LR';
    const centerX = options.centerX || window.innerWidth / 2;
    const centerY = options.centerY || window.innerHeight / 2;

    // Position the root node at the center
    storeState.updateNode(rootNodeId, {
      ...rootNode,
      position: { x: centerX, y: centerY }
    });

    // Build a tree structure
    const tree = this.buildTree(storeState, rootNodeId);

    // Position the nodes based on the tree structure
    this.positionTreeNodes(storeState, tree, {
      nodeSpacing,
      levelSpacing,
      direction,
      centerX,
      centerY
    });

    // Update the layout in the store
    storeState.updateLayout('tree');
  }

  /**
   * Build a tree structure from the nodes
   */
  private buildTree(storeState: any, rootNodeId: string): any {
    const nodes = storeState.nodes;
    
    // Create a tree structure
    const buildSubtree = (nodeId: string) => {
      const node = nodes[nodeId];
      if (!node) return null;

      // Find children
      const children = Object.values(nodes)
        .filter((n: any) => n.parent === nodeId)
        .map((n: any) => buildSubtree(n.id))
        .filter(Boolean);

      return {
        id: nodeId,
        node,
        children
      };
    };

    return buildSubtree(rootNodeId);
  }

  /**
   * Position nodes based on a tree structure
   */
  private positionTreeNodes(storeState: any, tree: any, options: any): void {
    const { nodeSpacing, levelSpacing, direction, centerX, centerY } = options;
    
    // Calculate the width and height of each subtree
    const calculateSize = (subtree: any) => {
      if (!subtree) return { width: 0, height: 0 };
      
      if (subtree.children.length === 0) {
        return { width: nodeSpacing, height: nodeSpacing };
      }
      
      // Calculate the size of each child
      const childSizes = subtree.children.map(calculateSize);
      
      // Calculate the total width and height
      let width = 0;
      let height = 0;
      
      if (direction === 'LR' || direction === 'RL') {
        // Horizontal layout
        width = levelSpacing + Math.max(...childSizes.map((s: any) => s.width));
        height = childSizes.reduce((sum: number, s: any) => sum + s.height, 0);
      } else {
        // Vertical layout
        width = childSizes.reduce((sum: number, s: any) => sum + s.width, 0);
        height = levelSpacing + Math.max(...childSizes.map((s: any) => s.height));
      }
      
      subtree.size = { width, height };
      return subtree.size;
    };
    
    // Calculate the size of the entire tree
    calculateSize(tree);
    
    // Position the nodes
    const positionNodes = (subtree: any, x: number, y: number) => {
      if (!subtree) return;
      
      // Position the current node
      const node = storeState.nodes[subtree.id];
      storeState.updateNode(subtree.id, {
        ...node,
        position: { x, y }
      });
      
      // Position the children
      if (subtree.children.length === 0) return;
      
      let childX = x;
      let childY = y;
      
      if (direction === 'LR') {
        // Left to right
        childX = x + levelSpacing;
        childY = y - (subtree.size.height / 2);
        
        for (const child of subtree.children) {
          positionNodes(child, childX, childY + (child.size.height / 2));
          childY += child.size.height;
        }
      } else if (direction === 'RL') {
        // Right to left
        childX = x - levelSpacing;
        childY = y - (subtree.size.height / 2);
        
        for (const child of subtree.children) {
          positionNodes(child, childX, childY + (child.size.height / 2));
          childY += child.size.height;
        }
      } else if (direction === 'TB') {
        // Top to bottom
        childX = x - (subtree.size.width / 2);
        childY = y + levelSpacing;
        
        for (const child of subtree.children) {
          positionNodes(child, childX + (child.size.width / 2), childY);
          childX += child.size.width;
        }
      } else if (direction === 'BT') {
        // Bottom to top
        childX = x - (subtree.size.width / 2);
        childY = y - levelSpacing;
        
        for (const child of subtree.children) {
          positionNodes(child, childX + (child.size.width / 2), childY);
          childX += child.size.width;
        }
      }
    };
    
    // Position the nodes starting from the root
    positionNodes(tree, centerX, centerY);
  }

  /**
   * Apply a radial layout to a sheet
   */
  private applyRadialLayout(storeState: any, options: LayoutOptions): void {
    // Implementation for radial layout
    console.log('LayoutService: Radial layout not yet implemented');
  }

  /**
   * Apply a force-directed layout to a sheet
   */
  private applyForceLayout(storeState: any, options: LayoutOptions): void {
    // Implementation for force-directed layout
    console.log('LayoutService: Force-directed layout not yet implemented');
  }

  /**
   * Apply a grid layout to a sheet
   */
  private applyGridLayout(storeState: any, options: LayoutOptions): void {
    // Implementation for grid layout
    console.log('LayoutService: Grid layout not yet implemented');
  }

  /**
   * Apply a horizontal layout to a sheet
   */
  private applyHorizontalLayout(storeState: any, options: LayoutOptions): void {
    // Implementation for horizontal layout
    console.log('LayoutService: Horizontal layout not yet implemented');
  }

  /**
   * Apply a vertical layout to a sheet
   */
  private applyVerticalLayout(storeState: any, options: LayoutOptions): void {
    // Implementation for vertical layout
    console.log('LayoutService: Vertical layout not yet implemented');
  }

  /**
   * Center the view on a node
   */
  public centerOnNode(sheetId: string, nodeId: string): boolean {
    try {
      // Get the sheet-specific store
      const store = mindSheetService.getMindMapSheetStore(sheetId);
      if (!store) {
        console.error('LayoutService: Cannot center on node in non-existent sheet:', sheetId);
        return false;
      }

      // Get the store state
      const storeState = store.getState();

      // Get the node
      const node = storeState.nodes[nodeId];
      if (!node) {
        console.error('LayoutService: Cannot center on non-existent node:', nodeId);
        return false;
      }

      // Center the view on the node
      storeState.centerOnNode(nodeId);

      console.log('LayoutService: Centered view on node', nodeId);

      return true;
    } catch (error) {
      console.error('LayoutService: Error centering on node:', error);

      // Log the error
      RegistrationManager.registerEvent(EventType.ERROR_OCCURRED, {
        component: 'LayoutService',
        method: 'centerOnNode',
        message: error.message,
        stack: error.stack
      });

      return false;
    }
  }

  /**
   * Center the view on the root node
   */
  public centerOnRoot(sheetId: string): boolean {
    try {
      // Get the sheet-specific store
      const store = mindSheetService.getMindMapSheetStore(sheetId);
      if (!store) {
        console.error('LayoutService: Cannot center on root in non-existent sheet:', sheetId);
        return false;
      }

      // Get the store state
      const storeState = store.getState();

      // Get the root node ID
      const rootNodeId = storeState.rootNodeId;
      if (!rootNodeId) {
        console.error('LayoutService: Cannot center on root, no root node defined');
        return false;
      }

      // Center the view on the root node
      return this.centerOnNode(sheetId, rootNodeId);
    } catch (error) {
      console.error('LayoutService: Error centering on root:', error);

      // Log the error
      RegistrationManager.registerEvent(EventType.ERROR_OCCURRED, {
        component: 'LayoutService',
        method: 'centerOnRoot',
        message: error.message,
        stack: error.stack
      });

      return false;
    }
  }
}

// Export the singleton instance
export const layoutService = LayoutService.getInstance();

// Export convenience functions
export const applyLayout = (sheetId: string, options: LayoutOptions) => 
  layoutService.applyLayout(sheetId, options);
export const centerOnNode = (sheetId: string, nodeId: string) => 
  layoutService.centerOnNode(sheetId, nodeId);
export const centerOnRoot = (sheetId: string) => 
  layoutService.centerOnRoot(sheetId);
