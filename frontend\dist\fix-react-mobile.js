// Mobile-specific React compatibility fixes
(function() {
  try {
    console.log('Applying mobile-specific React compatibility fixes...');
    
    // Check if we're on a mobile device
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    if (!isMobile) {
      console.log('Not on mobile device, skipping mobile-specific fixes');
      return;
    }
    
    console.log('Mobile device detected, applying fixes');
    
    // Ensure React is available
    if (!window.React) {
      console.error('React not found, cannot apply mobile fixes');
      return;
    }
    
    // Add mobile-specific polyfills
    
    // Fix touch event handling
    if (!window.TouchEvent && typeof document !== 'undefined') {
      window.TouchEvent = function TouchEvent(type, eventInitDict) {
        const event = document.createEvent('Event');
        event.initEvent(type, true, true);
        return event;
      };
      console.log('Added TouchEvent polyfill');
    }
    
    // Fix React touch event handling
    const React = window.React;
    const internals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
    
    if (internals && internals.ReactCurrentDispatcher && internals.ReactCurrentDispatcher.current) {
      const dispatcher = internals.ReactCurrentDispatcher.current;
      
      // Add useTouchHandler if it doesn't exist (for mobile compatibility)
      if (!dispatcher.useTouchHandler) {
        dispatcher.useTouchHandler = function(handler) {
          return handler;
        };
        console.log('Added useTouchHandler to dispatcher');
      }
    }
    
    // Add mobile viewport meta tag if not present
    if (typeof document !== 'undefined') {
      let viewportMeta = document.querySelector('meta[name="viewport"]');
      if (!viewportMeta) {
        viewportMeta = document.createElement('meta');
        viewportMeta.name = 'viewport';
        viewportMeta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
        document.head.appendChild(viewportMeta);
        console.log('Added viewport meta tag');
      }
    }
    
    console.log('Mobile-specific React fixes applied successfully');
  } catch (error) {
    console.error('Error applying mobile-specific React fixes:', error);
  }
})();
