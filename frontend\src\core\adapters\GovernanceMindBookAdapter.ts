/**
 * GovernanceMindBookAdapter
 *
 * Adapter for connecting governance actions to the MindBook.
 * Handles the creation of sheets based on governance actions.
 */

import { useMindBookStore } from '../state/MindBookStore';
import { MindSheetContentType } from '../state/StoreTypes';
import { initializeMindMap } from './MindMapAdapter';
import { createMindmapFromMBCP } from '../../components/MindMap/utils/MBCPProcessor';
import { useChatForkStore } from '../../components/ChatFork/ChatForkStore';

// Governance action types
export interface GovernanceAction {
  type: string;
  label?: string;
  data?: any;
}

/**
 * Process a governance action and update the MindBook store accordingly.
 *
 * @param action The governance action to process
 * @returns True if the action was processed successfully, false otherwise
 */
export const processGovernanceAction = (action: GovernanceAction): boolean => {
  console.log('GovernanceMindBookAdapter: Processing action:', action);

  // Get the MindBook store
  const store = useMindBookStore.getState();

  // Process the action based on its type
  switch (action.type) {
    case 'create_mindmap':
    case 'SHOW_MINDMAP':
      // Check for MBCP data in different possible locations
      const mbcpData = action.data?.mbcpData || action.payload?.mbcpData || action.data || action.payload;

      if (!mbcpData) {
        console.error('GovernanceMindBookAdapter: No MBCP data found in create_mindmap action');
        console.error('Action data:', action);
        return false;
      }

      console.log('GovernanceMindBookAdapter: Creating mindmap sheet with data:', mbcpData);

      // Create a new mindmap sheet with the MBCP data
      // Extract title from MBCP data if possible
      const rootText = mbcpData.root?.text || mbcpData.mindmap?.root?.text;
      const sheetTitle = rootText || action.data?.topic || action.payload?.topic || 'New Mindmap';
      const sheetId = store.createMindMapSheet(sheetTitle, mbcpData);

      // Initialize the mindmap
      createMindmapFromMBCP(mbcpData);

      console.log('GovernanceMindBookAdapter: Created mindmap sheet with ID:', sheetId);
      return true;

    case 'create_chatfork':
    case 'show_chatfork':
    case 'SHOW_CHATFORK':
      const chatforkData = action.data?.content || action.data || action.payload;

      if (!chatforkData) {
        console.error('GovernanceMindBookAdapter: No content found in chatfork action');
        return false;
      }

      console.log('GovernanceMindBookAdapter: Creating chatfork sheet with data:', chatforkData);

      // Create a new chatfork sheet with the content
      const chatforkTitle = action.data?.topic || action.payload?.topic || 'New ChatFork';
      const chatforkSheetId = store.createChatForkSheet(chatforkTitle, chatforkData);

      // Show the chatfork
      const chatForkStore = useChatForkStore.getState();
      chatForkStore.showChatFork(chatforkData, chatforkSheetId);

      console.log('GovernanceMindBookAdapter: Created chatfork sheet with ID:', chatforkSheetId);
      return true;

    default:
      console.warn(`GovernanceMindBookAdapter: Unknown action type: ${action.type}`);
      return false;
  }
};
