import { useEffect, useCallback } from 'react';
import { useMindMap } from '../context/MindMapContext';
import { useProjectManagement } from './useProjectManagement';
import { useNodeManagement } from './useNodeManagement';

export const useKeyboardShortcuts = () => {
  const {
    selectedId,
    setSelectedId,
    setShowNodeDialog,
    nodes,
    setNodes,
    connections,
    setConnections
  } = useMindMap();
  
  const { saveProject } = useProjectManagement();
  const { addChildNode, deleteNode } = useNodeManagement();

  // Delete selected node
  const handleDelete = useCallback(() => {
    if (!selectedId || selectedId === 'root') return;
    
    deleteNode(selectedId);
  }, [selectedId, deleteNode]);

  // Add new node
  const handleInsert = useCallback(() => {
    if (!selectedId) return;
    setShowNodeDialog(true);
  }, [selectedId, setShowNodeDialog]);

  // Save project
  const handleSave = useCallback((e: KeyboardEvent) => {
    if (e.ctrlKey && e.key.toLowerCase() === 's') {
      e.preventDefault();
      saveProject();
    }
  }, [saveProject]);

  // Add child node with Tab
  const handleTab = useCallback((e: KeyboardEvent) => {
    if (e.key === 'Tab' && selectedId) {
      e.preventDefault();
      
      // Tab: Add child and select it
      addChildNode();
    }
  }, [selectedId, addChildNode]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'Delete':
          handleDelete();
          break;
        case 'Insert':
          handleInsert();
          break;
        case 'Tab':
          handleTab(e);
          break;
        case 'Enter':
          if (selectedId) {
            e.preventDefault();
            setShowNodeDialog(true);
          }
          break;
        default:
          handleSave(e);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleDelete, handleInsert, handleSave, handleTab, selectedId, setShowNodeDialog]);
}; 