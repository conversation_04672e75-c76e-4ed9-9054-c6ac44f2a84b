import React from 'react';
import { Arrow } from 'react-konva';
import { Connection } from '../../types';
import { useMindMap } from '../../context/MindMapContext';

// Cast Konva components to any to bypass TypeScript errors
const KonvaArrow = Arrow as any;

interface ConnectionComponentProps {
  connection: Connection;
}

export const ConnectionComponent: React.FC<ConnectionComponentProps> = ({ connection }) => {
  // Use proper selector pattern for hooks
  const nodes = useMindMap(state => state.nodes);

  // Find the nodes for this connection
  const fromNode = nodes.find(node => node.id === connection.from);
  const toNode = nodes.find(node => node.id === connection.to);

  // If either node doesn't exist, don't render
  if (!fromNode || !toNode) {
    return null;
  }

  // Determine connection style properties
  const strokeWidth = connection.thickness || 2;
  const stroke = connection.color || '#555555';

  return (
    <KonvaArrow
      points={[fromNode.x, fromNode.y, toNode.x, toNode.y]}
      stroke={stroke}
      strokeWidth={strokeWidth}
      fill={stroke}
      pointerLength={5}
      pointerWidth={5}
    />
  );
};

export default ConnectionComponent;