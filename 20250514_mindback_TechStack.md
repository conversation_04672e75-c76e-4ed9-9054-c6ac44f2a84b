# MindBack Technical Stack Analysis

## Overview

MindBack is an AI-powered application that provides intelligent moderation and cognitive assistance through a mind mapping interface. The application follows a client-server architecture with a React-based frontend and a Python FastAPI backend. It integrates with various LLM providers to deliver AI-powered features.

## System Architecture

### High-Level Architecture

```
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│                 │      │                 │      │                 │
│  React Frontend │◄────►│  FastAPI Server │◄────►│   LLM Services  │
│                 │      │                 │      │                 │
└─────────────────┘      └─────────────────┘      └─────────────────┘
```

The application follows a three-tier architecture:
1. **Frontend**: React-based UI with component-based architecture
2. **Backend**: FastAPI server providing REST and WebSocket endpoints
3. **External Services**: Integration with LLM providers (OpenAI, Gemini, Claude)

## Frontend Technical Stack

### Core Technologies
- **Framework**: React 18.2.0
- **Build Tool**: Vite 6.3.5
- **Language**: TypeScript 4.9.5
- **Package Manager**: npm (Node.js v20 LTS)
- **Routing**: React Router DOM 6.20.0

### UI Components and Styling
- **UI Framework**: Material UI 6.4.7 with Emotion styling
- **Icons**: Material UI Icons 6.4.7
- **Animation**: Framer Motion 9.1.7
- **Canvas Rendering**: Konva/React-Konva 18.2.10 (for mind map visualization)
- **Drag and Drop**: React-Draggable 4.4.6, React-Rnd 10.5.2

### State Management
- **Global State**: Zustand 4.4.7
- **Event Handling**: Events 3.3.0

### Data Handling
- **HTTP Client**: Axios 1.6.2
- **Data Parsing**: js-yaml 4.1.0
- **Utilities**: Lodash 4.17.21, UUID 9.0.1

### Development Tools
- **Testing**: Vitest 3.1.3, React Testing Library 14.1.2
- **Linting**: ESLint 8.56.0
- **Code Analysis**: Dependency-Cruiser 16.10.1

## Backend Technical Stack

### Core Technologies
- **Framework**: FastAPI 0.110.0
- **ASGI Server**: Uvicorn 0.27.1
- **Language**: Python 3.x
- **Data Validation**: Pydantic 2.7.0+
- **Environment Management**: python-dotenv 1.0.0

### AI and LLM Integration
- **LLM Client**: OpenAI 1.13.3
- **Agent Framework**: CrewAI 0.22.5
- **Search Integration**: Tavily 0.3.1

### Data Processing
- **Data Analysis**: NumPy 1.26.3, Pandas 2.2.0
- **Network Analysis**: NetworkX 3.2.1
- **Configuration**: PyYAML 6.0.1

### Web and API
- **HTTP Client**: HTTPX 0.26.0
- **WebSockets**: WebSockets 12.0
- **CORS**: FastAPI CORS Middleware

### Database
- **ORM**: SQLAlchemy 2.0.25
- **Migrations**: Alembic 1.13.1

### Background Processing
- **Task Queue**: Celery 5.3.6
- **Message Broker**: Redis 5.0.1

### Development Tools
- **Testing**: Pytest 7.4.4, Pytest-AsyncIO 0.23.4
- **Code Formatting**: Black 24.1.1, isort 5.13.2
- **Linting**: Flake8 7.0.0
- **Documentation**: MkDocs 1.5.3, MkDocs-Material 9.5.3

## Application Features

### Core Features
1. **Governance Agent**: Dialog-based interface for interacting with LLMs
2. **MindBook/MindSheet**: Excel-like workbook/worksheet structure for organizing content
3. **Mind Mapping**: Visual representation of ideas and concepts
4. **ChatFork**: Exploration of different conversation paths
5. **Context Panel**: Provides contextual information at different levels (Foundational, Strategic, Operational)

### AI Integration
1. **Multi-LLM Support**: Integration with multiple LLM providers
2. **MBCP Format**: Standardized communication protocol for all LLM interactions
3. **Intent Detection**: Two-step process with initial intent detection followed by specialized response generation
4. **Prompt Library**: YAML-based prompt templates as the single source of truth

## Deployment and Environment

### Development Environment
- **Local Development**: Run via run_setup.ps1 script
- **Frontend Server**: Vite development server on http://localhost:5173
- **Backend Server**: Uvicorn on http://127.0.0.1:8000
- **Virtual Environment**: Python venv for backend dependencies
- **API Documentation**: Swagger UI at http://127.0.0.1:8000/docs

### Startup Process
1. Clean log files
2. Create and activate Python virtual environment
3. Install backend dependencies
4. Start backend server with Uvicorn
5. Install frontend dependencies
6. Start frontend development server with Vite

## Architecture Patterns

### Frontend Architecture
- **Feature-based Organization**: Code organized by features rather than technical concerns
- **Component Hierarchy**: Follows atomic design principles
- **State Management**: Zustand for global state with flux/redux pattern
- **Z-Index Strategy**: Clearly defined z-index ranges for UI layering

### Backend Architecture
- **API-First Design**: Well-defined API endpoints with OpenAPI documentation
- **Service-Oriented**: Modular services for different concerns
- **Dependency Injection**: FastAPI's dependency injection for service management
- **Asynchronous Processing**: Async/await pattern for non-blocking operations

## Conclusion

MindBack is built on modern web technologies with a clear separation between frontend and backend concerns. The application leverages React and FastAPI to create a responsive and scalable architecture, with strong integration capabilities for various LLM providers. The codebase follows a feature-based organization with clear architectural patterns and guidelines.
