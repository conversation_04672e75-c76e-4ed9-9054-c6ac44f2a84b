import { MindMapNode } from '../types/mindmap';

// Types for keyboard shortcut handlers
export interface KeyboardShortcutHandlers {
  openNodeDialog: (nodeId: string) => void;
  saveNodeChanges: () => void;
  addChildNode: (parentId: string, keepParentActive: boolean) => void;
  isDialogOpen: boolean;
  selectedNodeId: string | null;
}

/**
 * Handles keyboard events for the mind map
 * @param event The keyboard event
 * @param handlers Object containing handler functions
 * @param nodes The current nodes in the mind map
 * @returns boolean indicating if the event was handled
 */
export const handleKeyboardShortcut = (
  event: KeyboardEvent,
  handlers: KeyboardShortcutHandlers,
  nodes: MindMapNode[]
): boolean => {
  const { 
    openNodeDialog, 
    saveNodeChanges, 
    addChildNode, 
    isDialogOpen, 
    selectedNodeId 
  } = handlers;

  // If dialog is open, handle Enter key to save changes
  if (isDialogOpen) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      saveNodeChanges();
      return true;
    }
    return false;
  }

  // If no dialog is open and a node is selected
  if (selectedNodeId) {
    // Enter key: Open dialog for selected node
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      openNodeDialog(selectedNodeId);
      return true;
    }
    
    // Enter + Shift: Create child node but keep parent selected
    if (event.key === 'Enter' && event.shiftKey) {
      event.preventDefault();
      addChildNode(selectedNodeId, true);
      return true;
    }
    
    // Tab key: Create child node and select it
    if (event.key === 'Tab' && !event.shiftKey) {
      event.preventDefault();
      addChildNode(selectedNodeId, false);
      return true;
    }
  }
  
  return false;
};

/**
 * Positions the node dialog at the top-left of the screen
 * @param dialogElement The dialog DOM element
 */
export const positionNodeDialog = (dialogElement: HTMLElement | null): void => {
  if (!dialogElement) return;
  
  // Set initial position
  dialogElement.style.position = 'fixed';
  dialogElement.style.top = '80px'; // Increased from 20px to avoid toolbar overlap
  dialogElement.style.left = '20px';
  dialogElement.style.transform = 'none'; // Remove any transform
  
  // Add class for specific styling
  dialogElement.classList.add('top-left');
  
  // Ensure dialog is fully visible
  setTimeout(() => {
    // Get dialog dimensions
    const rect = dialogElement.getBoundingClientRect();
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;
    
    // Check if dialog is outside viewport
    const isOutsideRight = rect.right > windowWidth - 20;
    const isOutsideBottom = rect.bottom > windowHeight - 20;
    
    // Adjust position if needed
    if (isOutsideRight) {
      dialogElement.style.left = `${windowWidth - rect.width - 20}px`;
    }
    
    if (isOutsideBottom) {
      dialogElement.style.top = `${windowHeight - rect.height - 20}px`;
    }
    
    // Focus the text input
    const textArea = dialogElement.querySelector('textarea');
    if (textArea) {
      textArea.focus();
      textArea.select();
    }
  }, 50);
}; 