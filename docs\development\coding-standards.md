# Coding Standards

## General Guidelines

- Follow PEP 8 for Python code
- Use ESLint and <PERSON><PERSON><PERSON> for JavaScript/TypeScript
- Keep files under 300 lines
- Write meaningful comments


## Migrated Content

### General
- the main theme of color in the app is black and grey
- only in specific application do we use color (especially with agents)
- in dialogboxes with llm the posts of llm is in light grey and the user in light blue
- the font is arial


### Main Frame
- header
    -  mindback.ai logo (C:\Users\<USER>\Documents\VSCode\MindBack_Backup\MindBack_V1\Public\Logo\MB_logo.jpg) left aligned on black background followed by thext:"mindback.ai - intellegence moderation"
    - right aligned: three lines button (selection for settings, saving and loading) followed by the help button "?)
- main canvas area in light grey
- bottom field in black central text "mindback.ai" in white -> dialogbox "mindback.ai is a Filurium Production


### Governance dialog box
- size dynamic with the content
- default widthxhight: 800x600
- header
    - black field with MB_logo.jpg (C:\Users\<USER>\Documents\VSCode\MindBack_Backup\MindBack_V1\Public\Logo\MB_logo.jpg)left aligned followed by "Governance Agent" arial, 16p, bold, white
    - right aligned buttons
        - Size restoration
        - Position restoration
        - Collapse/expand functionality
        - deletion button connected to restoration button (Governance Agent) top right in the screen
- Main dialogbox
    - size 790 width, hight 200
    - llm post left-ailigned in light grey background, text arial, 12p, black
    - user post right aligned in light blue background, text arial, 12p, black 
    - all posts shall have time and date stamp
    - initial llm post: How can I assist you today?
- fiels grey with selection box for llm models and the toggle for live llm, default true
- input field shall be one line high, but be autoexpand to cover longer input, text 12p, arial black
    - default text "Type your message..." in light grey
- bottom right corner handle for sizing


### Node
- a node is a rectangular with soft edges
- it has a dark-grey outline, norma thin, when selected thicker
- it has a light grey filling, the main node 10% darker than the children
- in the upper-left part of the neode the index, e.g. 1.0, 1.1, 1.2, ... shall be displayed, arial, plain, 8p (dynalic sizing), white
- below the title of the node, arial, plain, 10p (dynamic sizing), white
- in the bottom there shall be five rectangular next to each other, dark-grey outline, leight-grey filling (+10% compared to node background)

### Node dialog box
- size 800x600, resizable with handle in bottom right corner
- header: black field with MB_logo.jpg (C:\Users\<USER>\Documents\VSCode\MindBack_Backup\MindBack_V1\Public\Logo\MB_logo.jpg) left aligned followed by the numbering and title of the node in arial, 16p, bold, white
- draggable by grabbing the header area
- close button in the top right corner of the header
- node information section
  - node title input field
  - node description textarea for detailed content
- content uses white background, light grey for fields
- resizing functionality with handle in bottom right corner
- similar dragging and sizing functionality as the governance dialog box 

## UI Style Guidelines

### General
- the main theme of color in the app is black and grey
- only in specific application do we use color (especially with agents)
- in dialogboxes with llm the posts of llm is in light grey and the user in light blue
- the font is arial

## Coding Patterns

# Coding Patterns and Structure
- Prioritize simple, maintainable solutions.
- Avoid hardcoded defaults and duplicated logic.
- Avoid files longer than 300 lines—modularize when expanding.
- Never mock or stub data in dev/prod—mocking is test-only.
- Never mask real error messages—report root causes directly.
- Do not overwrite `.env` files without confirmation from the user.
- Before adding new functionality, check for existing files with similar responsibilities.
- Reflect when the user reflects, and always ask if suggestions should be implemented.

## UI Style Guidelines

### General
- the main theme of color in the app is black and grey
- only in specific application do we use color (especially with agents)
- in dialogboxes with llm the posts of llm is in light grey and the user in light blue
- the font is arial