# ChatFork Enhanced Specification
## Multi-Level Forking & Knowledge Consolidation System

**Version**: 2.0  
**Date**: 2025-01-27  
**Status**: Development Planning  

---

## Overview

The enhanced ChatFork system transforms the current simple fork functionality into a sophisticated **hierarchical knowledge exploration and consolidation platform**. This system allows users to create unlimited levels of forks from any text or input field, then consolidate their work into various output formats.

## Core Vision

Create a powerful knowledge processing system where:
1. **Any text can be forked** for deeper exploration
2. **Any fork input can spawn sub-forks** creating unlimited hierarchy
3. **All work can be consolidated** into text, tasks, mindmaps, or other formats
4. **Knowledge flows bidirectionally** between ChatFork and MindMap systems

---

## Enhanced User Workflow

### 1. Initial Fork Creation
```
1. User selects text in ChatFork content
2. User presses Shift+Tab
3. Selected text gets persistent highlighting
4. Fork box appears in right panel with active input field
5. User can type questions, comments, or augmentations
```

### 2. Multi-Level Forking
```
1. User types in any fork input field
2. User presses Shift+Tab while in input field
3. New sub-fork created beneath current fork
4. Visual hierarchy shows parent-child relationships
5. Process repeats indefinitely for unlimited depth
```

### 3. Knowledge Consolidation
```
1. User completes exploration across multiple fork levels
2. User selects consolidation method:
   - Send all content to LLM for synthesis
   - Extract action items as tasks
   - Convert to MindMap structure
   - Export as structured document
   - Create new ChatFork from consolidated content
```

---

## Technical Architecture

### Fork Data Structure

```typescript
interface EnhancedChatFork {
  id: string;
  parentForkId?: string;              // Links to parent fork (null for root forks)
  parentTextSelection?: string;       // Original selected text (for root forks)
  level: number;                      // Hierarchy depth (0 = from text, 1+ = sub-forks)
  userInput: string;                  // Current content in input field
  children: string[];                 // Array of child fork IDs
  isInputActive: boolean;             // Whether input field is currently focused
  timestamp: Date;                    // Creation time
  
  // Text highlighting data (for root forks)
  originalTextRange?: {
    start: number;
    end: number;
    elementId: string;
    highlightColor: string;
  };
  
  // Consolidation metadata
  isMarkedForConsolidation: boolean;
  consolidationType?: 'llm' | 'task' | 'mindmap' | 'document';
  consolidationStatus?: 'pending' | 'processing' | 'complete';
}
```

### Keyboard Event Handling

```typescript
interface KeyboardEventContext {
  type: 'text_selection' | 'input_field';
  activeElement: HTMLElement;
  selectedText?: string;
  currentForkId?: string;
  inputFieldContent?: string;
}

// Shift+Tab behavior:
// - In text content: Create root fork from selection
// - In fork input field: Create sub-fork from current input
// - Tab alone: Navigate between input fields
// - Enter: Process input (future LLM integration)
```

### State Management Enhancement

```typescript
interface ChatForkStoreState {
  // Existing state
  content: ChatResponse | null;
  isVisible: boolean;
  activeSheetId: string | null;
  
  // Enhanced state
  forks: Record<string, EnhancedChatFork>;    // All forks by ID
  rootForkIds: string[];                       // Top-level forks (from text)
  activeForkId: string | null;                 // Currently focused fork
  highlightedTextRanges: TextHighlight[];     // Persistent text highlights
  
  // Consolidation state
  consolidationMode: boolean;                  // Whether in consolidation UI
  selectedForksIds: string[];                  // Forks selected for consolidation
  consolidationResult?: ConsolidationResult;   // Output from consolidation process
}
```

---

## UI/UX Design

### Layout Structure

```
┌─────────────────────────────────────────────────────────────────┐
│ ChatFork Header                                     [x]         │
├─────────────────────────────┬───────────────────────────────────┤
│ Main Content (60%)          │ Fork Tree Panel (40%)             │
│                             │                                   │
│ Lorem ipsum [highlighted]   │ ├── Fork 1: [input field____]     │
│ dolor sit amet,             │ │   ├── Sub 1.1: [input____]     │
│ consectetur [highlighted]   │ │   │   └── Sub 1.1.1: [inp__]   │
│ adipiscing elit...          │ │   └── Sub 1.2: [input____]     │
│                             │ ├── Fork 2: [input field____]     │
│ Sed do eiusmod tempor       │ │   └── Sub 2.1: [input____]     │
│ incididunt ut labore...     │                                   │
│                             │ [Consolidate Selected Forks]      │
│                             │ [Export Options ▼]               │
└─────────────────────────────┴───────────────────────────────────┘
```

### Visual Elements

1. **Text Highlighting**: 
   - Different colors for each root fork
   - Subtle borders/backgrounds that persist
   - Small indicators linking to fork panel

2. **Fork Tree Visualization**:
   - Indentation-based hierarchy
   - Connecting lines between levels
   - Collapsible/expandable sections
   - Color-coded by parent fork

3. **Input Field States**:
   - Active field has focus styling
   - Filled fields show content preview
   - Empty fields show contextual placeholders

4. **Consolidation Interface**:
   - Checkbox selection for forks
   - Preview panel for consolidation options
   - Progress indicators for processing

---

## Integration Points

### 1. MindMap System Integration

**Fork to MindMap**:
```
- Convert fork hierarchy into mindmap node structure
- Root forks become main branches
- Sub-forks become child nodes
- Preserve parent-child relationships
- Transfer input content as node text
```

**MindMap to Fork**:
```
- Import mindmap nodes as fork structure
- Node text becomes fork input content
- Maintain hierarchical relationships
- Enable continued forking from imported structure
```

### 2. LLM Integration

**Consolidation Prompts**:
```yaml
# Stored in: backend/Prompt_library/chatfork_consolidation.yaml

consolidation_types:
  synthesis:
    prompt: "Synthesize the following fork exploration into a coherent analysis..."
    output_format: "structured_text"
  
  task_extraction:
    prompt: "Extract actionable tasks from this fork exploration..."
    output_format: "task_list"
  
  knowledge_map:
    prompt: "Create a knowledge map from this exploration..."
    output_format: "mindmap_structure"
```

**Processing Flow**:
```
1. User selects forks for consolidation
2. System gathers all fork content and hierarchy
3. Sends to backend with consolidation type
4. LLM processes and returns structured result
5. Result displayed in consolidation preview
6. User can export, save, or create new content
```

### 3. Task Management Integration

**Task Creation**:
```typescript
interface ForkDerivedTask {
  id: string;
  title: string;                    // Extracted from fork content
  description: string;              // Full fork hierarchy context
  sourceForkId: string;             // Reference to originating fork
  priority: 'low' | 'medium' | 'high';
  dueDate?: Date;
  tags: string[];                   // Auto-generated from content
  parentForkStructure: string[];    // Full hierarchy for context
}
```

---

## Implementation Phases

### Phase 1: Multi-Level Fork Infrastructure
**Sprint 1-2 weeks**

- [ ] Enhance ChatFork state management for hierarchy
- [ ] Implement Shift+Tab keyboard handling
- [ ] Create fork tree data structure
- [ ] Basic parent-child fork relationships
- [ ] Input field focus management

**Deliverables**:
- Working Shift+Tab fork creation
- Hierarchical fork display
- Input field navigation

### Phase 2: Visual Hierarchy & Highlighting
**Sprint 2-3 weeks**

- [ ] Implement persistent text highlighting
- [ ] Fork tree visual representation with indentation
- [ ] Color-coding system for fork relationships
- [ ] Collapsible/expandable fork sections
- [ ] Responsive design for deep hierarchies

**Deliverables**:
- Visual fork tree with proper hierarchy
- Persistent text highlighting
- Professional UI/UX design

### Phase 3: Consolidation System
**Sprint 3-4 weeks**

- [ ] Fork selection interface
- [ ] Consolidation type options (LLM, tasks, mindmap)
- [ ] Backend integration for LLM consolidation
- [ ] Export functionality
- [ ] Result preview and editing

**Deliverables**:
- Working consolidation system
- LLM integration for synthesis
- Export to multiple formats

### Phase 4: Advanced Integration
**Sprint 2-3 weeks**

- [ ] MindMap ↔ ChatFork bidirectional conversion
- [ ] Task management integration
- [ ] Advanced consolidation options
- [ ] Performance optimization for large fork trees
- [ ] Comprehensive testing

**Deliverables**:
- Full system integration
- Bidirectional content flow
- Production-ready performance

---

## Success Criteria

### User Experience Goals
- [ ] Intuitive Shift+Tab workflow for fork creation
- [ ] Seamless navigation between multiple input fields
- [ ] Clear visual hierarchy for complex fork trees
- [ ] Efficient consolidation of exploration work
- [ ] Smooth integration with existing MindMap workflow

### Technical Goals
- [ ] Support for unlimited fork hierarchy depth
- [ ] Responsive performance with 100+ forks
- [ ] Reliable state persistence across sessions
- [ ] Robust keyboard event handling
- [ ] Clean integration with existing architecture

### Business Goals
- [ ] Enhanced knowledge exploration capabilities
- [ ] Streamlined research and analysis workflow
- [ ] Improved content creation and consolidation
- [ ] Better integration between different content types
- [ ] Increased user engagement and productivity

---

## Future Enhancements

### Advanced Features
- **Collaborative Forking**: Multiple users exploring same content
- **AI-Assisted Fork Suggestions**: LLM suggests interesting fork directions
- **Template Fork Patterns**: Pre-defined exploration templates
- **Fork Analytics**: Insights into exploration patterns
- **Version History**: Track fork evolution over time

### Integration Expansions
- **Document Import**: Fork directly from uploaded documents
- **Web Research**: Fork from web search results
- **Database Integration**: Fork from structured data
- **API Connections**: Fork from external data sources

---

## Technical Notes

### Performance Considerations
- Virtual scrolling for large fork trees
- Lazy loading of deep fork hierarchies
- Debounced input field updates
- Optimized re-rendering strategies

### Accessibility
- Keyboard navigation between all fork inputs
- Screen reader support for fork hierarchy
- Color-blind friendly highlighting scheme
- Proper focus management for complex UI

### Data Persistence
- Auto-save fork content during typing
- Persist fork hierarchy in MindBook storage
- Export/import fork structures
- Backup and recovery mechanisms

---

## Development Team Notes

### Architecture Alignment
- Follows existing MindBack coding patterns
- Integrates with current state management (Zustand)
- Maintains separation of concerns
- Preserves existing performance characteristics

### Testing Strategy
- Unit tests for fork hierarchy logic
- Integration tests for keyboard handling
- UI tests for complex fork interactions
- Performance tests for large fork trees

### Documentation Requirements
- User documentation for new workflow
- API documentation for consolidation endpoints
- Technical documentation for fork architecture
- Video tutorials for complex features

---

*This specification serves as the authoritative guide for implementing the enhanced ChatFork system. All development should align with these requirements and patterns.* 