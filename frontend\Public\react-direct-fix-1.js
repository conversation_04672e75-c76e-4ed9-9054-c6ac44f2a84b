// Additional React compatibility fixes
(function() {
  try {
    console.log('Applying additional React compatibility fixes...');
    
    // Ensure React is available
    if (!window.React) {
      console.error('React not found, cannot apply fixes');
      return;
    }
    
    // Add any missing React methods
    const React = window.React;
    
    // Add createContext if missing
    if (!React.createContext) {
      React.createContext = function(defaultValue) {
        const context = {
          Provider: function({ value, children }) {
            return children;
          },
          Consumer: function({ children }) {
            return children(defaultValue);
          },
          _currentValue: defaultValue
        };
        return context;
      };
      console.log('Added createContext to React');
    }
    
    // Add forwardRef if missing
    if (!React.forwardRef) {
      React.forwardRef = function(render) {
        return render;
      };
      console.log('Added forwardRef to React');
    }
    
    // Add lazy if missing
    if (!React.lazy) {
      React.lazy = function(factory) {
        let component = null;
        let status = 'pending';
        let result = null;
        
        const LazyComponent = function(props) {
          if (status === 'pending') {
            try {
              const promise = factory();
              status = 'loading';
              promise.then(
                mod => {
                  component = mod.default || mod;
                  status = 'resolved';
                },
                error => {
                  result = error;
                  status = 'rejected';
                }
              );
            } catch (error) {
              result = error;
              status = 'rejected';
            }
          }
          
          if (status === 'resolved') {
            return React.createElement(component, props);
          } else {
            return null;
          }
        };
        
        return LazyComponent;
      };
      console.log('Added lazy to React');
    }
    
    // Add Suspense if missing
    if (!React.Suspense) {
      React.Suspense = function({ fallback, children }) {
        return children;
      };
      console.log('Added Suspense to React');
    }
    
    // Add additional hooks
    
    // Add useLayoutEffect if missing
    if (!React.useLayoutEffect) {
      React.useLayoutEffect = React.useEffect || function() {};
      console.log('Added useLayoutEffect to React');
    }
    
    // Add useDebugValue if missing
    if (!React.useDebugValue) {
      React.useDebugValue = function() {};
      console.log('Added useDebugValue to React');
    }
    
    // Add useImperativeHandle if missing
    if (!React.useImperativeHandle) {
      React.useImperativeHandle = function(ref, createHandle) {
        if (ref && typeof ref === 'object' && 'current' in ref) {
          ref.current = createHandle();
        }
      };
      console.log('Added useImperativeHandle to React');
    }
    
    console.log('Additional React fixes applied successfully');
  } catch (error) {
    console.error('Error applying additional React fixes:', error);
  }
})();
