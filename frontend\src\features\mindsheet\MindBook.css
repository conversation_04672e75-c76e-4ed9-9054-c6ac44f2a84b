/**
 * MindBook Component Styles
 *
 * Styles for the MindBook component, which serves as the main container
 * for all content in the application.
 */

.mind-book {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  background-color: #f8fafc;
  z-index: 1; /* Ensure it's above the background but below other elements */
}

/* The governance container is positioned absolutely to allow it to float above other content */
.governance-container {
  position: absolute;
  top: 10px;
  /* Removed right: 10px to allow the governance box to be positioned freely */
  z-index: var(--z-index-governance-box); /* Using CSS variable for z-index */
  pointer-events: auto; /* Ensure it can receive mouse events */
  /* Remove width and height constraints that might be causing issues */
}

/* The sheets container holds all MindSheet components */
.sheets-container {
  flex: 1;
  position: absolute;
  overflow: hidden;
  width: 100%;
  height: calc(100% - 30px); /* Subtract the height of the tabs */
  top: 0;
  left: 0;
  right: 0;
  bottom: 30px; /* Leave space at the bottom for the tabs */
  z-index: 1; /* Ensure it's above the background but below other elements */
  background-color: #f8fafc;
}

/* We don't need sheet-tabs styles here as they're handled by MindSheetTabs.css */
