/**
 * KeyboardManager.ts
 *
 * A centralized service for managing keyboard events across the application.
 * This ensures that keyboard events are handled consistently and only affect
 * the active components.
 *
 * IMPORTANT: This service maintains strict isolation between different mindsheets
 * by only operating on the active sheet's store and checking multiple conditions
 * before performing any actions.
 */

import { getMindBookStore, getSheetMindMapStore, hasSheetMindMapStore, saveSheetMindMapStoreState } from './StoreService';
import RegistrationManager, { EventType } from './RegistrationManager';
import { MindSheetContentType } from '../state/StoreTypes';

// Singleton class to manage keyboard events
class KeyboardManager {
  private static instance: KeyboardManager;
  private isInitialized: boolean = false;

  // Private constructor to enforce singleton pattern
  private constructor() {
    // Initialize the keyboard event listeners
    this.initialize();
  }

  // Get the singleton instance
  public static getInstance(): KeyboardManager {
    if (!KeyboardManager.instance) {
      KeyboardManager.instance = new KeyboardManager();
    }
    return KeyboardManager.instance;
  }

  // Initialize the keyboard event listeners
  private initialize(): void {
    if (this.isInitialized) {
      return;
    }

    // Add global keyboard event listener
    window.addEventListener('keydown', this.handleKeyDown.bind(this), true);
    this.isInitialized = true;
    console.log('KeyboardManager: Initialized global keyboard event listener');
  }

  // Handle keydown events
  private handleKeyDown(e: KeyboardEvent): void {
    // Skip if focus is in an input or textarea
    if (
      e.target instanceof HTMLInputElement ||
      e.target instanceof HTMLTextAreaElement
    ) {
      return;
    }

    // Handle Tab key for mindmap node creation
    if (e.key === 'Tab') {
      this.handleTabKey(e);
    }
  }

  // Handle Tab key specifically
  private handleTabKey(e: KeyboardEvent): void {
    // Get the active sheet from MindBookStore
    const mindBookStore = getMindBookStore();
    const activeSheetId = mindBookStore.activeSheetId;

    if (!activeSheetId) {
      console.log('KeyboardManager: No active sheet found');
      return;
    }

    // Get the active sheet
    const activeSheet = mindBookStore.sheets.find(sheet => sheet.id === activeSheetId);
    if (!activeSheet) {
      console.log('KeyboardManager: Active sheet not found in sheets array');
      return;
    }

    // Only handle Tab key for mindmap sheets
    if (activeSheet.contentType !== MindSheetContentType.MINDMAP) {
      console.log('KeyboardManager: Active sheet is not a mindmap');
      return;
    }

    console.log('KeyboardManager: Tab key pressed for active mindmap sheet:', activeSheetId);

    // Verify that the store exists for this sheet
    if (!hasSheetMindMapStore(activeSheetId)) {
      console.log('KeyboardManager: No store found for active sheet:', activeSheetId);
      return;
    }

    // Get the sheet-specific store
    const sheetStore = getSheetMindMapStore(activeSheetId);

    // Double-check that we have a valid store
    if (!sheetStore) {
      console.log('KeyboardManager: Failed to get store for active sheet:', activeSheetId);
      return;
    }

    // Get the state from the store
    const storeState = sheetStore.getState();
    const { selectedNodeId, nodes } = storeState;

    // Verify we have a selected node and it exists in the nodes object
    if (!selectedNodeId) {
      console.log('KeyboardManager: No node selected in active mindmap');

      // If no node is selected but we have a root node, select it
      if (storeState.rootNodeId && nodes[storeState.rootNodeId]) {
        storeState.selectNode(storeState.rootNodeId);
        console.log('KeyboardManager: Selected root node:', storeState.rootNodeId);

        // Now we can proceed with the selected root node
        this.handleTabKeyWithNode(e, activeSheetId, storeState, storeState.rootNodeId);
      }
      return;
    }

    if (!nodes[selectedNodeId]) {
      console.log('KeyboardManager: Selected node not found in nodes object');
      return;
    }

    // Process the Tab key with the selected node
    this.handleTabKeyWithNode(e, activeSheetId, storeState, selectedNodeId);
  }

  // Helper method to handle Tab key with a specific node
  private handleTabKeyWithNode(e: KeyboardEvent, sheetId: string, storeState: any, nodeId: string): void {
    // Prevent default tab behavior
    e.preventDefault();
    e.stopPropagation();

    console.log('KeyboardManager: Creating new node for selected node:', nodeId, 'in sheet:', sheetId);

    // Get the selected node
    const selectedNode = storeState.nodes[nodeId];

    // Get window dimensions to ensure nodes are within visible area
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // Calculate position for the new node (offset from parent)
    const offsetX = Math.min(200, windowWidth * 0.15);
    const offsetY = Math.min(50, windowHeight * 0.05);

    // Calculate position ensuring it's within visible area
    const x = Math.max(100, Math.min(selectedNode.x + offsetX, windowWidth - 200));
    const y = Math.max(100, Math.min(selectedNode.y + offsetY, windowHeight - 100));

    try {
      // Add the child node using the store's addNode method directly
      const childId = storeState.addNode(
        nodeId,
        'New Node',
        x,
        y,
        { metadata: { isManuallyAdded: true, creationSource: 'keyboard' } }
      );

      console.log('KeyboardManager: Added child node with ID:', childId, 'to sheet:', sheetId);

      // Register the node creation event
      if (childId) {
        RegistrationManager.registerEvent(EventType.NODE_CREATED, {
          id: childId,
          sheetId: sheetId
        });

        // If not Shift+Tab, select the new node
        if (!e.shiftKey) {
          storeState.selectNode(childId);
          console.log('KeyboardManager: Selected new node:', childId, 'in sheet:', sheetId);
        } else {
          console.log('KeyboardManager: Keeping parent node selected (Shift+Tab) in sheet:', sheetId);
        }

        // Update the layout to ensure proper positioning of the new node
        setTimeout(() => {
          if (storeState.updateLayout) {
            storeState.updateLayout('tree');
            console.log('KeyboardManager: Updated layout after adding node in sheet:', sheetId);

            // Force a refresh of the canvas by dispatching a custom event
            try {
              // Create and dispatch a custom event to notify the canvas to refresh
              const refreshEvent = new CustomEvent('mindback:refresh_canvas', {
                detail: { sheetId: sheetId }
              });
              document.dispatchEvent(refreshEvent);
              console.log('KeyboardManager: Dispatched refresh_canvas event for sheet:', sheetId);
            } catch (error) {
              console.error('KeyboardManager: Error dispatching refresh event:', error);
            }
          }
        }, 50);

        // Save the state to ensure it's preserved when switching sheets
        // but only after a longer delay to prevent freezing
        setTimeout(() => {
          try {
            // Use the StoreService to save the sheet state
            saveSheetMindMapStoreState(sheetId);
            console.log('KeyboardManager: Saved state after adding node in sheet:', sheetId);
          } catch (error) {
            console.error('KeyboardManager: Error saving state after adding node:', error);
          }
        }, 300);

        // Focus the stage element for this sheet
        this.focusActiveSheetStage(sheetId);
      }
    } catch (error) {
      console.error('KeyboardManager: Error adding node:', error);
      RegistrationManager.registerEvent(EventType.ERROR_OCCURRED, {
        component: 'KeyboardManager',
        message: `Error adding node: ${error.message}`,
        stack: error.stack
      });
    }
  }

  // Helper method to focus the stage of the active sheet
  private focusActiveSheetStage(sheetId: string): void {
    try {
      // Find the stage container for the active sheet
      const sheetElement = document.querySelector(`[data-sheet-id="${sheetId}"]`);
      if (!sheetElement) {
        console.log('KeyboardManager: Sheet element not found for sheet:', sheetId);
        return;
      }

      // Find the Konva stage container within this sheet
      const stageContainer = sheetElement.querySelector('.konvajs-content');
      if (!stageContainer) {
        console.log('KeyboardManager: Stage container not found for sheet:', sheetId);
        return;
      }

      // Focus the stage container
      (stageContainer as HTMLElement).focus();
      console.log('KeyboardManager: Focused stage for sheet:', sheetId);
    } catch (error) {
      console.error('KeyboardManager: Error focusing stage:', error);
    }
  }

  // Clean up method (for testing or hot reloading)
  public cleanup(): void {
    if (this.isInitialized) {
      window.removeEventListener('keydown', this.handleKeyDown.bind(this), true);
      this.isInitialized = false;
      console.log('KeyboardManager: Cleaned up global keyboard event listener');
    }
  }
}

// Export the singleton instance
export default KeyboardManager.getInstance();
