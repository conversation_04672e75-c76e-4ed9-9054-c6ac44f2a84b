import React, { useState } from 'react';
import { useLayout } from '../hooks/useLayout';
import { LayoutStrategyType } from '../types';
import './LayoutSelector.css';

interface LayoutSelectorProps {
  defaultStrategy?: LayoutStrategyType;
  onStrategyChange?: (strategy: LayoutStrategyType) => void;
}

/**
 * Component for selecting and applying different layout strategies
 */
export const LayoutSelector: React.FC<LayoutSelectorProps> = ({
  defaultStrategy = 'leftToRight',
  onStrategyChange
}) => {
  const { applyLayout, getAvailableStrategies } = useLayout();
  const [selectedStrategy, setSelectedStrategy] = useState<LayoutStrategyType>(defaultStrategy);
  
  // Get available strategies
  const strategies = getAvailableStrategies();
  
  // Strategy names for display
  const strategyNames: Record<LayoutStrategyType, React.ReactNode> = {
    'leftToRight': 'Left to Right',
    'topDown': 'Top Down',
    'bottomUp': 'Bottom Up',
    'radial': 'Radial',
    'compactLeftToRight': (
      <span className="compact-layout-option">
        Compact Layout
        <span className="compact-badge">NEW!</span>
      </span>
    )
  };
  
  // Handle strategy selection
  const handleStrategyChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const strategy = e.target.value as LayoutStrategyType;
    setSelectedStrategy(strategy);
    
    if (onStrategyChange) {
      onStrategyChange(strategy);
    }
  };
  
  // Apply the selected layout strategy
  const handleApplyLayout = () => {
    applyLayout(selectedStrategy);
  };
  
  return (
    <div className="layout-selector">
      <div className="layout-selector-header">
        <h4>Layout Styles</h4>
      </div>
      <div className="layout-selector-content">
        <div className="layout-selector-row">
          <select 
            value={selectedStrategy}
            onChange={handleStrategyChange}
            className="layout-strategy-select"
          >
            {strategies.map(strategy => (
              <option key={strategy} value={strategy}>
                {typeof strategyNames[strategy] === 'string' 
                  ? strategyNames[strategy] 
                  : strategy === 'compactLeftToRight' ? 'Compact Layout (NEW!)' : strategy}
              </option>
            ))}
          </select>
          <button 
            onClick={handleApplyLayout}
            className="apply-layout-button manager-control-button"
            title="Apply Selected Layout"
          >
            <span className="button-icon">↻</span>
            <span className="button-text">Apply Layout</span>
          </button>
        </div>
        
        {selectedStrategy === 'compactLeftToRight' && (
          <div className="layout-description">
            Optimized layout that reduces whitespace and creates more compact diagrams
          </div>
        )}
      </div>
    </div>
  );
}; 