"""
Convert MB_logo.jpg to MB_logo.ico for Windows shortcut icons
"""
try:
    from PIL import Image
    import os
    
    # Path to the JPG logo
    jpg_path = "MB_logo.jpg"
    ico_path = "MB_logo.ico"
    
    # Check if JPG exists
    if not os.path.exists(jpg_path):
        print(f"Error: {jpg_path} not found in current directory")
        print("Please make sure MB_logo.jpg is in the Taskbar folder")
        exit(1)
    
    # Open and convert the image
    print(f"Converting {jpg_path} to {ico_path}...")
    
    # Open the JPG image
    img = Image.open(jpg_path)
    
    # Convert to RGBA if needed (for transparency support)
    if img.mode != 'RGBA':
        img = img.convert('RGBA')
    
    # Resize to common icon sizes (Windows will use the appropriate size)
    sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
    
    # Create a list of images at different sizes
    icon_images = []
    for size in sizes:
        resized_img = img.resize(size, Image.Resampling.LANCZOS)
        icon_images.append(resized_img)
    
    # Save as ICO with multiple sizes
    icon_images[0].save(ico_path, format='ICO', sizes=[(img.width, img.height) for img in icon_images])
    
    print(f"✅ Successfully converted to {ico_path}")
    print(f"Icon file created with sizes: {[f'{s[0]}x{s[1]}' for s in sizes]}")
    
except ImportError:
    print("❌ PIL (Pillow) library not found!")
    print("Installing Pillow...")
    import subprocess
    import sys
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "Pillow"])
        print("✅ Pillow installed successfully!")
        print("Please run this script again to convert the logo.")
    except Exception as e:
        print(f"❌ Failed to install Pillow: {e}")
        print("Please install manually: pip install Pillow")
        
except Exception as e:
    print(f"❌ Error converting image: {e}") 