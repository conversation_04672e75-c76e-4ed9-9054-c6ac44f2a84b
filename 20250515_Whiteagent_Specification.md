# WhiteAgent Specification

## 1. Overview

The WhiteAgent is a meta-agent system designed to enhance the MindBack governance agent with the ability to define, orchestrate, and manage specialized agents for different tasks. It serves as the central intelligence that coordinates agent activities, manages agent definitions, and provides a unified interface for users to interact with the multi-agent system.

## 2. Core Principles

1. **Meta-Agent Architecture**: WhiteAgent functions as a meta-agent that can create, configure, and orchestrate other agents.
2. **Task-Specific Specialization**: Enables the creation of specialized agents for different cognitive tasks.
3. **Unified Interface**: Provides a consistent user experience while leveraging multiple specialized agents.
4. **Transparent Operation**: Makes agent selection and transitions visible to users.
5. **Extensible Framework**: Allows for easy addition of new agent types and capabilities.

## 3. System Architecture

### 3.1 Component Hierarchy

```
WhiteAgent (Meta-Agent)
  ├── Agent Registry
  │     ├── Agent Definitions
  │     └── Agent Templates
  ├── Agent Orchestrator
  │     ├── Task Router
  │     └── Context Manager
  ├── Agent Factory
  │     ├── Agent Builder
  │     └── Agent Configurator
  └── Governance Interface
        ├── Agent Selector
        └── Transition Manager
```

### 3.2 Integration with Existing Architecture

The WhiteAgent integrates with the existing MindBack architecture:

- **Frontend**: Enhances the GovernanceChatDialog component with agent selection and visualization
- **Backend**: Extends the CrewAI framework with meta-agent capabilities
- **API**: Provides endpoints for agent definition, selection, and orchestration
- **MBCP**: Extends the MindBack Content Protocol to include agent attribution and transitions

## 4. Meta-Agent Capabilities

### 4.1 Agent Definition Framework

WhiteAgent provides a standardized framework for defining agents with the following attributes:

```yaml
agent:
  id: "unique_agent_id"
  name: "Human-Readable Agent Name"
  role: "Concise description of agent's role"
  goal: "Primary objective the agent aims to achieve"
  backstory: "Context and background for the agent's perspective"
  capabilities:
    - "Capability 1"
    - "Capability 2"
  constraints:
    - "Constraint 1"
    - "Constraint 2"
  behavior_patterns:
    - "Pattern 1"
    - "Pattern 2"
  communication_style:
    tone: "formal|casual|technical|etc"
    verbosity: "concise|detailed|etc"
    perspective: "first_person|third_person|etc"
  specialization:
    domains: ["domain1", "domain2"]
    tasks: ["task1", "task2"]
  tools:
    - name: "tool_name"
      description: "Tool description"
      parameters:
        - name: "param_name"
          type: "string|number|boolean|array|object"
          required: true|false
          description: "Parameter description"
  data_sources:
    - name: "source_name"
      type: "api|database|web|file"
      access_method: "Method to access this data source"
      reference: "Reference to detailed documentation"
  visual_identity:
    color: "#RRGGBB"
    icon: "icon_identifier"
    avatar: "avatar_url"
```

### 4.2 Agent Types

The WhiteAgent system includes several pre-defined agent types:

1. **Six Thinking Hats Agents**:
   - **Blue Hat**: Process coordination and overview
   - **White Hat**: Factual information and data
   - **Red Hat**: Emotional responses and intuition
   - **Black Hat**: Critical judgment and caution
   - **Yellow Hat**: Optimism and benefits
   - **Green Hat**: Creativity and alternatives

2. **Task-Specific Agents**:
   - **Intent Classifier**: Determines user intent
   - **Orchestration Agent**: Coordinates multi-agent workflows
   - **Execution Agent**: Implements specific actions
   - **Governance Agent**: Manages overall system behavior

3. **Domain-Specific Agents**:
   - **TRIZ Agent**: Systematic innovation
   - **Lateral Thinking Agent**: Creative problem-solving
   - **Patent Search Agent**: Technical research
   - **Market Analysis Agent**: Business insights
   - **Regulatory Research Agent**: Compliance and legal requirements
   - **Financial Analysis Agent**: Business performance assessment
   - **Data Retrieval Agent**: External data collection and processing

### 4.3 Agent Orchestration

WhiteAgent orchestrates agent interactions through:

1. **Task Routing**: Directs user queries to appropriate specialized agents
2. **Context Management**: Maintains shared context across agent transitions
3. **Collaborative Problem-Solving**: Enables multiple agents to contribute to complex tasks
4. **Conflict Resolution**: Resolves contradictory agent recommendations
5. **Seamless Transitions**: Manages handoffs between different agents

## 5. Implementation Details

### 5.1 Backend Implementation

The WhiteAgent backend leverages the CrewAI framework with extensions:

```python
# WhiteAgent Core
class WhiteAgent:
    def __init__(self):
        self.agent_registry = AgentRegistry()
        self.agent_orchestrator = AgentOrchestrator()
        self.agent_factory = AgentFactory()
        self.data_source_manager = DataSourceManager()
        self.tool_registry = ToolRegistry()

    def process_request(self, user_input, meta_topic=None, backstory=None, context=None):
        # Determine intent and select appropriate agents
        intent = self.determine_intent(user_input)
        agents = self.agent_registry.get_agents_for_intent(intent)

        # Create planning document if needed
        if self.requires_planning(intent, user_input):
            planning_agent = self.agent_factory.create_planning_agent()
            plan = planning_agent.create_plan(
                meta_topic=meta_topic,
                backstory=backstory,
                task=user_input
            )
            self.save_planning_document(plan)

        # Prepare data sources and tools
        required_data_sources = self.identify_required_data_sources(intent, user_input)
        data_context = self.data_source_manager.prepare_data_context(required_data_sources)

        # Orchestrate agent collaboration
        crew = self.agent_orchestrator.create_crew(
            agents=agents,
            context=context,
            data_context=data_context,
            meta_topic=meta_topic,
            backstory=backstory
        )
        result = crew.run(input=user_input)

        return self.format_response(result)

    def identify_required_data_sources(self, intent, user_input):
        # Analyze the user input to determine which data sources are needed
        data_sources = []

        # Check for financial analysis needs
        if "BWA" in user_input or "betriebswirtschaftliche Auswertung" in user_input:
            data_sources.append("financial_reports")

        # Check for regulatory research needs
        if "regulatory" in user_input or "compliance" in user_input:
            data_sources.append("regulatory_databases")

        # Check for market research needs
        if "market" in user_input or "industry" in user_input:
            data_sources.append("market_databases")

        return data_sources
```

### 5.2 Agent Registry

The Agent Registry maintains agent definitions and handles agent selection:

```python
class AgentRegistry:
    def __init__(self):
        self.agents = {}
        self.load_agent_definitions()

    def load_agent_definitions(self):
        # Load agent definitions from YAML files
        agent_files = glob.glob("agents/*.yaml")
        for file_path in agent_files:
            with open(file_path, 'r') as f:
                agent_def = yaml.safe_load(f)
                self.register_agent(agent_def)

    def register_agent(self, agent_def):
        agent_id = agent_def.get('id')
        self.agents[agent_id] = agent_def

    def get_agents_for_intent(self, intent):
        # Return appropriate agents based on intent
        return [agent for agent in self.agents.values()
                if intent in agent.get('specialization', {}).get('intents', [])]
```

### 5.3 Data Source Manager

The Data Source Manager handles access to external data sources:

```python
class DataSourceManager:
    def __init__(self):
        self.data_sources = {}
        self.load_data_sources()

    def load_data_sources(self):
        # Load data source definitions from markdown files
        with open("data_sources_reference.md", 'r') as f:
            self.data_sources_reference = f.read()

        # Parse the reference document to extract structured information
        # This is a simplified implementation
        self.data_sources = {
            "financial_reports": {
                "type": "file",
                "access_method": "local_file_system",
                "parser": "bwa_parser"
            },
            "regulatory_databases": {
                "type": "api",
                "sources": ["eurostat", "destatis", "bmwi"],
                "access_method": "api_client"
            },
            "market_databases": {
                "type": "web",
                "sources": ["handelsregister", "northdata"],
                "access_method": "web_scraper"
            }
        }

    def prepare_data_context(self, required_sources):
        # Prepare context with data from required sources
        context = {}

        for source_name in required_sources:
            if source_name in self.data_sources:
                source_config = self.data_sources[source_name]
                context[source_name] = self.fetch_data(source_name, source_config)

        return context

    def fetch_data(self, source_name, source_config):
        # Fetch data from the specified source
        # This would be implemented with appropriate API clients, web scrapers, etc.
        if source_config["type"] == "api":
            return self.fetch_from_api(source_name, source_config)
        elif source_config["type"] == "web":
            return self.fetch_from_web(source_name, source_config)
        elif source_config["type"] == "file":
            return self.fetch_from_file(source_name, source_config)
        else:
            return None
```

### 5.4 Frontend Integration

The WhiteAgent integrates with the frontend through:

1. **Agent Selector UI**: Allows users to explicitly select specialized agents
2. **Visual Indicators**: Shows which agent is currently active
3. **Transition Animations**: Visualizes handoffs between agents
4. **Agent Profiles**: Displays agent capabilities and specializations

## 6. User Experience

### 6.1 Agent Visibility

The WhiteAgent system makes agent operations visible to users through:

1. **Agent Identification**: Clear indication of which agent is responding
2. **Capability Disclosure**: Transparent communication of agent capabilities
3. **Transition Notifications**: Alerts when switching between agents
4. **Reasoning Transparency**: Explanation of why a particular agent was selected

### 6.2 User Controls

Users can interact with the WhiteAgent system through:

1. **Direct Agent Selection**: Explicitly choose a specialized agent
2. **Agent Configuration**: Adjust agent parameters and behavior
3. **Workflow Definition**: Create custom agent sequences for specific tasks
4. **Feedback Mechanisms**: Provide feedback on agent performance

## 7. Implementation Roadmap

### Phase 1: Core Framework (Week 1-2)
- Implement WhiteAgent core architecture
- Develop agent definition schema
- Create basic agent registry
- Integrate with existing governance agent

### Phase 2: Six Thinking Hats Agents (Week 3-4)
- Implement the six thinking hats agents
- Develop agent transition mechanisms
- Create visual indicators for active agents
- Test basic multi-agent workflows

### Phase 3: Advanced Orchestration (Week 5-6)
- Implement collaborative problem-solving
- Develop context management system
- Create conflict resolution mechanisms
- Enhance agent selection algorithms

### Phase 4: User Experience (Week 7-8)
- Implement agent selector UI
- Develop agent profiles and visualization
- Create transition animations
- Implement user feedback mechanisms

## 8. Technical Requirements

### 8.1 Dependencies

- **CrewAI**: For agent definition and orchestration
- **LangChain**: For LLM integration and tools
- **FastAPI**: For backend API endpoints
- **React**: For frontend components
- **Zustand**: For state management
- **Google Custom Search API**: For web search capabilities
- **FireCrawl/ScrapingBee**: For web scraping and data extraction
- **HTTPX**: For API requests to external data sources
- **Beautiful Soup/Playwright**: For HTML parsing and browser automation
- **Pandas/NumPy**: For data processing and analysis
- **NetworkX**: For relationship mapping and graph analysis
- **Markdown**: For parsing and generating markdown documents

### 8.2 Integration Points

1. **GovernanceChatDialog**: Frontend entry point for WhiteAgent
2. **CrewAI Framework**: Backend foundation for agent orchestration
3. **MBCP Protocol**: Data structure for agent communication
4. **MindBookStore**: Integration with MindBook/MindSheet architecture
5. **Data Sources Reference**: Markdown file containing data source definitions
6. **Planning Documents**: Markdown files for task planning and execution tracking
7. **Model Context Protocol (MCP)**: Framework for tool integration and context management

### 8.3 External Data Access

The WhiteAgent requires access to various external data sources:

1. **Statistical Databases**:
   - Eurostat API for EU-wide statistics
   - Destatis API for German national statistics
   - Industry-specific statistical sources

2. **Business Registries**:
   - Handelsregister for German company information
   - North Data for company financial and legal data
   - Industry association databases

3. **Web Search and Crawling**:
   - Google Custom Search API for general web search
   - FireCrawl/ScrapingBee for targeted data extraction
   - Domain-specific search capabilities

4. **Financial Analysis**:
   - BWA (Betriebswirtschaftliche Auswertung) templates and parsers
   - Financial ratio calculators
   - Industry benchmark databases

## 9. Task Planning and Execution

### 9.1 Planning Process

The WhiteAgent implements a structured planning process for complex tasks:

1. **Meta-Topic Analysis**: Understanding the overarching goal (e.g., "achieving positive cash flow in the scaffolding market within 6 months")
2. **Backstory Integration**: Incorporating contextual information to frame the specific task
3. **Task Decomposition**: Breaking down complex tasks into manageable sub-tasks
4. **Resource Identification**: Determining required data sources and tools
5. **Agent Selection**: Choosing appropriate specialized agents for each sub-task
6. **Timeline Creation**: Establishing a sequence and timeline for task execution

### 9.2 Planning Document Structure

Planning documents are generated in markdown format with the following structure:

```markdown
# Task Planning Document

## Meta-Topic
[Description of the overarching goal]

## Backstory
[Contextual information framing the specific task]

## Task Definition
[Specific task to be accomplished]

## Action Plan
- [ ] Action 1: [Description]
  - [ ] Sub-action 1.1: [Description]
  - [ ] Sub-action 1.2: [Description]
- [ ] Action 2: [Description]
  - [ ] Sub-action 2.1: [Description]
  - [ ] Sub-action 2.2: [Description]

## Required Resources
- Data Sources: [List of required data sources]
- Tools: [List of required tools]
- Agents: [List of required specialized agents]

## Timeline
- Phase 1 (Day 1-2): [Description]
- Phase 2 (Day 3-5): [Description]
- Phase 3 (Day 6-10): [Description]

## Success Criteria
- [Measurable outcome 1]
- [Measurable outcome 2]
- [Measurable outcome 3]
```

### 9.3 Execution Tracking

The WhiteAgent tracks task execution through:

1. **Checklist Updates**: Marking completed actions in the planning document
2. **Progress Reporting**: Generating progress reports at regular intervals
3. **Obstacle Identification**: Documenting challenges and proposed solutions
4. **Resource Utilization**: Tracking usage of data sources and tools
5. **Outcome Measurement**: Evaluating results against success criteria

## 10. Conclusion

The WhiteAgent specification provides a comprehensive framework for implementing meta-agent functionality in the MindBack governance agent. By enabling the definition, orchestration, and management of specialized agents, WhiteAgent enhances the system's ability to handle diverse tasks while maintaining a unified user experience.

This specification serves as the foundation for implementing the WhiteAgent system, with particular emphasis on data retrieval capabilities, task planning, and execution tracking. The system is designed to handle complex business analysis tasks, including regulatory research and financial analysis, by leveraging a wide range of external data sources and specialized agents.

The implementation follows a modular approach that allows for future extension as new requirements and capabilities emerge.
