"""
MindBack Content Protocol (MBCP) Models
Contains Pydantic models for MBCP structures and validation.
"""
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field, validator

# Import intent types from the central configuration
try:
    from ...config.intent_config import get_intent_types, is_valid_intent
except ImportError:
    # Fallback to absolute import if relative import fails
    from config.intent_config import get_intent_types, is_valid_intent

# Get intent types from the configuration
INTENT_TYPES = get_intent_types()
AGENT_TYPES = ["blue_hat", "white_hat", "red_hat", "black_hat", "yellow_hat", "green_hat"]

# Request models
class LLMChatRequest(BaseModel):
    """
    Request model for LLM chat interaction
    """
    prompt: str = Field(..., description="The user prompt/query")
    prompt_type: Optional[str] = Field(None, description="The type of prompt to use")
    model: str = Field("gpt-3.5-turbo", description="The LLM model to use")
    temperature: float = Field(0.7, description="Temperature for LLM generation")
    topic: Optional[str] = Field(None, description="Topic for the conversation")
    intent: Optional[str] = Field(None, description="Intent for the conversation")
    system_prompt: Optional[str] = Field(None, description="System prompt override")
    template_type: Optional[str] = Field(None, description="Template type for mindmap generation")
    messages: Optional[List[Dict[str, Any]]] = Field(None, description="Previous messages in the conversation")

# Action model for MBCP
class ActionModel(BaseModel):
    title: str = Field(..., description="Short name for the action")
    owner: str = Field(..., description="Responsible person")
    due_date: str = Field(..., description="Deadline in YYYY-MM-DD format")
    system: Optional[str] = Field(None, description="System to send action to")
    status: str = Field("pending", description="Action status")

    @validator('status')
    def validate_status(cls, v):
        if v not in ["pending", "in_progress", "done"]:
            raise ValueError(f"Invalid status value: {v}")
        return v

# Metadata model for MBCP
class MetadataModel(BaseModel):
    intent: str = Field(..., description="Node intent classification")
    agent: Optional[str] = Field(None, description="Thinking hat perspective")
    tags: List[str] = Field(default_factory=list, description="Node tags")
    action: Optional[ActionModel] = Field(None, description="Action item details")

    @validator('intent')
    def validate_intent(cls, v):
        if not is_valid_intent(v):
            raise ValueError(f"Invalid intent value: {v}")
        return v

    @validator('agent')
    def validate_agent(cls, v):
        if v and v not in AGENT_TYPES:
            raise ValueError(f"Invalid agent value: {v}")
        return v

# Node model for MBCP
class NodeModel(BaseModel):
    id: str = Field(..., description="Unique node identifier")
    text: str = Field(..., description="Node title text")
    description: str = Field(..., description="Node detailed description")
    children: List['NodeModel'] = Field(default_factory=list, description="Child nodes")
    metadata: MetadataModel = Field(..., description="Node metadata")

# Required for recursive models
NodeModel.update_forward_refs()

# Response models for LLM chat
class LLMChatResponse(BaseModel):
    """
    Response model for LLM chat interaction
    """
    success: bool = Field(..., description="Indicates if the request was successful")
    content: Dict[str, Any] = Field(..., description="Response content")
    model: Optional[str] = Field(None, description="The LLM model used")
    error: Optional[str] = Field(None, description="Error message if request failed")
    validation_info: Optional[Dict[str, Any]] = Field(None, description="Validation information")