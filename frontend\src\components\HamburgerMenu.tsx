import React, { useState, useEffect } from 'react';
import { 
  saveMindBook, 
  getMindBooksList, 
  loadMindBook,
  getSessionInfo,
  type MindBookListItem
} from '../core/services/MindBookPersistenceService';
import { useMindBookStore } from '../core/state/MindBookStore';
import './HamburgerMenu.css';

interface HamburgerMenuProps {
  isOpen: boolean;
  onClose: () => void;
  onOpenContextManager: () => void;
  onOpenProjectManager: () => void;
  onNavigateToStartup: () => void;
}

const HamburgerMenu: React.FC<HamburgerMenuProps> = ({
  isOpen,
  onClose,
  onOpenContextManager,
  onOpenProjectManager,
  onNavigateToStartup
}) => {
  const [recentMindBooks, setRecentMindBooks] = useState<MindBookListItem[]>([]);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [saveAsName, setSaveAsName] = useState('');
  const [saveAsDescription, setSaveAsDescription] = useState('');
  const [currentSessionName, setCurrentSessionName] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);

  const store = useMindBookStore();

  useEffect(() => {
    if (isOpen) {
      loadRecentMindBooks();
      loadCurrentSessionInfo();
    }
  }, [isOpen]);

  const loadRecentMindBooks = () => {
    try {
      const allMindBooks = getMindBooksList(); // This is synchronous
      const recent = allMindBooks
        .sort((a, b) => b.savedAt - a.savedAt)
        .slice(0, 5);
      setRecentMindBooks(recent);
    } catch (error) {
      console.error('Failed to load recent MindBooks:', error);
      setRecentMindBooks([]);
    }
  };

  const loadCurrentSessionInfo = async () => {
    try {
      // Get current active MindBook name from localStorage
      const activeMindBookId = localStorage.getItem('active_mindbook');
      if (activeMindBookId) {
        const mindBookData = localStorage.getItem(`mindbook_${activeMindBookId}`);
        if (mindBookData) {
          const parsedData = JSON.parse(mindBookData);
          setCurrentSessionName(parsedData.name || null);
          return;
        }
      }
      
      // Check if there's an auto-saved session
      const autoSaveData = localStorage.getItem('mindbook_autosave');
      if (autoSaveData) {
        const parsedData = JSON.parse(autoSaveData);
        setCurrentSessionName(parsedData.name || null);
        return;
      }
      
      setCurrentSessionName(null);
    } catch (error) {
      console.error('Failed to load session info:', error);
      setCurrentSessionName(null);
    }
  };

  const handleSaveCurrentMindBook = () => {
    if (!currentSessionName) {
      setShowSaveDialog(true);
      return;
    }

    setSaving(true);
    try {
      const success = saveMindBook(currentSessionName); // This is synchronous
      if (success) {
        console.log('MindBook saved successfully');
        onClose();
      } else {
        console.error('Failed to save MindBook');
      }
    } catch (error) {
      console.error('Failed to save MindBook:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleSaveAs = () => {
    if (!saveAsName.trim()) return;

    setSaving(true);
    try {
      const success = saveMindBook(saveAsName.trim(), saveAsDescription.trim() || undefined); // This is synchronous
      if (success) {
        console.log('MindBook saved as:', saveAsName);
        setShowSaveDialog(false);
        setSaveAsName('');
        setSaveAsDescription('');
        setCurrentSessionName(saveAsName.trim());
        onClose();
      } else {
        console.error('Failed to save MindBook as:', saveAsName);
      }
    } catch (error) {
      console.error('Failed to save MindBook:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleLoadMindBook = (mindBookId: string) => {
    try {
      const success = loadMindBook(mindBookId); // This is synchronous
      if (success) {
        onClose();
      } else {
        console.error('Failed to load MindBook:', mindBookId);
      }
    } catch (error) {
      console.error('Failed to load MindBook:', error);
    }
  };

  const hasSheets = store.sheets.length > 0;

  if (!isOpen) return null;

  return (
    <>
      <div className="hamburger-menu-overlay" onClick={onClose} />
      <div className="hamburger-menu">
        <div className="hamburger-menu-header">
          <h3>Menu</h3>
          <button className="hamburger-menu-close" onClick={onClose}>×</button>
        </div>

        <div className="hamburger-menu-content">
          {/* Current Session Actions */}
          <div className="menu-section">
            <h4 className="menu-section-title">Current Session</h4>
            {hasSheets ? (
              <>
                <button 
                  className="menu-item primary"
                  onClick={handleSaveCurrentMindBook}
                  disabled={saving}
                >
                  {saving ? 'Saving...' : currentSessionName ? `Save "${currentSessionName}"` : 'Save Current MindBook as...'}
                </button>
                <button 
                  className="menu-item"
                  onClick={() => setShowSaveDialog(true)}
                >
                  Save Current MindBook as...
                </button>
              </>
            ) : (
              <p className="menu-item-disabled">No sheets to save</p>
            )}
          </div>

          {/* Recent MindBooks */}
          <div className="menu-section">
            <h4 className="menu-section-title">Recent MindBooks</h4>
            {recentMindBooks.length > 0 ? (
              <div className="recent-mindbooks-list">
                {recentMindBooks.map((mindBook) => (
                  <button
                    key={mindBook.id}
                    className="menu-item recent-mindbook-item"
                    onClick={() => handleLoadMindBook(mindBook.id)}
                  >
                    <div className="recent-mindbook-info">
                      <span className="recent-mindbook-name">{mindBook.name}</span>
                      <span className="recent-mindbook-date">
                        {new Date(mindBook.savedAt).toLocaleDateString()}
                      </span>
                    </div>
                  </button>
                ))}
              </div>
            ) : (
              <p className="menu-item-disabled">No recent MindBooks</p>
            )}
            <button 
              className="menu-item secondary"
              onClick={onNavigateToStartup}
            >
              Browse All MindBooks
            </button>
          </div>

          {/* Context & Settings */}
          <div className="menu-section">
            <h4 className="menu-section-title">Context & Settings</h4>
            <button 
              className="menu-item"
              onClick={() => {
                onOpenContextManager();
                onClose();
              }}
            >
              Context Manager
            </button>
            <button 
              className="menu-item"
              onClick={() => {
                onOpenProjectManager();
                onClose();
              }}
            >
              Project Management
            </button>
            <button 
              className="menu-item"
              onClick={() => {
                // TODO: Implement app settings
                console.log('App Settings - to be implemented');
                onClose();
              }}
            >
              App Settings
            </button>
          </div>
        </div>
      </div>

      {/* Save As Dialog */}
      {showSaveDialog && (
        <div className="save-dialog-overlay">
          <div className="save-dialog">
            <div className="save-dialog-header">
              <h3>Save MindBook</h3>
              <button 
                className="save-dialog-close"
                onClick={() => {
                  setShowSaveDialog(false);
                  setSaveAsName('');
                  setSaveAsDescription('');
                }}
              >
                ×
              </button>
            </div>
            <div className="save-dialog-content">
              <div className="save-dialog-field">
                <label htmlFor="save-as-name">MindBook Name *</label>
                <input
                  id="save-as-name"
                  type="text"
                  value={saveAsName}
                  onChange={(e) => setSaveAsName(e.target.value)}
                  placeholder="Enter MindBook name"
                  autoFocus
                />
              </div>
              <div className="save-dialog-field">
                <label htmlFor="save-as-description">Description (optional)</label>
                <textarea
                  id="save-as-description"
                  value={saveAsDescription}
                  onChange={(e) => setSaveAsDescription(e.target.value)}
                  placeholder="Brief description of this MindBook"
                  rows={3}
                />
              </div>
            </div>
            <div className="save-dialog-actions">
              <button 
                className="save-dialog-button secondary"
                onClick={() => {
                  setShowSaveDialog(false);
                  setSaveAsName('');
                  setSaveAsDescription('');
                }}
              >
                Cancel
              </button>
              <button 
                className="save-dialog-button primary"
                onClick={handleSaveAs}
                disabled={!saveAsName.trim() || saving}
              >
                {saving ? 'Saving...' : 'Save MindBook'}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default HamburgerMenu; 