# System Architecture Overview

## Components

- Governance Box
- Intention Workflows
- AI Agents
- Mindmap Generation


## AI Integration Features

### AI Integration Features
1. **LLM Integration**: Interface with multiple LLM providers for text generation
2. **Governance Agent**: Dialog-based interface for interacting with LLMs
3. **CrewAI Integration**: Multi-agent system for specialized tasks
   - Status: Implemented with tool selection capability
   - Component: Integrated with Governance Agent dialog with toggle and settings
4. **Six Thinking Hats**: Specialized agents for different thinking modes
   - White Hat: Facts and information
   - Red Hat: Emotions and feelings
   - Black Hat: Critical judgment
   - Yellow Hat: Benefits and optimism
   - Green Hat: Creative alternatives
   - Blue Hat: Process control (via Governance Agent)

## Development Status

## AI Integration Features

### AI Integration Features
1. **LLM Integration**: Interface with multiple LLM providers for text generation
2. **Governance Agent**: Dialog-based interface for interacting with LLMs
3. **CrewAI Integration**: Multi-agent system for specialized tasks
   - Status: Implemented with tool selection capability
   - Component: Integrated with Governance Agent dialog with toggle and settings
4. **Six Thinking Hats**: Specialized agents for different thinking modes
   - White Hat: Facts and information
   - Red Hat: Emotions and feelings
   - Black Hat: Critical judgment
   - Yellow Hat: Benefits and optimism
   - Green Hat: Creative alternatives
   - Blue Hat: Process control (via Governance Agent)

## Development Status

## Mindmap Requirements

## Core Requirements

## AI Integration Features

### AI Integration Features
1. **LLM Integration**: Interface with multiple LLM providers for text generation
2. **Governance Agent**: Dialog-based interface for interacting with LLMs
3. **CrewAI Integration**: Multi-agent system for specialized tasks
   - Status: Implemented with tool selection capability
   - Component: Integrated with Governance Agent dialog with toggle and settings
4. **Six Thinking Hats**: Specialized agents for different thinking modes
   - White Hat: Facts and information
   - Red Hat: Emotions and feelings
   - Black Hat: Critical judgment
   - Yellow Hat: Benefits and optimism
   - Green Hat: Creative alternatives
   - Blue Hat: Process control (via Governance Agent)

## Development Status

## AI Integration Features

### AI Integration Features
1. **LLM Integration**: Interface with multiple LLM providers for text generation
2. **Governance Agent**: Dialog-based interface for interacting with LLMs
3. **CrewAI Integration**: Multi-agent system for specialized tasks
   - Status: Implemented with tool selection capability
   - Component: Integrated with Governance Agent dialog with toggle and settings
4. **Six Thinking Hats**: Specialized agents for different thinking modes
   - White Hat: Facts and information
   - Red Hat: Emotions and feelings
   - Black Hat: Critical judgment
   - Yellow Hat: Benefits and optimism
   - Green Hat: Creative alternatives
   - Blue Hat: Process control (via Governance Agent)

## Development Status

## Mindmap Requirements

## Core Requirements

## Mindmap Core Requirements

## Core Requirements