import { StateCreator } from 'zustand';
import { MindMapStore, HistoryEntry } from './types';

export interface UndoRedoSlice {
  history: HistoryEntry[];
  currentHistoryIndex: number;
  maxHistorySize: number;
  addToHistory: () => void;
  undo: () => void;
  redo: () => void;
  canUndo: () => boolean;
  canRedo: () => boolean;
}

export const createUndoRedoSlice: StateCreator<MindMapStore, [], [], UndoRedoSlice> = (set, get) => ({
  history: [],
  currentHistoryIndex: -1,
  maxHistorySize: 50,

  addToHistory: () => {
    const { nodes, connections, position, scale, history, currentHistoryIndex, maxHistorySize } = get();
    const newEntry: HistoryEntry = { nodes, connections, position, scale };

    // Remove future history if we're not at the end
    const newHistory = history.slice(0, currentHistoryIndex + 1);
    newHistory.push(newEntry);

    // Remove oldest entries if we exceed maxHistorySize
    if (newHistory.length > maxHistorySize) {
      newHistory.shift();
    }

    set({
      history: newHistory,
      currentHistoryIndex: newHistory.length - 1
    });
  },

  undo: () => {
    const { history, currentHistoryIndex } = get();
    if (currentHistoryIndex <= 0) return;

    const previousState = history[currentHistoryIndex - 1];
    set({
      ...previousState,
      currentHistoryIndex: currentHistoryIndex - 1
    });
  },

  redo: () => {
    const { history, currentHistoryIndex } = get();
    if (currentHistoryIndex >= history.length - 1) return;

    const nextState = history[currentHistoryIndex + 1];
    set({
      ...nextState,
      currentHistoryIndex: currentHistoryIndex + 1
    });
  },

  canUndo: () => {
    const { currentHistoryIndex } = get();
    return currentHistoryIndex > 0;
  },

  canRedo: () => {
    const { history, currentHistoryIndex } = get();
    return currentHistoryIndex < history.length - 1;
  }
}); 