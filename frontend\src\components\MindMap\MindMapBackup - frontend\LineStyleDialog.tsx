import React, { useState } from 'react';
import { useMindMap } from './context/MindMapContext';

export const LineStyleDialog: React.FC = () => {
  const {
    connections,
    setConnections,
    selectedId,
    setSelectedId
  } = useMindMap();

  const [lineStyle, setLineStyle] = useState<'straight' | 'curved'>('straight');
  const [lineThickness, setLineThickness] = useState<number>(2);
  const [lineColor, setLineColor] = useState<string>('#64748b');

  if (!selectedId || !selectedId.startsWith('conn-')) return null;

  // Parse connection IDs from the selectedId format "conn-fromId-toId"
  const [_, fromId, toId] = selectedId.split('-');
  
  // Find the selected connection
  const selectedConnection = connections.find(
    conn => conn.from === fromId && conn.to === toId
  );

  if (!selectedConnection) return null;

  // Initialize state from selected connection
  React.useEffect(() => {
    if (selectedConnection) {
      setLineStyle(selectedConnection.style || 'straight');
      setLineThickness(selectedConnection.thickness || 2);
      setLineColor(selectedConnection.color || '#64748b');
    }
  }, [selectedConnection]);

  const handleSave = () => {
    // Update the connection with new style properties
    setConnections(connections.map(conn => {
      if (conn.from === fromId && conn.to === toId) {
        return {
          ...conn,
          style: lineStyle,
          thickness: lineThickness,
          color: lineColor
        };
      }
      return conn;
    }));
    
    setSelectedId(null);
  };

  return (
    <div className="dialog-overlay">
      <div className="dialog">
        <div className="dialog-header">
          <h3>Edit Connection Style</h3>
        </div>
        
        <div className="form-group">
          <label>Line Style:</label>
          <select
            value={lineStyle}
            onChange={(e) => setLineStyle(e.target.value as 'straight' | 'curved')}
          >
            <option value="straight">Straight</option>
            <option value="curved">Curved</option>
          </select>
        </div>
        
        <div className="form-group">
          <label>Thickness:</label>
          <select
            value={lineThickness}
            onChange={(e) => setLineThickness(Number(e.target.value))}
          >
            <option value="1">Thin</option>
            <option value="2">Medium</option>
            <option value="3">Thick</option>
          </select>
        </div>
        
        <div className="form-group">
          <label>Color:</label>
          <input
            type="color"
            value={lineColor}
            onChange={(e) => setLineColor(e.target.value)}
          />
        </div>
        
        <div className="dialog-actions">
          <button onClick={handleSave}>Save</button>
          <button onClick={() => setSelectedId(null)}>Cancel</button>
        </div>
      </div>
    </div>
  );
}; 