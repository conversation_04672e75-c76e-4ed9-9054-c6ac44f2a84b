/* Clean Startup Screen - Cursor-inspired design */
.startup-screen-clean {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #000000;
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  padding: 0;
  margin: 0;
  position: relative;
}

/* Logo section - centered with same width as buttons combined */
.startup-logo-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 60px;
}

.startup-logo-centered {
  width: 424px;
  height: auto;
  max-height: 120px;
  object-fit: contain;
  filter: brightness(1.1);
}

/* Action buttons - side by side, both same style */
.startup-actions-clean {
  display: flex;
  gap: 24px;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
}

.startup-button-left,
.startup-button-right {
  background: transparent;
  border: 1px solid #333333;
  border-radius: 8px;
  color: #ffffff;
  padding: 16px 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 200px;
  width: 200px;
  text-align: center;
  white-space: nowrap;
}

.startup-button-left:hover,
.startup-button-right:hover {
  background: #1a1a1a;
  border-color: #555555;
  transform: translateY(-1px);
}

/* Recent MindBooks section */
.startup-recent-section {
  width: 424px;
  margin-top: 0;
}

.startup-no-mindbooks {
  padding: 20px;
  text-align: center;
  color: #888888;
  font-size: 14px;
  font-style: italic;
}

/* Keyboard shortcuts hint */
.startup-shortcuts-hint {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  text-align: center;
  color: #666666;
  font-size: 12px;
  padding: 20px;
  background: #000000;
}

/* Main content wrapper to help with centering */
.startup-content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60px; /* Space for the footer */
}

.startup-shortcuts-hint kbd {
  background: #333333;
  border: 1px solid #555555;
  border-radius: 3px;
  color: #ffffff;
  display: inline-block;
  font-size: 11px;
  line-height: 1.2;
  margin: 0 2px;
  padding: 2px 4px;
  white-space: nowrap;
}

/* Loading state */
.startup-loading {
  color: #888888;
  text-align: center;
  padding: 20px;
}

/* Recent MindBooks list */
.startup-recent-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.startup-recent-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: transparent;
  cursor: pointer;
  transition: background 0.2s ease;
  font-family: Arial, sans-serif;
}

.startup-recent-item:hover {
  background: rgba(255, 255, 255, 0.005);
}

.startup-recent-name {
  color: #666666;
  font-weight: normal;
  font-size: 14px;
  text-align: left;
  flex: 1;
}

.startup-recent-path {
  color: #666666;
  font-size: 12px;
  text-align: right;
  font-weight: normal;
  margin-left: 20px;
} 