# Development Rules - STRICT COMPLIANCE REQUIRED

## 🛑 THE GOLDEN RULE
**IF IT AIN'T BROKE, DON'T FIX IT**

Your mindmap functionality is working. Do not change working code.

## 📝 Before ANY Code Change - MANDATORY CHECKLIST

### Step 1: Record Current State
- [ ] Application starts without errors
- [ ] Can create automatic mindmap from governance chat
- [ ] Can create manual nodes with Tab key
- [ ] Can edit nodes by double-clicking
- [ ] Can switch between sheets
- [ ] Take screenshot of working mindmap

### Step 2: Justify the Change
- [ ] What specific bug does this fix?
- [ ] What specific user-visible improvement does this add?
- [ ] Is this change absolutely necessary?
- [ ] Can the user still use the app without this change? (If yes, consider skipping)

### Step 3: Plan the Minimal Change
- [ ] Which exact lines of code need to change?
- [ ] Is there a way to make a smaller change?
- [ ] Can this be done with CSS only?
- [ ] Can this be done by adding code instead of changing existing code?

### Step 4: Safety Measures
- [ ] `git status` (ensure clean working directory)
- [ ] `git commit -m "Working state before [change description]"`
- [ ] Copy the file you're changing to a backup location
- [ ] Tell someone what you're about to change

### Step 5: Make the Change
- [ ] Change ONLY the lines identified in Step 3
- [ ] Do not "clean up" other code while you're there
- [ ] Do not rename variables or functions
- [ ] Do not "improve" the code structure

### Step 6: Test Immediately
- [ ] Start the app
- [ ] Test the exact feature you changed
- [ ] Test that automatic mindmap creation still works
- [ ] Test that manual node creation still works
- [ ] Test that node editing still works
- [ ] Test that sheet switching still works

### Step 7: If ANYTHING Breaks
- [ ] `git stash` (save your changes)
- [ ] `git reset --hard HEAD~1` (go back to working state)
- [ ] Test that everything works again
- [ ] Either fix the change or abandon it
- [ ] NEVER push broken code

## 🚫 ABSOLUTELY FORBIDDEN

### Never, ever do these things:
1. **Rewrite a working component from scratch**
2. **Change the store architecture**  
3. **"Refactor" working code**
4. **Add new dependencies without approval**
5. **Change file structure or move files**
6. **Delete working components**
7. **"Optimize" performance of working code**
8. **Change how stores are accessed**
9. **Modify the MBCP processing logic**
10. **"Modernize" or "clean up" working code**

### Red flag phrases that mean STOP:
- "Let me rewrite this to be cleaner"
- "We should use a different architecture"
- "This code is messy, let me fix it"
- "I found a better way to do this"
- "Let me refactor this component"
- "We should upgrade/change this library"

## ✅ SAFE CHANGES (Allowed)

### Only these types of changes are permitted:
1. **Add CSS styling** (visual changes only)
2. **Add new optional features** (that don't affect existing code)
3. **Fix obvious bugs** (that break current functionality)
4. **Add simple if/else conditions** (that don't change existing flow)
5. **Add console.log statements** (for debugging)
6. **Update comments and documentation**

## 🎯 Daily Focus Strategy

### Monday: Visual Improvements Only
- Add styling, colors, borders
- Make manual vs auto nodes visually different
- NO logic changes

### Tuesday: Small Bug Fixes Only  
- Fix specific errors in console
- Fix obvious broken functionality
- NO architectural changes

### Wednesday: Testing Day
- Test all existing functionality
- Document what works
- NO new development

### Thursday: Documentation Day
- Update documentation
- Record how features work
- NO code changes

### Friday: Planning Day
- Plan next week's small changes
- Review what was accomplished
- NO coding

## 📊 Progress Tracking

### Weekly Goal: ONE Small Improvement
Not five improvements. Not a rewrite. ONE small, visible improvement.

### Success Metrics:
- ✅ App still works at end of week
- ✅ One new visual feature added
- ✅ No existing functionality broken
- ✅ No rewrites attempted

### Failure Indicators:
- ❌ App broken at end of week
- ❌ Attempted to rewrite components
- ❌ Changed store architecture
- ❌ Spent time on "refactoring"

## 🆘 Emergency Contacts

If you're tempted to do a big rewrite:
1. Step away from the computer
2. Go for a walk
3. Come back and read this document again
4. Pick ONE tiny thing to improve instead

Remember: **A working app with small improvements is infinitely better than a broken app with ambitious changes.** 