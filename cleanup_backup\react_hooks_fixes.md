# React Hooks Violations Fixes

## Problem Summary

After upgrading react-konva to version 18.2.10, we were still experiencing React Hooks violations in the MindSheet component. The specific issues were:

1. **Hooks Order Violation**: The order of hooks was changing between renders, causing <PERSON>act to throw errors.
2. **Hooks Inside Hooks**: Hooks were being called inside other hooks (specifically inside useEffect).
3. **Conditional Hook Calls**: Hooks were being called conditionally, violating the Rules of Hooks.

## Changes Made

### 1. Fixed Helper Functions

We refactored the `initializeMindMapForSheet` function to be a pure function that doesn't use any hooks or call any functions that use hooks:

```javascript
/**
 * Helper function to initialize a mindmap with content using a specific store
 * This is a pure function that doesn't use any hooks or call any functions that use hooks
 * It's defined outside the component to avoid React Hooks violations
 */
const initializeMindMapForSheet = (
  store: ReturnType<typeof getMindMapStore>,
  mbcpData: any,
  sheetId: string
): boolean => {
  try {
    // Get the store state directly
    const storeState = store.getState();
    
    // Extract data from mbcpData safely
    if (!mbcpData) {
      console.error('MindSheet: No MBCP data provided');
      return false;
    }
    
    // ... rest of the function ...
    
    return true;
  } catch (error) {
    console.error('MindSheet: Error initializing mindmap:', error);
    return false;
  }
};
```

### 2. Used Refs Instead of Props

We modified the useEffect hook to use `contentRef.current` instead of the `content` prop directly:

```javascript
useEffect(() => {
  // ... code ...
  
  // If we have content, initialize the mindmap with it
  if (contentRef.current) {
    // We need to use a custom version of initializeMindMap that uses our sheet-specific store
    const success = initializeMindMapForSheet(store, contentRef.current, id);
    console.log('MindSheet: Mindmap initialization ' + (success ? 'successful' : 'failed'));
  } else {
    console.warn('MindSheet: No content available for mindmap initialization');
  }
  
  // ... code ...
}, [id, contentType]);
```

### 3. Added Error Handling

We added try-catch blocks around JSON.stringify calls to prevent errors:

```javascript
try {
  console.log('MindSheet: Content provided for initialization:',
    typeof contentRef.current === 'object' ?
      JSON.stringify({
        intent: contentRef.current.intent,
        text: contentRef.current.text,
        // ... more properties ...
      }) :
      typeof contentRef.current
  );
} catch (e) {
  console.warn('MindSheet: Could not stringify content for logging');
}
```

### 4. Ensured Consistent Hook Order

We organized all hooks at the top level of the component in a consistent order:

```javascript
const MindSheet: React.FC<MindSheetProps> = ({
  id,
  title,
  contentType,
  isActive,
  content,
  onActivate
}) => {
  // 1. useState hooks
  const [initialized, setInitialized] = useState(false);
  const [sheetStore, setSheetStore] = useState<ReturnType<typeof getMindMapStore> | null>(null);
  
  // 2. useRef hooks
  const storeRef = useRef<ReturnType<typeof getMindMapStore> | null>(null);
  const contentRef = useRef<any>(content);
  
  // 3. Custom hooks
  const chatForkStore = useChatForkStore();
  const mindBookStore = useMindBookStore();
  
  // 4. useMemo hooks for handlers and components
  const openMindMapManager = useMemo(() => (e: React.MouseEvent) => {
    // ... handler code ...
  }, []);
  
  // ... more useMemo hooks ...
  
  // 5. useEffect hooks
  useEffect(() => {
    contentRef.current = content;
  }, [content]);
  
  // ... more useEffect hooks ...
  
  // Rest of the component...
};
```

## Testing

To verify that our changes have fixed the React Hooks violations, we should test both the manual and automatic workflows for building mindmaps:

1. **Manual Workflow**: Click the "Build Mind Map" button after receiving a teleological response
2. **Automatic Workflow**: Select "Teleological" from the intent dropdown and enter a prompt

Both workflows should now work without any React Hooks violations in the console.

## Conclusion

The root cause of the React Hooks violations was that we were calling hooks inside other hooks and not maintaining a consistent order of hooks between renders. By refactoring the helper functions to be pure functions that don't use hooks, using refs instead of props directly, and ensuring a consistent hook order, we've fixed the React Hooks violations in both workflows.

These changes, combined with the upgrade to react-konva 18.2.10, should resolve the dependency conflicts and React Hooks violations in the mindmap creation workflows.
