/**
 * UIPositioningManager.ts
 *
 * Core class for managing UI element positioning, sizing, and visibility.
 */

// Define element types for better type safety
export type UIElementType =
  | 'governance-box'
  | 'context-panel'
  | 'mindmap-manager'
  | 'node-box'
  | 'project-manager'
  | 'chat-fork'
  | string; // Allow for future elements

// Define z-index layers to ensure consistent stacking
export enum ZIndexLayer {
  BASE = 1000,
  PANELS = 1100,
  DIALOGS = 1200,
  POPOVERS = 1300,
  TOOLTIPS = 1400,
  MODALS = 1500
}

// Define positioning strategies
export type PositioningStrategy =
  | 'default'       // Use default position
  | 'right-side'    // Position on right side of screen
  | 'left-side'     // Position on left side of screen
  | 'center'        // Center on screen
  | 'avoid-overlap' // Avoid overlapping with other elements
  | 'manual';       // Manual positioning (user-defined)

export interface UIElementPosition {
  id: string;
  type: UIElementType;
  defaultPosition: { x: number, y: number };
  currentPosition: { x: number, y: number };
  size: { width: number, height: number };
  zIndex: number;
  visible: boolean;
  strategy: PositioningStrategy;
  priority: number; // Higher priority elements take precedence in positioning
  constraints?: {
    minX?: number;
    maxX?: number;
    minY?: number;
    maxY?: number;
  };
}

export class UIPositioningManager {
  private elements: Record<string, UIElementPosition> = {};
  private collisionHandlers: Record<string, (collidingElements: string[]) => void> = {};
  private globalEventHandlers: Array<(elements: Record<string, UIElementPosition>) => void> = [];

  // Register an element with the manager
  registerElement(element: UIElementPosition): void {
    console.log(`[UIPositioningManager] Registering element: ${element.id}`);
    this.elements[element.id] = element;
    this.notifyGlobalHandlers();
  }

  // Update an element's position
  updatePosition(id: string, position: { x: number, y: number }): void {
    if (this.elements[id]) {
      console.log(`[UIPositioningManager] Updating position for ${id}: (${position.x}, ${position.y})`);
      this.elements[id].currentPosition = position;
      this.checkCollisions(id);
      this.notifyGlobalHandlers();
    }
  }

  // Update an element's visibility
  updateVisibility(id: string, visible: boolean): void {
    if (this.elements[id]) {
      console.log(`[UIPositioningManager] Updating visibility for ${id}: ${visible}`);
      this.elements[id].visible = visible;
      if (visible) {
        this.checkCollisions(id);
      }
      this.notifyGlobalHandlers();
    }
  }

  // Update an element's size
  updateSize(id: string, size: { width: number, height: number }): void {
    if (this.elements[id]) {
      console.log(`[UIPositioningManager] Updating size for ${id}: (${size.width}, ${size.height})`);
      this.elements[id].size = size;
      this.checkCollisions(id);
      this.notifyGlobalHandlers();
    }
  }

  // Update an element's strategy
  updateStrategy(id: string, strategy: PositioningStrategy): void {
    if (this.elements[id]) {
      console.log(`[UIPositioningManager] Updating strategy for ${id}: ${strategy}`);
      this.elements[id].strategy = strategy;
      this.applyStrategy(id);
      this.notifyGlobalHandlers();
    }
  }

  // Apply the current strategy for an element
  applyStrategy(id: string): void {
    const element = this.elements[id];
    if (!element) return;

    console.log(`[UIPositioningManager] Applying strategy for ${id}: ${element.strategy}`);

    switch (element.strategy) {
      case 'right-side':
        this.positionOnRightSide(id);
        break;
      case 'left-side':
        this.positionOnLeftSide(id);
        break;
      case 'center':
        this.positionInCenter(id);
        break;
      case 'avoid-overlap':
        this.positionToAvoidOverlap(id);
        break;
      case 'default':
        this.resetPosition(id);
        break;
      case 'manual':
        // Do nothing, position is manually controlled
        break;
    }
  }

  // Position an element on the right side of the screen
  private positionOnRightSide(id: string): void {
    const element = this.elements[id];
    if (!element) return;

    const newX = window.innerWidth - element.size.width - 40;
    console.log(`[UIPositioningManager] Positioning ${id} on right side: ${newX}`);
    this.updatePosition(id, { x: newX, y: element.currentPosition.y });
  }

  // Position an element on the left side of the screen
  private positionOnLeftSide(id: string): void {
    const element = this.elements[id];
    if (!element) return;

    console.log(`[UIPositioningManager] Positioning ${id} on left side`);
    this.updatePosition(id, { x: 40, y: element.currentPosition.y });
  }

  // Position an element in the center of the screen
  private positionInCenter(id: string): void {
    const element = this.elements[id];
    if (!element) return;

    const newX = (window.innerWidth - element.size.width) / 2;
    const newY = (window.innerHeight - element.size.height) / 2;
    console.log(`[UIPositioningManager] Positioning ${id} in center: (${newX}, ${newY})`);
    this.updatePosition(id, { x: newX, y: newY });
  }

  // Position an element to avoid overlap with other elements
  private positionToAvoidOverlap(id: string): void {
    const element = this.elements[id];
    if (!element) return;

    // Get all visible elements except this one
    const otherElements = Object.values(this.elements).filter(
      e => e.id !== id && e.visible
    );

    // If no other elements, use default position
    if (otherElements.length === 0) {
      this.resetPosition(id);
      return;
    }

    console.log(`[UIPositioningManager] Positioning ${id} to avoid overlap with ${otherElements.length} elements`);

    // Try different positions until we find one without overlap
    const positions = [
      { x: element.defaultPosition.x, y: element.defaultPosition.y }, // Default
      { x: window.innerWidth - element.size.width - 40, y: element.currentPosition.y }, // Right
      { x: 40, y: element.currentPosition.y }, // Left
      { x: element.currentPosition.x, y: 40 }, // Top
      { x: element.currentPosition.x, y: window.innerHeight - element.size.height - 40 } // Bottom
    ];

    for (const pos of positions) {
      const wouldOverlap = otherElements.some(other =>
        this.wouldElementsOverlap(element, other, pos)
      );

      if (!wouldOverlap) {
        console.log(`[UIPositioningManager] Found non-overlapping position for ${id}: (${pos.x}, ${pos.y})`);
        this.updatePosition(id, pos);
        return;
      }
    }

    // If all positions overlap, use the right side as fallback
    console.log(`[UIPositioningManager] All positions overlap for ${id}, using right side as fallback`);
    this.positionOnRightSide(id);
  }

  // Check if an element would overlap with another at a given position
  private wouldElementsOverlap(
    element: UIElementPosition,
    other: UIElementPosition,
    position: { x: number, y: number }
  ): boolean {
    return (
      position.x < other.currentPosition.x + other.size.width &&
      position.x + element.size.width > other.currentPosition.x &&
      position.y < other.currentPosition.y + other.size.height &&
      position.y + element.size.height > other.currentPosition.y
    );
  }

  // Register a collision handler for an element
  registerCollisionHandler(id: string, handler: (collidingElements: string[]) => void): void {
    console.log(`[UIPositioningManager] Registering collision handler for ${id}`);
    this.collisionHandlers[id] = handler;
  }

  // Register a global event handler
  registerGlobalHandler(handler: (elements: Record<string, UIElementPosition>) => void): void {
    this.globalEventHandlers.push(handler);
  }

  // Notify all global handlers
  private notifyGlobalHandlers(): void {
    this.globalEventHandlers.forEach(handler => handler(this.getElements()));
  }

  // Check for collisions with other elements
  private checkCollisions(id: string): void {
    const element = this.elements[id];
    if (!element || !element.visible) return;

    const collidingElements: string[] = [];

    // Check for collisions with other visible elements
    Object.entries(this.elements).forEach(([otherId, otherElement]) => {
      if (otherId === id || !otherElement.visible) return;

      // Check for collision
      if (this.isColliding(element, otherElement)) {
        collidingElements.push(otherId);
      }
    });

    // If there are collisions and a handler is registered, call it
    if (collidingElements.length > 0) {
      console.log(`[UIPositioningManager] Collisions detected for ${id}: ${collidingElements.join(', ')}`);
      if (this.collisionHandlers[id]) {
        this.collisionHandlers[id](collidingElements);
      }
    }
  }

  // Check if two elements are colliding
  private isColliding(a: UIElementPosition, b: UIElementPosition): boolean {
    return (
      a.currentPosition.x < b.currentPosition.x + b.size.width &&
      a.currentPosition.x + a.size.width > b.currentPosition.x &&
      a.currentPosition.y < b.currentPosition.y + b.size.height &&
      a.currentPosition.y + a.size.height > b.currentPosition.y
    );
  }

  // Get all registered elements
  getElements(): Record<string, UIElementPosition> {
    return { ...this.elements };
  }

  // Get a specific element
  getElement(id: string): UIElementPosition | undefined {
    return this.elements[id];
  }

  // Reset an element to its default position
  resetPosition(id: string): void {
    if (this.elements[id]) {
      console.log(`[UIPositioningManager] Resetting position for ${id}`);
      this.elements[id].currentPosition = { ...this.elements[id].defaultPosition };
      this.checkCollisions(id);
      this.notifyGlobalHandlers();
    }
  }

  // Remove an element from the manager
  removeElement(id: string): void {
    console.log(`[UIPositioningManager] Removing element: ${id}`);
    delete this.elements[id];
    delete this.collisionHandlers[id];
    this.notifyGlobalHandlers();
  }

  // Handle window resize
  handleWindowResize(): void {
    console.log(`[UIPositioningManager] Handling window resize`);
    // Only adjust elements that would go off-screen
    Object.keys(this.elements).forEach(id => {
      const element = this.elements[id];
      if (element.visible) {
        // Only adjust if the element would go off-screen
        const rightEdge = element.currentPosition.x + element.size.width;
        const bottomEdge = element.currentPosition.y + element.size.height;

        if (rightEdge > window.innerWidth || bottomEdge > window.innerHeight) {
          console.log(`[UIPositioningManager] Element ${id} would go off-screen, adjusting`);

          // Calculate new position to keep element on screen
          let newX = element.currentPosition.x;
          let newY = element.currentPosition.y;

          if (rightEdge > window.innerWidth) {
            newX = Math.max(0, window.innerWidth - element.size.width);
          }

          if (bottomEdge > window.innerHeight) {
            newY = Math.max(0, window.innerHeight - element.size.height);
          }

          this.updatePosition(id, { x: newX, y: newY });
        }
      }
    });
  }
}
