// Browser polyfill for Node.js events module
(function() {
  // Simple EventEmitter implementation
  class EventEmitter {
    constructor() {
      this._events = {};
    }
    
    on(event, listener) {
      if (!this._events[event]) this._events[event] = [];
      this._events[event].push(listener);
      return this;
    }
    
    off(event, listener) {
      if (!this._events[event]) return this;
      this._events[event] = this._events[event].filter(l => l !== listener);
      return this;
    }
    
    emit(event, ...args) {
      if (!this._events[event]) return false;
      this._events[event].forEach(listener => listener.apply(this, args));
      return true;
    }
    
    once(event, listener) {
      const onceWrapper = (...args) => {
        this.off(event, onceWrapper);
        listener.apply(this, args);
      };
      this.on(event, onceWrapper);
      return this;
    }
  }
  
  // Create a module-like object for 'events'
  const eventsModule = {
    EventEmitter: EventEmitter
  };
  
  // Add to window for global access
  window.EventEmitter = EventEmitter;
  
  // Handle module imports
  const originalRequire = window.require;
  window.require = function(moduleName) {
    if (moduleName === 'events') {
      return eventsModule;
    }
    return originalRequire ? originalRequire(moduleName) : undefined;
  };
  
  // Handle ES module imports
  if (!window.exports) window.exports = {};
  if (!window.module) window.module = { exports: window.exports };
  
  // Add to module.exports for CommonJS compatibility
  window.module.exports = eventsModule;
  
  console.log('Events polyfill loaded');
})();
