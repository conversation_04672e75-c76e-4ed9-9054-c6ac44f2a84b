const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-v114B1Cq.js","assets/index-D-qkHQLI.css"])))=>i.map(i=>d[i]);
import{r as Hd,a as pu,g as Wd,b as ve,R as Ke,j as Ae,c as Yt,E as jt,m as Tt,d as vu,u as Al,_ as Ud,h as Xd}from"./index-v114B1Cq.js";var Yd=Math.PI/180;function jd(){return typeof window<"u"&&({}.toString.call(window)==="[object Window]"||{}.toString.call(window)==="[object global]")}const qn=typeof global<"u"?global:typeof window<"u"?window:typeof WorkerGlobalScope<"u"?self:{},J={_global:qn,version:"8.4.3",isBrowser:jd(),isUnminified:/param/.test((function(o){}).toString()),dblClickWindow:400,getAngle(o){return J.angleDeg?o*Yd:o},enableTrace:!1,pointerEventsEnabled:!0,autoDrawEnabled:!0,hitOnDragEnabled:!1,capturePointerEventsEnabled:!1,_mouseListenClick:!1,_touchListenClick:!1,_pointerListenClick:!1,_mouseInDblClickWindow:!1,_touchInDblClickWindow:!1,_pointerInDblClickWindow:!1,_mouseDblClickPointerId:null,_touchDblClickPointerId:null,_pointerDblClickPointerId:null,pixelRatio:typeof window<"u"&&window.devicePixelRatio||1,dragDistance:3,angleDeg:!0,showWarnings:!0,dragButtons:[0,1],isDragging(){return J.DD.isDragging},isDragReady(){return!!J.DD.node},releaseCanvasOnDestroy:!0,document:qn.document,_injectGlobal(o){qn.Konva=o}},Re=o=>{J[o.prototype.getClassName()]=o};J._injectGlobal(J);class st{constructor(e=[1,0,0,1,0,0]){this.dirty=!1,this.m=e&&e.slice()||[1,0,0,1,0,0]}reset(){this.m[0]=1,this.m[1]=0,this.m[2]=0,this.m[3]=1,this.m[4]=0,this.m[5]=0}copy(){return new st(this.m)}copyInto(e){e.m[0]=this.m[0],e.m[1]=this.m[1],e.m[2]=this.m[2],e.m[3]=this.m[3],e.m[4]=this.m[4],e.m[5]=this.m[5]}point(e){var r=this.m;return{x:r[0]*e.x+r[2]*e.y+r[4],y:r[1]*e.x+r[3]*e.y+r[5]}}translate(e,r){return this.m[4]+=this.m[0]*e+this.m[2]*r,this.m[5]+=this.m[1]*e+this.m[3]*r,this}scale(e,r){return this.m[0]*=e,this.m[1]*=e,this.m[2]*=r,this.m[3]*=r,this}rotate(e){var r=Math.cos(e),i=Math.sin(e),s=this.m[0]*r+this.m[2]*i,l=this.m[1]*r+this.m[3]*i,h=this.m[0]*-i+this.m[2]*r,c=this.m[1]*-i+this.m[3]*r;return this.m[0]=s,this.m[1]=l,this.m[2]=h,this.m[3]=c,this}getTranslation(){return{x:this.m[4],y:this.m[5]}}skew(e,r){var i=this.m[0]+this.m[2]*r,s=this.m[1]+this.m[3]*r,l=this.m[2]+this.m[0]*e,h=this.m[3]+this.m[1]*e;return this.m[0]=i,this.m[1]=s,this.m[2]=l,this.m[3]=h,this}multiply(e){var r=this.m[0]*e.m[0]+this.m[2]*e.m[1],i=this.m[1]*e.m[0]+this.m[3]*e.m[1],s=this.m[0]*e.m[2]+this.m[2]*e.m[3],l=this.m[1]*e.m[2]+this.m[3]*e.m[3],h=this.m[0]*e.m[4]+this.m[2]*e.m[5]+this.m[4],c=this.m[1]*e.m[4]+this.m[3]*e.m[5]+this.m[5];return this.m[0]=r,this.m[1]=i,this.m[2]=s,this.m[3]=l,this.m[4]=h,this.m[5]=c,this}invert(){var e=1/(this.m[0]*this.m[3]-this.m[1]*this.m[2]),r=this.m[3]*e,i=-this.m[1]*e,s=-this.m[2]*e,l=this.m[0]*e,h=e*(this.m[2]*this.m[5]-this.m[3]*this.m[4]),c=e*(this.m[1]*this.m[4]-this.m[0]*this.m[5]);return this.m[0]=r,this.m[1]=i,this.m[2]=s,this.m[3]=l,this.m[4]=h,this.m[5]=c,this}getMatrix(){return this.m}decompose(){var e=this.m[0],r=this.m[1],i=this.m[2],s=this.m[3],l=this.m[4],h=this.m[5],c=e*s-r*i;let f={x:l,y:h,rotation:0,scaleX:0,scaleY:0,skewX:0,skewY:0};if(e!=0||r!=0){var p=Math.sqrt(e*e+r*r);f.rotation=r>0?Math.acos(e/p):-Math.acos(e/p),f.scaleX=p,f.scaleY=c/p,f.skewX=(e*i+r*s)/c,f.skewY=0}else if(i!=0||s!=0){var v=Math.sqrt(i*i+s*s);f.rotation=Math.PI/2-(s>0?Math.acos(-i/v):-Math.acos(i/v)),f.scaleX=c/v,f.scaleY=v,f.skewX=0,f.skewY=(e*i+r*s)/c}return f.rotation=k._getRotation(f.rotation),f}}var Vd="[object Array]",qd="[object Number]",Qd="[object String]",Kd="[object Boolean]",Jd=Math.PI/180,Zd=180/Math.PI,us="#",$d="",ec="0",tc="Konva warning: ",Ll="Konva error: ",nc="rgb(",hs={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,132,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,255,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,203],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[119,128,144],slategrey:[119,128,144],snow:[255,255,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],transparent:[255,255,255,0],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,5]},rc=/rgb\((\d{1,3}),(\d{1,3}),(\d{1,3})\)/,Mi=[];const ic=typeof requestAnimationFrame<"u"&&requestAnimationFrame||function(o){setTimeout(o,60)},k={_isElement(o){return!!(o&&o.nodeType==1)},_isFunction(o){return!!(o&&o.constructor&&o.call&&o.apply)},_isPlainObject(o){return!!o&&o.constructor===Object},_isArray(o){return Object.prototype.toString.call(o)===Vd},_isNumber(o){return Object.prototype.toString.call(o)===qd&&!isNaN(o)&&isFinite(o)},_isString(o){return Object.prototype.toString.call(o)===Qd},_isBoolean(o){return Object.prototype.toString.call(o)===Kd},isObject(o){return o instanceof Object},isValidSelector(o){if(typeof o!="string")return!1;var e=o[0];return e==="#"||e==="."||e===e.toUpperCase()},_sign(o){return o===0||o>0?1:-1},requestAnimFrame(o){Mi.push(o),Mi.length===1&&ic(function(){const e=Mi;Mi=[],e.forEach(function(r){r()})})},createCanvasElement(){var o=document.createElement("canvas");try{o.style=o.style||{}}catch{}return o},createImageElement(){return document.createElement("img")},_isInDocument(o){for(;o=o.parentNode;)if(o==document)return!0;return!1},_urlToImage(o,e){var r=k.createImageElement();r.onload=function(){e(r)},r.src=o},_rgbToHex(o,e,r){return((1<<24)+(o<<16)+(e<<8)+r).toString(16).slice(1)},_hexToRgb(o){o=o.replace(us,$d);var e=parseInt(o,16);return{r:e>>16&255,g:e>>8&255,b:e&255}},getRandomColor(){for(var o=(Math.random()*16777215<<0).toString(16);o.length<6;)o=ec+o;return us+o},getRGB(o){var e;return o in hs?(e=hs[o],{r:e[0],g:e[1],b:e[2]}):o[0]===us?this._hexToRgb(o.substring(1)):o.substr(0,4)===nc?(e=rc.exec(o.replace(/ /g,"")),{r:parseInt(e[1],10),g:parseInt(e[2],10),b:parseInt(e[3],10)}):{r:0,g:0,b:0}},colorToRGBA(o){return o=o||"black",k._namedColorToRBA(o)||k._hex3ColorToRGBA(o)||k._hex4ColorToRGBA(o)||k._hex6ColorToRGBA(o)||k._hex8ColorToRGBA(o)||k._rgbColorToRGBA(o)||k._rgbaColorToRGBA(o)||k._hslColorToRGBA(o)},_namedColorToRBA(o){var e=hs[o.toLowerCase()];return e?{r:e[0],g:e[1],b:e[2],a:1}:null},_rgbColorToRGBA(o){if(o.indexOf("rgb(")===0){o=o.match(/rgb\(([^)]+)\)/)[1];var e=o.split(/ *, */).map(Number);return{r:e[0],g:e[1],b:e[2],a:1}}},_rgbaColorToRGBA(o){if(o.indexOf("rgba(")===0){o=o.match(/rgba\(([^)]+)\)/)[1];var e=o.split(/ *, */).map((r,i)=>r.slice(-1)==="%"?i===3?parseInt(r)/100:parseInt(r)/100*255:Number(r));return{r:e[0],g:e[1],b:e[2],a:e[3]}}},_hex8ColorToRGBA(o){if(o[0]==="#"&&o.length===9)return{r:parseInt(o.slice(1,3),16),g:parseInt(o.slice(3,5),16),b:parseInt(o.slice(5,7),16),a:parseInt(o.slice(7,9),16)/255}},_hex6ColorToRGBA(o){if(o[0]==="#"&&o.length===7)return{r:parseInt(o.slice(1,3),16),g:parseInt(o.slice(3,5),16),b:parseInt(o.slice(5,7),16),a:1}},_hex4ColorToRGBA(o){if(o[0]==="#"&&o.length===5)return{r:parseInt(o[1]+o[1],16),g:parseInt(o[2]+o[2],16),b:parseInt(o[3]+o[3],16),a:parseInt(o[4]+o[4],16)/255}},_hex3ColorToRGBA(o){if(o[0]==="#"&&o.length===4)return{r:parseInt(o[1]+o[1],16),g:parseInt(o[2]+o[2],16),b:parseInt(o[3]+o[3],16),a:1}},_hslColorToRGBA(o){if(/hsl\((\d+),\s*([\d.]+)%,\s*([\d.]+)%\)/g.test(o)){const[e,...r]=/hsl\((\d+),\s*([\d.]+)%,\s*([\d.]+)%\)/g.exec(o),i=Number(r[0])/360,s=Number(r[1])/100,l=Number(r[2])/100;let h,c,f;if(s===0)return f=l*255,{r:Math.round(f),g:Math.round(f),b:Math.round(f),a:1};l<.5?h=l*(1+s):h=l+s-l*s;const p=2*l-h,v=[0,0,0];for(let _=0;_<3;_++)c=i+1/3*-(_-1),c<0&&c++,c>1&&c--,6*c<1?f=p+(h-p)*6*c:2*c<1?f=h:3*c<2?f=p+(h-p)*(2/3-c)*6:f=p,v[_]=f*255;return{r:Math.round(v[0]),g:Math.round(v[1]),b:Math.round(v[2]),a:1}}},haveIntersection(o,e){return!(e.x>o.x+o.width||e.x+e.width<o.x||e.y>o.y+o.height||e.y+e.height<o.y)},cloneObject(o){var e={};for(var r in o)this._isPlainObject(o[r])?e[r]=this.cloneObject(o[r]):this._isArray(o[r])?e[r]=this.cloneArray(o[r]):e[r]=o[r];return e},cloneArray(o){return o.slice(0)},degToRad(o){return o*Jd},radToDeg(o){return o*Zd},_degToRad(o){return k.warn("Util._degToRad is removed. Please use public Util.degToRad instead."),k.degToRad(o)},_radToDeg(o){return k.warn("Util._radToDeg is removed. Please use public Util.radToDeg instead."),k.radToDeg(o)},_getRotation(o){return J.angleDeg?k.radToDeg(o):o},_capitalize(o){return o.charAt(0).toUpperCase()+o.slice(1)},throw(o){throw new Error(Ll+o)},error(o){console.error(Ll+o)},warn(o){J.showWarnings&&console.warn(tc+o)},each(o,e){for(var r in o)e(r,o[r])},_inRange(o,e,r){return e<=o&&o<r},_getProjectionToSegment(o,e,r,i,s,l){var h,c,f,p=(o-r)*(o-r)+(e-i)*(e-i);if(p==0)h=o,c=e,f=(s-r)*(s-r)+(l-i)*(l-i);else{var v=((s-o)*(r-o)+(l-e)*(i-e))/p;v<0?(h=o,c=e,f=(o-s)*(o-s)+(e-l)*(e-l)):v>1?(h=r,c=i,f=(r-s)*(r-s)+(i-l)*(i-l)):(h=o+v*(r-o),c=e+v*(i-e),f=(h-s)*(h-s)+(c-l)*(c-l))}return[h,c,f]},_getProjectionToLine(o,e,r){var i=k.cloneObject(o),s=Number.MAX_VALUE;return e.forEach(function(l,h){if(!(!r&&h===e.length-1)){var c=e[(h+1)%e.length],f=k._getProjectionToSegment(l.x,l.y,c.x,c.y,o.x,o.y),p=f[0],v=f[1],_=f[2];_<s&&(i.x=p,i.y=v,s=_)}}),i},_prepareArrayForTween(o,e,r){var i,s=[],l=[];if(o.length>e.length){var h=e;e=o,o=h}for(i=0;i<o.length;i+=2)s.push({x:o[i],y:o[i+1]});for(i=0;i<e.length;i+=2)l.push({x:e[i],y:e[i+1]});var c=[];return l.forEach(function(f){var p=k._getProjectionToLine(f,s,r);c.push(p.x),c.push(p.y)}),c},_prepareToStringify(o){var e;o.visitedByCircularReferenceRemoval=!0;for(var r in o)if(o.hasOwnProperty(r)&&o[r]&&typeof o[r]=="object"){if(e=Object.getOwnPropertyDescriptor(o,r),o[r].visitedByCircularReferenceRemoval||k._isElement(o[r]))if(e.configurable)delete o[r];else return null;else if(k._prepareToStringify(o[r])===null)if(e.configurable)delete o[r];else return null}return delete o.visitedByCircularReferenceRemoval,o},_assign(o,e){for(var r in e)o[r]=e[r];return o},_getFirstPointerId(o){return o.touches?o.changedTouches[0].identifier:o.pointerId||999},releaseCanvas(...o){J.releaseCanvasOnDestroy&&o.forEach(e=>{e.width=0,e.height=0})},drawRoundedRectPath(o,e,r,i){let s=0,l=0,h=0,c=0;typeof i=="number"?s=l=h=c=Math.min(i,e/2,r/2):(s=Math.min(i[0]||0,e/2,r/2),l=Math.min(i[1]||0,e/2,r/2),c=Math.min(i[2]||0,e/2,r/2),h=Math.min(i[3]||0,e/2,r/2)),o.moveTo(s,0),o.lineTo(e-l,0),o.arc(e-l,l,l,Math.PI*3/2,0,!1),o.lineTo(e,r-c),o.arc(e-c,r-c,c,0,Math.PI/2,!1),o.lineTo(h,r),o.arc(h,r-h,h,Math.PI/2,Math.PI,!1),o.lineTo(0,s),o.arc(s,s,s,Math.PI,Math.PI*3/2,!1)}};function hn(o){return k._isString(o)?'"'+o+'"':Object.prototype.toString.call(o)==="[object Number]"||k._isBoolean(o)?o:Object.prototype.toString.call(o)}function mu(o){return o>255?255:o<0?0:Math.round(o)}function U(){if(J.isUnminified)return function(o,e){return k._isNumber(o)||k.warn(hn(o)+' is a not valid value for "'+e+'" attribute. The value should be a number.'),o}}function Ls(o){if(J.isUnminified)return function(e,r){let i=k._isNumber(e),s=k._isArray(e)&&e.length==o;return!i&&!s&&k.warn(hn(e)+' is a not valid value for "'+r+'" attribute. The value should be a number or Array<number>('+o+")"),e}}function Ns(){if(J.isUnminified)return function(o,e){var r=k._isNumber(o),i=o==="auto";return r||i||k.warn(hn(o)+' is a not valid value for "'+e+'" attribute. The value should be a number or "auto".'),o}}function Jn(){if(J.isUnminified)return function(o,e){return k._isString(o)||k.warn(hn(o)+' is a not valid value for "'+e+'" attribute. The value should be a string.'),o}}function yu(){if(J.isUnminified)return function(o,e){const r=k._isString(o),i=Object.prototype.toString.call(o)==="[object CanvasGradient]"||o&&o.addColorStop;return r||i||k.warn(hn(o)+' is a not valid value for "'+e+'" attribute. The value should be a string or a native gradient.'),o}}function ac(){if(J.isUnminified)return function(o,e){const r=Int8Array?Object.getPrototypeOf(Int8Array):null;return r&&o instanceof r||(k._isArray(o)?o.forEach(function(i){k._isNumber(i)||k.warn('"'+e+'" attribute has non numeric element '+i+". Make sure that all elements are numbers.")}):k.warn(hn(o)+' is a not valid value for "'+e+'" attribute. The value should be a array of numbers.')),o}}function Mt(){if(J.isUnminified)return function(o,e){var r=o===!0||o===!1;return r||k.warn(hn(o)+' is a not valid value for "'+e+'" attribute. The value should be a boolean.'),o}}function sc(o){if(J.isUnminified)return function(e,r){return e==null||k.isObject(e)||k.warn(hn(e)+' is a not valid value for "'+r+'" attribute. The value should be an object with properties '+o),e}}var Sr="get",wr="set";const w={addGetterSetter(o,e,r,i,s){w.addGetter(o,e,r),w.addSetter(o,e,i,s),w.addOverloadedGetterSetter(o,e)},addGetter(o,e,r){var i=Sr+k._capitalize(e);o.prototype[i]=o.prototype[i]||function(){var s=this.attrs[e];return s===void 0?r:s}},addSetter(o,e,r,i){var s=wr+k._capitalize(e);o.prototype[s]||w.overWriteSetter(o,e,r,i)},overWriteSetter(o,e,r,i){var s=wr+k._capitalize(e);o.prototype[s]=function(l){return r&&l!==void 0&&l!==null&&(l=r.call(this,l,e)),this._setAttr(e,l),i&&i.call(this),this}},addComponentsGetterSetter(o,e,r,i,s){var l=r.length,h=k._capitalize,c=Sr+h(e),f=wr+h(e),p,v;o.prototype[c]=function(){var y={};for(p=0;p<l;p++)v=r[p],y[v]=this.getAttr(e+h(v));return y};var _=sc(r);o.prototype[f]=function(y){var C=this.attrs[e],S;i&&(y=i.call(this,y)),_&&_.call(this,y,e);for(S in y)y.hasOwnProperty(S)&&this._setAttr(e+h(S),y[S]);return y||r.forEach(M=>{this._setAttr(e+h(M),void 0)}),this._fireChangeEvent(e,C,y),s&&s.call(this),this},w.addOverloadedGetterSetter(o,e)},addOverloadedGetterSetter(o,e){var r=k._capitalize(e),i=wr+r,s=Sr+r;o.prototype[e]=function(){return arguments.length?(this[i](arguments[0]),this):this[s]()}},addDeprecatedGetterSetter(o,e,r,i){k.error("Adding deprecated "+e);var s=Sr+k._capitalize(e),l=e+" property is deprecated and will be removed soon. Look at Konva change log for more information.";o.prototype[s]=function(){k.error(l);var h=this.attrs[e];return h===void 0?r:h},w.addSetter(o,e,i,function(){k.error(l)}),w.addOverloadedGetterSetter(o,e)},backCompat(o,e){k.each(e,function(r,i){var s=o.prototype[i],l=Sr+k._capitalize(r),h=wr+k._capitalize(r);function c(){s.apply(this,arguments),k.error('"'+r+'" method is deprecated and will be removed soon. Use ""'+i+'" instead.')}o.prototype[r]=c,o.prototype[l]=c,o.prototype[h]=c})},afterSetFilter(){this._filterUpToDate=!1}};function oc(o){var e=[],r=o.length,i=k,s,l;for(s=0;s<r;s++)l=o[s],i._isNumber(l)?l=Math.round(l*1e3)/1e3:i._isString(l)||(l=l+""),e.push(l);return e}var Nl=",",lc="(",uc=")",hc="([",dc="])",cc=";",fc="()",gc="=",Il=["arc","arcTo","beginPath","bezierCurveTo","clearRect","clip","closePath","createLinearGradient","createPattern","createRadialGradient","drawImage","ellipse","fill","fillText","getImageData","createImageData","lineTo","moveTo","putImageData","quadraticCurveTo","rect","restore","rotate","save","scale","setLineDash","setTransform","stroke","strokeText","transform","translate"],pc=["fillStyle","strokeStyle","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","lineCap","lineDashOffset","lineJoin","lineWidth","miterLimit","font","textAlign","textBaseline","globalAlpha","globalCompositeOperation","imageSmoothingEnabled"];const vc=100;class Ui{constructor(e){this.canvas=e,J.enableTrace&&(this.traceArr=[],this._enableTrace())}fillShape(e){e.fillEnabled()&&this._fill(e)}_fill(e){}strokeShape(e){e.hasStroke()&&this._stroke(e)}_stroke(e){}fillStrokeShape(e){e.attrs.fillAfterStrokeEnabled?(this.strokeShape(e),this.fillShape(e)):(this.fillShape(e),this.strokeShape(e))}getTrace(e,r){var i=this.traceArr,s=i.length,l="",h,c,f,p;for(h=0;h<s;h++)c=i[h],f=c.method,f?(p=c.args,l+=f,e?l+=fc:k._isArray(p[0])?l+=hc+p.join(Nl)+dc:(r&&(p=p.map(v=>typeof v=="number"?Math.floor(v):v)),l+=lc+p.join(Nl)+uc)):(l+=c.property,e||(l+=gc+c.val)),l+=cc;return l}clearTrace(){this.traceArr=[]}_trace(e){var r=this.traceArr,i;r.push(e),i=r.length,i>=vc&&r.shift()}reset(){var e=this.getCanvas().getPixelRatio();this.setTransform(1*e,0,0,1*e,0,0)}getCanvas(){return this.canvas}clear(e){var r=this.getCanvas();e?this.clearRect(e.x||0,e.y||0,e.width||0,e.height||0):this.clearRect(0,0,r.getWidth()/r.pixelRatio,r.getHeight()/r.pixelRatio)}_applyLineCap(e){const r=e.attrs.lineCap;r&&this.setAttr("lineCap",r)}_applyOpacity(e){var r=e.getAbsoluteOpacity();r!==1&&this.setAttr("globalAlpha",r)}_applyLineJoin(e){const r=e.attrs.lineJoin;r&&this.setAttr("lineJoin",r)}setAttr(e,r){this._context[e]=r}arc(e,r,i,s,l,h){this._context.arc(e,r,i,s,l,h)}arcTo(e,r,i,s,l){this._context.arcTo(e,r,i,s,l)}beginPath(){this._context.beginPath()}bezierCurveTo(e,r,i,s,l,h){this._context.bezierCurveTo(e,r,i,s,l,h)}clearRect(e,r,i,s){this._context.clearRect(e,r,i,s)}clip(){this._context.clip()}closePath(){this._context.closePath()}createImageData(e,r){var i=arguments;if(i.length===2)return this._context.createImageData(e,r);if(i.length===1)return this._context.createImageData(e)}createLinearGradient(e,r,i,s){return this._context.createLinearGradient(e,r,i,s)}createPattern(e,r){return this._context.createPattern(e,r)}createRadialGradient(e,r,i,s,l,h){return this._context.createRadialGradient(e,r,i,s,l,h)}drawImage(e,r,i,s,l,h,c,f,p){var v=arguments,_=this._context;v.length===3?_.drawImage(e,r,i):v.length===5?_.drawImage(e,r,i,s,l):v.length===9&&_.drawImage(e,r,i,s,l,h,c,f,p)}ellipse(e,r,i,s,l,h,c,f){this._context.ellipse(e,r,i,s,l,h,c,f)}isPointInPath(e,r,i,s){return i?this._context.isPointInPath(i,e,r,s):this._context.isPointInPath(e,r,s)}fill(e){e?this._context.fill(e):this._context.fill()}fillRect(e,r,i,s){this._context.fillRect(e,r,i,s)}strokeRect(e,r,i,s){this._context.strokeRect(e,r,i,s)}fillText(e,r,i,s){s?this._context.fillText(e,r,i,s):this._context.fillText(e,r,i)}measureText(e){return this._context.measureText(e)}getImageData(e,r,i,s){return this._context.getImageData(e,r,i,s)}lineTo(e,r){this._context.lineTo(e,r)}moveTo(e,r){this._context.moveTo(e,r)}rect(e,r,i,s){this._context.rect(e,r,i,s)}putImageData(e,r,i){this._context.putImageData(e,r,i)}quadraticCurveTo(e,r,i,s){this._context.quadraticCurveTo(e,r,i,s)}restore(){this._context.restore()}rotate(e){this._context.rotate(e)}save(){this._context.save()}scale(e,r){this._context.scale(e,r)}setLineDash(e){this._context.setLineDash?this._context.setLineDash(e):"mozDash"in this._context?this._context.mozDash=e:"webkitLineDash"in this._context&&(this._context.webkitLineDash=e)}getLineDash(){return this._context.getLineDash()}setTransform(e,r,i,s,l,h){this._context.setTransform(e,r,i,s,l,h)}stroke(e){e?this._context.stroke(e):this._context.stroke()}strokeText(e,r,i,s){this._context.strokeText(e,r,i,s)}transform(e,r,i,s,l,h){this._context.transform(e,r,i,s,l,h)}translate(e,r){this._context.translate(e,r)}_enableTrace(){var e=this,r=Il.length,i=this.setAttr,s,l,h=function(c){var f=e[c],p;e[c]=function(){return l=oc(Array.prototype.slice.call(arguments,0)),p=f.apply(e,arguments),e._trace({method:c,args:l}),p}};for(s=0;s<r;s++)h(Il[s]);e.setAttr=function(){i.apply(e,arguments);var c=arguments[0],f=arguments[1];(c==="shadowOffsetX"||c==="shadowOffsetY"||c==="shadowBlur")&&(f=f/this.canvas.getPixelRatio()),e._trace({property:c,val:f})}}_applyGlobalCompositeOperation(e){const r=e.attrs.globalCompositeOperation;var i=!r||r==="source-over";i||this.setAttr("globalCompositeOperation",r)}}pc.forEach(function(o){Object.defineProperty(Ui.prototype,o,{get(){return this._context[o]},set(e){this._context[o]=e}})});class mc extends Ui{constructor(e){super(e),this._context=e._canvas.getContext("2d")}_fillColor(e){var r=e.fill();this.setAttr("fillStyle",r),e._fillFunc(this)}_fillPattern(e){this.setAttr("fillStyle",e._getFillPattern()),e._fillFunc(this)}_fillLinearGradient(e){var r=e._getLinearGradient();r&&(this.setAttr("fillStyle",r),e._fillFunc(this))}_fillRadialGradient(e){const r=e._getRadialGradient();r&&(this.setAttr("fillStyle",r),e._fillFunc(this))}_fill(e){const r=e.fill(),i=e.getFillPriority();if(r&&i==="color"){this._fillColor(e);return}const s=e.getFillPatternImage();if(s&&i==="pattern"){this._fillPattern(e);return}const l=e.getFillLinearGradientColorStops();if(l&&i==="linear-gradient"){this._fillLinearGradient(e);return}const h=e.getFillRadialGradientColorStops();if(h&&i==="radial-gradient"){this._fillRadialGradient(e);return}r?this._fillColor(e):s?this._fillPattern(e):l?this._fillLinearGradient(e):h&&this._fillRadialGradient(e)}_strokeLinearGradient(e){const r=e.getStrokeLinearGradientStartPoint(),i=e.getStrokeLinearGradientEndPoint(),s=e.getStrokeLinearGradientColorStops(),l=this.createLinearGradient(r.x,r.y,i.x,i.y);if(s){for(var h=0;h<s.length;h+=2)l.addColorStop(s[h],s[h+1]);this.setAttr("strokeStyle",l)}}_stroke(e){var r=e.dash(),i=e.getStrokeScaleEnabled();if(e.hasStroke()){if(!i){this.save();var s=this.getCanvas().getPixelRatio();this.setTransform(s,0,0,s,0,0)}this._applyLineCap(e),r&&e.dashEnabled()&&(this.setLineDash(r),this.setAttr("lineDashOffset",e.dashOffset())),this.setAttr("lineWidth",e.strokeWidth()),e.getShadowForStrokeEnabled()||this.setAttr("shadowColor","rgba(0,0,0,0)");var l=e.getStrokeLinearGradientColorStops();l?this._strokeLinearGradient(e):this.setAttr("strokeStyle",e.stroke()),e._strokeFunc(this),i||this.restore()}}_applyShadow(e){var r,i,s,l=(r=e.getShadowRGBA())!==null&&r!==void 0?r:"black",h=(i=e.getShadowBlur())!==null&&i!==void 0?i:5,c=(s=e.getShadowOffset())!==null&&s!==void 0?s:{x:0,y:0},f=e.getAbsoluteScale(),p=this.canvas.getPixelRatio(),v=f.x*p,_=f.y*p;this.setAttr("shadowColor",l),this.setAttr("shadowBlur",h*Math.min(Math.abs(v),Math.abs(_))),this.setAttr("shadowOffsetX",c.x*v),this.setAttr("shadowOffsetY",c.y*_)}}class yc extends Ui{constructor(e){super(e),this._context=e._canvas.getContext("2d",{willReadFrequently:!0})}_fill(e){this.save(),this.setAttr("fillStyle",e.colorKey),e._fillFuncHit(this),this.restore()}strokeShape(e){e.hasHitStroke()&&this._stroke(e)}_stroke(e){if(e.hasHitStroke()){const l=e.getStrokeScaleEnabled();if(!l){this.save();var r=this.getCanvas().getPixelRatio();this.setTransform(r,0,0,r,0,0)}this._applyLineCap(e);var i=e.hitStrokeWidth(),s=i==="auto"?e.strokeWidth():i;this.setAttr("lineWidth",s),this.setAttr("strokeStyle",e.colorKey),e._strokeFuncHit(this),l||this.restore()}}}var Ri;function _c(){if(Ri)return Ri;var o=k.createCanvasElement(),e=o.getContext("2d");return Ri=function(){var r=J._global.devicePixelRatio||1,i=e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1;return r/i}(),k.releaseCanvas(o),Ri}class Xi{constructor(e){this.pixelRatio=1,this.width=0,this.height=0,this.isCache=!1;var r=e||{},i=r.pixelRatio||J.pixelRatio||_c();this.pixelRatio=i,this._canvas=k.createCanvasElement(),this._canvas.style.padding="0",this._canvas.style.margin="0",this._canvas.style.border="0",this._canvas.style.background="transparent",this._canvas.style.position="absolute",this._canvas.style.top="0",this._canvas.style.left="0"}getContext(){return this.context}getPixelRatio(){return this.pixelRatio}setPixelRatio(e){var r=this.pixelRatio;this.pixelRatio=e,this.setSize(this.getWidth()/r,this.getHeight()/r)}setWidth(e){this.width=this._canvas.width=e*this.pixelRatio,this._canvas.style.width=e+"px";var r=this.pixelRatio,i=this.getContext()._context;i.scale(r,r)}setHeight(e){this.height=this._canvas.height=e*this.pixelRatio,this._canvas.style.height=e+"px";var r=this.pixelRatio,i=this.getContext()._context;i.scale(r,r)}getWidth(){return this.width}getHeight(){return this.height}setSize(e,r){this.setWidth(e||0),this.setHeight(r||0)}toDataURL(e,r){try{return this._canvas.toDataURL(e,r)}catch{try{return this._canvas.toDataURL()}catch(s){return k.error("Unable to get data URL. "+s.message+" For more info read https://konvajs.org/docs/posts/Tainted_Canvas.html."),""}}}}w.addGetterSetter(Xi,"pixelRatio",void 0,U());class Qn extends Xi{constructor(e={width:0,height:0}){super(e),this.context=new mc(this),this.setSize(e.width,e.height)}}class Is extends Xi{constructor(e={width:0,height:0}){super(e),this.hitCanvas=!0,this.context=new yc(this),this.setSize(e.width,e.height)}}const ce={get isDragging(){var o=!1;return ce._dragElements.forEach(e=>{e.dragStatus==="dragging"&&(o=!0)}),o},justDragged:!1,get node(){var o;return ce._dragElements.forEach(e=>{o=e.node}),o},_dragElements:new Map,_drag(o){const e=[];ce._dragElements.forEach((r,i)=>{const{node:s}=r,l=s.getStage();l.setPointersPositions(o),r.pointerId===void 0&&(r.pointerId=k._getFirstPointerId(o));const h=l._changedPointerPositions.find(p=>p.id===r.pointerId);if(h){if(r.dragStatus!=="dragging"){var c=s.dragDistance(),f=Math.max(Math.abs(h.x-r.startPointerPos.x),Math.abs(h.y-r.startPointerPos.y));if(f<c||(s.startDrag({evt:o}),!s.isDragging()))return}s._setDragPosition(o,r),e.push(s)}}),e.forEach(r=>{r.fire("dragmove",{type:"dragmove",target:r,evt:o},!0)})},_endDragBefore(o){const e=[];ce._dragElements.forEach(r=>{const{node:i}=r,s=i.getStage();if(o&&s.setPointersPositions(o),!s._changedPointerPositions.find(c=>c.id===r.pointerId))return;(r.dragStatus==="dragging"||r.dragStatus==="stopped")&&(ce.justDragged=!0,J._mouseListenClick=!1,J._touchListenClick=!1,J._pointerListenClick=!1,r.dragStatus="stopped");const h=r.node.getLayer()||r.node instanceof J.Stage&&r.node;h&&e.indexOf(h)===-1&&e.push(h)}),e.forEach(r=>{r.draw()})},_endDragAfter(o){ce._dragElements.forEach((e,r)=>{e.dragStatus==="stopped"&&e.node.fire("dragend",{type:"dragend",target:e.node,evt:o},!0),e.dragStatus!=="dragging"&&ce._dragElements.delete(r)})}};J.isBrowser&&(window.addEventListener("mouseup",ce._endDragBefore,!0),window.addEventListener("touchend",ce._endDragBefore,!0),window.addEventListener("mousemove",ce._drag),window.addEventListener("touchmove",ce._drag),window.addEventListener("mouseup",ce._endDragAfter,!1),window.addEventListener("touchend",ce._endDragAfter,!1));var Fi="absoluteOpacity",Ai="allEventListeners",Vt="absoluteTransform",Ol="absoluteScale",En="canvas",Sc="Change",wc="children",Cc="konva",Cs="listening",Dl="mouseenter",Gl="mouseleave",zl="set",bl="Shape",Bi=" ",Fl="stage",un="transform",xc="Stage",xs="visible",Ec=["xChange.konva","yChange.konva","scaleXChange.konva","scaleYChange.konva","skewXChange.konva","skewYChange.konva","rotationChange.konva","offsetXChange.konva","offsetYChange.konva","transformsEnabledChange.konva"].join(Bi);let Pc=1;class W{constructor(e){this._id=Pc++,this.eventListeners={},this.attrs={},this.index=0,this._allEventListeners=null,this.parent=null,this._cache=new Map,this._attachedDepsListeners=new Map,this._lastPos=null,this._batchingTransformChange=!1,this._needClearTransformCache=!1,this._filterUpToDate=!1,this._isUnderCache=!1,this._dragEventId=null,this._shouldFireChangeEvents=!1,this.setAttrs(e),this._shouldFireChangeEvents=!0}hasChildren(){return!1}_clearCache(e){(e===un||e===Vt)&&this._cache.get(e)?this._cache.get(e).dirty=!0:e?this._cache.delete(e):this._cache.clear()}_getCache(e,r){var i=this._cache.get(e),s=e===un||e===Vt,l=i===void 0||s&&i.dirty===!0;return l&&(i=r.call(this),this._cache.set(e,i)),i}_calculate(e,r,i){if(!this._attachedDepsListeners.get(e)){const s=r.map(l=>l+"Change.konva").join(Bi);this.on(s,()=>{this._clearCache(e)}),this._attachedDepsListeners.set(e,!0)}return this._getCache(e,i)}_getCanvasCache(){return this._cache.get(En)}_clearSelfAndDescendantCache(e){this._clearCache(e),e===Vt&&this.fire("absoluteTransformChange")}clearCache(){if(this._cache.has(En)){const{scene:e,filter:r,hit:i}=this._cache.get(En);k.releaseCanvas(e,r,i),this._cache.delete(En)}return this._clearSelfAndDescendantCache(),this._requestDraw(),this}cache(e){var r=e||{},i={};(r.x===void 0||r.y===void 0||r.width===void 0||r.height===void 0)&&(i=this.getClientRect({skipTransform:!0,relativeTo:this.getParent()}));var s=Math.ceil(r.width||i.width),l=Math.ceil(r.height||i.height),h=r.pixelRatio,c=r.x===void 0?Math.floor(i.x):r.x,f=r.y===void 0?Math.floor(i.y):r.y,p=r.offset||0,v=r.drawBorder||!1,_=r.hitCanvasPixelRatio||1;if(!s||!l){k.error("Can not cache the node. Width or height of the node equals 0. Caching is skipped.");return}s+=p*2+1,l+=p*2+1,c-=p,f-=p;var y=new Qn({pixelRatio:h,width:s,height:l}),C=new Qn({pixelRatio:h,width:0,height:0}),S=new Is({pixelRatio:_,width:s,height:l}),M=y.getContext(),T=S.getContext();return S.isCache=!0,y.isCache=!0,this._cache.delete(En),this._filterUpToDate=!1,r.imageSmoothingEnabled===!1&&(y.getContext()._context.imageSmoothingEnabled=!1,C.getContext()._context.imageSmoothingEnabled=!1),M.save(),T.save(),M.translate(-c,-f),T.translate(-c,-f),this._isUnderCache=!0,this._clearSelfAndDescendantCache(Fi),this._clearSelfAndDescendantCache(Ol),this.drawScene(y,this),this.drawHit(S,this),this._isUnderCache=!1,M.restore(),T.restore(),v&&(M.save(),M.beginPath(),M.rect(0,0,s,l),M.closePath(),M.setAttr("strokeStyle","red"),M.setAttr("lineWidth",5),M.stroke(),M.restore()),this._cache.set(En,{scene:y,filter:C,hit:S,x:c,y:f}),this._requestDraw(),this}isCached(){return this._cache.has(En)}getClientRect(e){throw new Error('abstract "getClientRect" method call')}_transformedRect(e,r){var i=[{x:e.x,y:e.y},{x:e.x+e.width,y:e.y},{x:e.x+e.width,y:e.y+e.height},{x:e.x,y:e.y+e.height}],s,l,h,c,f=this.getAbsoluteTransform(r);return i.forEach(function(p){var v=f.point(p);s===void 0&&(s=h=v.x,l=c=v.y),s=Math.min(s,v.x),l=Math.min(l,v.y),h=Math.max(h,v.x),c=Math.max(c,v.y)}),{x:s,y:l,width:h-s,height:c-l}}_drawCachedSceneCanvas(e){e.save(),e._applyOpacity(this),e._applyGlobalCompositeOperation(this);const r=this._getCanvasCache();e.translate(r.x,r.y);var i=this._getCachedSceneCanvas(),s=i.pixelRatio;e.drawImage(i._canvas,0,0,i.width/s,i.height/s),e.restore()}_drawCachedHitCanvas(e){var r=this._getCanvasCache(),i=r.hit;e.save(),e.translate(r.x,r.y),e.drawImage(i._canvas,0,0,i.width/i.pixelRatio,i.height/i.pixelRatio),e.restore()}_getCachedSceneCanvas(){var e=this.filters(),r=this._getCanvasCache(),i=r.scene,s=r.filter,l=s.getContext(),h,c,f,p;if(e){if(!this._filterUpToDate){var v=i.pixelRatio;s.setSize(i.width/i.pixelRatio,i.height/i.pixelRatio);try{for(h=e.length,l.clear(),l.drawImage(i._canvas,0,0,i.getWidth()/v,i.getHeight()/v),c=l.getImageData(0,0,s.getWidth(),s.getHeight()),f=0;f<h;f++){if(p=e[f],typeof p!="function"){k.error("Filter should be type of function, but got "+typeof p+" instead. Please check correct filters");continue}p.call(this,c),l.putImageData(c,0,0)}}catch(_){k.error("Unable to apply filter. "+_.message+" This post my help you https://konvajs.org/docs/posts/Tainted_Canvas.html.")}this._filterUpToDate=!0}return s}return i}on(e,r){if(this._cache&&this._cache.delete(Ai),arguments.length===3)return this._delegate.apply(this,arguments);var i=e.split(Bi),s=i.length,l,h,c,f,p;for(l=0;l<s;l++)h=i[l],c=h.split("."),f=c[0],p=c[1]||"",this.eventListeners[f]||(this.eventListeners[f]=[]),this.eventListeners[f].push({name:p,handler:r});return this}off(e,r){var i=(e||"").split(Bi),s=i.length,l,h,c,f,p,v;if(this._cache&&this._cache.delete(Ai),!e)for(h in this.eventListeners)this._off(h);for(l=0;l<s;l++)if(c=i[l],f=c.split("."),p=f[0],v=f[1],p)this.eventListeners[p]&&this._off(p,v,r);else for(h in this.eventListeners)this._off(h,v,r);return this}dispatchEvent(e){var r={target:this,type:e.type,evt:e};return this.fire(e.type,r),this}addEventListener(e,r){return this.on(e,function(i){r.call(this,i.evt)}),this}removeEventListener(e){return this.off(e),this}_delegate(e,r,i){var s=this;this.on(e,function(l){for(var h=l.target.findAncestors(r,!0,s),c=0;c<h.length;c++)l=k.cloneObject(l),l.currentTarget=h[c],i.call(h[c],l)})}remove(){return this.isDragging()&&this.stopDrag(),ce._dragElements.delete(this._id),this._remove(),this}_clearCaches(){this._clearSelfAndDescendantCache(Vt),this._clearSelfAndDescendantCache(Fi),this._clearSelfAndDescendantCache(Ol),this._clearSelfAndDescendantCache(Fl),this._clearSelfAndDescendantCache(xs),this._clearSelfAndDescendantCache(Cs)}_remove(){this._clearCaches();var e=this.getParent();e&&e.children&&(e.children.splice(this.index,1),e._setChildrenIndices(),this.parent=null)}destroy(){return this.remove(),this.clearCache(),this}getAttr(e){var r="get"+k._capitalize(e);return k._isFunction(this[r])?this[r]():this.attrs[e]}getAncestors(){for(var e=this.getParent(),r=[];e;)r.push(e),e=e.getParent();return r}getAttrs(){return this.attrs||{}}setAttrs(e){return this._batchTransformChanges(()=>{var r,i;if(!e)return this;for(r in e)r!==wc&&(i=zl+k._capitalize(r),k._isFunction(this[i])?this[i](e[r]):this._setAttr(r,e[r]))}),this}isListening(){return this._getCache(Cs,this._isListening)}_isListening(e){if(!this.listening())return!1;const i=this.getParent();return i&&i!==e&&this!==e?i._isListening(e):!0}isVisible(){return this._getCache(xs,this._isVisible)}_isVisible(e){if(!this.visible())return!1;const i=this.getParent();return i&&i!==e&&this!==e?i._isVisible(e):!0}shouldDrawHit(e,r=!1){if(e)return this._isVisible(e)&&this._isListening(e);var i=this.getLayer(),s=!1;ce._dragElements.forEach(h=>{h.dragStatus==="dragging"&&(h.node.nodeType==="Stage"||h.node.getLayer()===i)&&(s=!0)});var l=!r&&!J.hitOnDragEnabled&&s;return this.isListening()&&this.isVisible()&&!l}show(){return this.visible(!0),this}hide(){return this.visible(!1),this}getZIndex(){return this.index||0}getAbsoluteZIndex(){var e=this.getDepth(),r=this,i=0,s,l,h,c;function f(p){for(s=[],l=p.length,h=0;h<l;h++)c=p[h],i++,c.nodeType!==bl&&(s=s.concat(c.getChildren().slice())),c._id===r._id&&(h=l);s.length>0&&s[0].getDepth()<=e&&f(s)}return r.nodeType!==xc&&f(r.getStage().getChildren()),i}getDepth(){for(var e=0,r=this.parent;r;)e++,r=r.parent;return e}_batchTransformChanges(e){this._batchingTransformChange=!0,e(),this._batchingTransformChange=!1,this._needClearTransformCache&&(this._clearCache(un),this._clearSelfAndDescendantCache(Vt)),this._needClearTransformCache=!1}setPosition(e){return this._batchTransformChanges(()=>{this.x(e.x),this.y(e.y)}),this}getPosition(){return{x:this.x(),y:this.y()}}getRelativePointerPosition(){if(!this.getStage())return null;var e=this.getStage().getPointerPosition();if(!e)return null;var r=this.getAbsoluteTransform().copy();return r.invert(),r.point(e)}getAbsolutePosition(e){let r=!1,i=this.parent;for(;i;){if(i.isCached()){r=!0;break}i=i.parent}r&&!e&&(e=!0);var s=this.getAbsoluteTransform(e).getMatrix(),l=new st,h=this.offset();return l.m=s.slice(),l.translate(h.x,h.y),l.getTranslation()}setAbsolutePosition(e){var r=this._clearTransform();this.attrs.x=r.x,this.attrs.y=r.y,delete r.x,delete r.y,this._clearCache(un);var i=this._getAbsoluteTransform().copy();return i.invert(),i.translate(e.x,e.y),e={x:this.attrs.x+i.getTranslation().x,y:this.attrs.y+i.getTranslation().y},this._setTransform(r),this.setPosition({x:e.x,y:e.y}),this._clearCache(un),this._clearSelfAndDescendantCache(Vt),this}_setTransform(e){var r;for(r in e)this.attrs[r]=e[r]}_clearTransform(){var e={x:this.x(),y:this.y(),rotation:this.rotation(),scaleX:this.scaleX(),scaleY:this.scaleY(),offsetX:this.offsetX(),offsetY:this.offsetY(),skewX:this.skewX(),skewY:this.skewY()};return this.attrs.x=0,this.attrs.y=0,this.attrs.rotation=0,this.attrs.scaleX=1,this.attrs.scaleY=1,this.attrs.offsetX=0,this.attrs.offsetY=0,this.attrs.skewX=0,this.attrs.skewY=0,e}move(e){var r=e.x,i=e.y,s=this.x(),l=this.y();return r!==void 0&&(s+=r),i!==void 0&&(l+=i),this.setPosition({x:s,y:l}),this}_eachAncestorReverse(e,r){var i=[],s=this.getParent(),l,h;if(!(r&&r._id===this._id)){for(i.unshift(this);s&&(!r||s._id!==r._id);)i.unshift(s),s=s.parent;for(l=i.length,h=0;h<l;h++)e(i[h])}}rotate(e){return this.rotation(this.rotation()+e),this}moveToTop(){if(!this.parent)return k.warn("Node has no parent. moveToTop function is ignored."),!1;var e=this.index,r=this.parent.getChildren().length;return e<r-1?(this.parent.children.splice(e,1),this.parent.children.push(this),this.parent._setChildrenIndices(),!0):!1}moveUp(){if(!this.parent)return k.warn("Node has no parent. moveUp function is ignored."),!1;var e=this.index,r=this.parent.getChildren().length;return e<r-1?(this.parent.children.splice(e,1),this.parent.children.splice(e+1,0,this),this.parent._setChildrenIndices(),!0):!1}moveDown(){if(!this.parent)return k.warn("Node has no parent. moveDown function is ignored."),!1;var e=this.index;return e>0?(this.parent.children.splice(e,1),this.parent.children.splice(e-1,0,this),this.parent._setChildrenIndices(),!0):!1}moveToBottom(){if(!this.parent)return k.warn("Node has no parent. moveToBottom function is ignored."),!1;var e=this.index;return e>0?(this.parent.children.splice(e,1),this.parent.children.unshift(this),this.parent._setChildrenIndices(),!0):!1}setZIndex(e){if(!this.parent)return k.warn("Node has no parent. zIndex parameter is ignored."),this;(e<0||e>=this.parent.children.length)&&k.warn("Unexpected value "+e+" for zIndex property. zIndex is just index of a node in children of its parent. Expected value is from 0 to "+(this.parent.children.length-1)+".");var r=this.index;return this.parent.children.splice(r,1),this.parent.children.splice(e,0,this),this.parent._setChildrenIndices(),this}getAbsoluteOpacity(){return this._getCache(Fi,this._getAbsoluteOpacity)}_getAbsoluteOpacity(){var e=this.opacity(),r=this.getParent();return r&&!r._isUnderCache&&(e*=r.getAbsoluteOpacity()),e}moveTo(e){return this.getParent()!==e&&(this._remove(),e.add(this)),this}toObject(){var e={},r=this.getAttrs(),i,s,l,h,c;e.attrs={};for(i in r)s=r[i],c=k.isObject(s)&&!k._isPlainObject(s)&&!k._isArray(s),!c&&(l=typeof this[i]=="function"&&this[i],delete r[i],h=l?l.call(this):null,r[i]=s,h!==s&&(e.attrs[i]=s));return e.className=this.getClassName(),k._prepareToStringify(e)}toJSON(){return JSON.stringify(this.toObject())}getParent(){return this.parent}findAncestors(e,r,i){var s=[];r&&this._isMatch(e)&&s.push(this);for(var l=this.parent;l;){if(l===i)return s;l._isMatch(e)&&s.push(l),l=l.parent}return s}isAncestorOf(e){return!1}findAncestor(e,r,i){return this.findAncestors(e,r,i)[0]}_isMatch(e){if(!e)return!1;if(typeof e=="function")return e(this);var r=e.replace(/ /g,"").split(","),i=r.length,s,l;for(s=0;s<i;s++)if(l=r[s],k.isValidSelector(l)||(k.warn('Selector "'+l+'" is invalid. Allowed selectors examples are "#foo", ".bar" or "Group".'),k.warn('If you have a custom shape with such className, please change it to start with upper letter like "Triangle".'),k.warn("Konva is awesome, right?")),l.charAt(0)==="#"){if(this.id()===l.slice(1))return!0}else if(l.charAt(0)==="."){if(this.hasName(l.slice(1)))return!0}else if(this.className===l||this.nodeType===l)return!0;return!1}getLayer(){var e=this.getParent();return e?e.getLayer():null}getStage(){return this._getCache(Fl,this._getStage)}_getStage(){var e=this.getParent();if(e)return e.getStage()}fire(e,r={},i){return r.target=r.target||this,i?this._fireAndBubble(e,r):this._fire(e,r),this}getAbsoluteTransform(e){return e?this._getAbsoluteTransform(e):this._getCache(Vt,this._getAbsoluteTransform)}_getAbsoluteTransform(e){var r;if(e)return r=new st,this._eachAncestorReverse(function(s){var l=s.transformsEnabled();l==="all"?r.multiply(s.getTransform()):l==="position"&&r.translate(s.x()-s.offsetX(),s.y()-s.offsetY())},e),r;r=this._cache.get(Vt)||new st,this.parent?this.parent.getAbsoluteTransform().copyInto(r):r.reset();var i=this.transformsEnabled();if(i==="all")r.multiply(this.getTransform());else if(i==="position"){const s=this.attrs.x||0,l=this.attrs.y||0,h=this.attrs.offsetX||0,c=this.attrs.offsetY||0;r.translate(s-h,l-c)}return r.dirty=!1,r}getAbsoluteScale(e){for(var r=this;r;)r._isUnderCache&&(e=r),r=r.getParent();const s=this.getAbsoluteTransform(e).decompose();return{x:s.scaleX,y:s.scaleY}}getAbsoluteRotation(){return this.getAbsoluteTransform().decompose().rotation}getTransform(){return this._getCache(un,this._getTransform)}_getTransform(){var e,r,i=this._cache.get(un)||new st;i.reset();var s=this.x(),l=this.y(),h=J.getAngle(this.rotation()),c=(e=this.attrs.scaleX)!==null&&e!==void 0?e:1,f=(r=this.attrs.scaleY)!==null&&r!==void 0?r:1,p=this.attrs.skewX||0,v=this.attrs.skewY||0,_=this.attrs.offsetX||0,y=this.attrs.offsetY||0;return(s!==0||l!==0)&&i.translate(s,l),h!==0&&i.rotate(h),(p!==0||v!==0)&&i.skew(p,v),(c!==1||f!==1)&&i.scale(c,f),(_!==0||y!==0)&&i.translate(-1*_,-1*y),i.dirty=!1,i}clone(e){var r=k.cloneObject(this.attrs),i,s,l,h,c;for(i in e)r[i]=e[i];var f=new this.constructor(r);for(i in this.eventListeners)for(s=this.eventListeners[i],l=s.length,h=0;h<l;h++)c=s[h],c.name.indexOf(Cc)<0&&(f.eventListeners[i]||(f.eventListeners[i]=[]),f.eventListeners[i].push(c));return f}_toKonvaCanvas(e){e=e||{};var r=this.getClientRect(),i=this.getStage(),s=e.x!==void 0?e.x:Math.floor(r.x),l=e.y!==void 0?e.y:Math.floor(r.y),h=e.pixelRatio||1,c=new Qn({width:e.width||Math.ceil(r.width)||(i?i.width():0),height:e.height||Math.ceil(r.height)||(i?i.height():0),pixelRatio:h}),f=c.getContext();return e.imageSmoothingEnabled===!1&&(f._context.imageSmoothingEnabled=!1),f.save(),(s||l)&&f.translate(-1*s,-1*l),this.drawScene(c),f.restore(),c}toCanvas(e){return this._toKonvaCanvas(e)._canvas}toDataURL(e){e=e||{};var r=e.mimeType||null,i=e.quality||null,s=this._toKonvaCanvas(e).toDataURL(r,i);return e.callback&&e.callback(s),s}toImage(e){return new Promise((r,i)=>{try{const s=e==null?void 0:e.callback;s&&delete e.callback,k._urlToImage(this.toDataURL(e),function(l){r(l),s==null||s(l)})}catch(s){i(s)}})}toBlob(e){return new Promise((r,i)=>{try{const s=e==null?void 0:e.callback;s&&delete e.callback,this.toCanvas(e).toBlob(l=>{r(l),s==null||s(l)})}catch(s){i(s)}})}setSize(e){return this.width(e.width),this.height(e.height),this}getSize(){return{width:this.width(),height:this.height()}}getClassName(){return this.className||this.nodeType}getType(){return this.nodeType}getDragDistance(){return this.attrs.dragDistance!==void 0?this.attrs.dragDistance:this.parent?this.parent.getDragDistance():J.dragDistance}_off(e,r,i){var s=this.eventListeners[e],l,h,c;for(l=0;l<s.length;l++)if(h=s[l].name,c=s[l].handler,(h!=="konva"||r==="konva")&&(!r||h===r)&&(!i||i===c)){if(s.splice(l,1),s.length===0){delete this.eventListeners[e];break}l--}}_fireChangeEvent(e,r,i){this._fire(e+Sc,{oldVal:r,newVal:i})}addName(e){if(!this.hasName(e)){var r=this.name(),i=r?r+" "+e:e;this.name(i)}return this}hasName(e){if(!e)return!1;const r=this.name();if(!r)return!1;var i=(r||"").split(/\s/g);return i.indexOf(e)!==-1}removeName(e){var r=(this.name()||"").split(/\s/g),i=r.indexOf(e);return i!==-1&&(r.splice(i,1),this.name(r.join(" "))),this}setAttr(e,r){var i=this[zl+k._capitalize(e)];return k._isFunction(i)?i.call(this,r):this._setAttr(e,r),this}_requestDraw(){if(J.autoDrawEnabled){const e=this.getLayer()||this.getStage();e==null||e.batchDraw()}}_setAttr(e,r){var i=this.attrs[e];i===r&&!k.isObject(r)||(r==null?delete this.attrs[e]:this.attrs[e]=r,this._shouldFireChangeEvents&&this._fireChangeEvent(e,i,r),this._requestDraw())}_setComponentAttr(e,r,i){var s;i!==void 0&&(s=this.attrs[e],s||(this.attrs[e]=this.getAttr(e)),this.attrs[e][r]=i,this._fireChangeEvent(e,s,i))}_fireAndBubble(e,r,i){r&&this.nodeType===bl&&(r.target=this);var s=(e===Dl||e===Gl)&&(i&&(this===i||this.isAncestorOf&&this.isAncestorOf(i))||this.nodeType==="Stage"&&!i);if(!s){this._fire(e,r);var l=(e===Dl||e===Gl)&&i&&i.isAncestorOf&&i.isAncestorOf(this)&&!i.isAncestorOf(this.parent);(r&&!r.cancelBubble||!r)&&this.parent&&this.parent.isListening()&&!l&&(i&&i.parent?this._fireAndBubble.call(this.parent,e,r,i):this._fireAndBubble.call(this.parent,e,r))}}_getProtoListeners(e){let r=this._cache.get(Ai);if(!r){r={};let s=Object.getPrototypeOf(this);for(;s;){if(!s.eventListeners){s=Object.getPrototypeOf(s);continue}for(var i in s.eventListeners){const l=s.eventListeners[i],h=r[i]||[];r[i]=l.concat(h)}s=Object.getPrototypeOf(s)}this._cache.set(Ai,r)}return r[e]}_fire(e,r){r=r||{},r.currentTarget=this,r.type=e;const i=this._getProtoListeners(e);if(i)for(var s=0;s<i.length;s++)i[s].handler.call(this,r);const l=this.eventListeners[e];if(l)for(var s=0;s<l.length;s++)l[s].handler.call(this,r)}draw(){return this.drawScene(),this.drawHit(),this}_createDragElement(e){var r=e?e.pointerId:void 0,i=this.getStage(),s=this.getAbsolutePosition(),l=i._getPointerById(r)||i._changedPointerPositions[0]||s;ce._dragElements.set(this._id,{node:this,startPointerPos:l,offset:{x:l.x-s.x,y:l.y-s.y},dragStatus:"ready",pointerId:r})}startDrag(e,r=!0){ce._dragElements.has(this._id)||this._createDragElement(e);const i=ce._dragElements.get(this._id);i.dragStatus="dragging",this.fire("dragstart",{type:"dragstart",target:this,evt:e&&e.evt},r)}_setDragPosition(e,r){const i=this.getStage()._getPointerById(r.pointerId);if(i){var s={x:i.x-r.offset.x,y:i.y-r.offset.y},l=this.dragBoundFunc();if(l!==void 0){const h=l.call(this,s,e);h?s=h:k.warn("dragBoundFunc did not return any value. That is unexpected behavior. You must return new absolute position from dragBoundFunc.")}(!this._lastPos||this._lastPos.x!==s.x||this._lastPos.y!==s.y)&&(this.setAbsolutePosition(s),this._requestDraw()),this._lastPos=s}}stopDrag(e){const r=ce._dragElements.get(this._id);r&&(r.dragStatus="stopped"),ce._endDragBefore(e),ce._endDragAfter(e)}setDraggable(e){this._setAttr("draggable",e),this._dragChange()}isDragging(){const e=ce._dragElements.get(this._id);return e?e.dragStatus==="dragging":!1}_listenDrag(){this._dragCleanup(),this.on("mousedown.konva touchstart.konva",function(e){var r=e.evt.button!==void 0,i=!r||J.dragButtons.indexOf(e.evt.button)>=0;if(i&&!this.isDragging()){var s=!1;ce._dragElements.forEach(l=>{this.isAncestorOf(l.node)&&(s=!0)}),s||this._createDragElement(e)}})}_dragChange(){if(this.attrs.draggable)this._listenDrag();else{this._dragCleanup();var e=this.getStage();if(!e)return;const r=ce._dragElements.get(this._id),i=r&&r.dragStatus==="dragging",s=r&&r.dragStatus==="ready";i?this.stopDrag():s&&ce._dragElements.delete(this._id)}}_dragCleanup(){this.off("mousedown.konva"),this.off("touchstart.konva")}isClientRectOnScreen(e={x:0,y:0}){const r=this.getStage();if(!r)return!1;const i={x:-e.x,y:-e.y,width:r.width()+2*e.x,height:r.height()+2*e.y};return k.haveIntersection(i,this.getClientRect())}static create(e,r){return k._isString(e)&&(e=JSON.parse(e)),this._createNode(e,r)}static _createNode(e,r){var i=W.prototype.getClassName.call(e),s=e.children,l,h,c;r&&(e.attrs.container=r),J[i]||(k.warn('Can not find a node with class name "'+i+'". Fallback to "Shape".'),i="Shape");const f=J[i];if(l=new f(e.attrs),s)for(h=s.length,c=0;c<h;c++)l.add(W._createNode(s[c]));return l}}W.prototype.nodeType="Node";W.prototype._attrsAffectingSize=[];W.prototype.eventListeners={};W.prototype.on.call(W.prototype,Ec,function(){if(this._batchingTransformChange){this._needClearTransformCache=!0;return}this._clearCache(un),this._clearSelfAndDescendantCache(Vt)});W.prototype.on.call(W.prototype,"visibleChange.konva",function(){this._clearSelfAndDescendantCache(xs)});W.prototype.on.call(W.prototype,"listeningChange.konva",function(){this._clearSelfAndDescendantCache(Cs)});W.prototype.on.call(W.prototype,"opacityChange.konva",function(){this._clearSelfAndDescendantCache(Fi)});const _e=w.addGetterSetter;_e(W,"zIndex");_e(W,"absolutePosition");_e(W,"position");_e(W,"x",0,U());_e(W,"y",0,U());_e(W,"globalCompositeOperation","source-over",Jn());_e(W,"opacity",1,U());_e(W,"name","",Jn());_e(W,"id","",Jn());_e(W,"rotation",0,U());w.addComponentsGetterSetter(W,"scale",["x","y"]);_e(W,"scaleX",1,U());_e(W,"scaleY",1,U());w.addComponentsGetterSetter(W,"skew",["x","y"]);_e(W,"skewX",0,U());_e(W,"skewY",0,U());w.addComponentsGetterSetter(W,"offset",["x","y"]);_e(W,"offsetX",0,U());_e(W,"offsetY",0,U());_e(W,"dragDistance",null,U());_e(W,"width",0,U());_e(W,"height",0,U());_e(W,"listening",!0,Mt());_e(W,"preventDefault",!0,Mt());_e(W,"filters",null,function(o){return this._filterUpToDate=!1,o});_e(W,"visible",!0,Mt());_e(W,"transformsEnabled","all",Jn());_e(W,"size");_e(W,"dragBoundFunc");_e(W,"draggable",!1,Mt());w.backCompat(W,{rotateDeg:"rotate",setRotationDeg:"setRotation",getRotationDeg:"getRotation"});class ot extends W{constructor(){super(...arguments),this.children=[]}getChildren(e){if(!e)return this.children||[];const r=this.children||[];var i=[];return r.forEach(function(s){e(s)&&i.push(s)}),i}hasChildren(){return this.getChildren().length>0}removeChildren(){return this.getChildren().forEach(e=>{e.parent=null,e.index=0,e.remove()}),this.children=[],this._requestDraw(),this}destroyChildren(){return this.getChildren().forEach(e=>{e.parent=null,e.index=0,e.destroy()}),this.children=[],this._requestDraw(),this}add(...e){if(e.length===0)return this;if(e.length>1){for(var r=0;r<e.length;r++)this.add(e[r]);return this}const i=e[0];return i.getParent()?(i.moveTo(this),this):(this._validateAdd(i),i.index=this.getChildren().length,i.parent=this,i._clearCaches(),this.getChildren().push(i),this._fire("add",{child:i}),this._requestDraw(),this)}destroy(){return this.hasChildren()&&this.destroyChildren(),super.destroy(),this}find(e){return this._generalFind(e,!1)}findOne(e){var r=this._generalFind(e,!0);return r.length>0?r[0]:void 0}_generalFind(e,r){var i=[];return this._descendants(s=>{const l=s._isMatch(e);return l&&i.push(s),!!(l&&r)}),i}_descendants(e){let r=!1;const i=this.getChildren();for(const s of i){if(r=e(s),r)return!0;if(s.hasChildren()&&(r=s._descendants(e),r))return!0}return!1}toObject(){var e=W.prototype.toObject.call(this);return e.children=[],this.getChildren().forEach(r=>{e.children.push(r.toObject())}),e}isAncestorOf(e){for(var r=e.getParent();r;){if(r._id===this._id)return!0;r=r.getParent()}return!1}clone(e){var r=W.prototype.clone.call(this,e);return this.getChildren().forEach(function(i){r.add(i.clone())}),r}getAllIntersections(e){var r=[];return this.find("Shape").forEach(function(i){i.isVisible()&&i.intersects(e)&&r.push(i)}),r}_clearSelfAndDescendantCache(e){var r;super._clearSelfAndDescendantCache(e),!this.isCached()&&((r=this.children)===null||r===void 0||r.forEach(function(i){i._clearSelfAndDescendantCache(e)}))}_setChildrenIndices(){var e;(e=this.children)===null||e===void 0||e.forEach(function(r,i){r.index=i}),this._requestDraw()}drawScene(e,r){var i=this.getLayer(),s=e||i&&i.getCanvas(),l=s&&s.getContext(),h=this._getCanvasCache(),c=h&&h.scene,f=s&&s.isCache;if(!this.isVisible()&&!f)return this;if(c){l.save();var p=this.getAbsoluteTransform(r).getMatrix();l.transform(p[0],p[1],p[2],p[3],p[4],p[5]),this._drawCachedSceneCanvas(l),l.restore()}else this._drawChildren("drawScene",s,r);return this}drawHit(e,r){if(!this.shouldDrawHit(r))return this;var i=this.getLayer(),s=e||i&&i.hitCanvas,l=s&&s.getContext(),h=this._getCanvasCache(),c=h&&h.hit;if(c){l.save();var f=this.getAbsoluteTransform(r).getMatrix();l.transform(f[0],f[1],f[2],f[3],f[4],f[5]),this._drawCachedHitCanvas(l),l.restore()}else this._drawChildren("drawHit",s,r);return this}_drawChildren(e,r,i){var s,l=r&&r.getContext(),h=this.clipWidth(),c=this.clipHeight(),f=this.clipFunc(),p=h&&c||f;const v=i===this;if(p){l.save();var _=this.getAbsoluteTransform(i),y=_.getMatrix();if(l.transform(y[0],y[1],y[2],y[3],y[4],y[5]),l.beginPath(),f)f.call(this,l,this);else{var C=this.clipX(),S=this.clipY();l.rect(C,S,h,c)}l.clip(),y=_.copy().invert().getMatrix(),l.transform(y[0],y[1],y[2],y[3],y[4],y[5])}var M=!v&&this.globalCompositeOperation()!=="source-over"&&e==="drawScene";M&&(l.save(),l._applyGlobalCompositeOperation(this)),(s=this.children)===null||s===void 0||s.forEach(function(T){T[e](r,i)}),M&&l.restore(),p&&l.restore()}getClientRect(e){var r;e=e||{};var i=e.skipTransform,s=e.relativeTo,l,h,c,f,p={x:1/0,y:1/0,width:0,height:0},v=this;(r=this.children)===null||r===void 0||r.forEach(function(M){if(M.visible()){var T=M.getClientRect({relativeTo:v,skipShadow:e.skipShadow,skipStroke:e.skipStroke});T.width===0&&T.height===0||(l===void 0?(l=T.x,h=T.y,c=T.x+T.width,f=T.y+T.height):(l=Math.min(l,T.x),h=Math.min(h,T.y),c=Math.max(c,T.x+T.width),f=Math.max(f,T.y+T.height)))}});for(var _=this.find("Shape"),y=!1,C=0;C<_.length;C++){var S=_[C];if(S._isVisible(this)){y=!0;break}}return y&&l!==void 0?p={x:l,y:h,width:c-l,height:f-h}:p={x:0,y:0,width:0,height:0},i?p:this._transformedRect(p,s)}}w.addComponentsGetterSetter(ot,"clip",["x","y","width","height"]);w.addGetterSetter(ot,"clipX",void 0,U());w.addGetterSetter(ot,"clipY",void 0,U());w.addGetterSetter(ot,"clipWidth",void 0,U());w.addGetterSetter(ot,"clipHeight",void 0,U());w.addGetterSetter(ot,"clipFunc");const Nr=new Map,_u=J._global.PointerEvent!==void 0;function ds(o){return Nr.get(o)}function Os(o){return{evt:o,pointerId:o.pointerId}}function Su(o,e){return Nr.get(o)===e}function wu(o,e){Rr(o),e.getStage()&&(Nr.set(o,e),_u&&e._fire("gotpointercapture",Os(new PointerEvent("gotpointercapture"))))}function Rr(o,e){const r=Nr.get(o);if(!r)return;const i=r.getStage();i&&i.content,Nr.delete(o),_u&&r._fire("lostpointercapture",Os(new PointerEvent("lostpointercapture")))}var kc="Stage",Tc="string",Bl="px",Mc="mouseout",Cu="mouseleave",xu="mouseover",Eu="mouseenter",Pu="mousemove",ku="mousedown",Tu="mouseup",Er="pointermove",Pr="pointerdown",Vn="pointerup",kr="pointercancel",Rc="lostpointercapture",Li="pointerout",Ni="pointerleave",Ii="pointerover",Oi="pointerenter",Es="contextmenu",Mu="touchstart",Ru="touchend",Au="touchmove",Lu="touchcancel",Ps="wheel",Ac=5,Lc=[[Eu,"_pointerenter"],[ku,"_pointerdown"],[Pu,"_pointermove"],[Tu,"_pointerup"],[Cu,"_pointerleave"],[Mu,"_pointerdown"],[Au,"_pointermove"],[Ru,"_pointerup"],[Lu,"_pointercancel"],[xu,"_pointerover"],[Ps,"_wheel"],[Es,"_contextmenu"],[Pr,"_pointerdown"],[Er,"_pointermove"],[Vn,"_pointerup"],[kr,"_pointercancel"],[Rc,"_lostpointercapture"]];const cs={mouse:{[Li]:Mc,[Ni]:Cu,[Ii]:xu,[Oi]:Eu,[Er]:Pu,[Pr]:ku,[Vn]:Tu,[kr]:"mousecancel",pointerclick:"click",pointerdblclick:"dblclick"},touch:{[Li]:"touchout",[Ni]:"touchleave",[Ii]:"touchover",[Oi]:"touchenter",[Er]:Au,[Pr]:Mu,[Vn]:Ru,[kr]:Lu,pointerclick:"tap",pointerdblclick:"dbltap"},pointer:{[Li]:Li,[Ni]:Ni,[Ii]:Ii,[Oi]:Oi,[Er]:Er,[Pr]:Pr,[Vn]:Vn,[kr]:kr,pointerclick:"pointerclick",pointerdblclick:"pointerdblclick"}},Tr=o=>o.indexOf("pointer")>=0?"pointer":o.indexOf("touch")>=0?"touch":"mouse",Yn=o=>{const e=Tr(o);if(e==="pointer")return J.pointerEventsEnabled&&cs.pointer;if(e==="touch")return cs.touch;if(e==="mouse")return cs.mouse};function Hl(o={}){return(o.clipFunc||o.clipWidth||o.clipHeight)&&k.warn("Stage does not support clipping. Please use clip for Layers or Groups."),o}const Nc="Pointer position is missing and not registered by the stage. Looks like it is outside of the stage container. You can set it manually from event: stage.setPointersPositions(event);",Hi=[];let Yi=class extends ot{constructor(e){super(Hl(e)),this._pointerPositions=[],this._changedPointerPositions=[],this._buildDOM(),this._bindContentEvents(),Hi.push(this),this.on("widthChange.konva heightChange.konva",this._resizeDOM),this.on("visibleChange.konva",this._checkVisibility),this.on("clipWidthChange.konva clipHeightChange.konva clipFuncChange.konva",()=>{Hl(this.attrs)}),this._checkVisibility()}_validateAdd(e){const r=e.getType()==="Layer",i=e.getType()==="FastLayer";r||i||k.throw("You may only add layers to the stage.")}_checkVisibility(){if(!this.content)return;const e=this.visible()?"":"none";this.content.style.display=e}setContainer(e){if(typeof e===Tc){if(e.charAt(0)==="."){var r=e.slice(1);e=document.getElementsByClassName(r)[0]}else{var i;e.charAt(0)!=="#"?i=e:i=e.slice(1),e=document.getElementById(i)}if(!e)throw"Can not find container in document with id "+i}return this._setAttr("container",e),this.content&&(this.content.parentElement&&this.content.parentElement.removeChild(this.content),e.appendChild(this.content)),this}shouldDrawHit(){return!0}clear(){var e=this.children,r=e.length,i;for(i=0;i<r;i++)e[i].clear();return this}clone(e){return e||(e={}),e.container=typeof document<"u"&&document.createElement("div"),ot.prototype.clone.call(this,e)}destroy(){super.destroy();var e=this.content;e&&k._isInDocument(e)&&this.container().removeChild(e);var r=Hi.indexOf(this);return r>-1&&Hi.splice(r,1),k.releaseCanvas(this.bufferCanvas._canvas,this.bufferHitCanvas._canvas),this}getPointerPosition(){const e=this._pointerPositions[0]||this._changedPointerPositions[0];return e?{x:e.x,y:e.y}:(k.warn(Nc),null)}_getPointerById(e){return this._pointerPositions.find(r=>r.id===e)}getPointersPositions(){return this._pointerPositions}getStage(){return this}getContent(){return this.content}_toKonvaCanvas(e){e=e||{},e.x=e.x||0,e.y=e.y||0,e.width=e.width||this.width(),e.height=e.height||this.height();var r=new Qn({width:e.width,height:e.height,pixelRatio:e.pixelRatio||1}),i=r.getContext()._context,s=this.children;return(e.x||e.y)&&i.translate(-1*e.x,-1*e.y),s.forEach(function(l){if(l.isVisible()){var h=l._toKonvaCanvas(e);i.drawImage(h._canvas,e.x,e.y,h.getWidth()/h.getPixelRatio(),h.getHeight()/h.getPixelRatio())}}),r}getIntersection(e){if(!e)return null;var r=this.children,i=r.length,s=i-1,l;for(l=s;l>=0;l--){const h=r[l].getIntersection(e);if(h)return h}return null}_resizeDOM(){var e=this.width(),r=this.height();this.content&&(this.content.style.width=e+Bl,this.content.style.height=r+Bl),this.bufferCanvas.setSize(e,r),this.bufferHitCanvas.setSize(e,r),this.children.forEach(i=>{i.setSize({width:e,height:r}),i.draw()})}add(e,...r){if(arguments.length>1){for(var i=0;i<arguments.length;i++)this.add(arguments[i]);return this}super.add(e);var s=this.children.length;return s>Ac&&k.warn("The stage has "+s+" layers. Recommended maximum number of layers is 3-5. Adding more layers into the stage may drop the performance. Rethink your tree structure, you can use Konva.Group."),e.setSize({width:this.width(),height:this.height()}),e.draw(),J.isBrowser&&this.content.appendChild(e.canvas._canvas),this}getParent(){return null}getLayer(){return null}hasPointerCapture(e){return Su(e,this)}setPointerCapture(e){wu(e,this)}releaseCapture(e){Rr(e)}getLayers(){return this.children}_bindContentEvents(){J.isBrowser&&Lc.forEach(([e,r])=>{this.content.addEventListener(e,i=>{this[r](i)},{passive:!1})})}_pointerenter(e){this.setPointersPositions(e);const r=Yn(e.type);this._fire(r.pointerenter,{evt:e,target:this,currentTarget:this})}_pointerover(e){this.setPointersPositions(e);const r=Yn(e.type);this._fire(r.pointerover,{evt:e,target:this,currentTarget:this})}_getTargetShape(e){let r=this[e+"targetShape"];return r&&!r.getStage()&&(r=null),r}_pointerleave(e){const r=Yn(e.type),i=Tr(e.type);if(r){this.setPointersPositions(e);var s=this._getTargetShape(i),l=!ce.isDragging||J.hitOnDragEnabled;s&&l?(s._fireAndBubble(r.pointerout,{evt:e}),s._fireAndBubble(r.pointerleave,{evt:e}),this._fire(r.pointerleave,{evt:e,target:this,currentTarget:this}),this[i+"targetShape"]=null):l&&(this._fire(r.pointerleave,{evt:e,target:this,currentTarget:this}),this._fire(r.pointerout,{evt:e,target:this,currentTarget:this})),this.pointerPos=void 0,this._pointerPositions=[]}}_pointerdown(e){const r=Yn(e.type),i=Tr(e.type);if(r){this.setPointersPositions(e);var s=!1;this._changedPointerPositions.forEach(l=>{var h=this.getIntersection(l);if(ce.justDragged=!1,J["_"+i+"ListenClick"]=!0,!(h&&h.isListening()))return;J.capturePointerEventsEnabled&&h.setPointerCapture(l.id),this[i+"ClickStartShape"]=h,h._fireAndBubble(r.pointerdown,{evt:e,pointerId:l.id}),s=!0;const f=e.type.indexOf("touch")>=0;h.preventDefault()&&e.cancelable&&f&&e.preventDefault()}),s||this._fire(r.pointerdown,{evt:e,target:this,currentTarget:this,pointerId:this._pointerPositions[0].id})}}_pointermove(e){const r=Yn(e.type),i=Tr(e.type);if(!r)return;ce.isDragging&&ce.node.preventDefault()&&e.cancelable&&e.preventDefault(),this.setPointersPositions(e);var s=!ce.isDragging||J.hitOnDragEnabled;if(!s)return;var l={};let h=!1;var c=this._getTargetShape(i);this._changedPointerPositions.forEach(f=>{const p=ds(f.id)||this.getIntersection(f),v=f.id,_={evt:e,pointerId:v};var y=c!==p;if(y&&c&&(c._fireAndBubble(r.pointerout,Object.assign({},_),p),c._fireAndBubble(r.pointerleave,Object.assign({},_),p)),p){if(l[p._id])return;l[p._id]=!0}p&&p.isListening()?(h=!0,y&&(p._fireAndBubble(r.pointerover,Object.assign({},_),c),p._fireAndBubble(r.pointerenter,Object.assign({},_),c),this[i+"targetShape"]=p),p._fireAndBubble(r.pointermove,Object.assign({},_))):c&&(this._fire(r.pointerover,{evt:e,target:this,currentTarget:this,pointerId:v}),this[i+"targetShape"]=null)}),h||this._fire(r.pointermove,{evt:e,target:this,currentTarget:this,pointerId:this._changedPointerPositions[0].id})}_pointerup(e){const r=Yn(e.type),i=Tr(e.type);if(!r)return;this.setPointersPositions(e);const s=this[i+"ClickStartShape"],l=this[i+"ClickEndShape"];var h={};let c=!1;this._changedPointerPositions.forEach(f=>{const p=ds(f.id)||this.getIntersection(f);if(p){if(p.releaseCapture(f.id),h[p._id])return;h[p._id]=!0}const v=f.id,_={evt:e,pointerId:v};let y=!1;J["_"+i+"InDblClickWindow"]?(y=!0,clearTimeout(this[i+"DblTimeout"])):ce.justDragged||(J["_"+i+"InDblClickWindow"]=!0,clearTimeout(this[i+"DblTimeout"])),this[i+"DblTimeout"]=setTimeout(function(){J["_"+i+"InDblClickWindow"]=!1},J.dblClickWindow),p&&p.isListening()?(c=!0,this[i+"ClickEndShape"]=p,p._fireAndBubble(r.pointerup,Object.assign({},_)),J["_"+i+"ListenClick"]&&s&&s===p&&(p._fireAndBubble(r.pointerclick,Object.assign({},_)),y&&l&&l===p&&p._fireAndBubble(r.pointerdblclick,Object.assign({},_)))):(this[i+"ClickEndShape"]=null,J["_"+i+"ListenClick"]&&this._fire(r.pointerclick,{evt:e,target:this,currentTarget:this,pointerId:v}),y&&this._fire(r.pointerdblclick,{evt:e,target:this,currentTarget:this,pointerId:v}))}),c||this._fire(r.pointerup,{evt:e,target:this,currentTarget:this,pointerId:this._changedPointerPositions[0].id}),J["_"+i+"ListenClick"]=!1,e.cancelable&&i!=="touch"&&e.preventDefault()}_contextmenu(e){this.setPointersPositions(e);var r=this.getIntersection(this.getPointerPosition());r&&r.isListening()?r._fireAndBubble(Es,{evt:e}):this._fire(Es,{evt:e,target:this,currentTarget:this})}_wheel(e){this.setPointersPositions(e);var r=this.getIntersection(this.getPointerPosition());r&&r.isListening()?r._fireAndBubble(Ps,{evt:e}):this._fire(Ps,{evt:e,target:this,currentTarget:this})}_pointercancel(e){this.setPointersPositions(e);const r=ds(e.pointerId)||this.getIntersection(this.getPointerPosition());r&&r._fireAndBubble(Vn,Os(e)),Rr(e.pointerId)}_lostpointercapture(e){Rr(e.pointerId)}setPointersPositions(e){var r=this._getContentPosition(),i=null,s=null;e=e||window.event,e.touches!==void 0?(this._pointerPositions=[],this._changedPointerPositions=[],Array.prototype.forEach.call(e.touches,l=>{this._pointerPositions.push({id:l.identifier,x:(l.clientX-r.left)/r.scaleX,y:(l.clientY-r.top)/r.scaleY})}),Array.prototype.forEach.call(e.changedTouches||e.touches,l=>{this._changedPointerPositions.push({id:l.identifier,x:(l.clientX-r.left)/r.scaleX,y:(l.clientY-r.top)/r.scaleY})})):(i=(e.clientX-r.left)/r.scaleX,s=(e.clientY-r.top)/r.scaleY,this.pointerPos={x:i,y:s},this._pointerPositions=[{x:i,y:s,id:k._getFirstPointerId(e)}],this._changedPointerPositions=[{x:i,y:s,id:k._getFirstPointerId(e)}])}_setPointerPosition(e){k.warn('Method _setPointerPosition is deprecated. Use "stage.setPointersPositions(event)" instead.'),this.setPointersPositions(e)}_getContentPosition(){if(!this.content||!this.content.getBoundingClientRect)return{top:0,left:0,scaleX:1,scaleY:1};var e=this.content.getBoundingClientRect();return{top:e.top,left:e.left,scaleX:e.width/this.content.clientWidth||1,scaleY:e.height/this.content.clientHeight||1}}_buildDOM(){if(this.bufferCanvas=new Qn({width:this.width(),height:this.height()}),this.bufferHitCanvas=new Is({pixelRatio:1,width:this.width(),height:this.height()}),!!J.isBrowser){var e=this.container();if(!e)throw"Stage has no container. A container is required.";e.innerHTML="",this.content=document.createElement("div"),this.content.style.position="relative",this.content.style.userSelect="none",this.content.className="konvajs-content",this.content.setAttribute("role","presentation"),e.appendChild(this.content),this._resizeDOM()}}cache(){return k.warn("Cache function is not allowed for stage. You may use cache only for layers, groups and shapes."),this}clearCache(){return this}batchDraw(){return this.getChildren().forEach(function(e){e.batchDraw()}),this}};Yi.prototype.nodeType=kc;Re(Yi);w.addGetterSetter(Yi,"container");var Nu="hasShadow",Iu="shadowRGBA",Ou="patternImage",Du="linearGradient",Gu="radialGradient";let Di;function fs(){return Di||(Di=k.createCanvasElement().getContext("2d"),Di)}const Ar={};function Ic(o){o.fill()}function Oc(o){o.stroke()}function Dc(o){o.fill()}function Gc(o){o.stroke()}function zc(){this._clearCache(Nu)}function bc(){this._clearCache(Iu)}function Fc(){this._clearCache(Ou)}function Bc(){this._clearCache(Du)}function Hc(){this._clearCache(Gu)}class z extends W{constructor(e){super(e);let r;for(;r=k.getRandomColor(),!(r&&!(r in Ar)););this.colorKey=r,Ar[r]=this}getContext(){return k.warn("shape.getContext() method is deprecated. Please do not use it."),this.getLayer().getContext()}getCanvas(){return k.warn("shape.getCanvas() method is deprecated. Please do not use it."),this.getLayer().getCanvas()}getSceneFunc(){return this.attrs.sceneFunc||this._sceneFunc}getHitFunc(){return this.attrs.hitFunc||this._hitFunc}hasShadow(){return this._getCache(Nu,this._hasShadow)}_hasShadow(){return this.shadowEnabled()&&this.shadowOpacity()!==0&&!!(this.shadowColor()||this.shadowBlur()||this.shadowOffsetX()||this.shadowOffsetY())}_getFillPattern(){return this._getCache(Ou,this.__getFillPattern)}__getFillPattern(){if(this.fillPatternImage()){var e=fs();const r=e.createPattern(this.fillPatternImage(),this.fillPatternRepeat()||"repeat");if(r&&r.setTransform){const i=new st;i.translate(this.fillPatternX(),this.fillPatternY()),i.rotate(J.getAngle(this.fillPatternRotation())),i.scale(this.fillPatternScaleX(),this.fillPatternScaleY()),i.translate(-1*this.fillPatternOffsetX(),-1*this.fillPatternOffsetY());const s=i.getMatrix(),l=typeof DOMMatrix>"u"?{a:s[0],b:s[1],c:s[2],d:s[3],e:s[4],f:s[5]}:new DOMMatrix(s);r.setTransform(l)}return r}}_getLinearGradient(){return this._getCache(Du,this.__getLinearGradient)}__getLinearGradient(){var e=this.fillLinearGradientColorStops();if(e){for(var r=fs(),i=this.fillLinearGradientStartPoint(),s=this.fillLinearGradientEndPoint(),l=r.createLinearGradient(i.x,i.y,s.x,s.y),h=0;h<e.length;h+=2)l.addColorStop(e[h],e[h+1]);return l}}_getRadialGradient(){return this._getCache(Gu,this.__getRadialGradient)}__getRadialGradient(){var e=this.fillRadialGradientColorStops();if(e){for(var r=fs(),i=this.fillRadialGradientStartPoint(),s=this.fillRadialGradientEndPoint(),l=r.createRadialGradient(i.x,i.y,this.fillRadialGradientStartRadius(),s.x,s.y,this.fillRadialGradientEndRadius()),h=0;h<e.length;h+=2)l.addColorStop(e[h],e[h+1]);return l}}getShadowRGBA(){return this._getCache(Iu,this._getShadowRGBA)}_getShadowRGBA(){if(this.hasShadow()){var e=k.colorToRGBA(this.shadowColor());if(e)return"rgba("+e.r+","+e.g+","+e.b+","+e.a*(this.shadowOpacity()||1)+")"}}hasFill(){return this._calculate("hasFill",["fillEnabled","fill","fillPatternImage","fillLinearGradientColorStops","fillRadialGradientColorStops"],()=>this.fillEnabled()&&!!(this.fill()||this.fillPatternImage()||this.fillLinearGradientColorStops()||this.fillRadialGradientColorStops()))}hasStroke(){return this._calculate("hasStroke",["strokeEnabled","strokeWidth","stroke","strokeLinearGradientColorStops"],()=>this.strokeEnabled()&&this.strokeWidth()&&!!(this.stroke()||this.strokeLinearGradientColorStops()))}hasHitStroke(){const e=this.hitStrokeWidth();return e==="auto"?this.hasStroke():this.strokeEnabled()&&!!e}intersects(e){var r=this.getStage(),i=r.bufferHitCanvas,s;return i.getContext().clear(),this.drawHit(i,null,!0),s=i.context.getImageData(Math.round(e.x),Math.round(e.y),1,1).data,s[3]>0}destroy(){return W.prototype.destroy.call(this),delete Ar[this.colorKey],delete this.colorKey,this}_useBufferCanvas(e){var r;if(!this.getStage()||!((r=this.attrs.perfectDrawEnabled)!==null&&r!==void 0?r:!0))return!1;const s=e||this.hasFill(),l=this.hasStroke(),h=this.getAbsoluteOpacity()!==1;if(s&&l&&h)return!0;const c=this.hasShadow(),f=this.shadowForStrokeEnabled();return!!(s&&l&&c&&f)}setStrokeHitEnabled(e){k.warn("strokeHitEnabled property is deprecated. Please use hitStrokeWidth instead."),e?this.hitStrokeWidth("auto"):this.hitStrokeWidth(0)}getStrokeHitEnabled(){return this.hitStrokeWidth()!==0}getSelfRect(){var e=this.size();return{x:this._centroid?-e.width/2:0,y:this._centroid?-e.height/2:0,width:e.width,height:e.height}}getClientRect(e={}){const r=e.skipTransform,i=e.relativeTo,s=this.getSelfRect(),h=!e.skipStroke&&this.hasStroke()&&this.strokeWidth()||0,c=s.width+h,f=s.height+h,p=!e.skipShadow&&this.hasShadow(),v=p?this.shadowOffsetX():0,_=p?this.shadowOffsetY():0,y=c+Math.abs(v),C=f+Math.abs(_),S=p&&this.shadowBlur()||0,M=y+S*2,T=C+S*2,G={width:M,height:T,x:-(h/2+S)+Math.min(v,0)+s.x,y:-(h/2+S)+Math.min(_,0)+s.y};return r?G:this._transformedRect(G,i)}drawScene(e,r){var i=this.getLayer(),s=e||i.getCanvas(),l=s.getContext(),h=this._getCanvasCache(),c=this.getSceneFunc(),f=this.hasShadow(),p,v,_,y=s.isCache,C=r===this;if(!this.isVisible()&&!C)return this;if(h){l.save();var S=this.getAbsoluteTransform(r).getMatrix();return l.transform(S[0],S[1],S[2],S[3],S[4],S[5]),this._drawCachedSceneCanvas(l),l.restore(),this}if(!c)return this;if(l.save(),this._useBufferCanvas()&&!y){p=this.getStage(),v=p.bufferCanvas,_=v.getContext(),_.clear(),_.save(),_._applyLineJoin(this);var M=this.getAbsoluteTransform(r).getMatrix();_.transform(M[0],M[1],M[2],M[3],M[4],M[5]),c.call(this,_,this),_.restore();var T=v.pixelRatio;f&&l._applyShadow(this),l._applyOpacity(this),l._applyGlobalCompositeOperation(this),l.drawImage(v._canvas,0,0,v.width/T,v.height/T)}else{if(l._applyLineJoin(this),!C){var M=this.getAbsoluteTransform(r).getMatrix();l.transform(M[0],M[1],M[2],M[3],M[4],M[5]),l._applyOpacity(this),l._applyGlobalCompositeOperation(this)}f&&l._applyShadow(this),c.call(this,l,this)}return l.restore(),this}drawHit(e,r,i=!1){if(!this.shouldDrawHit(r,i))return this;var s=this.getLayer(),l=e||s.hitCanvas,h=l&&l.getContext(),c=this.hitFunc()||this.sceneFunc(),f=this._getCanvasCache(),p=f&&f.hit;if(this.colorKey||k.warn("Looks like your canvas has a destroyed shape in it. Do not reuse shape after you destroyed it. If you want to reuse shape you should call remove() instead of destroy()"),p){h.save();var v=this.getAbsoluteTransform(r).getMatrix();return h.transform(v[0],v[1],v[2],v[3],v[4],v[5]),this._drawCachedHitCanvas(h),h.restore(),this}if(!c)return this;if(h.save(),h._applyLineJoin(this),!(this===r)){var y=this.getAbsoluteTransform(r).getMatrix();h.transform(y[0],y[1],y[2],y[3],y[4],y[5])}return c.call(this,h,this),h.restore(),this}drawHitFromCache(e=0){var r=this._getCanvasCache(),i=this._getCachedSceneCanvas(),s=r.hit,l=s.getContext(),h=s.getWidth(),c=s.getHeight(),f,p,v,_,y,C;l.clear(),l.drawImage(i._canvas,0,0,h,c);try{for(f=l.getImageData(0,0,h,c),p=f.data,v=p.length,_=k._hexToRgb(this.colorKey),y=0;y<v;y+=4)C=p[y+3],C>e?(p[y]=_.r,p[y+1]=_.g,p[y+2]=_.b,p[y+3]=255):p[y+3]=0;l.putImageData(f,0,0)}catch(S){k.error("Unable to draw hit graph from cached scene canvas. "+S.message)}return this}hasPointerCapture(e){return Su(e,this)}setPointerCapture(e){wu(e,this)}releaseCapture(e){Rr(e)}}z.prototype._fillFunc=Ic;z.prototype._strokeFunc=Oc;z.prototype._fillFuncHit=Dc;z.prototype._strokeFuncHit=Gc;z.prototype._centroid=!1;z.prototype.nodeType="Shape";Re(z);z.prototype.eventListeners={};z.prototype.on.call(z.prototype,"shadowColorChange.konva shadowBlurChange.konva shadowOffsetChange.konva shadowOpacityChange.konva shadowEnabledChange.konva",zc);z.prototype.on.call(z.prototype,"shadowColorChange.konva shadowOpacityChange.konva shadowEnabledChange.konva",bc);z.prototype.on.call(z.prototype,"fillPriorityChange.konva fillPatternImageChange.konva fillPatternRepeatChange.konva fillPatternScaleXChange.konva fillPatternScaleYChange.konva fillPatternOffsetXChange.konva fillPatternOffsetYChange.konva fillPatternXChange.konva fillPatternYChange.konva fillPatternRotationChange.konva",Fc);z.prototype.on.call(z.prototype,"fillPriorityChange.konva fillLinearGradientColorStopsChange.konva fillLinearGradientStartPointXChange.konva fillLinearGradientStartPointYChange.konva fillLinearGradientEndPointXChange.konva fillLinearGradientEndPointYChange.konva",Bc);z.prototype.on.call(z.prototype,"fillPriorityChange.konva fillRadialGradientColorStopsChange.konva fillRadialGradientStartPointXChange.konva fillRadialGradientStartPointYChange.konva fillRadialGradientEndPointXChange.konva fillRadialGradientEndPointYChange.konva fillRadialGradientStartRadiusChange.konva fillRadialGradientEndRadiusChange.konva",Hc);w.addGetterSetter(z,"stroke",void 0,yu());w.addGetterSetter(z,"strokeWidth",2,U());w.addGetterSetter(z,"fillAfterStrokeEnabled",!1);w.addGetterSetter(z,"hitStrokeWidth","auto",Ns());w.addGetterSetter(z,"strokeHitEnabled",!0,Mt());w.addGetterSetter(z,"perfectDrawEnabled",!0,Mt());w.addGetterSetter(z,"shadowForStrokeEnabled",!0,Mt());w.addGetterSetter(z,"lineJoin");w.addGetterSetter(z,"lineCap");w.addGetterSetter(z,"sceneFunc");w.addGetterSetter(z,"hitFunc");w.addGetterSetter(z,"dash");w.addGetterSetter(z,"dashOffset",0,U());w.addGetterSetter(z,"shadowColor",void 0,Jn());w.addGetterSetter(z,"shadowBlur",0,U());w.addGetterSetter(z,"shadowOpacity",1,U());w.addComponentsGetterSetter(z,"shadowOffset",["x","y"]);w.addGetterSetter(z,"shadowOffsetX",0,U());w.addGetterSetter(z,"shadowOffsetY",0,U());w.addGetterSetter(z,"fillPatternImage");w.addGetterSetter(z,"fill",void 0,yu());w.addGetterSetter(z,"fillPatternX",0,U());w.addGetterSetter(z,"fillPatternY",0,U());w.addGetterSetter(z,"fillLinearGradientColorStops");w.addGetterSetter(z,"strokeLinearGradientColorStops");w.addGetterSetter(z,"fillRadialGradientStartRadius",0);w.addGetterSetter(z,"fillRadialGradientEndRadius",0);w.addGetterSetter(z,"fillRadialGradientColorStops");w.addGetterSetter(z,"fillPatternRepeat","repeat");w.addGetterSetter(z,"fillEnabled",!0);w.addGetterSetter(z,"strokeEnabled",!0);w.addGetterSetter(z,"shadowEnabled",!0);w.addGetterSetter(z,"dashEnabled",!0);w.addGetterSetter(z,"strokeScaleEnabled",!0);w.addGetterSetter(z,"fillPriority","color");w.addComponentsGetterSetter(z,"fillPatternOffset",["x","y"]);w.addGetterSetter(z,"fillPatternOffsetX",0,U());w.addGetterSetter(z,"fillPatternOffsetY",0,U());w.addComponentsGetterSetter(z,"fillPatternScale",["x","y"]);w.addGetterSetter(z,"fillPatternScaleX",1,U());w.addGetterSetter(z,"fillPatternScaleY",1,U());w.addComponentsGetterSetter(z,"fillLinearGradientStartPoint",["x","y"]);w.addComponentsGetterSetter(z,"strokeLinearGradientStartPoint",["x","y"]);w.addGetterSetter(z,"fillLinearGradientStartPointX",0);w.addGetterSetter(z,"strokeLinearGradientStartPointX",0);w.addGetterSetter(z,"fillLinearGradientStartPointY",0);w.addGetterSetter(z,"strokeLinearGradientStartPointY",0);w.addComponentsGetterSetter(z,"fillLinearGradientEndPoint",["x","y"]);w.addComponentsGetterSetter(z,"strokeLinearGradientEndPoint",["x","y"]);w.addGetterSetter(z,"fillLinearGradientEndPointX",0);w.addGetterSetter(z,"strokeLinearGradientEndPointX",0);w.addGetterSetter(z,"fillLinearGradientEndPointY",0);w.addGetterSetter(z,"strokeLinearGradientEndPointY",0);w.addComponentsGetterSetter(z,"fillRadialGradientStartPoint",["x","y"]);w.addGetterSetter(z,"fillRadialGradientStartPointX",0);w.addGetterSetter(z,"fillRadialGradientStartPointY",0);w.addComponentsGetterSetter(z,"fillRadialGradientEndPoint",["x","y"]);w.addGetterSetter(z,"fillRadialGradientEndPointX",0);w.addGetterSetter(z,"fillRadialGradientEndPointY",0);w.addGetterSetter(z,"fillPatternRotation",0);w.backCompat(z,{dashArray:"dash",getDashArray:"getDash",setDashArray:"getDash",drawFunc:"sceneFunc",getDrawFunc:"getSceneFunc",setDrawFunc:"setSceneFunc",drawHitFunc:"hitFunc",getDrawHitFunc:"getHitFunc",setDrawHitFunc:"setHitFunc"});var Wc="#",Uc="beforeDraw",Xc="draw",zu=[{x:0,y:0},{x:-1,y:-1},{x:1,y:-1},{x:1,y:1},{x:-1,y:1}],Yc=zu.length;let kn=class extends ot{constructor(e){super(e),this.canvas=new Qn,this.hitCanvas=new Is({pixelRatio:1}),this._waitingForDraw=!1,this.on("visibleChange.konva",this._checkVisibility),this._checkVisibility(),this.on("imageSmoothingEnabledChange.konva",this._setSmoothEnabled),this._setSmoothEnabled()}createPNGStream(){return this.canvas._canvas.createPNGStream()}getCanvas(){return this.canvas}getNativeCanvasElement(){return this.canvas._canvas}getHitCanvas(){return this.hitCanvas}getContext(){return this.getCanvas().getContext()}clear(e){return this.getContext().clear(e),this.getHitCanvas().getContext().clear(e),this}setZIndex(e){super.setZIndex(e);var r=this.getStage();return r&&r.content&&(r.content.removeChild(this.getNativeCanvasElement()),e<r.children.length-1?r.content.insertBefore(this.getNativeCanvasElement(),r.children[e+1].getCanvas()._canvas):r.content.appendChild(this.getNativeCanvasElement())),this}moveToTop(){W.prototype.moveToTop.call(this);var e=this.getStage();return e&&e.content&&(e.content.removeChild(this.getNativeCanvasElement()),e.content.appendChild(this.getNativeCanvasElement())),!0}moveUp(){var e=W.prototype.moveUp.call(this);if(!e)return!1;var r=this.getStage();return!r||!r.content?!1:(r.content.removeChild(this.getNativeCanvasElement()),this.index<r.children.length-1?r.content.insertBefore(this.getNativeCanvasElement(),r.children[this.index+1].getCanvas()._canvas):r.content.appendChild(this.getNativeCanvasElement()),!0)}moveDown(){if(W.prototype.moveDown.call(this)){var e=this.getStage();if(e){var r=e.children;e.content&&(e.content.removeChild(this.getNativeCanvasElement()),e.content.insertBefore(this.getNativeCanvasElement(),r[this.index+1].getCanvas()._canvas))}return!0}return!1}moveToBottom(){if(W.prototype.moveToBottom.call(this)){var e=this.getStage();if(e){var r=e.children;e.content&&(e.content.removeChild(this.getNativeCanvasElement()),e.content.insertBefore(this.getNativeCanvasElement(),r[1].getCanvas()._canvas))}return!0}return!1}getLayer(){return this}remove(){var e=this.getNativeCanvasElement();return W.prototype.remove.call(this),e&&e.parentNode&&k._isInDocument(e)&&e.parentNode.removeChild(e),this}getStage(){return this.parent}setSize({width:e,height:r}){return this.canvas.setSize(e,r),this.hitCanvas.setSize(e,r),this._setSmoothEnabled(),this}_validateAdd(e){var r=e.getType();r!=="Group"&&r!=="Shape"&&k.throw("You may only add groups and shapes to a layer.")}_toKonvaCanvas(e){return e=e||{},e.width=e.width||this.getWidth(),e.height=e.height||this.getHeight(),e.x=e.x!==void 0?e.x:this.x(),e.y=e.y!==void 0?e.y:this.y(),W.prototype._toKonvaCanvas.call(this,e)}_checkVisibility(){this.visible()?this.canvas._canvas.style.display="block":this.canvas._canvas.style.display="none"}_setSmoothEnabled(){this.getContext()._context.imageSmoothingEnabled=this.imageSmoothingEnabled()}getWidth(){if(this.parent)return this.parent.width()}setWidth(){k.warn('Can not change width of layer. Use "stage.width(value)" function instead.')}getHeight(){if(this.parent)return this.parent.height()}setHeight(){k.warn('Can not change height of layer. Use "stage.height(value)" function instead.')}batchDraw(){return this._waitingForDraw||(this._waitingForDraw=!0,k.requestAnimFrame(()=>{this.draw(),this._waitingForDraw=!1})),this}getIntersection(e){if(!this.isListening()||!this.isVisible())return null;for(var r=1,i=!1;;){for(let s=0;s<Yc;s++){const l=zu[s],h=this._getIntersection({x:e.x+l.x*r,y:e.y+l.y*r}),c=h.shape;if(c)return c;if(i=!!h.antialiased,!h.antialiased)break}if(i)r+=1;else return null}}_getIntersection(e){const r=this.hitCanvas.pixelRatio,i=this.hitCanvas.context.getImageData(Math.round(e.x*r),Math.round(e.y*r),1,1).data,s=i[3];if(s===255){const l=k._rgbToHex(i[0],i[1],i[2]),h=Ar[Wc+l];return h?{shape:h}:{antialiased:!0}}else if(s>0)return{antialiased:!0};return{}}drawScene(e,r){var i=this.getLayer(),s=e||i&&i.getCanvas();return this._fire(Uc,{node:this}),this.clearBeforeDraw()&&s.getContext().clear(),ot.prototype.drawScene.call(this,s,r),this._fire(Xc,{node:this}),this}drawHit(e,r){var i=this.getLayer(),s=e||i&&i.hitCanvas;return i&&i.clearBeforeDraw()&&i.getHitCanvas().getContext().clear(),ot.prototype.drawHit.call(this,s,r),this}enableHitGraph(){return this.hitGraphEnabled(!0),this}disableHitGraph(){return this.hitGraphEnabled(!1),this}setHitGraphEnabled(e){k.warn("hitGraphEnabled method is deprecated. Please use layer.listening() instead."),this.listening(e)}getHitGraphEnabled(e){return k.warn("hitGraphEnabled method is deprecated. Please use layer.listening() instead."),this.listening()}toggleHitCanvas(){if(!(!this.parent||!this.parent.content)){var e=this.parent,r=!!this.hitCanvas._canvas.parentNode;r?e.content.removeChild(this.hitCanvas._canvas):e.content.appendChild(this.hitCanvas._canvas)}}destroy(){return k.releaseCanvas(this.getNativeCanvasElement(),this.getHitCanvas()._canvas),super.destroy()}};kn.prototype.nodeType="Layer";Re(kn);w.addGetterSetter(kn,"imageSmoothingEnabled",!0);w.addGetterSetter(kn,"clearBeforeDraw",!0);w.addGetterSetter(kn,"hitGraphEnabled",!0,Mt());class Ds extends kn{constructor(e){super(e),this.listening(!1),k.warn('Konva.Fast layer is deprecated. Please use "new Konva.Layer({ listening: false })" instead.')}}Ds.prototype.nodeType="FastLayer";Re(Ds);let Kn=class extends ot{_validateAdd(e){var r=e.getType();r!=="Group"&&r!=="Shape"&&k.throw("You may only add groups and shapes to groups.")}};Kn.prototype.nodeType="Group";Re(Kn);var gs=function(){return qn.performance&&qn.performance.now?function(){return qn.performance.now()}:function(){return new Date().getTime()}}();class mt{constructor(e,r){this.id=mt.animIdCounter++,this.frame={time:0,timeDiff:0,lastTime:gs(),frameRate:0},this.func=e,this.setLayers(r)}setLayers(e){var r=[];return e?e.length>0?r=e:r=[e]:r=[],this.layers=r,this}getLayers(){return this.layers}addLayer(e){var r=this.layers,i=r.length,s;for(s=0;s<i;s++)if(r[s]._id===e._id)return!1;return this.layers.push(e),!0}isRunning(){var e=mt,r=e.animations,i=r.length,s;for(s=0;s<i;s++)if(r[s].id===this.id)return!0;return!1}start(){return this.stop(),this.frame.timeDiff=0,this.frame.lastTime=gs(),mt._addAnimation(this),this}stop(){return mt._removeAnimation(this),this}_updateFrameObject(e){this.frame.timeDiff=e-this.frame.lastTime,this.frame.lastTime=e,this.frame.time+=this.frame.timeDiff,this.frame.frameRate=1e3/this.frame.timeDiff}static _addAnimation(e){this.animations.push(e),this._handleAnimation()}static _removeAnimation(e){var r=e.id,i=this.animations,s=i.length,l;for(l=0;l<s;l++)if(i[l].id===r){this.animations.splice(l,1);break}}static _runFrames(){var e={},r=this.animations,i,s,l,h,c,f,p,v,_;for(h=0;h<r.length;h++)if(i=r[h],s=i.layers,l=i.func,i._updateFrameObject(gs()),f=s.length,l?_=l.call(i,i.frame)!==!1:_=!0,!!_)for(c=0;c<f;c++)p=s[c],p._id!==void 0&&(e[p._id]=p);for(v in e)e.hasOwnProperty(v)&&e[v].batchDraw()}static _animationLoop(){var e=mt;e.animations.length?(e._runFrames(),k.requestAnimFrame(e._animationLoop)):e.animRunning=!1}static _handleAnimation(){this.animRunning||(this.animRunning=!0,k.requestAnimFrame(this._animationLoop))}}mt.animations=[];mt.animIdCounter=0;mt.animRunning=!1;var jc={node:1,duration:1,easing:1,onFinish:1,yoyo:1},Vc=1,Wl=2,Ul=3,qc=0,Xl=["fill","stroke","shadowColor"];class Qc{constructor(e,r,i,s,l,h,c){this.prop=e,this.propFunc=r,this.begin=s,this._pos=s,this.duration=h,this._change=0,this.prevPos=0,this.yoyo=c,this._time=0,this._position=0,this._startTime=0,this._finish=0,this.func=i,this._change=l-this.begin,this.pause()}fire(e){var r=this[e];r&&r()}setTime(e){e>this.duration?this.yoyo?(this._time=this.duration,this.reverse()):this.finish():e<0?this.yoyo?(this._time=0,this.play()):this.reset():(this._time=e,this.update())}getTime(){return this._time}setPosition(e){this.prevPos=this._pos,this.propFunc(e),this._pos=e}getPosition(e){return e===void 0&&(e=this._time),this.func(e,this.begin,this._change,this.duration)}play(){this.state=Wl,this._startTime=this.getTimer()-this._time,this.onEnterFrame(),this.fire("onPlay")}reverse(){this.state=Ul,this._time=this.duration-this._time,this._startTime=this.getTimer()-this._time,this.onEnterFrame(),this.fire("onReverse")}seek(e){this.pause(),this._time=e,this.update(),this.fire("onSeek")}reset(){this.pause(),this._time=0,this.update(),this.fire("onReset")}finish(){this.pause(),this._time=this.duration,this.update(),this.fire("onFinish")}update(){this.setPosition(this.getPosition(this._time)),this.fire("onUpdate")}onEnterFrame(){var e=this.getTimer()-this._startTime;this.state===Wl?this.setTime(e):this.state===Ul&&this.setTime(this.duration-e)}pause(){this.state=Vc,this.fire("onPause")}getTimer(){return new Date().getTime()}}class Ie{constructor(e){var r=this,i=e.node,s=i._id,l,h=e.easing||Lr.Linear,c=!!e.yoyo,f;typeof e.duration>"u"?l=.3:e.duration===0?l=.001:l=e.duration,this.node=i,this._id=qc++;var p=i.getLayer()||(i instanceof J.Stage?i.getLayers():null);p||k.error("Tween constructor have `node` that is not in a layer. Please add node into layer first."),this.anim=new mt(function(){r.tween.onEnterFrame()},p),this.tween=new Qc(f,function(v){r._tweenFunc(v)},h,0,1,l*1e3,c),this._addListeners(),Ie.attrs[s]||(Ie.attrs[s]={}),Ie.attrs[s][this._id]||(Ie.attrs[s][this._id]={}),Ie.tweens[s]||(Ie.tweens[s]={});for(f in e)jc[f]===void 0&&this._addAttr(f,e[f]);this.reset(),this.onFinish=e.onFinish,this.onReset=e.onReset,this.onUpdate=e.onUpdate}_addAttr(e,r){var i=this.node,s=i._id,l,h,c,f,p,v,_,y;if(c=Ie.tweens[s][e],c&&delete Ie.attrs[s][c][e],l=i.getAttr(e),k._isArray(r))if(h=[],p=Math.max(r.length,l.length),e==="points"&&r.length!==l.length&&(r.length>l.length?(_=l,l=k._prepareArrayForTween(l,r,i.closed())):(v=r,r=k._prepareArrayForTween(r,l,i.closed()))),e.indexOf("fill")===0)for(f=0;f<p;f++)if(f%2===0)h.push(r[f]-l[f]);else{var C=k.colorToRGBA(l[f]);y=k.colorToRGBA(r[f]),l[f]=C,h.push({r:y.r-C.r,g:y.g-C.g,b:y.b-C.b,a:y.a-C.a})}else for(f=0;f<p;f++)h.push(r[f]-l[f]);else Xl.indexOf(e)!==-1?(l=k.colorToRGBA(l),y=k.colorToRGBA(r),h={r:y.r-l.r,g:y.g-l.g,b:y.b-l.b,a:y.a-l.a}):h=r-l;Ie.attrs[s][this._id][e]={start:l,diff:h,end:r,trueEnd:v,trueStart:_},Ie.tweens[s][e]=this._id}_tweenFunc(e){var r=this.node,i=Ie.attrs[r._id][this._id],s,l,h,c,f,p,v,_;for(s in i){if(l=i[s],h=l.start,c=l.diff,_=l.end,k._isArray(h))if(f=[],v=Math.max(h.length,_.length),s.indexOf("fill")===0)for(p=0;p<v;p++)p%2===0?f.push((h[p]||0)+c[p]*e):f.push("rgba("+Math.round(h[p].r+c[p].r*e)+","+Math.round(h[p].g+c[p].g*e)+","+Math.round(h[p].b+c[p].b*e)+","+(h[p].a+c[p].a*e)+")");else for(p=0;p<v;p++)f.push((h[p]||0)+c[p]*e);else Xl.indexOf(s)!==-1?f="rgba("+Math.round(h.r+c.r*e)+","+Math.round(h.g+c.g*e)+","+Math.round(h.b+c.b*e)+","+(h.a+c.a*e)+")":f=h+c*e;r.setAttr(s,f)}}_addListeners(){this.tween.onPlay=()=>{this.anim.start()},this.tween.onReverse=()=>{this.anim.start()},this.tween.onPause=()=>{this.anim.stop()},this.tween.onFinish=()=>{var e=this.node,r=Ie.attrs[e._id][this._id];r.points&&r.points.trueEnd&&e.setAttr("points",r.points.trueEnd),this.onFinish&&this.onFinish.call(this)},this.tween.onReset=()=>{var e=this.node,r=Ie.attrs[e._id][this._id];r.points&&r.points.trueStart&&e.points(r.points.trueStart),this.onReset&&this.onReset()},this.tween.onUpdate=()=>{this.onUpdate&&this.onUpdate.call(this)}}play(){return this.tween.play(),this}reverse(){return this.tween.reverse(),this}reset(){return this.tween.reset(),this}seek(e){return this.tween.seek(e*1e3),this}pause(){return this.tween.pause(),this}finish(){return this.tween.finish(),this}destroy(){var e=this.node._id,r=this._id,i=Ie.tweens[e],s;this.pause();for(s in i)delete Ie.tweens[e][s];delete Ie.attrs[e][r]}}Ie.attrs={};Ie.tweens={};W.prototype.to=function(o){var e=o.onFinish;o.node=this,o.onFinish=function(){this.destroy(),e&&e()};var r=new Ie(o);r.play()};const Lr={BackEaseIn(o,e,r,i){var s=1.70158;return r*(o/=i)*o*((s+1)*o-s)+e},BackEaseOut(o,e,r,i){var s=1.70158;return r*((o=o/i-1)*o*((s+1)*o+s)+1)+e},BackEaseInOut(o,e,r,i){var s=1.70158;return(o/=i/2)<1?r/2*(o*o*(((s*=1.525)+1)*o-s))+e:r/2*((o-=2)*o*(((s*=1.525)+1)*o+s)+2)+e},ElasticEaseIn(o,e,r,i,s,l){var h=0;return o===0?e:(o/=i)===1?e+r:(l||(l=i*.3),!s||s<Math.abs(r)?(s=r,h=l/4):h=l/(2*Math.PI)*Math.asin(r/s),-(s*Math.pow(2,10*(o-=1))*Math.sin((o*i-h)*(2*Math.PI)/l))+e)},ElasticEaseOut(o,e,r,i,s,l){var h=0;return o===0?e:(o/=i)===1?e+r:(l||(l=i*.3),!s||s<Math.abs(r)?(s=r,h=l/4):h=l/(2*Math.PI)*Math.asin(r/s),s*Math.pow(2,-10*o)*Math.sin((o*i-h)*(2*Math.PI)/l)+r+e)},ElasticEaseInOut(o,e,r,i,s,l){var h=0;return o===0?e:(o/=i/2)===2?e+r:(l||(l=i*(.3*1.5)),!s||s<Math.abs(r)?(s=r,h=l/4):h=l/(2*Math.PI)*Math.asin(r/s),o<1?-.5*(s*Math.pow(2,10*(o-=1))*Math.sin((o*i-h)*(2*Math.PI)/l))+e:s*Math.pow(2,-10*(o-=1))*Math.sin((o*i-h)*(2*Math.PI)/l)*.5+r+e)},BounceEaseOut(o,e,r,i){return(o/=i)<1/2.75?r*(7.5625*o*o)+e:o<2/2.75?r*(7.5625*(o-=1.5/2.75)*o+.75)+e:o<2.5/2.75?r*(7.5625*(o-=2.25/2.75)*o+.9375)+e:r*(7.5625*(o-=2.625/2.75)*o+.984375)+e},BounceEaseIn(o,e,r,i){return r-Lr.BounceEaseOut(i-o,0,r,i)+e},BounceEaseInOut(o,e,r,i){return o<i/2?Lr.BounceEaseIn(o*2,0,r,i)*.5+e:Lr.BounceEaseOut(o*2-i,0,r,i)*.5+r*.5+e},EaseIn(o,e,r,i){return r*(o/=i)*o+e},EaseOut(o,e,r,i){return-r*(o/=i)*(o-2)+e},EaseInOut(o,e,r,i){return(o/=i/2)<1?r/2*o*o+e:-r/2*(--o*(o-2)-1)+e},StrongEaseIn(o,e,r,i){return r*(o/=i)*o*o*o*o+e},StrongEaseOut(o,e,r,i){return r*((o=o/i-1)*o*o*o*o+1)+e},StrongEaseInOut(o,e,r,i){return(o/=i/2)<1?r/2*o*o*o*o*o+e:r/2*((o-=2)*o*o*o*o+2)+e},Linear(o,e,r,i){return r*o/i+e}},Pn=k._assign(J,{Util:k,Transform:st,Node:W,Container:ot,Stage:Yi,stages:Hi,Layer:kn,FastLayer:Ds,Group:Kn,DD:ce,Shape:z,shapes:Ar,Animation:mt,Tween:Ie,Easings:Lr,Context:Ui,Canvas:Xi});class qt extends z{_sceneFunc(e){var r=J.getAngle(this.angle()),i=this.clockwise();e.beginPath(),e.arc(0,0,this.outerRadius(),0,r,i),e.arc(0,0,this.innerRadius(),r,0,!i),e.closePath(),e.fillStrokeShape(this)}getWidth(){return this.outerRadius()*2}getHeight(){return this.outerRadius()*2}setWidth(e){this.outerRadius(e/2)}setHeight(e){this.outerRadius(e/2)}getSelfRect(){const e=this.innerRadius(),r=this.outerRadius(),i=this.clockwise(),s=J.getAngle(i?360-this.angle():this.angle()),l=Math.cos(Math.min(s,Math.PI)),h=1,c=Math.sin(Math.min(Math.max(Math.PI,s),3*Math.PI/2)),f=Math.sin(Math.min(s,Math.PI/2)),p=l*(l>0?e:r),v=h*r,_=c*(c>0?e:r),y=f*(f>0?r:e);return{x:p,y:i?-1*y:_,width:v-p,height:y-_}}}qt.prototype._centroid=!0;qt.prototype.className="Arc";qt.prototype._attrsAffectingSize=["innerRadius","outerRadius"];Re(qt);w.addGetterSetter(qt,"innerRadius",0,U());w.addGetterSetter(qt,"outerRadius",0,U());w.addGetterSetter(qt,"angle",0,U());w.addGetterSetter(qt,"clockwise",!1,Mt());function ks(o,e,r,i,s,l,h){var c=Math.sqrt(Math.pow(r-o,2)+Math.pow(i-e,2)),f=Math.sqrt(Math.pow(s-r,2)+Math.pow(l-i,2)),p=h*c/(c+f),v=h*f/(c+f),_=r-p*(s-o),y=i-p*(l-e),C=r+v*(s-o),S=i+v*(l-e);return[_,y,C,S]}function Yl(o,e){var r=o.length,i=[],s,l;for(s=2;s<r-2;s+=2)l=ks(o[s-2],o[s-1],o[s],o[s+1],o[s+2],o[s+3],e),!isNaN(l[0])&&(i.push(l[0]),i.push(l[1]),i.push(o[s]),i.push(o[s+1]),i.push(l[2]),i.push(l[3]));return i}let Qt=class extends z{constructor(e){super(e),this.on("pointsChange.konva tensionChange.konva closedChange.konva bezierChange.konva",function(){this._clearCache("tensionPoints")})}_sceneFunc(e){var r=this.points(),i=r.length,s=this.tension(),l=this.closed(),h=this.bezier(),c,f,p;if(i){if(e.beginPath(),e.moveTo(r[0],r[1]),s!==0&&i>4){for(c=this.getTensionPoints(),f=c.length,p=l?0:4,l||e.quadraticCurveTo(c[0],c[1],c[2],c[3]);p<f-2;)e.bezierCurveTo(c[p++],c[p++],c[p++],c[p++],c[p++],c[p++]);l||e.quadraticCurveTo(c[f-2],c[f-1],r[i-2],r[i-1])}else if(h)for(p=2;p<i;)e.bezierCurveTo(r[p++],r[p++],r[p++],r[p++],r[p++],r[p++]);else for(p=2;p<i;p+=2)e.lineTo(r[p],r[p+1]);l?(e.closePath(),e.fillStrokeShape(this)):e.strokeShape(this)}}getTensionPoints(){return this._getCache("tensionPoints",this._getTensionPoints)}_getTensionPoints(){return this.closed()?this._getTensionPointsClosed():Yl(this.points(),this.tension())}_getTensionPointsClosed(){var e=this.points(),r=e.length,i=this.tension(),s=ks(e[r-2],e[r-1],e[0],e[1],e[2],e[3],i),l=ks(e[r-4],e[r-3],e[r-2],e[r-1],e[0],e[1],i),h=Yl(e,i),c=[s[2],s[3]].concat(h).concat([l[0],l[1],e[r-2],e[r-1],l[2],l[3],s[0],s[1],e[0],e[1]]);return c}getWidth(){return this.getSelfRect().width}getHeight(){return this.getSelfRect().height}getSelfRect(){var e=this.points();if(e.length<4)return{x:e[0]||0,y:e[1]||0,width:0,height:0};this.tension()!==0?e=[e[0],e[1],...this._getTensionPoints(),e[e.length-2],e[e.length-1]]:e=this.points();for(var r=e[0],i=e[0],s=e[1],l=e[1],h,c,f=0;f<e.length/2;f++)h=e[f*2],c=e[f*2+1],r=Math.min(r,h),i=Math.max(i,h),s=Math.min(s,c),l=Math.max(l,c);return{x:r,y:s,width:i-r,height:l-s}}};Qt.prototype.className="Line";Qt.prototype._attrsAffectingSize=["points","bezier","tension"];Re(Qt);w.addGetterSetter(Qt,"closed",!1);w.addGetterSetter(Qt,"bezier",!1);w.addGetterSetter(Qt,"tension",0,U());w.addGetterSetter(Qt,"points",[],ac());class me extends z{constructor(e){super(e),this.dataArray=[],this.pathLength=0,this.dataArray=me.parsePathData(this.data()),this.pathLength=0;for(var r=0;r<this.dataArray.length;++r)this.pathLength+=this.dataArray[r].pathLength;this.on("dataChange.konva",function(){this.dataArray=me.parsePathData(this.data()),this.pathLength=0;for(var i=0;i<this.dataArray.length;++i)this.pathLength+=this.dataArray[i].pathLength})}_sceneFunc(e){var r=this.dataArray;e.beginPath();for(var i=!1,s=0;s<r.length;s++){var l=r[s].command,h=r[s].points;switch(l){case"L":e.lineTo(h[0],h[1]);break;case"M":e.moveTo(h[0],h[1]);break;case"C":e.bezierCurveTo(h[0],h[1],h[2],h[3],h[4],h[5]);break;case"Q":e.quadraticCurveTo(h[0],h[1],h[2],h[3]);break;case"A":var c=h[0],f=h[1],p=h[2],v=h[3],_=h[4],y=h[5],C=h[6],S=h[7],M=p>v?p:v,T=p>v?1:p/v,G=p>v?v/p:1;e.translate(c,f),e.rotate(C),e.scale(T,G),e.arc(0,0,M,_,_+y,1-S),e.scale(1/T,1/G),e.rotate(-C),e.translate(-c,-f);break;case"z":i=!0,e.closePath();break}}!i&&!this.hasFill()?e.strokeShape(this):e.fillStrokeShape(this)}getSelfRect(){var e=[];this.dataArray.forEach(function(p){if(p.command==="A"){var v=p.points[4],_=p.points[5],y=p.points[4]+_,C=Math.PI/180;if(Math.abs(v-y)<C&&(C=Math.abs(v-y)),_<0)for(let S=v-C;S>y;S-=C){const M=me.getPointOnEllipticalArc(p.points[0],p.points[1],p.points[2],p.points[3],S,0);e.push(M.x,M.y)}else for(let S=v+C;S<y;S+=C){const M=me.getPointOnEllipticalArc(p.points[0],p.points[1],p.points[2],p.points[3],S,0);e.push(M.x,M.y)}}else if(p.command==="C")for(let S=0;S<=1;S+=.01){const M=me.getPointOnCubicBezier(S,p.start.x,p.start.y,p.points[0],p.points[1],p.points[2],p.points[3],p.points[4],p.points[5]);e.push(M.x,M.y)}else e=e.concat(p.points)});for(var r=e[0],i=e[0],s=e[1],l=e[1],h,c,f=0;f<e.length/2;f++)h=e[f*2],c=e[f*2+1],isNaN(h)||(r=Math.min(r,h),i=Math.max(i,h)),isNaN(c)||(s=Math.min(s,c),l=Math.max(l,c));return{x:r,y:s,width:i-r,height:l-s}}getLength(){return this.pathLength}getPointAtLength(e){var r,i=0,s=this.dataArray.length;if(!s)return null;for(;i<s&&e>this.dataArray[i].pathLength;)e-=this.dataArray[i].pathLength,++i;if(i===s)return r=this.dataArray[i-1].points.slice(-2),{x:r[0],y:r[1]};if(e<.01)return r=this.dataArray[i].points.slice(0,2),{x:r[0],y:r[1]};var l=this.dataArray[i],h=l.points;switch(l.command){case"L":return me.getPointOnLine(e,l.start.x,l.start.y,h[0],h[1]);case"C":return me.getPointOnCubicBezier(e/l.pathLength,l.start.x,l.start.y,h[0],h[1],h[2],h[3],h[4],h[5]);case"Q":return me.getPointOnQuadraticBezier(e/l.pathLength,l.start.x,l.start.y,h[0],h[1],h[2],h[3]);case"A":var c=h[0],f=h[1],p=h[2],v=h[3],_=h[4],y=h[5],C=h[6];return _+=y*e/l.pathLength,me.getPointOnEllipticalArc(c,f,p,v,_,C)}return null}static getLineLength(e,r,i,s){return Math.sqrt((i-e)*(i-e)+(s-r)*(s-r))}static getPointOnLine(e,r,i,s,l,h,c){h===void 0&&(h=r),c===void 0&&(c=i);var f=(l-i)/(s-r+1e-8),p=Math.sqrt(e*e/(1+f*f));s<r&&(p*=-1);var v=f*p,_;if(s===r)_={x:h,y:c+v};else if((c-i)/(h-r+1e-8)===f)_={x:h+p,y:c+v};else{var y,C,S=this.getLineLength(r,i,s,l),M=(h-r)*(s-r)+(c-i)*(l-i);M=M/(S*S),y=r+M*(s-r),C=i+M*(l-i);var T=this.getLineLength(h,c,y,C),G=Math.sqrt(e*e-T*T);p=Math.sqrt(G*G/(1+f*f)),s<r&&(p*=-1),v=f*p,_={x:y+p,y:C+v}}return _}static getPointOnCubicBezier(e,r,i,s,l,h,c,f,p){function v(T){return T*T*T}function _(T){return 3*T*T*(1-T)}function y(T){return 3*T*(1-T)*(1-T)}function C(T){return(1-T)*(1-T)*(1-T)}var S=f*v(e)+h*_(e)+s*y(e)+r*C(e),M=p*v(e)+c*_(e)+l*y(e)+i*C(e);return{x:S,y:M}}static getPointOnQuadraticBezier(e,r,i,s,l,h,c){function f(C){return C*C}function p(C){return 2*C*(1-C)}function v(C){return(1-C)*(1-C)}var _=h*f(e)+s*p(e)+r*v(e),y=c*f(e)+l*p(e)+i*v(e);return{x:_,y}}static getPointOnEllipticalArc(e,r,i,s,l,h){var c=Math.cos(h),f=Math.sin(h),p={x:i*Math.cos(l),y:s*Math.sin(l)};return{x:e+(p.x*c-p.y*f),y:r+(p.x*f+p.y*c)}}static parsePathData(e){if(!e)return[];var r=e,i=["m","M","l","L","v","V","h","H","z","Z","c","C","q","Q","t","T","s","S","a","A"];r=r.replace(new RegExp(" ","g"),",");for(var s=0;s<i.length;s++)r=r.replace(new RegExp(i[s],"g"),"|"+i[s]);var l=r.split("|"),h=[],c=[],f=0,p=0,v=/([-+]?((\d+\.\d+)|((\d+)|(\.\d+)))(?:e[-+]?\d+)?)/gi,_;for(s=1;s<l.length;s++){var y=l[s],C=y.charAt(0);for(y=y.slice(1),c.length=0;_=v.exec(y);)c.push(_[0]);for(var S=[],M=0,T=c.length;M<T;M++){if(c[M]==="00"){S.push(0,0);continue}var G=parseFloat(c[M]);isNaN(G)?S.push(0):S.push(G)}for(;S.length>0&&!isNaN(S[0]);){var D=null,I=[],E=f,F=p,O,X,B,Y,j,N,V,b,te,ne;switch(C){case"l":f+=S.shift(),p+=S.shift(),D="L",I.push(f,p);break;case"L":f=S.shift(),p=S.shift(),I.push(f,p);break;case"m":var $=S.shift(),le=S.shift();if(f+=$,p+=le,D="M",h.length>2&&h[h.length-1].command==="z"){for(var ge=h.length-2;ge>=0;ge--)if(h[ge].command==="M"){f=h[ge].points[0]+$,p=h[ge].points[1]+le;break}}I.push(f,p),C="l";break;case"M":f=S.shift(),p=S.shift(),D="M",I.push(f,p),C="L";break;case"h":f+=S.shift(),D="L",I.push(f,p);break;case"H":f=S.shift(),D="L",I.push(f,p);break;case"v":p+=S.shift(),D="L",I.push(f,p);break;case"V":p=S.shift(),D="L",I.push(f,p);break;case"C":I.push(S.shift(),S.shift(),S.shift(),S.shift()),f=S.shift(),p=S.shift(),I.push(f,p);break;case"c":I.push(f+S.shift(),p+S.shift(),f+S.shift(),p+S.shift()),f+=S.shift(),p+=S.shift(),D="C",I.push(f,p);break;case"S":X=f,B=p,O=h[h.length-1],O.command==="C"&&(X=f+(f-O.points[2]),B=p+(p-O.points[3])),I.push(X,B,S.shift(),S.shift()),f=S.shift(),p=S.shift(),D="C",I.push(f,p);break;case"s":X=f,B=p,O=h[h.length-1],O.command==="C"&&(X=f+(f-O.points[2]),B=p+(p-O.points[3])),I.push(X,B,f+S.shift(),p+S.shift()),f+=S.shift(),p+=S.shift(),D="C",I.push(f,p);break;case"Q":I.push(S.shift(),S.shift()),f=S.shift(),p=S.shift(),I.push(f,p);break;case"q":I.push(f+S.shift(),p+S.shift()),f+=S.shift(),p+=S.shift(),D="Q",I.push(f,p);break;case"T":X=f,B=p,O=h[h.length-1],O.command==="Q"&&(X=f+(f-O.points[0]),B=p+(p-O.points[1])),f=S.shift(),p=S.shift(),D="Q",I.push(X,B,f,p);break;case"t":X=f,B=p,O=h[h.length-1],O.command==="Q"&&(X=f+(f-O.points[0]),B=p+(p-O.points[1])),f+=S.shift(),p+=S.shift(),D="Q",I.push(X,B,f,p);break;case"A":Y=S.shift(),j=S.shift(),N=S.shift(),V=S.shift(),b=S.shift(),te=f,ne=p,f=S.shift(),p=S.shift(),D="A",I=this.convertEndpointToCenterParameterization(te,ne,f,p,V,b,Y,j,N);break;case"a":Y=S.shift(),j=S.shift(),N=S.shift(),V=S.shift(),b=S.shift(),te=f,ne=p,f+=S.shift(),p+=S.shift(),D="A",I=this.convertEndpointToCenterParameterization(te,ne,f,p,V,b,Y,j,N);break}h.push({command:D||C,points:I,start:{x:E,y:F},pathLength:this.calcLength(E,F,D||C,I)})}(C==="z"||C==="Z")&&h.push({command:"z",points:[],start:void 0,pathLength:0})}return h}static calcLength(e,r,i,s){var l,h,c,f,p=me;switch(i){case"L":return p.getLineLength(e,r,s[0],s[1]);case"C":for(l=0,h=p.getPointOnCubicBezier(0,e,r,s[0],s[1],s[2],s[3],s[4],s[5]),f=.01;f<=1;f+=.01)c=p.getPointOnCubicBezier(f,e,r,s[0],s[1],s[2],s[3],s[4],s[5]),l+=p.getLineLength(h.x,h.y,c.x,c.y),h=c;return l;case"Q":for(l=0,h=p.getPointOnQuadraticBezier(0,e,r,s[0],s[1],s[2],s[3]),f=.01;f<=1;f+=.01)c=p.getPointOnQuadraticBezier(f,e,r,s[0],s[1],s[2],s[3]),l+=p.getLineLength(h.x,h.y,c.x,c.y),h=c;return l;case"A":l=0;var v=s[4],_=s[5],y=s[4]+_,C=Math.PI/180;if(Math.abs(v-y)<C&&(C=Math.abs(v-y)),h=p.getPointOnEllipticalArc(s[0],s[1],s[2],s[3],v,0),_<0)for(f=v-C;f>y;f-=C)c=p.getPointOnEllipticalArc(s[0],s[1],s[2],s[3],f,0),l+=p.getLineLength(h.x,h.y,c.x,c.y),h=c;else for(f=v+C;f<y;f+=C)c=p.getPointOnEllipticalArc(s[0],s[1],s[2],s[3],f,0),l+=p.getLineLength(h.x,h.y,c.x,c.y),h=c;return c=p.getPointOnEllipticalArc(s[0],s[1],s[2],s[3],y,0),l+=p.getLineLength(h.x,h.y,c.x,c.y),l}return 0}static convertEndpointToCenterParameterization(e,r,i,s,l,h,c,f,p){var v=p*(Math.PI/180),_=Math.cos(v)*(e-i)/2+Math.sin(v)*(r-s)/2,y=-1*Math.sin(v)*(e-i)/2+Math.cos(v)*(r-s)/2,C=_*_/(c*c)+y*y/(f*f);C>1&&(c*=Math.sqrt(C),f*=Math.sqrt(C));var S=Math.sqrt((c*c*(f*f)-c*c*(y*y)-f*f*(_*_))/(c*c*(y*y)+f*f*(_*_)));l===h&&(S*=-1),isNaN(S)&&(S=0);var M=S*c*y/f,T=S*-f*_/c,G=(e+i)/2+Math.cos(v)*M-Math.sin(v)*T,D=(r+s)/2+Math.sin(v)*M+Math.cos(v)*T,I=function(j){return Math.sqrt(j[0]*j[0]+j[1]*j[1])},E=function(j,N){return(j[0]*N[0]+j[1]*N[1])/(I(j)*I(N))},F=function(j,N){return(j[0]*N[1]<j[1]*N[0]?-1:1)*Math.acos(E(j,N))},O=F([1,0],[(_-M)/c,(y-T)/f]),X=[(_-M)/c,(y-T)/f],B=[(-1*_-M)/c,(-1*y-T)/f],Y=F(X,B);return E(X,B)<=-1&&(Y=Math.PI),E(X,B)>=1&&(Y=0),h===0&&Y>0&&(Y=Y-2*Math.PI),h===1&&Y<0&&(Y=Y+2*Math.PI),[G,D,c,f,O,Y,v,h]}}me.prototype.className="Path";me.prototype._attrsAffectingSize=["data"];Re(me);w.addGetterSetter(me,"data");class Tn extends Qt{_sceneFunc(e){super._sceneFunc(e);var r=Math.PI*2,i=this.points(),s=i,l=this.tension()!==0&&i.length>4;l&&(s=this.getTensionPoints());var h=this.pointerLength(),c=i.length,f,p;if(l){const y=[s[s.length-4],s[s.length-3],s[s.length-2],s[s.length-1],i[c-2],i[c-1]],C=me.calcLength(s[s.length-4],s[s.length-3],"C",y),S=me.getPointOnQuadraticBezier(Math.min(1,1-h/C),y[0],y[1],y[2],y[3],y[4],y[5]);f=i[c-2]-S.x,p=i[c-1]-S.y}else f=i[c-2]-i[c-4],p=i[c-1]-i[c-3];var v=(Math.atan2(p,f)+r)%r,_=this.pointerWidth();this.pointerAtEnding()&&(e.save(),e.beginPath(),e.translate(i[c-2],i[c-1]),e.rotate(v),e.moveTo(0,0),e.lineTo(-h,_/2),e.lineTo(-h,-_/2),e.closePath(),e.restore(),this.__fillStroke(e)),this.pointerAtBeginning()&&(e.save(),e.beginPath(),e.translate(i[0],i[1]),l?(f=(s[0]+s[2])/2-i[0],p=(s[1]+s[3])/2-i[1]):(f=i[2]-i[0],p=i[3]-i[1]),e.rotate((Math.atan2(-p,-f)+r)%r),e.moveTo(0,0),e.lineTo(-h,_/2),e.lineTo(-h,-_/2),e.closePath(),e.restore(),this.__fillStroke(e))}__fillStroke(e){var r=this.dashEnabled();r&&(this.attrs.dashEnabled=!1,e.setLineDash([])),e.fillStrokeShape(this),r&&(this.attrs.dashEnabled=!0)}getSelfRect(){const e=super.getSelfRect(),r=this.pointerWidth()/2;return{x:e.x-r,y:e.y-r,width:e.width+r*2,height:e.height+r*2}}}Tn.prototype.className="Arrow";Re(Tn);w.addGetterSetter(Tn,"pointerLength",10,U());w.addGetterSetter(Tn,"pointerWidth",10,U());w.addGetterSetter(Tn,"pointerAtBeginning",!1);w.addGetterSetter(Tn,"pointerAtEnding",!0);class Zn extends z{_sceneFunc(e){e.beginPath(),e.arc(0,0,this.attrs.radius||0,0,Math.PI*2,!1),e.closePath(),e.fillStrokeShape(this)}getWidth(){return this.radius()*2}getHeight(){return this.radius()*2}setWidth(e){this.radius()!==e/2&&this.radius(e/2)}setHeight(e){this.radius()!==e/2&&this.radius(e/2)}}Zn.prototype._centroid=!0;Zn.prototype.className="Circle";Zn.prototype._attrsAffectingSize=["radius"];Re(Zn);w.addGetterSetter(Zn,"radius",0,U());let dn=class extends z{_sceneFunc(e){var r=this.radiusX(),i=this.radiusY();e.beginPath(),e.save(),r!==i&&e.scale(1,i/r),e.arc(0,0,r,0,Math.PI*2,!1),e.restore(),e.closePath(),e.fillStrokeShape(this)}getWidth(){return this.radiusX()*2}getHeight(){return this.radiusY()*2}setWidth(e){this.radiusX(e/2)}setHeight(e){this.radiusY(e/2)}};dn.prototype.className="Ellipse";dn.prototype._centroid=!0;dn.prototype._attrsAffectingSize=["radiusX","radiusY"];Re(dn);w.addComponentsGetterSetter(dn,"radius",["x","y"]);w.addGetterSetter(dn,"radiusX",0,U());w.addGetterSetter(dn,"radiusY",0,U());class yt extends z{constructor(e){super(e),this.on("imageChange.konva",()=>{this._setImageLoad()}),this._setImageLoad()}_setImageLoad(){const e=this.image();e&&e.complete||e&&e.readyState===4||e&&e.addEventListener&&e.addEventListener("load",()=>{this._requestDraw()})}_useBufferCanvas(){return super._useBufferCanvas(!0)}_sceneFunc(e){const r=this.getWidth(),i=this.getHeight(),s=this.cornerRadius(),l=this.attrs.image;let h;if(l){const c=this.attrs.cropWidth,f=this.attrs.cropHeight;c&&f?h=[l,this.cropX(),this.cropY(),c,f,0,0,r,i]:h=[l,0,0,r,i]}(this.hasFill()||this.hasStroke()||s)&&(e.beginPath(),s?k.drawRoundedRectPath(e,r,i,s):e.rect(0,0,r,i),e.closePath(),e.fillStrokeShape(this)),l&&(s&&e.clip(),e.drawImage.apply(e,h))}_hitFunc(e){var r=this.width(),i=this.height(),s=this.cornerRadius();e.beginPath(),s?k.drawRoundedRectPath(e,r,i,s):e.rect(0,0,r,i),e.closePath(),e.fillStrokeShape(this)}getWidth(){var e,r;return(e=this.attrs.width)!==null&&e!==void 0?e:(r=this.image())===null||r===void 0?void 0:r.width}getHeight(){var e,r;return(e=this.attrs.height)!==null&&e!==void 0?e:(r=this.image())===null||r===void 0?void 0:r.height}static fromURL(e,r,i=null){var s=k.createImageElement();s.onload=function(){var l=new yt({image:s});r(l)},s.onerror=i,s.crossOrigin="Anonymous",s.src=e}}yt.prototype.className="Image";Re(yt);w.addGetterSetter(yt,"cornerRadius",0,Ls(4));w.addGetterSetter(yt,"image");w.addComponentsGetterSetter(yt,"crop",["x","y","width","height"]);w.addGetterSetter(yt,"cropX",0,U());w.addGetterSetter(yt,"cropY",0,U());w.addGetterSetter(yt,"cropWidth",0,U());w.addGetterSetter(yt,"cropHeight",0,U());var bu=["fontFamily","fontSize","fontStyle","padding","lineHeight","text","width","height","pointerDirection","pointerWidth","pointerHeight"],Kc="Change.konva",Jc="none",Ts="up",Ms="right",Rs="down",As="left",Zc=bu.length;class Gs extends Kn{constructor(e){super(e),this.on("add.konva",function(r){this._addListeners(r.child),this._sync()})}getText(){return this.find("Text")[0]}getTag(){return this.find("Tag")[0]}_addListeners(e){var r=this,i,s=function(){r._sync()};for(i=0;i<Zc;i++)e.on(bu[i]+Kc,s)}getWidth(){return this.getText().width()}getHeight(){return this.getText().height()}_sync(){var e=this.getText(),r=this.getTag(),i,s,l,h,c,f,p;if(e&&r){switch(i=e.width(),s=e.height(),l=r.pointerDirection(),h=r.pointerWidth(),p=r.pointerHeight(),c=0,f=0,l){case Ts:c=i/2,f=-1*p;break;case Ms:c=i+h,f=s/2;break;case Rs:c=i/2,f=s+p;break;case As:c=-1*h,f=s/2;break}r.setAttrs({x:-1*c,y:-1*f,width:i,height:s}),e.setAttrs({x:-1*c,y:-1*f})}}}Gs.prototype.className="Label";Re(Gs);class Mn extends z{_sceneFunc(e){var r=this.width(),i=this.height(),s=this.pointerDirection(),l=this.pointerWidth(),h=this.pointerHeight(),c=this.cornerRadius();let f=0,p=0,v=0,_=0;typeof c=="number"?f=p=v=_=Math.min(c,r/2,i/2):(f=Math.min(c[0]||0,r/2,i/2),p=Math.min(c[1]||0,r/2,i/2),_=Math.min(c[2]||0,r/2,i/2),v=Math.min(c[3]||0,r/2,i/2)),e.beginPath(),e.moveTo(f,0),s===Ts&&(e.lineTo((r-l)/2,0),e.lineTo(r/2,-1*h),e.lineTo((r+l)/2,0)),e.lineTo(r-p,0),e.arc(r-p,p,p,Math.PI*3/2,0,!1),s===Ms&&(e.lineTo(r,(i-h)/2),e.lineTo(r+l,i/2),e.lineTo(r,(i+h)/2)),e.lineTo(r,i-_),e.arc(r-_,i-_,_,0,Math.PI/2,!1),s===Rs&&(e.lineTo((r+l)/2,i),e.lineTo(r/2,i+h),e.lineTo((r-l)/2,i)),e.lineTo(v,i),e.arc(v,i-v,v,Math.PI/2,Math.PI,!1),s===As&&(e.lineTo(0,(i+h)/2),e.lineTo(-1*l,i/2),e.lineTo(0,(i-h)/2)),e.lineTo(0,f),e.arc(f,f,f,Math.PI,Math.PI*3/2,!1),e.closePath(),e.fillStrokeShape(this)}getSelfRect(){var e=0,r=0,i=this.pointerWidth(),s=this.pointerHeight(),l=this.pointerDirection(),h=this.width(),c=this.height();return l===Ts?(r-=s,c+=s):l===Rs?c+=s:l===As?(e-=i*1.5,h+=i):l===Ms&&(h+=i*1.5),{x:e,y:r,width:h,height:c}}}Mn.prototype.className="Tag";Re(Mn);w.addGetterSetter(Mn,"pointerDirection",Jc);w.addGetterSetter(Mn,"pointerWidth",0,U());w.addGetterSetter(Mn,"pointerHeight",0,U());w.addGetterSetter(Mn,"cornerRadius",0,Ls(4));let Ir=class extends z{_sceneFunc(e){var r=this.cornerRadius(),i=this.width(),s=this.height();e.beginPath(),r?k.drawRoundedRectPath(e,i,s,r):e.rect(0,0,i,s),e.closePath(),e.fillStrokeShape(this)}};Ir.prototype.className="Rect";Re(Ir);w.addGetterSetter(Ir,"cornerRadius",0,Ls(4));let Rn=class extends z{_sceneFunc(e){const r=this._getPoints();e.beginPath(),e.moveTo(r[0].x,r[0].y);for(var i=1;i<r.length;i++)e.lineTo(r[i].x,r[i].y);e.closePath(),e.fillStrokeShape(this)}_getPoints(){const e=this.attrs.sides,r=this.attrs.radius||0,i=[];for(var s=0;s<e;s++)i.push({x:r*Math.sin(s*2*Math.PI/e),y:-1*r*Math.cos(s*2*Math.PI/e)});return i}getSelfRect(){const e=this._getPoints();var r=e[0].x,i=e[0].y,s=e[0].x,l=e[0].y;return e.forEach(h=>{r=Math.min(r,h.x),i=Math.max(i,h.x),s=Math.min(s,h.y),l=Math.max(l,h.y)}),{x:r,y:s,width:i-r,height:l-s}}getWidth(){return this.radius()*2}getHeight(){return this.radius()*2}setWidth(e){this.radius(e/2)}setHeight(e){this.radius(e/2)}};Rn.prototype.className="RegularPolygon";Rn.prototype._centroid=!0;Rn.prototype._attrsAffectingSize=["radius"];Re(Rn);w.addGetterSetter(Rn,"radius",0,U());w.addGetterSetter(Rn,"sides",0,U());var jl=Math.PI*2;class An extends z{_sceneFunc(e){e.beginPath(),e.arc(0,0,this.innerRadius(),0,jl,!1),e.moveTo(this.outerRadius(),0),e.arc(0,0,this.outerRadius(),jl,0,!0),e.closePath(),e.fillStrokeShape(this)}getWidth(){return this.outerRadius()*2}getHeight(){return this.outerRadius()*2}setWidth(e){this.outerRadius(e/2)}setHeight(e){this.outerRadius(e/2)}}An.prototype.className="Ring";An.prototype._centroid=!0;An.prototype._attrsAffectingSize=["innerRadius","outerRadius"];Re(An);w.addGetterSetter(An,"innerRadius",0,U());w.addGetterSetter(An,"outerRadius",0,U());class zt extends z{constructor(e){super(e),this._updated=!0,this.anim=new mt(()=>{var r=this._updated;return this._updated=!1,r}),this.on("animationChange.konva",function(){this.frameIndex(0)}),this.on("frameIndexChange.konva",function(){this._updated=!0}),this.on("frameRateChange.konva",function(){this.anim.isRunning()&&(clearInterval(this.interval),this._setInterval())})}_sceneFunc(e){var r=this.animation(),i=this.frameIndex(),s=i*4,l=this.animations()[r],h=this.frameOffsets(),c=l[s+0],f=l[s+1],p=l[s+2],v=l[s+3],_=this.image();if((this.hasFill()||this.hasStroke())&&(e.beginPath(),e.rect(0,0,p,v),e.closePath(),e.fillStrokeShape(this)),_)if(h){var y=h[r],C=i*2;e.drawImage(_,c,f,p,v,y[C+0],y[C+1],p,v)}else e.drawImage(_,c,f,p,v,0,0,p,v)}_hitFunc(e){var r=this.animation(),i=this.frameIndex(),s=i*4,l=this.animations()[r],h=this.frameOffsets(),c=l[s+2],f=l[s+3];if(e.beginPath(),h){var p=h[r],v=i*2;e.rect(p[v+0],p[v+1],c,f)}else e.rect(0,0,c,f);e.closePath(),e.fillShape(this)}_useBufferCanvas(){return super._useBufferCanvas(!0)}_setInterval(){var e=this;this.interval=setInterval(function(){e._updateIndex()},1e3/this.frameRate())}start(){if(!this.isRunning()){var e=this.getLayer();this.anim.setLayers(e),this._setInterval(),this.anim.start()}}stop(){this.anim.stop(),clearInterval(this.interval)}isRunning(){return this.anim.isRunning()}_updateIndex(){var e=this.frameIndex(),r=this.animation(),i=this.animations(),s=i[r],l=s.length/4;e<l-1?this.frameIndex(e+1):this.frameIndex(0)}}zt.prototype.className="Sprite";Re(zt);w.addGetterSetter(zt,"animation");w.addGetterSetter(zt,"animations");w.addGetterSetter(zt,"frameOffsets");w.addGetterSetter(zt,"image");w.addGetterSetter(zt,"frameIndex",0,U());w.addGetterSetter(zt,"frameRate",17,U());w.backCompat(zt,{index:"frameIndex",getIndex:"getFrameIndex",setIndex:"setFrameIndex"});class cn extends z{_sceneFunc(e){var r=this.innerRadius(),i=this.outerRadius(),s=this.numPoints();e.beginPath(),e.moveTo(0,0-i);for(var l=1;l<s*2;l++){var h=l%2===0?i:r,c=h*Math.sin(l*Math.PI/s),f=-1*h*Math.cos(l*Math.PI/s);e.lineTo(c,f)}e.closePath(),e.fillStrokeShape(this)}getWidth(){return this.outerRadius()*2}getHeight(){return this.outerRadius()*2}setWidth(e){this.outerRadius(e/2)}setHeight(e){this.outerRadius(e/2)}}cn.prototype.className="Star";cn.prototype._centroid=!0;cn.prototype._attrsAffectingSize=["innerRadius","outerRadius"];Re(cn);w.addGetterSetter(cn,"numPoints",5,U());w.addGetterSetter(cn,"innerRadius",0,U());w.addGetterSetter(cn,"outerRadius",0,U());function Fu(o){return Array.from(o)}var jn="auto",$c="center",Cr="justify",ef="Change.konva",tf="2d",Vl="-",Bu="left",nf="text",rf="Text",af="top",sf="bottom",ql="middle",Hu="normal",of="px ",Gi=" ",lf="right",uf="word",hf="char",Ql="none",ps="…",Wu=["fontFamily","fontSize","fontStyle","fontVariant","padding","align","verticalAlign","lineHeight","text","width","height","wrap","ellipsis","letterSpacing"],df=Wu.length;function cf(o){return o.split(",").map(e=>{e=e.trim();const r=e.indexOf(" ")>=0,i=e.indexOf('"')>=0||e.indexOf("'")>=0;return r&&!i&&(e=`"${e}"`),e}).join(", ")}var zi;function vs(){return zi||(zi=k.createCanvasElement().getContext(tf),zi)}function ff(o){o.fillText(this._partialText,this._partialTextX,this._partialTextY)}function gf(o){o.strokeText(this._partialText,this._partialTextX,this._partialTextY)}function pf(o){return o=o||{},!o.fillLinearGradientColorStops&&!o.fillRadialGradientColorStops&&!o.fillPatternImage&&(o.fill=o.fill||"black"),o}let Me=class extends z{constructor(e){super(pf(e)),this._partialTextX=0,this._partialTextY=0;for(var r=0;r<df;r++)this.on(Wu[r]+ef,this._setTextData);this._setTextData()}_sceneFunc(e){var r=this.textArr,i=r.length;if(this.text()){var s=this.padding(),l=this.fontSize(),h=this.lineHeight()*l,c=this.verticalAlign(),f=0,p=this.align(),v=this.getWidth(),_=this.letterSpacing(),y=this.fill(),C=this.textDecoration(),S=C.indexOf("underline")!==-1,M=C.indexOf("line-through")!==-1,T,G=0,G=h/2,D=0,I=0;for(e.setAttr("font",this._getContextFont()),e.setAttr("textBaseline",ql),e.setAttr("textAlign",Bu),c===ql?f=(this.getHeight()-i*h-s*2)/2:c===sf&&(f=this.getHeight()-i*h-s*2),e.translate(s,f+s),T=0;T<i;T++){var D=0,I=0,E=r[T],F=E.text,O=E.width,X=E.lastInParagraph,B,Y,j;if(e.save(),p===lf?D+=v-O-s*2:p===$c&&(D+=(v-O-s*2)/2),S){e.save(),e.beginPath(),e.moveTo(D,G+I+Math.round(l/2)),B=F.split(" ").length-1,Y=B===0,j=p===Cr&&!X?v-s*2:O,e.lineTo(D+Math.round(j),G+I+Math.round(l/2)),e.lineWidth=l/15;const $=this._getLinearGradient();e.strokeStyle=$||y,e.stroke(),e.restore()}if(M){e.save(),e.beginPath(),e.moveTo(D,G+I),B=F.split(" ").length-1,Y=B===0,j=p===Cr&&X&&!Y?v-s*2:O,e.lineTo(D+Math.round(j),G+I),e.lineWidth=l/15;const $=this._getLinearGradient();e.strokeStyle=$||y,e.stroke(),e.restore()}if(_!==0||p===Cr){B=F.split(" ").length-1;for(var N=Fu(F),V=0;V<N.length;V++){var b=N[V];b===" "&&!X&&p===Cr&&(D+=(v-s*2-O)/B),this._partialTextX=D,this._partialTextY=G+I,this._partialText=b,e.fillStrokeShape(this),D+=this.measureSize(b).width+_}}else this._partialTextX=D,this._partialTextY=G+I,this._partialText=F,e.fillStrokeShape(this);e.restore(),i>1&&(G+=h)}}}_hitFunc(e){var r=this.getWidth(),i=this.getHeight();e.beginPath(),e.rect(0,0,r,i),e.closePath(),e.fillStrokeShape(this)}setText(e){var r=k._isString(e)?e:e==null?"":e+"";return this._setAttr(nf,r),this}getWidth(){var e=this.attrs.width===jn||this.attrs.width===void 0;return e?this.getTextWidth()+this.padding()*2:this.attrs.width}getHeight(){var e=this.attrs.height===jn||this.attrs.height===void 0;return e?this.fontSize()*this.textArr.length*this.lineHeight()+this.padding()*2:this.attrs.height}getTextWidth(){return this.textWidth}getTextHeight(){return k.warn("text.getTextHeight() method is deprecated. Use text.height() - for full height and text.fontSize() - for one line height."),this.textHeight}measureSize(e){var r=vs(),i=this.fontSize(),s;return r.save(),r.font=this._getContextFont(),s=r.measureText(e),r.restore(),{width:s.width,height:i}}_getContextFont(){return this.fontStyle()+Gi+this.fontVariant()+Gi+(this.fontSize()+of)+cf(this.fontFamily())}_addTextLine(e){this.align()===Cr&&(e=e.trim());var i=this._getTextWidth(e);return this.textArr.push({text:e,width:i,lastInParagraph:!1})}_getTextWidth(e){var r=this.letterSpacing(),i=e.length;return vs().measureText(e).width+(i?r*(i-1):0)}_setTextData(){var e=this.text().split(`
`),r=+this.fontSize(),i=0,s=this.lineHeight()*r,l=this.attrs.width,h=this.attrs.height,c=l!==jn&&l!==void 0,f=h!==jn&&h!==void 0,p=this.padding(),v=l-p*2,_=h-p*2,y=0,C=this.wrap(),S=C!==Ql,M=C!==hf&&S,T=this.ellipsis();this.textArr=[],vs().font=this._getContextFont();for(var G=T?this._getTextWidth(ps):0,D=0,I=e.length;D<I;++D){var E=e[D],F=this._getTextWidth(E);if(c&&F>v)for(;E.length>0;){for(var O=0,X=E.length,B="",Y=0;O<X;){var j=O+X>>>1,N=E.slice(0,j+1),V=this._getTextWidth(N)+G;V<=v?(O=j+1,B=N,Y=V):X=j}if(B){if(M){var b,te=E[B.length],ne=te===Gi||te===Vl;ne&&Y<=v?b=B.length:b=Math.max(B.lastIndexOf(Gi),B.lastIndexOf(Vl))+1,b>0&&(O=b,B=B.slice(0,O),Y=this._getTextWidth(B))}B=B.trimRight(),this._addTextLine(B),i=Math.max(i,Y),y+=s;var $=this._shouldHandleEllipsis(y);if($){this._tryToAddEllipsisToLastLine();break}if(E=E.slice(O),E=E.trimLeft(),E.length>0&&(F=this._getTextWidth(E),F<=v)){this._addTextLine(E),y+=s,i=Math.max(i,F);break}}else break}else this._addTextLine(E),y+=s,i=Math.max(i,F),this._shouldHandleEllipsis(y)&&D<I-1&&this._tryToAddEllipsisToLastLine();if(this.textArr[this.textArr.length-1]&&(this.textArr[this.textArr.length-1].lastInParagraph=!0),f&&y+s>_)break}this.textHeight=r,this.textWidth=i}_shouldHandleEllipsis(e){var r=+this.fontSize(),i=this.lineHeight()*r,s=this.attrs.height,l=s!==jn&&s!==void 0,h=this.padding(),c=s-h*2,f=this.wrap(),p=f!==Ql;return!p||l&&e+i>c}_tryToAddEllipsisToLastLine(){var e=this.attrs.width,r=e!==jn&&e!==void 0,i=this.padding(),s=e-i*2,l=this.ellipsis(),h=this.textArr[this.textArr.length-1];if(!(!h||!l)){if(r){var c=this._getTextWidth(h.text+ps)<s;c||(h.text=h.text.slice(0,h.text.length-3))}this.textArr.splice(this.textArr.length-1,1),this._addTextLine(h.text+ps)}}getStrokeScaleEnabled(){return!0}};Me.prototype._fillFunc=ff;Me.prototype._strokeFunc=gf;Me.prototype.className=rf;Me.prototype._attrsAffectingSize=["text","fontSize","padding","wrap","lineHeight","letterSpacing"];Re(Me);w.overWriteSetter(Me,"width",Ns());w.overWriteSetter(Me,"height",Ns());w.addGetterSetter(Me,"fontFamily","Arial");w.addGetterSetter(Me,"fontSize",12,U());w.addGetterSetter(Me,"fontStyle",Hu);w.addGetterSetter(Me,"fontVariant",Hu);w.addGetterSetter(Me,"padding",0,U());w.addGetterSetter(Me,"align",Bu);w.addGetterSetter(Me,"verticalAlign",af);w.addGetterSetter(Me,"lineHeight",1,U());w.addGetterSetter(Me,"wrap",uf);w.addGetterSetter(Me,"ellipsis",!1,Mt());w.addGetterSetter(Me,"letterSpacing",0,U());w.addGetterSetter(Me,"text","",Jn());w.addGetterSetter(Me,"textDecoration","");var vf="",Uu="normal";function Xu(o){o.fillText(this.partialText,0,0)}function Yu(o){o.strokeText(this.partialText,0,0)}class ze extends z{constructor(e){super(e),this.dummyCanvas=k.createCanvasElement(),this.dataArray=[],this.dataArray=me.parsePathData(this.attrs.data),this.on("dataChange.konva",function(){this.dataArray=me.parsePathData(this.attrs.data),this._setTextData()}),this.on("textChange.konva alignChange.konva letterSpacingChange.konva kerningFuncChange.konva fontSizeChange.konva fontFamilyChange.konva",this._setTextData),this._setTextData()}_sceneFunc(e){e.setAttr("font",this._getContextFont()),e.setAttr("textBaseline",this.textBaseline()),e.setAttr("textAlign","left"),e.save();var r=this.textDecoration(),i=this.fill(),s=this.fontSize(),l=this.glyphInfo;r==="underline"&&e.beginPath();for(var h=0;h<l.length;h++){e.save();var c=l[h].p0;e.translate(c.x,c.y),e.rotate(l[h].rotation),this.partialText=l[h].text,e.fillStrokeShape(this),r==="underline"&&(h===0&&e.moveTo(0,s/2+1),e.lineTo(s,s/2+1)),e.restore()}r==="underline"&&(e.strokeStyle=i,e.lineWidth=s/20,e.stroke()),e.restore()}_hitFunc(e){e.beginPath();var r=this.glyphInfo;if(r.length>=1){var i=r[0].p0;e.moveTo(i.x,i.y)}for(var s=0;s<r.length;s++){var l=r[s].p1;e.lineTo(l.x,l.y)}e.setAttr("lineWidth",this.fontSize()),e.setAttr("strokeStyle",this.colorKey),e.stroke()}getTextWidth(){return this.textWidth}getTextHeight(){return k.warn("text.getTextHeight() method is deprecated. Use text.height() - for full height and text.fontSize() - for one line height."),this.textHeight}setText(e){return Me.prototype.setText.call(this,e)}_getContextFont(){return Me.prototype._getContextFont.call(this)}_getTextSize(e){var r=this.dummyCanvas,i=r.getContext("2d");i.save(),i.font=this._getContextFont();var s=i.measureText(e);return i.restore(),{width:s.width,height:parseInt(this.attrs.fontSize,10)}}_setTextData(){var e=this,r=this._getTextSize(this.attrs.text),i=this.letterSpacing(),s=this.align(),l=this.kerningFunc();this.textWidth=r.width,this.textHeight=r.height;var h=Math.max(this.textWidth+((this.attrs.text||"").length-1)*i,0);this.glyphInfo=[];for(var c=0,f=0;f<e.dataArray.length;f++)e.dataArray[f].pathLength>0&&(c+=e.dataArray[f].pathLength);var p=0;s==="center"&&(p=Math.max(0,c/2-h/2)),s==="right"&&(p=Math.max(0,c-h));for(var v=Fu(this.text()),_=this.text().split(" ").length-1,y,C,S,M=-1,T=0,G=function(){T=0;for(var V=e.dataArray,b=M+1;b<V.length;b++){if(V[b].pathLength>0)return M=b,V[b];V[b].command==="M"&&(y={x:V[b].points[0],y:V[b].points[1]})}return{}},D=function(V){var b=e._getTextSize(V).width+i;V===" "&&s==="justify"&&(b+=(c-h)/_);var te=0,ne=0;for(C=void 0;Math.abs(b-te)/b>.01&&ne<20;){ne++;for(var $=te;S===void 0;)S=G(),S&&$+S.pathLength<b&&($+=S.pathLength,S=void 0);if(Object.keys(S).length===0||y===void 0)return;var le=!1;switch(S.command){case"L":me.getLineLength(y.x,y.y,S.points[0],S.points[1])>b?C=me.getPointOnLine(b,y.x,y.y,S.points[0],S.points[1],y.x,y.y):S=void 0;break;case"A":var ge=S.points[4],Le=S.points[5],re=S.points[4]+Le;T===0?T=ge+1e-8:b>te?T+=Math.PI/180*Le/Math.abs(Le):T-=Math.PI/360*Le/Math.abs(Le),(Le<0&&T<re||Le>=0&&T>re)&&(T=re,le=!0),C=me.getPointOnEllipticalArc(S.points[0],S.points[1],S.points[2],S.points[3],T,S.points[6]);break;case"C":T===0?b>S.pathLength?T=1e-8:T=b/S.pathLength:b>te?T+=(b-te)/S.pathLength/2:T=Math.max(T-(te-b)/S.pathLength/2,0),T>1&&(T=1,le=!0),C=me.getPointOnCubicBezier(T,S.start.x,S.start.y,S.points[0],S.points[1],S.points[2],S.points[3],S.points[4],S.points[5]);break;case"Q":T===0?T=b/S.pathLength:b>te?T+=(b-te)/S.pathLength:T-=(te-b)/S.pathLength,T>1&&(T=1,le=!0),C=me.getPointOnQuadraticBezier(T,S.start.x,S.start.y,S.points[0],S.points[1],S.points[2],S.points[3]);break}C!==void 0&&(te=me.getLineLength(y.x,y.y,C.x,C.y)),le&&(le=!1,S=void 0)}},I="C",E=e._getTextSize(I).width+i,F=p/E-1,O=0;O<F&&(D(I),!(y===void 0||C===void 0));O++)y=C;for(var X=0;X<v.length&&(D(v[X]),!(y===void 0||C===void 0));X++){var B=me.getLineLength(y.x,y.y,C.x,C.y),Y=0;if(l)try{Y=l(v[X-1],v[X])*this.fontSize()}catch{Y=0}y.x+=Y,C.x+=Y,this.textWidth+=Y;var j=me.getPointOnLine(Y+B/2,y.x,y.y,C.x,C.y),N=Math.atan2(C.y-y.y,C.x-y.x);this.glyphInfo.push({transposeX:j.x,transposeY:j.y,text:v[X],rotation:N,p0:y,p1:C}),y=C}}getSelfRect(){if(!this.glyphInfo.length)return{x:0,y:0,width:0,height:0};var e=[];this.glyphInfo.forEach(function(v){e.push(v.p0.x),e.push(v.p0.y),e.push(v.p1.x),e.push(v.p1.y)});for(var r=e[0]||0,i=e[0]||0,s=e[1]||0,l=e[1]||0,h,c,f=0;f<e.length/2;f++)h=e[f*2],c=e[f*2+1],r=Math.min(r,h),i=Math.max(i,h),s=Math.min(s,c),l=Math.max(l,c);var p=this.fontSize();return{x:r-p/2,y:s-p/2,width:i-r+p,height:l-s+p}}destroy(){return k.releaseCanvas(this.dummyCanvas),super.destroy()}}ze.prototype._fillFunc=Xu;ze.prototype._strokeFunc=Yu;ze.prototype._fillFuncHit=Xu;ze.prototype._strokeFuncHit=Yu;ze.prototype.className="TextPath";ze.prototype._attrsAffectingSize=["text","fontSize","data"];Re(ze);w.addGetterSetter(ze,"data");w.addGetterSetter(ze,"fontFamily","Arial");w.addGetterSetter(ze,"fontSize",12,U());w.addGetterSetter(ze,"fontStyle",Uu);w.addGetterSetter(ze,"align","left");w.addGetterSetter(ze,"letterSpacing",0,U());w.addGetterSetter(ze,"textBaseline","middle");w.addGetterSetter(ze,"fontVariant",Uu);w.addGetterSetter(ze,"text",vf);w.addGetterSetter(ze,"textDecoration",null);w.addGetterSetter(ze,"kerningFunc",null);var ju="tr-konva",mf=["resizeEnabledChange","rotateAnchorOffsetChange","rotateEnabledChange","enabledAnchorsChange","anchorSizeChange","borderEnabledChange","borderStrokeChange","borderStrokeWidthChange","borderDashChange","anchorStrokeChange","anchorStrokeWidthChange","anchorFillChange","anchorCornerRadiusChange","ignoreStrokeChange"].map(o=>o+`.${ju}`).join(" "),Kl="nodesRect",yf=["widthChange","heightChange","scaleXChange","scaleYChange","skewXChange","skewYChange","rotationChange","offsetXChange","offsetYChange","transformsEnabledChange","strokeWidthChange"],_f={"top-left":-45,"top-center":0,"top-right":45,"middle-right":-90,"middle-left":90,"bottom-left":-135,"bottom-center":180,"bottom-right":135};const Sf="ontouchstart"in J._global;function wf(o,e){if(o==="rotater")return"crosshair";e+=k.degToRad(_f[o]||0);var r=(k.radToDeg(e)%360+360)%360;return k._inRange(r,315+22.5,360)||k._inRange(r,0,22.5)?"ns-resize":k._inRange(r,45-22.5,45+22.5)?"nesw-resize":k._inRange(r,90-22.5,90+22.5)?"ew-resize":k._inRange(r,135-22.5,135+22.5)?"nwse-resize":k._inRange(r,180-22.5,180+22.5)?"ns-resize":k._inRange(r,225-22.5,225+22.5)?"nesw-resize":k._inRange(r,270-22.5,270+22.5)?"ew-resize":k._inRange(r,315-22.5,315+22.5)?"nwse-resize":(k.error("Transformer has unknown angle for cursor detection: "+r),"pointer")}var Wi=["top-left","top-center","top-right","middle-right","middle-left","bottom-left","bottom-center","bottom-right"];function Cf(o){return{x:o.x+o.width/2*Math.cos(o.rotation)+o.height/2*Math.sin(-o.rotation),y:o.y+o.height/2*Math.cos(o.rotation)+o.width/2*Math.sin(o.rotation)}}function Vu(o,e,r){const i=r.x+(o.x-r.x)*Math.cos(e)-(o.y-r.y)*Math.sin(e),s=r.y+(o.x-r.x)*Math.sin(e)+(o.y-r.y)*Math.cos(e);return Object.assign(Object.assign({},o),{rotation:o.rotation+e,x:i,y:s})}function xf(o,e){const r=Cf(o);return Vu(o,e,r)}function Ef(o,e,r){let i=e;for(let s=0;s<o.length;s++){const l=J.getAngle(o[s]),h=Math.abs(l-e)%(Math.PI*2);Math.min(h,Math.PI*2-h)<r&&(i=l)}return i}class fe extends Kn{constructor(e){super(e),this._transforming=!1,this._createElements(),this._handleMouseMove=this._handleMouseMove.bind(this),this._handleMouseUp=this._handleMouseUp.bind(this),this.update=this.update.bind(this),this.on(mf,this.update),this.getNode()&&this.update()}attachTo(e){return this.setNode(e),this}setNode(e){return k.warn("tr.setNode(shape), tr.node(shape) and tr.attachTo(shape) methods are deprecated. Please use tr.nodes(nodesArray) instead."),this.setNodes([e])}getNode(){return this._nodes&&this._nodes[0]}_getEventNamespace(){return ju+this._id}setNodes(e=[]){this._nodes&&this._nodes.length&&this.detach();const r=e.filter(s=>s.isAncestorOf(this)?(k.error("Konva.Transformer cannot be an a child of the node you are trying to attach"),!1):!0);this._nodes=e=r,e.length===1&&this.useSingleNodeRotation()?this.rotation(e[0].getAbsoluteRotation()):this.rotation(0),this._nodes.forEach(s=>{const l=()=>{this.nodes().length===1&&this.useSingleNodeRotation()&&this.rotation(this.nodes()[0].getAbsoluteRotation()),this._resetTransformCache(),!this._transforming&&!this.isDragging()&&this.update()},h=s._attrsAffectingSize.map(c=>c+"Change."+this._getEventNamespace()).join(" ");s.on(h,l),s.on(yf.map(c=>c+`.${this._getEventNamespace()}`).join(" "),l),s.on(`absoluteTransformChange.${this._getEventNamespace()}`,l),this._proxyDrag(s)}),this._resetTransformCache();var i=!!this.findOne(".top-left");return i&&this.update(),this}_proxyDrag(e){let r;e.on(`dragstart.${this._getEventNamespace()}`,i=>{r=e.getAbsolutePosition(),!this.isDragging()&&e!==this.findOne(".back")&&this.startDrag(i,!1)}),e.on(`dragmove.${this._getEventNamespace()}`,i=>{if(!r)return;const s=e.getAbsolutePosition(),l=s.x-r.x,h=s.y-r.y;this.nodes().forEach(c=>{if(c===e||c.isDragging())return;const f=c.getAbsolutePosition();c.setAbsolutePosition({x:f.x+l,y:f.y+h}),c.startDrag(i)}),r=null})}getNodes(){return this._nodes||[]}getActiveAnchor(){return this._movingAnchorName}detach(){this._nodes&&this._nodes.forEach(e=>{e.off("."+this._getEventNamespace())}),this._nodes=[],this._resetTransformCache()}_resetTransformCache(){this._clearCache(Kl),this._clearCache("transform"),this._clearSelfAndDescendantCache("absoluteTransform")}_getNodeRect(){return this._getCache(Kl,this.__getNodeRect)}__getNodeShape(e,r=this.rotation(),i){var s=e.getClientRect({skipTransform:!0,skipShadow:!0,skipStroke:this.ignoreStroke()}),l=e.getAbsoluteScale(i),h=e.getAbsolutePosition(i),c=s.x*l.x-e.offsetX()*l.x,f=s.y*l.y-e.offsetY()*l.y;const p=(J.getAngle(e.getAbsoluteRotation())+Math.PI*2)%(Math.PI*2),v={x:h.x+c*Math.cos(p)+f*Math.sin(-p),y:h.y+f*Math.cos(p)+c*Math.sin(p),width:s.width*l.x,height:s.height*l.y,rotation:p};return Vu(v,-J.getAngle(r),{x:0,y:0})}__getNodeRect(){var e=this.getNode();if(!e)return{x:-1e8,y:-1e8,width:0,height:0,rotation:0};const r=[];this.nodes().map(p=>{const v=p.getClientRect({skipTransform:!0,skipShadow:!0,skipStroke:this.ignoreStroke()});var _=[{x:v.x,y:v.y},{x:v.x+v.width,y:v.y},{x:v.x+v.width,y:v.y+v.height},{x:v.x,y:v.y+v.height}],y=p.getAbsoluteTransform();_.forEach(function(C){var S=y.point(C);r.push(S)})});const i=new st;i.rotate(-J.getAngle(this.rotation()));var s,l,h,c;r.forEach(function(p){var v=i.point(p);s===void 0&&(s=h=v.x,l=c=v.y),s=Math.min(s,v.x),l=Math.min(l,v.y),h=Math.max(h,v.x),c=Math.max(c,v.y)}),i.invert();const f=i.point({x:s,y:l});return{x:f.x,y:f.y,width:h-s,height:c-l,rotation:J.getAngle(this.rotation())}}getX(){return this._getNodeRect().x}getY(){return this._getNodeRect().y}getWidth(){return this._getNodeRect().width}getHeight(){return this._getNodeRect().height}_createElements(){this._createBack(),Wi.forEach((function(e){this._createAnchor(e)}).bind(this)),this._createAnchor("rotater")}_createAnchor(e){var r=new Ir({stroke:"rgb(0, 161, 255)",fill:"white",strokeWidth:1,name:e+" _anchor",dragDistance:0,draggable:!0,hitStrokeWidth:Sf?10:"auto"}),i=this;r.on("mousedown touchstart",function(s){i._handleMouseDown(s)}),r.on("dragstart",s=>{r.stopDrag(),s.cancelBubble=!0}),r.on("dragend",s=>{s.cancelBubble=!0}),r.on("mouseenter",()=>{var s=J.getAngle(this.rotation()),l=wf(e,s);r.getStage().content&&(r.getStage().content.style.cursor=l),this._cursorChange=!0}),r.on("mouseout",()=>{r.getStage().content&&(r.getStage().content.style.cursor=""),this._cursorChange=!1}),this.add(r)}_createBack(){var e=new z({name:"back",width:0,height:0,draggable:!0,sceneFunc(r){var i=this.getParent(),s=i.padding();r.beginPath(),r.rect(-s,-s,this.width()+s*2,this.height()+s*2),r.moveTo(this.width()/2,-s),i.rotateEnabled()&&r.lineTo(this.width()/2,-i.rotateAnchorOffset()*k._sign(this.height())-s),r.fillStrokeShape(this)},hitFunc:(r,i)=>{if(this.shouldOverdrawWholeArea()){var s=this.padding();r.beginPath(),r.rect(-s,-s,i.width()+s*2,i.height()+s*2),r.fillStrokeShape(i)}}});this.add(e),this._proxyDrag(e),e.on("dragstart",r=>{r.cancelBubble=!0}),e.on("dragmove",r=>{r.cancelBubble=!0}),e.on("dragend",r=>{r.cancelBubble=!0}),this.on("dragmove",r=>{this.update()})}_handleMouseDown(e){this._movingAnchorName=e.target.name().split(" ")[0];var r=this._getNodeRect(),i=r.width,s=r.height,l=Math.sqrt(Math.pow(i,2)+Math.pow(s,2));this.sin=Math.abs(s/l),this.cos=Math.abs(i/l),typeof window<"u"&&(window.addEventListener("mousemove",this._handleMouseMove),window.addEventListener("touchmove",this._handleMouseMove),window.addEventListener("mouseup",this._handleMouseUp,!0),window.addEventListener("touchend",this._handleMouseUp,!0)),this._transforming=!0;var h=e.target.getAbsolutePosition(),c=e.target.getStage().getPointerPosition();this._anchorDragOffset={x:c.x-h.x,y:c.y-h.y},this._fire("transformstart",{evt:e.evt,target:this.getNode()}),this._nodes.forEach(f=>{f._fire("transformstart",{evt:e.evt,target:f})})}_handleMouseMove(e){var r,i,s,l=this.findOne("."+this._movingAnchorName),h=l.getStage();h.setPointersPositions(e);const c=h.getPointerPosition();let f={x:c.x-this._anchorDragOffset.x,y:c.y-this._anchorDragOffset.y};const p=l.getAbsolutePosition();this.anchorDragBoundFunc()&&(f=this.anchorDragBoundFunc()(p,f,e)),l.setAbsolutePosition(f);const v=l.getAbsolutePosition();if(!(p.x===v.x&&p.y===v.y)){if(this._movingAnchorName==="rotater"){var _=this._getNodeRect();r=l.x()-_.width/2,i=-l.y()+_.height/2;let V=Math.atan2(-i,r)+Math.PI/2;_.height<0&&(V-=Math.PI);var y=J.getAngle(this.rotation());const b=y+V,te=J.getAngle(this.rotationSnapTolerance()),$=Ef(this.rotationSnaps(),b,te)-_.rotation,le=xf(_,$);this._fitNodesInto(le,e);return}var C=this.keepRatio()||e.shiftKey,D=this.centeredScaling()||e.altKey;if(this._movingAnchorName==="top-left"){if(C){var S=D?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".bottom-right").x(),y:this.findOne(".bottom-right").y()};s=Math.sqrt(Math.pow(S.x-l.x(),2)+Math.pow(S.y-l.y(),2));var M=this.findOne(".top-left").x()>S.x?-1:1,T=this.findOne(".top-left").y()>S.y?-1:1;r=s*this.cos*M,i=s*this.sin*T,this.findOne(".top-left").x(S.x-r),this.findOne(".top-left").y(S.y-i)}}else if(this._movingAnchorName==="top-center")this.findOne(".top-left").y(l.y());else if(this._movingAnchorName==="top-right"){if(C){var S=D?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".bottom-left").x(),y:this.findOne(".bottom-left").y()};s=Math.sqrt(Math.pow(l.x()-S.x,2)+Math.pow(S.y-l.y(),2));var M=this.findOne(".top-right").x()<S.x?-1:1,T=this.findOne(".top-right").y()>S.y?-1:1;r=s*this.cos*M,i=s*this.sin*T,this.findOne(".top-right").x(S.x+r),this.findOne(".top-right").y(S.y-i)}var G=l.position();this.findOne(".top-left").y(G.y),this.findOne(".bottom-right").x(G.x)}else if(this._movingAnchorName==="middle-left")this.findOne(".top-left").x(l.x());else if(this._movingAnchorName==="middle-right")this.findOne(".bottom-right").x(l.x());else if(this._movingAnchorName==="bottom-left"){if(C){var S=D?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".top-right").x(),y:this.findOne(".top-right").y()};s=Math.sqrt(Math.pow(S.x-l.x(),2)+Math.pow(l.y()-S.y,2));var M=S.x<l.x()?-1:1,T=l.y()<S.y?-1:1;r=s*this.cos*M,i=s*this.sin*T,l.x(S.x-r),l.y(S.y+i)}G=l.position(),this.findOne(".top-left").x(G.x),this.findOne(".bottom-right").y(G.y)}else if(this._movingAnchorName==="bottom-center")this.findOne(".bottom-right").y(l.y());else if(this._movingAnchorName==="bottom-right"){if(C){var S=D?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".top-left").x(),y:this.findOne(".top-left").y()};s=Math.sqrt(Math.pow(l.x()-S.x,2)+Math.pow(l.y()-S.y,2));var M=this.findOne(".bottom-right").x()<S.x?-1:1,T=this.findOne(".bottom-right").y()<S.y?-1:1;r=s*this.cos*M,i=s*this.sin*T,this.findOne(".bottom-right").x(S.x+r),this.findOne(".bottom-right").y(S.y+i)}}else console.error(new Error("Wrong position argument of selection resizer: "+this._movingAnchorName));var D=this.centeredScaling()||e.altKey;if(D){var I=this.findOne(".top-left"),E=this.findOne(".bottom-right"),F=I.x(),O=I.y(),X=this.getWidth()-E.x(),B=this.getHeight()-E.y();E.move({x:-F,y:-O}),I.move({x:X,y:B})}var Y=this.findOne(".top-left").getAbsolutePosition();r=Y.x,i=Y.y;var j=this.findOne(".bottom-right").x()-this.findOne(".top-left").x(),N=this.findOne(".bottom-right").y()-this.findOne(".top-left").y();this._fitNodesInto({x:r,y:i,width:j,height:N,rotation:J.getAngle(this.rotation())},e)}}_handleMouseUp(e){this._removeEvents(e)}getAbsoluteTransform(){return this.getTransform()}_removeEvents(e){if(this._transforming){this._transforming=!1,typeof window<"u"&&(window.removeEventListener("mousemove",this._handleMouseMove),window.removeEventListener("touchmove",this._handleMouseMove),window.removeEventListener("mouseup",this._handleMouseUp,!0),window.removeEventListener("touchend",this._handleMouseUp,!0));var r=this.getNode();this._fire("transformend",{evt:e,target:r}),r&&this._nodes.forEach(i=>{i._fire("transformend",{evt:e,target:i})}),this._movingAnchorName=null}}_fitNodesInto(e,r){var i=this._getNodeRect();const s=1;if(k._inRange(e.width,-this.padding()*2-s,s)){this.update();return}if(k._inRange(e.height,-this.padding()*2-s,s)){this.update();return}const l=this.flipEnabled();var h=new st;if(h.rotate(J.getAngle(this.rotation())),this._movingAnchorName&&e.width<0&&this._movingAnchorName.indexOf("left")>=0){const _=h.point({x:-this.padding()*2,y:0});if(e.x+=_.x,e.y+=_.y,e.width+=this.padding()*2,this._movingAnchorName=this._movingAnchorName.replace("left","right"),this._anchorDragOffset.x-=_.x,this._anchorDragOffset.y-=_.y,!l){this.update();return}}else if(this._movingAnchorName&&e.width<0&&this._movingAnchorName.indexOf("right")>=0){const _=h.point({x:this.padding()*2,y:0});if(this._movingAnchorName=this._movingAnchorName.replace("right","left"),this._anchorDragOffset.x-=_.x,this._anchorDragOffset.y-=_.y,e.width+=this.padding()*2,!l){this.update();return}}if(this._movingAnchorName&&e.height<0&&this._movingAnchorName.indexOf("top")>=0){const _=h.point({x:0,y:-this.padding()*2});if(e.x+=_.x,e.y+=_.y,this._movingAnchorName=this._movingAnchorName.replace("top","bottom"),this._anchorDragOffset.x-=_.x,this._anchorDragOffset.y-=_.y,e.height+=this.padding()*2,!l){this.update();return}}else if(this._movingAnchorName&&e.height<0&&this._movingAnchorName.indexOf("bottom")>=0){const _=h.point({x:0,y:this.padding()*2});if(this._movingAnchorName=this._movingAnchorName.replace("bottom","top"),this._anchorDragOffset.x-=_.x,this._anchorDragOffset.y-=_.y,e.height+=this.padding()*2,!l){this.update();return}}if(this.boundBoxFunc()){const _=this.boundBoxFunc()(i,e);_?e=_:k.warn("boundBoxFunc returned falsy. You should return new bound rect from it!")}const c=1e7,f=new st;f.translate(i.x,i.y),f.rotate(i.rotation),f.scale(i.width/c,i.height/c);const p=new st;p.translate(e.x,e.y),p.rotate(e.rotation),p.scale(e.width/c,e.height/c);const v=p.multiply(f.invert());this._nodes.forEach(_=>{var y;const C=_.getParent().getAbsoluteTransform(),S=_.getTransform().copy();S.translate(_.offsetX(),_.offsetY());const M=new st;M.multiply(C.copy().invert()).multiply(v).multiply(C).multiply(S);const T=M.decompose();_.setAttrs(T),this._fire("transform",{evt:r,target:_}),_._fire("transform",{evt:r,target:_}),(y=_.getLayer())===null||y===void 0||y.batchDraw()}),this.rotation(k._getRotation(e.rotation)),this._resetTransformCache(),this.update(),this.getLayer().batchDraw()}forceUpdate(){this._resetTransformCache(),this.update()}_batchChangeChild(e,r){this.findOne(e).setAttrs(r)}update(){var e,r=this._getNodeRect();this.rotation(k._getRotation(r.rotation));var i=r.width,s=r.height,l=this.enabledAnchors(),h=this.resizeEnabled(),c=this.padding(),f=this.anchorSize();this.find("._anchor").forEach(p=>{p.setAttrs({width:f,height:f,offsetX:f/2,offsetY:f/2,stroke:this.anchorStroke(),strokeWidth:this.anchorStrokeWidth(),fill:this.anchorFill(),cornerRadius:this.anchorCornerRadius()})}),this._batchChangeChild(".top-left",{x:0,y:0,offsetX:f/2+c,offsetY:f/2+c,visible:h&&l.indexOf("top-left")>=0}),this._batchChangeChild(".top-center",{x:i/2,y:0,offsetY:f/2+c,visible:h&&l.indexOf("top-center")>=0}),this._batchChangeChild(".top-right",{x:i,y:0,offsetX:f/2-c,offsetY:f/2+c,visible:h&&l.indexOf("top-right")>=0}),this._batchChangeChild(".middle-left",{x:0,y:s/2,offsetX:f/2+c,visible:h&&l.indexOf("middle-left")>=0}),this._batchChangeChild(".middle-right",{x:i,y:s/2,offsetX:f/2-c,visible:h&&l.indexOf("middle-right")>=0}),this._batchChangeChild(".bottom-left",{x:0,y:s,offsetX:f/2+c,offsetY:f/2-c,visible:h&&l.indexOf("bottom-left")>=0}),this._batchChangeChild(".bottom-center",{x:i/2,y:s,offsetY:f/2-c,visible:h&&l.indexOf("bottom-center")>=0}),this._batchChangeChild(".bottom-right",{x:i,y:s,offsetX:f/2-c,offsetY:f/2-c,visible:h&&l.indexOf("bottom-right")>=0}),this._batchChangeChild(".rotater",{x:i/2,y:-this.rotateAnchorOffset()*k._sign(s)-c,visible:this.rotateEnabled()}),this._batchChangeChild(".back",{width:i,height:s,visible:this.borderEnabled(),stroke:this.borderStroke(),strokeWidth:this.borderStrokeWidth(),dash:this.borderDash(),x:0,y:0}),(e=this.getLayer())===null||e===void 0||e.batchDraw()}isTransforming(){return this._transforming}stopTransform(){if(this._transforming){this._removeEvents();var e=this.findOne("."+this._movingAnchorName);e&&e.stopDrag()}}destroy(){return this.getStage()&&this._cursorChange&&this.getStage().content&&(this.getStage().content.style.cursor=""),Kn.prototype.destroy.call(this),this.detach(),this._removeEvents(),this}toObject(){return W.prototype.toObject.call(this)}clone(e){var r=W.prototype.clone.call(this,e);return r}getClientRect(){return this.nodes().length>0?super.getClientRect():{x:0,y:0,width:0,height:0}}}function Pf(o){return o instanceof Array||k.warn("enabledAnchors value should be an array"),o instanceof Array&&o.forEach(function(e){Wi.indexOf(e)===-1&&k.warn("Unknown anchor name: "+e+". Available names are: "+Wi.join(", "))}),o||[]}fe.prototype.className="Transformer";Re(fe);w.addGetterSetter(fe,"enabledAnchors",Wi,Pf);w.addGetterSetter(fe,"flipEnabled",!0,Mt());w.addGetterSetter(fe,"resizeEnabled",!0);w.addGetterSetter(fe,"anchorSize",10,U());w.addGetterSetter(fe,"rotateEnabled",!0);w.addGetterSetter(fe,"rotationSnaps",[]);w.addGetterSetter(fe,"rotateAnchorOffset",50,U());w.addGetterSetter(fe,"rotationSnapTolerance",5,U());w.addGetterSetter(fe,"borderEnabled",!0);w.addGetterSetter(fe,"anchorStroke","rgb(0, 161, 255)");w.addGetterSetter(fe,"anchorStrokeWidth",1,U());w.addGetterSetter(fe,"anchorFill","white");w.addGetterSetter(fe,"anchorCornerRadius",0,U());w.addGetterSetter(fe,"borderStroke","rgb(0, 161, 255)");w.addGetterSetter(fe,"borderStrokeWidth",1,U());w.addGetterSetter(fe,"borderDash");w.addGetterSetter(fe,"keepRatio",!0);w.addGetterSetter(fe,"centeredScaling",!1);w.addGetterSetter(fe,"ignoreStroke",!1);w.addGetterSetter(fe,"padding",0,U());w.addGetterSetter(fe,"node");w.addGetterSetter(fe,"nodes");w.addGetterSetter(fe,"boundBoxFunc");w.addGetterSetter(fe,"anchorDragBoundFunc");w.addGetterSetter(fe,"shouldOverdrawWholeArea",!1);w.addGetterSetter(fe,"useSingleNodeRotation",!0);w.backCompat(fe,{lineEnabled:"borderEnabled",rotateHandlerOffset:"rotateAnchorOffset",enabledHandlers:"enabledAnchors"});class Kt extends z{_sceneFunc(e){e.beginPath(),e.arc(0,0,this.radius(),0,J.getAngle(this.angle()),this.clockwise()),e.lineTo(0,0),e.closePath(),e.fillStrokeShape(this)}getWidth(){return this.radius()*2}getHeight(){return this.radius()*2}setWidth(e){this.radius(e/2)}setHeight(e){this.radius(e/2)}}Kt.prototype.className="Wedge";Kt.prototype._centroid=!0;Kt.prototype._attrsAffectingSize=["radius"];Re(Kt);w.addGetterSetter(Kt,"radius",0,U());w.addGetterSetter(Kt,"angle",0,U());w.addGetterSetter(Kt,"clockwise",!1);w.backCompat(Kt,{angleDeg:"angle",getAngleDeg:"getAngle",setAngleDeg:"setAngle"});function Jl(){this.r=0,this.g=0,this.b=0,this.a=0,this.next=null}var kf=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],Tf=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];function Mf(o,e){var r=o.data,i=o.width,s=o.height,l,h,c,f,p,v,_,y,C,S,M,T,G,D,I,E,F,O,X,B,Y,j,N,V,b=e+e+1,te=i-1,ne=s-1,$=e+1,le=$*($+1)/2,ge=new Jl,Le=null,re=ge,pe=null,Pe=null,_t=kf[e],St=Tf[e];for(c=1;c<b;c++)re=re.next=new Jl,c===$&&(Le=re);for(re.next=ge,_=v=0,h=0;h<s;h++){for(E=F=O=X=y=C=S=M=0,T=$*(B=r[v]),G=$*(Y=r[v+1]),D=$*(j=r[v+2]),I=$*(N=r[v+3]),y+=le*B,C+=le*Y,S+=le*j,M+=le*N,re=ge,c=0;c<$;c++)re.r=B,re.g=Y,re.b=j,re.a=N,re=re.next;for(c=1;c<$;c++)f=v+((te<c?te:c)<<2),y+=(re.r=B=r[f])*(V=$-c),C+=(re.g=Y=r[f+1])*V,S+=(re.b=j=r[f+2])*V,M+=(re.a=N=r[f+3])*V,E+=B,F+=Y,O+=j,X+=N,re=re.next;for(pe=ge,Pe=Le,l=0;l<i;l++)r[v+3]=N=M*_t>>St,N!==0?(N=255/N,r[v]=(y*_t>>St)*N,r[v+1]=(C*_t>>St)*N,r[v+2]=(S*_t>>St)*N):r[v]=r[v+1]=r[v+2]=0,y-=T,C-=G,S-=D,M-=I,T-=pe.r,G-=pe.g,D-=pe.b,I-=pe.a,f=_+((f=l+e+1)<te?f:te)<<2,E+=pe.r=r[f],F+=pe.g=r[f+1],O+=pe.b=r[f+2],X+=pe.a=r[f+3],y+=E,C+=F,S+=O,M+=X,pe=pe.next,T+=B=Pe.r,G+=Y=Pe.g,D+=j=Pe.b,I+=N=Pe.a,E-=B,F-=Y,O-=j,X-=N,Pe=Pe.next,v+=4;_+=i}for(l=0;l<i;l++){for(F=O=X=E=C=S=M=y=0,v=l<<2,T=$*(B=r[v]),G=$*(Y=r[v+1]),D=$*(j=r[v+2]),I=$*(N=r[v+3]),y+=le*B,C+=le*Y,S+=le*j,M+=le*N,re=ge,c=0;c<$;c++)re.r=B,re.g=Y,re.b=j,re.a=N,re=re.next;for(p=i,c=1;c<=e;c++)v=p+l<<2,y+=(re.r=B=r[v])*(V=$-c),C+=(re.g=Y=r[v+1])*V,S+=(re.b=j=r[v+2])*V,M+=(re.a=N=r[v+3])*V,E+=B,F+=Y,O+=j,X+=N,re=re.next,c<ne&&(p+=i);for(v=l,pe=ge,Pe=Le,h=0;h<s;h++)f=v<<2,r[f+3]=N=M*_t>>St,N>0?(N=255/N,r[f]=(y*_t>>St)*N,r[f+1]=(C*_t>>St)*N,r[f+2]=(S*_t>>St)*N):r[f]=r[f+1]=r[f+2]=0,y-=T,C-=G,S-=D,M-=I,T-=pe.r,G-=pe.g,D-=pe.b,I-=pe.a,f=l+((f=h+$)<ne?f:ne)*i<<2,y+=E+=pe.r=r[f],C+=F+=pe.g=r[f+1],S+=O+=pe.b=r[f+2],M+=X+=pe.a=r[f+3],pe=pe.next,T+=B=Pe.r,G+=Y=Pe.g,D+=j=Pe.b,I+=N=Pe.a,E-=B,F-=Y,O-=j,X-=N,Pe=Pe.next,v+=i}}const Rf=function(e){var r=Math.round(this.blurRadius());r>0&&Mf(e,r)};w.addGetterSetter(W,"blurRadius",0,U(),w.afterSetFilter);const Af=function(o){var e=this.brightness()*255,r=o.data,i=r.length,s;for(s=0;s<i;s+=4)r[s]+=e,r[s+1]+=e,r[s+2]+=e};w.addGetterSetter(W,"brightness",0,U(),w.afterSetFilter);const Lf=function(o){var e=Math.pow((this.contrast()+100)/100,2),r=o.data,i=r.length,s=150,l=150,h=150,c;for(c=0;c<i;c+=4)s=r[c],l=r[c+1],h=r[c+2],s/=255,s-=.5,s*=e,s+=.5,s*=255,l/=255,l-=.5,l*=e,l+=.5,l*=255,h/=255,h-=.5,h*=e,h+=.5,h*=255,s=s<0?0:s>255?255:s,l=l<0?0:l>255?255:l,h=h<0?0:h>255?255:h,r[c]=s,r[c+1]=l,r[c+2]=h};w.addGetterSetter(W,"contrast",0,U(),w.afterSetFilter);const Nf=function(o){var e=this.embossStrength()*10,r=this.embossWhiteLevel()*255,i=this.embossDirection(),s=this.embossBlend(),l=0,h=0,c=o.data,f=o.width,p=o.height,v=f*4,_=p;switch(i){case"top-left":l=-1,h=-1;break;case"top":l=-1,h=0;break;case"top-right":l=-1,h=1;break;case"right":l=0,h=1;break;case"bottom-right":l=1,h=1;break;case"bottom":l=1,h=0;break;case"bottom-left":l=1,h=-1;break;case"left":l=0,h=-1;break;default:k.error("Unknown emboss direction: "+i)}do{var y=(_-1)*v,C=l;_+C<1&&(C=0),_+C>p&&(C=0);var S=(_-1+C)*f*4,M=f;do{var T=y+(M-1)*4,G=h;M+G<1&&(G=0),M+G>f&&(G=0);var D=S+(M-1+G)*4,I=c[T]-c[D],E=c[T+1]-c[D+1],F=c[T+2]-c[D+2],O=I,X=O>0?O:-O,B=E>0?E:-E,Y=F>0?F:-F;if(B>X&&(O=E),Y>X&&(O=F),O*=e,s){var j=c[T]+O,N=c[T+1]+O,V=c[T+2]+O;c[T]=j>255?255:j<0?0:j,c[T+1]=N>255?255:N<0?0:N,c[T+2]=V>255?255:V<0?0:V}else{var b=r-O;b<0?b=0:b>255&&(b=255),c[T]=c[T+1]=c[T+2]=b}}while(--M)}while(--_)};w.addGetterSetter(W,"embossStrength",.5,U(),w.afterSetFilter);w.addGetterSetter(W,"embossWhiteLevel",.5,U(),w.afterSetFilter);w.addGetterSetter(W,"embossDirection","top-left",null,w.afterSetFilter);w.addGetterSetter(W,"embossBlend",!1,null,w.afterSetFilter);function ms(o,e,r,i,s){var l=r-e,h=s-i,c;return l===0?i+h/2:h===0?i:(c=(o-e)/l,c=h*c+i,c)}const If=function(o){var e=o.data,r=e.length,i=e[0],s=i,l,h=e[1],c=h,f,p=e[2],v=p,_,y,C=this.enhance();if(C!==0){for(y=0;y<r;y+=4)l=e[y+0],l<i?i=l:l>s&&(s=l),f=e[y+1],f<h?h=f:f>c&&(c=f),_=e[y+2],_<p?p=_:_>v&&(v=_);s===i&&(s=255,i=0),c===h&&(c=255,h=0),v===p&&(v=255,p=0);var S,M,T,G,D,I,E,F,O;for(C>0?(M=s+C*(255-s),T=i-C*(i-0),D=c+C*(255-c),I=h-C*(h-0),F=v+C*(255-v),O=p-C*(p-0)):(S=(s+i)*.5,M=s+C*(s-S),T=i+C*(i-S),G=(c+h)*.5,D=c+C*(c-G),I=h+C*(h-G),E=(v+p)*.5,F=v+C*(v-E),O=p+C*(p-E)),y=0;y<r;y+=4)e[y+0]=ms(e[y+0],i,s,T,M),e[y+1]=ms(e[y+1],h,c,I,D),e[y+2]=ms(e[y+2],p,v,O,F)}};w.addGetterSetter(W,"enhance",0,U(),w.afterSetFilter);const Of=function(o){var e=o.data,r=e.length,i,s;for(i=0;i<r;i+=4)s=.34*e[i]+.5*e[i+1]+.16*e[i+2],e[i]=s,e[i+1]=s,e[i+2]=s};w.addGetterSetter(W,"hue",0,U(),w.afterSetFilter);w.addGetterSetter(W,"saturation",0,U(),w.afterSetFilter);w.addGetterSetter(W,"luminance",0,U(),w.afterSetFilter);const Df=function(o){var e=o.data,r=e.length,i=1,s=Math.pow(2,this.saturation()),l=Math.abs(this.hue()+360)%360,h=this.luminance()*127,c,f=i*s*Math.cos(l*Math.PI/180),p=i*s*Math.sin(l*Math.PI/180),v=.299*i+.701*f+.167*p,_=.587*i-.587*f+.33*p,y=.114*i-.114*f-.497*p,C=.299*i-.299*f-.328*p,S=.587*i+.413*f+.035*p,M=.114*i-.114*f+.293*p,T=.299*i-.3*f+1.25*p,G=.587*i-.586*f-1.05*p,D=.114*i+.886*f-.2*p,I,E,F,O;for(c=0;c<r;c+=4)I=e[c+0],E=e[c+1],F=e[c+2],O=e[c+3],e[c+0]=v*I+_*E+y*F+h,e[c+1]=C*I+S*E+M*F+h,e[c+2]=T*I+G*E+D*F+h,e[c+3]=O},Gf=function(o){var e=o.data,r=e.length,i=Math.pow(2,this.value()),s=Math.pow(2,this.saturation()),l=Math.abs(this.hue()+360)%360,h,c=i*s*Math.cos(l*Math.PI/180),f=i*s*Math.sin(l*Math.PI/180),p=.299*i+.701*c+.167*f,v=.587*i-.587*c+.33*f,_=.114*i-.114*c-.497*f,y=.299*i-.299*c-.328*f,C=.587*i+.413*c+.035*f,S=.114*i-.114*c+.293*f,M=.299*i-.3*c+1.25*f,T=.587*i-.586*c-1.05*f,G=.114*i+.886*c-.2*f,D,I,E,F;for(h=0;h<r;h+=4)D=e[h+0],I=e[h+1],E=e[h+2],F=e[h+3],e[h+0]=p*D+v*I+_*E,e[h+1]=y*D+C*I+S*E,e[h+2]=M*D+T*I+G*E,e[h+3]=F};w.addGetterSetter(W,"hue",0,U(),w.afterSetFilter);w.addGetterSetter(W,"saturation",0,U(),w.afterSetFilter);w.addGetterSetter(W,"value",0,U(),w.afterSetFilter);const zf=function(o){var e=o.data,r=e.length,i;for(i=0;i<r;i+=4)e[i]=255-e[i],e[i+1]=255-e[i+1],e[i+2]=255-e[i+2]};var bf=function(o,e,r){var i=o.data,s=e.data,l=o.width,h=o.height,c=r.polarCenterX||l/2,f=r.polarCenterY||h/2,p,v,_,y=0,C=0,S=0,M=0,T,G=Math.sqrt(c*c+f*f);v=l-c,_=h-f,T=Math.sqrt(v*v+_*_),G=T>G?T:G;var D=h,I=l,E,F,O=360/I*Math.PI/180,X,B;for(F=0;F<I;F+=1)for(X=Math.sin(F*O),B=Math.cos(F*O),E=0;E<D;E+=1)v=Math.floor(c+G*E/D*B),_=Math.floor(f+G*E/D*X),p=(_*l+v)*4,y=i[p+0],C=i[p+1],S=i[p+2],M=i[p+3],p=(F+E*l)*4,s[p+0]=y,s[p+1]=C,s[p+2]=S,s[p+3]=M},Ff=function(o,e,r){var i=o.data,s=e.data,l=o.width,h=o.height,c=r.polarCenterX||l/2,f=r.polarCenterY||h/2,p,v,_,y,C,S=0,M=0,T=0,G=0,D,I=Math.sqrt(c*c+f*f);v=l-c,_=h-f,D=Math.sqrt(v*v+_*_),I=D>I?D:I;var E=h,F=l,O,X,B=0,Y,j;for(v=0;v<l;v+=1)for(_=0;_<h;_+=1)y=v-c,C=_-f,O=Math.sqrt(y*y+C*C)*E/I,X=(Math.atan2(C,y)*180/Math.PI+360+B)%360,X=X*F/360,Y=Math.floor(X),j=Math.floor(O),p=(j*l+Y)*4,S=i[p+0],M=i[p+1],T=i[p+2],G=i[p+3],p=(_*l+v)*4,s[p+0]=S,s[p+1]=M,s[p+2]=T,s[p+3]=G};const Bf=function(o){var e=o.width,r=o.height,i,s,l,h,c,f,p,v,_,y,C=Math.round(this.kaleidoscopePower()),S=Math.round(this.kaleidoscopeAngle()),M=Math.floor(e*(S%360)/360);if(!(C<1)){var T=k.createCanvasElement();T.width=e,T.height=r;var G=T.getContext("2d").getImageData(0,0,e,r);k.releaseCanvas(T),bf(o,G,{polarCenterX:e/2,polarCenterY:r/2});for(var D=e/Math.pow(2,C);D<=8;)D=D*2,C-=1;D=Math.ceil(D);var I=D,E=0,F=I,O=1;for(M+D>e&&(E=I,F=0,O=-1),s=0;s<r;s+=1)for(i=E;i!==F;i+=O)l=Math.round(i+M)%e,_=(e*s+l)*4,c=G.data[_+0],f=G.data[_+1],p=G.data[_+2],v=G.data[_+3],y=(e*s+i)*4,G.data[y+0]=c,G.data[y+1]=f,G.data[y+2]=p,G.data[y+3]=v;for(s=0;s<r;s+=1)for(I=Math.floor(D),h=0;h<C;h+=1){for(i=0;i<I+1;i+=1)_=(e*s+i)*4,c=G.data[_+0],f=G.data[_+1],p=G.data[_+2],v=G.data[_+3],y=(e*s+I*2-i-1)*4,G.data[y+0]=c,G.data[y+1]=f,G.data[y+2]=p,G.data[y+3]=v;I*=2}Ff(G,o,{})}};w.addGetterSetter(W,"kaleidoscopePower",2,U(),w.afterSetFilter);w.addGetterSetter(W,"kaleidoscopeAngle",0,U(),w.afterSetFilter);function bi(o,e,r){var i=(r*o.width+e)*4,s=[];return s.push(o.data[i++],o.data[i++],o.data[i++],o.data[i++]),s}function xr(o,e){return Math.sqrt(Math.pow(o[0]-e[0],2)+Math.pow(o[1]-e[1],2)+Math.pow(o[2]-e[2],2))}function Hf(o){for(var e=[0,0,0],r=0;r<o.length;r++)e[0]+=o[r][0],e[1]+=o[r][1],e[2]+=o[r][2];return e[0]/=o.length,e[1]/=o.length,e[2]/=o.length,e}function Wf(o,e){var r=bi(o,0,0),i=bi(o,o.width-1,0),s=bi(o,0,o.height-1),l=bi(o,o.width-1,o.height-1),h=e||10;if(xr(r,i)<h&&xr(i,l)<h&&xr(l,s)<h&&xr(s,r)<h){for(var c=Hf([i,r,l,s]),f=[],p=0;p<o.width*o.height;p++){var v=xr(c,[o.data[p*4],o.data[p*4+1],o.data[p*4+2]]);f[p]=v<h?0:255}return f}}function Uf(o,e){for(var r=0;r<o.width*o.height;r++)o.data[4*r+3]=e[r]}function Xf(o,e,r){for(var i=[1,1,1,1,0,1,1,1,1],s=Math.round(Math.sqrt(i.length)),l=Math.floor(s/2),h=[],c=0;c<r;c++)for(var f=0;f<e;f++){for(var p=c*e+f,v=0,_=0;_<s;_++)for(var y=0;y<s;y++){var C=c+_-l,S=f+y-l;if(C>=0&&C<r&&S>=0&&S<e){var M=C*e+S,T=i[_*s+y];v+=o[M]*T}}h[p]=v===255*8?255:0}return h}function Yf(o,e,r){for(var i=[1,1,1,1,1,1,1,1,1],s=Math.round(Math.sqrt(i.length)),l=Math.floor(s/2),h=[],c=0;c<r;c++)for(var f=0;f<e;f++){for(var p=c*e+f,v=0,_=0;_<s;_++)for(var y=0;y<s;y++){var C=c+_-l,S=f+y-l;if(C>=0&&C<r&&S>=0&&S<e){var M=C*e+S,T=i[_*s+y];v+=o[M]*T}}h[p]=v>=255*4?255:0}return h}function jf(o,e,r){for(var i=[.1111111111111111,.1111111111111111,.1111111111111111,.1111111111111111,.1111111111111111,.1111111111111111,.1111111111111111,.1111111111111111,.1111111111111111],s=Math.round(Math.sqrt(i.length)),l=Math.floor(s/2),h=[],c=0;c<r;c++)for(var f=0;f<e;f++){for(var p=c*e+f,v=0,_=0;_<s;_++)for(var y=0;y<s;y++){var C=c+_-l,S=f+y-l;if(C>=0&&C<r&&S>=0&&S<e){var M=C*e+S,T=i[_*s+y];v+=o[M]*T}}h[p]=v}return h}const Vf=function(o){var e=this.threshold(),r=Wf(o,e);return r&&(r=Xf(r,o.width,o.height),r=Yf(r,o.width,o.height),r=jf(r,o.width,o.height),Uf(o,r)),o};w.addGetterSetter(W,"threshold",0,U(),w.afterSetFilter);const qf=function(o){var e=this.noise()*255,r=o.data,i=r.length,s=e/2,l;for(l=0;l<i;l+=4)r[l+0]+=s-2*s*Math.random(),r[l+1]+=s-2*s*Math.random(),r[l+2]+=s-2*s*Math.random()};w.addGetterSetter(W,"noise",.2,U(),w.afterSetFilter);const Qf=function(o){var e=Math.ceil(this.pixelSize()),r=o.width,i=o.height,s,l,h,c,f,p,v,_=Math.ceil(r/e),y=Math.ceil(i/e),C,S,M,T,G,D,I,E=o.data;if(e<=0){k.error("pixelSize value can not be <= 0");return}for(G=0;G<_;G+=1)for(D=0;D<y;D+=1){for(c=0,f=0,p=0,v=0,C=G*e,S=C+e,M=D*e,T=M+e,I=0,s=C;s<S;s+=1)if(!(s>=r))for(l=M;l<T;l+=1)l>=i||(h=(r*l+s)*4,c+=E[h+0],f+=E[h+1],p+=E[h+2],v+=E[h+3],I+=1);for(c=c/I,f=f/I,p=p/I,v=v/I,s=C;s<S;s+=1)if(!(s>=r))for(l=M;l<T;l+=1)l>=i||(h=(r*l+s)*4,E[h+0]=c,E[h+1]=f,E[h+2]=p,E[h+3]=v)}};w.addGetterSetter(W,"pixelSize",8,U(),w.afterSetFilter);const Kf=function(o){var e=Math.round(this.levels()*254)+1,r=o.data,i=r.length,s=255/e,l;for(l=0;l<i;l+=1)r[l]=Math.floor(r[l]/s)*s};w.addGetterSetter(W,"levels",.5,U(),w.afterSetFilter);const Jf=function(o){var e=o.data,r=e.length,i=this.red(),s=this.green(),l=this.blue(),h,c;for(h=0;h<r;h+=4)c=(.34*e[h]+.5*e[h+1]+.16*e[h+2])/255,e[h]=c*i,e[h+1]=c*s,e[h+2]=c*l,e[h+3]=e[h+3]};w.addGetterSetter(W,"red",0,function(o){return this._filterUpToDate=!1,o>255?255:o<0?0:Math.round(o)});w.addGetterSetter(W,"green",0,function(o){return this._filterUpToDate=!1,o>255?255:o<0?0:Math.round(o)});w.addGetterSetter(W,"blue",0,mu,w.afterSetFilter);const Zf=function(o){var e=o.data,r=e.length,i=this.red(),s=this.green(),l=this.blue(),h=this.alpha(),c,f;for(c=0;c<r;c+=4)f=1-h,e[c]=i*h+e[c]*f,e[c+1]=s*h+e[c+1]*f,e[c+2]=l*h+e[c+2]*f};w.addGetterSetter(W,"red",0,function(o){return this._filterUpToDate=!1,o>255?255:o<0?0:Math.round(o)});w.addGetterSetter(W,"green",0,function(o){return this._filterUpToDate=!1,o>255?255:o<0?0:Math.round(o)});w.addGetterSetter(W,"blue",0,mu,w.afterSetFilter);w.addGetterSetter(W,"alpha",1,function(o){return this._filterUpToDate=!1,o>1?1:o<0?0:o});const $f=function(o){var e=o.data,r=e.length,i,s,l,h;for(i=0;i<r;i+=4)s=e[i+0],l=e[i+1],h=e[i+2],e[i+0]=Math.min(255,s*.393+l*.769+h*.189),e[i+1]=Math.min(255,s*.349+l*.686+h*.168),e[i+2]=Math.min(255,s*.272+l*.534+h*.131)},eg=function(o){var e=o.data,r=o.width,i=o.height,s=r*4,l=i;do{var h=(l-1)*s,c=r;do{var f=h+(c-1)*4,p=e[f],v=e[f+1],_=e[f+2];p>127&&(p=255-p),v>127&&(v=255-v),_>127&&(_=255-_),e[f]=p,e[f+1]=v,e[f+2]=_}while(--c)}while(--l)},tg=function(o){var e=this.threshold()*255,r=o.data,i=r.length,s;for(s=0;s<i;s+=1)r[s]=r[s]<e?0:255};w.addGetterSetter(W,"threshold",.5,U(),w.afterSetFilter);Pn.Util._assign(Pn,{Arc:qt,Arrow:Tn,Circle:Zn,Ellipse:dn,Image:yt,Label:Gs,Tag:Mn,Line:Qt,Path:me,Rect:Ir,RegularPolygon:Rn,Ring:An,Sprite:zt,Star:cn,Text:Me,TextPath:ze,Transformer:fe,Wedge:Kt,Filters:{Blur:Rf,Brighten:Af,Contrast:Lf,Emboss:Nf,Enhance:If,Grayscale:Of,HSL:Df,HSV:Gf,Invert:zf,Kaleidoscope:Bf,Mask:Vf,Noise:qf,Pixelate:Qf,Posterize:Kf,RGB:Jf,RGBA:Zf,Sepia:$f,Solarize:eg,Threshold:tg}});var ys={exports:{}};/**
 * @license React
 * react-reconciler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _s,Zl;function ng(){return Zl||(Zl=1,_s=function(e){var r={},i=Hd(),s=pu(),l=Object.assign;function h(t){for(var n="https://reactjs.org/docs/error-decoder.html?invariant="+t,a=1;a<arguments.length;a++)n+="&args[]="+encodeURIComponent(arguments[a]);return"Minified React error #"+t+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var c=i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,f=Symbol.for("react.element"),p=Symbol.for("react.portal"),v=Symbol.for("react.fragment"),_=Symbol.for("react.strict_mode"),y=Symbol.for("react.profiler"),C=Symbol.for("react.provider"),S=Symbol.for("react.context"),M=Symbol.for("react.forward_ref"),T=Symbol.for("react.suspense"),G=Symbol.for("react.suspense_list"),D=Symbol.for("react.memo"),I=Symbol.for("react.lazy"),E=Symbol.for("react.offscreen"),F=Symbol.iterator;function O(t){return t===null||typeof t!="object"?null:(t=F&&t[F]||t["@@iterator"],typeof t=="function"?t:null)}function X(t){if(t==null)return null;if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case v:return"Fragment";case p:return"Portal";case y:return"Profiler";case _:return"StrictMode";case T:return"Suspense";case G:return"SuspenseList"}if(typeof t=="object")switch(t.$$typeof){case S:return(t.displayName||"Context")+".Consumer";case C:return(t._context.displayName||"Context")+".Provider";case M:var n=t.render;return t=t.displayName,t||(t=n.displayName||n.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case D:return n=t.displayName||null,n!==null?n:X(t.type)||"Memo";case I:n=t._payload,t=t._init;try{return X(t(n))}catch{}}return null}function B(t){var n=t.type;switch(t.tag){case 24:return"Cache";case 9:return(n.displayName||"Context")+".Consumer";case 10:return(n._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return t=n.render,t=t.displayName||t.name||"",n.displayName||(t!==""?"ForwardRef("+t+")":"ForwardRef");case 7:return"Fragment";case 5:return n;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return X(n);case 8:return n===_?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof n=="function")return n.displayName||n.name||null;if(typeof n=="string")return n}return null}function Y(t){var n=t,a=t;if(t.alternate)for(;n.return;)n=n.return;else{t=n;do n=t,(n.flags&4098)!==0&&(a=n.return),t=n.return;while(t)}return n.tag===3?a:null}function j(t){if(Y(t)!==t)throw Error(h(188))}function N(t){var n=t.alternate;if(!n){if(n=Y(t),n===null)throw Error(h(188));return n!==t?null:t}for(var a=t,u=n;;){var d=a.return;if(d===null)break;var g=d.alternate;if(g===null){if(u=d.return,u!==null){a=u;continue}break}if(d.child===g.child){for(g=d.child;g;){if(g===a)return j(d),t;if(g===u)return j(d),n;g=g.sibling}throw Error(h(188))}if(a.return!==u.return)a=d,u=g;else{for(var m=!1,x=d.child;x;){if(x===a){m=!0,a=d,u=g;break}if(x===u){m=!0,u=d,a=g;break}x=x.sibling}if(!m){for(x=g.child;x;){if(x===a){m=!0,a=g,u=d;break}if(x===u){m=!0,u=g,a=d;break}x=x.sibling}if(!m)throw Error(h(189))}}if(a.alternate!==u)throw Error(h(190))}if(a.tag!==3)throw Error(h(188));return a.stateNode.current===a?t:n}function V(t){return t=N(t),t!==null?b(t):null}function b(t){if(t.tag===5||t.tag===6)return t;for(t=t.child;t!==null;){var n=b(t);if(n!==null)return n;t=t.sibling}return null}function te(t){if(t.tag===5||t.tag===6)return t;for(t=t.child;t!==null;){if(t.tag!==4){var n=te(t);if(n!==null)return n}t=t.sibling}return null}var ne=Array.isArray,$=e.getPublicInstance,le=e.getRootHostContext,ge=e.getChildHostContext,Le=e.prepareForCommit,re=e.resetAfterCommit,pe=e.createInstance,Pe=e.appendInitialChild,_t=e.finalizeInitialChildren,St=e.prepareUpdate,Vi=e.shouldSetTextContent,Bs=e.createTextInstance,Hs=e.scheduleTimeout,th=e.cancelTimeout,qi=e.noTimeout,Or=e.isPrimaryRenderer,rt=e.supportsMutation,Dr=e.supportsPersistence,lt=e.supportsHydration,nh=e.getInstanceFromNode,rh=e.preparePortalMount,ih=e.getCurrentEventPriority,ah=e.detachDeletedInstance,sh=e.supportsMicrotasks,oh=e.scheduleMicrotask,$n=e.supportsTestSelectors,lh=e.findFiberRoot,uh=e.getBoundingRect,hh=e.getTextContent,er=e.isHiddenSubtree,dh=e.matchAccessibilityRole,ch=e.setFocusIfFocusable,fh=e.setupIntersectionObserver,gh=e.appendChild,ph=e.appendChildToContainer,vh=e.commitTextUpdate,mh=e.commitMount,yh=e.commitUpdate,_h=e.insertBefore,Sh=e.insertInContainerBefore,wh=e.removeChild,Ch=e.removeChildFromContainer,Ws=e.resetTextContent,xh=e.hideInstance,Eh=e.hideTextInstance,Ph=e.unhideInstance,kh=e.unhideTextInstance,Th=e.clearContainer,Mh=e.cloneInstance,Us=e.createContainerChildSet,Xs=e.appendChildToContainerChildSet,Rh=e.finalizeContainerChildren,Qi=e.replaceContainerChildren,Ys=e.cloneHiddenInstance,js=e.cloneHiddenTextInstance,Ah=e.canHydrateInstance,Lh=e.canHydrateTextInstance,Nh=e.canHydrateSuspenseInstance,Vs=e.isSuspenseInstancePending,Ki=e.isSuspenseInstanceFallback,Ih=e.getSuspenseInstanceFallbackErrorDetails,Oh=e.registerSuspenseInstanceRetry,Gr=e.getNextHydratableSibling,Dh=e.getFirstHydratableChild,Gh=e.getFirstHydratableChildWithinContainer,zh=e.getFirstHydratableChildWithinSuspenseInstance,bh=e.hydrateInstance,Fh=e.hydrateTextInstance,Bh=e.hydrateSuspenseInstance,Hh=e.getNextHydratableInstanceAfterSuspenseInstance,Wh=e.commitHydratedContainer,Uh=e.commitHydratedSuspenseInstance,Xh=e.clearSuspenseBoundary,Yh=e.clearSuspenseBoundaryFromContainer,jh=e.shouldDeleteUnhydratedTailInstances,Vh=e.didNotMatchHydratedContainerTextInstance,qh=e.didNotMatchHydratedTextInstance,Ji;function tr(t){if(Ji===void 0)try{throw Error()}catch(a){var n=a.stack.trim().match(/\n( *(at )?)/);Ji=n&&n[1]||""}return`
`+Ji+t}var Zi=!1;function $i(t,n){if(!t||Zi)return"";Zi=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(n)if(n=function(){throw Error()},Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(n,[])}catch(H){var u=H}Reflect.construct(t,[],n)}else{try{n.call()}catch(H){u=H}t.call(n.prototype)}else{try{throw Error()}catch(H){u=H}t()}}catch(H){if(H&&u&&typeof H.stack=="string"){for(var d=H.stack.split(`
`),g=u.stack.split(`
`),m=d.length-1,x=g.length-1;1<=m&&0<=x&&d[m]!==g[x];)x--;for(;1<=m&&0<=x;m--,x--)if(d[m]!==g[x]){if(m!==1||x!==1)do if(m--,x--,0>x||d[m]!==g[x]){var A=`
`+d[m].replace(" at new "," at ");return t.displayName&&A.includes("<anonymous>")&&(A=A.replace("<anonymous>",t.displayName)),A}while(1<=m&&0<=x);break}}}finally{Zi=!1,Error.prepareStackTrace=a}return(t=t?t.displayName||t.name:"")?tr(t):""}var Qh=Object.prototype.hasOwnProperty,ea=[],Ln=-1;function Jt(t){return{current:t}}function we(t){0>Ln||(t.current=ea[Ln],ea[Ln]=null,Ln--)}function Se(t,n){Ln++,ea[Ln]=t.current,t.current=n}var Zt={},Xe=Jt(Zt),Je=Jt(!1),gn=Zt;function Nn(t,n){var a=t.type.contextTypes;if(!a)return Zt;var u=t.stateNode;if(u&&u.__reactInternalMemoizedUnmaskedChildContext===n)return u.__reactInternalMemoizedMaskedChildContext;var d={},g;for(g in a)d[g]=n[g];return u&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=n,t.__reactInternalMemoizedMaskedChildContext=d),d}function Ze(t){return t=t.childContextTypes,t!=null}function zr(){we(Je),we(Xe)}function qs(t,n,a){if(Xe.current!==Zt)throw Error(h(168));Se(Xe,n),Se(Je,a)}function Qs(t,n,a){var u=t.stateNode;if(n=n.childContextTypes,typeof u.getChildContext!="function")return a;u=u.getChildContext();for(var d in u)if(!(d in n))throw Error(h(108,B(t)||"Unknown",d));return l({},a,u)}function br(t){return t=(t=t.stateNode)&&t.__reactInternalMemoizedMergedChildContext||Zt,gn=Xe.current,Se(Xe,t),Se(Je,Je.current),!0}function Ks(t,n,a){var u=t.stateNode;if(!u)throw Error(h(169));a?(t=Qs(t,n,gn),u.__reactInternalMemoizedMergedChildContext=t,we(Je),we(Xe),Se(Xe,t)):we(Je),Se(Je,a)}var wt=Math.clz32?Math.clz32:Zh,Kh=Math.log,Jh=Math.LN2;function Zh(t){return t>>>=0,t===0?32:31-(Kh(t)/Jh|0)|0}var Fr=64,Br=4194304;function nr(t){switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return t&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return t}}function Hr(t,n){var a=t.pendingLanes;if(a===0)return 0;var u=0,d=t.suspendedLanes,g=t.pingedLanes,m=a&268435455;if(m!==0){var x=m&~d;x!==0?u=nr(x):(g&=m,g!==0&&(u=nr(g)))}else m=a&~d,m!==0?u=nr(m):g!==0&&(u=nr(g));if(u===0)return 0;if(n!==0&&n!==u&&(n&d)===0&&(d=u&-u,g=n&-n,d>=g||d===16&&(g&4194240)!==0))return n;if((u&4)!==0&&(u|=a&16),n=t.entangledLanes,n!==0)for(t=t.entanglements,n&=u;0<n;)a=31-wt(n),d=1<<a,u|=t[a],n&=~d;return u}function $h(t,n){switch(t){case 1:case 2:case 4:return n+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return n+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ed(t,n){for(var a=t.suspendedLanes,u=t.pingedLanes,d=t.expirationTimes,g=t.pendingLanes;0<g;){var m=31-wt(g),x=1<<m,A=d[m];A===-1?((x&a)===0||(x&u)!==0)&&(d[m]=$h(x,n)):A<=n&&(t.expiredLanes|=x),g&=~x}}function ta(t){return t=t.pendingLanes&-1073741825,t!==0?t:t&1073741824?1073741824:0}function Js(){var t=Fr;return Fr<<=1,(Fr&4194240)===0&&(Fr=64),t}function na(t){for(var n=[],a=0;31>a;a++)n.push(t);return n}function rr(t,n,a){t.pendingLanes|=n,n!==536870912&&(t.suspendedLanes=0,t.pingedLanes=0),t=t.eventTimes,n=31-wt(n),t[n]=a}function td(t,n){var a=t.pendingLanes&~n;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.expiredLanes&=n,t.mutableReadLanes&=n,t.entangledLanes&=n,n=t.entanglements;var u=t.eventTimes;for(t=t.expirationTimes;0<a;){var d=31-wt(a),g=1<<d;n[d]=0,u[d]=-1,t[d]=-1,a&=~g}}function ra(t,n){var a=t.entangledLanes|=n;for(t=t.entanglements;a;){var u=31-wt(a),d=1<<u;d&n|t[u]&n&&(t[u]|=n),a&=~d}}var ue=0;function Zs(t){return t&=-t,1<t?4<t?(t&268435455)!==0?16:536870912:4:1}var ia=s.unstable_scheduleCallback,$s=s.unstable_cancelCallback,nd=s.unstable_shouldYield,rd=s.unstable_requestPaint,be=s.unstable_now,aa=s.unstable_ImmediatePriority,id=s.unstable_UserBlockingPriority,sa=s.unstable_NormalPriority,ad=s.unstable_IdlePriority,Wr=null,Rt=null;function sd(t){if(Rt&&typeof Rt.onCommitFiberRoot=="function")try{Rt.onCommitFiberRoot(Wr,t,void 0,(t.current.flags&128)===128)}catch{}}function od(t,n){return t===n&&(t!==0||1/t===1/n)||t!==t&&n!==n}var Ct=typeof Object.is=="function"?Object.is:od,bt=null,Ur=!1,oa=!1;function eo(t){bt===null?bt=[t]:bt.push(t)}function ld(t){Ur=!0,eo(t)}function At(){if(!oa&&bt!==null){oa=!0;var t=0,n=ue;try{var a=bt;for(ue=1;t<a.length;t++){var u=a[t];do u=u(!0);while(u!==null)}bt=null,Ur=!1}catch(d){throw bt!==null&&(bt=bt.slice(t+1)),ia(aa,At),d}finally{ue=n,oa=!1}}return null}var In=[],On=0,Xr=null,Yr=0,ut=[],ht=0,pn=null,Ft=1,Bt="";function vn(t,n){In[On++]=Yr,In[On++]=Xr,Xr=t,Yr=n}function to(t,n,a){ut[ht++]=Ft,ut[ht++]=Bt,ut[ht++]=pn,pn=t;var u=Ft;t=Bt;var d=32-wt(u)-1;u&=~(1<<d),a+=1;var g=32-wt(n)+d;if(30<g){var m=d-d%5;g=(u&(1<<m)-1).toString(32),u>>=m,d-=m,Ft=1<<32-wt(n)+d|a<<d|u,Bt=g+t}else Ft=1<<g|a<<d|u,Bt=t}function la(t){t.return!==null&&(vn(t,1),to(t,1,0))}function ua(t){for(;t===Xr;)Xr=In[--On],In[On]=null,Yr=In[--On],In[On]=null;for(;t===pn;)pn=ut[--ht],ut[ht]=null,Bt=ut[--ht],ut[ht]=null,Ft=ut[--ht],ut[ht]=null}var it=null,dt=null,xe=!1,ir=!1,xt=null;function no(t,n){var a=vt(5,null,null,0);a.elementType="DELETED",a.stateNode=n,a.return=t,n=t.deletions,n===null?(t.deletions=[a],t.flags|=16):n.push(a)}function ro(t,n){switch(t.tag){case 5:return n=Ah(n,t.type,t.pendingProps),n!==null?(t.stateNode=n,it=t,dt=Dh(n),!0):!1;case 6:return n=Lh(n,t.pendingProps),n!==null?(t.stateNode=n,it=t,dt=null,!0):!1;case 13:if(n=Nh(n),n!==null){var a=pn!==null?{id:Ft,overflow:Bt}:null;return t.memoizedState={dehydrated:n,treeContext:a,retryLane:1073741824},a=vt(18,null,null,0),a.stateNode=n,a.return=t,t.child=a,it=t,dt=null,!0}return!1;default:return!1}}function ha(t){return(t.mode&1)!==0&&(t.flags&128)===0}function da(t){if(xe){var n=dt;if(n){var a=n;if(!ro(t,n)){if(ha(t))throw Error(h(418));n=Gr(a);var u=it;n&&ro(t,n)?no(u,a):(t.flags=t.flags&-4097|2,xe=!1,it=t)}}else{if(ha(t))throw Error(h(418));t.flags=t.flags&-4097|2,xe=!1,it=t}}}function io(t){for(t=t.return;t!==null&&t.tag!==5&&t.tag!==3&&t.tag!==13;)t=t.return;it=t}function jr(t){if(!lt||t!==it)return!1;if(!xe)return io(t),xe=!0,!1;if(t.tag!==3&&(t.tag!==5||jh(t.type)&&!Vi(t.type,t.memoizedProps))){var n=dt;if(n){if(ha(t))throw ao(),Error(h(418));for(;n;)no(t,n),n=Gr(n)}}if(io(t),t.tag===13){if(!lt)throw Error(h(316));if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(h(317));dt=Hh(t)}else dt=it?Gr(t.stateNode):null;return!0}function ao(){for(var t=dt;t;)t=Gr(t)}function Dn(){lt&&(dt=it=null,ir=xe=!1)}function ca(t){xt===null?xt=[t]:xt.push(t)}var ud=c.ReactCurrentBatchConfig;function Vr(t,n){if(Ct(t,n))return!0;if(typeof t!="object"||t===null||typeof n!="object"||n===null)return!1;var a=Object.keys(t),u=Object.keys(n);if(a.length!==u.length)return!1;for(u=0;u<a.length;u++){var d=a[u];if(!Qh.call(n,d)||!Ct(t[d],n[d]))return!1}return!0}function hd(t){switch(t.tag){case 5:return tr(t.type);case 16:return tr("Lazy");case 13:return tr("Suspense");case 19:return tr("SuspenseList");case 0:case 2:case 15:return t=$i(t.type,!1),t;case 11:return t=$i(t.type.render,!1),t;case 1:return t=$i(t.type,!0),t;default:return""}}function ar(t,n,a){if(t=a.ref,t!==null&&typeof t!="function"&&typeof t!="object"){if(a._owner){if(a=a._owner,a){if(a.tag!==1)throw Error(h(309));var u=a.stateNode}if(!u)throw Error(h(147,t));var d=u,g=""+t;return n!==null&&n.ref!==null&&typeof n.ref=="function"&&n.ref._stringRef===g?n.ref:(n=function(m){var x=d.refs;m===null?delete x[g]:x[g]=m},n._stringRef=g,n)}if(typeof t!="string")throw Error(h(284));if(!a._owner)throw Error(h(290,t))}return t}function qr(t,n){throw t=Object.prototype.toString.call(n),Error(h(31,t==="[object Object]"?"object with keys {"+Object.keys(n).join(", ")+"}":t))}function so(t){var n=t._init;return n(t._payload)}function oo(t){function n(R,P){if(t){var L=R.deletions;L===null?(R.deletions=[P],R.flags|=16):L.push(P)}}function a(R,P){if(!t)return null;for(;P!==null;)n(R,P),P=P.sibling;return null}function u(R,P){for(R=new Map;P!==null;)P.key!==null?R.set(P.key,P):R.set(P.index,P),P=P.sibling;return R}function d(R,P){return R=sn(R,P),R.index=0,R.sibling=null,R}function g(R,P,L){return R.index=L,t?(L=R.alternate,L!==null?(L=L.index,L<P?(R.flags|=2,P):L):(R.flags|=2,P)):(R.flags|=1048576,P)}function m(R){return t&&R.alternate===null&&(R.flags|=2),R}function x(R,P,L,Q){return P===null||P.tag!==6?(P=os(L,R.mode,Q),P.return=R,P):(P=d(P,L),P.return=R,P)}function A(R,P,L,Q){var ee=L.type;return ee===v?Z(R,P,L.props.children,Q,L.key):P!==null&&(P.elementType===ee||typeof ee=="object"&&ee!==null&&ee.$$typeof===I&&so(ee)===P.type)?(Q=d(P,L.props),Q.ref=ar(R,P,L),Q.return=R,Q):(Q=Pi(L.type,L.key,L.props,null,R.mode,Q),Q.ref=ar(R,P,L),Q.return=R,Q)}function H(R,P,L,Q){return P===null||P.tag!==4||P.stateNode.containerInfo!==L.containerInfo||P.stateNode.implementation!==L.implementation?(P=ls(L,R.mode,Q),P.return=R,P):(P=d(P,L.children||[]),P.return=R,P)}function Z(R,P,L,Q,ee){return P===null||P.tag!==7?(P=xn(L,R.mode,Q,ee),P.return=R,P):(P=d(P,L),P.return=R,P)}function ie(R,P,L){if(typeof P=="string"&&P!==""||typeof P=="number")return P=os(""+P,R.mode,L),P.return=R,P;if(typeof P=="object"&&P!==null){switch(P.$$typeof){case f:return L=Pi(P.type,P.key,P.props,null,R.mode,L),L.ref=ar(R,null,P),L.return=R,L;case p:return P=ls(P,R.mode,L),P.return=R,P;case I:var Q=P._init;return ie(R,Q(P._payload),L)}if(ne(P)||O(P))return P=xn(P,R.mode,L,null),P.return=R,P;qr(R,P)}return null}function q(R,P,L,Q){var ee=P!==null?P.key:null;if(typeof L=="string"&&L!==""||typeof L=="number")return ee!==null?null:x(R,P,""+L,Q);if(typeof L=="object"&&L!==null){switch(L.$$typeof){case f:return L.key===ee?A(R,P,L,Q):null;case p:return L.key===ee?H(R,P,L,Q):null;case I:return ee=L._init,q(R,P,ee(L._payload),Q)}if(ne(L)||O(L))return ee!==null?null:Z(R,P,L,Q,null);qr(R,L)}return null}function Ce(R,P,L,Q,ee){if(typeof Q=="string"&&Q!==""||typeof Q=="number")return R=R.get(L)||null,x(P,R,""+Q,ee);if(typeof Q=="object"&&Q!==null){switch(Q.$$typeof){case f:return R=R.get(Q.key===null?L:Q.key)||null,A(P,R,Q,ee);case p:return R=R.get(Q.key===null?L:Q.key)||null,H(P,R,Q,ee);case I:var se=Q._init;return Ce(R,P,L,se(Q._payload),ee)}if(ne(Q)||O(Q))return R=R.get(L)||null,Z(P,R,Q,ee,null);qr(P,Q)}return null}function ye(R,P,L,Q){for(var ee=null,se=null,ae=P,he=P=0,He=null;ae!==null&&he<L.length;he++){ae.index>he?(He=ae,ae=null):He=ae.sibling;var de=q(R,ae,L[he],Q);if(de===null){ae===null&&(ae=He);break}t&&ae&&de.alternate===null&&n(R,ae),P=g(de,P,he),se===null?ee=de:se.sibling=de,se=de,ae=He}if(he===L.length)return a(R,ae),xe&&vn(R,he),ee;if(ae===null){for(;he<L.length;he++)ae=ie(R,L[he],Q),ae!==null&&(P=g(ae,P,he),se===null?ee=ae:se.sibling=ae,se=ae);return xe&&vn(R,he),ee}for(ae=u(R,ae);he<L.length;he++)He=Ce(ae,R,he,L[he],Q),He!==null&&(t&&He.alternate!==null&&ae.delete(He.key===null?he:He.key),P=g(He,P,he),se===null?ee=He:se.sibling=He,se=He);return t&&ae.forEach(function(on){return n(R,on)}),xe&&vn(R,he),ee}function nt(R,P,L,Q){var ee=O(L);if(typeof ee!="function")throw Error(h(150));if(L=ee.call(L),L==null)throw Error(h(151));for(var se=ee=null,ae=P,he=P=0,He=null,de=L.next();ae!==null&&!de.done;he++,de=L.next()){ae.index>he?(He=ae,ae=null):He=ae.sibling;var on=q(R,ae,de.value,Q);if(on===null){ae===null&&(ae=He);break}t&&ae&&on.alternate===null&&n(R,ae),P=g(on,P,he),se===null?ee=on:se.sibling=on,se=on,ae=He}if(de.done)return a(R,ae),xe&&vn(R,he),ee;if(ae===null){for(;!de.done;he++,de=L.next())de=ie(R,de.value,Q),de!==null&&(P=g(de,P,he),se===null?ee=de:se.sibling=de,se=de);return xe&&vn(R,he),ee}for(ae=u(R,ae);!de.done;he++,de=L.next())de=Ce(ae,R,he,de.value,Q),de!==null&&(t&&de.alternate!==null&&ae.delete(de.key===null?he:de.key),P=g(de,P,he),se===null?ee=de:se.sibling=de,se=de);return t&&ae.forEach(function(Bd){return n(R,Bd)}),xe&&vn(R,he),ee}function Xt(R,P,L,Q){if(typeof L=="object"&&L!==null&&L.type===v&&L.key===null&&(L=L.props.children),typeof L=="object"&&L!==null){switch(L.$$typeof){case f:e:{for(var ee=L.key,se=P;se!==null;){if(se.key===ee){if(ee=L.type,ee===v){if(se.tag===7){a(R,se.sibling),P=d(se,L.props.children),P.return=R,R=P;break e}}else if(se.elementType===ee||typeof ee=="object"&&ee!==null&&ee.$$typeof===I&&so(ee)===se.type){a(R,se.sibling),P=d(se,L.props),P.ref=ar(R,se,L),P.return=R,R=P;break e}a(R,se);break}else n(R,se);se=se.sibling}L.type===v?(P=xn(L.props.children,R.mode,Q,L.key),P.return=R,R=P):(Q=Pi(L.type,L.key,L.props,null,R.mode,Q),Q.ref=ar(R,P,L),Q.return=R,R=Q)}return m(R);case p:e:{for(se=L.key;P!==null;){if(P.key===se)if(P.tag===4&&P.stateNode.containerInfo===L.containerInfo&&P.stateNode.implementation===L.implementation){a(R,P.sibling),P=d(P,L.children||[]),P.return=R,R=P;break e}else{a(R,P);break}else n(R,P);P=P.sibling}P=ls(L,R.mode,Q),P.return=R,R=P}return m(R);case I:return se=L._init,Xt(R,P,se(L._payload),Q)}if(ne(L))return ye(R,P,L,Q);if(O(L))return nt(R,P,L,Q);qr(R,L)}return typeof L=="string"&&L!==""||typeof L=="number"?(L=""+L,P!==null&&P.tag===6?(a(R,P.sibling),P=d(P,L),P.return=R,R=P):(a(R,P),P=os(L,R.mode,Q),P.return=R,R=P),m(R)):a(R,P)}return Xt}var Gn=oo(!0),lo=oo(!1),Qr=Jt(null),Kr=null,zn=null,fa=null;function ga(){fa=zn=Kr=null}function uo(t,n,a){Or?(Se(Qr,n._currentValue),n._currentValue=a):(Se(Qr,n._currentValue2),n._currentValue2=a)}function pa(t){var n=Qr.current;we(Qr),Or?t._currentValue=n:t._currentValue2=n}function va(t,n,a){for(;t!==null;){var u=t.alternate;if((t.childLanes&n)!==n?(t.childLanes|=n,u!==null&&(u.childLanes|=n)):u!==null&&(u.childLanes&n)!==n&&(u.childLanes|=n),t===a)break;t=t.return}}function bn(t,n){Kr=t,fa=zn=null,t=t.dependencies,t!==null&&t.firstContext!==null&&((t.lanes&n)!==0&&($e=!0),t.firstContext=null)}function ct(t){var n=Or?t._currentValue:t._currentValue2;if(fa!==t)if(t={context:t,memoizedValue:n,next:null},zn===null){if(Kr===null)throw Error(h(308));zn=t,Kr.dependencies={lanes:0,firstContext:t}}else zn=zn.next=t;return n}var mn=null;function ma(t){mn===null?mn=[t]:mn.push(t)}function ho(t,n,a,u){var d=n.interleaved;return d===null?(a.next=a,ma(n)):(a.next=d.next,d.next=a),n.interleaved=a,Lt(t,u)}function Lt(t,n){t.lanes|=n;var a=t.alternate;for(a!==null&&(a.lanes|=n),a=t,t=t.return;t!==null;)t.childLanes|=n,a=t.alternate,a!==null&&(a.childLanes|=n),a=t,t=t.return;return a.tag===3?a.stateNode:null}var $t=!1;function ya(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function co(t,n){t=t.updateQueue,n.updateQueue===t&&(n.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,effects:t.effects})}function Ht(t,n){return{eventTime:t,lane:n,tag:0,payload:null,callback:null,next:null}}function en(t,n,a){var u=t.updateQueue;if(u===null)return null;if(u=u.shared,(oe&2)!==0){var d=u.pending;return d===null?n.next=n:(n.next=d.next,d.next=n),u.pending=n,Lt(t,a)}return d=u.interleaved,d===null?(n.next=n,ma(u)):(n.next=d.next,d.next=n),u.interleaved=n,Lt(t,a)}function Jr(t,n,a){if(n=n.updateQueue,n!==null&&(n=n.shared,(a&4194240)!==0)){var u=n.lanes;u&=t.pendingLanes,a|=u,n.lanes=a,ra(t,a)}}function fo(t,n){var a=t.updateQueue,u=t.alternate;if(u!==null&&(u=u.updateQueue,a===u)){var d=null,g=null;if(a=a.firstBaseUpdate,a!==null){do{var m={eventTime:a.eventTime,lane:a.lane,tag:a.tag,payload:a.payload,callback:a.callback,next:null};g===null?d=g=m:g=g.next=m,a=a.next}while(a!==null);g===null?d=g=n:g=g.next=n}else d=g=n;a={baseState:u.baseState,firstBaseUpdate:d,lastBaseUpdate:g,shared:u.shared,effects:u.effects},t.updateQueue=a;return}t=a.lastBaseUpdate,t===null?a.firstBaseUpdate=n:t.next=n,a.lastBaseUpdate=n}function Zr(t,n,a,u){var d=t.updateQueue;$t=!1;var g=d.firstBaseUpdate,m=d.lastBaseUpdate,x=d.shared.pending;if(x!==null){d.shared.pending=null;var A=x,H=A.next;A.next=null,m===null?g=H:m.next=H,m=A;var Z=t.alternate;Z!==null&&(Z=Z.updateQueue,x=Z.lastBaseUpdate,x!==m&&(x===null?Z.firstBaseUpdate=H:x.next=H,Z.lastBaseUpdate=A))}if(g!==null){var ie=d.baseState;m=0,Z=H=A=null,x=g;do{var q=x.lane,Ce=x.eventTime;if((u&q)===q){Z!==null&&(Z=Z.next={eventTime:Ce,lane:0,tag:x.tag,payload:x.payload,callback:x.callback,next:null});e:{var ye=t,nt=x;switch(q=n,Ce=a,nt.tag){case 1:if(ye=nt.payload,typeof ye=="function"){ie=ye.call(Ce,ie,q);break e}ie=ye;break e;case 3:ye.flags=ye.flags&-65537|128;case 0:if(ye=nt.payload,q=typeof ye=="function"?ye.call(Ce,ie,q):ye,q==null)break e;ie=l({},ie,q);break e;case 2:$t=!0}}x.callback!==null&&x.lane!==0&&(t.flags|=64,q=d.effects,q===null?d.effects=[x]:q.push(x))}else Ce={eventTime:Ce,lane:q,tag:x.tag,payload:x.payload,callback:x.callback,next:null},Z===null?(H=Z=Ce,A=ie):Z=Z.next=Ce,m|=q;if(x=x.next,x===null){if(x=d.shared.pending,x===null)break;q=x,x=q.next,q.next=null,d.lastBaseUpdate=q,d.shared.pending=null}}while(!0);if(Z===null&&(A=ie),d.baseState=A,d.firstBaseUpdate=H,d.lastBaseUpdate=Z,n=d.shared.interleaved,n!==null){d=n;do m|=d.lane,d=d.next;while(d!==n)}else g===null&&(d.shared.lanes=0);_n|=m,t.lanes=m,t.memoizedState=ie}}function go(t,n,a){if(t=n.effects,n.effects=null,t!==null)for(n=0;n<t.length;n++){var u=t[n],d=u.callback;if(d!==null){if(u.callback=null,u=a,typeof d!="function")throw Error(h(191,d));d.call(u)}}}var sr={},ft=Jt(sr),or=Jt(sr),Fn=Jt(sr);function Nt(t){if(t===sr)throw Error(h(174));return t}function _a(t,n){Se(Fn,n),Se(or,t),Se(ft,sr),t=le(n),we(ft),Se(ft,t)}function Bn(){we(ft),we(or),we(Fn)}function po(t){var n=Nt(Fn.current),a=Nt(ft.current);n=ge(a,t.type,n),a!==n&&(Se(or,t),Se(ft,n))}function Sa(t){or.current===t&&(we(ft),we(or))}var ke=Jt(0);function $r(t){for(var n=t;n!==null;){if(n.tag===13){var a=n.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||Vs(a)||Ki(a)))return n}else if(n.tag===19&&n.memoizedProps.revealOrder!==void 0){if((n.flags&128)!==0)return n}else if(n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return null;n=n.return}n.sibling.return=n.return,n=n.sibling}return null}var wa=[];function Ca(){for(var t=0;t<wa.length;t++){var n=wa[t];Or?n._workInProgressVersionPrimary=null:n._workInProgressVersionSecondary=null}wa.length=0}var ei=c.ReactCurrentDispatcher,xa=c.ReactCurrentBatchConfig,yn=0,Te=null,De=null,Fe=null,ti=!1,lr=!1,ur=0,dd=0;function Ye(){throw Error(h(321))}function Ea(t,n){if(n===null)return!1;for(var a=0;a<n.length&&a<t.length;a++)if(!Ct(t[a],n[a]))return!1;return!0}function Pa(t,n,a,u,d,g){if(yn=g,Te=n,n.memoizedState=null,n.updateQueue=null,n.lanes=0,ei.current=t===null||t.memoizedState===null?pd:vd,t=a(u,d),lr){g=0;do{if(lr=!1,ur=0,25<=g)throw Error(h(301));g+=1,Fe=De=null,n.updateQueue=null,ei.current=md,t=a(u,d)}while(lr)}if(ei.current=ii,n=De!==null&&De.next!==null,yn=0,Fe=De=Te=null,ti=!1,n)throw Error(h(300));return t}function ka(){var t=ur!==0;return ur=0,t}function It(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Fe===null?Te.memoizedState=Fe=t:Fe=Fe.next=t,Fe}function gt(){if(De===null){var t=Te.alternate;t=t!==null?t.memoizedState:null}else t=De.next;var n=Fe===null?Te.memoizedState:Fe.next;if(n!==null)Fe=n,De=t;else{if(t===null)throw Error(h(310));De=t,t={memoizedState:De.memoizedState,baseState:De.baseState,baseQueue:De.baseQueue,queue:De.queue,next:null},Fe===null?Te.memoizedState=Fe=t:Fe=Fe.next=t}return Fe}function hr(t,n){return typeof n=="function"?n(t):n}function Ta(t){var n=gt(),a=n.queue;if(a===null)throw Error(h(311));a.lastRenderedReducer=t;var u=De,d=u.baseQueue,g=a.pending;if(g!==null){if(d!==null){var m=d.next;d.next=g.next,g.next=m}u.baseQueue=d=g,a.pending=null}if(d!==null){g=d.next,u=u.baseState;var x=m=null,A=null,H=g;do{var Z=H.lane;if((yn&Z)===Z)A!==null&&(A=A.next={lane:0,action:H.action,hasEagerState:H.hasEagerState,eagerState:H.eagerState,next:null}),u=H.hasEagerState?H.eagerState:t(u,H.action);else{var ie={lane:Z,action:H.action,hasEagerState:H.hasEagerState,eagerState:H.eagerState,next:null};A===null?(x=A=ie,m=u):A=A.next=ie,Te.lanes|=Z,_n|=Z}H=H.next}while(H!==null&&H!==g);A===null?m=u:A.next=x,Ct(u,n.memoizedState)||($e=!0),n.memoizedState=u,n.baseState=m,n.baseQueue=A,a.lastRenderedState=u}if(t=a.interleaved,t!==null){d=t;do g=d.lane,Te.lanes|=g,_n|=g,d=d.next;while(d!==t)}else d===null&&(a.lanes=0);return[n.memoizedState,a.dispatch]}function Ma(t){var n=gt(),a=n.queue;if(a===null)throw Error(h(311));a.lastRenderedReducer=t;var u=a.dispatch,d=a.pending,g=n.memoizedState;if(d!==null){a.pending=null;var m=d=d.next;do g=t(g,m.action),m=m.next;while(m!==d);Ct(g,n.memoizedState)||($e=!0),n.memoizedState=g,n.baseQueue===null&&(n.baseState=g),a.lastRenderedState=g}return[g,u]}function vo(){}function mo(t,n){var a=Te,u=gt(),d=n(),g=!Ct(u.memoizedState,d);if(g&&(u.memoizedState=d,$e=!0),u=u.queue,Ra(So.bind(null,a,u,t),[t]),u.getSnapshot!==n||g||Fe!==null&&Fe.memoizedState.tag&1){if(a.flags|=2048,dr(9,_o.bind(null,a,u,d,n),void 0,null),Be===null)throw Error(h(349));(yn&30)!==0||yo(a,n,d)}return d}function yo(t,n,a){t.flags|=16384,t={getSnapshot:n,value:a},n=Te.updateQueue,n===null?(n={lastEffect:null,stores:null},Te.updateQueue=n,n.stores=[t]):(a=n.stores,a===null?n.stores=[t]:a.push(t))}function _o(t,n,a,u){n.value=a,n.getSnapshot=u,wo(n)&&Co(t)}function So(t,n,a){return a(function(){wo(n)&&Co(t)})}function wo(t){var n=t.getSnapshot;t=t.value;try{var a=n();return!Ct(t,a)}catch{return!0}}function Co(t){var n=Lt(t,1);n!==null&&pt(n,t,1,-1)}function xo(t){var n=It();return typeof t=="function"&&(t=t()),n.memoizedState=n.baseState=t,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:hr,lastRenderedState:t},n.queue=t,t=t.dispatch=gd.bind(null,Te,t),[n.memoizedState,t]}function dr(t,n,a,u){return t={tag:t,create:n,destroy:a,deps:u,next:null},n=Te.updateQueue,n===null?(n={lastEffect:null,stores:null},Te.updateQueue=n,n.lastEffect=t.next=t):(a=n.lastEffect,a===null?n.lastEffect=t.next=t:(u=a.next,a.next=t,t.next=u,n.lastEffect=t)),t}function Eo(){return gt().memoizedState}function ni(t,n,a,u){var d=It();Te.flags|=t,d.memoizedState=dr(1|n,a,void 0,u===void 0?null:u)}function ri(t,n,a,u){var d=gt();u=u===void 0?null:u;var g=void 0;if(De!==null){var m=De.memoizedState;if(g=m.destroy,u!==null&&Ea(u,m.deps)){d.memoizedState=dr(n,a,g,u);return}}Te.flags|=t,d.memoizedState=dr(1|n,a,g,u)}function Po(t,n){return ni(8390656,8,t,n)}function Ra(t,n){return ri(2048,8,t,n)}function ko(t,n){return ri(4,2,t,n)}function To(t,n){return ri(4,4,t,n)}function Mo(t,n){if(typeof n=="function")return t=t(),n(t),function(){n(null)};if(n!=null)return t=t(),n.current=t,function(){n.current=null}}function Ro(t,n,a){return a=a!=null?a.concat([t]):null,ri(4,4,Mo.bind(null,n,t),a)}function Aa(){}function Ao(t,n){var a=gt();n=n===void 0?null:n;var u=a.memoizedState;return u!==null&&n!==null&&Ea(n,u[1])?u[0]:(a.memoizedState=[t,n],t)}function Lo(t,n){var a=gt();n=n===void 0?null:n;var u=a.memoizedState;return u!==null&&n!==null&&Ea(n,u[1])?u[0]:(t=t(),a.memoizedState=[t,n],t)}function No(t,n,a){return(yn&21)===0?(t.baseState&&(t.baseState=!1,$e=!0),t.memoizedState=a):(Ct(a,n)||(a=Js(),Te.lanes|=a,_n|=a,t.baseState=!0),n)}function cd(t,n){var a=ue;ue=a!==0&&4>a?a:4,t(!0);var u=xa.transition;xa.transition={};try{t(!1),n()}finally{ue=a,xa.transition=u}}function Io(){return gt().memoizedState}function fd(t,n,a){var u=rn(t);if(a={lane:u,action:a,hasEagerState:!1,eagerState:null,next:null},Oo(t))Do(n,a);else if(a=ho(t,n,a,u),a!==null){var d=qe();pt(a,t,u,d),Go(a,n,u)}}function gd(t,n,a){var u=rn(t),d={lane:u,action:a,hasEagerState:!1,eagerState:null,next:null};if(Oo(t))Do(n,d);else{var g=t.alternate;if(t.lanes===0&&(g===null||g.lanes===0)&&(g=n.lastRenderedReducer,g!==null))try{var m=n.lastRenderedState,x=g(m,a);if(d.hasEagerState=!0,d.eagerState=x,Ct(x,m)){var A=n.interleaved;A===null?(d.next=d,ma(n)):(d.next=A.next,A.next=d),n.interleaved=d;return}}catch{}finally{}a=ho(t,n,d,u),a!==null&&(d=qe(),pt(a,t,u,d),Go(a,n,u))}}function Oo(t){var n=t.alternate;return t===Te||n!==null&&n===Te}function Do(t,n){lr=ti=!0;var a=t.pending;a===null?n.next=n:(n.next=a.next,a.next=n),t.pending=n}function Go(t,n,a){if((a&4194240)!==0){var u=n.lanes;u&=t.pendingLanes,a|=u,n.lanes=a,ra(t,a)}}var ii={readContext:ct,useCallback:Ye,useContext:Ye,useEffect:Ye,useImperativeHandle:Ye,useInsertionEffect:Ye,useLayoutEffect:Ye,useMemo:Ye,useReducer:Ye,useRef:Ye,useState:Ye,useDebugValue:Ye,useDeferredValue:Ye,useTransition:Ye,useMutableSource:Ye,useSyncExternalStore:Ye,useId:Ye,unstable_isNewReconciler:!1},pd={readContext:ct,useCallback:function(t,n){return It().memoizedState=[t,n===void 0?null:n],t},useContext:ct,useEffect:Po,useImperativeHandle:function(t,n,a){return a=a!=null?a.concat([t]):null,ni(4194308,4,Mo.bind(null,n,t),a)},useLayoutEffect:function(t,n){return ni(4194308,4,t,n)},useInsertionEffect:function(t,n){return ni(4,2,t,n)},useMemo:function(t,n){var a=It();return n=n===void 0?null:n,t=t(),a.memoizedState=[t,n],t},useReducer:function(t,n,a){var u=It();return n=a!==void 0?a(n):n,u.memoizedState=u.baseState=n,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},u.queue=t,t=t.dispatch=fd.bind(null,Te,t),[u.memoizedState,t]},useRef:function(t){var n=It();return t={current:t},n.memoizedState=t},useState:xo,useDebugValue:Aa,useDeferredValue:function(t){return It().memoizedState=t},useTransition:function(){var t=xo(!1),n=t[0];return t=cd.bind(null,t[1]),It().memoizedState=t,[n,t]},useMutableSource:function(){},useSyncExternalStore:function(t,n,a){var u=Te,d=It();if(xe){if(a===void 0)throw Error(h(407));a=a()}else{if(a=n(),Be===null)throw Error(h(349));(yn&30)!==0||yo(u,n,a)}d.memoizedState=a;var g={value:a,getSnapshot:n};return d.queue=g,Po(So.bind(null,u,g,t),[t]),u.flags|=2048,dr(9,_o.bind(null,u,g,a,n),void 0,null),a},useId:function(){var t=It(),n=Be.identifierPrefix;if(xe){var a=Bt,u=Ft;a=(u&~(1<<32-wt(u)-1)).toString(32)+a,n=":"+n+"R"+a,a=ur++,0<a&&(n+="H"+a.toString(32)),n+=":"}else a=dd++,n=":"+n+"r"+a.toString(32)+":";return t.memoizedState=n},unstable_isNewReconciler:!1},vd={readContext:ct,useCallback:Ao,useContext:ct,useEffect:Ra,useImperativeHandle:Ro,useInsertionEffect:ko,useLayoutEffect:To,useMemo:Lo,useReducer:Ta,useRef:Eo,useState:function(){return Ta(hr)},useDebugValue:Aa,useDeferredValue:function(t){var n=gt();return No(n,De.memoizedState,t)},useTransition:function(){var t=Ta(hr)[0],n=gt().memoizedState;return[t,n]},useMutableSource:vo,useSyncExternalStore:mo,useId:Io,unstable_isNewReconciler:!1},md={readContext:ct,useCallback:Ao,useContext:ct,useEffect:Ra,useImperativeHandle:Ro,useInsertionEffect:ko,useLayoutEffect:To,useMemo:Lo,useReducer:Ma,useRef:Eo,useState:function(){return Ma(hr)},useDebugValue:Aa,useDeferredValue:function(t){var n=gt();return De===null?n.memoizedState=t:No(n,De.memoizedState,t)},useTransition:function(){var t=Ma(hr)[0],n=gt().memoizedState;return[t,n]},useMutableSource:vo,useSyncExternalStore:mo,useId:Io,unstable_isNewReconciler:!1};function Et(t,n){if(t&&t.defaultProps){n=l({},n),t=t.defaultProps;for(var a in t)n[a]===void 0&&(n[a]=t[a]);return n}return n}function La(t,n,a,u){n=t.memoizedState,a=a(u,n),a=a==null?n:l({},n,a),t.memoizedState=a,t.lanes===0&&(t.updateQueue.baseState=a)}var ai={isMounted:function(t){return(t=t._reactInternals)?Y(t)===t:!1},enqueueSetState:function(t,n,a){t=t._reactInternals;var u=qe(),d=rn(t),g=Ht(u,d);g.payload=n,a!=null&&(g.callback=a),n=en(t,g,d),n!==null&&(pt(n,t,d,u),Jr(n,t,d))},enqueueReplaceState:function(t,n,a){t=t._reactInternals;var u=qe(),d=rn(t),g=Ht(u,d);g.tag=1,g.payload=n,a!=null&&(g.callback=a),n=en(t,g,d),n!==null&&(pt(n,t,d,u),Jr(n,t,d))},enqueueForceUpdate:function(t,n){t=t._reactInternals;var a=qe(),u=rn(t),d=Ht(a,u);d.tag=2,n!=null&&(d.callback=n),n=en(t,d,u),n!==null&&(pt(n,t,u,a),Jr(n,t,u))}};function zo(t,n,a,u,d,g,m){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(u,g,m):n.prototype&&n.prototype.isPureReactComponent?!Vr(a,u)||!Vr(d,g):!0}function bo(t,n,a){var u=!1,d=Zt,g=n.contextType;return typeof g=="object"&&g!==null?g=ct(g):(d=Ze(n)?gn:Xe.current,u=n.contextTypes,g=(u=u!=null)?Nn(t,d):Zt),n=new n(a,g),t.memoizedState=n.state!==null&&n.state!==void 0?n.state:null,n.updater=ai,t.stateNode=n,n._reactInternals=t,u&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=d,t.__reactInternalMemoizedMaskedChildContext=g),n}function Fo(t,n,a,u){t=n.state,typeof n.componentWillReceiveProps=="function"&&n.componentWillReceiveProps(a,u),typeof n.UNSAFE_componentWillReceiveProps=="function"&&n.UNSAFE_componentWillReceiveProps(a,u),n.state!==t&&ai.enqueueReplaceState(n,n.state,null)}function Na(t,n,a,u){var d=t.stateNode;d.props=a,d.state=t.memoizedState,d.refs={},ya(t);var g=n.contextType;typeof g=="object"&&g!==null?d.context=ct(g):(g=Ze(n)?gn:Xe.current,d.context=Nn(t,g)),d.state=t.memoizedState,g=n.getDerivedStateFromProps,typeof g=="function"&&(La(t,n,g,a),d.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof d.getSnapshotBeforeUpdate=="function"||typeof d.UNSAFE_componentWillMount!="function"&&typeof d.componentWillMount!="function"||(n=d.state,typeof d.componentWillMount=="function"&&d.componentWillMount(),typeof d.UNSAFE_componentWillMount=="function"&&d.UNSAFE_componentWillMount(),n!==d.state&&ai.enqueueReplaceState(d,d.state,null),Zr(t,a,d,u),d.state=t.memoizedState),typeof d.componentDidMount=="function"&&(t.flags|=4194308)}function Hn(t,n){try{var a="",u=n;do a+=hd(u),u=u.return;while(u);var d=a}catch(g){d=`
Error generating stack: `+g.message+`
`+g.stack}return{value:t,source:n,stack:d,digest:null}}function Ia(t,n,a){return{value:t,source:null,stack:a??null,digest:n??null}}function Oa(t,n){try{console.error(n.value)}catch(a){setTimeout(function(){throw a})}}var yd=typeof WeakMap=="function"?WeakMap:Map;function Bo(t,n,a){a=Ht(-1,a),a.tag=3,a.payload={element:null};var u=n.value;return a.callback=function(){_i||(_i=!0,es=u),Oa(t,n)},a}function Ho(t,n,a){a=Ht(-1,a),a.tag=3;var u=t.type.getDerivedStateFromError;if(typeof u=="function"){var d=n.value;a.payload=function(){return u(d)},a.callback=function(){Oa(t,n)}}var g=t.stateNode;return g!==null&&typeof g.componentDidCatch=="function"&&(a.callback=function(){Oa(t,n),typeof u!="function"&&(tn===null?tn=new Set([this]):tn.add(this));var m=n.stack;this.componentDidCatch(n.value,{componentStack:m!==null?m:""})}),a}function Wo(t,n,a){var u=t.pingCache;if(u===null){u=t.pingCache=new yd;var d=new Set;u.set(n,d)}else d=u.get(n),d===void 0&&(d=new Set,u.set(n,d));d.has(a)||(d.add(a),t=Nd.bind(null,t,n,a),n.then(t,t))}function Uo(t){do{var n;if((n=t.tag===13)&&(n=t.memoizedState,n=n!==null?n.dehydrated!==null:!0),n)return t;t=t.return}while(t!==null);return null}function Xo(t,n,a,u,d){return(t.mode&1)===0?(t===n?t.flags|=65536:(t.flags|=128,a.flags|=131072,a.flags&=-52805,a.tag===1&&(a.alternate===null?a.tag=17:(n=Ht(-1,1),n.tag=2,en(a,n,1))),a.lanes|=1),t):(t.flags|=65536,t.lanes=d,t)}var _d=c.ReactCurrentOwner,$e=!1;function Qe(t,n,a,u){n.child=t===null?lo(n,null,a,u):Gn(n,t.child,a,u)}function Yo(t,n,a,u,d){a=a.render;var g=n.ref;return bn(n,d),u=Pa(t,n,a,u,g,d),a=ka(),t!==null&&!$e?(n.updateQueue=t.updateQueue,n.flags&=-2053,t.lanes&=~d,Wt(t,n,d)):(xe&&a&&la(n),n.flags|=1,Qe(t,n,u,d),n.child)}function jo(t,n,a,u,d){if(t===null){var g=a.type;return typeof g=="function"&&!ss(g)&&g.defaultProps===void 0&&a.compare===null&&a.defaultProps===void 0?(n.tag=15,n.type=g,Vo(t,n,g,u,d)):(t=Pi(a.type,null,u,n,n.mode,d),t.ref=n.ref,t.return=n,n.child=t)}if(g=t.child,(t.lanes&d)===0){var m=g.memoizedProps;if(a=a.compare,a=a!==null?a:Vr,a(m,u)&&t.ref===n.ref)return Wt(t,n,d)}return n.flags|=1,t=sn(g,u),t.ref=n.ref,t.return=n,n.child=t}function Vo(t,n,a,u,d){if(t!==null){var g=t.memoizedProps;if(Vr(g,u)&&t.ref===n.ref)if($e=!1,n.pendingProps=u=g,(t.lanes&d)!==0)(t.flags&131072)!==0&&($e=!0);else return n.lanes=t.lanes,Wt(t,n,d)}return Da(t,n,a,u,d)}function qo(t,n,a){var u=n.pendingProps,d=u.children,g=t!==null?t.memoizedState:null;if(u.mode==="hidden")if((n.mode&1)===0)n.memoizedState={baseLanes:0,cachePool:null,transitions:null},Se(Un,at),at|=a;else{if((a&1073741824)===0)return t=g!==null?g.baseLanes|a:a,n.lanes=n.childLanes=1073741824,n.memoizedState={baseLanes:t,cachePool:null,transitions:null},n.updateQueue=null,Se(Un,at),at|=t,null;n.memoizedState={baseLanes:0,cachePool:null,transitions:null},u=g!==null?g.baseLanes:a,Se(Un,at),at|=u}else g!==null?(u=g.baseLanes|a,n.memoizedState=null):u=a,Se(Un,at),at|=u;return Qe(t,n,d,a),n.child}function Qo(t,n){var a=n.ref;(t===null&&a!==null||t!==null&&t.ref!==a)&&(n.flags|=512,n.flags|=2097152)}function Da(t,n,a,u,d){var g=Ze(a)?gn:Xe.current;return g=Nn(n,g),bn(n,d),a=Pa(t,n,a,u,g,d),u=ka(),t!==null&&!$e?(n.updateQueue=t.updateQueue,n.flags&=-2053,t.lanes&=~d,Wt(t,n,d)):(xe&&u&&la(n),n.flags|=1,Qe(t,n,a,d),n.child)}function Ko(t,n,a,u,d){if(Ze(a)){var g=!0;br(n)}else g=!1;if(bn(n,d),n.stateNode===null)oi(t,n),bo(n,a,u),Na(n,a,u,d),u=!0;else if(t===null){var m=n.stateNode,x=n.memoizedProps;m.props=x;var A=m.context,H=a.contextType;typeof H=="object"&&H!==null?H=ct(H):(H=Ze(a)?gn:Xe.current,H=Nn(n,H));var Z=a.getDerivedStateFromProps,ie=typeof Z=="function"||typeof m.getSnapshotBeforeUpdate=="function";ie||typeof m.UNSAFE_componentWillReceiveProps!="function"&&typeof m.componentWillReceiveProps!="function"||(x!==u||A!==H)&&Fo(n,m,u,H),$t=!1;var q=n.memoizedState;m.state=q,Zr(n,u,m,d),A=n.memoizedState,x!==u||q!==A||Je.current||$t?(typeof Z=="function"&&(La(n,a,Z,u),A=n.memoizedState),(x=$t||zo(n,a,x,u,q,A,H))?(ie||typeof m.UNSAFE_componentWillMount!="function"&&typeof m.componentWillMount!="function"||(typeof m.componentWillMount=="function"&&m.componentWillMount(),typeof m.UNSAFE_componentWillMount=="function"&&m.UNSAFE_componentWillMount()),typeof m.componentDidMount=="function"&&(n.flags|=4194308)):(typeof m.componentDidMount=="function"&&(n.flags|=4194308),n.memoizedProps=u,n.memoizedState=A),m.props=u,m.state=A,m.context=H,u=x):(typeof m.componentDidMount=="function"&&(n.flags|=4194308),u=!1)}else{m=n.stateNode,co(t,n),x=n.memoizedProps,H=n.type===n.elementType?x:Et(n.type,x),m.props=H,ie=n.pendingProps,q=m.context,A=a.contextType,typeof A=="object"&&A!==null?A=ct(A):(A=Ze(a)?gn:Xe.current,A=Nn(n,A));var Ce=a.getDerivedStateFromProps;(Z=typeof Ce=="function"||typeof m.getSnapshotBeforeUpdate=="function")||typeof m.UNSAFE_componentWillReceiveProps!="function"&&typeof m.componentWillReceiveProps!="function"||(x!==ie||q!==A)&&Fo(n,m,u,A),$t=!1,q=n.memoizedState,m.state=q,Zr(n,u,m,d);var ye=n.memoizedState;x!==ie||q!==ye||Je.current||$t?(typeof Ce=="function"&&(La(n,a,Ce,u),ye=n.memoizedState),(H=$t||zo(n,a,H,u,q,ye,A)||!1)?(Z||typeof m.UNSAFE_componentWillUpdate!="function"&&typeof m.componentWillUpdate!="function"||(typeof m.componentWillUpdate=="function"&&m.componentWillUpdate(u,ye,A),typeof m.UNSAFE_componentWillUpdate=="function"&&m.UNSAFE_componentWillUpdate(u,ye,A)),typeof m.componentDidUpdate=="function"&&(n.flags|=4),typeof m.getSnapshotBeforeUpdate=="function"&&(n.flags|=1024)):(typeof m.componentDidUpdate!="function"||x===t.memoizedProps&&q===t.memoizedState||(n.flags|=4),typeof m.getSnapshotBeforeUpdate!="function"||x===t.memoizedProps&&q===t.memoizedState||(n.flags|=1024),n.memoizedProps=u,n.memoizedState=ye),m.props=u,m.state=ye,m.context=A,u=H):(typeof m.componentDidUpdate!="function"||x===t.memoizedProps&&q===t.memoizedState||(n.flags|=4),typeof m.getSnapshotBeforeUpdate!="function"||x===t.memoizedProps&&q===t.memoizedState||(n.flags|=1024),u=!1)}return Ga(t,n,a,u,g,d)}function Ga(t,n,a,u,d,g){Qo(t,n);var m=(n.flags&128)!==0;if(!u&&!m)return d&&Ks(n,a,!1),Wt(t,n,g);u=n.stateNode,_d.current=n;var x=m&&typeof a.getDerivedStateFromError!="function"?null:u.render();return n.flags|=1,t!==null&&m?(n.child=Gn(n,t.child,null,g),n.child=Gn(n,null,x,g)):Qe(t,n,x,g),n.memoizedState=u.state,d&&Ks(n,a,!0),n.child}function Jo(t){var n=t.stateNode;n.pendingContext?qs(t,n.pendingContext,n.pendingContext!==n.context):n.context&&qs(t,n.context,!1),_a(t,n.containerInfo)}function Zo(t,n,a,u,d){return Dn(),ca(d),n.flags|=256,Qe(t,n,a,u),n.child}var za={dehydrated:null,treeContext:null,retryLane:0};function ba(t){return{baseLanes:t,cachePool:null,transitions:null}}function $o(t,n,a){var u=n.pendingProps,d=ke.current,g=!1,m=(n.flags&128)!==0,x;if((x=m)||(x=t!==null&&t.memoizedState===null?!1:(d&2)!==0),x?(g=!0,n.flags&=-129):(t===null||t.memoizedState!==null)&&(d|=1),Se(ke,d&1),t===null)return da(n),t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null)?((n.mode&1)===0?n.lanes=1:Ki(t)?n.lanes=8:n.lanes=1073741824,null):(m=u.children,t=u.fallback,g?(u=n.mode,g=n.child,m={mode:"hidden",children:m},(u&1)===0&&g!==null?(g.childLanes=0,g.pendingProps=m):g=ki(m,u,0,null),t=xn(t,u,a,null),g.return=n,t.return=n,g.sibling=t,n.child=g,n.child.memoizedState=ba(a),n.memoizedState=za,t):Fa(n,m));if(d=t.memoizedState,d!==null&&(x=d.dehydrated,x!==null))return Sd(t,n,m,u,x,d,a);if(g){g=u.fallback,m=n.mode,d=t.child,x=d.sibling;var A={mode:"hidden",children:u.children};return(m&1)===0&&n.child!==d?(u=n.child,u.childLanes=0,u.pendingProps=A,n.deletions=null):(u=sn(d,A),u.subtreeFlags=d.subtreeFlags&14680064),x!==null?g=sn(x,g):(g=xn(g,m,a,null),g.flags|=2),g.return=n,u.return=n,u.sibling=g,n.child=u,u=g,g=n.child,m=t.child.memoizedState,m=m===null?ba(a):{baseLanes:m.baseLanes|a,cachePool:null,transitions:m.transitions},g.memoizedState=m,g.childLanes=t.childLanes&~a,n.memoizedState=za,u}return g=t.child,t=g.sibling,u=sn(g,{mode:"visible",children:u.children}),(n.mode&1)===0&&(u.lanes=a),u.return=n,u.sibling=null,t!==null&&(a=n.deletions,a===null?(n.deletions=[t],n.flags|=16):a.push(t)),n.child=u,n.memoizedState=null,u}function Fa(t,n){return n=ki({mode:"visible",children:n},t.mode,0,null),n.return=t,t.child=n}function si(t,n,a,u){return u!==null&&ca(u),Gn(n,t.child,null,a),t=Fa(n,n.pendingProps.children),t.flags|=2,n.memoizedState=null,t}function Sd(t,n,a,u,d,g,m){if(a)return n.flags&256?(n.flags&=-257,u=Ia(Error(h(422))),si(t,n,m,u)):n.memoizedState!==null?(n.child=t.child,n.flags|=128,null):(g=u.fallback,d=n.mode,u=ki({mode:"visible",children:u.children},d,0,null),g=xn(g,d,m,null),g.flags|=2,u.return=n,g.return=n,u.sibling=g,n.child=u,(n.mode&1)!==0&&Gn(n,t.child,null,m),n.child.memoizedState=ba(m),n.memoizedState=za,g);if((n.mode&1)===0)return si(t,n,m,null);if(Ki(d))return u=Ih(d).digest,g=Error(h(419)),u=Ia(g,u,void 0),si(t,n,m,u);if(a=(m&t.childLanes)!==0,$e||a){if(u=Be,u!==null){switch(m&-m){case 4:d=2;break;case 16:d=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:d=32;break;case 536870912:d=268435456;break;default:d=0}d=(d&(u.suspendedLanes|m))!==0?0:d,d!==0&&d!==g.retryLane&&(g.retryLane=d,Lt(t,d),pt(u,t,d,-1))}return as(),u=Ia(Error(h(421))),si(t,n,m,u)}return Vs(d)?(n.flags|=128,n.child=t.child,n=Id.bind(null,t),Oh(d,n),null):(t=g.treeContext,lt&&(dt=zh(d),it=n,xe=!0,xt=null,ir=!1,t!==null&&(ut[ht++]=Ft,ut[ht++]=Bt,ut[ht++]=pn,Ft=t.id,Bt=t.overflow,pn=n)),n=Fa(n,u.children),n.flags|=4096,n)}function el(t,n,a){t.lanes|=n;var u=t.alternate;u!==null&&(u.lanes|=n),va(t.return,n,a)}function Ba(t,n,a,u,d){var g=t.memoizedState;g===null?t.memoizedState={isBackwards:n,rendering:null,renderingStartTime:0,last:u,tail:a,tailMode:d}:(g.isBackwards=n,g.rendering=null,g.renderingStartTime=0,g.last=u,g.tail=a,g.tailMode=d)}function tl(t,n,a){var u=n.pendingProps,d=u.revealOrder,g=u.tail;if(Qe(t,n,u.children,a),u=ke.current,(u&2)!==0)u=u&1|2,n.flags|=128;else{if(t!==null&&(t.flags&128)!==0)e:for(t=n.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&el(t,a,n);else if(t.tag===19)el(t,a,n);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===n)break e;for(;t.sibling===null;){if(t.return===null||t.return===n)break e;t=t.return}t.sibling.return=t.return,t=t.sibling}u&=1}if(Se(ke,u),(n.mode&1)===0)n.memoizedState=null;else switch(d){case"forwards":for(a=n.child,d=null;a!==null;)t=a.alternate,t!==null&&$r(t)===null&&(d=a),a=a.sibling;a=d,a===null?(d=n.child,n.child=null):(d=a.sibling,a.sibling=null),Ba(n,!1,d,a,g);break;case"backwards":for(a=null,d=n.child,n.child=null;d!==null;){if(t=d.alternate,t!==null&&$r(t)===null){n.child=d;break}t=d.sibling,d.sibling=a,a=d,d=t}Ba(n,!0,a,null,g);break;case"together":Ba(n,!1,null,null,void 0);break;default:n.memoizedState=null}return n.child}function oi(t,n){(n.mode&1)===0&&t!==null&&(t.alternate=null,n.alternate=null,n.flags|=2)}function Wt(t,n,a){if(t!==null&&(n.dependencies=t.dependencies),_n|=n.lanes,(a&n.childLanes)===0)return null;if(t!==null&&n.child!==t.child)throw Error(h(153));if(n.child!==null){for(t=n.child,a=sn(t,t.pendingProps),n.child=a,a.return=n;t.sibling!==null;)t=t.sibling,a=a.sibling=sn(t,t.pendingProps),a.return=n;a.sibling=null}return n.child}function wd(t,n,a){switch(n.tag){case 3:Jo(n),Dn();break;case 5:po(n);break;case 1:Ze(n.type)&&br(n);break;case 4:_a(n,n.stateNode.containerInfo);break;case 10:uo(n,n.type._context,n.memoizedProps.value);break;case 13:var u=n.memoizedState;if(u!==null)return u.dehydrated!==null?(Se(ke,ke.current&1),n.flags|=128,null):(a&n.child.childLanes)!==0?$o(t,n,a):(Se(ke,ke.current&1),t=Wt(t,n,a),t!==null?t.sibling:null);Se(ke,ke.current&1);break;case 19:if(u=(a&n.childLanes)!==0,(t.flags&128)!==0){if(u)return tl(t,n,a);n.flags|=128}var d=n.memoizedState;if(d!==null&&(d.rendering=null,d.tail=null,d.lastEffect=null),Se(ke,ke.current),u)break;return null;case 22:case 23:return n.lanes=0,qo(t,n,a)}return Wt(t,n,a)}function Ot(t){t.flags|=4}function nl(t,n){if(t!==null&&t.child===n.child)return!0;if((n.flags&16)!==0)return!1;for(t=n.child;t!==null;){if((t.flags&12854)!==0||(t.subtreeFlags&12854)!==0)return!1;t=t.sibling}return!0}var cr,fr,li,ui;if(rt)cr=function(t,n){for(var a=n.child;a!==null;){if(a.tag===5||a.tag===6)Pe(t,a.stateNode);else if(a.tag!==4&&a.child!==null){a.child.return=a,a=a.child;continue}if(a===n)break;for(;a.sibling===null;){if(a.return===null||a.return===n)return;a=a.return}a.sibling.return=a.return,a=a.sibling}},fr=function(){},li=function(t,n,a,u,d){if(t=t.memoizedProps,t!==u){var g=n.stateNode,m=Nt(ft.current);a=St(g,a,t,u,d,m),(n.updateQueue=a)&&Ot(n)}},ui=function(t,n,a,u){a!==u&&Ot(n)};else if(Dr){cr=function(t,n,a,u){for(var d=n.child;d!==null;){if(d.tag===5){var g=d.stateNode;a&&u&&(g=Ys(g,d.type,d.memoizedProps,d)),Pe(t,g)}else if(d.tag===6)g=d.stateNode,a&&u&&(g=js(g,d.memoizedProps,d)),Pe(t,g);else if(d.tag!==4){if(d.tag===22&&d.memoizedState!==null)g=d.child,g!==null&&(g.return=d),cr(t,d,!0,!0);else if(d.child!==null){d.child.return=d,d=d.child;continue}}if(d===n)break;for(;d.sibling===null;){if(d.return===null||d.return===n)return;d=d.return}d.sibling.return=d.return,d=d.sibling}};var rl=function(t,n,a,u){for(var d=n.child;d!==null;){if(d.tag===5){var g=d.stateNode;a&&u&&(g=Ys(g,d.type,d.memoizedProps,d)),Xs(t,g)}else if(d.tag===6)g=d.stateNode,a&&u&&(g=js(g,d.memoizedProps,d)),Xs(t,g);else if(d.tag!==4){if(d.tag===22&&d.memoizedState!==null)g=d.child,g!==null&&(g.return=d),rl(t,d,!0,!0);else if(d.child!==null){d.child.return=d,d=d.child;continue}}if(d===n)break;for(;d.sibling===null;){if(d.return===null||d.return===n)return;d=d.return}d.sibling.return=d.return,d=d.sibling}};fr=function(t,n){var a=n.stateNode;if(!nl(t,n)){t=a.containerInfo;var u=Us(t);rl(u,n,!1,!1),a.pendingChildren=u,Ot(n),Rh(t,u)}},li=function(t,n,a,u,d){var g=t.stateNode,m=t.memoizedProps;if((t=nl(t,n))&&m===u)n.stateNode=g;else{var x=n.stateNode,A=Nt(ft.current),H=null;m!==u&&(H=St(x,a,m,u,d,A)),t&&H===null?n.stateNode=g:(g=Mh(g,H,a,m,u,n,t,x),_t(g,a,u,d,A)&&Ot(n),n.stateNode=g,t?Ot(n):cr(g,n,!1,!1))}},ui=function(t,n,a,u){a!==u?(t=Nt(Fn.current),a=Nt(ft.current),n.stateNode=Bs(u,t,a,n),Ot(n)):n.stateNode=t.stateNode}}else fr=function(){},li=function(){},ui=function(){};function gr(t,n){if(!xe)switch(t.tailMode){case"hidden":n=t.tail;for(var a=null;n!==null;)n.alternate!==null&&(a=n),n=n.sibling;a===null?t.tail=null:a.sibling=null;break;case"collapsed":a=t.tail;for(var u=null;a!==null;)a.alternate!==null&&(u=a),a=a.sibling;u===null?n||t.tail===null?t.tail=null:t.tail.sibling=null:u.sibling=null}}function je(t){var n=t.alternate!==null&&t.alternate.child===t.child,a=0,u=0;if(n)for(var d=t.child;d!==null;)a|=d.lanes|d.childLanes,u|=d.subtreeFlags&14680064,u|=d.flags&14680064,d.return=t,d=d.sibling;else for(d=t.child;d!==null;)a|=d.lanes|d.childLanes,u|=d.subtreeFlags,u|=d.flags,d.return=t,d=d.sibling;return t.subtreeFlags|=u,t.childLanes=a,n}function Cd(t,n,a){var u=n.pendingProps;switch(ua(n),n.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return je(n),null;case 1:return Ze(n.type)&&zr(),je(n),null;case 3:return a=n.stateNode,Bn(),we(Je),we(Xe),Ca(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(t===null||t.child===null)&&(jr(n)?Ot(n):t===null||t.memoizedState.isDehydrated&&(n.flags&256)===0||(n.flags|=1024,xt!==null&&(rs(xt),xt=null))),fr(t,n),je(n),null;case 5:Sa(n),a=Nt(Fn.current);var d=n.type;if(t!==null&&n.stateNode!=null)li(t,n,d,u,a),t.ref!==n.ref&&(n.flags|=512,n.flags|=2097152);else{if(!u){if(n.stateNode===null)throw Error(h(166));return je(n),null}if(t=Nt(ft.current),jr(n)){if(!lt)throw Error(h(175));t=bh(n.stateNode,n.type,n.memoizedProps,a,t,n,!ir),n.updateQueue=t,t!==null&&Ot(n)}else{var g=pe(d,u,a,t,n);cr(g,n,!1,!1),n.stateNode=g,_t(g,d,u,a,t)&&Ot(n)}n.ref!==null&&(n.flags|=512,n.flags|=2097152)}return je(n),null;case 6:if(t&&n.stateNode!=null)ui(t,n,t.memoizedProps,u);else{if(typeof u!="string"&&n.stateNode===null)throw Error(h(166));if(t=Nt(Fn.current),a=Nt(ft.current),jr(n)){if(!lt)throw Error(h(176));if(t=n.stateNode,a=n.memoizedProps,(u=Fh(t,a,n,!ir))&&(d=it,d!==null))switch(d.tag){case 3:Vh(d.stateNode.containerInfo,t,a,(d.mode&1)!==0);break;case 5:qh(d.type,d.memoizedProps,d.stateNode,t,a,(d.mode&1)!==0)}u&&Ot(n)}else n.stateNode=Bs(u,t,a,n)}return je(n),null;case 13:if(we(ke),u=n.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(xe&&dt!==null&&(n.mode&1)!==0&&(n.flags&128)===0)ao(),Dn(),n.flags|=98560,d=!1;else if(d=jr(n),u!==null&&u.dehydrated!==null){if(t===null){if(!d)throw Error(h(318));if(!lt)throw Error(h(344));if(d=n.memoizedState,d=d!==null?d.dehydrated:null,!d)throw Error(h(317));Bh(d,n)}else Dn(),(n.flags&128)===0&&(n.memoizedState=null),n.flags|=4;je(n),d=!1}else xt!==null&&(rs(xt),xt=null),d=!0;if(!d)return n.flags&65536?n:null}return(n.flags&128)!==0?(n.lanes=a,n):(a=u!==null,a!==(t!==null&&t.memoizedState!==null)&&a&&(n.child.flags|=8192,(n.mode&1)!==0&&(t===null||(ke.current&1)!==0?Ge===0&&(Ge=3):as())),n.updateQueue!==null&&(n.flags|=4),je(n),null);case 4:return Bn(),fr(t,n),t===null&&rh(n.stateNode.containerInfo),je(n),null;case 10:return pa(n.type._context),je(n),null;case 17:return Ze(n.type)&&zr(),je(n),null;case 19:if(we(ke),d=n.memoizedState,d===null)return je(n),null;if(u=(n.flags&128)!==0,g=d.rendering,g===null)if(u)gr(d,!1);else{if(Ge!==0||t!==null&&(t.flags&128)!==0)for(t=n.child;t!==null;){if(g=$r(t),g!==null){for(n.flags|=128,gr(d,!1),t=g.updateQueue,t!==null&&(n.updateQueue=t,n.flags|=4),n.subtreeFlags=0,t=a,a=n.child;a!==null;)u=a,d=t,u.flags&=14680066,g=u.alternate,g===null?(u.childLanes=0,u.lanes=d,u.child=null,u.subtreeFlags=0,u.memoizedProps=null,u.memoizedState=null,u.updateQueue=null,u.dependencies=null,u.stateNode=null):(u.childLanes=g.childLanes,u.lanes=g.lanes,u.child=g.child,u.subtreeFlags=0,u.deletions=null,u.memoizedProps=g.memoizedProps,u.memoizedState=g.memoizedState,u.updateQueue=g.updateQueue,u.type=g.type,d=g.dependencies,u.dependencies=d===null?null:{lanes:d.lanes,firstContext:d.firstContext}),a=a.sibling;return Se(ke,ke.current&1|2),n.child}t=t.sibling}d.tail!==null&&be()>$a&&(n.flags|=128,u=!0,gr(d,!1),n.lanes=4194304)}else{if(!u)if(t=$r(g),t!==null){if(n.flags|=128,u=!0,t=t.updateQueue,t!==null&&(n.updateQueue=t,n.flags|=4),gr(d,!0),d.tail===null&&d.tailMode==="hidden"&&!g.alternate&&!xe)return je(n),null}else 2*be()-d.renderingStartTime>$a&&a!==1073741824&&(n.flags|=128,u=!0,gr(d,!1),n.lanes=4194304);d.isBackwards?(g.sibling=n.child,n.child=g):(t=d.last,t!==null?t.sibling=g:n.child=g,d.last=g)}return d.tail!==null?(n=d.tail,d.rendering=n,d.tail=n.sibling,d.renderingStartTime=be(),n.sibling=null,t=ke.current,Se(ke,u?t&1|2:t&1),n):(je(n),null);case 22:case 23:return is(),a=n.memoizedState!==null,t!==null&&t.memoizedState!==null!==a&&(n.flags|=8192),a&&(n.mode&1)!==0?(at&1073741824)!==0&&(je(n),rt&&n.subtreeFlags&6&&(n.flags|=8192)):je(n),null;case 24:return null;case 25:return null}throw Error(h(156,n.tag))}function xd(t,n){switch(ua(n),n.tag){case 1:return Ze(n.type)&&zr(),t=n.flags,t&65536?(n.flags=t&-65537|128,n):null;case 3:return Bn(),we(Je),we(Xe),Ca(),t=n.flags,(t&65536)!==0&&(t&128)===0?(n.flags=t&-65537|128,n):null;case 5:return Sa(n),null;case 13:if(we(ke),t=n.memoizedState,t!==null&&t.dehydrated!==null){if(n.alternate===null)throw Error(h(340));Dn()}return t=n.flags,t&65536?(n.flags=t&-65537|128,n):null;case 19:return we(ke),null;case 4:return Bn(),null;case 10:return pa(n.type._context),null;case 22:case 23:return is(),null;case 24:return null;default:return null}}var hi=!1,Ve=!1,Ed=typeof WeakSet=="function"?WeakSet:Set,K=null;function Wn(t,n){var a=t.ref;if(a!==null)if(typeof a=="function")try{a(null)}catch(u){Ee(t,n,u)}else a.current=null}function Ha(t,n,a){try{a()}catch(u){Ee(t,n,u)}}var il=!1;function Pd(t,n){for(Le(t.containerInfo),K=n;K!==null;)if(t=K,n=t.child,(t.subtreeFlags&1028)!==0&&n!==null)n.return=t,K=n;else for(;K!==null;){t=K;try{var a=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(a!==null){var u=a.memoizedProps,d=a.memoizedState,g=t.stateNode,m=g.getSnapshotBeforeUpdate(t.elementType===t.type?u:Et(t.type,u),d);g.__reactInternalSnapshotBeforeUpdate=m}break;case 3:rt&&Th(t.stateNode.containerInfo);break;case 5:case 6:case 4:case 17:break;default:throw Error(h(163))}}catch(x){Ee(t,t.return,x)}if(n=t.sibling,n!==null){n.return=t.return,K=n;break}K=t.return}return a=il,il=!1,a}function pr(t,n,a){var u=n.updateQueue;if(u=u!==null?u.lastEffect:null,u!==null){var d=u=u.next;do{if((d.tag&t)===t){var g=d.destroy;d.destroy=void 0,g!==void 0&&Ha(n,a,g)}d=d.next}while(d!==u)}}function di(t,n){if(n=n.updateQueue,n=n!==null?n.lastEffect:null,n!==null){var a=n=n.next;do{if((a.tag&t)===t){var u=a.create;a.destroy=u()}a=a.next}while(a!==n)}}function Wa(t){var n=t.ref;if(n!==null){var a=t.stateNode;switch(t.tag){case 5:t=$(a);break;default:t=a}typeof n=="function"?n(t):n.current=t}}function al(t){var n=t.alternate;n!==null&&(t.alternate=null,al(n)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(n=t.stateNode,n!==null&&ah(n)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}function sl(t){return t.tag===5||t.tag===3||t.tag===4}function ol(t){e:for(;;){for(;t.sibling===null;){if(t.return===null||sl(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.flags&2||t.child===null||t.tag===4)continue e;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Ua(t,n,a){var u=t.tag;if(u===5||u===6)t=t.stateNode,n?Sh(a,t,n):ph(a,t);else if(u!==4&&(t=t.child,t!==null))for(Ua(t,n,a),t=t.sibling;t!==null;)Ua(t,n,a),t=t.sibling}function Xa(t,n,a){var u=t.tag;if(u===5||u===6)t=t.stateNode,n?_h(a,t,n):gh(a,t);else if(u!==4&&(t=t.child,t!==null))for(Xa(t,n,a),t=t.sibling;t!==null;)Xa(t,n,a),t=t.sibling}var We=null,Pt=!1;function Dt(t,n,a){for(a=a.child;a!==null;)Ya(t,n,a),a=a.sibling}function Ya(t,n,a){if(Rt&&typeof Rt.onCommitFiberUnmount=="function")try{Rt.onCommitFiberUnmount(Wr,a)}catch{}switch(a.tag){case 5:Ve||Wn(a,n);case 6:if(rt){var u=We,d=Pt;We=null,Dt(t,n,a),We=u,Pt=d,We!==null&&(Pt?Ch(We,a.stateNode):wh(We,a.stateNode))}else Dt(t,n,a);break;case 18:rt&&We!==null&&(Pt?Yh(We,a.stateNode):Xh(We,a.stateNode));break;case 4:rt?(u=We,d=Pt,We=a.stateNode.containerInfo,Pt=!0,Dt(t,n,a),We=u,Pt=d):(Dr&&(u=a.stateNode.containerInfo,d=Us(u),Qi(u,d)),Dt(t,n,a));break;case 0:case 11:case 14:case 15:if(!Ve&&(u=a.updateQueue,u!==null&&(u=u.lastEffect,u!==null))){d=u=u.next;do{var g=d,m=g.destroy;g=g.tag,m!==void 0&&((g&2)!==0||(g&4)!==0)&&Ha(a,n,m),d=d.next}while(d!==u)}Dt(t,n,a);break;case 1:if(!Ve&&(Wn(a,n),u=a.stateNode,typeof u.componentWillUnmount=="function"))try{u.props=a.memoizedProps,u.state=a.memoizedState,u.componentWillUnmount()}catch(x){Ee(a,n,x)}Dt(t,n,a);break;case 21:Dt(t,n,a);break;case 22:a.mode&1?(Ve=(u=Ve)||a.memoizedState!==null,Dt(t,n,a),Ve=u):Dt(t,n,a);break;default:Dt(t,n,a)}}function ll(t){var n=t.updateQueue;if(n!==null){t.updateQueue=null;var a=t.stateNode;a===null&&(a=t.stateNode=new Ed),n.forEach(function(u){var d=Od.bind(null,t,u);a.has(u)||(a.add(u),u.then(d,d))})}}function kt(t,n){var a=n.deletions;if(a!==null)for(var u=0;u<a.length;u++){var d=a[u];try{var g=t,m=n;if(rt){var x=m;e:for(;x!==null;){switch(x.tag){case 5:We=x.stateNode,Pt=!1;break e;case 3:We=x.stateNode.containerInfo,Pt=!0;break e;case 4:We=x.stateNode.containerInfo,Pt=!0;break e}x=x.return}if(We===null)throw Error(h(160));Ya(g,m,d),We=null,Pt=!1}else Ya(g,m,d);var A=d.alternate;A!==null&&(A.return=null),d.return=null}catch(H){Ee(d,n,H)}}if(n.subtreeFlags&12854)for(n=n.child;n!==null;)ul(n,t),n=n.sibling}function ul(t,n){var a=t.alternate,u=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:if(kt(n,t),Gt(t),u&4){try{pr(3,t,t.return),di(3,t)}catch(q){Ee(t,t.return,q)}try{pr(5,t,t.return)}catch(q){Ee(t,t.return,q)}}break;case 1:kt(n,t),Gt(t),u&512&&a!==null&&Wn(a,a.return);break;case 5:if(kt(n,t),Gt(t),u&512&&a!==null&&Wn(a,a.return),rt){if(t.flags&32){var d=t.stateNode;try{Ws(d)}catch(q){Ee(t,t.return,q)}}if(u&4&&(d=t.stateNode,d!=null)){var g=t.memoizedProps;if(a=a!==null?a.memoizedProps:g,u=t.type,n=t.updateQueue,t.updateQueue=null,n!==null)try{yh(d,n,u,a,g,t)}catch(q){Ee(t,t.return,q)}}}break;case 6:if(kt(n,t),Gt(t),u&4&&rt){if(t.stateNode===null)throw Error(h(162));d=t.stateNode,g=t.memoizedProps,a=a!==null?a.memoizedProps:g;try{vh(d,a,g)}catch(q){Ee(t,t.return,q)}}break;case 3:if(kt(n,t),Gt(t),u&4){if(rt&&lt&&a!==null&&a.memoizedState.isDehydrated)try{Wh(n.containerInfo)}catch(q){Ee(t,t.return,q)}if(Dr){d=n.containerInfo,g=n.pendingChildren;try{Qi(d,g)}catch(q){Ee(t,t.return,q)}}}break;case 4:if(kt(n,t),Gt(t),u&4&&Dr){g=t.stateNode,d=g.containerInfo,g=g.pendingChildren;try{Qi(d,g)}catch(q){Ee(t,t.return,q)}}break;case 13:kt(n,t),Gt(t),d=t.child,d.flags&8192&&(g=d.memoizedState!==null,d.stateNode.isHidden=g,!g||d.alternate!==null&&d.alternate.memoizedState!==null||(Za=be())),u&4&&ll(t);break;case 22:var m=a!==null&&a.memoizedState!==null;if(t.mode&1?(Ve=(a=Ve)||m,kt(n,t),Ve=a):kt(n,t),Gt(t),u&8192){if(a=t.memoizedState!==null,(t.stateNode.isHidden=a)&&!m&&(t.mode&1)!==0)for(K=t,u=t.child;u!==null;){for(n=K=u;K!==null;){m=K;var x=m.child;switch(m.tag){case 0:case 11:case 14:case 15:pr(4,m,m.return);break;case 1:Wn(m,m.return);var A=m.stateNode;if(typeof A.componentWillUnmount=="function"){var H=m,Z=m.return;try{var ie=H;A.props=ie.memoizedProps,A.state=ie.memoizedState,A.componentWillUnmount()}catch(q){Ee(H,Z,q)}}break;case 5:Wn(m,m.return);break;case 22:if(m.memoizedState!==null){cl(n);continue}}x!==null?(x.return=m,K=x):cl(n)}u=u.sibling}if(rt){e:if(u=null,rt)for(n=t;;){if(n.tag===5){if(u===null){u=n;try{d=n.stateNode,a?xh(d):Ph(n.stateNode,n.memoizedProps)}catch(q){Ee(t,t.return,q)}}}else if(n.tag===6){if(u===null)try{g=n.stateNode,a?Eh(g):kh(g,n.memoizedProps)}catch(q){Ee(t,t.return,q)}}else if((n.tag!==22&&n.tag!==23||n.memoizedState===null||n===t)&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break e;for(;n.sibling===null;){if(n.return===null||n.return===t)break e;u===n&&(u=null),n=n.return}u===n&&(u=null),n.sibling.return=n.return,n=n.sibling}}}break;case 19:kt(n,t),Gt(t),u&4&&ll(t);break;case 21:break;default:kt(n,t),Gt(t)}}function Gt(t){var n=t.flags;if(n&2){try{if(rt){e:{for(var a=t.return;a!==null;){if(sl(a)){var u=a;break e}a=a.return}throw Error(h(160))}switch(u.tag){case 5:var d=u.stateNode;u.flags&32&&(Ws(d),u.flags&=-33);var g=ol(t);Xa(t,g,d);break;case 3:case 4:var m=u.stateNode.containerInfo,x=ol(t);Ua(t,x,m);break;default:throw Error(h(161))}}}catch(A){Ee(t,t.return,A)}t.flags&=-3}n&4096&&(t.flags&=-4097)}function kd(t,n,a){K=t,hl(t)}function hl(t,n,a){for(var u=(t.mode&1)!==0;K!==null;){var d=K,g=d.child;if(d.tag===22&&u){var m=d.memoizedState!==null||hi;if(!m){var x=d.alternate,A=x!==null&&x.memoizedState!==null||Ve;x=hi;var H=Ve;if(hi=m,(Ve=A)&&!H)for(K=d;K!==null;)m=K,A=m.child,m.tag===22&&m.memoizedState!==null?fl(d):A!==null?(A.return=m,K=A):fl(d);for(;g!==null;)K=g,hl(g),g=g.sibling;K=d,hi=x,Ve=H}dl(t)}else(d.subtreeFlags&8772)!==0&&g!==null?(g.return=d,K=g):dl(t)}}function dl(t){for(;K!==null;){var n=K;if((n.flags&8772)!==0){var a=n.alternate;try{if((n.flags&8772)!==0)switch(n.tag){case 0:case 11:case 15:Ve||di(5,n);break;case 1:var u=n.stateNode;if(n.flags&4&&!Ve)if(a===null)u.componentDidMount();else{var d=n.elementType===n.type?a.memoizedProps:Et(n.type,a.memoizedProps);u.componentDidUpdate(d,a.memoizedState,u.__reactInternalSnapshotBeforeUpdate)}var g=n.updateQueue;g!==null&&go(n,g,u);break;case 3:var m=n.updateQueue;if(m!==null){if(a=null,n.child!==null)switch(n.child.tag){case 5:a=$(n.child.stateNode);break;case 1:a=n.child.stateNode}go(n,m,a)}break;case 5:var x=n.stateNode;a===null&&n.flags&4&&mh(x,n.type,n.memoizedProps,n);break;case 6:break;case 4:break;case 12:break;case 13:if(lt&&n.memoizedState===null){var A=n.alternate;if(A!==null){var H=A.memoizedState;if(H!==null){var Z=H.dehydrated;Z!==null&&Uh(Z)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(h(163))}Ve||n.flags&512&&Wa(n)}catch(ie){Ee(n,n.return,ie)}}if(n===t){K=null;break}if(a=n.sibling,a!==null){a.return=n.return,K=a;break}K=n.return}}function cl(t){for(;K!==null;){var n=K;if(n===t){K=null;break}var a=n.sibling;if(a!==null){a.return=n.return,K=a;break}K=n.return}}function fl(t){for(;K!==null;){var n=K;try{switch(n.tag){case 0:case 11:case 15:var a=n.return;try{di(4,n)}catch(A){Ee(n,a,A)}break;case 1:var u=n.stateNode;if(typeof u.componentDidMount=="function"){var d=n.return;try{u.componentDidMount()}catch(A){Ee(n,d,A)}}var g=n.return;try{Wa(n)}catch(A){Ee(n,g,A)}break;case 5:var m=n.return;try{Wa(n)}catch(A){Ee(n,m,A)}}}catch(A){Ee(n,n.return,A)}if(n===t){K=null;break}var x=n.sibling;if(x!==null){x.return=n.return,K=x;break}K=n.return}}var ci=0,fi=1,gi=2,pi=3,vi=4;if(typeof Symbol=="function"&&Symbol.for){var vr=Symbol.for;ci=vr("selector.component"),fi=vr("selector.has_pseudo_class"),gi=vr("selector.role"),pi=vr("selector.test_id"),vi=vr("selector.text")}function ja(t){var n=nh(t);if(n!=null){if(typeof n.memoizedProps["data-testname"]!="string")throw Error(h(364));return n}if(t=lh(t),t===null)throw Error(h(362));return t.stateNode.current}function Va(t,n){switch(n.$$typeof){case ci:if(t.type===n.value)return!0;break;case fi:e:{n=n.value,t=[t,0];for(var a=0;a<t.length;){var u=t[a++],d=t[a++],g=n[d];if(u.tag!==5||!er(u)){for(;g!=null&&Va(u,g);)d++,g=n[d];if(d===n.length){n=!0;break e}else for(u=u.child;u!==null;)t.push(u,d),u=u.sibling}}n=!1}return n;case gi:if(t.tag===5&&dh(t.stateNode,n.value))return!0;break;case vi:if((t.tag===5||t.tag===6)&&(t=hh(t),t!==null&&0<=t.indexOf(n.value)))return!0;break;case pi:if(t.tag===5&&(t=t.memoizedProps["data-testname"],typeof t=="string"&&t.toLowerCase()===n.value.toLowerCase()))return!0;break;default:throw Error(h(365))}return!1}function qa(t){switch(t.$$typeof){case ci:return"<"+(X(t.value)||"Unknown")+">";case fi:return":has("+(qa(t)||"")+")";case gi:return'[role="'+t.value+'"]';case vi:return'"'+t.value+'"';case pi:return'[data-testname="'+t.value+'"]';default:throw Error(h(365))}}function gl(t,n){var a=[];t=[t,0];for(var u=0;u<t.length;){var d=t[u++],g=t[u++],m=n[g];if(d.tag!==5||!er(d)){for(;m!=null&&Va(d,m);)g++,m=n[g];if(g===n.length)a.push(d);else for(d=d.child;d!==null;)t.push(d,g),d=d.sibling}}return a}function Qa(t,n){if(!$n)throw Error(h(363));t=ja(t),t=gl(t,n),n=[],t=Array.from(t);for(var a=0;a<t.length;){var u=t[a++];if(u.tag===5)er(u)||n.push(u.stateNode);else for(u=u.child;u!==null;)t.push(u),u=u.sibling}return n}var Td=Math.ceil,mi=c.ReactCurrentDispatcher,Ka=c.ReactCurrentOwner,Ne=c.ReactCurrentBatchConfig,oe=0,Be=null,Oe=null,Ue=0,at=0,Un=Jt(0),Ge=0,mr=null,_n=0,yi=0,Ja=0,yr=null,et=null,Za=0,$a=1/0,Ut=null;function Xn(){$a=be()+500}var _i=!1,es=null,tn=null,Si=!1,nn=null,wi=0,_r=0,ts=null,Ci=-1,xi=0;function qe(){return(oe&6)!==0?be():Ci!==-1?Ci:Ci=be()}function rn(t){return(t.mode&1)===0?1:(oe&2)!==0&&Ue!==0?Ue&-Ue:ud.transition!==null?(xi===0&&(xi=Js()),xi):(t=ue,t!==0?t:ih())}function pt(t,n,a,u){if(50<_r)throw _r=0,ts=null,Error(h(185));rr(t,a,u),((oe&2)===0||t!==Be)&&(t===Be&&((oe&2)===0&&(yi|=a),Ge===4&&an(t,Ue)),tt(t,u),a===1&&oe===0&&(n.mode&1)===0&&(Xn(),Ur&&At()))}function tt(t,n){var a=t.callbackNode;ed(t,n);var u=Hr(t,t===Be?Ue:0);if(u===0)a!==null&&$s(a),t.callbackNode=null,t.callbackPriority=0;else if(n=u&-u,t.callbackPriority!==n){if(a!=null&&$s(a),n===1)t.tag===0?ld(vl.bind(null,t)):eo(vl.bind(null,t)),sh?oh(function(){(oe&6)===0&&At()}):ia(aa,At),a=null;else{switch(Zs(u)){case 1:a=aa;break;case 4:a=id;break;case 16:a=sa;break;case 536870912:a=ad;break;default:a=sa}a=Pl(a,pl.bind(null,t))}t.callbackPriority=n,t.callbackNode=a}}function pl(t,n){if(Ci=-1,xi=0,(oe&6)!==0)throw Error(h(327));var a=t.callbackNode;if(Cn()&&t.callbackNode!==a)return null;var u=Hr(t,t===Be?Ue:0);if(u===0)return null;if((u&30)!==0||(u&t.expiredLanes)!==0||n)n=Ei(t,u);else{n=u;var d=oe;oe|=2;var g=_l();(Be!==t||Ue!==n)&&(Ut=null,Xn(),Sn(t,n));do try{Ad();break}catch(x){yl(t,x)}while(!0);ga(),mi.current=g,oe=d,Oe!==null?n=0:(Be=null,Ue=0,n=Ge)}if(n!==0){if(n===2&&(d=ta(t),d!==0&&(u=d,n=ns(t,d))),n===1)throw a=mr,Sn(t,0),an(t,u),tt(t,be()),a;if(n===6)an(t,u);else{if(d=t.current.alternate,(u&30)===0&&!Md(d)&&(n=Ei(t,u),n===2&&(g=ta(t),g!==0&&(u=g,n=ns(t,g))),n===1))throw a=mr,Sn(t,0),an(t,u),tt(t,be()),a;switch(t.finishedWork=d,t.finishedLanes=u,n){case 0:case 1:throw Error(h(345));case 2:wn(t,et,Ut);break;case 3:if(an(t,u),(u&130023424)===u&&(n=Za+500-be(),10<n)){if(Hr(t,0)!==0)break;if(d=t.suspendedLanes,(d&u)!==u){qe(),t.pingedLanes|=t.suspendedLanes&d;break}t.timeoutHandle=Hs(wn.bind(null,t,et,Ut),n);break}wn(t,et,Ut);break;case 4:if(an(t,u),(u&4194240)===u)break;for(n=t.eventTimes,d=-1;0<u;){var m=31-wt(u);g=1<<m,m=n[m],m>d&&(d=m),u&=~g}if(u=d,u=be()-u,u=(120>u?120:480>u?480:1080>u?1080:1920>u?1920:3e3>u?3e3:4320>u?4320:1960*Td(u/1960))-u,10<u){t.timeoutHandle=Hs(wn.bind(null,t,et,Ut),u);break}wn(t,et,Ut);break;case 5:wn(t,et,Ut);break;default:throw Error(h(329))}}}return tt(t,be()),t.callbackNode===a?pl.bind(null,t):null}function ns(t,n){var a=yr;return t.current.memoizedState.isDehydrated&&(Sn(t,n).flags|=256),t=Ei(t,n),t!==2&&(n=et,et=a,n!==null&&rs(n)),t}function rs(t){et===null?et=t:et.push.apply(et,t)}function Md(t){for(var n=t;;){if(n.flags&16384){var a=n.updateQueue;if(a!==null&&(a=a.stores,a!==null))for(var u=0;u<a.length;u++){var d=a[u],g=d.getSnapshot;d=d.value;try{if(!Ct(g(),d))return!1}catch{return!1}}}if(a=n.child,n.subtreeFlags&16384&&a!==null)a.return=n,n=a;else{if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return!0;n=n.return}n.sibling.return=n.return,n=n.sibling}}return!0}function an(t,n){for(n&=~Ja,n&=~yi,t.suspendedLanes|=n,t.pingedLanes&=~n,t=t.expirationTimes;0<n;){var a=31-wt(n),u=1<<a;t[a]=-1,n&=~u}}function vl(t){if((oe&6)!==0)throw Error(h(327));Cn();var n=Hr(t,0);if((n&1)===0)return tt(t,be()),null;var a=Ei(t,n);if(t.tag!==0&&a===2){var u=ta(t);u!==0&&(n=u,a=ns(t,u))}if(a===1)throw a=mr,Sn(t,0),an(t,n),tt(t,be()),a;if(a===6)throw Error(h(345));return t.finishedWork=t.current.alternate,t.finishedLanes=n,wn(t,et,Ut),tt(t,be()),null}function ml(t){nn!==null&&nn.tag===0&&(oe&6)===0&&Cn();var n=oe;oe|=1;var a=Ne.transition,u=ue;try{if(Ne.transition=null,ue=1,t)return t()}finally{ue=u,Ne.transition=a,oe=n,(oe&6)===0&&At()}}function is(){at=Un.current,we(Un)}function Sn(t,n){t.finishedWork=null,t.finishedLanes=0;var a=t.timeoutHandle;if(a!==qi&&(t.timeoutHandle=qi,th(a)),Oe!==null)for(a=Oe.return;a!==null;){var u=a;switch(ua(u),u.tag){case 1:u=u.type.childContextTypes,u!=null&&zr();break;case 3:Bn(),we(Je),we(Xe),Ca();break;case 5:Sa(u);break;case 4:Bn();break;case 13:we(ke);break;case 19:we(ke);break;case 10:pa(u.type._context);break;case 22:case 23:is()}a=a.return}if(Be=t,Oe=t=sn(t.current,null),Ue=at=n,Ge=0,mr=null,Ja=yi=_n=0,et=yr=null,mn!==null){for(n=0;n<mn.length;n++)if(a=mn[n],u=a.interleaved,u!==null){a.interleaved=null;var d=u.next,g=a.pending;if(g!==null){var m=g.next;g.next=d,u.next=m}a.pending=u}mn=null}return t}function yl(t,n){do{var a=Oe;try{if(ga(),ei.current=ii,ti){for(var u=Te.memoizedState;u!==null;){var d=u.queue;d!==null&&(d.pending=null),u=u.next}ti=!1}if(yn=0,Fe=De=Te=null,lr=!1,ur=0,Ka.current=null,a===null||a.return===null){Ge=1,mr=n,Oe=null;break}e:{var g=t,m=a.return,x=a,A=n;if(n=Ue,x.flags|=32768,A!==null&&typeof A=="object"&&typeof A.then=="function"){var H=A,Z=x,ie=Z.tag;if((Z.mode&1)===0&&(ie===0||ie===11||ie===15)){var q=Z.alternate;q?(Z.updateQueue=q.updateQueue,Z.memoizedState=q.memoizedState,Z.lanes=q.lanes):(Z.updateQueue=null,Z.memoizedState=null)}var Ce=Uo(m);if(Ce!==null){Ce.flags&=-257,Xo(Ce,m,x,g,n),Ce.mode&1&&Wo(g,H,n),n=Ce,A=H;var ye=n.updateQueue;if(ye===null){var nt=new Set;nt.add(A),n.updateQueue=nt}else ye.add(A);break e}else{if((n&1)===0){Wo(g,H,n),as();break e}A=Error(h(426))}}else if(xe&&x.mode&1){var Xt=Uo(m);if(Xt!==null){(Xt.flags&65536)===0&&(Xt.flags|=256),Xo(Xt,m,x,g,n),ca(Hn(A,x));break e}}g=A=Hn(A,x),Ge!==4&&(Ge=2),yr===null?yr=[g]:yr.push(g),g=m;do{switch(g.tag){case 3:g.flags|=65536,n&=-n,g.lanes|=n;var R=Bo(g,A,n);fo(g,R);break e;case 1:x=A;var P=g.type,L=g.stateNode;if((g.flags&128)===0&&(typeof P.getDerivedStateFromError=="function"||L!==null&&typeof L.componentDidCatch=="function"&&(tn===null||!tn.has(L)))){g.flags|=65536,n&=-n,g.lanes|=n;var Q=Ho(g,x,n);fo(g,Q);break e}}g=g.return}while(g!==null)}wl(a)}catch(ee){n=ee,Oe===a&&a!==null&&(Oe=a=a.return);continue}break}while(!0)}function _l(){var t=mi.current;return mi.current=ii,t===null?ii:t}function as(){(Ge===0||Ge===3||Ge===2)&&(Ge=4),Be===null||(_n&268435455)===0&&(yi&268435455)===0||an(Be,Ue)}function Ei(t,n){var a=oe;oe|=2;var u=_l();(Be!==t||Ue!==n)&&(Ut=null,Sn(t,n));do try{Rd();break}catch(d){yl(t,d)}while(!0);if(ga(),oe=a,mi.current=u,Oe!==null)throw Error(h(261));return Be=null,Ue=0,Ge}function Rd(){for(;Oe!==null;)Sl(Oe)}function Ad(){for(;Oe!==null&&!nd();)Sl(Oe)}function Sl(t){var n=El(t.alternate,t,at);t.memoizedProps=t.pendingProps,n===null?wl(t):Oe=n,Ka.current=null}function wl(t){var n=t;do{var a=n.alternate;if(t=n.return,(n.flags&32768)===0){if(a=Cd(a,n,at),a!==null){Oe=a;return}}else{if(a=xd(a,n),a!==null){a.flags&=32767,Oe=a;return}if(t!==null)t.flags|=32768,t.subtreeFlags=0,t.deletions=null;else{Ge=6,Oe=null;return}}if(n=n.sibling,n!==null){Oe=n;return}Oe=n=t}while(n!==null);Ge===0&&(Ge=5)}function wn(t,n,a){var u=ue,d=Ne.transition;try{Ne.transition=null,ue=1,Ld(t,n,a,u)}finally{Ne.transition=d,ue=u}return null}function Ld(t,n,a,u){do Cn();while(nn!==null);if((oe&6)!==0)throw Error(h(327));a=t.finishedWork;var d=t.finishedLanes;if(a===null)return null;if(t.finishedWork=null,t.finishedLanes=0,a===t.current)throw Error(h(177));t.callbackNode=null,t.callbackPriority=0;var g=a.lanes|a.childLanes;if(td(t,g),t===Be&&(Oe=Be=null,Ue=0),(a.subtreeFlags&2064)===0&&(a.flags&2064)===0||Si||(Si=!0,Pl(sa,function(){return Cn(),null})),g=(a.flags&15990)!==0,(a.subtreeFlags&15990)!==0||g){g=Ne.transition,Ne.transition=null;var m=ue;ue=1;var x=oe;oe|=4,Ka.current=null,Pd(t,a),ul(a,t),re(t.containerInfo),t.current=a,kd(a),rd(),oe=x,ue=m,Ne.transition=g}else t.current=a;if(Si&&(Si=!1,nn=t,wi=d),g=t.pendingLanes,g===0&&(tn=null),sd(a.stateNode),tt(t,be()),n!==null)for(u=t.onRecoverableError,a=0;a<n.length;a++)d=n[a],u(d.value,{componentStack:d.stack,digest:d.digest});if(_i)throw _i=!1,t=es,es=null,t;return(wi&1)!==0&&t.tag!==0&&Cn(),g=t.pendingLanes,(g&1)!==0?t===ts?_r++:(_r=0,ts=t):_r=0,At(),null}function Cn(){if(nn!==null){var t=Zs(wi),n=Ne.transition,a=ue;try{if(Ne.transition=null,ue=16>t?16:t,nn===null)var u=!1;else{if(t=nn,nn=null,wi=0,(oe&6)!==0)throw Error(h(331));var d=oe;for(oe|=4,K=t.current;K!==null;){var g=K,m=g.child;if((K.flags&16)!==0){var x=g.deletions;if(x!==null){for(var A=0;A<x.length;A++){var H=x[A];for(K=H;K!==null;){var Z=K;switch(Z.tag){case 0:case 11:case 15:pr(8,Z,g)}var ie=Z.child;if(ie!==null)ie.return=Z,K=ie;else for(;K!==null;){Z=K;var q=Z.sibling,Ce=Z.return;if(al(Z),Z===H){K=null;break}if(q!==null){q.return=Ce,K=q;break}K=Ce}}}var ye=g.alternate;if(ye!==null){var nt=ye.child;if(nt!==null){ye.child=null;do{var Xt=nt.sibling;nt.sibling=null,nt=Xt}while(nt!==null)}}K=g}}if((g.subtreeFlags&2064)!==0&&m!==null)m.return=g,K=m;else e:for(;K!==null;){if(g=K,(g.flags&2048)!==0)switch(g.tag){case 0:case 11:case 15:pr(9,g,g.return)}var R=g.sibling;if(R!==null){R.return=g.return,K=R;break e}K=g.return}}var P=t.current;for(K=P;K!==null;){m=K;var L=m.child;if((m.subtreeFlags&2064)!==0&&L!==null)L.return=m,K=L;else e:for(m=P;K!==null;){if(x=K,(x.flags&2048)!==0)try{switch(x.tag){case 0:case 11:case 15:di(9,x)}}catch(ee){Ee(x,x.return,ee)}if(x===m){K=null;break e}var Q=x.sibling;if(Q!==null){Q.return=x.return,K=Q;break e}K=x.return}}if(oe=d,At(),Rt&&typeof Rt.onPostCommitFiberRoot=="function")try{Rt.onPostCommitFiberRoot(Wr,t)}catch{}u=!0}return u}finally{ue=a,Ne.transition=n}}return!1}function Cl(t,n,a){n=Hn(a,n),n=Bo(t,n,1),t=en(t,n,1),n=qe(),t!==null&&(rr(t,1,n),tt(t,n))}function Ee(t,n,a){if(t.tag===3)Cl(t,t,a);else for(;n!==null;){if(n.tag===3){Cl(n,t,a);break}else if(n.tag===1){var u=n.stateNode;if(typeof n.type.getDerivedStateFromError=="function"||typeof u.componentDidCatch=="function"&&(tn===null||!tn.has(u))){t=Hn(a,t),t=Ho(n,t,1),n=en(n,t,1),t=qe(),n!==null&&(rr(n,1,t),tt(n,t));break}}n=n.return}}function Nd(t,n,a){var u=t.pingCache;u!==null&&u.delete(n),n=qe(),t.pingedLanes|=t.suspendedLanes&a,Be===t&&(Ue&a)===a&&(Ge===4||Ge===3&&(Ue&130023424)===Ue&&500>be()-Za?Sn(t,0):Ja|=a),tt(t,n)}function xl(t,n){n===0&&((t.mode&1)===0?n=1:(n=Br,Br<<=1,(Br&130023424)===0&&(Br=4194304)));var a=qe();t=Lt(t,n),t!==null&&(rr(t,n,a),tt(t,a))}function Id(t){var n=t.memoizedState,a=0;n!==null&&(a=n.retryLane),xl(t,a)}function Od(t,n){var a=0;switch(t.tag){case 13:var u=t.stateNode,d=t.memoizedState;d!==null&&(a=d.retryLane);break;case 19:u=t.stateNode;break;default:throw Error(h(314))}u!==null&&u.delete(n),xl(t,a)}var El;El=function(t,n,a){if(t!==null)if(t.memoizedProps!==n.pendingProps||Je.current)$e=!0;else{if((t.lanes&a)===0&&(n.flags&128)===0)return $e=!1,wd(t,n,a);$e=(t.flags&131072)!==0}else $e=!1,xe&&(n.flags&1048576)!==0&&to(n,Yr,n.index);switch(n.lanes=0,n.tag){case 2:var u=n.type;oi(t,n),t=n.pendingProps;var d=Nn(n,Xe.current);bn(n,a),d=Pa(null,n,u,t,d,a);var g=ka();return n.flags|=1,typeof d=="object"&&d!==null&&typeof d.render=="function"&&d.$$typeof===void 0?(n.tag=1,n.memoizedState=null,n.updateQueue=null,Ze(u)?(g=!0,br(n)):g=!1,n.memoizedState=d.state!==null&&d.state!==void 0?d.state:null,ya(n),d.updater=ai,n.stateNode=d,d._reactInternals=n,Na(n,u,t,a),n=Ga(null,n,u,!0,g,a)):(n.tag=0,xe&&g&&la(n),Qe(null,n,d,a),n=n.child),n;case 16:u=n.elementType;e:{switch(oi(t,n),t=n.pendingProps,d=u._init,u=d(u._payload),n.type=u,d=n.tag=Gd(u),t=Et(u,t),d){case 0:n=Da(null,n,u,t,a);break e;case 1:n=Ko(null,n,u,t,a);break e;case 11:n=Yo(null,n,u,t,a);break e;case 14:n=jo(null,n,u,Et(u.type,t),a);break e}throw Error(h(306,u,""))}return n;case 0:return u=n.type,d=n.pendingProps,d=n.elementType===u?d:Et(u,d),Da(t,n,u,d,a);case 1:return u=n.type,d=n.pendingProps,d=n.elementType===u?d:Et(u,d),Ko(t,n,u,d,a);case 3:e:{if(Jo(n),t===null)throw Error(h(387));u=n.pendingProps,g=n.memoizedState,d=g.element,co(t,n),Zr(n,u,null,a);var m=n.memoizedState;if(u=m.element,lt&&g.isDehydrated)if(g={element:u,isDehydrated:!1,cache:m.cache,pendingSuspenseBoundaries:m.pendingSuspenseBoundaries,transitions:m.transitions},n.updateQueue.baseState=g,n.memoizedState=g,n.flags&256){d=Hn(Error(h(423)),n),n=Zo(t,n,u,a,d);break e}else if(u!==d){d=Hn(Error(h(424)),n),n=Zo(t,n,u,a,d);break e}else for(lt&&(dt=Gh(n.stateNode.containerInfo),it=n,xe=!0,xt=null,ir=!1),a=lo(n,null,u,a),n.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling;else{if(Dn(),u===d){n=Wt(t,n,a);break e}Qe(t,n,u,a)}n=n.child}return n;case 5:return po(n),t===null&&da(n),u=n.type,d=n.pendingProps,g=t!==null?t.memoizedProps:null,m=d.children,Vi(u,d)?m=null:g!==null&&Vi(u,g)&&(n.flags|=32),Qo(t,n),Qe(t,n,m,a),n.child;case 6:return t===null&&da(n),null;case 13:return $o(t,n,a);case 4:return _a(n,n.stateNode.containerInfo),u=n.pendingProps,t===null?n.child=Gn(n,null,u,a):Qe(t,n,u,a),n.child;case 11:return u=n.type,d=n.pendingProps,d=n.elementType===u?d:Et(u,d),Yo(t,n,u,d,a);case 7:return Qe(t,n,n.pendingProps,a),n.child;case 8:return Qe(t,n,n.pendingProps.children,a),n.child;case 12:return Qe(t,n,n.pendingProps.children,a),n.child;case 10:e:{if(u=n.type._context,d=n.pendingProps,g=n.memoizedProps,m=d.value,uo(n,u,m),g!==null)if(Ct(g.value,m)){if(g.children===d.children&&!Je.current){n=Wt(t,n,a);break e}}else for(g=n.child,g!==null&&(g.return=n);g!==null;){var x=g.dependencies;if(x!==null){m=g.child;for(var A=x.firstContext;A!==null;){if(A.context===u){if(g.tag===1){A=Ht(-1,a&-a),A.tag=2;var H=g.updateQueue;if(H!==null){H=H.shared;var Z=H.pending;Z===null?A.next=A:(A.next=Z.next,Z.next=A),H.pending=A}}g.lanes|=a,A=g.alternate,A!==null&&(A.lanes|=a),va(g.return,a,n),x.lanes|=a;break}A=A.next}}else if(g.tag===10)m=g.type===n.type?null:g.child;else if(g.tag===18){if(m=g.return,m===null)throw Error(h(341));m.lanes|=a,x=m.alternate,x!==null&&(x.lanes|=a),va(m,a,n),m=g.sibling}else m=g.child;if(m!==null)m.return=g;else for(m=g;m!==null;){if(m===n){m=null;break}if(g=m.sibling,g!==null){g.return=m.return,m=g;break}m=m.return}g=m}Qe(t,n,d.children,a),n=n.child}return n;case 9:return d=n.type,u=n.pendingProps.children,bn(n,a),d=ct(d),u=u(d),n.flags|=1,Qe(t,n,u,a),n.child;case 14:return u=n.type,d=Et(u,n.pendingProps),d=Et(u.type,d),jo(t,n,u,d,a);case 15:return Vo(t,n,n.type,n.pendingProps,a);case 17:return u=n.type,d=n.pendingProps,d=n.elementType===u?d:Et(u,d),oi(t,n),n.tag=1,Ze(u)?(t=!0,br(n)):t=!1,bn(n,a),bo(n,u,d),Na(n,u,d,a),Ga(null,n,u,!0,t,a);case 19:return tl(t,n,a);case 22:return qo(t,n,a)}throw Error(h(156,n.tag))};function Pl(t,n){return ia(t,n)}function Dd(t,n,a,u){this.tag=t,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=n,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=u,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function vt(t,n,a,u){return new Dd(t,n,a,u)}function ss(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Gd(t){if(typeof t=="function")return ss(t)?1:0;if(t!=null){if(t=t.$$typeof,t===M)return 11;if(t===D)return 14}return 2}function sn(t,n){var a=t.alternate;return a===null?(a=vt(t.tag,n,t.key,t.mode),a.elementType=t.elementType,a.type=t.type,a.stateNode=t.stateNode,a.alternate=t,t.alternate=a):(a.pendingProps=n,a.type=t.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=t.flags&14680064,a.childLanes=t.childLanes,a.lanes=t.lanes,a.child=t.child,a.memoizedProps=t.memoizedProps,a.memoizedState=t.memoizedState,a.updateQueue=t.updateQueue,n=t.dependencies,a.dependencies=n===null?null:{lanes:n.lanes,firstContext:n.firstContext},a.sibling=t.sibling,a.index=t.index,a.ref=t.ref,a}function Pi(t,n,a,u,d,g){var m=2;if(u=t,typeof t=="function")ss(t)&&(m=1);else if(typeof t=="string")m=5;else e:switch(t){case v:return xn(a.children,d,g,n);case _:m=8,d|=8;break;case y:return t=vt(12,a,n,d|2),t.elementType=y,t.lanes=g,t;case T:return t=vt(13,a,n,d),t.elementType=T,t.lanes=g,t;case G:return t=vt(19,a,n,d),t.elementType=G,t.lanes=g,t;case E:return ki(a,d,g,n);default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case C:m=10;break e;case S:m=9;break e;case M:m=11;break e;case D:m=14;break e;case I:m=16,u=null;break e}throw Error(h(130,t==null?t:typeof t,""))}return n=vt(m,a,n,d),n.elementType=t,n.type=u,n.lanes=g,n}function xn(t,n,a,u){return t=vt(7,t,u,n),t.lanes=a,t}function ki(t,n,a,u){return t=vt(22,t,u,n),t.elementType=E,t.lanes=a,t.stateNode={isHidden:!1},t}function os(t,n,a){return t=vt(6,t,null,n),t.lanes=a,t}function ls(t,n,a){return n=vt(4,t.children!==null?t.children:[],t.key,n),n.lanes=a,n.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},n}function zd(t,n,a,u,d){this.tag=n,this.containerInfo=t,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=qi,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=na(0),this.expirationTimes=na(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=na(0),this.identifierPrefix=u,this.onRecoverableError=d,lt&&(this.mutableSourceEagerHydrationData=null)}function kl(t,n,a,u,d,g,m,x,A){return t=new zd(t,n,a,x,A),n===1?(n=1,g===!0&&(n|=8)):n=0,g=vt(3,null,null,n),t.current=g,g.stateNode=t,g.memoizedState={element:u,isDehydrated:a,cache:null,transitions:null,pendingSuspenseBoundaries:null},ya(g),t}function Tl(t){if(!t)return Zt;t=t._reactInternals;e:{if(Y(t)!==t||t.tag!==1)throw Error(h(170));var n=t;do{switch(n.tag){case 3:n=n.stateNode.context;break e;case 1:if(Ze(n.type)){n=n.stateNode.__reactInternalMemoizedMergedChildContext;break e}}n=n.return}while(n!==null);throw Error(h(171))}if(t.tag===1){var a=t.type;if(Ze(a))return Qs(t,a,n)}return n}function Ml(t){var n=t._reactInternals;if(n===void 0)throw typeof t.render=="function"?Error(h(188)):(t=Object.keys(t).join(","),Error(h(268,t)));return t=V(n),t===null?null:t.stateNode}function Rl(t,n){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var a=t.retryLane;t.retryLane=a!==0&&a<n?a:n}}function Ti(t,n){Rl(t,n),(t=t.alternate)&&Rl(t,n)}function bd(t){return t=V(t),t===null?null:t.stateNode}function Fd(){return null}return r.attemptContinuousHydration=function(t){if(t.tag===13){var n=Lt(t,134217728);if(n!==null){var a=qe();pt(n,t,134217728,a)}Ti(t,134217728)}},r.attemptDiscreteHydration=function(t){if(t.tag===13){var n=Lt(t,1);if(n!==null){var a=qe();pt(n,t,1,a)}Ti(t,1)}},r.attemptHydrationAtCurrentPriority=function(t){if(t.tag===13){var n=rn(t),a=Lt(t,n);if(a!==null){var u=qe();pt(a,t,n,u)}Ti(t,n)}},r.attemptSynchronousHydration=function(t){switch(t.tag){case 3:var n=t.stateNode;if(n.current.memoizedState.isDehydrated){var a=nr(n.pendingLanes);a!==0&&(ra(n,a|1),tt(n,be()),(oe&6)===0&&(Xn(),At()))}break;case 13:ml(function(){var u=Lt(t,1);if(u!==null){var d=qe();pt(u,t,1,d)}}),Ti(t,1)}},r.batchedUpdates=function(t,n){var a=oe;oe|=1;try{return t(n)}finally{oe=a,oe===0&&(Xn(),Ur&&At())}},r.createComponentSelector=function(t){return{$$typeof:ci,value:t}},r.createContainer=function(t,n,a,u,d,g,m){return kl(t,n,!1,null,a,u,d,g,m)},r.createHasPseudoClassSelector=function(t){return{$$typeof:fi,value:t}},r.createHydrationContainer=function(t,n,a,u,d,g,m,x,A){return t=kl(a,u,!0,t,d,g,m,x,A),t.context=Tl(null),a=t.current,u=qe(),d=rn(a),g=Ht(u,d),g.callback=n??null,en(a,g,d),t.current.lanes=d,rr(t,d,u),tt(t,u),t},r.createPortal=function(t,n,a){var u=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:p,key:u==null?null:""+u,children:t,containerInfo:n,implementation:a}},r.createRoleSelector=function(t){return{$$typeof:gi,value:t}},r.createTestNameSelector=function(t){return{$$typeof:pi,value:t}},r.createTextSelector=function(t){return{$$typeof:vi,value:t}},r.deferredUpdates=function(t){var n=ue,a=Ne.transition;try{return Ne.transition=null,ue=16,t()}finally{ue=n,Ne.transition=a}},r.discreteUpdates=function(t,n,a,u,d){var g=ue,m=Ne.transition;try{return Ne.transition=null,ue=1,t(n,a,u,d)}finally{ue=g,Ne.transition=m,oe===0&&Xn()}},r.findAllNodes=Qa,r.findBoundingRects=function(t,n){if(!$n)throw Error(h(363));n=Qa(t,n),t=[];for(var a=0;a<n.length;a++)t.push(uh(n[a]));for(n=t.length-1;0<n;n--){a=t[n];for(var u=a.x,d=u+a.width,g=a.y,m=g+a.height,x=n-1;0<=x;x--)if(n!==x){var A=t[x],H=A.x,Z=H+A.width,ie=A.y,q=ie+A.height;if(u>=H&&g>=ie&&d<=Z&&m<=q){t.splice(n,1);break}else if(u!==H||a.width!==A.width||q<g||ie>m){if(!(g!==ie||a.height!==A.height||Z<u||H>d)){H>u&&(A.width+=H-u,A.x=u),Z<d&&(A.width=d-H),t.splice(n,1);break}}else{ie>g&&(A.height+=ie-g,A.y=g),q<m&&(A.height=m-ie),t.splice(n,1);break}}}return t},r.findHostInstance=Ml,r.findHostInstanceWithNoPortals=function(t){return t=N(t),t=t!==null?te(t):null,t===null?null:t.stateNode},r.findHostInstanceWithWarning=function(t){return Ml(t)},r.flushControlled=function(t){var n=oe;oe|=1;var a=Ne.transition,u=ue;try{Ne.transition=null,ue=1,t()}finally{ue=u,Ne.transition=a,oe=n,oe===0&&(Xn(),At())}},r.flushPassiveEffects=Cn,r.flushSync=ml,r.focusWithin=function(t,n){if(!$n)throw Error(h(363));for(t=ja(t),n=gl(t,n),n=Array.from(n),t=0;t<n.length;){var a=n[t++];if(!er(a)){if(a.tag===5&&ch(a.stateNode))return!0;for(a=a.child;a!==null;)n.push(a),a=a.sibling}}return!1},r.getCurrentUpdatePriority=function(){return ue},r.getFindAllNodesFailureDescription=function(t,n){if(!$n)throw Error(h(363));var a=0,u=[];t=[ja(t),0];for(var d=0;d<t.length;){var g=t[d++],m=t[d++],x=n[m];if((g.tag!==5||!er(g))&&(Va(g,x)&&(u.push(qa(x)),m++,m>a&&(a=m)),m<n.length))for(g=g.child;g!==null;)t.push(g,m),g=g.sibling}if(a<n.length){for(t=[];a<n.length;a++)t.push(qa(n[a]));return`findAllNodes was able to match part of the selector:
  `+(u.join(" > ")+`

No matching component was found for:
  `)+t.join(" > ")}return null},r.getPublicRootInstance=function(t){if(t=t.current,!t.child)return null;switch(t.child.tag){case 5:return $(t.child.stateNode);default:return t.child.stateNode}},r.injectIntoDevTools=function(t){if(t={bundleType:t.bundleType,version:t.version,rendererPackageName:t.rendererPackageName,rendererConfig:t.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:c.ReactCurrentDispatcher,findHostInstanceByFiber:bd,findFiberByHostInstance:t.findFiberByHostInstance||Fd,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1"},typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u")t=!1;else{var n=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(n.isDisabled||!n.supportsFiber)t=!0;else{try{Wr=n.inject(t),Rt=n}catch{}t=!!n.checkDCE}}return t},r.isAlreadyRendering=function(){return!1},r.observeVisibleRects=function(t,n,a,u){if(!$n)throw Error(h(363));t=Qa(t,n);var d=fh(t,a,u).disconnect;return{disconnect:function(){d()}}},r.registerMutableSourceForHydration=function(t,n){var a=n._getVersion;a=a(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a)},r.runWithPriority=function(t,n){var a=ue;try{return ue=t,n()}finally{ue=a}},r.shouldError=function(){return null},r.shouldSuspend=function(){return!1},r.updateContainer=function(t,n,a,u){var d=n.current,g=qe(),m=rn(d);return a=Tl(a),n.context===null?n.context=a:n.pendingContext=a,n=Ht(g,m),n.payload={element:t},u=u===void 0?null:u,u!==null&&(n.callback=u),t=en(d,n,m),t!==null&&(pt(t,d,m,g),Jr(t,d,m)),m},r}),_s}var $l;function rg(){return $l||($l=1,ys.exports=ng()),ys.exports}var ig=rg();const ag=Wd(ig);var Ss={exports:{}},ln={};/**
 * @license React
 * react-reconciler-constants.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var eu;function sg(){return eu||(eu=1,ln.ConcurrentRoot=1,ln.ContinuousEventPriority=4,ln.DefaultEventPriority=16,ln.DiscreteEventPriority=1,ln.IdleEventPriority=536870912,ln.LegacyRoot=0),ln}var tu;function og(){return tu||(tu=1,Ss.exports=sg()),Ss.exports}var qu=og();const nu={children:!0,ref:!0,key:!0,style:!0,forwardedRef:!0,unstable_applyCache:!0,unstable_applyDrawHitFromCache:!0};let ru=!1,iu=!1;const zs=".react-konva-event",lg=`ReactKonva: You have a Konva node with draggable = true and position defined but no onDragMove or onDragEnd events are handled.
Position of a node will be changed during drag&drop, so you should update state of the react app as well.
Consider to add onDragMove or onDragEnd events.
For more info see: https://github.com/konvajs/react-konva/issues/256
`,ug=`ReactKonva: You are using "zIndex" attribute for a Konva node.
react-konva may get confused with ordering. Just define correct order of elements in your render function of a component.
For more info see: https://github.com/konvajs/react-konva/issues/194
`,hg={};function ji(o,e,r=hg){if(!ru&&"zIndex"in e&&(console.warn(ug),ru=!0),!iu&&e.draggable){var i=e.x!==void 0||e.y!==void 0,s=e.onDragEnd||e.onDragMove;i&&!s&&(console.warn(lg),iu=!0)}for(var l in r)if(!nu[l]){var h=l.slice(0,2)==="on",c=r[l]!==e[l];if(h&&c){var f=l.substr(2).toLowerCase();f.substr(0,7)==="content"&&(f="content"+f.substr(7,1).toUpperCase()+f.substr(8)),o.off(f,r[l])}var p=!e.hasOwnProperty(l);p&&o.setAttr(l,void 0)}var v=e._useStrictMode,_={},y=!1;const C={};for(var l in e)if(!nu[l]){var h=l.slice(0,2)==="on",S=r[l]!==e[l];if(h&&S){var f=l.substr(2).toLowerCase();f.substr(0,7)==="content"&&(f="content"+f.substr(7,1).toUpperCase()+f.substr(8)),e[l]&&(C[f]=e[l])}!h&&(e[l]!==r[l]||v&&e[l]!==o.getAttr(l))&&(y=!0,_[l]=e[l])}y&&(o.setAttrs(_),fn(o));for(var f in C)o.on(f+zs,C[f])}function fn(o){if(!J.autoDrawEnabled){var e=o.getLayer()||o.getStage();e&&e.batchDraw()}}var ws=pu();const Qu={},dg={};Pn.Node.prototype._applyProps=ji;function cg(o,e){if(typeof e=="string"){console.error(`Do not use plain text as child of Konva.Node. You are using text: ${e}`);return}o.add(e),fn(o)}function fg(o,e,r){let i=Pn[o];i||(console.error(`Konva has no node with the type ${o}. Group will be used instead. If you use minimal version of react-konva, just import required nodes into Konva: "import "konva/lib/shapes/${o}"  If you want to render DOM elements as part of canvas tree take a look into this demo: https://konvajs.github.io/docs/react/DOM_Portal.html`),i=Pn.Group);const s={},l={};for(var h in e){var c=h.slice(0,2)==="on";c?l[h]=e[h]:s[h]=e[h]}const f=new i(s);return ji(f,l),f}function gg(o,e,r){console.error(`Text components are not supported for now in ReactKonva. Your text is: "${o}"`)}function pg(o,e,r){return!1}function vg(o){return o}function mg(){return null}function yg(){return null}function _g(o,e,r,i){return dg}function Sg(){}function wg(o){}function Cg(o,e){return!1}function xg(){return Qu}function Eg(){return Qu}const Pg=setTimeout,kg=clearTimeout,Tg=-1;function Mg(o,e){return!1}const Rg=!1,Ag=!0,Lg=!0;function Ng(o,e){e.parent===o?e.moveToTop():o.add(e),fn(o)}function Ig(o,e){e.parent===o?e.moveToTop():o.add(e),fn(o)}function Ku(o,e,r){e._remove(),o.add(e),e.setZIndex(r.getZIndex()),fn(o)}function Og(o,e,r){Ku(o,e,r)}function Dg(o,e){e.destroy(),e.off(zs),fn(o)}function Gg(o,e){e.destroy(),e.off(zs),fn(o)}function zg(o,e,r){console.error(`Text components are not yet supported in ReactKonva. You text is: "${r}"`)}function bg(o,e,r){}function Fg(o,e,r,i,s){ji(o,s,i)}function Bg(o){o.hide(),fn(o)}function Hg(o){}function Wg(o,e){(e.visible==null||e.visible)&&o.show()}function Ug(o,e){}function Xg(o){}function Yg(){}const jg=()=>qu.DefaultEventPriority,Vg=Object.freeze(Object.defineProperty({__proto__:null,appendChild:Ng,appendChildToContainer:Ig,appendInitialChild:cg,cancelTimeout:kg,clearContainer:Xg,commitMount:bg,commitTextUpdate:zg,commitUpdate:Fg,createInstance:fg,createTextInstance:gg,detachDeletedInstance:Yg,finalizeInitialChildren:pg,getChildHostContext:Eg,getCurrentEventPriority:jg,getPublicInstance:vg,getRootHostContext:xg,hideInstance:Bg,hideTextInstance:Hg,idlePriority:ws.unstable_IdlePriority,insertBefore:Ku,insertInContainerBefore:Og,isPrimaryRenderer:Rg,noTimeout:Tg,now:ws.unstable_now,prepareForCommit:mg,preparePortalMount:yg,prepareUpdate:_g,removeChild:Dg,removeChildFromContainer:Gg,resetAfterCommit:Sg,resetTextContent:wg,run:ws.unstable_runWithPriority,scheduleTimeout:Pg,shouldDeprioritizeSubtree:Cg,shouldSetTextContent:Mg,supportsMutation:Lg,unhideInstance:Wg,unhideTextInstance:Ug,warnsIfNotActing:Ag},Symbol.toStringTag,{value:"Module"}));var qg=Object.defineProperty,Qg=Object.defineProperties,Kg=Object.getOwnPropertyDescriptors,au=Object.getOwnPropertySymbols,Jg=Object.prototype.hasOwnProperty,Zg=Object.prototype.propertyIsEnumerable,su=(o,e,r)=>e in o?qg(o,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):o[e]=r,ou=(o,e)=>{for(var r in e||(e={}))Jg.call(e,r)&&su(o,r,e[r]);if(au)for(var r of au(e))Zg.call(e,r)&&su(o,r,e[r]);return o},$g=(o,e)=>Qg(o,Kg(e)),lu,uu;typeof window<"u"&&((lu=window.document)!=null&&lu.createElement||((uu=window.navigator)==null?void 0:uu.product)==="ReactNative")?ve.useLayoutEffect:ve.useEffect;function Ju(o,e,r){if(!o)return;if(r(o)===!0)return o;let i=o.child;for(;i;){const s=Ju(i,e,r);if(s)return s;i=i.sibling}}function Zu(o){try{return Object.defineProperties(o,{_currentRenderer:{get(){return null},set(){}},_currentRenderer2:{get(){return null},set(){}}})}catch{return o}}const hu=console.error;console.error=function(){const o=[...arguments].join("");if(o!=null&&o.startsWith("Warning:")&&o.includes("useContext")){console.error=hu;return}return hu.apply(this,arguments)};const bs=Zu(ve.createContext(null));class $u extends ve.Component{render(){return ve.createElement(bs.Provider,{value:this._reactInternals},this.props.children)}}function ep(){const o=ve.useContext(bs);if(o===null)throw new Error("its-fine: useFiber must be called within a <FiberProvider />!");const e=ve.useId();return ve.useMemo(()=>{for(const i of[o,o==null?void 0:o.alternate]){if(!i)continue;const s=Ju(i,!1,l=>{let h=l.memoizedState;for(;h;){if(h.memoizedState===e)return!0;h=h.next}});if(s)return s}},[o,e])}function tp(){const o=ep(),[e]=ve.useState(()=>new Map);e.clear();let r=o;for(;r;){if(r.type&&typeof r.type=="object"){const s=r.type._context===void 0&&r.type.Provider===r.type?r.type:r.type._context;s&&s!==bs&&!e.has(s)&&e.set(s,ve.useContext(Zu(s)))}r=r.return}return e}function np(){const o=tp();return ve.useMemo(()=>Array.from(o.keys()).reduce((e,r)=>i=>ve.createElement(e,null,ve.createElement(r.Provider,$g(ou({},i),{value:o.get(r)}))),e=>ve.createElement($u,ou({},e))),[o])}function rp(o){const e=Ke.useRef({});return Ke.useLayoutEffect(()=>{e.current=o}),Ke.useLayoutEffect(()=>()=>{e.current={}},[]),e.current}const ip=o=>{const e=Ke.useRef(),r=Ke.useRef(),i=Ke.useRef(),s=rp(o),l=np(),h=c=>{const{forwardedRef:f}=o;f&&(typeof f=="function"?f(c):f.current=c)};return Ke.useLayoutEffect(()=>(r.current=new Pn.Stage({width:o.width,height:o.height,container:e.current}),h(r.current),i.current=Mr.createContainer(r.current,qu.LegacyRoot,!1,null),Mr.updateContainer(Ke.createElement(l,{},o.children),i.current),()=>{Pn.isBrowser&&(h(null),Mr.updateContainer(null,i.current,null),r.current.destroy())}),[]),Ke.useLayoutEffect(()=>{h(r.current),ji(r.current,o,s),Mr.updateContainer(Ke.createElement(l,{},o.children),i.current,null)}),Ke.createElement("div",{ref:e,id:o.id,accessKey:o.accessKey,className:o.className,role:o.role,style:o.style,tabIndex:o.tabIndex,title:o.title})},ap="Layer",Fs="Group",sp="Rect",op="Ellipse",lp="Line",du="Text",cu="RegularPolygon",Mr=ag(Vg);Mr.injectIntoDevTools({findHostInstanceByFiber:()=>null,bundleType:0,version:Ke.version,rendererPackageName:"react-konva"});const up=Ke.forwardRef((o,e)=>Ke.createElement($u,{},Ke.createElement(ip,{...o,forwardedRef:e}))),hp=({node:o,isSelected:e,onClick:r,updateNode:i,selectNode:s})=>{var D,I;const l=()=>{const E=document.querySelector(".nodebox-overlay");return!!E&&window.getComputedStyle(E).display!=="none"},h=E=>{var B;const F=document.querySelector(".mind-sheet.active"),O=F==null?void 0:F.getAttribute("data-sheet-id");if(O&&!Tt.getSheetConfig(O).nodes.dragging.enabled)return E.evt.preventDefault(),!1;if(l())return console.log("NodeComponent: Drag prevented - NodeBox is open"),E.evt.preventDefault(),!1;if((B=o.metadata)!=null&&B.isEditing)return console.log("NodeComponent: Drag prevented - Node is in editing mode"),E.evt.preventDefault(),!1;console.log("NodeComponent: Drag started for node:",o.id);const X={x:o.x,y:o.y};E.target.setAttrs({initialPos:X}),E.evt&&E.evt.stopPropagation(),Yt.registerEvent(jt.NODE_MOVED,{id:o.id,action:"drag_start",position:X})},c=E=>{const F=document.querySelector(".mind-sheet.active"),O=F==null?void 0:F.getAttribute("data-sheet-id");if(!O)return;const X=E.target.x(),B=E.target.y(),Y=Tt.getSheetConfig(O),j=Tt.validateNodeDrag(O,o.id,{x:X,y:B});if(Y.nodes.dragging.preventWidthResize){const N=E.target.getAttr("initialPos")||{x:o.x,y:o.y},V=Math.abs(X-N.x),b=Math.abs(B-N.y);V>10&&b<5&&(console.log("NodeComponent: Preventing potential width dragging - enforcing both X and Y movement"),E.target.y(N.y+(V>0?5:-5)))}E.target.x(j.x),E.target.y(j.y),i(o.id,{x:j.x,y:j.y}),console.log(`NodeComponent: Node ${o.id} dragged to (${j.x}, ${j.y})`)},f=E=>{const F=E.target.x(),O=E.target.y();console.log(`NodeComponent: Drag ended for node ${o.id} at position (${F}, ${O})`);const X=document.querySelector(".mind-sheet.active"),B=X==null?void 0:X.getAttribute("data-sheet-id");if(B){const Y=Tt.validateNodeDrag(B,o.id,{x:F,y:O});i(o.id,{x:Y.x,y:Y.y,metadata:{...o.metadata,positioned:!0,lastMoved:Date.now()}}),Yt.registerEvent(jt.NODE_MOVED,{id:o.id,action:"drag_end",position:Y});const j=Tt.getSheetConfig(B);(!j.positioning.autoLayout.triggerOnNodeMove||j.positioning.autoLayout.preserveManualPositions)&&console.log("NodeComponent: Auto-layout disabled by governance - preserving manual position")}},p=()=>{const E=e||o.isSelected;console.log(`Node ${o.id} isSelected:`,E,"prop:",e,"node.isSelected:",o.isSelected);const F={onClick:N=>{N.cancelBubble=!0,N.evt&&(N.evt.stopPropagation(),N.evt.preventDefault()),r(),console.log("Shape clicked, selecting node:",o.id),s(o.id),Yt.registerEvent(jt.NODE_SELECTED,{id:o.id,text:o.text,position:{x:_,y}})},onTap:N=>{N.cancelBubble=!0,N.evt&&(N.evt.stopPropagation(),N.evt.preventDefault()),r(),console.log("Shape tapped, selecting node:",o.id),s(o.id),Yt.registerEvent(jt.NODE_SELECTED,{id:o.id,text:o.text,position:{x:_,y}})}},O="#3498db",X=3,B=10,Y=.5,j={width:C,height:S,fill:o.color||"#ffffff",stroke:E?O:o.borderColor||"#2c3e50",strokeWidth:E?X:1,shadowColor:E?O:"rgba(0,0,0,0.3)",shadowBlur:E?B:5,shadowOffset:{x:0,y:2},shadowOpacity:E?Y:.5,cornerRadius:5,...F};switch(o.shape){case"ellipse":return Ae.jsx(op,{radiusX:C/2,radiusY:S/2,fill:o.color||"#ffffff",stroke:E?O:o.borderColor||"#2c3e50",strokeWidth:E?X:1,shadowColor:E?O:"rgba(0,0,0,0.3)",shadowBlur:E?B:5,shadowOffset:{x:0,y:2},shadowOpacity:E?Y:.5,...F});case"diamond":return Ae.jsx(cu,{sides:4,radius:Math.min(C,S)/2,rotation:45,fill:o.color||"#ffffff",stroke:E?O:o.borderColor||"#2c3e50",strokeWidth:E?X:1,shadowColor:E?O:"rgba(0,0,0,0.3)",shadowBlur:E?B:5,shadowOffset:{x:0,y:2},shadowOpacity:E?Y:.5,...F});case"hexagon":return Ae.jsx(cu,{sides:6,radius:Math.min(C,S)/2,fill:o.color||"#ffffff",stroke:E?O:o.borderColor||"#2c3e50",strokeWidth:E?X:1,shadowColor:E?O:"rgba(0,0,0,0.3)",shadowBlur:E?B:5,shadowOffset:{x:0,y:2},shadowOpacity:E?Y:.5,...F});case"rectangle":default:return Ae.jsx(sp,{...j})}},v=E=>{console.log("NODEBOX DEBUG: Double-click detected on node:",o.id),E&&E.evt&&(E.evt.preventDefault(),E.cancelBubble=!0,E.evt.stopPropagation()),s(o.id);const F=document.querySelector(".mind-sheet.active"),O=F?F.getAttribute("data-sheet-id"):null;if(!O){console.error("NODEBOX DEBUG: Could not find active sheet ID");return}try{vu(O).getState().updateNode(o.id,{metadata:{...o.metadata||{},isEditing:!0}}),console.log("NODEBOX DEBUG: Updated node metadata with isEditing flag using sheet-specific store");const B=document.querySelector(`[data-node-id="${o.id}"]`);B&&(B.setAttribute("draggable","false"),console.log("NODEBOX DEBUG: Set node to non-draggable")),Yt.registerEvent(jt.NODE_SELECTED,{id:o.id,text:o.text,position:{x:o.x,y:o.y}}),Yt.registerEvent(jt.NODE_OPENED,{id:o.id});const Y=new CustomEvent("mindback:open_nodebox",{detail:{nodeId:o.id,sheetId:O}});document.dispatchEvent(Y),console.log("NODEBOX DEBUG: Dispatched open_nodebox event for node:",o.id)}catch(X){console.error("NODEBOX DEBUG: Error in double-click handler:",X)}},_=o.x||0,y=o.y||0,C=o.width||200,S=o.height||100;console.log(`Rendering node ${o.id} at position (${_}, ${y}) with dimensions ${C}x${S}`),_===0&&y===0&&console.warn(`Node ${o.id} has position (0,0), which may cause it to be off-screen`);const M=((D=o.metadata)==null?void 0:D.isEditing)||!1,T=!M&&!l();console.log(`Node ${o.id} draggable state:`,{isEditing:M,nodeboxOpen:l(),shouldBeDraggable:T});const G=E=>{const F=document.querySelector(".mind-sheet.active"),O=F==null?void 0:F.getAttribute("data-sheet-id");if(!O)return E;const X=Tt.getSheetConfig(O),B=Tt.validateNodeDrag(O,o.id,E);if(X.nodes.dragging.preventWidthResize){const Y=o.x||0,j=o.y||0,N=Math.abs(E.x-Y),V=Math.abs(E.y-j);if(N>20&&V<5)return console.log("NodeComponent: Preventing width-only dragging"),{x:Math.min(Math.max(E.x,Y-50),Y+50),y:j+(E.x>Y?10:-10)};if(N<3&&V<3)return{x:Y,y:j}}if(X.nodes.dragging.constrainToCanvas){const Y=window.innerWidth,j=window.innerHeight,N=o.width||200,V=o.height||100;return{x:Math.max(0,Math.min(B.x,Y-N)),y:Math.max(0,Math.min(B.y,j-V))}}return B};return Ae.jsxs(Fs,{x:_,y,draggable:T,dragBoundFunc:T?G:void 0,"data-node-id":o.id,onDragStart:h,onDragMove:c,onDragEnd:f,onClick:E=>{E.cancelBubble=!0,E.evt&&(E.evt.stopPropagation(),E.evt.preventDefault()),r(),console.log("Force selecting node on click:",o.id),s(o.id),setTimeout(()=>s(o.id),0),setTimeout(()=>s(o.id),50),setTimeout(()=>s(o.id),100),Yt.registerEvent(jt.NODE_SELECTED,{id:o.id,text:o.text,position:{x:_,y}}),console.log("Node clicked:",o.id,o.text,"isSelected:",e)},onTap:E=>{E.cancelBubble=!0,E.evt&&(E.evt.stopPropagation(),E.evt.preventDefault()),r(),console.log("Force selecting node on tap:",o.id),s(o.id),setTimeout(()=>s(o.id),0),setTimeout(()=>s(o.id),50),setTimeout(()=>s(o.id),100),Yt.registerEvent(jt.NODE_SELECTED,{id:o.id,text:o.text,position:{x:_,y}}),console.log("Node tapped:",o.id,"isSelected:",e)},onDblClick:E=>{console.log("Double click event triggered on node:",o.id),E.cancelBubble=!0,E.evt&&(E.evt.stopPropagation(),E.evt.preventDefault()),v(E)},onDblTap:E=>{console.log("Double tap event triggered on node:",o.id),E.cancelBubble=!0,E.evt&&(E.evt.stopPropagation(),E.evt.preventDefault()),v(E)},children:[p(),Ae.jsx(du,{text:((I=o.metadata)==null?void 0:I.nodePath)||"1.0",width:C,height:20,y:-S/2+15,align:"center",verticalAlign:"middle",fontSize:10,fontFamily:"Arial, sans-serif",fill:"#666666",listening:!0,onClick:E=>{E.cancelBubble=!0,E.evt&&(E.evt.stopPropagation(),E.evt.preventDefault()),r(),s(o.id),console.log("Path text clicked, selecting node:",o.id)}}),Ae.jsx(du,{text:o.text||"Untitled Node",width:C,height:S,align:"center",verticalAlign:"middle",fontSize:14,fontFamily:"Arial",fill:"#333333",padding:5,ellipsis:!0,listening:!0,onClick:E=>{E.cancelBubble=!0,E.evt&&(E.evt.stopPropagation(),E.evt.preventDefault()),r(),s(o.id),console.log("Title text clicked, selecting node:",o.id)}})]})},fu=(o,e)=>{const r=o.x+o.width/2,i=o.y+o.height/2,s=e.x+e.width/2,l=e.y+e.height/2,h=Math.atan2(l-i,s-r),c=Math.min(o.width,o.height)/2,f=Math.min(e.width,e.height)/2,p=r+Math.cos(h)*c,v=i+Math.sin(h)*c,_=s-Math.cos(h)*f,y=l-Math.sin(h)*f;return{startX:p,startY:v,endX:_,endY:y}},dp=({connection:o,fromNode:e,toNode:r})=>{if(!e||!r)return null;const[i,s]=ve.useState(()=>fu(e,r));ve.useEffect(()=>{const C=fu(e,r);s(C)},[e.x,e.y,r.x,r.y,e.width,e.height,r.width,r.height]);const{startX:l,startY:h,endX:c,endY:f}=i;let p;switch(o.style){case"dashed":p=[10,5];break;case"dotted":p=[2,2];break;case"solid":default:p=[];break}const v=Al.getState().selectedConnectionId,_=o.id===v,y=()=>{Al.getState().selectConnection(o.id)};return ve.useEffect(()=>{console.log(`Connection ${o.id} points updated:`,{from:{id:e.id,x:e.x,y:e.y},to:{id:r.id,x:r.x,y:r.y},points:{startX:l,startY:h,endX:c,endY:f}})},[o.id,e.id,r.id,l,h,c,f]),Ae.jsx(Fs,{onClick:y,children:Ae.jsx(lp,{points:[l,h,c,f],stroke:_?"#3498db":o.color||"#2c3e50",strokeWidth:_?(o.width||2)+1:o.width||2,dash:p,lineCap:"round",lineJoin:"round",shadowColor:_?"#3498db":void 0,shadowBlur:_?5:0,shadowOffset:{x:0,y:0},shadowOpacity:.5,listening:!0})})},cp=up,eh=ve.forwardRef(({width:o,height:e,draggable:r=!1,x:i=0,y:s=0,scaleX:l=1,scaleY:h=1,onDragStart:c,onDragMove:f,onDragEnd:p,onWheel:v,onKeyDown:_,onKeyUp:y,onClick:C,onMouseDown:S,onMouseMove:M,onMouseUp:T,onMouseEnter:G,onMouseLeave:D,tabIndex:I=0,children:E},F)=>Ae.jsx(cp,{ref:F,width:o,height:e,draggable:r,x:i,y:s,scaleX:l,scaleY:h,onDragStart:c,onDragMove:f,onDragEnd:p,onWheel:v,onKeyDown:_,onKeyUp:y,onClick:C,onMouseDown:S,onMouseMove:M,onMouseUp:T,onMouseEnter:G,onMouseLeave:D,tabIndex:I,children:E}));eh.displayName="StageCompatWrapper";console.log("MindMapCanvas: Window dimensions:",{width:window.innerWidth,height:window.innerHeight});const fp=ap,gu=Fs,gp=({width:o,height:e,sheetId:r,store:i})=>{var X,B,Y,j;const s=ve.useRef(null),l=ve.useRef(i);i!==l.current&&(l.current=i);const[h,c]=ve.useState(()=>l.current?l.current.getState():{nodes:{},connections:[],position:{x:0,y:0},scale:1,selectedNodeId:null,rootNodeId:null});ve.useEffect(()=>{if(l.current){console.log(`MindMapCanvas: Subscribing to store changes for sheet: ${r}`),c(l.current.getState());const N=l.current.subscribe(V=>{console.log(`MindMapCanvas: Store updated for sheet ${r}:`,{nodeCount:Object.keys(V.nodes||{}).length,connectionCount:(V.connections||[]).length,position:V.position,scale:V.scale}),c(V)});return()=>{N&&N()}}},[r]),ve.useEffect(()=>{l.current&&(console.log(`MindMapCanvas: Using sheet-specific store for sheet: ${r}`),console.log(`MindMapCanvas: Store has ${Object.keys(l.current.getState().nodes).length} nodes`))},[r]);const f=h.nodes||{},p=h.connections||[],v=h.position||{x:0,y:0},_=h.scale||1,y=h.selectedNodeId,C=((X=l.current)==null?void 0:X.getState().selectNode)||(()=>{}),S=((B=l.current)==null?void 0:B.getState().setPosition)||(()=>{}),M=((Y=l.current)==null?void 0:Y.getState().setScale)||(()=>{}),T=((j=l.current)==null?void 0:j.getState().updateNode)||(()=>{});ve.useEffect(()=>{var N;if(y&&s.current){if(console.log("Node selected in sheet:",r,"nodeId:",y),!((N=document.querySelector(`[data-sheet-id="${r}"]`))==null?void 0:N.classList.contains("active"))){console.log("Not focusing stage because sheet is not active:",r);return}setTimeout(()=>{if(s.current){const b=s.current.getStage().container();b.focus(),console.log("Stage focused for sheet:",r,"success:",document.activeElement===b);const te=Tt.getSheetConfig(r);if(!te.positioning.viewport.centerOnNodeSelect&&te.positioning.viewport.preventJumpOnClick){console.log("MindMapCanvas: Auto-centering disabled by governance - preventing viewport jump");return}const ne=f[y];if(ne&&te.positioning.viewport.centerOnNodeSelect){console.log("Selected node found:",ne.id,ne.text);const $=s.current.getStage(),le=$.width(),ge=$.height(),Le=ne.x*_+v.x,re=ne.y*_+v.y;if(Le<0||re<0||Le>le||re>ge){console.log("Selected node is off-screen, centering view (governance allows)");const Pe={x:le/2-ne.x,y:ge/2-ne.y};Tt.validateViewportChange(r,{position:Pe})&&(S(Pe),l.current&&l.current.getState().setPosition(Pe),console.log("MindMapCanvas: Centered view on selected node:",ne.id,"new position:",Pe))}}setTimeout(()=>{try{const $=new CustomEvent("mindback:node_selected",{detail:{nodeId:y,sheetId:r}});document.dispatchEvent($)}catch($){console.error("Error updating node selection state:",$)}},50)}},100)}},[y,f,_,v,r,S]);const G=()=>s.current?(s.current.getStage().container().focus(),console.log("Stage focused via public method"),!0):!1;ve.useEffect(()=>{var b;if(!((b=document.querySelector(`[data-sheet-id="${r}"]`))==null?void 0:b.classList.contains("active"))){console.log("MindMapCanvas: Not focusing because sheet is not active:",r);return}const V=setTimeout(()=>{G()},100);return()=>clearTimeout(V)},[r]),ve.useEffect(()=>{const N=V=>{(V.detail.sheetId===r||V.detail.sheetId==="current")&&(console.log("MindMapCanvas: Received refresh_canvas event for sheet:",r),setTimeout(()=>{if(l.current)try{const b=l.current.getState();if(console.log("MindMapCanvas: Current state before refresh:",{nodeCount:Object.keys(b.nodes||{}).length,connectionCount:(b.connections||[]).length,selectedNodeId:b.selectedNodeId,rootNodeId:b.rootNodeId}),V.detail.nodeId){console.log("MindMapCanvas: Refresh event includes node ID:",V.detail.nodeId);const te=b.nodes[V.detail.nodeId];te?(console.log("MindMapCanvas: Node found in store:",te.id,te.text),b.selectNode(te.id)):console.log("MindMapCanvas: Node not found in store:",V.detail.nodeId)}b.updateLayout?(b.updateLayout("tree"),console.log("MindMapCanvas: Updated layout after refresh event")):console.warn("MindMapCanvas: updateLayout function not found in store")}catch(b){console.error("MindMapCanvas: Error refreshing canvas:",b)}},50))};return document.addEventListener("mindback:refresh_canvas",N),()=>{document.removeEventListener("mindback:refresh_canvas",N)}},[r]);const D=N=>{N.target===s.current.getStage()&&console.log("Stage drag started")},I=N=>{if(N.target===s.current.getStage()){const V=s.current.position();S(V)}},E=N=>{if(N.target===s.current.getStage()){console.log("Stage drag ended");const V=s.current.position();S(V)}},F=N=>{N.evt.preventDefault();const V=document.querySelector(".mind-sheet.active"),b=V==null?void 0:V.getAttribute("data-sheet-id");if(!b)return;const te=N.target.getStage(),ne=te.scaleX(),$=te.getPointerPosition(),le={x:($.x-te.x())/ne,y:($.y-te.y())/ne},ge=N.evt.deltaY>0?-1:1,Le=1.1,re=ge>0?ne*Le:ne/Le;if(!Tt.validateViewportChange(b,{scale:re})){console.log("MindMapCanvas: Scale change rejected by governance");return}M(re);const pe={x:$.x-le.x*re,y:$.y-le.y*re};Tt.validateViewportChange(b,{position:pe})&&S(pe),console.log("MindMapCanvas: Zoom applied - scale:",re,"position:",pe)},O=N=>{var b,te;switch(["Tab","Enter","Delete","Backspace","Escape"].includes(N.key)&&N.preventDefault(),console.log("MindMapCanvas: Key pressed:",N.key,"for sheet:",r),N.key){case"Tab":if(y&&f[y]){console.log("MindMapCanvas: Tab key - adding child node");const ne=f[y],$=ne.x+250,le=ne.y,ge=(b=l.current)==null?void 0:b.getState().addNode(y,"New Node",$,le);ge&&(C(ge),console.log("MindMapCanvas: Child node added and selected:",ge))}break;case"Enter":if(y&&f[y]){console.log("MindMapCanvas: Enter key - adding sibling node");const ne=f[y],$=p.find(ge=>ge.to===y),le=$?$.from:null;if(le&&f[le]){f[le];const ge=ne.x,Le=ne.y+100,re=(te=l.current)==null?void 0:te.getState().addNode(le,"New Node",ge,Le);re&&(C(re),console.log("MindMapCanvas: Sibling node added and selected:",re))}else console.log("MindMapCanvas: No parent found for sibling creation")}break}};return!f||Object.keys(f).length===0?(console.log("MindMapCanvas: No nodes found, showing loading state for sheet:",r),Ae.jsx("div",{className:"loading-canvas",children:Ae.jsxs("div",{className:"initializing-mindmap",children:[Ae.jsx("div",{className:"loading-spinner"}),Ae.jsxs("p",{children:["Initializing canvas for sheet ",r,"..."]}),Ae.jsx("button",{onClick:()=>{console.log("Forcing sheet-specific store update for sheet:",r);try{Ud(async()=>{const{useMindBookStore:N}=await import("./index-v114B1Cq.js").then(V=>V.M);return{useMindBookStore:N}},__vite__mapDeps([0,1])).then(({useMindBookStore:N})=>{const b=N.getState().getSheetState(r);if(b&&b.nodes&&Object.keys(b.nodes).length>0){console.log("MindMapCanvas: Found saved state in MindBookStore, applying it");const te=l.current.getState();Object.entries(b.nodes).forEach(([ne,$])=>{te.updateNode(ne,$)}),b.connections&&Array.isArray(b.connections)&&b.connections.forEach(ne=>{ne.from&&ne.to&&te.addConnection(ne.from,ne.to,{color:ne.color,width:ne.width,style:ne.style})}),b.position&&te.setPosition(b.position),b.scale&&te.setScale(b.scale),b.rootNodeId&&(te.rootNodeId=b.rootNodeId)}}).catch(N=>{console.error("MindMapCanvas: Error importing MindBookStore:",N)})}catch(N){console.error("MindMapCanvas: Error applying saved state:",N)}if(l.current){const{updateLayout:N}=l.current.getState();N&&N("tree")}},style:{marginTop:"10px",padding:"5px 10px"},children:"Refresh Canvas"})]})})):(console.log("MindMapCanvas: Rendering with nodes:",Object.keys(f).length),console.log(`MindMapCanvas: Using store position (${v.x}, ${v.y}) for stage - width: ${o}, height: ${e}`),Ae.jsx("div",{className:"mindmap-canvas-container",style:{width:"100%",height:"100%"},children:Ae.jsx(eh,{ref:s,width:o,height:e,draggable:!0,x:v.x,y:v.y,scaleX:_,scaleY:_,onDragStart:D,onDragMove:I,onDragEnd:E,onWheel:F,onKeyDown:O,onClick:N=>{N.target===s.current.getStage()&&s.current&&(s.current.getStage().container().focus(),console.log("Stage clicked and focused"),y&&(C(null),console.log("Deselected node on stage click")))},tabIndex:0,children:Ae.jsxs(fp,{children:[Ae.jsx(gu,{children:p.map(N=>Ae.jsx(dp,{connection:N,fromNode:f[N.from],toNode:f[N.to]},N.id))}),Ae.jsx(gu,{children:Object.values(f).map(N=>Ae.jsx(hp,{node:N,isSelected:N.id===y,onClick:()=>C(N.id),updateNode:T,selectNode:C},N.id))})]})})}))},Ep=o=>{const[e,r]=ve.useState(!1),i=vu(o.sheetId);return ve.useEffect(()=>{console.log("MindMapCanvasWrapper: Mounted for sheet:",o.sheetId),Xd(o.sheetId)?console.log("MindMapCanvasWrapper: Using existing store for sheet:",o.sheetId):console.log("MindMapCanvasWrapper: Creating new store for sheet:",o.sheetId);const s=i.getState(),l=Object.keys(s.nodes).length>0;console.log("MindMapCanvasWrapper: Store has nodes:",l,"count:",Object.keys(s.nodes).length),Yt.registerEvent(jt.CANVAS_MOUNTED,{sheetId:o.sheetId,hasNodes:l}),r(!0);const h=i.subscribe(v=>v.selectedNodeId,v=>{if(v){console.log("MindMapCanvasWrapper: Node selected in store, dispatching event:",v);try{const _=new CustomEvent("mindback:node_selected",{detail:{nodeId:v,sheetId:o.sheetId}});document.dispatchEvent(_)}catch(_){console.error("MindMapCanvasWrapper: Error dispatching node_selected event:",_)}}}),c=i.subscribe(v=>Object.keys(v.nodes).length,(v,_)=>{if(v>_){console.log("MindMapCanvasWrapper: Node added to store, dispatching refresh event");try{const y=i.getState().nodes,C=Object.keys(y),S=C[C.length-1];setTimeout(()=>{const M=new CustomEvent("mindback:refresh_canvas",{detail:{nodeId:S,sheetId:o.sheetId}});document.dispatchEvent(M),console.log("MindMapCanvasWrapper: Dispatched refresh_canvas event after adding node:",S)},10)}catch(y){console.error("MindMapCanvasWrapper: Error dispatching refresh event:",y)}}}),f=v=>{var _,y;(((_=v.detail)==null?void 0:_.sheetId)===o.sheetId||((y=v.detail)==null?void 0:y.sheetId)==="current")&&(console.log("MindMapCanvasWrapper: Received refresh event for sheet:",o.sheetId),r(C=>!C))},p=v=>{var _;if(((_=v.detail)==null?void 0:_.sheetId)===o.sheetId){console.log("MindMapCanvasWrapper: Received center view event for sheet:",o.sheetId);const y=i.getState(),C=window.innerWidth,S=window.innerHeight;if(y.rootNodeId&&y.nodes[y.rootNodeId]){const M=y.nodes[y.rootNodeId],T=C/2,G=S/2;y.setPosition({x:T-M.x,y:G-M.y}),y.selectNode(y.rootNodeId),console.log("MindMapCanvasWrapper: Centered view on root node for sheet:",o.sheetId)}else y.setPosition({x:0,y:0}),console.log("MindMapCanvasWrapper: Reset position to origin for sheet:",o.sheetId);r(M=>!M)}};return document.addEventListener("mindback:refresh_canvas",f),document.addEventListener("mindback:center_view",p),()=>{console.log("MindMapCanvasWrapper: Unmounted for sheet:",o.sheetId),document.removeEventListener("mindback:refresh_canvas",f),document.removeEventListener("mindback:center_view",p),h&&h(),c&&c()}},[o.sheetId,i]),ve.createElement(gp,{...o,store:i,key:`canvas-${o.sheetId}-${e}`})};export{Ep as default};
