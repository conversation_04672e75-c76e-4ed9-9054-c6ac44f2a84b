/**
 * ChatForkManager.tsx
 *
 * A management dialog for ChatFork mindsheets.
 * Provides controls and information about the current ChatFork content.
 */

import React, { useState, useEffect } from 'react';
import { useChatForkStore } from './ChatForkStore';
import { useMindBookStore } from '../../core/state/MindBookStore';
import { MindSheetContentType } from '../../core/state/StoreTypes';
import './ChatFork.css';

interface ChatForkManagerProps {
  open: boolean;
  onClose: () => void;
  sheetId: string;
}

const ChatForkManager: React.FC<ChatForkManagerProps> = ({
  open,
  onClose,
  sheetId
}) => {
  const chatForkStore = useChatForkStore();
  const { sheets } = useMindBookStore();
  
  // Find the current sheet
  const currentSheet = sheets.find(s => s.id === sheetId);
  const isChatForkSheet = currentSheet?.contentType === MindSheetContentType.CHATFORK;

  // Local state for manager
  const [selectedText, setSelectedText] = useState('');

  useEffect(() => {
    if (chatForkStore.selectedText) {
      setSelectedText(chatForkStore.selectedText);
    }
  }, [chatForkStore.selectedText]);

  // Don't render if not open or not a ChatFork sheet
  if (!open || !isChatForkSheet || !currentSheet) {
    return null;
  }

  const handleCreateNodeFromSelection = () => {
    if (selectedText.trim()) {
      console.log('ChatForkManager: Creating node from selection:', selectedText);
      // TODO: Implement node creation from selected text
      // This could create a new mindsheet or add to an existing mindmap
      alert(`Creating node from: "${selectedText.substring(0, 50)}..."`);
    }
  };

  const handleExportContent = () => {
    if (currentSheet.content) {
      console.log('ChatForkManager: Exporting content for sheet:', sheetId);
      // Create a downloadable text file of the content
      const content = currentSheet.content.full_text || currentSheet.content.text || '';
      const blob = new Blob([content], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${currentSheet.title || 'chatfork'}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const handleRefreshContent = () => {
    console.log('ChatForkManager: Refreshing content for sheet:', sheetId);
    // Force refresh the ChatFork content
    if (currentSheet.content) {
      chatForkStore.showChatFork(currentSheet.content, sheetId);
    }
  };

  return (
    <div className="chatfork-manager-overlay">
      <div className="chatfork-manager-dialog">
        {/* Header */}
        <div className="chatfork-manager-header">
          <h3>ChatFork Manager</h3>
          <button 
            className="chatfork-manager-close"
            onClick={onClose}
            title="Close Manager"
          >
            ✕
          </button>
        </div>

        {/* Content */}
        <div className="chatfork-manager-content">
          <div className="chatfork-info-section">
            <h4>Current ChatFork</h4>
            <div className="chatfork-info">
              <p><strong>Title:</strong> {currentSheet.title}</p>
              <p><strong>Sheet ID:</strong> {sheetId}</p>
              <p><strong>Content Type:</strong> {currentSheet.contentType}</p>
              <p><strong>Has Content:</strong> {currentSheet.content ? 'Yes' : 'No'}</p>
              {currentSheet.content && (
                <p><strong>Content Length:</strong> {currentSheet.content.full_text?.length || 0} characters</p>
              )}
            </div>
          </div>

          {selectedText && (
            <div className="chatfork-selection-section">
              <h4>Selected Text</h4>
              <div className="selected-text-preview">
                {selectedText.substring(0, 200)}
                {selectedText.length > 200 && '...'}
              </div>
              <button 
                className="chatfork-manager-button"
                onClick={handleCreateNodeFromSelection}
              >
                Create Node from Selection
              </button>
            </div>
          )}

          <div className="chatfork-actions-section">
            <h4>Actions</h4>
            <div className="chatfork-actions">
              <button 
                className="chatfork-manager-button"
                onClick={handleRefreshContent}
              >
                Refresh Content
              </button>
              <button 
                className="chatfork-manager-button"
                onClick={handleExportContent}
              >
                Export as Text
              </button>
              <button 
                className="chatfork-manager-button"
                onClick={() => {
                  // TODO: Implement conversion to mindmap
                  alert('Convert to MindMap functionality coming soon!');
                }}
              >
                Convert to MindMap
              </button>
            </div>
          </div>

          <div className="chatfork-stats-section">
            <h4>Statistics</h4>
            <div className="chatfork-stats">
              <p>Words: {currentSheet.content?.full_text?.split(' ').length || 0}</p>
              <p>Characters: {currentSheet.content?.full_text?.length || 0}</p>
              <p>Created: {new Date(currentSheet.createdAt || Date.now()).toLocaleString()}</p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="chatfork-manager-footer">
          <button 
            className="chatfork-manager-button secondary"
            onClick={onClose}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatForkManager; 