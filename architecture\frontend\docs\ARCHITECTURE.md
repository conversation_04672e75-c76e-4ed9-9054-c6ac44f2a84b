# Frontend Architecture

## Layer Structure
- /components - Reusable UI components
- /features - Feature-specific modules
- /core - Core business logic
- /shared - Shared utilities and types
- /api - API integration layer

## Dependency Rules
1. Feature modules can only depend on core and shared
2. Components should be pure and not contain business logic
3. Core modules cannot depend on UI components
4. API layer should be isolated and only accessed through core

## Module Organization
- Each feature follows Domain-Driven Design
- Shared components use atomic design principles
- State management follows flux/redux pattern