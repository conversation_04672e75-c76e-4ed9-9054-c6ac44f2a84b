// Site padding and layout fixes
(function() {
  try {
    console.log('Applying site padding and layout fixes...');
    
    // Add CSS to fix padding and layout issues
    if (typeof document !== 'undefined') {
      const style = document.createElement('style');
      style.textContent = `
        /* Fix box-sizing for all elements */
        *, *::before, *::after {
          box-sizing: border-box;
        }
        
        /* Fix body margin and padding */
        body {
          margin: 0;
          padding: 0;
          overflow-x: hidden;
          min-height: 100vh;
          display: flex;
          flex-direction: column;
        }
        
        /* Fix root container */
        #root {
          display: flex;
          flex-direction: column;
          flex: 1;
          min-height: 100vh;
        }
        
        /* Fix container padding */
        .container {
          padding-left: 1rem;
          padding-right: 1rem;
          width: 100%;
          margin-left: auto;
          margin-right: auto;
        }
        
        /* Responsive container sizes */
        @media (min-width: 576px) {
          .container {
            max-width: 540px;
          }
        }
        
        @media (min-width: 768px) {
          .container {
            max-width: 720px;
          }
        }
        
        @media (min-width: 992px) {
          .container {
            max-width: 960px;
          }
        }
        
        @media (min-width: 1200px) {
          .container {
            max-width: 1140px;
          }
        }
        
        /* Fix for flex containers */
        .flex-container {
          display: flex;
        }
        
        .flex-column {
          flex-direction: column;
        }
        
        .flex-row {
          flex-direction: row;
        }
        
        .flex-wrap {
          flex-wrap: wrap;
        }
        
        .flex-nowrap {
          flex-wrap: nowrap;
        }
        
        .justify-content-start {
          justify-content: flex-start;
        }
        
        .justify-content-end {
          justify-content: flex-end;
        }
        
        .justify-content-center {
          justify-content: center;
        }
        
        .justify-content-between {
          justify-content: space-between;
        }
        
        .justify-content-around {
          justify-content: space-around;
        }
        
        .align-items-start {
          align-items: flex-start;
        }
        
        .align-items-end {
          align-items: flex-end;
        }
        
        .align-items-center {
          align-items: center;
        }
        
        .align-items-baseline {
          align-items: baseline;
        }
        
        .align-items-stretch {
          align-items: stretch;
        }
        
        /* Fix for grid containers */
        .grid-container {
          display: grid;
          grid-template-columns: repeat(12, 1fr);
          grid-gap: 1rem;
        }
        
        /* Fix for common spacing issues */
        .m-0 { margin: 0 !important; }
        .mt-0 { margin-top: 0 !important; }
        .mr-0 { margin-right: 0 !important; }
        .mb-0 { margin-bottom: 0 !important; }
        .ml-0 { margin-left: 0 !important; }
        
        .m-1 { margin: 0.25rem !important; }
        .mt-1 { margin-top: 0.25rem !important; }
        .mr-1 { margin-right: 0.25rem !important; }
        .mb-1 { margin-bottom: 0.25rem !important; }
        .ml-1 { margin-left: 0.25rem !important; }
        
        .m-2 { margin: 0.5rem !important; }
        .mt-2 { margin-top: 0.5rem !important; }
        .mr-2 { margin-right: 0.5rem !important; }
        .mb-2 { margin-bottom: 0.5rem !important; }
        .ml-2 { margin-left: 0.5rem !important; }
        
        .m-3 { margin: 1rem !important; }
        .mt-3 { margin-top: 1rem !important; }
        .mr-3 { margin-right: 1rem !important; }
        .mb-3 { margin-bottom: 1rem !important; }
        .ml-3 { margin-left: 1rem !important; }
        
        .p-0 { padding: 0 !important; }
        .pt-0 { padding-top: 0 !important; }
        .pr-0 { padding-right: 0 !important; }
        .pb-0 { padding-bottom: 0 !important; }
        .pl-0 { padding-left: 0 !important; }
        
        .p-1 { padding: 0.25rem !important; }
        .pt-1 { padding-top: 0.25rem !important; }
        .pr-1 { padding-right: 0.25rem !important; }
        .pb-1 { padding-bottom: 0.25rem !important; }
        .pl-1 { padding-left: 0.25rem !important; }
        
        .p-2 { padding: 0.5rem !important; }
        .pt-2 { padding-top: 0.5rem !important; }
        .pr-2 { padding-right: 0.5rem !important; }
        .pb-2 { padding-bottom: 0.5rem !important; }
        .pl-2 { padding-left: 0.5rem !important; }
        
        .p-3 { padding: 1rem !important; }
        .pt-3 { padding-top: 1rem !important; }
        .pr-3 { padding-right: 1rem !important; }
        .pb-3 { padding-bottom: 1rem !important; }
        .pl-3 { padding-left: 1rem !important; }
      `;
      document.head.appendChild(style);
      console.log('Added padding and layout fixes');
    }
    
    console.log('Site padding and layout fixes applied successfully');
  } catch (error) {
    console.error('Error applying site padding and layout fixes:', error);
  }
})();
