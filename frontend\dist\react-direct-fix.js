// Direct fix for React compatibility issues
(function() {
  try {
    console.log('Applying direct React compatibility fixes...');
    
    // Create a mock React object if needed
    if (!window.React) {
      window.React = {
        createElement: function() { return {}; },
        Fragment: Symbol('Fragment'),
        StrictMode: Symbol('StrictMode'),
        useState: function(initialState) {
          return [
            typeof initialState === 'function' ? initialState() : initialState,
            function() {}
          ];
        },
        useEffect: function() {},
        useContext: function() { return {}; },
        useReducer: function(reducer, initialState) { return [initialState, function() {}]; },
        useCallback: function(callback) { return callback; },
        useMemo: function(factory) { return factory(); },
        useRef: function(initialValue) { return { current: initialValue }; },
        __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED: {
          ReactCurrentDispatcher: {
            current: {}
          }
        }
      };
      
      console.log('Created mock React object');
    }
    
    // Ensure the internals object exists
    if (!window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) {
      window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = {
        ReactCurrentDispatcher: {
          current: {}
        }
      };
      
      console.log('Created React internals object');
    }
    
    // Ensure ReactCurrentDispatcher exists
    const internals = window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
    if (!internals.ReactCurrentDispatcher) {
      internals.ReactCurrentDispatcher = {
        current: {}
      };
      
      console.log('Created ReactCurrentDispatcher object');
    }
    
    // Ensure current exists
    if (!internals.ReactCurrentDispatcher.current) {
      internals.ReactCurrentDispatcher.current = {};
      
      console.log('Created current dispatcher object');
    }
    
    // Add missing hooks to the dispatcher
    const dispatcher = internals.ReactCurrentDispatcher.current;
    
    // Add useInternalStore if it doesn't exist
    if (!dispatcher.useInternalStore) {
      dispatcher.useInternalStore = function(subscribe, getSnapshot) {
        console.log('Using polyfilled useInternalStore');
        return getSnapshot();
      };
      
      console.log('Added useInternalStore to dispatcher');
    }
    
    console.log('React direct fixes applied successfully');
  } catch (error) {
    console.error('Error applying React direct fixes:', error);
  }
})();
