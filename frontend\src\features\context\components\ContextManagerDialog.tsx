/**
 * ContextManagerDialog.tsx
 * 
 * Dialog for managing Context Settings profiles.
 * Allows users to create, select, duplicate, and delete context settings.
 */

import React, { useState, useEffect } from 'react';
import { useContextStore, ContextSettings } from '../store/ContextStore';
import { setContextSettings, autoSaveSession, saveMindBook } from '../../../core/services/MindBookPersistenceService';
import { useMindBookStore } from '../../../core/state/MindBookStore';
import './ContextManagerDialog.css';

interface ContextManagerDialogProps {
  open: boolean;
  onClose: () => void;
}

const ContextManagerDialog: React.FC<ContextManagerDialogProps> = ({ open, onClose }) => {
  const {
    currentContextSettings,
    getContextSettingsList,
    loadContextSettings,
    createNewContextSettings,
    deleteContextSettings,
    duplicateContextSettings,
    exportContextSettings,
    importContextSettings
  } = useContextStore();

  // MindBook store for project association
  const mindBookStore = useMindBookStore();

  const [availableSettings, setAvailableSettings] = useState<ContextSettings[]>([]);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showLoadDialog, setShowLoadDialog] = useState(false);
  const [newName, setNewName] = useState('');
  const [newDescription, setNewDescription] = useState('');
  const [selectedForDelete, setSelectedForDelete] = useState<string | null>(null);

  // Load available context settings
  useEffect(() => {
    if (open) {
      const settings = getContextSettingsList();
      setAvailableSettings(settings);
    }
  }, [open, getContextSettingsList]);

  // Simple context association - just trigger auto-save without forcing naming
  const associateContextWithProject = (contextSettingsId: string, contextName: string) => {
    try {
      // The context is already loaded in the ContextStore at this point
      // Just trigger an auto-save to capture the current state with the loaded context
      autoSaveSession();
      
      console.log('ContextManagerDialog: Associated context with current project:', contextName);
    } catch (error) {
      console.error('ContextManagerDialog: Failed to associate context with project:', error);
    }
  };

  // Handle selecting context settings
  const handleSelect = (id: string) => {
    const success = loadContextSettings(id);
    if (success) {
      // Get the context settings name for auto-save
      const contextSettings = availableSettings.find(cs => cs.id === id);
      if (contextSettings) {
        // Auto-save to project
        associateContextWithProject(id, contextSettings.name);
      }
      console.log('ContextManagerDialog: Selected context settings and associated with project:', id);
    }
  };

  // Handle creating new context settings
  const handleCreate = () => {
    if (newName.trim()) {
      const newId = createNewContextSettings(newName.trim(), newDescription.trim());
      if (newId) {
        setShowCreateDialog(false);
        setNewName('');
        setNewDescription('');
        // Reload the list
        const settings = getContextSettingsList();
        setAvailableSettings(settings);
        // Auto-select the new settings and auto-save to project
        associateContextWithProject(newId, newName.trim());
      }
    }
  };

  // Handle duplicating context settings
  const handleDuplicate = (id: string, originalName: string) => {
    const newName = `${originalName} (Copy)`;
    const newId = duplicateContextSettings(id, newName);
    if (newId) {
      // Reload the list
      const settings = getContextSettingsList();
      setAvailableSettings(settings);
      console.log('ContextManagerDialog: Duplicated context settings:', newId);
    }
  };

  // Handle deleting context settings
  const handleDelete = (id: string) => {
    if (selectedForDelete === id) {
      const success = deleteContextSettings(id);
      if (success) {
        // Reload the list
        const settings = getContextSettingsList();
        setAvailableSettings(settings);
        setSelectedForDelete(null);
        console.log('ContextManagerDialog: Deleted context settings:', id);
      }
    } else {
      setSelectedForDelete(id);
    }
  };

  // Handle export
  const handleExport = (id: string, name: string) => {
    const exportData = exportContextSettings(id);
    if (exportData) {
      // Create download
      const blob = new Blob([exportData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `context-settings-${name.replace(/\s+/g, '-')}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  // Handle import
  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const data = e.target?.result as string;
        const newId = importContextSettings(data);
        if (newId) {
          // Reload the list
          const settings = getContextSettingsList();
          setAvailableSettings(settings);
          console.log('ContextManagerDialog: Imported context settings:', newId);
        }
      };
      reader.readAsText(file);
    }
    // Reset the input
    event.target.value = '';
  };

  if (!open) return null;

  return (
    <div className="context-manager-overlay">
      <div className="context-manager-dialog">
        <div className="context-manager-header">
          <h3>Context Settings Manager</h3>
          <div className="context-header-actions">
            <button
              className="context-action-button"
              onClick={() => setShowCreateDialog(true)}
            >
              New
            </button>
            <button
              className="context-action-button"
              onClick={() => setShowLoadDialog(true)}
            >
              Load
            </button>
          </div>
          <button className="context-manager-close" onClick={onClose}>
            ×
          </button>
        </div>

        <div className="context-manager-content">
          {/* Current Context Section */}
          <div className="context-current-section">
            <h4>Current Context</h4>
            {currentContextSettings ? (
              <div className="context-current-info">
                <div className="context-current-name">{currentContextSettings.name}</div>
                {currentContextSettings.description && (
                  <div className="context-current-description">{currentContextSettings.description}</div>
                )}
                <div className="context-current-meta">
                  Last updated: {new Date(currentContextSettings.updatedAt).toLocaleDateString()}
                </div>
              </div>
            ) : (
              <div className="context-no-current">No context settings selected</div>
            )}
          </div>

          {/* Available Context Settings List */}
          <div className="context-list-section">
            <div className="context-list-header">
              <h4>Available Context Settings</h4>
              <div className="context-list-actions">
                <button
                  className="context-action-button small"
                  onClick={() => setShowCreateDialog(true)}
                >
                  + New
                </button>
              </div>
            </div>
            <div className="context-settings-list">
              {availableSettings.map(setting => (
                <div
                  key={setting.id}
                  className={`context-setting-item ${currentContextSettings?.id === setting.id ? 'active' : ''}`}
                >
                  <div className="context-setting-info">
                    <div className="context-setting-name">{setting.name}</div>
                    {setting.description && (
                      <div className="context-setting-description">{setting.description}</div>
                    )}
                    <div className="context-setting-meta">
                      Updated: {new Date(setting.updatedAt).toLocaleDateString()}
                    </div>
                  </div>
                  <div className="context-setting-actions">
                    <button
                      className="context-action-button small"
                      onClick={() => handleSelect(setting.id)}
                    >
                      Select
                    </button>
                    <button
                      className="context-action-button small secondary"
                      onClick={() => handleDuplicate(setting.id, setting.name)}
                    >
                      Duplicate
                    </button>
                    <button
                      className="context-action-button small danger"
                      onClick={() => handleDelete(setting.id)}
                    >
                      {selectedForDelete === setting.id ? 'Confirm' : 'Delete'}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Create Dialog */}
        {showCreateDialog && (
          <div className="context-create-overlay">
            <div className="context-create-dialog">
              <h4>Create New Context Settings</h4>
              <div className="context-create-form">
                <input
                  type="text"
                  placeholder="Context settings name..."
                  value={newName}
                  onChange={(e) => setNewName(e.target.value)}
                  className="context-create-input"
                />
                <textarea
                  placeholder="Description (optional)..."
                  value={newDescription}
                  onChange={(e) => setNewDescription(e.target.value)}
                  className="context-create-textarea"
                  rows={3}
                />
                <div className="context-create-actions">
                  <button onClick={handleCreate} className="context-action-button">
                    Create
                  </button>
                  <button 
                    onClick={() => {
                      setShowCreateDialog(false);
                      setNewName('');
                      setNewDescription('');
                    }} 
                    className="context-action-button secondary"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Load Dialog */}
        {showLoadDialog && (
          <div className="context-create-overlay">
            <div className="context-create-dialog">
              <h4>Load Context Settings</h4>
              <div className="context-load-list">
                {availableSettings.length > 0 ? (
                  availableSettings.map(setting => (
                    <div
                      key={setting.id}
                      className={`context-load-item ${currentContextSettings?.id === setting.id ? 'active' : ''}`}
                      onClick={() => {
                        handleSelect(setting.id);
                        setShowLoadDialog(false);
                      }}
                    >
                      <div className="context-load-name">{setting.name}</div>
                      <div className="context-load-description">
                        {setting.description || 'No description'}
                      </div>
                      <div className="context-load-meta">
                        Updated: {new Date(setting.updatedAt).toLocaleDateString()}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="context-no-settings">
                    No context settings available. Create a new one first.
                  </div>
                )}
              </div>
              <div className="context-load-actions">
                <label className="context-import-button">
                  Import from File
                  <input
                    type="file"
                    accept=".json"
                    onChange={(e) => {
                      handleImport(e);
                      setShowLoadDialog(false);
                    }}
                    style={{ display: 'none' }}
                  />
                </label>
                <button 
                  onClick={() => setShowLoadDialog(false)} 
                  className="context-action-button secondary"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="context-manager-footer">
          <div className="context-manager-info">
            <small>{availableSettings.length} context settings available</small>
          </div>
          <button className="context-action-button" onClick={onClose}>
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ContextManagerDialog; 