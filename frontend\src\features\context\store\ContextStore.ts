/**
 * ContextStore.ts
 * 
 * Manages Context Settings as independent, reusable entities.
 * Context Settings can be shared across multiple MindBooks.
 */

import { create } from 'zustand';
import { v4 as uuidv4 } from 'uuid';

// Context input types
export type ContextInputType = 'text' | 'file' | 'reference';
export type ContextLevel = 'foundational' | 'strategic' | 'operational';

// Individual context input data
export interface ContextInput {
  type: ContextInputType;
  content: string;
  metadata?: {
    fileName?: string;
    fileType?: string;
    referenceSheetId?: string;
    referenceTitle?: string;
  };
}

// Context level data (foundational, strategic, operational)
export interface ContextLevelData {
  level: ContextLevel;
  inputs: ContextInput[];
  activeInputType: ContextInputType;
}

// Complete context settings entity
export interface ContextSettings {
  id: string;
  name: string;
  description?: string;
  foundational: ContextLevelData;
  strategic: ContextLevelData;
  operational: ContextLevelData;
  createdAt: number;
  updatedAt: number;
  tags?: string[];
}

// Store state interface
interface ContextState {
  // Current context settings being edited/used
  currentContextSettings: ContextSettings | null;
  
  // All available context settings
  availableContextSettings: ContextSettings[];
  
  // UI state
  activeLevel: ContextLevel;
  
  // Actions
  createNewContextSettings: (name: string, description?: string) => string;
  loadContextSettings: (id: string) => boolean;
  saveContextSettings: (contextSettings: ContextSettings) => boolean;
  updateContextInput: (level: ContextLevel, type: ContextInputType, content: string, metadata?: any) => void;
  setActiveLevel: (level: ContextLevel) => void;
  setActiveInputType: (level: ContextLevel, type: ContextInputType) => void;
  deleteContextSettings: (id: string) => boolean;
  duplicateContextSettings: (id: string, newName: string) => string | null;
  getContextSettingsList: () => ContextSettings[];
  exportContextSettings: (id: string) => string | null;
  importContextSettings: (data: string) => string | null;
  clearCurrentContextSettings: () => void;
}

// Helper function to create empty context level data
const createEmptyContextLevel = (level: ContextLevel): ContextLevelData => ({
  level,
  inputs: [
    { type: 'text', content: '' },
    { type: 'file', content: '', metadata: {} },
    { type: 'reference', content: '', metadata: {} }
  ],
  activeInputType: 'text'
});

// Helper function to create new context settings
const createNewContextSettingsEntity = (name: string, description?: string): ContextSettings => ({
  id: uuidv4(),
  name: name.trim(),
  description: description?.trim(),
  foundational: createEmptyContextLevel('foundational'),
  strategic: createEmptyContextLevel('strategic'),
  operational: createEmptyContextLevel('operational'),
  createdAt: Date.now(),
  updatedAt: Date.now(),
  tags: []
});

// Storage keys
const CONTEXT_SETTINGS_PREFIX = 'context_settings_';
const CONTEXT_SETTINGS_LIST_KEY = 'context_settings_list';
const CURRENT_CONTEXT_SETTINGS_KEY = 'current_context_settings';

// Create the store
export const useContextStore = create<ContextState>((set, get) => ({
  // Initial state
  currentContextSettings: null,
  availableContextSettings: [],
  activeLevel: 'foundational',

  // Create new context settings
  createNewContextSettings: (name: string, description?: string) => {
    const newContextSettings = createNewContextSettingsEntity(name, description);
    
    // Save to localStorage
    try {
      localStorage.setItem(
        CONTEXT_SETTINGS_PREFIX + newContextSettings.id, 
        JSON.stringify(newContextSettings)
      );
      
      // Update list
      const currentList = get().getContextSettingsList();
      const updatedList = [...currentList, {
        id: newContextSettings.id,
        name: newContextSettings.name,
        description: newContextSettings.description,
        createdAt: newContextSettings.createdAt,
        updatedAt: newContextSettings.updatedAt
      }];
      
      localStorage.setItem(CONTEXT_SETTINGS_LIST_KEY, JSON.stringify(updatedList));
      
      // Update store state
      set(state => ({
        currentContextSettings: newContextSettings,
        availableContextSettings: [...state.availableContextSettings, newContextSettings]
      }));
      
      console.log('ContextStore: Created new context settings:', newContextSettings.name);
      return newContextSettings.id;
    } catch (error) {
      console.error('ContextStore: Failed to create context settings:', error);
      return '';
    }
  },

  // Load context settings by ID
  loadContextSettings: (id: string) => {
    try {
      const savedData = localStorage.getItem(CONTEXT_SETTINGS_PREFIX + id);
      if (!savedData) {
        console.warn('ContextStore: Context settings not found:', id);
        return false;
      }

      const contextSettings: ContextSettings = JSON.parse(savedData);
      
      set({
        currentContextSettings: contextSettings,
        activeLevel: 'foundational' // Reset to foundational when loading
      });
      
      // Set as current context settings
      localStorage.setItem(CURRENT_CONTEXT_SETTINGS_KEY, id);
      
      console.log('ContextStore: Loaded context settings:', contextSettings.name);
      return true;
    } catch (error) {
      console.error('ContextStore: Failed to load context settings:', error);
      return false;
    }
  },

  // Save current context settings
  saveContextSettings: (contextSettings: ContextSettings) => {
    try {
      const updatedSettings = {
        ...contextSettings,
        updatedAt: Date.now()
      };

      localStorage.setItem(
        CONTEXT_SETTINGS_PREFIX + updatedSettings.id, 
        JSON.stringify(updatedSettings)
      );

      set({ currentContextSettings: updatedSettings });
      
      console.log('ContextStore: Saved context settings:', updatedSettings.name);
      return true;
    } catch (error) {
      console.error('ContextStore: Failed to save context settings:', error);
      return false;
    }
  },

  // Update context input
  updateContextInput: (level: ContextLevel, type: ContextInputType, content: string, metadata?: any) => {
    const state = get();
    if (!state.currentContextSettings) {
      console.warn('ContextStore: No current context settings to update');
      return;
    }

    const updatedSettings = { ...state.currentContextSettings };
    const levelData = updatedSettings[level];
    
    // Find and update the input
    const inputIndex = levelData.inputs.findIndex(input => input.type === type);
    if (inputIndex !== -1) {
      levelData.inputs[inputIndex] = {
        type,
        content: content,
        metadata: metadata || levelData.inputs[inputIndex].metadata
      };
    }

    // Auto-save the updated settings
    get().saveContextSettings(updatedSettings);
  },

  // Set active level (foundational, strategic, operational)
  setActiveLevel: (level: ContextLevel) => {
    set({ activeLevel: level });
  },

  // Set active input type for a level
  setActiveInputType: (level: ContextLevel, type: ContextInputType) => {
    const state = get();
    if (!state.currentContextSettings) return;

    const updatedSettings = { ...state.currentContextSettings };
    updatedSettings[level].activeInputType = type;

    set({ currentContextSettings: updatedSettings });
  },

  // Delete context settings
  deleteContextSettings: (id: string) => {
    try {
      localStorage.removeItem(CONTEXT_SETTINGS_PREFIX + id);
      
      // Update list
      const currentList = get().getContextSettingsList();
      const updatedList = currentList.filter(item => item.id !== id);
      localStorage.setItem(CONTEXT_SETTINGS_LIST_KEY, JSON.stringify(updatedList));
      
      // Update store state
      set(state => ({
        availableContextSettings: state.availableContextSettings.filter(cs => cs.id !== id),
        currentContextSettings: state.currentContextSettings?.id === id ? null : state.currentContextSettings
      }));
      
      console.log('ContextStore: Deleted context settings:', id);
      return true;
    } catch (error) {
      console.error('ContextStore: Failed to delete context settings:', error);
      return false;
    }
  },

  // Duplicate context settings
  duplicateContextSettings: (id: string, newName: string) => {
    try {
      const savedData = localStorage.getItem(CONTEXT_SETTINGS_PREFIX + id);
      if (!savedData) return null;

      const originalSettings: ContextSettings = JSON.parse(savedData);
      const duplicatedSettings = createNewContextSettingsEntity(newName);
      
      // Copy the content but keep new ID and timestamps
      duplicatedSettings.foundational = { ...originalSettings.foundational };
      duplicatedSettings.strategic = { ...originalSettings.strategic };
      duplicatedSettings.operational = { ...originalSettings.operational };
      duplicatedSettings.description = originalSettings.description;
      duplicatedSettings.tags = [...(originalSettings.tags || [])];

      // Save the duplicated settings
      get().saveContextSettings(duplicatedSettings);
      
      return duplicatedSettings.id;
    } catch (error) {
      console.error('ContextStore: Failed to duplicate context settings:', error);
      return null;
    }
  },

  // Get list of all context settings
  getContextSettingsList: () => {
    try {
      const listData = localStorage.getItem(CONTEXT_SETTINGS_LIST_KEY);
      return listData ? JSON.parse(listData) : [];
    } catch (error) {
      console.error('ContextStore: Failed to get context settings list:', error);
      return [];
    }
  },

  // Export context settings as JSON
  exportContextSettings: (id: string) => {
    try {
      const savedData = localStorage.getItem(CONTEXT_SETTINGS_PREFIX + id);
      return savedData || null;
    } catch (error) {
      console.error('ContextStore: Failed to export context settings:', error);
      return null;
    }
  },

  // Import context settings from JSON
  importContextSettings: (data: string) => {
    try {
      const contextSettings: ContextSettings = JSON.parse(data);
      
      // Generate new ID to avoid conflicts
      contextSettings.id = uuidv4();
      contextSettings.updatedAt = Date.now();
      
      // Save the imported settings
      const success = get().saveContextSettings(contextSettings);
      return success ? contextSettings.id : null;
    } catch (error) {
      console.error('ContextStore: Failed to import context settings:', error);
      return null;
    }
  },

  // Clear current context settings (for new sessions)
  clearCurrentContextSettings: () => {
    set({ 
      currentContextSettings: null,
      activeLevel: 'foundational' // Reset to foundational level
    });
    localStorage.removeItem(CURRENT_CONTEXT_SETTINGS_KEY);
    console.log('ContextStore: Cleared current context settings');
  }
}));

// Initialize the store on first load
const initializeContextStore = () => {
  try {
    // Don't auto-load context settings for new sessions
    // Users should explicitly choose to load or create context settings
    console.log('ContextStore: Initialized with empty context settings');
  } catch (error) {
    console.error('ContextStore: Failed to initialize:', error);
  }
};

// Auto-initialize
initializeContextStore(); 