// Placeholder file for: frontend\src\components\MindMap\core\models\HatContribution.ts

/**
 * HatContribution model for MindMap
 * Represents contributions from different thinking hats to a node
 */

import { v4 as uuidv4 } from 'uuid';
import { HatType } from './Node';

/**
 * Main HatContribution interface
 */
export interface HatContribution {
  // Core properties
  id: string;           // Unique identifier
  nodeId: string;       // Associated node ID
  hat: HatType;         // Hat type
  content: string;      // Contribution content
  
  // Metadata
  author?: string;      // Who created this contribution (user or LLM)
  contextPrompt?: string; // Prompt that generated this contribution
  
  // LLM metadata
  confidence?: number;  // LLM confidence score (0-1)
  reasoning?: string;   // LLM reasoning for this contribution
  
  // Timestamps
  createdAt: number;    // Creation timestamp
  updatedAt: number;    // Last update timestamp
}

/**
 * Default values for a new hat contribution
 */
export const DEFAULT_HAT_CONTRIBUTION: Omit<HatContribution, 'id' | 'nodeId' | 'hat' | 'content'> = {
  createdAt: Date.now(),
  updatedAt: Date.now()
};

/**
 * Creates a new hat contribution
 */
export function createHatContribution(params: {
  nodeId: string;
  hat: HatType;
  content: string;
  author?: string;
  contextPrompt?: string;
  confidence?: number;
  reasoning?: string;
}): HatContribution {
  return {
    ...DEFAULT_HAT_CONTRIBUTION,
    id: uuidv4(),
    nodeId: params.nodeId,
    hat: params.hat,
    content: params.content,
    author: params.author,
    contextPrompt: params.contextPrompt,
    confidence: params.confidence,
    reasoning: params.reasoning,
    createdAt: Date.now(),
    updatedAt: Date.now()
  };
}

/**
 * Updates a hat contribution
 */
export function updateHatContribution(
  contribution: HatContribution, 
  updates: Partial<HatContribution>
): HatContribution {
  return {
    ...contribution,
    ...updates,
    updatedAt: Date.now()
  };
}

/**
 * Hat contribution prompt templates by hat type
 */
export const HAT_PROMPTS: Record<HatType, string> = {
  blue: "As the process coordinator (Blue Hat), organize and structure this topic: {{nodeContent}}",
  white: "Focusing only on facts and information (White Hat), what do we know about: {{nodeContent}}",
  red: "From an emotional and intuitive perspective (Red Hat), how do you feel about: {{nodeContent}}",
  black: "Critically evaluate potential problems and risks (Black Hat) for: {{nodeContent}}",
  yellow: "Looking at the positive aspects and benefits (Yellow Hat), what opportunities exist in: {{nodeContent}}",
  green: "Generate creative ideas and alternatives (Green Hat) for: {{nodeContent}}"
};

/**
 * Get the appropriate prompt template for a hat type
 */
export function getHatPrompt(hat: HatType, nodeContent: string): string {
  return HAT_PROMPTS[hat].replace('{{nodeContent}}', nodeContent);
}

/**
 * Hat colors for visualization
 */
export const HAT_COLORS: Record<HatType, string> = {
  blue: '#3b82f6',   // Blue
  white: '#f8fafc',  // White
  red: '#ef4444',    // Red
  black: '#1e293b',  // Dark Slate
  yellow: '#eab308', // Yellow
  green: '#22c55e'   // Green
};
