"""
Logging API routes for MindBack application.
Provides endpoints for storing and retrieving logs.
"""

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import logging
from datetime import datetime
import os
import json

# Get the existing logger
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/logging", tags=["logging"])

# Models
class LogEntry(BaseModel):
    """Model for a log entry"""
    timestamp: str
    event_type: str
    source: str  # 'frontend' or 'backend'
    details: Optional[Dict[str, Any]] = None
    message: str

class LogResponse(BaseModel):
    """Response model for log operations"""
    success: bool
    message: str

# In-memory log storage (will be lost on server restart)
# For a production app, consider using a database
log_entries = []

# Maximum number of log entries to keep in memory
MAX_LOG_ENTRIES = 1000

# Endpoints
@router.post("/event", response_model=LogResponse)
async def log_event(entry: LogEntry):
    """
    Log an event from the frontend or backend
    """
    try:
        # Add the log entry to the in-memory storage
        log_entries.append(entry.dict())
        
        # Trim the log entries if they exceed the maximum
        if len(log_entries) > MAX_LOG_ENTRIES:
            log_entries.pop(0)  # Remove the oldest entry
        
        # Also log to the backend logger
        logger.info(f"[{entry.source}] {entry.event_type}: {entry.message}")
        
        return LogResponse(
            success=True,
            message="Log entry recorded successfully"
        )
    except Exception as e:
        logger.error(f"Error recording log entry: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error recording log entry: {str(e)}")

@router.get("/events", response_model=List[LogEntry])
async def get_log_events(limit: int = 100, source: Optional[str] = None):
    """
    Get recent log events, optionally filtered by source
    """
    try:
        # Filter by source if provided
        filtered_logs = log_entries
        if source:
            filtered_logs = [log for log in log_entries if log["source"] == source]
        
        # Return the most recent logs up to the limit
        return filtered_logs[-limit:]
    except Exception as e:
        logger.error(f"Error retrieving log entries: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving log entries: {str(e)}")

@router.get("/health")
async def logging_health():
    """
    Check if the logging service is working
    """
    return {
        "status": "ok",
        "log_entries_count": len(log_entries),
        "max_entries": MAX_LOG_ENTRIES
    }
