/**
 * Asset Path Utility
 * 
 * This utility provides a standardized way to reference assets across the application.
 * It handles path differences between development and production environments.
 */

/**
 * Gets the correct path for an asset in the public directory
 * @param path The path to the asset, relative to the public directory
 * @returns The correct path to use in the application
 */
export function getAssetPath(path: string): string {
  // Ensure path starts with a slash but doesn't have a leading public or ./public
  const cleanPath = path.replace(/^(\.\/)?Public\//, '').replace(/^\//, '');
  
  // In development, Vite serves assets from the public directory at root
  // In production, assets are copied to the root of the build directory
  return `/${cleanPath}`;
}

/**
 * Logo assets with fallbacks
 */
export const LogoAssets = {
  // Primary logo 
  PRIMARY_LOGO: getAssetPath('Logo/MB_logo.jpg'),
  
  // Fallback logos in order of preference
  FALLBACK_LOGOS: [
    getAssetPath('Logo/mindback_logo.jpg'),
    getAssetPath('Logo/mindback_logo2.jpg'),
    getAssetPath('Logo/MB_logo_new.jpg')
  ],
  
  /**
   * Get the primary logo with a fallback mechanism
   * @param onError Optional callback when primary logo fails to load
   * @returns An object with src and onError handler for React components
   */
  getPrimaryLogo(onError?: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void) {
    // Add cache buster for development
    const cacheBuster = process.env.NODE_ENV === 'development' ? `?v=${Date.now()}` : '';
    
    return {
      src: this.PRIMARY_LOGO + cacheBuster,
      onError: (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
        console.error('Primary logo failed to load:', this.PRIMARY_LOGO);
        
        // Try the first fallback with cache buster
        e.currentTarget.src = this.FALLBACK_LOGOS[0] + cacheBuster;
        
        // Call the optional error handler
        if (onError) onError(e);
      }
    };
  }
}; 