# Mindmap Creation Workflow Fixes

## Problem Summary

The mindmap creation workflow was experiencing React Hooks violations in both the manual (Build Mind Map button) and automatic (teleological intent) workflows. The specific issues were:

1. **Hooks Order Violation**: The order of hooks was changing between renders, causing React to throw errors.
2. **Conditional Hook Calls**: Hooks were being called conditionally, violating the Rules of Hooks.
3. **Hooks Inside Hooks**: Hooks were being called inside other hooks (specifically inside useEffect and useMemo).
4. **Duplicate Initialization**: The mindmap was being initialized twice - once in MindMapAdapter.ts and once in MindSheet.tsx.

## Changes Made

### 1. Fixed MindSheet Component

- Ensured all hooks are called unconditionally at the top level in the exact same order every time
- Moved all useMemo hooks to the top level to ensure consistent order
- Used refs to store content to avoid unnecessary re-renders
- Consolidated useEffect hooks to ensure consistent hook order
- Ensured proper dependency arrays for useEffect and useMemo

```javascript
// Before
const MindSheet: React.FC<MindSheetProps> = ({
  id,
  title,
  contentType,
  isActive,
  content,
  onActivate
}) => {
  const [initialized, setInitialized] = useState(false);
  const [sheetStore, setSheetStore] = useState<ReturnType<typeof getMindMapStore> | null>(null);
  const storeRef = useRef<ReturnType<typeof getMindMapStore> | null>(null);
  const contentRef = useRef<any>(content);
  const chatForkStore = useChatForkStore();
  const mindBookStore = useMindBookStore();
  
  // More hooks and logic...
}

// After
const MindSheet: React.FC<MindSheetProps> = ({
  id,
  title,
  contentType,
  isActive,
  content,
  onActivate
}) => {
  // 1. useState hooks
  const [initialized, setInitialized] = useState(false);
  const [sheetStore, setSheetStore] = useState<ReturnType<typeof getMindMapStore> | null>(null);
  
  // 2. useRef hooks
  const storeRef = useRef<ReturnType<typeof getMindMapStore> | null>(null);
  const contentRef = useRef<any>(content);
  
  // 3. Custom hooks
  const chatForkStore = useChatForkStore();
  const mindBookStore = useMindBookStore();
  
  // 4. useMemo hooks for handlers and components
  const openMindMapManager = useMemo(() => (e: React.MouseEvent) => {
    // Handler logic...
  }, []);
  
  const handleCloseMindMapManager = useMemo(() => () => {
    // Handler logic...
  }, []);
  
  // More hooks and logic...
}
```

### 2. Fixed Helper Functions

- Refactored `createChildNodesFromMBCP` to be a pure function that doesn't use any hooks
- Updated `initializeMindMapForSheet` to not call any hooks directly
- Passed `storeState` instead of `store` to avoid calling hooks inside other functions

```javascript
// Before
const createChildNodesFromMBCP = (
  store: ReturnType<typeof getMindMapStore>,
  parentId: string,
  children: any[]
) => {
  const storeState = store.getState();
  // Logic...
}

// After
const createChildNodesFromMBCP = (
  storeState: any,
  parentId: string,
  children: any[]
) => {
  // Logic...
}
```

### 3. Consolidated Initialization Logic

- Modified MindMapAdapter.ts to use the sheet-specific store from MindMapStoreFactory
- Updated the initializeMindMap function to use the sheet-specific store
- Updated the processChildNodes function to use the sheet-specific store
- Removed duplicate initialization logic

```javascript
// Before
export const initializeMindMap = (mbcpData: any): boolean => {
  // Get the MindMap store
  const mindMapStore = useMindMapStore.getState();
  // Logic...
}

// After
export const initializeMindMap = (mbcpData: any): boolean => {
  // Get the sheet-specific store
  const sheetStore = getMindMapStore(activeSheet.id);
  // Logic...
}
```

### 4. Implemented Proper State Management

- Ensured all mindmap operations use the sheet-specific store
- Removed direct usage of the global MindMapStore
- Ensured proper cleanup when switching between sheets

## Testing

To verify that our changes have fixed the React Hooks violations, we should test both the manual and automatic workflows for building mindmaps:

1. **Manual Workflow**: Click the "Build Mind Map" button after receiving a teleological response
2. **Automatic Workflow**: Select "Teleological" from the intent dropdown and enter a prompt

Both workflows should now work without any React Hooks violations in the console.

## Conclusion

The root cause of the React Hooks violations was that the MindSheet component was using hooks conditionally and in a different order during remounting. Additionally, there was duplicate initialization logic between MindMapAdapter.ts and MindSheet.tsx, causing conflicts.

By ensuring all hooks are called unconditionally at the top level and consolidating the initialization logic to use the sheet-specific store from MindMapStoreFactory, we've fixed the React Hooks violations in both workflows.
