<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindBack Frontend Visualization</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #2980b9;
            margin-top: 30px;
        }
        .section {
            margin-bottom: 40px;
            padding: 20px;
            border-radius: 5px;
            background-color: #f8f9fa;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .note {
            background-color: #fff8dc;
            border-left: 4px solid #ffd700;
            padding: 10px 15px;
            margin: 20px 0;
        }
        .section-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .dot {
            height: 12px;
            width: 12px;
            background-color: #3498db;
            border-radius: 50%;
            display: inline-block;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
        });
    </script>
</head>
<body>
    <h1>MindBack Frontend Visualization</h1>
    
    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>Application Architecture Overview</h2>
        </div>
        <div class="mermaid">
        flowchart TD
            App["App / AppRefactored"] --> MindBook["MindBook"]
            App --> GovernanceChat["GovernanceChat"]
            
            MindBook --> MindSheet["MindSheet"]
            MindSheet --> MindMapCanvas["MindMapCanvas"]
            MindSheet --> ChatForkCanvas["ChatForkCanvas"]
            
            GovernanceChat --> MessageList["MessageList"]
            GovernanceChat --> MessageInput["MessageInput"]
            GovernanceChat --> ModelSelector["ModelSelector"]
            
            MessageInput --> GovernanceLLM["GovernanceLLM API"]
            GovernanceLLM --> MainRouter["MainRouter"]
            
            MainRouter --> MindMapAction["MindMap Action"]
            MainRouter --> ChatForkAction["ChatFork Action"]
            MainRouter --> FactualAction["Factual Response"]
            
            MindMapAction --> MindBookStore["MindBookStore"]
            ChatForkAction --> MindBookStore
            MindBookStore --> MindSheet
            
            classDef container fill:#f9a825,stroke:#f57f17,stroke-width:2px
            classDef component fill:#bbdefb,stroke:#64b5f6,stroke-width:1px
            classDef service fill:#ce93d8,stroke:#ab47bc,stroke-width:1px
            classDef store fill:#a5d6a7,stroke:#66bb6a,stroke-width:1px
            classDef router fill:#ffcc80,stroke:#ff9800,stroke-width:1px
            
            class App,MindBook,GovernanceChat container
            class MindSheet,MindMapCanvas,ChatForkCanvas,MessageList,MessageInput,ModelSelector component
            class GovernanceLLM service
            class MindBookStore,MindMapStore store
            class MainRouter router
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>Directory Structure</h2>
        </div>
        <div class="mermaid">
        flowchart LR
            Frontend["Frontend"] --> SrcDir["src/"]
            
            SrcDir --> ComponentsDir["components/"]
            SrcDir --> FeaturesDir["features/"]
            SrcDir --> CoreDir["core/"]
            SrcDir --> ServicesDir["services/"]
            SrcDir --> GovernanceDir["governance/"]
            SrcDir --> UtilsDir["utils/"]
            
            ComponentsDir --> MindMapDir["MindMap/"]
            ComponentsDir --> ChatForkDir["ChatFork/"]
            ComponentsDir --> SharedDir["shared/"]
            
            FeaturesDir --> MindmapFeatureDir["mindmap/"]
            FeaturesDir --> MindsheetDir["mindsheet/"]
            FeaturesDir --> GovernanceFeatureDir["governance/"]
            
            CoreDir --> StateDir["state/"]
            CoreDir --> AdaptersDir["adapters/"]
            CoreDir --> ServicesDir2["services/"]
            CoreDir --> RoutingDir["routing/"]
            
            ServicesDir --> ApiDir["api/"]
            ServicesDir --> TransformersDir["transformers/"]
            
            GovernanceDir --> ChatDir["chat/"]
            GovernanceDir --> AgentsDir["agents/"]
            GovernanceDir --> TemplatesDir["templates/"]
            
            classDef main fill:#f9a825,stroke:#f57f17,stroke-width:2px
            classDef directory fill:#4caf50,stroke:#388e3c,stroke-width:1px,color:white
            classDef feature fill:#bbdefb,stroke:#64b5f6,stroke-width:1px
            
            class Frontend main
            class SrcDir,ComponentsDir,FeaturesDir,CoreDir,ServicesDir,GovernanceDir,UtilsDir directory
            class MindMapDir,ChatForkDir,SharedDir,MindmapFeatureDir,MindsheetDir,GovernanceFeatureDir,StateDir,AdaptersDir,ServicesDir2,RoutingDir,ApiDir,TransformersDir,ChatDir,AgentsDir,TemplatesDir feature
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>User Input Flow</h2>
        </div>
        <div class="mermaid">
        sequenceDiagram
            participant User
            participant GovernanceChat
            participant MessageInput
            participant API as GovernanceLLM API
            participant MainRouter
            participant MindBookStore
            participant MindSheet
            
            User->>GovernanceChat: Enter message
            GovernanceChat->>MessageInput: Capture input
            MessageInput->>GovernanceChat: Submit message
            GovernanceChat->>API: Send message to LLM
            API-->>GovernanceChat: Return MBCP response
            GovernanceChat->>MainRouter: Route response based on intent
            
            alt Factual Intent
                MainRouter-->>GovernanceChat: Display in GovernanceChat
            else Exploratory Intent
                MainRouter->>MindBookStore: Create ChatFork sheet
                MindBookStore->>MindSheet: Render ChatFork
                MindSheet-->>User: Display ChatFork
            else Teleological Intent
                MainRouter->>MindBookStore: Create MindMap sheet
                MindBookStore->>MindSheet: Render MindMap
                MindSheet-->>User: Display MindMap
            end
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>State Management</h2>
        </div>
        <div class="mermaid">
        flowchart TD
            subgraph Stores["Zustand Stores"]
                MindMapStore["MindMapStore"]
                MindBookStore["MindBookStore"]
                ChatForkStore["ChatForkStore"]
                ChatStore["ChatStore"]
            end
            
            subgraph Components["React Components"]
                MindMapCanvas["MindMapCanvas"]
                ChatForkCanvas["ChatForkCanvas"]
                GovernanceChat["GovernanceChat"]
                MindSheet["MindSheet"]
                MindBook["MindBook"]
            end
            
            MindMapStore --> MindMapCanvas
            ChatForkStore --> ChatForkCanvas
            ChatStore --> GovernanceChat
            MindBookStore --> MindSheet
            MindBookStore --> MindBook
            
            subgraph Actions["Store Actions"]
                MindMapActions["- addNode\n- updateNode\n- deleteNode\n- addConnection\n- selectNode"]
                MindBookActions["- createSheet\n- createMindMapSheet\n- createChatForkSheet\n- setActiveSheet"]
                ChatForkActions["- showChatFork\n- hideChatFork\n- setSelectedText"]
                ChatActions["- addMessage\n- setMessages\n- clearMessages"]
            end
            
            MindMapStore --- MindMapActions
            MindBookStore --- MindBookActions
            ChatForkStore --- ChatForkActions
            ChatStore --- ChatActions
            
            classDef store fill:#a5d6a7,stroke:#66bb6a,stroke-width:1px
            classDef component fill:#bbdefb,stroke:#64b5f6,stroke-width:1px
            classDef action fill:#ffcc80,stroke:#ff9800,stroke-width:1px
            
            class MindMapStore,MindBookStore,ChatForkStore,ChatStore store
            class MindMapCanvas,ChatForkCanvas,GovernanceChat,MindSheet,MindBook component
            class MindMapActions,MindBookActions,ChatForkActions,ChatActions action
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>MainRouter Implementation</h2>
        </div>
        <div class="mermaid">
        flowchart TD
            LLMResponse["LLM Response"] --> MainRouter["MainRouter.routeIntent()"]
            
            MainRouter --> IntentCheck{{"What is the intent?"}}
            
            IntentCheck -- "factual" --> FactualAction["Display in GovernanceChat"]
            IntentCheck -- "exploratory" --> ExploratoryAction["Create ChatFork Sheet"]
            IntentCheck -- "teleological" --> TeleologicalAction["Create MindMap Sheet"]
            IntentCheck -- "instantiation" --> InstantiationAction["Create Template Sheet"]
            IntentCheck -- "situational" --> SituationalAction["Create Situational Sheet"]
            IntentCheck -- "miscellaneous" --> MiscAction["Display in GovernanceChat"]
            
            ExploratoryAction --> ChatForkSheet["ChatFork Sheet"]
            TeleologicalAction --> MindMapSheet["MindMap Sheet"]
            InstantiationAction --> TemplateSheet["Template Sheet"]
            SituationalAction --> SituationalSheet["Situational Sheet"]
            
            classDef input fill:#f9a825,stroke:#f57f17,stroke-width:2px
            classDef router fill:#ffcc80,stroke:#ff9800,stroke-width:1px
            classDef decision fill:#fff59d,stroke:#ffee58,stroke-width:1px
            classDef action fill:#ce93d8,stroke:#ab47bc,stroke-width:1px
            classDef output fill:#90caf9,stroke:#42a5f5,stroke-width:1px
            
            class LLMResponse input
            class MainRouter router
            class IntentCheck decision
            class FactualAction,ExploratoryAction,TeleologicalAction,InstantiationAction,SituationalAction,MiscAction action
            class ChatForkSheet,MindMapSheet,TemplateSheet,SituationalSheet output
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>MindBook and MindSheet Structure</h2>
        </div>
        <div class="mermaid">
        flowchart TD
            MindBook["MindBook Component"] --> SheetTabs["Sheet Tabs"]
            MindBook --> SheetsContainer["Sheets Container"]
            
            SheetsContainer --> MindSheet1["MindSheet (MindMap)"]
            SheetsContainer --> MindSheet2["MindSheet (ChatFork)"]
            SheetsContainer --> MindSheet3["MindSheet (Template)"]
            
            MindSheet1 --> MindMapCanvas["MindMapCanvas"]
            MindSheet2 --> ChatForkCanvas["ChatForkCanvas"]
            MindSheet3 --> TemplateCanvas["TemplateCanvas"]
            
            SheetTabs --> Tab1["MindMap Tab"]
            SheetTabs --> Tab2["ChatFork Tab"]
            SheetTabs --> Tab3["Template Tab"]
            
            Tab1 -.-> MindSheet1
            Tab2 -.-> MindSheet2
            Tab3 -.-> MindSheet3
            
            classDef container fill:#f9a825,stroke:#f57f17,stroke-width:2px
            classDef component fill:#bbdefb,stroke:#64b5f6,stroke-width:1px
            classDef canvas fill:#ce93d8,stroke:#ab47bc,stroke-width:1px
            classDef tab fill:#a5d6a7,stroke:#66bb6a,stroke-width:1px
            
            class MindBook,SheetsContainer container
            class MindSheet1,MindSheet2,MindSheet3,SheetTabs component
            class MindMapCanvas,ChatForkCanvas,TemplateCanvas canvas
            class Tab1,Tab2,Tab3 tab
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>API Communication Flow</h2>
        </div>
        <div class="mermaid">
        sequenceDiagram
            participant Frontend
            participant API as Backend API
            participant LLM as LLM Service
            
            Frontend->>API: POST /api/llm/chat
            API->>LLM: Call LLM with YAML prompt
            LLM-->>API: Return raw response
            API->>API: Process response to MBCP format
            API-->>Frontend: Return MBCP response
            
            alt Teleological Intent
                Frontend->>Frontend: Route to MindMap
            else Exploratory Intent
                Frontend->>Frontend: Route to ChatFork
            else Factual Intent
                Frontend->>Frontend: Display in GovernanceChat
            end
            
            Note over Frontend,API: All communication uses the MBCP format
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>Component Interaction</h2>
        </div>
        <div class="mermaid">
        flowchart TD
            subgraph AppContainer["App Container"]
                App["App / AppRefactored"]
            end
            
            subgraph GovernanceContainer["Governance Container"]
                GovernanceChat["GovernanceChat"]
                MessageList["MessageList"]
                MessageInput["MessageInput"]
                ModelSelector["ModelSelector"]
            end
            
            subgraph MindBookContainer["MindBook Container"]
                MindBook["MindBook"]
                SheetTabs["Sheet Tabs"]
                MindSheets["MindSheets"]
            end
            
            subgraph MindMapContainer["MindMap Container"]
                MindMapCanvas["MindMapCanvas"]
                NodeComponent["NodeComponent"]
                ConnectionComponent["ConnectionComponent"]
                NodeBox["NodeBox"]
            end
            
            subgraph ChatForkContainer["ChatFork Container"]
                ChatForkCanvas["ChatForkCanvas"]
                ChatForkNode["ChatForkNode"]
                ChatForkConnection["ChatForkConnection"]
            end
            
            App --> GovernanceChat
            App --> MindBook
            
            GovernanceChat --> MessageList
            GovernanceChat --> MessageInput
            GovernanceChat --> ModelSelector
            
            MindBook --> SheetTabs
            MindBook --> MindSheets
            
            MindSheets --> MindMapCanvas
            MindSheets --> ChatForkCanvas
            
            MindMapCanvas --> NodeComponent
            MindMapCanvas --> ConnectionComponent
            MindMapCanvas --> NodeBox
            
            ChatForkCanvas --> ChatForkNode
            ChatForkCanvas --> ChatForkConnection
            
            classDef container fill:#f9a825,stroke:#f57f17,stroke-width:2px,color:white
            classDef component fill:#bbdefb,stroke:#64b5f6,stroke-width:1px
            
            class AppContainer,GovernanceContainer,MindBookContainer,MindMapContainer,ChatForkContainer container
            class App,GovernanceChat,MessageList,MessageInput,ModelSelector,MindBook,SheetTabs,MindSheets,MindMapCanvas,NodeComponent,ConnectionComponent,NodeBox,ChatForkCanvas,ChatForkNode,ChatForkConnection component
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>Z-Index Layering</h2>
        </div>
        <div class="mermaid">
        flowchart TB
            subgraph ZIndexes["Z-Index Hierarchy"]
                direction TB
                PopoverMenus["Popover Menus (2200)"]
                GovernanceDialog["Governance Chat Dialog (2100)"]
                NodeDialog["Node Dialog (2000)"]
                Toolbar["Toolbar (1000)"]
                MindMapNodes["MindMap Nodes (200)"]
                MindMapConnections["MindMap Connections (150)"]
                MindMapCanvas["MindMap Canvas (100)"]
            end
            
            classDef zindex1 fill:#f9a825,stroke:#f57f17,stroke-width:2px
            classDef zindex2 fill:#ffb74d,stroke:#f57f17,stroke-width:2px
            classDef zindex3 fill:#fff176,stroke:#f57f17,stroke-width:2px
            classDef zindex4 fill:#aed581,stroke:#558b2f,stroke-width:2px
            classDef zindex5 fill:#4fc3f7,stroke:#0288d1,stroke-width:2px
            classDef zindex6 fill:#9575cd,stroke:#512da8,stroke-width:2px
            classDef zindex7 fill:#f48fb1,stroke:#c2185b,stroke-width:2px
            
            class PopoverMenus zindex1
            class GovernanceDialog zindex2
            class NodeDialog zindex3
            class Toolbar zindex4
            class MindMapNodes zindex5
            class MindMapConnections zindex6
            class MindMapCanvas zindex7
        </div>
    </div>

    <div class="note">
        <p><strong>Note:</strong> This visualization represents the current state of the frontend based on code analysis. 
        The architecture shows a modular approach with clear separation between components, features, and services. 
        The MindBook/MindSheet structure provides a flexible container for different content types (MindMap, ChatFork, etc.), 
        while the MainRouter handles routing of LLM responses based on intent.</p>
    </div>

</body>
</html>
