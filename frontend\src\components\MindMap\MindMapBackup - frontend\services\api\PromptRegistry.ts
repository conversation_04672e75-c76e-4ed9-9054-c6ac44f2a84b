import { PromptData, PromptResponse } from './types';

/**
 * PromptRegistry that handles communication with the backend API for prompt management
 */
export class PromptRegistry {
  private static instance: PromptRegistry | null = null;
  private apiBaseUrl: string;
  
  constructor() {
    this.apiBaseUrl = (window as any).API_BASE_URL || 'http://localhost:8000/api';
  }
  
  /**
   * Get the singleton instance of the PromptRegistry
   */
  public static getInstance(): PromptRegistry {
    if (!PromptRegistry.instance) {
      PromptRegistry.instance = new PromptRegistry();
    }
    return PromptRegistry.instance;
  }
  
  /**
   * Initialize the registry
   * @param apiBaseUrl Optional API base URL to use for fetching prompts
   */
  public static async initialize(apiBaseUrl?: string): Promise<void> {
    console.log('[PromptRegistry] 🔄 Initializing PromptRegistry');
    // Get or create instance with provided API URL
    const instance = PromptRegistry.getInstance();
    if (apiBaseUrl) {
      instance.apiBaseUrl = apiBaseUrl;
    }
    // Test connection to backend
    try {
      await instance.getPrompt('test');
      console.log('[PromptRegistry] ✅ Successfully connected to backend API');
    } catch (error) {
      console.warn('[PromptRegistry] ⚠️ Could not connect to backend API:', error);
    }
  }
  
  /**
   * Get a prompt by type from the backend
   * @param promptType The type of prompt to retrieve
   * @returns Promise resolving to the prompt response
   */
  public async getPrompt(promptType: string): Promise<PromptResponse> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/llm/prompt/${promptType}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch prompt: ${response.status} ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`Error fetching prompt '${promptType}':`, error);
      // Return a default error response
      return {
        success: false,
        prompt_type: promptType,
        file_path: '',
        content: '',
        parsed: {} as PromptData,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Get a system prompt by type from the backend
   * @param promptType The type of system prompt to retrieve
   * @returns Promise resolving to the system prompt content
   */
  public async getSystemPrompt(promptType: string): Promise<string> {
    try {
      const promptData = await this.getPrompt(`${promptType}_system`);
      if (promptData.success && promptData.parsed.system_role) {
        return promptData.parsed.system_role;
      }
      return ''; // Return empty string if no system role found
    } catch (error) {
      console.warn(`Could not fetch system prompt for '${promptType}':`, error);
      return ''; // Return empty string on error
    }
  }
} 