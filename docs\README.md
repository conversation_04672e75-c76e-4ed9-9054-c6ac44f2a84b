# MindBack Documentation

Welcome to the MindBack documentation.

## Contents

- [Architecture](architecture/overview.md)
- [Development](development/workflow.md)
- [API](api/endpoints.md)
- [User Guides](user-guides/getting-started.md)
- [Prompts](prompts/overview.md)


## Development Status

## Development Status

## Recent Development Status

## Development Status

## Development Status

## Development Status

## Recent Development Status

## Development Status

## MindBack Development Overview

MindBack.ai – AI-Powered Meeting Facilitator & Brainstorming Crew

MindBack.ai is an AI-driven meeting facilitator that structures discussions, guides brainstorming sessions, and ensures balanced decision-making. Using the Six Thinking Hats framework, it enables structured, AI-assisted collaboration, helping teams explore problems, generate ideas, and make informed decisions.

🚀 Key Features

1️⃣ Structured Meeting Facilitation

MindBack.ai provides an AI moderator that ensures discussions remain productive and structured. It sets agendas, manages the thinking process, and facilitates brainstorming using proven frameworks.

2️⃣ Six Thinking Hats Framework 🎩

MindBack.ai organizes discussions using <PERSON>'s Six Thinking Hats, ensuring a balanced approach:

Blue Hat (Process & Control) – Starts the session by setting goals, defining the problem, and structuring the thinking process. The facilitator ensures everyone stays on track.

White Hat (Facts & Information) – Gathers all available data and identifies what is known and what needs to be researched further. The white hat shall have support form several ai agents 

Red Hat (Emotions & Intuition) – Allows gut reactions and emotional responses to surface without justification. This captures initial instinctive thoughts before deeper analysis.

Yellow Hat (Optimism & Benefits) – Explores positive possibilities, strengths, and advantages of potential ideas. This fosters a constructive mindset before critiquing.

Black Hat (Critical Judgment) – Identifies risks, weaknesses, and potential issues. This ensures a balanced perspective by addressing possible downsides.

Green Hat (Creativity & Alternatives) – Generates new ideas, alternatives, and creative solutions based on insights from the previous steps. This phase is about exploring possibilities beyond the obvious.

3. the UI
there is a field top aligned in slight darker color
leftaligned on the top aligned field there is text "mindback.ai
There are two llm dialogue fields:
    a. at the bottom of the UI a dialogue box and chat train window for managing the brainstorming process. This is the dialogue field with the blue hat as moderator of the process
    b. a llm dialogue field as in cursor.com, where chatting with the other agents

4.  The producture structure is displayed with a mindmap structure
- The app starts with one main node in the middel of the UI sheet (Design per below)The main node shall have a default colour light darker than the other nodes
- for each node there shall be a popup window (in size just a bit smaller than the screen) upon activating and clicking (double clicking, or pressing enter when activated) including. at the top the blanc field to enter the caption of the node, below the field two sheet tabs:
    a. Node - this governs the brainstorming content for the node:
        i. id# of the node
        ii. a description (caption) of the node

    b. Design - this governs the design of the node:
        i. shape: rectangular with sharp edges, rectangular, circle, triangle
        ii. field color: light grey
        iii. boarderline: black
        iiii. activating the node: change color to light blue
    c. shortcuts of the mindmap 
        i. tab in an activated field add a children and select the new child
        ii. tab + shift in an activated field: add a child but keep mother active
        iii. no nodes shall overlap. a new creatednode shall be placed behind and adjacent to nodes in the same child age
        iv. the description of the short cuts shall be organised in a general help button

5. Design and operation of the mindmap
    Design
        a. default is a rectangulat with smooth edges stating "Project Topic" (caption)
    Operation
    a. there shall be a separate popup field to manage the mindmap. it shall be movable. when clicking on the menue it shall open and close: 
    b. a clickable arrow which show the direction in which the mindmap is developing. the location of the arrow shall be on right side in the popup
        i. default the mindmap develop from left to the right
        ii. by clicking the arrow the arrow turn a quarter and also the mindmap is turned in the same direction
    c. there shall be a button to zoom out to display the whole mindmap
    d. there shall be zoomin/zoomout button (connected with mouse rolling wheel, by pressing shift and roll)
    e. nodes can be alone, but if they are draged and dropped over another node, the dropped node shall become a child of the node dropped upon
    f. the lines between the nodes shall be clickable. when clicked the line get activated, when clicked again (or double clicked) a popup field with design:
        i.  straight lines, curved, arrows
        ii. thickness: 1, 2, 3
        iii. color
    g. a new node is only created adjacent to the activated node
    h. a connectionand can be deleted
    i. when adding nodes so that the nodes getoutside of the screen the whole tree shall move in the oposit direction until the main node has reached the frame and then start zoomout for all nods to be visible
    j. when clicking on nodes together with shift all nodes shall be selected and there shall be an option to group the selected nodes with a curly bracket and at the center of the bracket there shall be only one node as child, if nodes are not adjacent, a. the llm shall try to organize them to be adjacent to ad the curly bracket or b. the selected nodes are get a diffrent colored outline compared to standard and lines are drawn to a single node which is located in a lower age location
    k. there is a second dialogue box the same size as the Mindmap Manger named "projects, which shall show in a second line the saved name of the project and when clicking open up to open other projects

The dialoguebox of a node


    1. Core Node Management:
Root node starts at the center of the viewport with darker color than other nodes
Nodes can be added as children to any existing node
Nodes can exist independently on the canvas without connections
Each node has text, color, shape, and border properties
2. Node Interaction:
Nodes can be dragged and moved freely
Nodes can be selected by clicking
Double-clicking opens the node dialog
Alt + Double-click adds a child node
Tab adds a child node and selects it
Tab + Shift adds a child node but keeps parent selected
Enter key opens the dialog for the currently activated node
3. Canvas Controls:
Pan the canvas using dedicated button in mindmap manager
Zoom in/out using either:
Buttons in mindmap manager
Mouse wheel
Canvas maintains position and scale state
Canvas can be reset to default view
Canvas can zoom to fit all nodes
4. Node Relationships:
Nodes can exist independently without connections
Nodes can be connected to show parent-child relationships
Lines can be selected to open a dialog box for:
Line style selection
Color customization
Line thickness adjustment
Connections are directional (from parent to child)
Connections are automatically redrawn when nodes move
5. Layout Management:
Default mindmap growth direction is left to right
Direction changes 90° clockwise when clicking direction button
Child nodes are positioned with spacing to prevent overlap
Multiple children are arranged vertically relative to their parent
6. Node Dialog and Properties:
Two-sheet dialog box:
"Node" sheet contains:
Node ID number
Node caption field
Text field for content
(Placeholder for future hat selection and LLM interaction)
"Design" sheet contains:
Shape selection (rectangle, sharp rectangle, circle, triangle)
Color settings
Border properties
7. Project Management:
Mind maps can be saved as projects
Projects can be loaded and edited
Projects have names and save timestamps
Changes can be tracked through lastSaved state
8. UI Elements:
Two-tabbed node dialog for editing properties
Project dialog for managing saves/loads
Mindmap manager with pan/zoom/direction controls
Toolbar at the top (40px height)
Background color and styling
9. Event Handling:
Mouse events for dragging and selecting
Keyboard shortcuts for common actions
Touch/gesture support (through mouse event mapping)
Proper event propagation control
10. State Management:
Centralized state through Context API
Separate states for UI, canvas, and project management
Node and connection states are synchronized
Selection state is maintained across operations

Implementing the LLMs
1. the governance llm will support in building the mindmap according to the discussion with the user
2. each node will be a development area according to the methon of the six hats. The governance llm will focus on the topic of the node in attention and delegate tasks to ai hat agents to solve tasks in their area of competence.
3. depending on the interaction the governance llm will fork the mindmap further
4. the target is to elaborately deal with the project topic and at the end generate an output document, which can come in different forms ppt, word report, json...and be versioned as it is taken that the project can iterate over time
5. 

MindMap Manager
- guids the overall mindmap
- #nods, #lines
- orientation: turn a quarter arrow, center the mm, zoomin/zoomout

special hints
a. we are using "react-dom": "17.0.2" and "react-konva": "17.0.2-6", because 18 was not working stable
b. 


Multi-Agent AI Brainstorming Crew

MindBack.ai integrates specialist AI agents, each contributing insights from different domains:

🧠 TRIZ Agent – Applies systematic inventive problem-solving.

🔄 Lateral Thinking Agent – Generates unconventional solutions.

📚 Patent Search Agent – Fetches related innovations.

🚀 Y Combinator Agent – Analyzes startup trends.

💰 Crunchbase Agent – Retrieves investment & competitor insights.

📰 TechCrunch Agent – Identifies market & industry shifts.

4️⃣ Multi-Modal Interaction 

porter five forces

blue ocean

MindBack.ai supports various input methods, ensuring flexibility and accessibility:
✔️ Multiple-choice selection – Click on AI-generated suggestions for faster ideation.
✔️ Text input – Type freely to guide AI agents in real time.
✔️ Group collaboration via SMS/WhatsApp – Anonymous feedback from multiple participants.
✔️ Voice-to-text & text-to-voice – Speak ideas or listen to AI responses for accessibility and seamless interaction.

5️⃣ Memory & Knowledge Retention 🧠

Uses RAG (Retrieval-Augmented Generation) to store & recall past discussions.

The coordinating LLM tracks project history, ensuring continuity across sessions.

6️⃣ Real-Time Visual Structuring 🌍

Mind maps & structured idea trees are dynamically generated using Mermaid.js, Miro API, or graph visualization tools.

Users see structured outputs evolving in real time.

🔥 Why MindBack.ai?

Unlike basic brainstorming tools, MindBack.ai is an AI-led meeting assistant that:✅ Guides discussions step-by-step using structured thinking models.✅ Balances emotions, facts, risks, and opportunities for better decision-making.✅ Integrates expert-level analysis from multiple domains.✅ Retains context & adapts dynamically for long-term strategy building.✅ Visualizes complex ideas into structured insights.

🛠️ Future Enhancements

🛠️ Live collaboration mode (multiple users interacting in real-time).

📊 Integration with business intelligence tools (for deeper data-driven insights).

🔗 API access for enterprise integration.

MindBack.ai – AI-driven structured discussions and brainstorming, built for real-world decision-making.

20250307 llm response to 
1. Core Structure with RAG Integration
MindMap/
├── core/
│   ├── models/
│   │   ├── Node.ts                # Enhanced with hat contribution tracking
│   │   ├── Connection.ts          # Supports tree and bird relationships
│   │   └── HatContribution.ts     # NEW: Tracks hat-specific insights
│   ├── rag/                       # NEW: RAG integration layer
│   │   ├── GraphRAG.ts            # For structural relationships
│   │   ├── MemoryRAG.ts           # For conversation history
│   │   └── HybridRAG.ts           # Future: Combined graph+vector approach
│   ├── state/                     # Now uses Zustand
│   │   └── MindMapStore.ts        # Centralized state management
│   └── operations/
│       ├── NodeOperations.ts      # Node manipulation logic
│       ├── LayoutEngine.ts        # Handles auto-arrangement
│       └── RelationshipManager.ts # NEW: Manages node connections

2. Enhanced Component Architecture

components/
├── Canvas/                       # Visual mindmap rendering
│   ├── MindMapCanvas.tsx         # Main canvas with virtual rendering
│   ├── NodeRenderer.tsx          # Shows hat contribution indicators
│   └── ConnectionRenderer.tsx    # Handles tree and bird connections
├── Controls/
│   ├── MindMapControls.tsx       # Directional controls, zoom, etc.
│   └── LLMControls.tsx           # Hat selection, agent controls
├── Dialogs/
│   ├── NodeDialog/
│   │   ├── NodeDialogContainer.tsx
│   │   ├── NodeInfoTab.tsx
│   │   ├── NodeDesignTab.tsx
│   │   └── NodeHatTab.tsx        # RENAMED: Focuses on hat interactions
│   └── ProjectDialog.tsx
└── Agents/                      # NEW: LLM agent components
    ├── GovernanceAgent.tsx      # G-LLM interface
    ├── HatAgents.tsx            # Six thinking hats interfaces
    └── SpecialistAgents.tsx     # Domain-specific agents (TRIZ, etc.)

3. LLM Service Layer
services/
├── api/
│   ├── LLMService.ts            # Base LLM communication
│   ├── GovernanceLLM.ts         # NEW: G-LLM specific operations
│   └── HatLLM.ts                # NEW: Hat-specific operations
├── prompts/                     # NEW: YAML-based prompt library
│   ├── governance/              # G-LLM prompts
│   ├── hats/                    # Six thinking hats prompts
│   └── specialists/             # Domain specialist prompts
└── storage/
    ├── LocalStorage.ts          # Session persistence
    └── ExportService.ts         # Mind map export functionality

4. Event Management System

events/                          # NEW: Event-based communication
├── EventBus.ts                  # Central event dispatcher
├── EventTypes.ts                # Defined event categories
└── handlers/                    # Event-specific handlers
    ├── NodeEventHandlers.ts     # Node-related events
    ├── LLMEventHandlers.ts      # LLM response events
    └── UIEventHandlers.ts       # UI interaction events

5. Context and Hooks Layer
contexts/
├── MindMapContext.tsx           # Now powered by Zustand
└── LLMContext.tsx               # LLM state and operations

hooks/
├── useNodeInteractions.ts       # Node management
├── useCanvas.ts                 # Canvas operations
├── useLLMIntegration.ts         # LLM communication
├── useVirtualRendering.ts       # NEW: Performance optimization
├── useHatContributions.ts       # NEW: Hat-specific contributions
└── useGovernance.ts             # NEW: G-LLM interactions



20250307 Answer to questionnaire  "LLM Integration Points & Data Flow"

### **Updated MindBack.ai Development Questionnaire (Comprehensive Review with Numbering)**

#### **1. LLM Integration Points & Data Flow**
##### **1.1 Core Integration Points**
- **Node Content Generation**  
  ✅ **Graph RAG for G-LLM** → Since the Governance LLM (G-LLM) **structures the tree and suggests nodes**, we should use **Graph RAG** to model relationships between concepts, ensuring the mindmap has an underlying structured reasoning flow.
  ✅ **G-LLM moderates node discussions** by:  
  1. Summarizing why the node is important.  
  2. Structuring topics to address.  
  3. Suggesting a method for discussion (e.g., Six Hats).  
  4. Assigning tasks to agents dynamically.  
  ✅ **User interaction is constant** → Users engage with hat LLMs inside the node, and discussions continuously update via **RAG**.

##### **1.2 Connection Logic (How relationships between nodes are determined and suggested)**  
  ✅ **Graph RAG for Relationship Management** → Since MindBack needs both hierarchical (parent-child) and lateral (bird) relationships, a **Graph RAG is optimal** for capturing and retrieving structured relationships.  
  ✅ **Hybrid Graph + Vector RAG (Later Upgrade)** → If we want dynamic lateral node suggestions, a **Hybrid RAG** would allow both structural and semantic retrieval.
  ✅ **G-LLM creates candidate relationships** dynamically.  
  ✅ **User can modify relationships manually** → Lines between nodes are clickable dialogue boxes.  
  ✅ **Relationship types:**  
  - **Tree relationships** (direct parent-child connections).  
  - **Bird relationships** (lateral links across branches).
  ✅ **Event-Triggered Updates** → Instead of requiring a full refresh, the system updates a node **when an LLM response is completed**.

##### **1.3 Hat-Based Reasoning**  
  ✅ **Memory-Augmented RAG for Hat Conversations** → Since hat-based discussions evolve, the system should remember previous discussions dynamically instead of just retrieving static documents.  
  ✅ **G-LLM initiates agent involvement** → It requests specific hat insights based on node needs.
  ✅ **Hat Contributions are Visually Tracked** → Each node has **five colored rectangles** that indicate which hat agents have contributed insights.

##### **1.4 Conversation Structure (How chat history influences the mindmap and vice versa)**  
  ✅ **Memory-Augmented RAG ensures that:**  
  - Past conversations are retained.  
  - Insights from different hats accumulate over time.  
  - Users can revisit previous discussions without losing context.

#### **2. State Management & Persistence**
##### **2.1 State Architecture**
- ✅ **Zustand for managing real-time mindmap state**.  
- ✅ **Local Storage for saving sessions between reloads**.  
- ✅ **Undo/Redo NOT needed for MVP**.  
- ✅ **Nodes and relationships are stored in a structured format for easy state recovery**.  

#### **3. UI/UX Requirements & User Flows**
##### **3.1 Node Creation and Interaction**
- ✅ **Tab-Based Node Creation System** (manual & LLM-generated).  
- ✅ **Parking lot nodes for later integration**.  
- ✅ **Hat-based color indicators in each node**.  
- ✅ **Event-based UI updates when LLM tasks complete**.  

#### **4. Performance & Scalability Concerns**
##### **4.1 Client-Side Optimization**
- ✅ **Virtual rendering for handling large mindmaps**.  
- ✅ **Lazy loading of nodes for memory efficiency**.  
- ✅ **Rate-limiting and batching for LLM requests**.  

#### **5. Component Architecture & Extensibility**
##### **5.1 Component Modularity**
- ✅ **Modularized UI components** for scalability.  
- ✅ **Support for dynamic theming (light/dark mode)**.  
- ✅ **Future extensibility for third-party plugins**.  

#### **6. Backend/API Integration**
##### **6.1 API and LLM Handling**
- ✅ **Multiple LLM selection options (GPT-4.0 vs. GPT-4.0 Mini)**.  
- ✅ **Batch requests where possible**.  
- ✅ **Dedicated function for cost and token tracking**.  
- ✅ **Strict error handling – no hallucination of responses**.  

#### **7. Miscellaneous**
##### **7.1 Additional Enhancements**
- ✅ **Governance Prompting Agent** → Dynamically generates or selects prompts based on user needs.  
- ✅ **YAML-based prompt library for structured, reusable prompts**.  
- ✅ **Prompt Hierarchy** → Some prompts must be **static** (structured outputs), while others can be **generated dynamically** by the system.

---

### **Next Steps**
📌 **This is the fully reviewed and corrected questionnaire, now with numbered sections.**
📌 **Would you like a final confirmation before proceeding with implementation?**



structure of the directory (20250305):
a. we need to monitor and manage the main memory usage in MemoryProfiling.tsx. The usage of memory while making new code needs to be controlled
b. we are targeting a highly modular structure to be able to be flexible in the development and scale the app


20250306 (Core Architecture With LLM Integration Support):
MindMap/
├── core/                    # Core domain logic
│   ├── models/              # Data structures and interfaces
│   │   ├── Node.ts          # Enhanced node model with metadata for LLM
│   │   └── Connection.ts    # Connection model with styling options
│   ├── state/               # Central state management
│   │   ├── MindMapStore.ts  # Main state container
│   │   └── CommandManager.ts # For undo/redo operations
│   └── operations/          # Business logic operations
│       ├── NodeOperations.ts # Node creation/editing
│       └── LayoutEngine.ts   # Auto-arrangement logic for LLM suggestions
├── components/              # UI components
│   ├── Canvas/              # Canvas-specific components
│   │   ├── MindMapCanvas.tsx # Main rendering component
│   │   ├── NodeRenderer.tsx  # Enhanced node rendering with LLM data
│   │   └── ConnectionRenderer.tsx # Connection rendering
│   ├── Controls/            # UI controls 
│   │   ├── MindMapControls.tsx # Main controls panel
│   │   └── LLMControls.tsx   # Controls for LLM operations
│   └── Dialogs/             # Modal dialogs with expandable architecture
│       ├── NodeDialog/       # Modular node dialog system
│       │   ├── NodeDialogContainer.tsx  # Container component
│       │   ├── NodeInfoTab.tsx         # Basic node info
│       │   ├── NodeDesignTab.tsx       # Design options
│       │   └── NodeLLMTab.tsx          # LLM interaction panel
│       └── ProjectDialog.tsx # Project management
├── hooks/                   # React hooks for shared functionality
│   ├── useNodeInteraction.ts  # Node operations
│   ├── useCanvas.ts           # Canvas management
│   └── useLLMIntegration.ts   # LLM communication
├── services/                # External service integration
│   ├── api/                 # API clients 
│   │   └── LLMService.ts    # Communication with LLM APIs
│   └── storage/             # Data persistence
└── contexts/                # React contexts
    ├── MindMapContext.tsx   # Main state context
    └── LLMContext.tsx       # LLM state and functionality

files:
1. Core Models
frontend/src/components/MindMap/core/models/Node.ts
This will contain the enhanced Node model with LLM metadata support:
Basic node properties (id, text, position, etc.)
Design properties (shape, color, border)
LLM-specific metadata (hat type, agent suggestions, etc.)
frontend/src/components/MindMap/core/models/Connection.ts
This will define the Connection model with styling options:
Source and target node IDs
Styling properties (line style, thickness, color)
Directional properties
2. State Management
frontend/src/components/MindMap/core/state/MindMapStore.ts
Central state container for the MindMap:
Node and connection state
Selection state
Canvas state (position, scale)
frontend/src/components/MindMap/core/state/CommandManager.ts
For undo/redo operations:
Command pattern implementation
History tracking
Execution methods
3. Business Logic
frontend/src/components/MindMap/core/operations/NodeOperations.ts
Node creation and editing logic:
Adding/removing nodes
Node positioning
Child node arrangement
frontend/src/components/MindMap/core/operations/LayoutEngine.ts
Auto-arrangement logic for LLM suggestions:
Automatic layout adjustments
Node spacing
Direction-based layout
4. UI Components
frontend/src/components/MindMap/components/Canvas/MindMapCanvas.tsx
Main rendering component:
Canvas setup
Event handling
Node and connection rendering
frontend/src/components/MindMap/components/Canvas/NodeRenderer.tsx
Node rendering with LLM data:
Node visualization
LLM metadata display
Interaction handling
frontend/src/components/MindMap/components/Canvas/ConnectionRenderer.tsx
Connection rendering:
Line drawing
Styling application
Selection handling
frontend/src/components/MindMap/components/Controls/MindMapControls.tsx
Main controls panel:
Zoom/pan controls
Direction controls
Layout controls
frontend/src/components/MindMap/components/Controls/LLMControls.tsx
Controls for LLM operations:
Hat selection
Agent interaction
Suggestion display
frontend/src/components/MindMap/components/Dialogs/NodeDialog/NodeDialogContainer.tsx
Container component for node dialog:
Dialog framework
Tab navigation
State management
frontend/src/components/MindMap/components/Dialogs/NodeDialog/NodeInfoTab.tsx
Basic node info:
Node caption
Content editing
ID display
frontend/src/components/MindMap/components/Dialogs/NodeDialog/NodeDesignTab.tsx
Design options:
Shape selection
Color picker
Border styling
frontend/src/components/MindMap/components/Dialogs/NodeDialog/NodeLLMTab.tsx
LLM interaction panel:
Hat selection
Agent interaction
Suggestion implementation
frontend/src/components/MindMap/components/Dialogs/ProjectDialog.tsx
Project management:
Save/load functionality
Project naming
Timestamp display
5. Hooks
frontend/src/components/MindMap/hooks/useNodeInteraction.ts
Node operations:
Selection handling
Drag and drop
Keyboard shortcuts
frontend/src/components/MindMap/hooks/useCanvas.ts
Canvas management:
Pan/zoom functionality
View state management
Fit to view
frontend/src/components/MindMap/hooks/useLLMIntegration.ts
LLM communication:
Agent interaction
Suggestion handling
Six Thinking Hats implementation
6. Services
frontend/src/components/MindMap/services/api/LLMService.ts
Communication with LLM APIs:
Agent requests
Suggestion fetching
Hat-specific operations
frontend/src/components/MindMap/services/storage/ProjectStorage.ts
Data persistence:
Save/load functionality
Local storage
Export options
7. Contexts
frontend/src/components/MindMap/contexts/MindMapContext.tsx
Main state context:
Node and connection state
Canvas state
Selection state
frontend/src/components/MindMap/contexts/LLMContext.tsx
LLM state and functionality:
Agent state
Suggestion state
Hat selection state
Would you like me to guide you through implementing these files one by one? Or would you prefer to focus on a specific part of the implementation first?
ok please start create the files or summarize the files in one powershell command for me to execute




20250305 (outdated):
MindBack/
├── backend/
│   ├── main.py               # FastAPI entry point
│   ├── api/                  # API endpoints
│   │   ├── chat.py           # WebSocket handler for AI chat
│   │   ├── mindmap.py        # REST API for mind map save/load
│   │   ├── rag.py            # Endpoint for RAG retrieval/testing
│   │
│   ├── services/             # Core logic
│   │   ├── llm_handler.py    # Handles OpenAI API calls
│   │   ├── rag_manager.py    # Retrieves relevant RAG data before LLM call
│   │
│   ├── database/             # Vector storage & retrieval
│   │   ├── conversation_rag.py  # Stores user-AI chat history
│   │   ├── mindmap_rag.py       # Stores the mind map structure
│   │   ├── quantitative_rag.py  # Stores business/factual data
│   │   ├── strategy_rag.py      # Stores strategic frameworks
│   │   ├── migrations/          # (NEW) Handles schema updates
│   │   ├── backups/             # (NEW) Stores automatic backups
│   │   ├── cache_manager.py     # (NEW) Implements caching
│   │
│   ├── monitoring/              # (NEW) Logging & Observability
│   │   ├── logs/                # Logs for RAG/API interactions
│   │   ├── metrics.py           # Tracks performance stats
│   │   ├── error_tracking.py    # Logs API errors
│   │
│   ├── middleware/              # (NEW) Security Features
│   │   ├── auth.py              # Handles authentication
│   │   ├── rate_limiting.py     # Prevents API overuse
│   │   ├── input_validation.py  # Sanitizes input
│   │
│   ├── config/                  # Config files
│   │   ├── settings.py          # API keys, DB settings
│   │
│   ├── requirements.txt         # Python dependencies
│
├── frontend/
│   ├── src/
│   │   ├── components/          # UI Components
│   │   │   ├── ChatPanel.tsx    # Chat interface
│   │   │   ├── MindMapCanvas.tsx # Mind map visualization
│   │   │   ├── SuggestedNodes.tsx # Displays AI-suggested nodes
│   │   │
│   │   ├── api/                 # Handles API requests
│   │   │   ├── chatApi.ts       # Handles chat WebSocket API calls
│   │   │   ├── mindMapApi.ts    # Fetches mind map data from backend
│   │
│   ├── package.json             # Frontend dependencies
│   ├── vite.config.js           # Vite configuration
│
├── docs/                        # (NEW) Documentation
│   ├── api_reference.md         # API documentation
│   ├── rag_tuning.md            # Guide on RAG tuning
│   ├── system_architecture.md   # Diagrams and design principles
│   ├── usage_guide.md           # User setup and workflow guide
│
├── run_setup.ps1                # Setup automation script
├── setup_structure.py           # Python setup script
└── restructure.ps1              # Restructuring script