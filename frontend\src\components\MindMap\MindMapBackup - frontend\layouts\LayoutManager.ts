import { Connection } from '../core/models/Connection';
import { Node } from '../core/models/Node';
import { BottomUpLayout } from './strategies/BottomUpLayout';
import { CompactLeftToRightLayout } from './strategies/CompactLeftToRightLayout';
import { LeftToRightLayout } from './strategies/LeftToRightLayout';
import { RadialLayout } from './strategies/RadialLayout';
import { TopDownLayout } from './strategies/TopDownLayout';
import { DEFAULT_LAYOUT_CONFIG, LayoutConfig, LayoutManager as ILayoutManager, LayoutStrategy, LayoutStrategyType } from './types';

/**
 * Manages layout strategies and applies them to nodes
 */
export class LayoutManager implements ILayoutManager {
  private strategies: Map<LayoutStrategyType, LayoutStrategy> = new Map();

  constructor() {
    // Register default strategies
    this.registerStrategy(new LeftToRightLayout());
    this.registerStrategy(new TopDownLayout());
    this.registerStrategy(new RadialLayout());
    this.registerStrategy(new BottomUpLayout());
    // Register compact layout strategy
    this.registerStrategy(new CompactLeftToRightLayout());
  }

  /**
   * Apply a layout strategy to nodes
   */
  applyLayout(
    nodes: Record<string, Node>,
    connections: Connection[],
    rootId: string,
    strategyType: LayoutStrategyType = 'leftToRight',
    config: LayoutConfig = DEFAULT_LAYOUT_CONFIG
  ): Record<string, Node> {
    const strategy = this.strategies.get(strategyType);
    
    if (!strategy) {
      console.error(`Layout strategy '${strategyType}' not found`);
      return nodes;
    }
    
    console.log(`Applying layout strategy: ${strategyType}`);
    return strategy.calculateLayout(nodes, connections, rootId, config);
  }

  /**
   * Register a new layout strategy
   */
  registerStrategy(strategy: LayoutStrategy): void {
    this.strategies.set(strategy.name, strategy);
  }

  /**
   * Get available strategy types
   */
  getAvailableStrategies(): LayoutStrategyType[] {
    return Array.from(this.strategies.keys());
  }
} 