/**
 * MindBookService.ts
 * 
 * Service layer for MindBook operations.
 * This service provides methods for interacting with the MindBookStore
 * and handles MindBook-level operations.
 * 
 * The service layer pattern helps break circular dependencies by providing
 * a single point of access for store operations without direct imports.
 */

import { v4 as uuidv4 } from 'uuid';
import { useMindBookStore } from '../state/MindBookStore';
import { MindSheetContentType, SheetData } from '../state/StoreTypes';
import RegistrationManager, { EventType } from './RegistrationManager';
import { applicationService } from './ApplicationService';

/**
 * MindBookService class
 * 
 * Singleton service for MindBook operations.
 */
class MindBookService {
  private static instance: MindBookService;

  private constructor() {
    // Private constructor to prevent direct instantiation
    console.log('MindBookService: Initialized');
  }

  /**
   * Get the singleton instance of the MindBookService
   */
  public static getInstance(): MindBookService {
    if (!MindBookService.instance) {
      MindBookService.instance = new MindBookService();
    }
    return MindBookService.instance;
  }

  /**
   * Get all sheets in the MindBook
   */
  public getSheets(): SheetData[] {
    return useMindBookStore.getState().sheets;
  }

  /**
   * Get the active sheet ID
   */
  public getActiveSheetId(): string | null {
    return useMindBookStore.getState().activeSheetId;
  }

  /**
   * Get a sheet by ID
   */
  public getSheetById(sheetId: string): SheetData | undefined {
    return this.getSheets().find(sheet => sheet.id === sheetId);
  }

  /**
   * Get the active sheet
   */
  public getActiveSheet(): SheetData | undefined {
    const activeSheetId = this.getActiveSheetId();
    if (!activeSheetId) return undefined;
    return this.getSheetById(activeSheetId);
  }

  /**
   * Set the active sheet
   */
  public setActiveSheet(sheetId: string): void {
    const store = useMindBookStore.getState();
    
    // Get the current active sheet ID
    const currentActiveSheetId = store.activeSheetId;
    
    // If the active sheet is changing, log the deactivation of the previous sheet
    if (currentActiveSheetId && currentActiveSheetId !== sheetId) {
      const currentSheet = this.getSheetById(currentActiveSheetId);
      if (currentSheet) {
        RegistrationManager.registerEvent(EventType.SHEET_DEACTIVATED, {
          id: currentActiveSheetId,
          type: currentSheet.contentType
        });
      }
    }
    
    // Set the new active sheet
    store.setActiveSheet(sheetId);
    
    // Log the activation of the new sheet
    const newSheet = this.getSheetById(sheetId);
    if (newSheet) {
      RegistrationManager.registerEvent(EventType.SHEET_ACTIVATED, {
        id: sheetId,
        type: newSheet.contentType
      });
    }
    
    console.log('MindBookService: Active sheet set to', sheetId);
  }

  /**
   * Create a new sheet
   */
  public createSheet(title: string, contentType: MindSheetContentType, content: any): string {
    const store = useMindBookStore.getState();
    
    // Generate a unique ID
    const id = uuidv4();
    
    // Create the sheet
    const newSheet: SheetData = {
      id,
      title,
      contentType,
      content
    };
    
    // Add the sheet to the store
    store.setSheets([...store.sheets, newSheet]);
    
    // Set the new sheet as active
    store.setActiveSheet(id);
    
    // Log the sheet creation
    RegistrationManager.registerEvent(EventType.MINDSHEET_CREATED, {
      id,
      title,
      type: contentType
    });
    
    console.log('MindBookService: Sheet created', id, title, contentType);
    
    return id;
  }

  /**
   * Create a new MindMap sheet
   */
  public createMindMapSheet(title: string, mbcpData: any): string {
    // Show loading indicator
    applicationService.setLoading(true, `Creating MindMap: ${title}`);
    
    try {
      const sheetId = this.createSheet(title, MindSheetContentType.MINDMAP, mbcpData);
      
      // Log the mindmap creation
      RegistrationManager.registerEvent(EventType.MINDMAP_SELECTED, {
        id: sheetId,
        title
      });
      
      return sheetId;
    } finally {
      // Hide loading indicator
      applicationService.setLoading(false);
    }
  }

  /**
   * Create a new ChatFork sheet
   */
  public createChatForkSheet(title: string, content: any): string {
    // Show loading indicator
    applicationService.setLoading(true, `Creating ChatFork: ${title}`);
    
    try {
      const sheetId = this.createSheet(title, MindSheetContentType.CHATFORK, content);
      
      // Log the chatfork creation
      RegistrationManager.registerEvent(EventType.CHATFORK_SELECTED, {
        id: sheetId,
        title
      });
      
      return sheetId;
    } finally {
      // Hide loading indicator
      applicationService.setLoading(false);
    }
  }

  /**
   * Remove a sheet
   */
  public removeSheet(sheetId: string): void {
    const store = useMindBookStore.getState();
    
    // Get the sheet before removing it
    const sheet = this.getSheetById(sheetId);
    
    // Remove the sheet
    store.removeSheet(sheetId);
    
    // Log the sheet removal
    if (sheet) {
      RegistrationManager.registerEvent(EventType.SHEET_DEACTIVATED, {
        id: sheetId,
        type: sheet.contentType
      });
    }
    
    console.log('MindBookService: Sheet removed', sheetId);
  }

  /**
   * Update sheet content
   */
  public updateSheetContent(sheetId: string, content: any): void {
    const store = useMindBookStore.getState();
    store.updateSheetContent(sheetId, content);
    
    console.log('MindBookService: Sheet content updated', sheetId);
  }

  /**
   * Update sheet title
   */
  public updateSheetTitle(sheetId: string, title: string): void {
    const store = useMindBookStore.getState();
    store.updateSheetTitle(sheetId, title);
    
    console.log('MindBookService: Sheet title updated', sheetId, title);
  }

  /**
   * Save sheet state
   */
  public saveSheetState(sheetId: string, state: any): void {
    const store = useMindBookStore.getState();
    
    // Skip saving if state is empty or invalid
    if (!state || !Object.keys(state).length) {
      console.log('MindBookService: Skipping save for empty state, sheet:', sheetId);
      return;
    }
    
    // Check if the sheet exists
    const sheet = this.getSheetById(sheetId);
    if (!sheet) {
      console.warn('MindBookService: Cannot save state for non-existent sheet:', sheetId);
      return;
    }
    
    // Check if the state has actually changed
    const currentState = store.getSheetState(sheetId);
    if (currentState && JSON.stringify(currentState) === JSON.stringify(state)) {
      console.log('MindBookService: State unchanged, skipping save for sheet:', sheetId);
      return;
    }
    
    // Save the state
    store.saveSheetState(sheetId, state);
    
    console.log('MindBookService: Sheet state saved', sheetId);
  }

  /**
   * Get sheet state
   */
  public getSheetState(sheetId: string): any {
    const store = useMindBookStore.getState();
    return store.getSheetState(sheetId);
  }
}

// Export the singleton instance
export const mindBookService = MindBookService.getInstance();

// Export convenience functions
export const getSheets = () => mindBookService.getSheets();
export const getActiveSheetId = () => mindBookService.getActiveSheetId();
export const getSheetById = (sheetId: string) => mindBookService.getSheetById(sheetId);
export const getActiveSheet = () => mindBookService.getActiveSheet();
export const setActiveSheet = (sheetId: string) => mindBookService.setActiveSheet(sheetId);
export const createSheet = (title: string, contentType: MindSheetContentType, content: any) => 
  mindBookService.createSheet(title, contentType, content);
export const createMindMapSheet = (title: string, mbcpData: any) => 
  mindBookService.createMindMapSheet(title, mbcpData);
export const createChatForkSheet = (title: string, content: any) => 
  mindBookService.createChatForkSheet(title, content);
export const removeSheet = (sheetId: string) => mindBookService.removeSheet(sheetId);
export const updateSheetContent = (sheetId: string, content: any) => 
  mindBookService.updateSheetContent(sheetId, content);
export const updateSheetTitle = (sheetId: string, title: string) => 
  mindBookService.updateSheetTitle(sheetId, title);
export const saveSheetState = (sheetId: string, state: any) => 
  mindBookService.saveSheetState(sheetId, state);
export const getSheetState = (sheetId: string) => mindBookService.getSheetState(sheetId);
