// Direct fix for the events module error
(function() {
  // Create a more complete EventEmitter implementation
  class EventEmitter {
    constructor() {
      this._events = Object.create(null);
      this._eventsCount = 0;
      this._maxListeners = undefined;
    }

    setMaxListeners(n) {
      if (typeof n !== 'number' || n < 0 || Number.isNaN(n)) {
        throw new TypeError('n must be a non-negative number');
      }
      this._maxListeners = n;
      return this;
    }

    getMaxListeners() {
      return this._maxListeners === undefined ? 10 : this._maxListeners;
    }

    emit(type, ...args) {
      if (!this._events[type]) return false;
      
      const handler = this._events[type];
      if (typeof handler === 'function') {
        handler.apply(this, args);
      } else if (Array.isArray(handler)) {
        const listeners = handler.slice();
        for (let i = 0; i < listeners.length; i++) {
          listeners[i].apply(this, args);
        }
      }
      
      return true;
    }

    addListener(type, listener) {
      return this.on(type, listener);
    }

    on(type, listener) {
      if (typeof listener !== 'function') {
        throw new TypeError('listener must be a function');
      }
      
      if (!this._events[type]) {
        this._events[type] = listener;
        this._eventsCount++;
      } else if (Array.isArray(this._events[type])) {
        this._events[type].push(listener);
      } else {
        this._events[type] = [this._events[type], listener];
      }
      
      return this;
    }

    once(type, listener) {
      if (typeof listener !== 'function') {
        throw new TypeError('listener must be a function');
      }
      
      const onceWrapper = (...args) => {
        this.removeListener(type, onceWrapper);
        listener.apply(this, args);
      };
      
      onceWrapper.listener = listener;
      this.on(type, onceWrapper);
      
      return this;
    }

    removeListener(type, listener) {
      if (typeof listener !== 'function') {
        throw new TypeError('listener must be a function');
      }
      
      if (!this._events[type]) return this;
      
      const list = this._events[type];
      
      if (list === listener || (list.listener && list.listener === listener)) {
        if (--this._eventsCount === 0) {
          this._events = Object.create(null);
        } else {
          delete this._events[type];
        }
      } else if (Array.isArray(list)) {
        let position = -1;
        
        for (let i = list.length - 1; i >= 0; i--) {
          if (list[i] === listener || (list[i].listener && list[i].listener === listener)) {
            position = i;
            break;
          }
        }
        
        if (position < 0) return this;
        
        if (position === 0) {
          list.shift();
        } else {
          list.splice(position, 1);
        }
        
        if (list.length === 1) {
          this._events[type] = list[0];
        }
      }
      
      return this;
    }

    off(type, listener) {
      return this.removeListener(type, listener);
    }

    removeAllListeners(type) {
      if (this._events === undefined) return this;
      
      if (type) {
        if (this._events[type]) {
          if (--this._eventsCount === 0) {
            this._events = Object.create(null);
          } else {
            delete this._events[type];
          }
        }
      } else {
        this._events = Object.create(null);
        this._eventsCount = 0;
      }
      
      return this;
    }

    listeners(type) {
      if (!this._events[type]) return [];
      
      if (typeof this._events[type] === 'function') {
        return [this._events[type]];
      }
      
      return this._events[type].slice();
    }

    rawListeners(type) {
      if (!this._events[type]) return [];
      
      if (typeof this._events[type] === 'function') {
        return [this._events[type]];
      }
      
      return this._events[type].slice();
    }

    listenerCount(type) {
      if (!this._events[type]) return 0;
      
      if (typeof this._events[type] === 'function') {
        return 1;
      }
      
      return this._events[type].length;
    }

    eventNames() {
      return Object.keys(this._events);
    }
  }

  // Create a complete events module
  const eventsModule = {
    EventEmitter,
    once: function(emitter, name) {
      return new Promise((resolve, reject) => {
        function eventListener(...args) {
          if (errorListener !== undefined) {
            emitter.removeListener('error', errorListener);
          }
          resolve(args);
        }
        let errorListener;
        
        if (name !== 'error') {
          errorListener = (err) => {
            emitter.removeListener(name, eventListener);
            reject(err);
          };
          emitter.once('error', errorListener);
        }
        
        emitter.once(name, eventListener);
      });
    },
    on: function(emitter, name) {
      const out = [];
      out.next = () => {
        return new Promise((resolve, reject) => {
          function eventListener(...args) {
            if (errorListener !== undefined) {
              emitter.removeListener('error', errorListener);
            }
            resolve({ value: args, done: false });
          }
          
          let errorListener;
          
          if (name !== 'error') {
            errorListener = (err) => {
              emitter.removeListener(name, eventListener);
              reject(err);
            };
            emitter.once('error', errorListener);
          }
          
          emitter.once(name, eventListener);
        });
      };
      return out;
    }
  };

  // Make it globally available
  window.EventEmitter = EventEmitter;
  window.events = eventsModule;
  
  // Override require for events module
  const originalRequire = window.require;
  window.require = function(moduleName) {
    if (moduleName === 'events') {
      return eventsModule;
    }
    return originalRequire ? originalRequire(moduleName) : undefined;
  };
  
  // Directly patch the error
  try {
    // This is a direct fix for the specific error in the console
    if (window.__vite_browser_external_events_EventEmitter === undefined) {
      window.__vite_browser_external_events_EventEmitter = EventEmitter;
    }
  } catch (e) {
    console.log('Could not set __vite_browser_external_events_EventEmitter:', e);
  }
  
  console.log('Direct fix for events module applied');
})();
