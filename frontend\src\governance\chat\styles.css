/* Import theme variables */
@import '../../styles/theme.css';

/* Fix the z-index issues with a proper backdrop */
.governance-dialog-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
  z-index: 1000;
  pointer-events: none; /* Make the backdrop not intercept clicks */
}

/* Main container for the dialog - FIXED Z-INDEX */
.governance-chat-dialog-container {
  z-index: 3000 !important; /* Higher than MindSheet (1500) and NodeBox (2100) */
  transition: all 0.3s ease-in-out;
  will-change: transform;
}

/* Removed automatic positioning when context panel is open */

/* Collapsed state styling */
.governance-chat-dialog-container.collapsed {
  height: 40px !important;
  min-height: 40px !important;
  overflow: hidden;
  opacity: 0.85;
  transition: height 0.3s ease-in-out, opacity 0.3s ease;
}

.governance-chat-dialog-container.collapsed:hover {
  opacity: 1;
}

/* Core styles for the governance dialog */
.governance-dialog {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden !important;
  display: flex;
  flex-direction: column;
  background-color: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Dialog header changes for collapsed state */
.governance-chat-dialog-container.collapsed .dialog-header {
  border-radius: 8px;
}

/* Remove old Dialog-specific styles */
/* COMMENTED OUT - These were hiding the governance box */
/*
.governance-dialog-paper {
  display: none;
}

.MuiPaper-root.governance-dialog-paper {
  display: none;
}

.governance-dialog-root {
  display: none;
}
*/

/* React-Rnd resize handles - enhance visibility */
.rnd-resize-handle {
  position: absolute;
  background-color: transparent;
  z-index: 1150 !important;
}

.rnd-resize-handle.bottom {
  bottom: 0;
  left: 15px;
  right: 15px;
  height: 6px;
  cursor: ns-resize;
}

.rnd-resize-handle.right {
  top: 15px;
  right: 0;
  bottom: 15px;
  width: 6px;
  cursor: ew-resize;
}

.rnd-resize-handle.bottom-right {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 15px;
  height: 15px;
  cursor: nwse-resize;
  z-index: 1150 !important;
}

/* Remove old custom resize handle styles */
.custom-resize-handle {
  display: none !important;
}

/* Dialog header */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #000;
  color: white;
  cursor: move;
  user-select: none;
  height: 40px;
  border-radius: 8px 8px 0 0;
  position: relative;
  z-index: 2100; /* Higher z-index to ensure it's above other content */
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 100%;
}

/* Add a more visible indicator that the header is draggable */
.dialog-header::before {
  content: "⋮⋮";
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 2px;
  font-size: 12px;
  opacity: 0.5;
  pointer-events: none;
}

.dialog-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  font-weight: 500;
  color: white;
  user-select: none;
}

.dialog-header-logo {
  height: 24px;
  width: 24px;
  object-fit: contain;
  border-radius: 4px;
}

.dialog-header-text {
  font-weight: 500;
  font-size: 16px;
  font-family: Arial, sans-serif;
}

.dialog-header-buttons {
  display: flex;
  gap: 4px;
}

/* Dialog controls */
.dialog-controls {
  display: flex;
  gap: 4px;
}

.dialog-control-button {
  background: none;
  border: none;
  color: white;
  font-size: 14px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  padding: 0;
  transition: background-color 0.2s;
}

.dialog-control-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Add these important styles for the header buttons */
.dialog-header-button {
  color: white !important;
  padding: 4px !important;
}

.dialog-header-button:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Special styling for minimize button */
.minimize-button {
  color: rgba(255, 255, 255, 0.9) !important;
}

.minimize-button:hover {
  color: white !important;
  background-color: rgba(255, 255, 255, 0.2) !important;
}

.dialog-header-text {
  font-weight: 500;
  font-size: 16px;
  font-family: Arial, sans-serif;
}

/* Fix the header title logo styling */
.dialog-header-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dialog-header-logo {
  height: 24px;
  width: 24px;
  object-fit: contain;
  border-radius: 4px;
}

/* Message list styles */
.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  background-color: #fcfcfc;
}

/* Model selector row between message list and input */
.model-selector-row {
  padding: 12px 16px;
  background-color: #f5f5f5;
  border-top: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.model-selector-row select {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
  cursor: pointer;
}

.model-selector-row select:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 1px rgba(33, 150, 243, 0.2);
}

/* Ensure the model selector takes up appropriate space */
.model-selector-row .model-selector {
  width: 100%;
  justify-content: flex-start;
}

/* Message input styles */
.message-input-container {
  padding: 12px 16px;
  border-top: 1px solid #e0e0e0;
  background-color: white;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1200;
}

.message-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  font-family: inherit;
  resize: none;
  min-height: 40px;
  max-height: 150px;
  outline: none;
  transition: border-color 0.2s;
}

.message-input:focus {
  border-color: #2196f3;
  box-shadow: 0 0 0 1px rgba(33, 150, 243, 0.2);
}

.send-button {
  margin-top: 8px;
  align-self: flex-end;
  padding: 8px 16px;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.send-button:hover {
  background-color: #1976d2;
}

.send-button:disabled {
  background-color: #bdbdbd;
  cursor: not-allowed;
}

/* Dialog content layout */
.dialog-content {
  display: flex;
  flex-direction: column;
  height: calc(100% - 40px); /* Subtract header height */
  overflow: hidden;
}

/* Governance chat content */
.governance-chat-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* Message container */
.governance-chat-content .message-container {
  flex: 1;
  overflow-y: auto;
  background-color: #fcfcfc;
}

/* Model selector container */
.governance-chat-content .model-selector-container {
  padding: 12px 16px;
  background-color: #f5f5f5;
  border-top: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
}

/* Input container */
.governance-chat-content .input-container {
  padding: 12px 16px;
  background-color: white;
  border-top: 1px solid #e0e0e0;
}

/* Message styles */
.message {
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 12px;
  max-width: 85%;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  line-height: 1.4;
}

.message.user {
  align-self: flex-end;
  background-color: #e3f2fd;
  color: #000;
}

.message.assistant {
  align-self: flex-start;
  background-color: #f5f5f5;
  color: #000;
}

.message.system {
  align-self: center;
  background-color: transparent;
  box-shadow: none;
  color: #757575;
  font-style: italic;
  padding: 4px 8px;
  margin: 4px 0;
  max-width: 100%;
}

.system-message {
  color: #757575;
  font-style: italic;
  font-size: 0.85rem;
}

/* Remove legacy resize handle styles */
body.governance-dialog-resizing {
  user-select: none !important;
}

/* Build mindmap button */
.build-mindmap-button {
  display: none; /* Hide by default unless specifically enabled */
  margin-top: 16px;
  align-self: center;
  padding: 8px 16px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.15s;
}

/* New Project Button */
.new-project-button {
  background-color: #2e7d32 !important;
  color: white !important;
  margin-right: 8px !important;
  border-radius: 4px !important;
  padding: 4px 8px !important;
}

.new-project-button:hover {
  background-color: #388e3c !important;
}

/* Webkit scrollbar styling */
.message-list::-webkit-scrollbar {
  width: 8px;
}

.message-list::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 4px;
}

.message-list::-webkit-scrollbar-thumb {
  background-color: #2196F3;
  border-radius: 4px;
  border: 2px solid #f5f5f5;
}

.message-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 10px;
  color: #666;
}

.message-timestamp {
  font-family: Arial, sans-serif;
  font-size: 10px;
  opacity: 0.7;
  align-self: flex-end;
}

.message-status {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Status indicators */
.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  position: relative;
  transition: all 0.3s ease;
}

.status-indicator.success {
  background-color: #4CAF50;
}

.status-indicator.error {
  background-color: #f44336;
}

.status-indicator.pending {
  background-color: #FFC107; /* Amber color for pending */
  animation: pulse-pending 1s infinite;
}

@keyframes pulse-pending {
  0% {
    opacity: 0.6;
    transform: scale(0.95);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.6;
    transform: scale(0.95);
  }
}

/* Loading indicator */
.loading-indicator {
  display: flex;
  justify-content: center;
  padding: 20px;
  align-self: flex-start;
}

.loading-dot {
  width: 8px;
  height: 8px;
  background-color: #95a5a6;
  border-radius: 50%;
  margin: 0 4px;
  animation: pulse 1.5s infinite ease-in-out;
}

.loading-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0.4s;
}

.spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #2196f3;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Message actions */
.message-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.action-button {
  background-color: #e0e0e0;
  border: none;
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 11px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-family: Arial, sans-serif;
}

.action-button:hover {
  background-color: #d0d0d0;
}

/* Model selector */
.model-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* Add styles for node section */
.node-section {
  display: flex;
  align-items: center;
  margin-top: 6px;
  margin-bottom: 6px;
  padding-left: 8px;
  border-left: 2px solid #2196F3;
}

.node-section .MuiInputLabel-root {
  color: #1976D2;
  font-weight: 500;
}

.node-section .MuiOutlinedInput-root {
  background-color: #fff;
}

.node-section .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #2196F3;
  border-width: 2px;
}

.node-section .MuiMenuItem-root {
  font-size: 13px;
  padding: 6px 16px;
}

.toggle-container {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 10px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.05);
  font-size: 13px;
  color: #666;
}

/* CrewAI toggle styling */
.toggle-container .crew-active {
  color: #9c27b0; /* Purple for active */
}

.toggle-container .crew-inactive {
  color: #666; /* Gray for inactive */
}

.settings-button {
  color: #666;
  transition: color 0.2s;
}

.settings-button:hover {
  color: #9c27b0;
}

/* CrewAI settings panel styling */
.crew-settings-panel {
  padding: 16px;
  min-width: 250px;
  max-width: 350px;
}

.crew-settings-panel h6 {
  margin-bottom: 16px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.setting-item {
  margin-bottom: 16px;
}

.setting-item:last-child {
  margin-bottom: 0;
}

/* Make the model selector container responsive */
.model-selector-container {
  display: flex;
  align-items: center;
}

@media (max-width: 600px) {
  .model-selector {
    flex-direction: column;
    align-items: flex-start;
  }

  .toggle-container {
    margin-left: 0;
    margin-top: 10px;
    flex-direction: column;
    align-items: flex-start;
  }
}

/* LLM toggle */
.live-llm-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: rgba(46, 175, 80, 0.15);
  padding: 4px 10px;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
}

.live-llm-toggle .live {
  color: #2eaf50;
  font-weight: 500;
  font-size: 13px;
}

.mindmap-mode {
  position: fixed;
  bottom: 0;
  height: 300px !important;
  transition: all 0.3s ease-in-out;
}

.mindmap-mode .message-list {
  height: calc(100% - 48px);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* React Draggable Transparent Selection */
.react-draggable-transparent-selection,
.governance-dialog-dragging {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  cursor: move !important;
}

.react-draggable-transparent-selection *,
.governance-dialog-dragging * {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

.react-draggable-transparent-selection .dialog-header-buttons,
.governance-dialog-dragging .dialog-header-buttons {
  cursor: default !important;
}

.react-draggable-transparent-selection .dialog-header-button,
.governance-dialog-dragging .dialog-header-button {
  cursor: pointer !important;
}

/* Ensure the dialog is draggable */
.governance-chat-dialog-container {
  cursor: default;
}

/* Make the dialog header more obviously draggable */
.dialog-header {
  cursor: move !important;
  user-select: none !important;
  -webkit-user-select: none !important;
}

/* Popover styling */
.MuiPopover-root {
  z-index: var(--z-index-popovers) !important;
}

/* Ensure the dialog is draggable */
.react-draggable {
  position: absolute !important;
  z-index: 1000 !important; /* Fixed z-index to ensure it doesn't cover mindmap */
  will-change: transform !important;
  transition: none !important;
  pointer-events: auto !important;
}

/* Override any transform that might be applied by Material-UI to the Paper component */
.MuiPaper-root.governance-dialog-paper {
  position: absolute !important;
  pointer-events: auto !important;
  margin: 0 !important;
  max-width: none !important;
  max-height: none !important;
  z-index: var(--z-index-governance-box) !important;
}

/* Custom dialog class */
.custom-dialog {
  pointer-events: none !important;
}

.custom-dialog .MuiBackdrop-root {
  background-color: transparent !important;
}

/* Special styling for the Build Mindmap button */
.build-mindmap-button {
  background-color: #3b82f6 !important;
  color: white !important;
  font-weight: bold;
}

.build-mindmap-button:hover {
  background-color: #2563eb !important;
}

/* Mindmap mode */
.mindmap-mode .governance-chat-dialog {
  background-color: rgba(255, 255, 255, 0.95);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .governance-dialog-paper {
    width: 90vw !important;
    height: 80vh !important;
  }

  .message {
    max-width: 90%;
  }
}

/* Method choice styles */
.action-button-container {
  position: relative;
  display: inline-block;
}

.method-choice-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: var(--z-index-popovers); /* Using CSS variable for z-index */
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  margin-top: 4px;
  min-width: 180px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.method-choice-option {
  background: none;
  border: none;
  padding: 10px 12px;
  text-align: left;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.method-choice-option:hover {
  background-color: #f5f5f5;
}

.method-choice-option:first-child {
  border-bottom: 1px solid #eee;
}

.method-choice-close {
  background-color: #f5f5f5;
  border: none;
  border-top: 1px solid #eee;
  padding: 8px 12px;
  text-align: center;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  transition: background-color 0.2s;
}

.method-choice-close:hover {
  background-color: #e5e5e5;
}

/* Add indicator dot for Live LLM */
.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #2eaf50;
  margin-right: 4px;
  display: inline-block;
}

.model-selector-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.model-selector {
  display: flex;
  align-items: center;
}

.model-selector .MuiFormControl-root {
  min-width: 150px;
}

.model-selector .toggle-container {
  display: flex;
  align-items: center;
  margin-left: 12px;
}

.model-selector .MuiFormControlLabel-root.live {
  color: #1976d2;
}

.model-selector .MuiFormControlLabel-root.mock {
  color: #757575;
}

/* Intention section styling */
.context-section {
  display: flex;
  align-items: center;
  margin-right: 12px;
}

.context-section .MuiFormControl-root {
  min-width: 150px;
}

/* Template select field styling */
.template-select-field {
  min-width: 200px;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
  cursor: pointer;
  animation: fadeIn 0.3s ease-in-out;
}

.template-select-field:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 1px rgba(33, 150, 243, 0.2);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateX(-10px); }
  to { opacity: 1; transform: translateX(0); }
}

/* Style for intention dropdown */
#intention-select-menu, .MuiMenu-root {
  z-index: var(--z-index-popovers) !important; /* Using CSS variable for z-index */
  max-height: 400px !important;
}

#intention-select-menu .MuiMenuItem-root,
.MuiMenu-root .MuiMenuItem-root {
  font-size: 14px;
  min-height: 36px;
}

/* Style for the arrow icon in the Instantanious option */
#intention-select-menu .MuiMenuItem-root svg {
  opacity: 0.7;
  transition: transform 0.2s;
}

#intention-select-menu .MuiMenuItem-root:hover svg {
  opacity: 1;
  transform: translateX(2px);
}

/* Ensure nested menu appears on top */
.MuiPopover-root {
  z-index: var(--z-index-popovers) !important; /* Using CSS variable for z-index */
}

/* Style for the nested menu */
.template-select-field {
  min-width: 200px;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
  cursor: pointer;
}

/* Ensure the dropdown appears above other elements */
.MuiPopover-root.MuiModal-root {
  z-index: 9999 !important;
}

/* Custom styling for the close button */
.close-button {
  color: #757575;
}

.close-button:hover {
  color: #d32f2f;
  background-color: rgba(211, 47, 47, 0.04);
}