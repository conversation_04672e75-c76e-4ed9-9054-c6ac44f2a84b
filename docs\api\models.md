# Data Models

## MBCP Format

```json
{
  "nodes": [...],
  "connections": [...]
}
```


## Migrated Content

# MindBack Content Protocol (MBCP) Specification

## Overview
The **MindBack Content Protocol (MBCP)** defines the standard data structure for nodes within the MindBack system. It ensures consistency across LLM outputs, agent workflows, RAG memory, and integration with external systems like HubSpot or Notion.

MBCP is designed to be:
- **Structured** for memory and action extraction
- **Semantic** to capture user intention and agent contribution
- **Composable** for rendering as mindmaps, chatforks, or tables
- **Translatable** to other protocols like MCP (Modular Content Protocol)

---

## Node Schema
Each node in MBCP is a JSON object that may contain required and optional fields.

### Required Fields
| Field         | Type   | Description                              |
|---------------|--------|------------------------------------------|
| `id`          | string | Unique identifier for the node           |
| `text`        | string | Headline or label (≤50 characters)       |
| `description` | string | Extended explanation or reasoning        |

### Optional Fields
| Field         | Type      | Description                                                        |
|---------------|-----------|--------------------------------------------------------------------|
| `intent`      | enum      | One of: `factual`, `teleological`, `exploratory`                  |
| `agent`       | enum      | One of: `blue_hat`, `white_hat`, `red_hat`, `black_hat`, `yellow_hat`, `green_hat` |
| `tags`        | array     | List of strings (for topic indexing and filtering)                |
| `parent_id`   | string    | ID of the parent node                                              |
| `created_by`  | enum      | One of: `user`, `llm`, `agent`                                     |
| `timestamp`   | ISO string| Node creation timestamp                                            |
| `embedding_ref` | string  | Reference to stored vector embedding                               |
| `children`    | array     | List of child nodes (recursive MBCP nodes)                        |

### Action Sub-object (Optional)
| Field       | Type   | Description                                     |
|-------------|--------|-------------------------------------------------|
| `title`     | string | Name of the action                             |
| `owner`     | string | Responsible party                              |
| `due_date`  | date   | Deadline in YYYY-MM-DD                         |
| `system`    | string | Target system (e.g., `hubspot`, `notion`)      |
| `status`    | enum   | One of: `pending`, `in_progress`, `done`       |

---

## Example MBCP Node
```json
{
  "id": "node_1_1",
  "text": "Evaluate market fit",
  "description": "Assess how well the product matches the Swedish market.",
  "intent": "teleological",
  "agent": "yellow_hat",
  "tags": ["market", "strategy", "expansion"],
  "parent_id": "node_1",
  "created_by": "llm",
  "timestamp": "2025-03-25T12:30:00Z",
  "action": {
    "title": "Interview local clients",
    "owner": "Sven",
    "due_date": "2025-04-01",
    "system": "hubspot",
    "status": "pending"
  },
  "children": []
}
```

---

## MBCP to MCP Translation Map
For interoperability, MBCP can be translated into a simplified Modular Content Protocol (MCP) format.

| MBCP Field     | MCP Field          |
|----------------|--------------------|
| `id`           | `id`               |
| `text`         | `title` or `text`  |
| `description`  | `content`          |
| `intent`       | `type` or `tags`   |
| `agent`        | `agent` or `source`|
| `tags`         | `tags`             |
| `action`       | `action` (custom)  |
| `children`     | `children`         |

If MCP compliance is required, you can flatten or omit MBCP-specific fields while preserving semantics via tags or metadata.

---

## Usage
- **LLMs** should be instructed to output nodes in MBCP format.
- **Frontends** should render based on `type`, `intent`, and `agent`.
- **Memory layers (RAG)** can embed and index nodes using `tags`, `intent`, `text`, and `embedding_ref`.
- **Export pipelines** can extract `action` sub-objects and sync them to external systems.

---

## Versioning
This is **MBCP v1.0**, designed for MindBack’s structured interaction workflows. Future versions may include:
- Support for link-type edges (non-hierarchical relationships)
- Traceability fields (e.g., `justified_by`, `contradicted_by`)
- Localization (e.g., `language`, `translation_of`)

---

For questions, updates, or proposing changes to the MBCP spec, contact the system architecture team.



## MBCP Node Schema

## Node Schema
Each node in MBCP is a JSON object that may contain required and optional fields.

## MBCP Node Schema

## Node Schema
Each node in MBCP is a JSON object that may contain required and optional fields.