import React, { useEffect, useState } from 'react';
import { useChatForkStore } from './ChatForkStore';
import ChatForkCanvas from './ChatForkCanvas';
import { useMindBookStore } from '../../core/state/MindBookStore';
import { MindSheetContentType } from '../../core/state/StoreTypes';
import RegistrationManager, { EventType } from '../../core/services/RegistrationManager';
import './ChatFork.css';

/**
 * ChatForkCanvasContainer - Container component for the canvas-based ChatFork
 * This component connects to the ChatForkStore and renders the ChatForkCanvas
 * It also integrates with the MindBookStore to work with mindsheets
 */
const ChatForkCanvasContainer: React.FC = () => {
  const { content, isVisible, hideChatFork, activeSheetId } = useChatForkStore();

  // Effect to handle ChatFork visibility and active sheet
  useEffect(() => {
    if (isVisible && content) {
      // FIXED: Get store references outside of setTimeout to avoid hook violations
      const mindBookStore = useMindBookStore.getState();
      const chatForkStore = useChatForkStore.getState();

      // Defer the state update to avoid React warnings about updating during render
      setTimeout(() => {
        // If we have an active sheet ID, make sure it's the active sheet
        if (activeSheetId) {
          console.log('ChatForkCanvasContainer: Setting active sheet:', activeSheetId);
          mindBookStore.setActiveSheet(activeSheetId);

          // Check if the sheet exists
          const sheetExists = mindBookStore.sheets.some(sheet => sheet.id === activeSheetId);
          if (!sheetExists) {
            console.warn('ChatForkCanvasContainer: Sheet does not exist:', activeSheetId);
            // Clear the active sheet ID
            chatForkStore.setActiveSheetId(null);
          }
        }
      }, 0); // Defer to next tick
    }
  }, [isVisible, content, activeSheetId]);

  // Handle closing the ChatFork
  const handleClose = () => {
    console.log('ChatForkCanvasContainer: Closing ChatFork');
    hideChatFork();

    // FIXED: Get the latest state outside of event handler to avoid hook violations
    const mindBookStore = useMindBookStore.getState();
    const chatForkStore = useChatForkStore.getState();

    // If there are other sheets, switch to the first one that's not this ChatFork
    if (mindBookStore.sheets.length > 1 && activeSheetId) {
      const otherSheet = mindBookStore.sheets.find(sheet =>
        sheet.id !== activeSheetId
      );
      if (otherSheet) {
        console.log('ChatForkCanvasContainer: Switching to other sheet:', otherSheet.id);
        mindBookStore.setActiveSheet(otherSheet.id);
      }
    }

    // Clear the active sheet ID in the ChatForkStore
    chatForkStore.setActiveSheetId(null);
  };

  // Log state for debugging
  useEffect(() => {
    console.log('ChatForkCanvasContainer: State update', {
      isVisible,
      hasContent: !!content,
      activeSheetId,
      contentSample: content ? content.text : null
    });
  }, [isVisible, content, activeSheetId]);

  // If not visible or no content, don't render anything
  if (!isVisible || !content) {
    console.log('ChatForkCanvasContainer: Not rendering - isVisible:', isVisible, 'hasContent:', !!content);
    return null;
  }

  // Log that we're about to render the ChatFork
  console.log('ChatForkCanvasContainer: Rendering ChatFork with content:', {
    title: content.text,
    contentLength: content.full_text?.length || 0,
    activeSheetId
  });

  // Check if we're in a MindSheet context
  // If we have an activeSheetId, the ChatFork should be rendered by the MindSheet component
  // But we'll render it here as well to ensure it's visible
  const inMindSheetContext = activeSheetId ? true : false;

  if (inMindSheetContext) {
    console.log('ChatForkCanvasContainer: Rendering in MindSheet context with sheetId:', activeSheetId);
  }

  // Render as a standalone component (fallback for backward compatibility)
  return (
    <div className="chatfork-standalone-container">
      <ChatForkCanvas
        content={content}
        isVisible={isVisible}
        onClose={handleClose}
        sheetId={activeSheetId}
      />
    </div>
  );
};

export default ChatForkCanvasContainer;
