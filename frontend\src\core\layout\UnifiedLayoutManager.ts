/**
 * UnifiedLayoutManager.ts
 * 
 * SINGLE SOURCE OF TRUTH for all layout operations
 * Enforces governance rules and prevents layout chaos
 */

import { LayoutStrategyType, LayoutRequest, LayoutResponse, DEFAULT_LAYOUT_CONFIG, LayoutConfig } from '../types/LayoutTypes';
import { LayoutGovernanceService } from '../services/LayoutGovernanceService';
import { Node, Connection } from '../state/MindMapStore';

// Layout strategy implementations
interface LayoutStrategy {
  readonly name: LayoutStrategyType;
  calculateLayout(
    nodes: Record<string, Node>,
    connections: Connection[],
    rootId: string,
    config?: LayoutConfig
  ): Record<string, Node>;
}

// Left-to-right layout implementation
class LeftToRightLayoutStrategy implements LayoutStrategy {
  readonly name: LayoutStrategyType = 'leftToRight';

  calculateLayout(
    nodes: Record<string, Node>,
    connections: Connection[],
    rootId: string,
    config: LayoutConfig = DEFAULT_LAYOUT_CONFIG
  ): Record<string, Node> {
    if (!nodes[rootId]) {
      console.warn('Root node not found:', rootId);
      return nodes;
    }

    const updatedNodes = { ...nodes };
    const levels = this.buildNodeLevels(nodes, connections, rootId);
    
    levels.forEach((level, levelIndex) => {
      const levelNodes = level;
      const totalHeight = levelNodes.length * (config.nodeHeight + config.verticalSpacing);
      const startY = -totalHeight / 2;
      
      levelNodes.forEach((nodeId, nodeIndex) => {
        const node = updatedNodes[nodeId];
        if (!node) return;
        
        const x = levelIndex * config.levelSpacing;
        const y = startY + nodeIndex * (config.nodeHeight + config.verticalSpacing);
        
        updatedNodes[nodeId] = { ...node, x, y };
      });
    });

    return updatedNodes;
  }

  private buildNodeLevels(nodes: Record<string, Node>, connections: Connection[], rootId: string): string[][] {
    const levels: string[][] = [];
    const visited = new Set<string>();
    const queue: Array<{ nodeId: string; level: number }> = [{ nodeId: rootId, level: 0 }];

    while (queue.length > 0) {
      const { nodeId, level } = queue.shift()!;
      
      if (visited.has(nodeId)) continue;
      visited.add(nodeId);

      if (!levels[level]) levels[level] = [];
      levels[level].push(nodeId);

      // Find children
      const children = connections
        .filter(conn => conn.from === nodeId)
        .map(conn => conn.to)
        .filter(childId => !visited.has(childId));

      children.forEach(childId => {
        queue.push({ nodeId: childId, level: level + 1 });
      });
    }

    return levels;
  }
}

// Top-down layout implementation
class TopDownLayoutStrategy implements LayoutStrategy {
  readonly name: LayoutStrategyType = 'topDown';

  calculateLayout(
    nodes: Record<string, Node>,
    connections: Connection[],
    rootId: string,
    config: LayoutConfig = DEFAULT_LAYOUT_CONFIG
  ): Record<string, Node> {
    if (!nodes[rootId]) {
      console.warn('Root node not found:', rootId);
      return nodes;
    }

    const updatedNodes = { ...nodes };
    const levels = this.buildNodeLevels(nodes, connections, rootId);
    
    levels.forEach((level, levelIndex) => {
      const levelNodes = level;
      const totalWidth = levelNodes.length * (config.nodeWidth + config.horizontalSpacing);
      const startX = -totalWidth / 2;
      
      levelNodes.forEach((nodeId, nodeIndex) => {
        const node = updatedNodes[nodeId];
        if (!node) return;
        
        const x = startX + nodeIndex * (config.nodeWidth + config.horizontalSpacing);
        const y = levelIndex * config.levelSpacing;
        
        updatedNodes[nodeId] = { ...node, x, y };
      });
    });

    return updatedNodes;
  }

  private buildNodeLevels(nodes: Record<string, Node>, connections: Connection[], rootId: string): string[][] {
    const levels: string[][] = [];
    const visited = new Set<string>();
    const queue: Array<{ nodeId: string; level: number }> = [{ nodeId: rootId, level: 0 }];

    while (queue.length > 0) {
      const { nodeId, level } = queue.shift()!;
      
      if (visited.has(nodeId)) continue;
      visited.add(nodeId);

      if (!levels[level]) levels[level] = [];
      levels[level].push(nodeId);

      const children = connections
        .filter(conn => conn.from === nodeId)
        .map(conn => conn.to)
        .filter(childId => !visited.has(childId));

      children.forEach(childId => {
        queue.push({ nodeId: childId, level: level + 1 });
      });
    }

    return levels;
  }
}

// Radial layout implementation
class RadialLayoutStrategy implements LayoutStrategy {
  readonly name: LayoutStrategyType = 'radial';

  calculateLayout(
    nodes: Record<string, Node>,
    connections: Connection[],
    rootId: string,
    config: LayoutConfig = DEFAULT_LAYOUT_CONFIG
  ): Record<string, Node> {
    if (!nodes[rootId]) {
      console.warn('Root node not found:', rootId);
      return nodes;
    }

    const updatedNodes = { ...nodes };
    const levels = this.buildNodeLevels(nodes, connections, rootId);
    
    // Position root at center
    updatedNodes[rootId] = { ...updatedNodes[rootId], x: 0, y: 0 };
    
    levels.forEach((level, levelIndex) => {
      if (levelIndex === 0) return; // Skip root level
      
      const levelNodes = level;
      const radius = levelIndex * 150; // Increasing radius per level
      const angleStep = (2 * Math.PI) / levelNodes.length;
      
      levelNodes.forEach((nodeId, nodeIndex) => {
        const node = updatedNodes[nodeId];
        if (!node) return;
        
        const angle = nodeIndex * angleStep;
        const x = radius * Math.cos(angle);
        const y = radius * Math.sin(angle);
        
        updatedNodes[nodeId] = { ...node, x, y };
      });
    });

    return updatedNodes;
  }

  private buildNodeLevels(nodes: Record<string, Node>, connections: Connection[], rootId: string): string[][] {
    const levels: string[][] = [];
    const visited = new Set<string>();
    const queue: Array<{ nodeId: string; level: number }> = [{ nodeId: rootId, level: 0 }];

    while (queue.length > 0) {
      const { nodeId, level } = queue.shift()!;
      
      if (visited.has(nodeId)) continue;
      visited.add(nodeId);

      if (!levels[level]) levels[level] = [];
      levels[level].push(nodeId);

      const children = connections
        .filter(conn => conn.from === nodeId)
        .map(conn => conn.to)
        .filter(childId => !visited.has(childId));

      children.forEach(childId => {
        queue.push({ nodeId: childId, level: level + 1 });
      });
    }

    return levels;
  }
}

/**
 * Unified Layout Manager - Single point of control for all layout operations
 */
export class UnifiedLayoutManager {
  private static instance: UnifiedLayoutManager;
  private governance: LayoutGovernanceService;
  private strategies: Map<LayoutStrategyType, LayoutStrategy> = new Map();
  private lastLayoutTime: Map<string, number> = new Map();

  static getInstance(): UnifiedLayoutManager {
    if (!this.instance) {
      this.instance = new UnifiedLayoutManager();
    }
    return this.instance;
  }

  constructor() {
    this.governance = LayoutGovernanceService.getInstance();
    this.initializeStrategies();
  }

  private initializeStrategies(): void {
    this.strategies.set('leftToRight', new LeftToRightLayoutStrategy());
    this.strategies.set('topDown', new TopDownLayoutStrategy());
    this.strategies.set('radial', new RadialLayoutStrategy());
    // Add other strategies as needed
  }

  /**
   * SINGLE ENTRY POINT for all layout changes
   * Enforces governance rules and prevents chaos
   */
  async requestLayoutChange(request: LayoutRequest): Promise<LayoutResponse> {
    const timestamp = Date.now();
    
    console.log(`[UnifiedLayoutManager] Layout change requested:`, {
      strategy: request.strategy,
      sheetId: request.sheetId,
      origin: request.requestOrigin,
      reason: request.reason
    });

    // GOVERNANCE VALIDATION - CRITICAL
    const validation = this.governance.validateLayoutRequest(request);
    if (!validation.success) {
      console.warn(`[UnifiedLayoutManager] Layout request REJECTED by governance:`, validation.reason);
      return {
        ...validation,
        timestamp
      };
    }

    // Rate limiting to prevent excessive changes
    const lastTime = this.lastLayoutTime.get(request.sheetId) || 0;
    const timeSinceLastLayout = timestamp - lastTime;
    if (timeSinceLastLayout < 500 && request.requestOrigin === 'auto') {
      console.warn(`[UnifiedLayoutManager] Layout request RATE LIMITED for sheet ${request.sheetId}`);
      return {
        success: false,
        strategy: request.strategy,
        reason: 'Rate limited - too frequent layout changes',
        timestamp
      };
    }

    try {
      // Apply the layout strategy
      const success = await this.applyLayoutStrategy(request);
      
      if (success) {
        this.lastLayoutTime.set(request.sheetId, timestamp);
        
        // Store user preference if user-initiated
        if (request.requestOrigin === 'user') {
          this.governance.setUserPreference(request.sheetId, request.strategy);
        }
        
        console.log(`[UnifiedLayoutManager] Layout successfully applied: ${request.strategy} for sheet ${request.sheetId}`);
      }

      return {
        success,
        strategy: request.strategy,
        reason: success ? 'Layout applied successfully' : 'Layout application failed',
        timestamp
      };
    } catch (error) {
      console.error(`[UnifiedLayoutManager] Error applying layout:`, error);
      return {
        success: false,
        strategy: request.strategy,
        reason: `Layout error: ${error.message}`,
        timestamp
      };
    }
  }

  /**
   * Check if layout change is allowed without executing it
   */
  canChangeLayout(request: Partial<LayoutRequest>): boolean {
    if (!request.strategy || !request.sheetId) return false;
    
    const fullRequest: LayoutRequest = {
      strategy: request.strategy,
      sheetId: request.sheetId,
      requestOrigin: request.requestOrigin || 'user'
    };
    
    const validation = this.governance.validateLayoutRequest(fullRequest);
    return validation.success;
  }

  /**
   * Get the preferred layout strategy for a sheet
   */
  getPreferredStrategy(sheetId: string): LayoutStrategyType {
    return this.governance.getPreferredStrategy(sheetId);
  }

  private async applyLayoutStrategy(request: LayoutRequest): Promise<boolean> {
    const strategy = this.strategies.get(request.strategy);
    if (!strategy) {
      throw new Error(`Layout strategy '${request.strategy}' not found`);
    }

    // Get the store for this sheet
    const store = this.getSheetStore(request.sheetId);
    if (!store) {
      throw new Error(`Store not found for sheet: ${request.sheetId}`);
    }

    const state = store.getState();
    const { nodes, connections, rootNodeId } = state;

    if (!rootNodeId || !nodes[rootNodeId]) {
      console.warn(`[UnifiedLayoutManager] No root node found for sheet ${request.sheetId}`);
      return false;
    }

    // Calculate new layout
    const updatedNodes = strategy.calculateLayout(nodes, connections, rootNodeId);

    // Update the store with new positions
    Object.entries(updatedNodes).forEach(([nodeId, node]) => {
      if (nodes[nodeId] && (nodes[nodeId].x !== node.x || nodes[nodeId].y !== node.y)) {
        store.getState().updateNode(nodeId, { x: node.x, y: node.y });
      }
    });

    // Update current layout strategy in store
    store.setState({ currentLayoutStrategy: request.strategy });

    return true;
  }

  private getSheetStore(sheetId: string): any {
    // This will be connected to the store factory
    // For now, we'll use a global method that should be implemented
    if (typeof window !== 'undefined' && (window as any).getSheetMindMapStore) {
      const store = (window as any).getSheetMindMapStore(sheetId);
      if (!store) {
        console.error(`[UnifiedLayoutManager] Store factory returned null for sheet: ${sheetId}`);
        console.log(`[UnifiedLayoutManager] Available stores:`, Object.keys((window as any).useMindMapStoreRegistry?.getState?.().stores || {}));
      }
      return store;
    }
    
    console.error(`[UnifiedLayoutManager] Store factory not available for sheet: ${sheetId}`);
    console.log(`[UnifiedLayoutManager] window.getSheetMindMapStore:`, typeof (window as any).getSheetMindMapStore);
    return null;
  }
}

// Export singleton instance
export const unifiedLayoutManager = UnifiedLayoutManager.getInstance(); 