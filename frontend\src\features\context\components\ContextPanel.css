/* ContextPanel.css */

.context-panel {
  position: fixed;
  left: -400px;
  top: 40px;
  width: 400px;
  height: calc(100vh - 110px);
  background-color: white;
  border-right: 1px solid #e0e0e0;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  transition: left 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.context-panel.open {
  left: 0;
}

.context-panel-header {
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.context-panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.context-name {
  font-size: 14px;
  font-weight: normal;
  color: #666;
}

.context-panel-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.context-panel-close {
  background: none;
  border: none;
  color: #666;
  font-size: 18px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.context-panel-close:hover {
  background-color: #e0e0e0;
  color: #333;
}

.context-panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

/* Level Selection */
.context-levels {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.context-level-button {
  flex: 1;
  background: none;
  border: none;
  padding: 12px 8px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
  font-size: 14px;
  color: #666;
}

.context-level-button:hover {
  background-color: #f5f5f5;
  color: #333;
}

.context-level-button.active {
  color: #007bff;
  border-bottom-color: #007bff;
  background-color: #f8f9fa;
}

/* Context Level Section */
.context-level-section {
  margin-bottom: 20px;
}

.context-level-title {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.context-input-tabs {
  display: flex;
  margin-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
}

.context-input-tab {
  flex: 1;
  background: none;
  border: none;
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
  font-size: 13px;
  color: #666;
}

.context-input-tab:hover {
  background-color: #f5f5f5;
  color: #333;
}

.context-input-tab.active {
  color: #007bff;
  border-bottom-color: #007bff;
  background-color: #f8f9fa;
}

.context-input-content {
  padding-top: 12px;
}

.context-text-input {
  width: 100%;
  min-height: 120px;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.4;
  box-sizing: border-box;
}

.context-text-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.context-input-actions {
  margin-top: 8px;
  display: flex;
  justify-content: flex-end;
}

.context-action-button {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.context-action-button:hover {
  background-color: #0056b3;
}

.context-action-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.context-action-button.small {
  padding: 6px 12px;
  font-size: 12px;
}

.context-action-button.secondary {
  background-color: #6c757d;
}

.context-action-button.secondary:hover {
  background-color: #545b62;
}

.context-file-upload-button {
  display: inline-block;
  background-color: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 8px;
  color: #666;
}

.context-file-upload-button:hover {
  border-color: #007bff;
  background-color: #f0f7ff;
  color: #007bff;
}

.context-file-upload-info {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.context-reference-selector {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
  box-sizing: border-box;
}

.context-reference-selector:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* No Settings State */
.context-no-settings {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.context-no-settings p {
  margin: 0 0 16px 0;
  font-style: italic;
}

/* New Context Dialog */
.context-new-dialog {
  position: absolute;
  top: 60px;
  left: 16px;
  right: 16px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.context-new-dialog-content {
  padding: 16px;
}

.context-new-dialog h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.context-new-input,
.context-new-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 8px;
  box-sizing: border-box;
}

.context-new-input:focus,
.context-new-textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.context-new-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 12px;
}

.context-panel-footer {
  background-color: #f5f5f5;
  border-top: 1px solid #e0e0e0;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.context-info {
  font-size: 11px;
  color: #666;
}

/* Responsive */
@media (max-width: 768px) {
  .context-panel {
    width: 100%;
    left: -100%;
  }
  
  .context-panel.open {
    left: 0;
  }
  
  .context-levels {
    flex-direction: column;
  }
  
  .context-input-tabs {
    flex-direction: column;
  }
  
  .context-input-tab {
    border-bottom: none;
    border-left: 2px solid transparent;
  }
  
  .context-input-tab.active {
    border-bottom: none;
    border-left-color: #007bff;
  }
} 