
# MindBack Development Plan

## Overview
This development plan implements the **Parallel Three-Stage Prompt Architecture** as outlined in `20250611_Unified_Prompt_Strategy.md`.

### **Key Strategy Updates:**
- **Parallel Development**: New three-stage pipeline developed alongside existing system
- **No Backend Modifications**: Existing `llm.py` and current prompting remain untouched
- **MBCP-Native Memory**: All memory uses MBCP-compatible formats, no Letta integration
- **Base64 for Large Data**: `[ZIPPED]::` blocks for structured documents, readable blocks for semantic context
- **Memory-First Implementation**: Build memory foundation before three-stage pipeline

### **Architecture Components:**
1. **Routing Stage**: Decision-making and intent classification (`routing_prompt.yaml`)
2. **Memory Stage**: MBCP-compatible context retrieval and backstory enrichment
3. **Execution Stage**: Unified response generation with full context (`unified_Prompt.yaml`)

---

## 🧠 Phase 1: MBCP-Native Memory Foundation (Priority)

### **1.1 Memory System Implementation**
**Objective**: Build MBCP-compatible memory system without touching existing backend

#### **New Memory Services (No Existing Code Changes)**
- [x] **1.1.1** Create `MBCPMemoryService` (new file: `backend/api/services/mbcp_memory_service.py`)
  - [x] Implement conversation threading for ChatFork lineage
  - [x] Add parent context retrieval for forked chats
  - [x] Create MBCP-compatible memory storage format

- [ ] **1.1.2** Create `ContextBlockGenerator` (new file: `backend/api/services/context_block_generator.py`)
  - [ ] Generate `[CTX]::` compressed contextual metadata
  - [ ] Generate `[MEM]::` structured conversation history
  - [ ] Generate `[ZIPPED]::` Base64 encoded large data blocks
  - [ ] Create context compression utilities

- [x] **1.1.3** Enhance `MemorySnapshotService` (extend existing)
  - [x] Add MBCP-compatible snapshot format
  - [x] Implement snapshot-based memory retrieval
  - [x] Create memory relevance scoring for context blocks

#### **Frontend Memory Integration**
- [x] **1.1.4** Extend `ChatMemoryService` (no backend changes)
  - [x] Add conversation threading capabilities
  - [x] Implement parent context capture for forks
  - [x] Create memory block preparation for API calls

- [x] **1.1.5** Create memory testing interface
  - [x] Test memory retrieval with existing ChatFork workflow
  - [x] Validate MBCP format consistency
  - [x] Test Base64 encoding/decoding for large contexts

## 🚀 Phase 2: Parallel Three-Stage Pipeline (New Development)

### **2.1 New Pipeline Infrastructure**
**Objective**: Create completely separate three-stage pipeline alongside existing system

#### **New Backend Endpoints (No Existing Code Changes)**
- [ ] **2.1.1** Create new API routes (new file: `backend/api/routes/three_stage_llm.py`)
  - [ ] `/api/llm/three-stage/route` - Stage 1: Routing
  - [ ] `/api/llm/three-stage/memory` - Stage 2: Memory retrieval
  - [ ] `/api/llm/three-stage/execute` - Stage 3: Unified execution
  - [ ] `/api/llm/three-stage/pipeline` - Full pipeline endpoint

- [ ] **2.1.2** Create `ThreeStageOrchestrator` (new file: `backend/api/services/three_stage_orchestrator.py`)
  - [ ] Implement stage progression logic
  - [ ] Add conditional memory retrieval
  - [ ] Create pipeline state management
  - [ ] Add comprehensive logging and error handling

- [ ] **2.1.3** Create new prompt service (new file: `backend/api/services/three_stage_prompt_service.py`)
  - [ ] Load three-stage specific prompts
  - [ ] Handle context block injection
  - [ ] Manage Base64 encoding for large data

#### **Three-Stage Prompt Templates**
- [ ] **2.1.4** Finalize routing prompt (`routing_prompt.yaml`)
  - [ ] Test routing decision accuracy across intent types
  - [ ] Optimize for token efficiency with gpt-3.5-turbo
  - [ ] Add comprehensive routing examples and edge cases

- [ ] **2.1.5** Complete unified execution prompt (`unified_Prompt.yaml`)
  - [ ] Validate MBCP format consistency across all sheet types
  - [ ] Test context block handling (`[CTX]::`, `[MEM]::`, `[ZIPPED]::`)
  - [ ] Optimize for different response types (mindmap, chatfork, agent tasks)

### **2.2 Frontend Pipeline Integration**
**Objective**: Add three-stage pipeline option to frontend without breaking existing functionality

#### **Pipeline Selection Interface**
- [ ] **2.2.1** Create pipeline selection mechanism
  - [ ] Add toggle between legacy and three-stage pipeline
  - [ ] Implement feature flag for gradual rollout
  - [ ] Create A/B testing framework

- [ ] **2.2.2** Update governance box for three-stage support
  - [ ] Add new API endpoints to LLM service calls
  - [ ] Implement pipeline stage indicators in UI
  - [ ] Add three-stage specific error handling

#### **Memory-Enhanced ChatFork**
- [ ] **2.2.3** Enhance ChatFork creation with memory context
  - [ ] Implement parent context capture using new memory services
  - [ ] Add backstory context to three-stage fork requests
  - [ ] Update fork UI to show context lineage and memory blocks

- [ ] **2.2.4** Create memory visualization components
  - [ ] Add conversation thread display for debugging
  - [ ] Implement context block viewer (`[CTX]::`, `[MEM]::`, `[ZIPPED]::`)
  - [ ] Create memory injection indicators in UI

## 🔧 Phase 3: Advanced Features and Optimization

### **3.1 Base64 Large Data Handling**
**Objective**: Implement efficient handling of large structured data

#### **ZIPPED Block Implementation**
- [ ] **3.1.1** Create Base64 encoding service
  - [ ] Implement compression for large mindbook states
  - [ ] Add encoding for financial statements and documents
  - [ ] Create agent log compression and encoding

- [ ] **3.1.2** Optimize Base64 strategy
  - [ ] Test token efficiency vs. readable format
  - [ ] Implement smart compression thresholds
  - [ ] Add decode instruction optimization for LLMs

### **3.2 Performance and Migration**
**Objective**: Optimize performance and plan migration strategy

#### **Performance Optimization**
- [ ] **3.2.1** Implement pipeline performance monitoring
  - [ ] Add stage-specific latency tracking
  - [ ] Monitor token usage across stages
  - [ ] Create performance comparison with legacy system

- [ ] **3.2.2** Add caching and optimization
  - [ ] Cache context blocks for repeated queries
  - [ ] Implement memory block caching
  - [ ] Optimize Base64 encoding/decoding

#### **Migration Strategy**
- [ ] **3.2.3** Plan gradual migration
  - [ ] Create feature-by-feature migration plan
  - [ ] Implement rollback mechanisms
  - [ ] Plan legacy system deprecation timeline

---

## � Phase 4: Advanced Memory and Event Sourcing

### **4.1 Event Sourcing Implementation**
**Objective**: Solve double memory issue with event-based approach

#### **Event-Based Memory System**
- [ ] **4.1.1** Create `EventSourcingService` (new file)
  - [ ] Implement lightweight event logging in MBCP format
  - [ ] Add event-based state reconstruction
  - [ ] Create event stream management for memory queries

- [ ] **4.1.2** Enhance snapshot system for continuous memory
  - [ ] Implement event-driven snapshot triggers
  - [ ] Add incremental snapshot updates based on events
  - [ ] Create snapshot-based memory queries with event gaps

#### **Smart Memory Triggers**
- [ ] **4.1.3** Implement intelligent snapshot triggers
  - [ ] Add content-based snapshot triggers (LLM responses, sheet creation)
  - [ ] Implement debouncing for excessive snapshots
  - [ ] Create memory relevance scoring and retention policies

### **4.2 Tool Integration**
**Objective**: Integrate external tools through three-stage pipeline

#### **Firecrawl Integration**
- [ ] **4.2.1** Implement Firecrawl routing in Stage 1
  - [ ] Add web search detection in routing prompt
  - [ ] Create Firecrawl API integration service
  - [ ] Implement tool result formatting for MBCP

- [ ] **4.2.2** Create tool delegation framework
  - [ ] Add generic tool routing mechanism
  - [ ] Implement tool result caching
  - [ ] Create tool-specific context block generation

---

## 📋 Legacy Items (Preserved from Original Plan)

### **Legacy Phase 1: Minimal Change - Copy Existing Pattern**
- [x] **1.1** Find the existing teleological block in `backend/api/routes/llm.py` (around line 128)
- [x] **1.2** Copy the exact same logic and add it as an `elif` block for exploratory
- [x] **1.3** Change only these 2 things:
  - [x] `'teleological'` → `'exploratory'`
  - [x] `'initiate_mindmap2'` → `'initiate_chatfork2'`
- [ ] **1.4** Test that teleological still works (regression test)
- [ ] **1.5** Test that exploratory now works

### **UI Cleanup Tasks**
- [ ] Remove ChatFork header with non-functioning buttons
- [ ] Fix Windows taskbar implementation for start/stop app

### **Settings Enhancement (2025.06.08)**
- [ ] Expand context settings with:
  - [ ] Tech stack configuration (LLM APIs, Firecrawl, etc.)
  - [ ] Knowledge source integration (EU, Destatis, Polymarket)
  - [ ] Data source hooks for changing data

---

## 🔧 Technical Implementation Details

### **Three-Stage Pipeline Architecture**

#### **Stage 1: Routing (`routing_prompt.yaml`)**
```
Input: [CTX]:: + [USER]::
Output: { route, requires_memory, tool }
Models: gpt-3.5-turbo (fast, cheap)
```

**Routing Decisions:**
- `factual_response`: Direct answer, no further processing
- `embedding_and_continue`: Route to Stage 3 with memory
- `firecrawl`: External tool delegation

#### **Stage 2: Memory Retrieval (Conditional)**
```
Triggers: requires_memory = true
Sources: ChatMemoryService, Snapshots, ContextStore
Output: [MEM]:: block for Stage 3
```

**Memory Strategies:**
- **Conversation Threading**: Parent context for forks
- **Snapshot Chaining**: Historical state evolution
- **Context Injection**: Foundational/Strategic/Operational settings
- **Event Sourcing**: Lightweight action logging

#### **Stage 3: Unified Execution (`unified_Prompt.yaml`)**
```
Input: [CTX]:: + [MEM]:: + [USER]::
Output: MBCP-formatted response
Models: gpt-4o (complex reasoning)
```

**Response Types:**
- **MindMap Nodes**: Structured node creation/expansion
- **ChatFork Threads**: Conversation continuation with backstory
- **Agent Tasks**: Delegation to specialized agents

### **Context Block Specifications**

#### **[CTX]:: Block Format**
```
mm/teleo/topic=Buy&Build/context=Segmentation/sheet=mindmap_001/node=active_node_id
```

**Components:**
- `mm/teleo`: Sheet type and intent
- `topic`: Current discussion topic
- `context`: Active context level
- `sheet`: Current sheet identifier
- `node`: Active node reference

#### **[MEM]:: Block Format**
```
THREAD: parent_conversation_summary
CONTEXT: foundational_context_summary
EVENTS: recent_significant_actions
NODES: related_mindmap_nodes
```

**Memory Sources:**
- **ChatMemoryService**: Recent structured messages
- **ContextStore**: Active context settings
- **RegistrationManager**: Significant user events
- **SnapshotService**: Historical state snapshots

#### **[USER]:: Block Format**
```
Direct user input without modification
```

### **Implementation Priority Matrix**

#### **High Priority (Phase 1)**
1. **Three-Stage Pipeline Core** - Essential for architecture
2. **Memory Integration** - Solves backstory problem
3. **ChatFork Context** - Primary use case
4. **MBCP Consistency** - Maintains format standards

#### **Medium Priority (Phase 2)**
1. **Event Sourcing** - Solves double memory issue
2. **Advanced Memory Features** - Performance optimization
3. **Tool Integration** - Extends capabilities
4. **UI Enhancements** - User experience

#### **Low Priority (Phase 3)**
1. **Performance Optimization** - Polish and scale
2. **Advanced Debugging** - Developer experience
3. **Monitoring Systems** - Production readiness

### **Success Metrics**

#### **Functional Success**
- [ ] ChatFork creation includes parent conversation context
- [ ] Memory retrieval works across sheet switches
- [ ] Three-stage pipeline processes all intent types
- [ ] MBCP format consistency maintained

#### **Performance Success**
- [ ] Simple queries bypass expensive stages (< 2s response)
- [ ] Complex queries complete within acceptable time (< 10s)
- [ ] Memory retrieval adds < 1s overhead
- [ ] Token usage optimized vs. monolithic approach

#### **User Experience Success**
- [ ] Forked chats maintain conversation continuity
- [ ] Context switches preserve working memory
- [ ] No regression in existing functionality
- [ ] Clear feedback on processing stages

### **Risk Mitigation**

#### **Technical Risks**
- **Pipeline Complexity**: Implement comprehensive logging and debugging
- **Memory Overhead**: Use compression and relevance filtering
- **Latency Issues**: Implement caching and parallel processing
- **Context Loss**: Add validation and recovery mechanisms

#### **Integration Risks**
- **MBCP Compatibility**: Extensive testing with existing components
- **Frontend Changes**: Gradual rollout with feature flags
- **Backward Compatibility**: Maintain legacy endpoints during transition

### **Testing Strategy**

#### **Unit Testing**
- [ ] Three-stage pipeline components
- [ ] Memory retrieval services
- [ ] Context block generation
- [ ] MBCP format validation

#### **Integration Testing**
- [ ] End-to-end pipeline flow
- [ ] ChatFork creation with context
- [ ] Memory persistence and retrieval
- [ ] Frontend-backend integration

#### **Performance Testing**
- [ ] Pipeline latency under load
- [ ] Memory usage optimization
- [ ] Token consumption analysis
- [ ] Concurrent user scenarios

---

## 📚 Reference Documentation

### **Key Files**
- `20250611_Unified_Prompt_Strategy.md` - Overall strategy and architecture
- `backend/Prompt_library/routing_prompt.yaml` - Stage 1 implementation
- `backend/Prompt_library/unified_Prompt.yaml` - Stage 3 implementation
- `frontend/src/services/ChatMemoryService.ts` - Memory management
- `backend/api/routes/llm.py` - Current LLM routing logic

### **Architecture Alignment**
This implementation directly supports the unified strategy by:
- **Solving Backstory Problem**: Memory stage provides conversation context
- **Addressing Double Memory**: Event sourcing with snapshot checkpoints
- **Maintaining MBCP Format**: Consistent structured responses
- **Enabling Tool Integration**: Clean delegation points in routing stage
- **Supporting Agent Workflows**: Structured handoff mechanisms
