import React, { useState, useEffect, useRef } from 'react';
import { useMindMapStore } from '../../core/state/MindMapStore';
import './ProjectManagementDialog.css';

interface Project {
  name: string;
  savedAt: number;
}

interface ProjectManagementDialogProps {
  onClose: () => void;
  setShowGovernanceChat?: (show: boolean) => void;
}

const ProjectManagementDialog: React.FC<ProjectManagementDialogProps> = ({ 
  onClose,
  setShowGovernanceChat
}) => {
  const { 
    projectName, 
    loadProject, 
    saveProject, 
    setProjectName, 
    createNewProject,
    deleteProject,
    renameProject,
    duplicateProject,
    getProjectsList,
    exportProjectToFile,
    importProjectFromFile
  } = useMindMapStore();

  // Close the governance chat dialog when project dialog opens
  useEffect(() => {
    if (setShowGovernanceChat) {
      setShowGovernanceChat(false);
    }
  }, [setShowGovernanceChat]);

  const [projects, setProjects] = useState<Project[]>([]);
  const [newProjectName, setNewProjectName] = useState('');
  const [renameTarget, setRenameTarget] = useState<string | null>(null);
  const [newName, setNewName] = useState('');
  const [activeTab, setActiveTab] = useState<'save' | 'open' | 'new' | 'export'>('save');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load projects list when dialog opens
  useEffect(() => {
    const projectsList = getProjectsList();
    setProjects(projectsList);
  }, [getProjectsList]);

  // Handle saving current project
  const handleSaveProject = () => {
    if (!projectName || projectName === 'Untitled' || projectName === 'Untitled Project') {
      setError('Please provide a project name');
      return;
    }
    
    const result = saveProject();
    if (result) {
      // Refresh projects list
      setProjects(getProjectsList());
      setError(null);
    } else {
      setError('Failed to save project');
    }
  };

  // Handle saving as new project name
  const handleSaveAs = () => {
    if (!newProjectName.trim()) {
      setError('Please provide a project name');
      return;
    }
    
    // Update project name and save
    setProjectName(newProjectName);
    const result = saveProject();
    
    if (result) {
      // Refresh projects list
      setProjects(getProjectsList());
      setNewProjectName('');
      setError(null);
    } else {
      setError('Failed to save project');
    }
  };

  // Handle opening a project
  const handleOpenProject = (name: string) => {
    const result = loadProject(name);
    if (result) {
      onClose();
    } else {
      setError(`Failed to load project: ${name}`);
    }
  };

  // Handle deleting a project
  const handleDeleteProject = (name: string) => {
    if (window.confirm(`Are you sure you want to delete "${name}"?`)) {
      const result = deleteProject(name);
      if (result) {
        // Refresh projects list
        setProjects(getProjectsList());
        setError(null);
      } else {
        setError(`Failed to delete project: ${name}`);
      }
    }
  };

  // Handle renaming a project
  const handleRenameProject = (oldName: string) => {
    if (!newName.trim()) {
      setError('Please provide a new name');
      return;
    }
    
    const result = renameProject(oldName, newName);
    if (result) {
      // Refresh projects list
      setProjects(getProjectsList());
      setRenameTarget(null);
      setNewName('');
      setError(null);
    } else {
      setError(`Failed to rename project: ${oldName}`);
    }
  };

  // Handle creating a new project
  const handleCreateNewProject = () => {
    if (!newProjectName.trim()) {
      setError('Please provide a project name');
      return;
    }
    
    const result = createNewProject(newProjectName);
    if (result) {
      onClose();
    } else {
      setError('Failed to create new project');
    }
  };

  // Handle duplicating a project
  const handleDuplicateProject = (name: string) => {
    const duplicateName = `${name} (Copy)`;
    const result = duplicateProject(name, duplicateName);
    
    if (result) {
      // Refresh projects list
      setProjects(getProjectsList());
      setError(null);
    } else {
      setError(`Failed to duplicate project: ${name}`);
    }
  };

  // Handle exporting a project to a file
  const handleExportProject = (name: string) => {
    try {
      exportProjectToFile(name);
      setSuccess(`Project "${name}" has been exported to your downloads folder`);
      setTimeout(() => setSuccess(null), 5000);
    } catch (error) {
      setError(`Failed to export project: ${name}`);
    }
  };

  // Handle importing a project from a file
  const handleImportProject = async (e: React.ChangeEvent<HTMLInputElement>) => {
    setError(null);
    if (!e.target.files || e.target.files.length === 0) {
      return;
    }
    
    const file = e.target.files[0];
    if (!file.name.toLowerCase().endsWith('.json')) {
      setError('Please select a JSON file');
      return;
    }
    
    try {
      const result = await importProjectFromFile(file);
      if (result) {
        // Refresh projects list
        setProjects(getProjectsList());
        setSuccess(`Project "${file.name}" has been imported successfully`);
        setTimeout(() => setSuccess(null), 5000);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      } else {
        setError('Failed to import project: Invalid file format');
      }
    } catch (error) {
      setError('Failed to import project');
    }
  };

  // Format date for display
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  return (
    <div className="project-dialog-overlay">
      <div className="project-dialog-container">
        <div className="project-dialog-header">
          <h2>Project Management</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        
        <div className="project-dialog-tabs">
          <button 
            className={`tab-button ${activeTab === 'save' ? 'active' : ''}`}
            onClick={() => setActiveTab('save')}
          >
            Save
          </button>
          <button 
            className={`tab-button ${activeTab === 'open' ? 'active' : ''}`}
            onClick={() => setActiveTab('open')}
          >
            Open
          </button>
          <button 
            className={`tab-button ${activeTab === 'new' ? 'active' : ''}`}
            onClick={() => setActiveTab('new')}
          >
            New
          </button>
          <button 
            className={`tab-button ${activeTab === 'export' ? 'active' : ''}`}
            onClick={() => setActiveTab('export')}
          >
            Export/Import
          </button>
        </div>
        
        <div className="project-dialog-content">
          {error && <div className="error-message">{error}</div>}
          {success && <div className="success-message">{success}</div>}
          
          {/* Save Tab */}
          {activeTab === 'save' && (
            <div>
              <div className="current-project">
                <p>Current project: <strong>{projectName}</strong></p>
                <button 
                  className="action-button"
                  onClick={handleSaveProject}
                  disabled={!projectName || projectName === 'Untitled' || projectName === 'Untitled Project'}
                >
                  Save Project
                </button>
              </div>
              
              <div className="save-as">
                <h3>Save As</h3>
                <div className="input-group">
                  <input
                    type="text"
                    value={newProjectName}
                    onChange={(e) => setNewProjectName(e.target.value)}
                    placeholder="New project name"
                  />
                  <button 
                    className="action-button"
                    onClick={handleSaveAs}
                    disabled={!newProjectName.trim()}
                  >
                    Save As
                  </button>
                </div>
              </div>
            </div>
          )}
          
          {/* Open Tab */}
          {activeTab === 'open' && (
            <div>
              <h3>Saved Projects</h3>
              {projects.length === 0 ? (
                <p>No saved projects found</p>
              ) : (
                <ul className="projects-list">
                  {projects.map((project) => (
                    <li key={project.name} className="project-item">
                      {renameTarget === project.name ? (
                        <div className="rename-controls">
                          <input
                            type="text"
                            value={newName}
                            onChange={(e) => setNewName(e.target.value)}
                            placeholder="New name"
                            autoFocus
                          />
                          <div>
                            <button 
                              className="small-button"
                              onClick={() => handleRenameProject(project.name)}
                            >
                              Save
                            </button>
                            <button 
                              className="small-button cancel"
                              onClick={() => {
                                setRenameTarget(null);
                                setNewName('');
                              }}
                            >
                              Cancel
                            </button>
                          </div>
                        </div>
                      ) : (
                        <>
                          <div className="project-info">
                            <span className="project-name">{project.name}</span>
                            <span className="project-date">{formatDate(project.savedAt)}</span>
                          </div>
                          <div className="project-actions">
                            <button 
                              className="small-button"
                              onClick={() => handleOpenProject(project.name)}
                              title="Open project"
                            >
                              Open
                            </button>
                            <button 
                              className="small-button"
                              onClick={() => {
                                setRenameTarget(project.name);
                                setNewName(project.name);
                              }}
                              title="Rename project"
                            >
                              Rename
                            </button>
                            <button 
                              className="small-button"
                              onClick={() => handleDuplicateProject(project.name)}
                              title="Duplicate project"
                            >
                              Duplicate
                            </button>
                            <button 
                              className="small-button delete"
                              onClick={() => handleDeleteProject(project.name)}
                              title="Delete project"
                            >
                              Delete
                            </button>
                          </div>
                        </>
                      )}
                    </li>
                  ))}
                </ul>
              )}
            </div>
          )}
          
          {/* New Tab */}
          {activeTab === 'new' && (
            <div>
              <h3>Create New Project</h3>
              <p className="info-text">
                This will create a new project with a clean mind map. Your current work will be saved before switching.
              </p>
              <div className="input-group">
                <input
                  type="text"
                  value={newProjectName}
                  onChange={(e) => setNewProjectName(e.target.value)}
                  placeholder="New project name"
                />
                <button 
                  className="action-button"
                  onClick={handleCreateNewProject}
                  disabled={!newProjectName.trim()}
                >
                  Create Project
                </button>
              </div>
            </div>
          )}
          
          {/* Export/Import Tab */}
          {activeTab === 'export' && (
            <div>
              <div className="export-section">
                <h3>Export Project</h3>
                <p className="info-text">
                  Save your project to a file on your computer that you can backup or share.
                </p>
                <div className="export-project-list">
                  {projects.length === 0 ? (
                    <p>No saved projects to export</p>
                  ) : (
                    <ul className="projects-list">
                      {projects.map((project) => (
                        <li key={project.name} className="project-item">
                          <div className="project-info">
                            <span className="project-name">{project.name}</span>
                            <span className="project-date">{formatDate(project.savedAt)}</span>
                          </div>
                          <div className="project-actions">
                            <button 
                              className="small-button"
                              onClick={() => handleExportProject(project.name)}
                              title="Export project to file"
                            >
                              Export
                            </button>
                          </div>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              </div>
              
              <div className="import-section">
                <h3>Import Project</h3>
                <p className="info-text">
                  Import a project from a file that was previously exported.
                </p>
                <div className="import-controls">
                  <input
                    type="file"
                    ref={fileInputRef}
                    accept=".json"
                    onChange={handleImportProject}
                    className="file-input"
                  />
                  <button 
                    className="action-button"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    Select File
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
        
        <div className="project-dialog-footer">
          <button className="cancel-button" onClick={onClose}>Close</button>
        </div>
      </div>
    </div>
  );
};

export default ProjectManagementDialog; 