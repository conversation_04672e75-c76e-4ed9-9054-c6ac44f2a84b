/**
 * MessageInput.css
 *
 * Styles for the MessageInput component.
 */

.message-input-container {
  display: flex;
  padding: 12px;
  border-top: 1px solid #e2e8f0;
  background-color: #ffffff;
}

.message-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
}

.message-input:focus {
  border-color: #3182ce;
}

.send-button {
  margin-left: 8px;
  padding: 8px 16px;
  background-color: #3182ce;
  border: 1px solid #3182ce;
  border-radius: 4px;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.send-button:hover {
  background-color: #2b6cb0;
  border-color: #2b6cb0;
}

.send-button:disabled {
  background-color: #a0aec0;
  border-color: #a0aec0;
  cursor: not-allowed;
}

/* Debug button */
.debug-button {
  margin-left: 8px;
  padding: 8px 16px;
  background-color: #f56565;
  border: 1px solid #e53e3e;
  border-radius: 4px;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.debug-button:hover {
  background-color: #e53e3e;
  border-color: #c53030;
}
