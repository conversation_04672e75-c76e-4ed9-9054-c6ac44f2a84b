/**
 * MessageList.css
 * 
 * Styles for the MessageList component.
 */

.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message {
  display: flex;
  flex-direction: column;
  max-width: 80%;
  padding: 12px;
  border-radius: 8px;
  position: relative;
}

.message.user {
  align-self: flex-end;
  background-color: #3498db;
  color: #ffffff;
}

.message.assistant {
  align-self: flex-start;
  background-color: #f1f5f9;
  color: #1e293b;
}

.message.error {
  background-color: #fee2e2;
  color: #b91c1c;
}

.message-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.message-text {
  white-space: pre-wrap;
  word-break: break-word;
}

.message-timestamp {
  font-size: 10px;
  opacity: 0.7;
  align-self: flex-end;
  margin-top: 4px;
}

.suggested-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.action-button {
  padding: 6px 12px;
  background-color: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background-color: #f8fafc;
  border-color: #cbd5e0;
}

.build-mindmap-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.build-mindmap-button {
  padding: 8px 16px;
  background-color: #3182ce;
  border: 1px solid #3182ce;
  border-radius: 4px;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.build-mindmap-button:hover {
  background-color: #2b6cb0;
  border-color: #2b6cb0;
}
