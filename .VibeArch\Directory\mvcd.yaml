codebase:
- file: MindBack/backend/main.py
  element: main
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1740931776.0
- file: architecture/frontend/scripts/validate-deps.js
  element: validate-deps
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - dependency-cruiser
  - path
  loc: 22
  last_modified: 1744033158.0
- file: architecture/frontend/tools/dependency-cruiser.config.js/.dependency-cruiser.js
  element: .dependency-cruiser
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 216
  last_modified: 1744026286.0
- file: backend/Prompt_library/instantiation_template_router.py
  element: instantiation_template_router
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 86
  last_modified: 1743370428.0
- file: backend/api/config/settings.py
  element: Settings
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - dotenv
  - pydantic_settings
  loc: 23
  last_modified: 1743806738.0
- file: backend/api/main.py
  element: root
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - fastapi
  - logging
  - uvicorn
  loc: 69
  last_modified: 1745506034.2682712
- file: backend/api/models/mbcp_models.py
  element: LLMChatRequest
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - pydantic
  loc: 57
  last_modified: 1745528550.2101526
- file: backend/api/routes/llm.py
  element: llm_chat
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - fastapi
  - logging
  - uuid
  - yaml
  loc: 465
  last_modified: 1748930020.4579444
- file: backend/api/routes/llm_fixed.py
  element: ActionModel
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - fastapi
  - logging
  - pydantic
  - yaml
  loc: 171
  last_modified: 1743415202.0
- file: backend/api/routes/logging.py
  element: LogEntry
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - fastapi
  - logging
  - pydantic
  loc: 51
  last_modified: 1745483091.3712733
- file: backend/api/schemas/mbcp_schemas.py
  element: get_mbcp_function_schema
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - logging
  loc: 156
  last_modified: 1745529181.330057
- file: backend/api/services/openai_service.py
  element: MetadataModel
  type: Utility
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - fastapi
  - logging
  - openai
  - pydantic
  loc: 132
  last_modified: 1743412058.0
- file: backend/api/services/prompt_service.py
  element: load_yaml_prompt
  type: Utility
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - logging
  - yaml
  loc: 83
  last_modified: 1748848433.87568
- file: backend/api/services/response_processor.py
  element: process_llm_response
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - logging
  loc: 211
  last_modified: 1745528560.245338
- file: backend/api/services/template_processor.py
  element: replace_template_variables
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - logging
  loc: 44
  last_modified: 1744143368.0
- file: backend/app/main.py
  element: root
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - fastapi
  loc: 16
  last_modified: 1740986776.0
- file: backend/app/routers/mindmap.py
  element: MindMapNode
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - fastapi
  - pydantic
  loc: 84
  last_modified: 1740990640.0
- file: backend/config/intent_config.py
  element: load_intent_config
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - logging
  - yaml
  loc: 43
  last_modified: 1745527521.1071389
- file: backend/main.py
  element: ConnectionManager
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - dotenv
  - fastapi
  - logging
  - openai
  - pydantic
  loc: 162
  last_modified: 1744198038.0
- file: backend/scripts/sync_intent_types.py
  element: generate_frontend_intent_types
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - yaml
  loc: 24
  last_modified: 1745527701.0677278
- file: backend/test_api_key.py
  element: test_api_key
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - crewai
  - dotenv
  - langchain_openai
  - openai
  loc: 67
  last_modified: 1742598104.0
- file: frontend/dist/assets/OptimizedMindMap_Modular-16ee055c.js
  element: OptimizedMindMap_Modular-16ee055c
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 26
  last_modified: 1744724262.8323786
- file: frontend/dist/assets/index-80986735.js
  element: theme
  type: Context
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 134
  last_modified: 1744724262.8323786
- file: frontend/src/App.tsx
  element: App
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - react-router-dom
  loc: 125
  last_modified: 1747082974.7878327
- file: frontend/src/AppRefactored.tsx
  element: AppRefactored
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 92
  last_modified: 1747168968.1131313
- file: frontend/src/components/ChatFork/ChatForkCanvas.tsx
  element: ChatForkCanvas
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 137
  last_modified: 1748893696.2042396
- file: frontend/src/components/ChatFork/ChatForkCanvasContainer.tsx
  element: ChatForkCanvasContainer
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 71
  last_modified: 1747146005.1640704
- file: frontend/src/components/ChatFork/ChatForkConnection.tsx
  element: ChatForkConnection
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 113
  last_modified: 1742601482.0
- file: frontend/src/components/ChatFork/ChatForkContainer.tsx
  element: ChatForkContainer
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 262
  last_modified: 1744707021.3691163
- file: frontend/src/components/ChatFork/ChatForkNode.tsx
  element: ChatForkNode
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 38
  last_modified: 1742601472.0
- file: frontend/src/components/ChatFork/ChatForkStore.ts
  element: ChatForkStore
  type: Store
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - zustand
  loc: 62
  last_modified: 1748894005.1160755
- file: frontend/src/components/ChatFork/ChatForkView.tsx
  element: ChatForkView
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 91
  last_modified: 1744126834.0
- file: frontend/src/components/ChatFork/index.ts
  element: index
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 5
  last_modified: 1742601452.0
- file: frontend/src/components/ChatFork/index.tsx
  element: ChatFork
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 358
  last_modified: 1742974800.0
- file: frontend/src/components/ErrorBoundary.tsx
  element: ErrorBoundary
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 115
  last_modified: 1746707124.3876188
- file: frontend/src/components/InitialView.tsx
  element: InitialView
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - react-router-dom
  loc: 161
  last_modified: 1748852277.6342013
- file: frontend/src/components/MindMap/MindMapBackup - frontend/Connection.tsx
  element: Connection
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 32
  last_modified: 1741075236.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/DesignControls.tsx
  element: DesignControls
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 93
  last_modified: 1741200976.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/LineStyleDialog.tsx
  element: LineStyleDialog
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 82
  last_modified: 1741293248.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/MindMap.tsx
  element: MindMap
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 6
  last_modified: 1741603776.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/Node.tsx
  element: Node
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 72
  last_modified: 1742475516.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/ProjectDialog.tsx
  element: ProjectDialog
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 89
  last_modified: 1741200940.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/Toolbar.tsx
  element: Toolbar
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 20
  last_modified: 1741279826.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Agents/GovernanceAgent.tsx
  element: GovernanceAgent
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 292
  last_modified: 1742988946.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Agents/HatAgents.tsx
  element: HatAgents
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375410.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Agents/SpecialistAgents.tsx
  element: SpecialistAgents
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375412.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Canvas/MindMapCanvasSimple.tsx
  element: MindMapCanvasSimple
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - konva
  - react
  - react-konva
  loc: 265
  last_modified: 1744053258.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Canvas/hooks/useCanvasInteraction.ts
  element: useCanvasInteraction
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 145
  last_modified: 1742406982.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Canvas/hooks/useCanvasRendering.ts
  element: useCanvasRendering
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 77
  last_modified: 1741775856.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Canvas/hooks/useCanvasState.ts
  element: useCanvasState
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 103
  last_modified: 1741905124.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Canvas/types/canvas.types.ts
  element: canvas.types
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 30
  last_modified: 1741904020.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/ControlPanel/ManualNodeControls.tsx
  element: ManualNodeControls
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 124
  last_modified: 1743710354.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Controls/LLMControls.tsx
  element: LLMControls
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375410.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Controls/MindMapControls.tsx
  element: MindMapControls
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 134
  last_modified: 1741484306.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Controls/MindMapToolbar.tsx
  element: MindMapToolbar
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 53
  last_modified: 1741769184.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Dialogs/ConnectionDialog.tsx
  element: ConnectionDialog
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 337
  last_modified: 1741424508.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Dialogs/DesignControlsDialog.tsx
  element: DesignControlsDialog
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 69
  last_modified: 1741598910.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Dialogs/NodeDialog/NewNodeDialog.tsx
  element: NewNodeDialog
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - react-draggable
  loc: 300
  last_modified: 1743714206.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Dialogs/NodeDialog/NodeDesignTab.tsx
  element: NodeDesignTab
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375410.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Dialogs/NodeDialog/NodeHatTab.tsx
  element: NodeHatTab
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375410.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Dialogs/NodeDialog/NodeInfoTab.tsx
  element: NodeInfoTab
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375410.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Dialogs/NodeDialog/NodeLLMTab.tsx
  element: NodeLLMTab
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 116
  last_modified: 1741435042.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Dialogs/ProjectDialog.tsx
  element: ProjectDialog
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 39
  last_modified: 1741598892.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Dialogs/ProjectManagementDialog.tsx
  element: ProjectManagementDialog
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 410
  last_modified: 1742491614.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Dialogs/StartupDialog/StartupDialog.tsx
  element: StartupDialog
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 114
  last_modified: 1741441184.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Dialogs/index.ts
  element: index
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 4
  last_modified: 1743631792.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Manager/EnhancedMindMapManager.tsx
  element: EnhancedMindMapManager
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - react-draggable
  loc: 402
  last_modified: 1746692420.8617039
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/MindMapManager/MindMapManager.tsx
  element: MindMapManager
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 26
  last_modified: 1741484304.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/MindMapView.tsx
  element: MindMapView
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 223
  last_modified: 1742476678.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Node/ConnectionComponent.tsx
  element: ConnectionComponent
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - react-konva
  loc: 29
  last_modified: 1746692391.0629308
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Node/NodeComponent.tsx
  element: NodeComponent
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - konva
  - react
  - react-konva
  loc: 109
  last_modified: 1742206786.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/Node/NodeRenderer.tsx
  element: NodeRenderer
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - konva
  - react
  - react-konva
  loc: 156
  last_modified: 1743714194.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/components/index.ts
  element: index
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 8
  last_modified: **********.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/config/api.ts
  element: api
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 18
  last_modified: **********.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/context/MindMapContext.tsx
  element: MindMapProvider
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 245
  last_modified: **********.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/core/MindMapCore.ts
  element: MindMapCore
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 145
  last_modified: **********.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/core/adapters/ChatForkAdapter.ts
  element: ChatForkAdapter
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 145
  last_modified: **********.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/core/createNode.ts
  element: createNode
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 32
  last_modified: **********.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/core/models/Connection.ts
  element: DEFAULT
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - uuid
  loc: 105
  last_modified: 1741451872.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/core/models/HatContribution.ts
  element: DEFAULT
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - uuid
  loc: 70
  last_modified: 1741375530.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/core/models/Node.ts
  element: Node
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - uuid
  loc: 158
  last_modified: 1743708930.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/core/operations/LayoutEngine.ts
  element: DEFAULT
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 136
  last_modified: 1742503154.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/core/operations/NodeOperations.ts
  element: NodeOperations
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 29
  last_modified: 1741775890.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/core/operations/RelationshipManager.ts
  element: RelationshipManager
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375410.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/core/rag/GraphRAG.ts
  element: GraphRAG
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375410.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/core/rag/HybridRAG.ts
  element: HybridRAG
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375410.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/core/rag/MemoryRAG.ts
  element: MemoryRAG
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375410.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/core/state/ChatForkStore.ts
  element: ChatForkStore
  type: Store
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - zustand
  loc: 115
  last_modified: 1743782168.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/core/state/CommandManager.ts
  element: CommandManager
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375410.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/core/state/MindMapStore.ts
  element: useMindMapStore
  type: Store
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - uuid
  - zustand
  loc: 936
  last_modified: 1746738226.457809
- file: frontend/src/components/MindMap/MindMapBackup - frontend/core/state/store/connectionSlice.ts
  element: connectionSlice
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - zustand
  loc: 41
  last_modified: 1741776574.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/core/state/store/index.ts
  element: index
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - zustand
  loc: 12
  last_modified: 1741776618.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/core/state/store/nodeSlice.ts
  element: nodeSlice
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - zustand
  loc: 50
  last_modified: 1741776558.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/core/state/store/projectSlice.ts
  element: projectSlice
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - zustand
  loc: 56
  last_modified: 1741776500.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/core/state/store/types.ts
  element: types
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 69
  last_modified: 1744048320.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/core/state/store/undoRedoSlice.ts
  element: undoRedoSlice
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - zustand
  loc: 56
  last_modified: 1741776540.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/events/EventBus.ts
  element: EventBus
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375412.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/events/EventTypes.ts
  element: EventTypes
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375412.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/events/handlers/LLMEventHandlers.ts
  element: LLMEventHandlers
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375412.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/events/handlers/NodeEventHandlers.ts
  element: NodeEventHandlers
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375412.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/events/handlers/UIEventHandlers.ts
  element: UIEventHandlers
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375412.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/hooks/useAutosave.ts
  element: useAutosave
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 36
  last_modified: 1741933800.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/hooks/useCanvas.ts
  element: useCanvas
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375412.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/hooks/useGovernance.ts
  element: useGovernance
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375412.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/hooks/useHatContributions.ts
  element: useHatContributions
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375412.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/hooks/useKeyboardShortcuts.ts
  element: useKeyboardShortcuts
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 63
  last_modified: 1741606862.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/hooks/useLLMIntegration.ts
  element: useLLMIntegration
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375412.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/hooks/useMindMapCore.ts
  element: useMindMapCore
  type: Hook
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 94
  last_modified: 1746692762.135091
- file: frontend/src/components/MindMap/MindMapBackup - frontend/hooks/useNodeInteraction.ts
  element: useNodeInteraction
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375412.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/hooks/useNodeManagement.ts
  element: useNodeManagement
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 150
  last_modified: 1741607730.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/hooks/useProjectManagement.ts
  element: useProjectManagement
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 155
  last_modified: 1741078508.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/hooks/useVirtualRendering.ts
  element: useVirtualRendering
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375412.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/hooks/useZoomAndPan.ts
  element: useZoomAndPan
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 132
  last_modified: 1741484308.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/index.ts
  element: index
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 93
  last_modified: 1741078730.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/index.tsx
  element: MindMap
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 115
  last_modified: 1743705910.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/layouts/LayoutManager.ts
  element: LayoutManager
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 39
  last_modified: 1742569328.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/layouts/adapters/MindMapStoreAdapter.ts
  element: MindMapStoreAdapter
  type: Store
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 60
  last_modified: 1742577500.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/layouts/components/LayoutSelector.tsx
  element: LayoutSelector
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 75
  last_modified: 1742577604.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/layouts/hooks/useLayout.ts
  element: useLayout
  type: Hook
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 18
  last_modified: 1742479500.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/layouts/index.ts
  element: index
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 16
  last_modified: 1742577562.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/layouts/strategies/BottomUpLayout.ts
  element: BottomUpLayout
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 38
  last_modified: 1742479516.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/layouts/strategies/CompactLeftToRightLayout.ts
  element: CompactLeftToRightLayout
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 86
  last_modified: 1742569314.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/layouts/strategies/LeftToRightLayout.ts
  element: LeftToRightLayout
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 37
  last_modified: 1742479416.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/layouts/strategies/RadialLayout.ts
  element: RadialLayout
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 47
  last_modified: 1742479448.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/layouts/strategies/TopDownLayout.ts
  element: TopDownLayout
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 37
  last_modified: 1742479430.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/layouts/types.ts
  element: DEFAULT
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 52
  last_modified: 1742569286.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/layouts/utils.ts
  element: utils
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 185
  last_modified: 1742577552.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/services/api/HatLLM.ts
  element: HatLLM
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375412.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/services/api/LLMService.ts
  element: LLMService
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 114
  last_modified: 1741767368.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/services/api/PromptRegistry.ts
  element: PromptRegistry
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 58
  last_modified: 1741777966.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/services/api/debug.ts
  element: debug
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 1
  last_modified: 1741766774.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/services/api/types.ts
  element: types
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 23
  last_modified: 1741777854.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/services/prompts/YAMLLoader.ts
  element: YAMLLoader
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - js-yaml
  loc: 23
  last_modified: 1741628454.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/services/storage/ExportService.ts
  element: ExportService
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375412.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/services/storage/LocalStorage.ts
  element: LocalStorage
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741375412.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/services/storage/ProjectStorage.ts
  element: ProjectStorage
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 0
  last_modified: 1741296224.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/types/MessageStatus.ts
  element: MessageStatusValues
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 6
  last_modified: 1741812556.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/types/index.ts
  element: index
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 108
  last_modified: 1742206392.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/utils/MBCPProcessor.ts
  element: MBCPProcessor
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 612
  last_modified: 1744047732.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/utils/ManualJsonProcessor.ts
  element: ManualJsonProcessor
  type: Store
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 536
  last_modified: 1743703502.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/utils/debug.ts
  element: debug
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 7
  last_modified: 1741680212.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/utils/security.ts
  element: security
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 18
  last_modified: 1741812550.0
- file: frontend/src/components/MindMap/MindMapBackup - frontend/utils/testManualMethod.ts
  element: testManualMethod
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 113
  last_modified: 1742544350.0
- file: frontend/src/components/MindMap/components/Canvas/StageCompatWrapper.tsx
  element: StageCompatWrapper
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - react-konva
  loc: 85
  last_modified: 1747168225.587489
- file: frontend/src/components/MindMap/components/Manager/EnhancedMindMapManager.tsx
  element: EnhancedMindMapManager
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - react-draggable
  loc: 502
  last_modified: 1748861512.5627592
- file: frontend/src/components/MindMap/components/Manager/EnhancedMindMapManagerWrapper.tsx
  element: EnhancedMindMapManagerWrapper
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 18
  last_modified: 1747050113.9340127
- file: frontend/src/components/MindMap/components/Manager/index.ts
  element: index
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 1
  last_modified: 1746738503.5063748
- file: frontend/src/components/MindMap/layouts/components/LayoutSelector.tsx
  element: LayoutSelector
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 177
  last_modified: 1748862180.6575031
- file: frontend/src/components/MindMap/layouts/index.ts
  element: index
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 2
  last_modified: 1746738691.4062881
- file: frontend/src/components/MindMap/layouts/types.ts
  element: DEFAULT
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 52
  last_modified: 1746738646.1391706
- file: frontend/src/components/MindMap/utils/MBCPProcessor.ts
  element: MBCPProcessor
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - uuid
  loc: 194
  last_modified: **********.9836948
- file: frontend/src/components/MindMap/utils/MBCPProcessorFix.ts
  element: MBCPProcessorFix
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - uuid
  loc: 188
  last_modified: **********.9429636
- file: frontend/src/components/OptimizedMindMap_Modular.tsx
  element: OptimizedMindMap
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 264
  last_modified: **********.9626932
- file: frontend/src/components/index.ts
  element: index
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 1
  last_modified: 1741603722.0
- file: frontend/src/core/adapters/GovernanceMindBookAdapter.ts
  element: GovernanceMindBookAdapter
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 49
  last_modified: 1747144921.9811165
- file: frontend/src/core/adapters/GovernanceMindMapAdapter.ts
  element: GovernanceMindMapAdapter
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - uuid
  loc: 60
  last_modified: 1748865047.1670496
- file: frontend/src/core/adapters/MindMapAdapter.ts
  element: MindMapAdapter
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 114
  last_modified: **********.984692
- file: frontend/src/core/config/intentTypes.ts
  element: intentTypes
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 90
  last_modified: 1745527562.2575817
- file: frontend/src/core/governance/MindMapGovernance.ts
  element: DEFAULT
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 220
  last_modified: 1748860256.5752845
- file: frontend/src/core/layout/LayoutManager.ts
  element: LayoutManager
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 364
  last_modified: 1748868255.2576537
- file: frontend/src/core/layout/UnifiedLayoutManager.ts
  element: UnifiedLayoutManager
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 286
  last_modified: 1748873517.1689603
- file: frontend/src/core/mbcp/MBCPProcessor.ts
  element: MBCPProcessor
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - uuid
  loc: 252
  last_modified: **********.9836948
- file: frontend/src/core/mbcp/TeleologicalWorkflow.ts
  element: TeleologicalWorkflow
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - uuid
  loc: 407
  last_modified: **********.7443817
- file: frontend/src/core/mindsheet/createMindsheet.ts
  element: createMindsheet
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 28
  last_modified: **********.0130847
- file: frontend/src/core/positioning/PositioningContext.tsx
  element: PositioningManagerProvider
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 99
  last_modified: **********.1241484
- file: frontend/src/core/positioning/UIPositioningManager.ts
  element: UIPositioningManager
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 249
  last_modified: **********.105469
- file: frontend/src/core/positioning/index.ts
  element: index
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 2
  last_modified: **********.9620867
- file: frontend/src/core/routing/MainRouter.ts
  element: MainRouter
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 187
  last_modified: **********.945653
- file: frontend/src/core/services/ApplicationService.ts
  element: ApplicationService
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 165
  last_modified: **********.3687425
- file: frontend/src/core/services/KeyboardManager.ts
  element: KeyboardManager
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 169
  last_modified: **********.9686942
- file: frontend/src/core/services/LayoutGovernanceService.ts
  element: LayoutGovernanceService
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 331
  last_modified: **********.173544
- file: frontend/src/core/services/LayoutService.ts
  element: LayoutService
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 268
  last_modified: **********.9626932
- file: frontend/src/core/services/MindBookService.ts
  element: MindBookService
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - uuid
  loc: 163
  last_modified: 1747163732.627715
- file: frontend/src/core/services/MindObjectService.ts
  element: MindObjectService
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - uuid
  loc: 372
  last_modified: 1747169238.3850267
- file: frontend/src/core/services/MindSheetService.ts
  element: MindSheetService
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 193
  last_modified: 1747163777.1580772
- file: frontend/src/core/services/RegistrationManager.ts
  element: RegistrationManager
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 216
  last_modified: 1747164953.6905327
- file: frontend/src/core/services/StoreService.ts
  element: StoreService
  type: Store
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 135
  last_modified: 1748861696.9699903
- file: frontend/src/core/state/ApplicationStore.ts
  element: ApplicationStore
  type: Store
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - zustand
  loc: 293
  last_modified: 1747163600.4029891
- file: frontend/src/core/state/MindBookStore.ts
  element: MindBookStore
  type: Store
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - uuid
  - zustand
  loc: 95
  last_modified: 1747162987.4325898
- file: frontend/src/core/state/MindMapStore.ts
  element: MindMapStore
  type: Store
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - uuid
  - zustand
  loc: 315
  last_modified: **********.9686942
- file: frontend/src/core/state/MindMapStoreFactory.ts
  element: MindMapStoreFactory
  type: Store
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - zustand
  loc: 125
  last_modified: 1748873526.668091
- file: frontend/src/core/state/MindMapStoreManager.ts
  element: MindMapStoreManager
  type: Store
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - zustand
  loc: 142
  last_modified: 1746816711.428962
- file: frontend/src/core/state/StoreRegistry.ts
  element: StoreRegistry
  type: Store
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - zustand
  loc: 96
  last_modified: 1747144381.761118
- file: frontend/src/core/state/StoreTypes.ts
  element: StoreTypes
  type: Store
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 52
  last_modified: 1747144496.4544182
- file: frontend/src/core/types/LayoutTypes.ts
  element: DEFAULT
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 124
  last_modified: 1748861111.6208515
- file: frontend/src/core/types/MindMapTypes.ts
  element: MindMapTypes
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 48
  last_modified: 1747087659.7918937
- file: frontend/src/core/utils/GovernanceTestUtils.ts
  element: GovernanceTestUtils
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 121
  last_modified: 1748873243.346377
- file: frontend/src/features/context/components/ContextPanel.tsx
  element: ContextPanel
  type: Context
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 138
  last_modified: 1745396553.7845316
- file: frontend/src/features/context/components/ContextPanelPositioned.tsx
  element: ContextPanelPositioned
  type: Context
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 67
  last_modified: 1746652670.083161
- file: frontend/src/features/context/components/ContextToggleButton.tsx
  element: ContextToggleButton
  type: Context
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 36
  last_modified: 1745396517.7344372
- file: frontend/src/features/context/components/FooterContextButton.tsx
  element: FooterContextButton
  type: Context
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 17
  last_modified: 1745397113.9528613
- file: frontend/src/features/context/index.ts
  element: index
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 3
  last_modified: 1745413098.757728
- file: frontend/src/features/governance/GovernanceChat.tsx
  element: GovernanceChat
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - react-rnd
  loc: 155
  last_modified: 1745497545.1554313
- file: frontend/src/features/governance/components/ChatHeader.tsx
  element: ChatHeader
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 63
  last_modified: 1744238926.0
- file: frontend/src/features/governance/components/MessageInput.tsx
  element: MessageInput
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 68
  last_modified: 1744152236.0
- file: frontend/src/features/governance/components/MessageList.tsx
  element: MessageList
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 90
  last_modified: 1744237484.0
- file: frontend/src/features/governance/hooks/useGovernanceChat.ts
  element: useGovernanceChat
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - uuid
  loc: 197
  last_modified: 1745492368.790462
- file: frontend/src/features/governance/types.ts
  element: types
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 18
  last_modified: 1744152012.0
- file: frontend/src/features/mindmap/MindMap.tsx
  element: MindMap
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 137
  last_modified: 1747169432.2540362
- file: frontend/src/features/mindmap/components/Canvas/ConnectionComponent.tsx
  element: ConnectionComponent
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - react-konva
  loc: 81
  last_modified: 1747130503.3108232
- file: frontend/src/features/mindmap/components/Canvas/ConnectionComponentRefactored.tsx
  element: ConnectionComponent
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - react-konva
  loc: 93
  last_modified: 1747165542.8468053
- file: frontend/src/features/mindmap/components/Canvas/ConnectionComponentTest.tsx
  element: ConnectionComponentTest
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 14
  last_modified: 1746701263.2494698
- file: frontend/src/features/mindmap/components/Canvas/MindMapCanvas.tsx
  element: MindMapCanvas
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - react-konva
  loc: 418
  last_modified: **********.9656942
- file: frontend/src/features/mindmap/components/Canvas/MindMapCanvasRefactored.tsx
  element: MindMapCanvas
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - react-konva
  loc: 254
  last_modified: 1748873180.4237146
- file: frontend/src/features/mindmap/components/Canvas/MindMapCanvasWrapper.tsx
  element: MindMapCanvasWrapper
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 126
  last_modified: 1748868255.2769084
- file: frontend/src/features/mindmap/components/Canvas/NodeComponent.tsx
  element: NodeComponent
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - react-konva
  loc: 422
  last_modified: 1748860615.2021282
- file: frontend/src/features/mindmap/components/Canvas/NodeComponentRefactored.tsx
  element: NodeComponent
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - react-konva
  loc: 366
  last_modified: 1747171577.8819153
- file: frontend/src/features/mindmap/components/Dialogs/ProjectDialog.tsx
  element: ProjectDialog
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 132
  last_modified: 1744039642.0
- file: frontend/src/features/mindmap/components/NodeBox/NodeBox.tsx
  element: NodeBox
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - react-rnd
  loc: 578
  last_modified: 1748851379.3685946
- file: frontend/src/features/mindmap/components/NodeBox/index.ts
  element: index
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 1
  last_modified: 1744464118.2077534
- file: frontend/src/features/mindmap/components/Toolbar/MindMapToolbar.tsx
  element: MindMapToolbar
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 72
  last_modified: 1746690786.2219067
- file: frontend/src/features/mindsheet/MindBook.tsx
  element: MindBook
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 55
  last_modified: 1747144584.9720106
- file: frontend/src/features/mindsheet/MindSheet.tsx
  element: MindSheet
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 449
  last_modified: 1748893675.1979086
- file: frontend/src/features/mindsheet/MindSheetTabs.tsx
  element: MindSheetTabs
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 150
  last_modified: 1748868255.2769084
- file: frontend/src/features/mindsheet/MindSheetWrapper.tsx
  element: MindSheetWrapper
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 54
  last_modified: 1747148497.0041678
- file: frontend/src/features/mindsheet/index.ts
  element: index
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 4
  last_modified: 1747144904.527784
- file: frontend/src/features/mindsheet/test_mindsheet_tabs.js
  element: test_mindsheet_tabs
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 59
  last_modified: 1744609006.0977774
- file: frontend/src/governance/chat/GovernanceBoxPositioned.tsx
  element: GovernanceBoxPositioned
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - react-rnd
  - uuid
  loc: 595
  last_modified: 1748893987.5826318
- file: frontend/src/governance/chat/GovernanceChatDialog.tsx
  element: GovernanceChatDialog
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 34
  last_modified: 1745396746.7751307
- file: frontend/src/governance/chat/Implementation.tsx
  element: Implementation
  type: Hook
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - react-rnd
  - uuid
  loc: 471
  last_modified: 1747158836.4139657
- file: frontend/src/governance/chat/IntentSelector.tsx
  element: IntentSelector
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 91
  last_modified: 1748873190.343972
- file: frontend/src/governance/chat/MessageList.tsx
  element: MessageList
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - uuid
  loc: 250
  last_modified: 1747156382.080314
- file: frontend/src/governance/chat/components/DialogHeader.tsx
  element: DialogHeader
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 151
  last_modified: 1747083957.9205482
- file: frontend/src/governance/chat/components/FooterGovernanceButton.tsx
  element: FooterGovernanceButton
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 23
  last_modified: 1747078589.364609
- file: frontend/src/governance/chat/components/MessageInput.tsx
  element: MessageInput
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 87
  last_modified: 1744147278.0
- file: frontend/src/governance/chat/components/MessageList.tsx
  element: MessageList
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 86
  last_modified: 1743703562.0
- file: frontend/src/governance/chat/components/ModelSelector.tsx
  element: ModelSelector
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 359
  last_modified: 1747158799.9770741
- file: frontend/src/governance/chat/hooks/useChat.ts
  element: useChat
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  - uuid
  loc: 464
  last_modified: 1747158190.183917
- file: frontend/src/governance/chat/hooks/useContextStateMachine.ts
  element: useContextStateMachine
  type: Context
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - lodash
  - react
  loc: 243
  last_modified: 1748894047.2092996
- file: frontend/src/governance/chat/hooks/useDragAndResize.ts
  element: useDragAndResize
  type: Hook
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 121
  last_modified: 1741812560.0
- file: frontend/src/governance/chat/index.ts
  element: GovernanceChatDialog
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 2
  last_modified: 1744239192.0
- file: frontend/src/governance/chat/state/ChatStore.ts
  element: useChatStore
  type: Store
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - zustand
  loc: 55
  last_modified: 1746654166.4409864
- file: frontend/src/governance/chat/state/ContextStateMachine.ts
  element: contextStateMachine
  type: Context
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - events
  - uuid
  loc: 190
  last_modified: 1747158556.2285528
- file: frontend/src/governance/chat/types/index.ts
  element: index
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 63
  last_modified: 1743627068.0
- file: frontend/src/governance/chat/utils/debug.ts
  element: debug
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 77
  last_modified: 1743024682.0
- file: frontend/src/index.tsx
  element: index
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 80
  last_modified: 1746706447.1837764
- file: frontend/src/main.tsx
  element: main
  type: Component
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 116
  last_modified: 1748873256.1364417
- file: frontend/src/polyfills/events.js
  element: eventEmitter
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - events
  loc: 4
  last_modified: 1747161751.7359378
- file: frontend/src/polyfills/index.js
  element: index
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 9
  last_modified: 1747161763.256517
- file: frontend/src/services/ChatMemoryService.ts
  element: ChatMemoryService
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 110
  last_modified: 1743627008.0
- file: frontend/src/services/api/GovernanceAPI.ts
  element: GovernanceAPI
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 60
  last_modified: 1746705741.2654872
- file: frontend/src/services/api/GovernanceLLM.ts
  element: GovernanceLLM
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 53
  last_modified: **********.9656942
- file: frontend/src/types.d.ts
  element: BrowserRouter
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 17
  last_modified: 1740929966.0
- file: frontend/src/types/MessageStatus.ts
  element: MessageStatusValues
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 6
  last_modified: 1743627776.0
- file: frontend/src/types/mindmap.ts
  element: mindmap
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 43
  last_modified: 1740994870.0
- file: frontend/src/types/react-draggable.d.ts
  element: class
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - react
  loc: 55
  last_modified: 1741812562.0
- file: frontend/src/utils/keyboardHandler.ts
  element: keyboardHandler
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 70
  last_modified: 1744565343.398626
- file: frontend/src/utils/keyboardShortcuts.ts
  element: keyboardShortcuts
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 73
  last_modified: 1740995344.0
- file: frontend/src/utils/polyfills.ts
  element: eventEmitter
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 45
  last_modified: 1747161579.45645
- file: frontend/src/utils/reactCompatibility.ts
  element: reactCompatibility
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 107
  last_modified: 1747162504.639081
- file: frontend/src/vite-env.d.ts
  element: vite-env.d
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies: []
  loc: 6
  last_modified: 1741465174.0
- file: frontend/vite.config.js
  element: defineConfig
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - vite
  loc: 11
  last_modified: 1746738226.2321508
- file: frontend/vite.config.ts
  element: defineConfig
  type: Type
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - path
  - vite
  loc: 104
  last_modified: 1747161805.265673
