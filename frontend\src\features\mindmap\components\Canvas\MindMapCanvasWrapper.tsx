/**
 * MindMapCanvasWrapper Component
 *
 * A wrapper component that handles all store access for MindMapCanvas.
 * This component ensures that Zustand store hooks are called at the top level
 * and passes down store references as props to the MindMapCanvas component.
 */

import React, { useEffect, useState } from 'react';
import MindMapCanvas from './MindMapCanvas';
import { getMindMapStore, hasMindMapStore } from '../../../../core/state/MindMapStoreFactory';
import RegistrationManager, { EventType } from '../../../../core/services/RegistrationManager';

interface MindMapCanvasWrapperProps {
  width: number;
  height: number;
  sheetId: string;
}

const MindMapCanvasWrapper: React.FC<MindMapCanvasWrapperProps> = (props) => {
  // State to track if the store is ready
  const [storeReady, setStoreReady] = useState(false);

  // Get the sheet-specific store
  const store = getMindMapStore(props.sheetId);

  // Effect to ensure the store is properly initialized
  useEffect(() => {
    console.log('MindMapCanvasWrapper: Mounted for sheet:', props.sheetId);

    // Ensure the store exists for this sheet
    if (!hasMindMapStore(props.sheetId)) {
      console.log('MindMapCanvasWrapper: Creating new store for sheet:', props.sheetId);
    } else {
      console.log('MindMapCanvasWrapper: Using existing store for sheet:', props.sheetId);
    }

    // Check if the store has nodes
    const storeState = store.getState();
    const hasNodes = Object.keys(storeState.nodes).length > 0;

    console.log('MindMapCanvasWrapper: Store has nodes:', hasNodes, 'count:', Object.keys(storeState.nodes).length);

    // Register the canvas mounted event
    RegistrationManager.registerEvent(EventType.CANVAS_MOUNTED, {
      sheetId: props.sheetId,
      hasNodes
    });

    // Mark the store as ready
    setStoreReady(true);

    // Set up a listener for node selection events from the store
    // This is needed because we removed the event dispatching from the store methods
    const unsubscribeFromNodeSelection = store.subscribe(
      (state) => state.selectedNodeId,
      (selectedNodeId) => {
        if (selectedNodeId) {
          console.log('MindMapCanvasWrapper: Node selected in store, dispatching event:', selectedNodeId);
          try {
            const event = new CustomEvent('mindback:node_selected', {
              detail: {
                nodeId: selectedNodeId,
                sheetId: props.sheetId
              }
            });
            document.dispatchEvent(event);
          } catch (error) {
            console.error('MindMapCanvasWrapper: Error dispatching node_selected event:', error);
          }
        }
      }
    );

    // Set up a listener for node changes in the store
    // This is needed to dispatch refresh events when nodes are added/updated
    const unsubscribeFromNodeChanges = store.subscribe(
      (state) => Object.keys(state.nodes).length,
      (nodeCount, prevNodeCount) => {
        if (nodeCount > prevNodeCount) {
          console.log('MindMapCanvasWrapper: Node added to store, dispatching refresh event');
          try {
            // Get the most recently added node
            const nodes = store.getState().nodes;
            const nodeIds = Object.keys(nodes);
            const latestNodeId = nodeIds[nodeIds.length - 1];

            // Dispatch a refresh event
            setTimeout(() => {
              const refreshEvent = new CustomEvent('mindback:refresh_canvas', {
                detail: {
                  nodeId: latestNodeId,
                  sheetId: props.sheetId
                }
              });
              document.dispatchEvent(refreshEvent);
              console.log('MindMapCanvasWrapper: Dispatched refresh_canvas event after adding node:', latestNodeId);
            }, 10);
          } catch (error) {
            console.error('MindMapCanvasWrapper: Error dispatching refresh event:', error);
          }
        }
      }
    );

    // Set up a listener for refresh events
    const handleRefreshCanvas = (event: CustomEvent) => {
      if (event.detail?.sheetId === props.sheetId || event.detail?.sheetId === 'current') {
        console.log('MindMapCanvasWrapper: Received refresh event for sheet:', props.sheetId);
        // Force a re-render by updating state
        setStoreReady(prev => !prev);
      }
    };

    // Set up a listener for center view events
    const handleCenterView = (event: CustomEvent) => {
      if (event.detail?.sheetId === props.sheetId) {
        console.log('MindMapCanvasWrapper: Received center view event for sheet:', props.sheetId);

        // Get the store state
        const storeState = store.getState();

        // Get window dimensions
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;

        // Center the view on the root node
        if (storeState.rootNodeId && storeState.nodes[storeState.rootNodeId]) {
          // Get the root node
          const rootNode = storeState.nodes[storeState.rootNodeId];

          // Calculate stage position to center the root node on screen
          // The stage position should move the stage so that the root node appears in the center
          const centerX = windowWidth / 2;
          const centerY = windowHeight / 2;
          
          // Stage position calculation: center - node position
          storeState.setPosition({
            x: centerX - rootNode.x,
            y: centerY - rootNode.y
          });

          // Select the root node
          storeState.selectNode(storeState.rootNodeId);

          console.log('MindMapCanvasWrapper: Centered view on root node for sheet:', props.sheetId);
        } else {
          // If no root node, reset to origin
          storeState.setPosition({
            x: 0,
            y: 0
          });

          console.log('MindMapCanvasWrapper: Reset position to origin for sheet:', props.sheetId);
        }

        // Force a re-render
        setStoreReady(prev => !prev);
      }
    };

    // Add event listeners
    document.addEventListener('mindback:refresh_canvas', handleRefreshCanvas as EventListener);
    document.addEventListener('mindback:center_view', handleCenterView as EventListener);

    return () => {
      console.log('MindMapCanvasWrapper: Unmounted for sheet:', props.sheetId);
      // Remove event listeners
      document.removeEventListener('mindback:refresh_canvas', handleRefreshCanvas as EventListener);
      document.removeEventListener('mindback:center_view', handleCenterView as EventListener);

      // Unsubscribe from store subscriptions
      if (unsubscribeFromNodeSelection) {
        unsubscribeFromNodeSelection();
      }
      if (unsubscribeFromNodeChanges) {
        unsubscribeFromNodeChanges();
      }
    };
  }, [props.sheetId, store]);

  // Pass down all props and store references to the MindMapCanvas component
  return (
    <MindMapCanvas
      {...props}
      store={store}
      key={`canvas-${props.sheetId}-${storeReady}`}
    />
  );
};

export default MindMapCanvasWrapper;
