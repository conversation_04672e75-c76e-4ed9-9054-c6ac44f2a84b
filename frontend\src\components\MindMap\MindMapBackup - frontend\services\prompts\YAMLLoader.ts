import * as yaml from 'js-yaml';

/**
 * Loads a YAML file directly as a string
 * This approach uses fetch instead of relying on Vite's import capabilities
 * Compatible with Vite 4.x
 * 
 * @param filePath - Path to the YAML file (relative to the public directory)
 * @returns Promise resolving to the parsed YAML data
 */
export async function loadYAMLFile<T>(filePath: string): Promise<T> {
  try {
    // Use fetch to load the file
    const response = await fetch(filePath);
    if (!response.ok) {
      throw new Error(`Failed to load YAML file: ${filePath}`);
    }
    
    const yamlText = await response.text();
    const parsedData = yaml.load(yamlText) as T;
    return parsedData;
  } catch (error) {
    console.error(`Error loading YAML file ${filePath}:`, error);
    throw error;
  }
}

/**
 * Loads a YAML file synchronously (for use in development only)
 * This is a fallback for when async loading is not practical
 * 
 * @param yamlContent - Raw YAML content as a string
 * @returns The parsed YAML data
 */
export function parseYAMLString<T>(yamlContent: string): T {
  try {
    return yaml.load(yamlContent) as T;
  } catch (error) {
    console.error('Error parsing YAML string:', error);
    throw error;
  }
} 