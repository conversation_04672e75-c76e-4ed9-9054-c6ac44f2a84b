import React, { useState, useEffect } from 'react';
import Switch from '@mui/material/Switch';
import FormControlLabel from '@mui/material/FormControlLabel';
import { Select, MenuItem, FormControl, InputLabel, IconButton, Popover, Typography, Slider, Tooltip, Box, Divider } from '@mui/material';
import SettingsIcon from '@mui/icons-material/Settings';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import RegistrationManager, { EventType } from '../../../core/services/RegistrationManager';
import { getAllIntentTypes } from '../../../core/config/intentTypes';
// Define a simplified Node interface locally to avoid import issues
interface Node {
  id: string;
  text: string;
  metadata?: {
    nodePath?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

// Define colors locally instead of importing
const NODE_COLORS = {
  SELECTED: '#2196f3', // Blue color for selected nodes
  DEFAULT: '#000000'   // Default text color
};

// Default node name to select
const DEFAULT_NODE_NAME = 'Gov';

type ModelOption = {
  value: string;
  label: string;
};

export interface ModelSelectorProps {
  useLiveLLM: boolean;
  onLiveLLMToggle: () => void;
  selectedModel: string;
  onModelChange: (model: string) => void;
  nodes?: Record<string, Node>; // Mind map nodes
  selectedNodeId?: string | null; // Currently selected node
  onNodeSelect?: (nodeId: string) => void; // Handler for node selection
  // New props for logging toggle
  showLogging?: boolean;
  onLoggingToggle?: () => void;
  // New props for CrewAI (keeping for backward compatibility)
  useCrewAI?: boolean;
  onCrewAIToggle?: () => void;
  crewSettings?: {
    maxIterations: number;
    temperature: number;
    verbose: boolean;
  };
  onCrewSettingsChange?: (settings: any) => void;
  // New props for template selection
  selectedContext?: string;
  onContextChange?: (context: string) => void;
  // New props for state machine
  isTransitioning?: boolean;
  isCircuitOpen?: boolean;
}

// Available model options
const modelOptions: ModelOption[] = [
  { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
  { value: 'gpt-4-turbo', label: 'GPT-4 Turbo' },
  { value: 'claude-3-sonnet', label: 'Claude 3 Sonnet' },
  { value: 'claude-3-opus', label: 'Claude 3 Opus' },
  { value: 'claude-3.5-sonnet', label: 'Claude 3.5 Sonnet' },
  { value: 'gemini-1.5-flash-8b', label: 'Gemini 1.5 Flash-8B' },
  { value: 'gemini-2.0-flash', label: 'Gemini 2.0 Flash' },
];

// Template options with nested structure
const templateOptions = [
  { value: 'agentic', label: 'Agentic' },
  { value: 'mindmap', label: 'Mindmap' },
  { value: 'chatfork', label: 'Chatfork' },
  { value: 'situational', label: 'Situational' },
  { value: 'backtrack', label: 'Backtrack' },
  {
    value: 'instantanious',
    label: 'Instantanious',
    subOptions: [
      { value: 'bmc', label: 'Business Model Canvas' },
      { value: 'swot', label: 'SWOT Analysis' },
      { value: 'triz', label: 'TRIZ Method' },
      { value: 'five_forces', label: 'Five Forces (Porter\'s Model)' },
      { value: 'pestel', label: 'PESTEL Analysis' },
      { value: 'ansoff', label: 'Ansoff Matrix' },
      { value: 'value_prop', label: 'Value Proposition Canvas' },
      { value: 'jtbd', label: 'Jobs to be Done (JTBD)' },
      { value: 'business_case', label: 'Business Case Template' },
      { value: 'raci', label: 'RACI Matrix' },
      { value: 'moscow', label: 'MoSCoW Prioritization' },
      { value: 'okr', label: 'OKRs (Objectives and Key Results)' },
      { value: 'persona', label: 'User Persona Canvas' }
    ]
  }
];

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  useLiveLLM,
  onLiveLLMToggle,
  selectedModel,
  onModelChange,
  nodes = {},
  selectedNodeId = null,
  onNodeSelect = () => {},
  // New prop defaults for logging toggle
  showLogging = true,
  onLoggingToggle = () => {},
  // New prop defaults for CrewAI (keeping for backward compatibility)
  useCrewAI = false,
  onCrewAIToggle = () => {},
  crewSettings = {
    maxIterations: 3,
    temperature: 0.7,
    verbose: true
  },
  onCrewSettingsChange = () => {},
  // New prop defaults for template selection
  selectedContext = 'agentic',
  onContextChange = () => {},
  // New prop defaults for state machine
  isTransitioning = false,
  isCircuitOpen = false
}) => {
  // State for settings popover
  const [settingsAnchorEl, setSettingsAnchorEl] = useState<HTMLButtonElement | null>(null);
  const settingsOpen = Boolean(settingsAnchorEl);

  // Convert nodes object to array and sort by nodePath
  const sortedNodes = Object.values(nodes)
    .filter(node => node.metadata?.nodePath) // Only include nodes with a path
    .sort((a, b) => {
      const pathA = a.metadata?.nodePath || '';
      const pathB = b.metadata?.nodePath || '';
      return pathA.localeCompare(pathB);
    });

  // Log nodes received for debugging
  React.useEffect(() => {
    console.log('ModelSelector received nodes:', Object.keys(nodes).length);
    console.log('ModelSelector sorted nodes:', sortedNodes.length);
  }, [nodes, sortedNodes.length]);

  // Find the Gov node if it exists
  const findGovNode = () => {
    const govNode = sortedNodes.find(node =>
      node.text === DEFAULT_NODE_NAME ||
      node.metadata?.nodePath?.includes(DEFAULT_NODE_NAME)
    );
    return govNode?.id || '';
  };

  // Handlers for CrewAI settings
  const handleSettingsClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setSettingsAnchorEl(event.currentTarget);
  };

  const handleSettingsClose = () => {
    setSettingsAnchorEl(null);
  };

  const handleMaxIterationsChange = (event: any, newValue: number | number[]) => {
    onCrewSettingsChange({
      ...crewSettings,
      maxIterations: newValue as number
    });
  };

  const handleTemperatureChange = (event: any, newValue: number | number[]) => {
    onCrewSettingsChange({
      ...crewSettings,
      temperature: newValue as number
    });
  };

  const handleVerboseToggle = () => {
    onCrewSettingsChange({
      ...crewSettings,
      verbose: !crewSettings.verbose
    });
  };

  const handleChange = (event: any) => {
    const newModel = event.target.value as string;

    // Register model selection event
    RegistrationManager.registerEvent(EventType.MODEL_SELECTED, {
      model: newModel,
      previousModel: selectedModel
    });

    onModelChange(newModel);
  };

  // Debug log for state changes
  useEffect(() => {
    console.log('selectedContext:', selectedContext);
  }, [selectedContext]);

  // Function to get the display label for the selected context
  const getContextLabel = () => {
    // Check if it's a sub-option from Instantanious
    const instantaniousOption = templateOptions.find(option => option.value === 'instantanious');
    if (instantaniousOption && instantaniousOption.subOptions) {
      const subOption = instantaniousOption.subOptions.find(sub => sub.value === selectedContext);
      if (subOption) {
        return subOption.label;
      }
    }

    // Otherwise, find the main intention
    const intention = templateOptions.find(i => i.value === selectedContext);
    return intention ? intention.label : selectedContext;
  };

  // Handle intention change
  const handleContextChange = (event: any) => {
    const value = event.target.value as string;
    console.log('Selected value:', value);

    // Call the parent handler with the selected value
    if (onContextChange) {
      onContextChange(value);
    }
  };

  // Simple intention change handler
  const handleIntentionChange = (event: any) => {
    const value = event.target.value as string;
    console.log('Intention selected:', value);

    // Register intention selection event
    RegistrationManager.registerEvent(EventType.INTENTION_SELECTED, {
      name: value,
      previousContext: selectedContext
    });

    // Call the parent handler with the selected value
    if (onContextChange) {
      onContextChange(value);
    }
  };

  return (
    <div className="model-selector">
      <div className="model-section">
        <FormControl variant="outlined" size="small">
          <InputLabel id="model-select-label">Model</InputLabel>
          <Select
            labelId="model-select-label"
            id="model-select"
            value={selectedModel}
            onChange={handleChange}
            label="Model"
            MenuProps={{
              sx: { zIndex: 2200 } // Ensure the dropdown menu appears above everything
            }}
          >
            <MenuItem value="gpt-3.5-turbo">GPT-3.5 Turbo</MenuItem>
            <MenuItem value="gpt-4">GPT-4</MenuItem>
            <MenuItem value="gpt-4-turbo">GPT-4 Turbo</MenuItem>
            <MenuItem value="gpt-4o-mini">GPT-4o-mini</MenuItem>
            <MenuItem value="claude-3-opus">Claude 3 Opus</MenuItem>
            <MenuItem value="claude-3-sonnet">Claude 3 Sonnet</MenuItem>
            <MenuItem value="claude-3.5-sonnet">Claude 3.5 Sonnet</MenuItem>
            <MenuItem value="gemini-1.5-flash-8b">Gemini 1.5 Flash-8B</MenuItem>
            <MenuItem value="gemini-2.0-flash">Gemini 2.0 Flash</MenuItem>
          </Select>
        </FormControl>
      </div>

      {/* Intention selection box */}
      <div className="context-section">
        <FormControl variant="outlined" size="small">
          <InputLabel id="intention-select-label">
            {isTransitioning ? 'Processing...' : 'Intention'}
          </InputLabel>
          <Select
            labelId="intention-select-label"
            id="intention-select"
            value={selectedContext}
            renderValue={() => isTransitioning ? '⏳ Processing...' : getContextLabel()}
            onChange={handleIntentionChange}
            label={isTransitioning ? 'Processing...' : 'Intention'}
            defaultValue="agentic"
            disabled={isTransitioning || isCircuitOpen}
            sx={{
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: isCircuitOpen ? 'red' : (isTransitioning ? 'orange' : 'inherit')
              },
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: isCircuitOpen ? 'red' : (isTransitioning ? 'orange' : 'inherit')
              }
            }}
            MenuProps={{
              sx: { zIndex: 9999 } // Ensure the dropdown menu appears above everything
            }}
          >
            {/* Intent types from configuration */}
            {getAllIntentTypes().map(intent => (
              <MenuItem key={intent.id} value={intent.id}>
                {intent.displayName}
              </MenuItem>
            ))}

            {/* Additional UI-specific options */}
            <MenuItem value="agentic">Agentic</MenuItem>
            <MenuItem value="backtrack">Backtrack</MenuItem>

            {/* Instantanious with sub-options */}
            <MenuItem
              value="instantanious"
              sx={{
                fontWeight: 'bold',
                display: 'flex',
                justifyContent: 'space-between',
                borderBottom: '1px dashed #ccc'
              }}
            >
              Instantanious
              <KeyboardArrowRightIcon fontSize="small" sx={{ ml: 1, color: '#666' }} />
            </MenuItem>

            {/* Sub-options with indentation */}
            {templateOptions.find(o => o.value === 'instantanious')?.subOptions?.map(subOption => (
              <MenuItem
                key={subOption.value}
                value={subOption.value}
                sx={{
                  pl: 4,  // Indent sub-options
                  fontSize: '13px',
                  color: '#555'
                }}
              >
                {subOption.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </div>

      <div className="toggle-container">
        <FormControlLabel
          control={
            <Switch
              checked={useLiveLLM}
              onChange={onLiveLLMToggle}
              name="liveLLM"
            />
          }
          label={useLiveLLM ? "Live LLM" : "Mock LLM"}
          className={useLiveLLM ? "live" : "mock"}
        />

        {/* Logging Toggle - Grey color */}
        <FormControlLabel
          control={
            <Switch
              checked={showLogging}
              onChange={onLoggingToggle}
              name="showLogging"
              color="default" // Changed from "secondary" to "default" for grey color
              sx={{
                '& .MuiSwitch-switchBase.Mui-checked': {
                  color: '#9e9e9e', // Grey color when checked
                },
                '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                  backgroundColor: '#9e9e9e', // Grey track when checked
                },
              }}
            />
          }
          label="Show Logging"
          className={showLogging ? "logging-active" : "logging-inactive"}
        />


      </div>

      {/* Always show node section if there are any nodes */}
      {nodes && onNodeSelect && Object.keys(nodes).length > 0 && (
        <div className="node-section">
          <FormControl variant="outlined" size="small" sx={{ minWidth: 200, marginLeft: 1 }}>
            <InputLabel id="node-select-label">Select Node</InputLabel>
            <Select
              labelId="node-select-label"
              id="node-select"
              value={selectedNodeId || ''}
              onChange={(e) => onNodeSelect(e.target.value as string)}
              label="Select Node"
              MenuProps={{
                sx: {
                  zIndex: 2200,
                  '& .MuiMenu-paper': {
                    maxHeight: 300
                  }
                }
              }}
            >
              {/* Add None option */}
              <MenuItem value="">
                <em>None</em>
              </MenuItem>
              {/* Use all nodes regardless of metadata */}
              {Object.values(nodes).map((node) => (
                <MenuItem key={node.id} value={node.id} sx={{
                  '&.Mui-selected': {
                    backgroundColor: 'rgba(25, 118, 210, 0.12)'
                  }
                }}>
                  {node.metadata?.nodePath || node.text || node.id}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </div>
      )}
    </div>
  );
};