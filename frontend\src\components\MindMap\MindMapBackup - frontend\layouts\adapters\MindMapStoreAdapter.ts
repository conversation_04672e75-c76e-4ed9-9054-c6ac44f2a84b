import { Connection } from '../../core/models/Connection';
import { Node } from '../../core/models/Node';
import { useMindMapStore } from '../../core/state/MindMapStore';
import { DEFAULT_LAYOUT_CONFIG, LayoutConfig, LayoutStrategyType } from '../types';
import { LayoutManager } from '../LayoutManager';

/**
 * Adapter for interfacing between MindMapStore and the new Layout System
 * This allows the new layout system to be used without modifying existing code
 */
export class MindMapStoreAdapter {
  private layoutManager: LayoutManager;
  
  constructor() {
    this.layoutManager = new LayoutManager();
  }
  
  /**
   * Apply a layout strategy to the current MindMap
   * 
   * Available strategies:
   * - leftToRight: Standard horizontal tree layout
   * - topDown: Vertical tree layout from top to bottom
   * - bottomUp: Vertical tree layout from bottom to top
   * - radial: Circular layout with root in center
   * - compactLeftToRight: NEW! Optimized compact layout with reduced whitespace
   */
  applyLayoutToStore(
    strategyType: LayoutStrategyType = 'leftToRight',
    config: LayoutConfig = DEFAULT_LAYOUT_CONFIG
  ): void {
    const store = useMindMapStore.getState();
    const { nodes, connections, rootNodeId } = store;
    
    if (!rootNodeId) {
      console.warn('No root node found in MindMapStore');
      return;
    }
    
    // Only process nodes that are not stabilized
    const nodesToLayout: Record<string, Node> = {};
    const stabilizedNodes: Record<string, Node> = {};
    
    // Separate stabilized and non-stabilized nodes
    Object.entries(nodes).forEach(([id, node]) => {
      if ((node as Node).metadata?.positionsStabilized) {
        stabilizedNodes[id] = node as Node;
      } else {
        nodesToLayout[id] = node as Node;
      }
    });
    
    // If all nodes are stabilized, we should skip layout
    if (Object.keys(nodesToLayout).length === 0) {
      console.log('All nodes have stabilized positions, skipping layout');
      return;
    }
    
    try {
      // Apply layout to non-stabilized nodes
      const layoutedNodes = this.layoutManager.applyLayout(
        nodesToLayout,
        connections,
        rootNodeId,
        strategyType,
        config
      );
      
      // Merge layouted nodes with stabilized nodes
      const finalNodes = {
        ...layoutedNodes,
        ...stabilizedNodes
      };
      
      // Update the store with the new node positions
      useMindMapStore.setState({
        nodes: finalNodes
      });
      
      console.log(`Successfully applied ${strategyType} layout to MindMap`);
    } catch (error) {
      console.error('Error applying layout to MindMapStore:', error);
    }
  }
  
  /**
   * Get available layout strategies
   */
  getAvailableStrategies(): LayoutStrategyType[] {
    return this.layoutManager.getAvailableStrategies();
  }
}

/**
 * Factory function to create a new MindMapStoreAdapter instance
 */
export function createMindMapStoreAdapter(): MindMapStoreAdapter {
  return new MindMapStoreAdapter();
} 