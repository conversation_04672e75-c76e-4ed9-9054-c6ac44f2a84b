from fastapi import APIRouter, HTTPException, Body, Depends
from pydantic import BaseModel, Field, validator
import yaml
import os
from typing import Optional, Dict, Any, List, Union
import logging
from ..services.openai_service import get_openai_client, generate_openai_chat_completion
from ..config.settings import get_settings
import json
import sys
import re
from pathlib import Path

# Add import for the instantiation_template_router
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from Prompt_library.instantiation_template_router import instantiation_template_router

# Get the existing logger instead of creating a new one
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/llm", tags=["llm"])

# Path to prompt library
PROMPT_LIBRARY_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "Prompt_library")

# Constants for intent types
INTENT_TYPES = ["factual", "exploratory", "teleological", "instantiation", "miscellaneous"]
AGENT_TYPES = ["blue_hat", "white_hat", "red_hat", "black_hat", "yellow_hat", "green_hat"]

# Pydantic models
class ActionModel(BaseModel):
    title: str = Field(..., description="Short name for the action")
    owner: str = Field(..., description="Responsible person")
    due_date: str = Field(..., description="Deadline in YYYY-MM-DD format")
    system: Optional[str] = Field(None, description="System to send action to")
    status: str = Field(..., description="Action status")

    @validator('status')
    def validate_status(cls, v):
        if v not in ["pending", "in_progress", "done"]:
            raise ValueError(f"Invalid status value: {v}")
        return v

class MetadataModel(BaseModel):
    intent: str = Field(..., description="Node intent classification")
    agent: Optional[str] = Field(None, description="Thinking hat perspective")
    tags: List[str] = Field(default_factory=list, description="Node tags")
    action: Optional[ActionModel] = Field(None, description="Action item details")

    @validator('intent')
    def validate_intent(cls, v):
        if v not in INTENT_TYPES:
            raise ValueError(f"Invalid intent value: {v}")
        return v

    @validator('agent')
    def validate_agent(cls, v):
        if v and v not in AGENT_TYPES:
            raise ValueError(f"Invalid agent value: {v}")
        return v

class NodeModel(BaseModel):
    id: str = Field(..., description="Unique node identifier")
    text: str = Field(..., description="Node title text")
    description: str = Field(..., description="Node detailed description")
    children: List['NodeModel'] = Field(default_factory=list, description="Child nodes")
    metadata: MetadataModel = Field(..., description="Node metadata")

NodeModel.update_forward_refs()

class LLMChatRequest(BaseModel):
    # Support both naming conventions to handle frontend requests
    prompt: str
    # Traditional fields
    messages: Optional[List[Dict[str, str]]] = None
    # New fields from frontend
    system_prompt: Optional[str] = None
    prompt_type: str = "default"
    # Optional topic field
    topic: Optional[str] = None
    # Model and settings
    model: str = "gpt-3.5-turbo"
    temperature: float = 0.7
    # Intent specification
    intent: Optional[str] = Field(None, description="Specify the intent type for the response")

    @validator('intent')
    def validate_intent(cls, v):
        if v and v not in INTENT_TYPES:
            raise ValueError(f"Invalid intent value: {v}")
        return v

class LLMChatResponse(BaseModel):
    success: bool = True
    content: Dict[str, Any]  # Changed from str to Dict to ensure proper JSON structure
    model: Optional[str] = None
    usage: Optional[Dict[str, int]] = None
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    validation_info: Optional[Dict[str, Any]] = None

# Helper functions
def load_yaml_prompt(prompt_type: str) -> Dict[str, Any]:
    """
    Load a YAML prompt file from the Prompt_library directory
    """
    try:
        # Log which file we're trying to load
        logger.info(f"Attempting to load prompt file for type: '{prompt_type}'")
        
        # Check for both .yaml and .yml extensions
        for ext in [".yaml", ".yml"]:
            file_path = os.path.join(PROMPT_LIBRARY_PATH, f"{prompt_type}{ext}")
            if os.path.exists(file_path):
                logger.info(f"Found prompt file at: {file_path}")
                try:
                    with open(file_path, 'r', encoding='utf-8') as file:
                        file_content = file.read()
                        logger.debug(f"File content preview: {file_content[:100]}...")
                        try:
                            prompt_data = yaml.safe_load(file_content)
                            logger.info(f"Successfully loaded and parsed YAML from {file_path}")
                            return prompt_data
                        except yaml.YAMLError as ye:
                            # More detailed YAML parsing error
                            logger.error(f"YAML parsing error in {file_path}: {str(ye)}")
                            # Try to read the problematic line
                            if hasattr(ye, 'problem_mark'):
                                mark = ye.problem_mark
                                line_num = mark.line + 1
                                column = mark.column + 1
                                logger.error(f"Error position: line {line_num}, column {column}")
                                # Try to show the problematic line
                                try:
                                    with open(file_path, 'r', encoding='utf-8') as f:
                                        lines = f.readlines()
                                        if line_num <= len(lines):
                                            logger.error(f"Problematic line: {lines[line_num-1].rstrip()}")
                                except Exception as line_err:
                                    logger.error(f"Could not read problematic line: {str(line_err)}")
                            raise
                except Exception as file_err:
                    logger.error(f"Error reading file {file_path}: {str(file_err)}")
                    raise
                    
        # Also check for system prompts
        for ext in [".yaml", ".yml"]:
            file_path = os.path.join(PROMPT_LIBRARY_PATH, f"system_{prompt_type}{ext}")
            if os.path.exists(file_path):
                logger.info(f"Found system prompt file at: {file_path}")
                try:
                    with open(file_path, 'r', encoding='utf-8') as file:
                        system_prompt = yaml.safe_load(file)
                        
                    # Try to load the content prompt
                    content_path = os.path.join(PROMPT_LIBRARY_PATH, f"{prompt_type}{ext}")
                    if os.path.exists(content_path):
                        logger.info(f"Found matching content prompt at: {content_path}")
                        with open(content_path, 'r', encoding='utf-8') as content_file:
                            content_prompt = yaml.safe_load(content_file)
                            # Merge the system and content prompts
                            return {
                                "system_role": system_prompt.get("system_role", ""),
                                "content": content_prompt.get("content", ""),
                                "guidelines": content_prompt.get("guidelines", []) + system_prompt.get("guidelines", []),
                                "topics": content_prompt.get("topics", {})
                            }
                        
                    return system_prompt
                except yaml.YAMLError as ye:
                    logger.error(f"YAML parsing error in {file_path}: {str(ye)}")
                    if hasattr(ye, 'problem_mark'):
                        mark = ye.problem_mark
                        line_num = mark.line + 1
                        column = mark.column + 1
                        logger.error(f"Error position: line {line_num}, column {column}")
                    raise
                except Exception as e:
                    logger.error(f"Error reading system prompt file {file_path}: {str(e)}")
                    raise
                
        # If we reach here, prompt file not found
        logger.warning(f"Prompt file for type '{prompt_type}' not found")
        return {
            "system_role": "",
            "content": "",
            "guidelines": []
        }
    except Exception as e:
        logger.error(f"Error loading prompt file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error loading prompt file: {str(e)}")

def format_prompt_with_values(prompt_data: Dict[str, Any], values: Dict[str, str]) -> Dict[str, str]:
    """
    Format a prompt by replacing placeholders with values
    """
    try:
        # Deep copy to avoid modifying the original
        formatted = dict(prompt_data)
        
        # Format system role and content
        if "system_role" in formatted and formatted["system_role"]:
            for key, value in values.items():
                formatted["system_role"] = formatted["system_role"].replace(f"{{{key}}}", value)
        
        if "content" in formatted and formatted["content"]:
            for key, value in values.items():
                formatted["content"] = formatted["content"].replace(f"{{{key}}}", value)
                
        return formatted
    except Exception as e:
        logger.error(f"Error formatting prompt: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error formatting prompt: {str(e)}")

# Endpoints
@router.post("/chat", response_model=LLMChatResponse)
async def llm_chat(
    request: LLMChatRequest,
    openai_client = Depends(get_openai_client)
):
    """
    Generate a response from the LLM using YAML prompts with function calling to ensure MBCP structure
    """
    try:
        logger.info(f"Received chat request with prompt_type: {request.prompt_type}")
        logger.info(f"Intent: {request.intent}")
        logger.info(f"Prompt: {request.prompt[:100]}...")
    except Exception as e:
        logger.error(f"Error processing chat request: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing chat request: {str(e)}") 