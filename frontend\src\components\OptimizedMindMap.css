/* Mind Map Styles */
.optimized-mind-map {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* App Header */
.app-header {
  background-color: #000000; /* Changed from #1e88e5 to black */
  color: white;
  padding: 0 20px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between; /* Adjusted for toolbar alignment */
  z-index: 20;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.logo {
  font-size: 18px;
  font-weight: normal; /* Non-bold Arial */
  font-family: Arial, sans-serif;
  margin-right: 15px;
}

.subtitle {
  font-size: 14px;
  opacity: 0.8;
}

/* MindSheet Tabs Container - DEPRECATED, use .mindsheet-tabs instead */
.mindsheet-tabs-container {
  display: none; /* Hide this container as we're using .mindsheet-tabs instead */
}

.sheet-controls {
  display: flex;
  gap: 8px;
}

.sheet-control-button {
  padding: 5px 12px;
  background-color: #f1f1f1;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 13px;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s;
}

.sheet-control-button:hover {
  background-color: #e0e0e0;
}

/* Toolbar */
.toolbar {
  display: flex;
  gap: 10px;
}

.toolbar-icon {
  width: 24px;
  height: 24px;
  background-color: transparent;
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.toolbar-icon:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Main Content */
.main-content {
  display: flex;
  flex: 1;
  height: calc(100vh - 40px);
  overflow: hidden;
}

/* Canvas Container */
.canvas-container {
  flex: 1;
  height: 100%;
  position: relative;
}

/* Chat Container */
.chat-container {
  display: none;
}

/* Adjust MindMapCanvas for chat area */
.mind-map-container {
  position: absolute;
  top: 40px; /* Below the header */
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  background-color: #f8fafc;
  z-index: 10; /* Higher z-index to ensure it's visible */
  display: block !important; /* Force display */
}

.mind-map {
  position: absolute;
  transform-origin: 0 0;
  min-width: 100%;
  min-height: 100%;
}

/* Nodes */
.node {
  position: absolute;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  user-select: none;
  overflow: hidden;
  padding: 5px;
  text-align: center;
  font-size: 14px;
  color: white;
  font-weight: bold;
  z-index: 2;
  transition: box-shadow 0.2s, transform 0.2s;
}

.node:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

.node.selected {
  box-shadow: 0 0 0 2px #000, 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Connection Lines */
.connection-line {
  position: absolute;
  height: 2px;
  background-color: #adb5bd;
  transform-origin: 0 0;
  z-index: 1;
}

/* Dialogs */
.node-dialog, .project-list-dialog, .design-controls-dialog {
  position: absolute;
  background-color: white;
  padding: 20px;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  color: #343a40;
}

.close-button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #adb5bd;
}

.close-button:hover {
  color: #343a40;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #495057;
}

.form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.form-group input[type="color"] {
  height: 40px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.dialog-footer button {
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-left: 10px;
}

.dialog-footer button:first-child {
  background-color: #e9ecef;
  color: #495057;
}

.dialog-footer button:last-child {
  background-color: #4dabf7;
  color: white;
}

/* Project List */
.project-list {
  max-height: 400px;
  overflow-y: auto;
}

.project-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.project-name {
  font-weight: bold;
  color: #343a40;
}

.project-date {
  font-size: 12px;
  color: #6c757d;
}

.project-actions {
  display: flex;
}

/* Design Controls */
.direction-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin: 20px 0;
}

.direction-button {
  padding: 10px;
  border: 1px solid #dee2e6;
  border-radius: 5px;
  background-color: #fff;
  cursor: pointer;
}

.direction-button.selected {
  border: 2px solid #4dabf7;
  background-color: #e7f5ff;
}

.color-scheme {
  margin-top: 20px;
}

.color-scheme h4 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #343a40;
}

.color-buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 5px;
}

.color-button {
  width: 30px;
  height: 30px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.direction-control {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.direction-label {
  margin-right: 5px;
  font-size: 14px;
  color: #343a40;
}

.direction-arrow {
  cursor: pointer;
}

/* Debug styles */
.debug-border {
  border: 1px solid red;
}

/* Control Buttons Bar */
.control-buttons-bar {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 1500; /* Higher z-index to ensure visibility */
  pointer-events: auto; /* Ensure clicks are registered */
}

/* Control Buttons */
.control-button {
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 4px; /* More rectangular shape */
  padding: 12px 20px;
  font-weight: 600; /* Bolder text */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  text-transform: uppercase; /* More prominent text */
  letter-spacing: 0.5px;
}

.control-button:hover {
  background-color: #1976D2;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px); /* Slight lift effect on hover */
}

/* Specific styling for the MindMap Manager button */
.manager-button {
  background-color: #3b82f6; /* Different color to stand out */
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 2000; /* Even higher z-index to ensure it's always on top */
}

.governance-button {
  background-color: #2196F3;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  z-index: 1200; /* Ensure it's above other elements */
}

.manager-button {
  background-color: #4CAF50; /* Green color for manager button */
}

.manager-button:hover {
  background-color: #388E3C;
}

/* Governance Chat Toggle Button - keep for backwards compatibility */
.governance-chat-toggle {
  position: fixed;
  bottom: 80px; /* Moved up to avoid overlap with MindMap Manager button */
  right: 20px;
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 30px;
  padding: 12px 20px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  z-index: 1500; /* Higher z-index to ensure visibility */
  transition: all 0.2s ease;
}

.governance-chat-toggle:hover {
  background-color: #1976D2;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* ChatFork Dialog Styles */
.chatfork-dialog {
  z-index: 1000;
}

.chatfork-dialog-header {
  cursor: move;
}

/* Ensure the ChatFork dialog appears above standard dialogs but below GovernanceBox */
.chatfork-container {
  z-index: var(--z-index-chatfork); /* Using CSS variable for z-index */
}

/* Ensure the minimized ChatFork appears above other elements */
.minimized-chatfork {
  z-index: var(--z-index-chatfork); /* Using CSS variable for z-index */
}