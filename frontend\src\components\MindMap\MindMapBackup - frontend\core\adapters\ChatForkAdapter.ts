/**
 * ChatForkAdapter provides integration between UI components and ChatForkStore
 * Only manages ChatFork components state, not GovernanceChat
 *
 * This adapter has been enhanced to support the independent ChatFork workflow
 * with minimization and position management.
 */
import { useChatForkStore } from '../state/ChatForkStore';
import { ChatResponse } from '../../../../services/api/GovernanceLLM';
import { useMindMapStore } from '../state/MindMapStore';

export class ChatForkAdapter {
  /**
   * Handle actions coming from the governance chat
   * This should NOT affect GovernanceChat visibility
   */
  static handleChatForkAction(action: any): boolean {
    console.log('ChatForkAdapter - Handling action:', action);

    if (action.type === 'show_chatfork' || action.type === 'explore_topic') {
      // Extract intent data using multiple possible paths for robustness
      const intent = action.data?.intent ||
                     action.data?.responseType?.type ||
                     action.data?.content?.intent;

      console.log('ChatForkAdapter - Detected intent:', intent);

      // Check if the action contains the required fields for ChatFork
      const hasChatForkData = action.data?.full_text ||
                             action.data?.content?.full_text ||
                             action.data?.templateOutput?.full_text;

      // Check if the action contains UI labels for ChatFork
      const hasChatForkUI = action.data?.ui_labels?.chatfork_button ||
                           action.data?.content?.ui_labels?.chatfork_button;

      // Process based on data structure, not intent
      if (hasChatForkData || hasChatForkUI) {
        try {
          console.log('ChatForkAdapter - Processing action based on data structure');

          // Process the MBCP content to ensure it has the expected structure
          const mbcpContent = action.data.templateOutput || action.data.content || action.data.mindmap || action.data;

          // Create proper ChatResponse object
          const chatResponse: ChatResponse = {
            text: action.data.title || action.data.text || action.data.topic || 'Exploration',
            responseType: {
              type: 'exploratory',
              requiresMindmap: false,
              requiresChatFork: true
            },
            suggestedActions: [],
            // Add full_text directly to the chatResponse object
            full_text: mbcpContent.full_text || mbcpContent.description ||
                      (mbcpContent.root?.description ? mbcpContent.root.description : null) ||
                      'No detailed content available for this topic.',
            // Add root_topic directly to the chatResponse object
            root_topic: action.data.title || action.data.text || action.data.topic || 'Exploration',
            templateOutput: {
              // Pass through the original MBCP content
              ...mbcpContent
            }
          };

          // Close any existing popups with the same content
          document.querySelectorAll('.democracy-popup').forEach(el => {
            (el as HTMLElement).style.display = 'none';
          });

          console.log('ChatForkAdapter - ChatResponse for ChatFork:', chatResponse);
          console.log('ChatForkAdapter - Title:', chatResponse.text);
          console.log('ChatForkAdapter - TemplateOutput:', chatResponse.templateOutput);

          // Show the ChatFork in the store - this should update all subscribed components
          // IMPORTANT: This doesn't affect GovernanceChat, only ChatForkView
          useChatForkStore.setState({
            content: chatResponse,
            isVisible: true,
            isMinimized: false, // Ensure it's not minimized when first shown
            selectedText: null,
            selectionContext: null
          });

          // When showing ChatFork, dispatch event to minimize GovernanceChat
          window.dispatchEvent(new CustomEvent('minimize-governance-chat'));

          // Debug: Log the current state after update
          setTimeout(() => {
            const state = useChatForkStore.getState();
            console.log('ChatForkAdapter - State after update:', {
              isVisible: state.isVisible,
              isMinimized: state.isMinimized,
              hasContent: !!state.content,
              contentTitle: state.content?.text
            });
          }, 100);

          return true;
        } catch (error) {
          console.error('ChatForkAdapter - Error handling action:', error);
          return false;
        }
      } else {
        console.log('ChatForkAdapter - Required ChatFork data not found, not handling');
      }
    } else {
      console.log('ChatForkAdapter - Action is not show_chatfork or explore_topic, not handling');
    }

    // If we get here, action was not handled by this adapter
    return false;
  }

  /**
   * Set the selected text and context in the store
   */
  static setSelectedText(text: string, context: string): void {
    console.log('ChatForkAdapter - Setting selected text:', text);
    useChatForkStore.setState({
      selectedText: text,
      selectionContext: context
    });
  }

  /**
   * Clear the selected text
   */
  static clearSelection(): void {
    console.log('ChatForkAdapter - Clearing selection');
    useChatForkStore.setState({
      selectedText: null,
      selectionContext: null
    });
  }

  /**
   * Hide the ChatFork
   * This only affects ChatFork visibility, not GovernanceChat
   */
  static hideChatFork(): void {
    console.log('ChatForkAdapter - Hiding ChatFork');
    useChatForkStore.setState({
      isVisible: false,
      isMinimized: false,
      content: null,
      selectedText: null,
      selectionContext: null
    });
  }

  /**
   * Minimize the ChatFork dialog
   */
  static minimizeChatFork(): void {
    console.log('ChatForkAdapter - Minimizing ChatFork');
    useChatForkStore.setState({ isMinimized: true });
  }

  /**
   * Maximize the ChatFork dialog
   */
  static maximizeChatFork(): void {
    console.log('ChatForkAdapter - Maximizing ChatFork');
    useChatForkStore.setState({ isMinimized: false });
  }

  /**
   * Toggle the minimized state of the ChatFork dialog
   */
  static toggleMinimized(): void {
    const { isMinimized } = useChatForkStore.getState();
    console.log(`ChatForkAdapter - Toggling minimized state from ${isMinimized} to ${!isMinimized}`);
    useChatForkStore.setState({ isMinimized: !isMinimized });
  }

  /**
   * Set the position of the ChatFork dialog
   */
  static setPosition(x: number, y: number): void {
    useChatForkStore.setState({ position: { x, y } });
  }

  /**
   * Create a fork node from the selected text
   */
  static createForkFromSelection(): string | null {
    const { selectedText, selectionContext, content } = useChatForkStore.getState();

    console.log('ChatForkAdapter - Creating fork from selection:', {
      selectedText,
      contentTitle: content?.text
    });

    if (!selectedText || !content) {
      console.warn('ChatForkAdapter - Cannot create fork: missing text or content');
      return null;
    }

    // Get store actions
    const { addNode, addEdge, getNextNodeId, setSelectedNodeId } = useMindMapStore.getState();

    // Generate a unique ID for the new node
    const newNodeId = getNextNodeId();

    // Create a new node from the selected text
    const newNode = {
      id: newNodeId,
      title: selectedText.length > 30 ? selectedText.substring(0, 30) + '...' : selectedText,
      text: selectedText,
      intent: 'exploratory', // Mark as exploratory
      source: content.text,
      x: 200,
      y: 200,
      width: 180,
      height: 100
    };

    console.log('ChatForkAdapter - Adding node to mindmap:', newNode);

    // Add the node to the mindmap
    addNode(newNode);

    // Clear the selection after creating the fork
    useChatForkStore.setState({
      selectedText: null,
      selectionContext: null
    });

    // Select the new node
    setSelectedNodeId(newNodeId);

    return newNodeId;
  }
}