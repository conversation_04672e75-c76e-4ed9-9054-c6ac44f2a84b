/**
 * ConnectionComponent.tsx
 *
 * Component for rendering a connection between nodes in the mind map.
 * This component ensures connections stay attached to nodes when they move.
 */

import React, { useEffect, useState } from 'react';
import { Line, Group } from 'react-konva';
import { Connection, Node } from '../../../../core/types/MindMapTypes';
import { useMindMapStore } from '../../../../core/state/MindMapStore';

interface ConnectionComponentProps {
  connection: Connection;
  fromNode: Node;
  toNode: Node;
}

// Helper function to calculate connection points
const calculateConnectionPoints = (fromNode: Node, toNode: Node) => {
  // Calculate center points of nodes
  const fromX = fromNode.x + fromNode.width / 2;
  const fromY = fromNode.y + fromNode.height / 2;
  const toX = toNode.x + toNode.width / 2;
  const toY = toNode.y + toNode.height / 2;

  // Calculate the angle between nodes
  const angle = Math.atan2(toY - fromY, toX - fromX);

  // Calculate the points where the line should start and end
  // This ensures the line starts and ends at the edge of the nodes
  const fromNodeRadius = Math.min(fromNode.width, fromNode.height) / 2;
  const toNodeRadius = Math.min(toNode.width, toNode.height) / 2;

  const startX = fromX + Math.cos(angle) * fromNodeRadius;
  const startY = fromY + Math.sin(angle) * fromNodeRadius;
  const endX = toX - Math.cos(angle) * toNodeRadius;
  const endY = toY - Math.sin(angle) * toNodeRadius;

  return { startX, startY, endX, endY };
};

const ConnectionComponent: React.FC<ConnectionComponentProps> = ({
  connection,
  fromNode,
  toNode
}) => {
  // Skip rendering if either node is missing
  if (!fromNode || !toNode) {
    return null;
  }

  // Use state to track connection points
  const [points, setPoints] = useState(() => calculateConnectionPoints(fromNode, toNode));

  // Update connection points when nodes move
  useEffect(() => {
    const newPoints = calculateConnectionPoints(fromNode, toNode);
    setPoints(newPoints);
  }, [fromNode.x, fromNode.y, toNode.x, toNode.y, fromNode.width, fromNode.height, toNode.width, toNode.height]);

  // Destructure points for clarity
  const { startX, startY, endX, endY } = points;

  // Determine line dash based on style
  let dash;
  switch (connection.style) {
    case 'dashed':
      dash = [10, 5];
      break;
    case 'dotted':
      dash = [2, 2];
      break;
    case 'solid':
    default:
      dash = [];
      break;
  }

  // Get the selected connection ID directly from the store
  const selectedConnectionId = useMindMapStore.getState().selectedConnectionId;

  // Determine if this connection is selected
  const isSelected = connection.id === selectedConnectionId;

  // Handle click to select the connection
  const handleClick = () => {
    useMindMapStore.getState().selectConnection(connection.id);
  };

  // Add a debug log to verify connection points are updating
  useEffect(() => {
    console.log(`Connection ${connection.id} points updated:`, {
      from: { id: fromNode.id, x: fromNode.x, y: fromNode.y },
      to: { id: toNode.id, x: toNode.x, y: toNode.y },
      points: { startX, startY, endX, endY }
    });
  }, [connection.id, fromNode.id, toNode.id, startX, startY, endX, endY]);

  return (
    <Group onClick={handleClick}>
      <Line
        points={[startX, startY, endX, endY]}
        stroke={isSelected ? '#3498db' : (connection.color || '#2c3e50')}
        strokeWidth={isSelected ? (connection.width || 2) + 1 : (connection.width || 2)}
        dash={dash}
        lineCap="round"
        lineJoin="round"
        shadowColor={isSelected ? '#3498db' : undefined}
        shadowBlur={isSelected ? 5 : 0}
        shadowOffset={{ x: 0, y: 0 }}
        shadowOpacity={0.5}
        listening={true} // Ensure the line responds to events
      />
    </Group>
  );
};

export default ConnectionComponent;
