import { Node, Connection, Direction } from '../types';

interface MindMapConfig {
  nodeWidth: number;
  nodeHeight: number;
  horizontalSpacing: number;
  verticalSpacing: number;
  levelSpacing: number;
}

const DEFAULT_CONFIG: MindMapConfig = {
  nodeWidth: 140,
  nodeHeight: 60,
  horizontalSpacing: 120,
  verticalSpacing: 60,
  levelSpacing: 200
};

export class MindMapCore {
  private nodes: Map<string, Node>;
  private connections: Map<string, Connection>;
  private config: MindMapConfig;
  private rootId: string | null;

  constructor(config: Partial<MindMapConfig> = {}) {
    this.nodes = new Map();
    this.connections = new Map();
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.rootId = null;
  }

  public addNode(node: Node): void {
    this.nodes.set(node.id, node);
    if (!this.rootId) {
      this.rootId = node.id;
    }
  }

  public addConnection(connection: Connection): void {
    const id = `${connection.from}-${connection.to}`;
    this.connections.set(id, connection);
  }

  public getNode(id: string): Node | undefined {
    return this.nodes.get(id);
  }

  public getNodes(): Node[] {
    return Array.from(this.nodes.values());
  }

  public getConnections(): Connection[] {
    return Array.from(this.connections.values());
  }

  public updateNode(id: string, updates: Partial<Node>): void {
    const node = this.nodes.get(id);
    if (node) {
      this.nodes.set(id, { ...node, ...updates });
    }
  }

  public deleteNode(id: string): void {
    // Delete node and its connections
    this.nodes.delete(id);
    const connectionsToDelete = Array.from(this.connections.entries())
      .filter(([_, conn]) => conn.from === id || conn.to === id)
      .map(([connId]) => connId);
    
    connectionsToDelete.forEach(connId => this.connections.delete(connId));

    // If root node was deleted, set new root
    if (this.rootId === id) {
      this.rootId = this.nodes.size > 0 ? Array.from(this.nodes.keys())[0] : null;
    }
  }

  public layout(direction: Direction = 'right'): void {
    if (!this.rootId) return;

    // Build tree structure
    const levels: string[][] = [];
    const processed = new Set<string>();

    const buildLevels = (nodeId: string, level: number) => {
      if (!levels[level]) {
        levels[level] = [];
      }
      levels[level].push(nodeId);
      processed.add(nodeId);

      // Find children
      const children = Array.from(this.connections.values())
        .filter(conn => conn.from === nodeId && !processed.has(conn.to))
        .map(conn => conn.to);

      children.forEach(childId => buildLevels(childId, level + 1));
    };

    buildLevels(this.rootId, 0);

    // Calculate positions
    levels.forEach((levelNodes, levelIndex) => {
      const levelWidth = levelNodes.length * (this.config.nodeWidth + this.config.horizontalSpacing);
      const levelHeight = levelNodes.length * (this.config.nodeHeight + this.config.verticalSpacing);
      const startX = -levelWidth / 2;
      const startY = -levelHeight / 2;

      levelNodes.forEach((nodeId, nodeIndex) => {
        const node = this.nodes.get(nodeId);
        if (!node) return;

        let x = 0, y = 0;
        switch (direction) {
          case 'right':
            x = levelIndex * (this.config.nodeWidth + this.config.levelSpacing);
            y = startY + nodeIndex * (this.config.nodeHeight + this.config.verticalSpacing);
            break;
          case 'down':
            x = startX + nodeIndex * (this.config.nodeWidth + this.config.horizontalSpacing);
            y = levelIndex * (this.config.nodeHeight + this.config.levelSpacing);
            break;
          case 'left':
            x = -levelIndex * (this.config.nodeWidth + this.config.levelSpacing);
            y = startY + nodeIndex * (this.config.nodeHeight + this.config.verticalSpacing);
            break;
          case 'up':
            x = startX + nodeIndex * (this.config.nodeWidth + this.config.horizontalSpacing);
            y = -levelIndex * (this.config.nodeHeight + this.config.levelSpacing);
            break;
        }

        this.updateNode(nodeId, { x, y });
      });
    });

    this.adjustOverlappingNodes();
  }

  private adjustOverlappingNodes(): void {
    const nodeIds = Array.from(this.nodes.keys());
    const adjustedNodes = new Map(this.nodes);

    for (let i = 0; i < nodeIds.length; i++) {
      for (let j = i + 1; j < nodeIds.length; j++) {
        const node1 = adjustedNodes.get(nodeIds[i]);
        const node2 = adjustedNodes.get(nodeIds[j]);
        if (!node1 || !node2) continue;

        const dx = Math.abs(node1.x - node2.x);
        const dy = Math.abs(node1.y - node2.y);
        const minDistance = Math.max(
          this.config.horizontalSpacing,
          this.config.verticalSpacing
        );

        if (dx < minDistance && dy < minDistance) {
          const distance = Math.sqrt(dx * dx + dy * dy);
          if (distance < minDistance) {
            const force = (minDistance - distance) / 2;
            const angle = Math.atan2(node2.y - node1.y, node2.x - node1.x);

            adjustedNodes.set(nodeIds[i], {
              ...node1,
              x: node1.x - Math.cos(angle) * force,
              y: node1.y - Math.sin(angle) * force
            });

            adjustedNodes.set(nodeIds[j], {
              ...node2,
              x: node2.x + Math.cos(angle) * force,
              y: node2.y + Math.sin(angle) * force
            });
          }
        }
      }
    }

    this.nodes = adjustedNodes;
  }
} 