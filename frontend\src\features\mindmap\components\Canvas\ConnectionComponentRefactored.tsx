/**
 * ConnectionComponent.tsx
 *
 * Component for rendering a connection between nodes in the mind map.
 * This component ensures connections stay attached to nodes when they move.
 * This version has been refactored to use the new service layer.
 */

import React, { useEffect, useState } from 'react';
import { Line, Group } from 'react-konva';
import { Connection, Node } from '../../../../core/types/MindMapTypes';
import { mindObjectService } from '../../../../core/services/MindObjectService';
import { mindSheetService } from '../../../../core/services/MindSheetService';

interface ConnectionComponentProps {
  connection: Connection;
  fromNode: Node;
  toNode: Node;
  sheetId: string;
}

// Helper function to calculate connection points
const calculateConnectionPoints = (fromNode: Node, toNode: Node) => {
  // Calculate center points of nodes
  const fromX = fromNode.x + fromNode.width / 2;
  const fromY = fromNode.y + fromNode.height / 2;
  const toX = toNode.x + toNode.width / 2;
  const toY = toNode.y + toNode.height / 2;

  // Calculate the angle between nodes
  const angle = Math.atan2(toY - fromY, toX - fromX);

  // Calculate the points where the line should start and end
  // This ensures the line starts and ends at the edge of the nodes
  const fromNodeRadius = Math.min(fromNode.width, fromNode.height) / 2;
  const toNodeRadius = Math.min(toNode.width, toNode.height) / 2;

  const startX = fromX + Math.cos(angle) * fromNodeRadius;
  const startY = fromY + Math.sin(angle) * fromNodeRadius;
  const endX = toX - Math.cos(angle) * toNodeRadius;
  const endY = toY - Math.sin(angle) * toNodeRadius;

  return { startX, startY, endX, endY };
};

const ConnectionComponent: React.FC<ConnectionComponentProps> = ({
  connection,
  fromNode,
  toNode,
  sheetId
}) => {
  // Skip rendering if either node is missing
  if (!fromNode || !toNode) {
    return null;
  }

  // Use state to track connection points
  const [points, setPoints] = useState(() => calculateConnectionPoints(fromNode, toNode));
  
  // Use state to track if this connection is selected
  const [isSelected, setIsSelected] = useState(false);

  // Update connection points when nodes move
  useEffect(() => {
    const newPoints = calculateConnectionPoints(fromNode, toNode);
    setPoints(newPoints);
  }, [fromNode.x, fromNode.y, toNode.x, toNode.y, fromNode.width, fromNode.height, toNode.width, toNode.height]);

  // Subscribe to store changes to update selection state
  useEffect(() => {
    // Get the sheet-specific store
    const store = mindSheetService.getMindMapSheetStore(sheetId);
    if (!store) return;

    // Get initial selection state
    const initialState = store.getState();
    setIsSelected(initialState.selectedConnectionId === connection.id);

    // Subscribe to selection changes
    const unsubscribe = store.subscribe(
      (state: any) => state.selectedConnectionId,
      (selectedConnectionId: string | null) => {
        setIsSelected(selectedConnectionId === connection.id);
      }
    );

    return () => {
      if (unsubscribe) unsubscribe();
    };
  }, [connection.id, sheetId]);

  // Destructure points for clarity
  const { startX, startY, endX, endY } = points;

  // Determine line dash based on style
  let dash;
  switch (connection.style) {
    case 'dashed':
      dash = [10, 5];
      break;
    case 'dotted':
      dash = [2, 2];
      break;
    case 'solid':
    default:
      dash = [];
      break;
  }

  // Handle click to select the connection
  const handleClick = () => {
    // Get the sheet-specific store
    const store = mindSheetService.getMindMapSheetStore(sheetId);
    if (!store) return;

    // Select the connection
    store.getState().selectConnection(connection.id);
  };

  return (
    <Group onClick={handleClick}>
      <Line
        points={[startX, startY, endX, endY]}
        stroke={isSelected ? '#3498db' : (connection.color || '#2c3e50')}
        strokeWidth={isSelected ? (connection.width || 2) + 1 : (connection.width || 2)}
        dash={dash}
        lineCap="round"
        lineJoin="round"
        shadowColor={isSelected ? '#3498db' : undefined}
        shadowBlur={isSelected ? 5 : 0}
        shadowOffset={{ x: 0, y: 0 }}
        shadowOpacity={0.5}
        listening={true} // Ensure the line responds to events
      />
    </Group>
  );
};

export default ConnectionComponent;
