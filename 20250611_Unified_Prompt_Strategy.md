## MindBack Prompting Strategy Canvas

This canvas tracks all key decisions and components related to LLM prompting, memory usage, and model orchestration in MindBack. It supports a modular architecture and preserves context for continued iteration.

---

### 🧠 1. Prompting System Design

#### ✅ Current Strategy

**UPDATED: Parallel Three-Stage Architecture**

* **Separation Strategy**:
  * Keep existing prompting system (`initiation_prompt2.yaml` + specialized prompts) completely untouched
  * Develop new three-stage pipeline as parallel system with separate endpoints
  * Frontend can choose between legacy and new pipeline

* **Three-Stage Pipeline (New Development)**:

  **Stage 1: Routing (`routing_prompt.yaml`)**
  * Analyzes user input and determines processing path
  * Decides: `factual_response`, `embedding_and_continue`, or `firecrawl`
  * Determines if memory retrieval is needed
  * Uses lightweight model (gpt-3.5-turbo) for efficiency

  **Stage 2: Memory Retrieval (Conditional)**
  * Triggered only when `requires_memory = true`
  * Sources: MBCP-compatible snapshots, conversation threads, context settings
  * Generates structured memory blocks for Stage 3

  **Stage 3: Unified Execution (`unified_Prompt.yaml`)**
  * Receives enriched context from previous stages
  * Generates MBCP-formatted responses
  * Maintains consistency across all sheet types

* **Memory Architecture (MBCP-Native)**:
  * **`[CTX]::`** - Compressed contextual metadata in human-readable format
  * **`[MEM]::`** - Structured conversation history and context in MBCP format
  * **`[ZIPPED]::`** - Base64 encoded large structured data (mindbook state, financial docs, agent logs)
  * **`[USER]::`** - Direct user input

* **Large Data Handling**:
  * Financial statements, complete mindbook state, agent logs → `[ZIPPED]::` (Base64)
  * Conversation context, node relationships → `[MEM]::` (structured)
  * Current session metadata → `[CTX]::` (compressed but readable)

* **No Backend Modifications**:
  * New endpoints: `/api/llm/three-stage/*`
  * Existing `/api/llm/chat` remains untouched
  * New services created alongside existing ones

#### 🔁 Open Questions

* Should we maintain partial specialization prompts for rare cases?
* Could we encode node metadata directly in the `[CTX]::` format for even shorter injection?
* Can Call 2 be optionalized (memory writes async or skipped when irrelevant)?
* How often should snapshots be stored, and should they replace or supplement latent memory?

---

### 🧩 2. Context Injection

#### ✅ Compression + Expansion (Two-Layer Context)

* Layer 1: `[CTX]::mm/teleo/topic=Buy&Build/context=Segmentation/...`
* Layer 2: `[HINT]::We are expanding the segmentation node in a strategic mindmap.`

#### 🔧 Backend Responsibilities

* Generate `[CTX]`, `[HINT]`, `[MEM]`, `[ZIPPED]`, `[SNAP]` blocks dynamically
* Merge them with `[USER]` into the unified continuation prompt
* Keep logic centralized in `prompt_service.py`

---

### 🧠 3. Model Routing

#### ✅ Selection Logic (via `select_model()`)

* Routes based on `intention`, agent layer, and RAG complexity:

  * `factual` → `gpt-3.5-turbo`
  * `teleological`, `exploratory`, `instantiation` → `gpt-4o`
  * `backtrace`, `ResearchGPT` present, or many RAG refs → `gpt-4-turbo`

#### 🔁 Considerations

* Should we allow user override?
* Claude/Mistral fallback path?
* Embed model metadata into `[CTX]::` to expose model choice to the LLM

---

### 🧬 4. Memory Strategy: MBCP-Native Memory System

#### ✅ Direction

**DROPPED: Letta Integration**
- Letta's latent format conflicts with MBCP's structured, traceable design
- Undermines UX transparency and debugging capabilities
- Introduces unnecessary complexity for memory retrieval

**NEW: MBCP-Compatible Memory System**
* All memory persistence uses MBCP-compatible formats:
  * Snapshot-based memory storage
  * Context blocks in structured format
  * Node state preservation
  * ChatFork thread structure
* Memory retrieval via:
  * `[MEM]::` - Structured conversation history and context
  * `[ZIPPED]::` - Base64 encoded large data (mindbook state, financial docs, agent logs)
  * `[CTX]::` - Compressed contextual metadata

#### ✅ Implementation Strategy

* **Snapshot-Based Memory**: Use existing MemorySnapshotService as foundation
* **Conversation Threading**: Track ChatFork lineage in MBCP format
* **Context Preservation**: Maintain foundational/strategic/operational context
* **Large Data Handling**: Base64 encode via `[ZIPPED]::` for structured documents

---

### 🔁 5. Open Threads to Return To

* **Three-stage pipeline optimization**: Token efficiency across stages
* **Memory hierarchy**: Snapshots + MBCP + context blocks → retention policies
* **Dynamic context compression**: Smart truncation for large mindbook states
* **Visual memory inspection**: UI components for memory lineage and context blocks
* **Multi-agent integration**: Clean handoff points in three-stage pipeline
* **Performance optimization**: Detect simple queries to bypass expensive stages
* **Base64 optimization**: Compression strategies for `[ZIPPED]::` blocks
* **Snapshot chaining**: Blockchain-style memory evolution tracking (`[SNAP]::history+present`)
* **Migration strategy**: Gradual transition from legacy to three-stage pipeline
