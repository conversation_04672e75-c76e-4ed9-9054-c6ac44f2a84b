/**
 * SessionPersistenceService.ts
 * 
 * Handles persistence and restoration of MindBook sessions including sheets and their state.
 * This service ensures that user sessions are preserved across browser refreshes.
 */

import { useMindBookStore } from '../state/MindBookStore';
import { SheetData, MindSheetContentType } from '../state/StoreTypes';
import { EventType } from './RegistrationManager';

const SESSION_STORAGE_KEY = 'mindback_session';
const MINDBOOK_STATE_KEY = 'mindback_mindbook_state';

interface SessionData {
  sheets: SheetData[];
  activeSheetId: string | null;
  timestamp: number;
  version: string;
}

/**
 * @deprecated Use MindBookPersistenceService instead. This service will be removed in a future update.
 */
class SessionPersistenceService {
  private static instance: SessionPersistenceService;
  private version = '1.0.0';

  private constructor() {
    console.warn('SessionPersistenceService is deprecated. Use MindBookPersistenceService instead.');
    console.log('SessionPersistenceService: Initialized');
  }

  /**
   * Get the singleton instance of the SessionPersistenceService
   */
  public static getInstance(): SessionPersistenceService {
    if (!SessionPersistenceService.instance) {
      SessionPersistenceService.instance = new SessionPersistenceService();
    }
    return SessionPersistenceService.instance;
  }

  /**
   * @deprecated Use MindBookPersistenceService.autoSaveSession() instead
   */
  public saveSession(): boolean {
    console.warn('SessionPersistenceService.saveSession is deprecated. Use MindBookPersistenceService.autoSaveSession instead.');
    // Forward to MindBookPersistenceService
    const { autoSaveSession } = require('./MindBookPersistenceService');
    return autoSaveSession();
  }

  /**
   * @deprecated Use MindBookPersistenceService.restoreAutoSavedSession() instead
   */
  public restoreSession(): boolean {
    console.warn('SessionPersistenceService.restoreSession is deprecated. Use MindBookPersistenceService.restoreAutoSavedSession instead.');
    // Forward to MindBookPersistenceService
    const { restoreAutoSavedSession } = require('./MindBookPersistenceService');
    return restoreAutoSavedSession();
  }

  /**
   * @deprecated Use MindBookPersistenceService.clearSession() instead
   */
  public clearSession(): void {
    console.warn('SessionPersistenceService.clearSession is deprecated. Use MindBookPersistenceService methods instead.');
    try {
      localStorage.removeItem(SESSION_STORAGE_KEY);
      console.log('SessionPersistenceService: Session cleared');
    } catch (error) {
      console.error('SessionPersistenceService: Failed to clear session', error);
    }
  }

  /**
   * Auto-save session with debouncing
   */
  private autoSaveTimeout: NodeJS.Timeout | null = null;
  
  public scheduleAutoSave(delay: number = 2000): void {
    if (this.autoSaveTimeout) {
      clearTimeout(this.autoSaveTimeout);
    }
    
    this.autoSaveTimeout = setTimeout(() => {
      this.saveSession();
    }, delay);
  }

  /**
   * Get session info without loading it
   */
  public getSessionInfo(): { exists: boolean; timestamp?: number; sheetsCount?: number } {
    try {
      const savedSession = localStorage.getItem(SESSION_STORAGE_KEY);
      
      if (!savedSession) {
        return { exists: false };
      }

      const sessionData: SessionData = JSON.parse(savedSession);
      
      return {
        exists: true,
        timestamp: sessionData.timestamp,
        sheetsCount: sessionData.sheets.length
      };
    } catch (error) {
      console.error('SessionPersistenceService: Failed to get session info', error);
      return { exists: false };
    }
  }
}

// Export singleton instance
const sessionPersistenceService = SessionPersistenceService.getInstance();

// Export convenience functions
/**
 * @deprecated Use MindBookPersistenceService.autoSaveSession instead
 */
export const saveSession = () => sessionPersistenceService.saveSession();

/**
 * @deprecated Use MindBookPersistenceService.restoreAutoSavedSession instead
 */
export const restoreSession = () => sessionPersistenceService.restoreSession();

/**
 * @deprecated Use MindBookPersistenceService methods instead
 */
export const clearSession = () => sessionPersistenceService.clearSession();

export default sessionPersistenceService; 