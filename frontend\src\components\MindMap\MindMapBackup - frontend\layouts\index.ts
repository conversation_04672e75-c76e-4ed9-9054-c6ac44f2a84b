/**
 * MindMap Layout System
 * 
 * A modular, strategy-based system for organizing mindmap nodes
 * with support for various layout algorithms.
 */

// Export components
export * from './components/LayoutSelector';

// Export layout manager
export { LayoutManager } from './LayoutManager';

// Export layout strategies
export { LeftToRightLayout } from './strategies/LeftToRightLayout';
export { TopDownLayout } from './strategies/TopDownLayout';
export { BottomUpLayout } from './strategies/BottomUpLayout';
export { RadialLayout } from './strategies/RadialLayout';
export { CompactLeftToRightLayout } from './strategies/CompactLeftToRightLayout';

// Export types
export * from './types';

// Export utilities
export * from './utils';

// Export hooks
export * from './hooks/useLayout';

// Export adapter
export * from './adapters/MindMapStoreAdapter';

// Create layout adapter instance
import { MindMapStoreAdapter } from './adapters/MindMapStoreAdapter';
export const layoutAdapter = new MindMapStoreAdapter();

/**
 * Utility function to apply a layout to the current MindMap
 * without needing to create an adapter instance
 */
export function applyLayout(strategyType: import('./types').LayoutStrategyType = 'leftToRight'): void {
  layoutAdapter.applyLayoutToStore(strategyType);
} 