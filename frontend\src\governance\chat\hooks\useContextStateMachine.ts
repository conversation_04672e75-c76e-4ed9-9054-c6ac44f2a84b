/**
 * useContextStateMachine.ts
 * 
 * A React hook for using the ContextStateMachine in components.
 * This provides a clean interface for components to interact with the state machine.
 */

import { useState, useEffect, useCallback } from 'react';
import contextStateMachine, { 
  ContextState, 
  TransitionEvent, 
  TransitionResult 
} from '../state/ContextStateMachine';
import { useMindBookStore } from '../../../core/state/MindBookStore';
import { getMindMapStore } from '../../../core/state/MindMapStoreFactory';
import { useChatForkStore } from '../../../components/ChatFork/ChatForkStore';
import { MindSheetContentType } from '../../../core/state/StoreTypes';
import RegistrationManager, { EventType } from '../../../core/services/RegistrationManager';
import useChatStore from '../state/ChatStore';
import { debounce } from 'lodash';

/**
 * Hook for using the context state machine
 */
export const useContextStateMachine = () => {
  // Track the current state
  const [currentState, setCurrentState] = useState<ContextState>(contextStateMachine.getState());
  const [templateValue, setTemplateValue] = useState<string | null>(contextStateMachine.getTemplateValue());
  const [isTransitioning, setIsTransitioning] = useState<boolean>(contextStateMachine.isTransitioning());
  const [isCircuitOpen, setIsCircuitOpen] = useState<boolean>(contextStateMachine.isCircuitOpen());
  const [error, setError] = useState<Error | null>(null);

  // Update state when the state machine changes
  useEffect(() => {
    const handleStateChanged = (data: any) => {
      setCurrentState(data.to);
      setError(null);
    };

    const handleTransitioning = () => {
      setIsTransitioning(true);
    };

    const handleTransitionComplete = () => {
      setIsTransitioning(false);
      setTemplateValue(contextStateMachine.getTemplateValue());
    };

    const handleCircuitOpen = () => {
      setIsCircuitOpen(true);
      setError(new Error('Circuit breaker open - too many transitions'));
    };

    const handleCircuitClosed = () => {
      setIsCircuitOpen(false);
    };

    const handleError = (err: Error) => {
      setError(err);
    };

    // Subscribe to events
    contextStateMachine.on('stateChanged', handleStateChanged);
    contextStateMachine.on('transitioning', handleTransitioning);
    contextStateMachine.on('transitionComplete', handleTransitionComplete);
    contextStateMachine.on('circuitOpen', handleCircuitOpen);
    contextStateMachine.on('circuitClosed', handleCircuitClosed);
    contextStateMachine.on('error', handleError);

    // Unsubscribe on cleanup
    return () => {
      contextStateMachine.off('stateChanged', handleStateChanged);
      contextStateMachine.off('transitioning', handleTransitioning);
      contextStateMachine.off('transitionComplete', handleTransitionComplete);
      contextStateMachine.off('circuitOpen', handleCircuitOpen);
      contextStateMachine.off('circuitClosed', handleCircuitClosed);
      contextStateMachine.off('error', handleError);
    };
  }, []);

  // Create a debounced select function to prevent rapid changes
  const selectContext = useCallback(
    debounce((context: string) => {
      console.log('Selecting context:', context);

      // Don't transition if we're already in that state
      if (
        (context === 'teleological' && currentState === ContextState.TELEOLOGICAL) ||
        (context === 'mindmap' && currentState === ContextState.MINDMAP) ||
        (context === 'chatfork' && currentState === ContextState.CHATFORK) ||
        (context === 'instantanious' && currentState === ContextState.INSTANTANIOUS) ||
        (templateValue === context && currentState === ContextState.TEMPLATE)
      ) {
        console.log('Already in this state, ignoring');
        return;
      }

      // Map the context to a transition event
      let event: TransitionEvent;
      let data: any = undefined;

      switch (context) {
        case 'teleological':
          event = TransitionEvent.SELECT_TELEOLOGICAL;
          break;
        case 'mindmap':
          event = TransitionEvent.SELECT_MINDMAP;
          break;
        case 'chatfork':
          event = TransitionEvent.SELECT_CHATFORK;
          break;
        case 'instantanious':
          event = TransitionEvent.SELECT_INSTANTANIOUS;
          break;
        default:
          // Assume it's a template
          event = TransitionEvent.SELECT_TEMPLATE;
          data = { value: context };
          break;
      }

      // Trigger the transition
      const result = contextStateMachine.transition(event, data);

      // Handle the result
      if (!result.success) {
        console.error('Transition failed:', result.error);
        setError(result.error || new Error('Unknown error'));
        return;
      }

      // Process the state change
      processStateChange(result);
    }, 300),
    [currentState, templateValue]
  );

  // Process state changes and perform side effects
  const processStateChange = useCallback((result: TransitionResult) => {
    // Skip processing if the transition failed
    if (!result.success) return;

    // Process based on the new state
    switch (result.currentState) {
      case ContextState.TELEOLOGICAL:
        processTeleologicalContext();
        break;
      case ContextState.MINDMAP:
        processMindmapContext();
        break;
      case ContextState.CHATFORK:
        processChatforkContext();
        break;
      case ContextState.TEMPLATE:
        processTemplateContext(contextStateMachine.getTemplateValue());
        break;
      case ContextState.ERROR:
        // Already handled by setting the error state
        break;
      default:
        // No processing needed for other states
        break;
    }
  }, []);

  // Process teleological context
  const processTeleologicalContext = useCallback(() => {
    console.log('Processing teleological context');
    
    // Check if we already have a teleological mindsheet
    const mindBookStore = useMindBookStore.getState();
    const existingTeleologicalSheets = mindBookStore.sheets.filter(sheet => 
      sheet.title === 'Teleological Mindmap'
    );
    
    // If we already have a teleological mindsheet, just activate it
    if (existingTeleologicalSheets.length > 0) {
      console.log('Found existing teleological mindsheet, activating it');
      const existingSheetId = existingTeleologicalSheets[0].id;
      mindBookStore.setActiveSheet(existingSheetId);
      
      // Add a system message indicating the mindmap was activated
      const systemMessage = {
        id: Date.now().toString(),
        text: 'Existing teleological mindmap activated',
        sender: 'system',
        timestamp: new Date()
      };
      useChatStore.getState().addMessage(systemMessage);
      return;
    }
    
    // Create a simple MBCP structure with just a root node
    const mbcpData = {
      intent: 'teleological',
      text: 'Teleological Mindmap',
      description: 'A mindmap for teleological intent',
      created: new Date().toISOString(),
      mindmap: {
        root: {
          text: 'Teleological Mindmap',
          description: 'Click to edit this mindmap',
          metadata: {
            intent: 'teleological',
            tags: ['mindmap']
          },
          children: []
        }
      }
    };
    
    // Create the mindsheet
    console.log('Creating mindsheet in MindBookStore');
    const sheetId = mindBookStore.createMindMapSheet('Teleological Mindmap', mbcpData);
    
    if (!sheetId) {
      console.error('Failed to create mindsheet');
      return;
    }
    
    console.log('Successfully created mindsheet with ID:', sheetId);
    
    // Set it as the active sheet
    console.log('Setting as active sheet');
    mindBookStore.setActiveSheet(sheetId);
    
    // Initialize the mindmap using the sheet-specific store
    console.log('Initializing mindmap with sheet-specific store');
    
    // Get the sheet-specific store
    const sheetStore = getMindMapStore(sheetId);
    
    // Initialize the store with window dimensions
    sheetStore.getState().initialize(window.innerWidth, window.innerHeight);
    
    // Create a new project with the root node
    const rootNodeId = sheetStore.getState().createNewProject('Teleological Mindmap');
    
    if (rootNodeId) {
      console.log('Created root node with ID:', rootNodeId);
      
      // Update the layout
      setTimeout(() => {
        sheetStore.getState().updateLayout('tree');
      }, 100);
    }
    
    // Add a system message indicating the mindmap was created
    const systemMessage = {
      id: Date.now().toString(),
      text: 'Teleological mindmap created successfully',
      sender: 'system',
      timestamp: new Date()
    };
    useChatStore.getState().addMessage(systemMessage);
  }, []);

  // Process mindmap context
  const processMindmapContext = useCallback(() => {
    console.log('Processing mindmap context');
    
    // Register the intention selection event
    RegistrationManager.registerEvent(EventType.INTENTION_SELECTED, { name: 'Mindmap' });
    
    // Add a message from the assistant prompting the user to enter the topic
    const assistantMessage = {
      id: (Date.now() + 100).toString(),
      text: 'Please click the "Build Mind Map" button to create a new mindmap, or enter a topic for your mindmap.',
      sender: 'assistant',
      timestamp: new Date()
    };
    
    // Add the message to the chat
    useChatStore.getState().addMessage(assistantMessage);
  }, []);

  // Process chatfork context
  const processChatforkContext = useCallback(() => {
    console.log('Processing chatfork context');
    
    // ChatFork context selection is not supported without backend data
    console.error('ChatFork creation failed: No backend data available');
    
    // Add an error message to the chat
    const errorMessage = {
      id: (Date.now() + 100).toString(),
      text: 'ChatFork creation failed: Backend data is required. Please use the "Explore" button on specific topics to create ChatForks.',
      sender: 'assistant',
      timestamp: new Date()
    };
    
    // Add the message to the chat
    useChatStore.getState().addMessage(errorMessage);
    
    // Throw error to fail hard
    throw new Error('ChatFork creation requires backend data');
  }, []);

  // Process template context
  const processTemplateContext = useCallback((templateValue: string | null) => {
    if (!templateValue) return;
    
    console.log('Processing template context:', templateValue);
    
    // Find the template name from the instantaniousTemplates array
    // This would normally be imported from a common location
    const instantaniousTemplates = [
      { value: 'bmc', label: 'Business Model Canvas' },
      { value: 'swot', label: 'SWOT Analysis' },
      { value: 'triz', label: 'TRIZ Method' },
      { value: 'five_forces', label: 'Five Forces (Porter\'s Model)' },
      { value: 'pestel', label: 'PESTEL Analysis' },
      { value: 'ansoff', label: 'Ansoff Matrix' },
      { value: 'value_prop', label: 'Value Proposition Canvas' },
      { value: 'jtbd', label: 'Jobs to be Done (JTBD)' },
      { value: 'business_case', label: 'Business Case Template' },
      { value: 'raci', label: 'RACI Matrix' },
      { value: 'moscow', label: 'MoSCoW Prioritization' },
      { value: 'okr', label: 'OKRs (Objectives and Key Results)' },
      { value: 'persona', label: 'User Persona Canvas' }
    ];
    
    const template = instantaniousTemplates.find(t => t.value === templateValue);
    if (template) {
      // Register the template selection event
      RegistrationManager.registerEvent(EventType.TEMPLATE_SELECTED, { name: template.label });
    }
  }, []);

  // Reset the state machine
  const resetStateMachine = useCallback(() => {
    contextStateMachine.reset();
  }, []);

  // Return the hook API
  return {
    currentState,
    templateValue,
    isTransitioning,
    isCircuitOpen,
    error,
    selectContext,
    resetStateMachine
  };
};

export default useContextStateMachine;
