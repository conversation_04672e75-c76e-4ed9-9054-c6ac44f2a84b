/* ChatFork Container */
.chat-fork-container {
  position: relative;
  width: 100%;
  min-height: 500px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  overflow: auto;
  font-family: 'Arial', sans-serif;
}

/* Container Wrapper */
.chat-fork-container-wrapper {
  position: fixed;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
  width: 90%;
  max-width: 1200px;
  height: 90vh;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 0 0 10px 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  overflow: hidden;
}

.chat-fork-container-wrapper.closed {
  top: -100vh;
}

.chat-fork-container-wrapper.open {
  top: 0;
}

/* Governance Agent Box */
.governance-agent-box {
  width: 100%;
  background-color: #f0f8ff;
  border-bottom: 1px solid #ccc;
  overflow: hidden;
}

.governance-header {
  padding: 10px 20px;
  background-color: #0066cc;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.governance-header h3 {
  margin: 0;
  font-size: 16px;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
}

.governance-content {
  padding: 15px;
  overflow: hidden;
}

/* Loading, Error, and Response states */
.loading, .error, .initial-state, .response {
  padding: 10px;
}

.loading {
  color: #666;
  font-style: italic;
}

.error {
  color: #d32f2f;
}

.transition-message {
  margin-top: 10px;
  font-style: italic;
  color: #666;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

/* ChatFork Visualization */
.chat-fork-visualization {
  flex: 1;
  overflow: auto;
  padding: 20px;
  background-color: white;
}

/* Node Styles */
.chat-fork-node {
  position: relative;
  margin: 10px 0;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  cursor: pointer;
  max-width: 90%;
  overflow: hidden;
}

.chat-fork-node:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* Node types */
.chat-fork-node-title {
  background-color: #f0f4f8;
  border: 1px solid #d1dce8;
  font-size: 1.2em;
  font-weight: bold;
}

.chat-fork-node-explanation {
  background-color: #e8f7ff;
  border: 1px solid #b3e0ff;
}

.chat-fork-node-section {
  background-color: #e1f0fc;
  border: 1px solid #b9dcf3;
  margin-left: 40px;
}

.chat-fork-node-subpoint {
  background-color: #f5e8fc;
  border: 1px solid #e1c3f3;
  margin-left: 80px;
}

.chat-fork-node-discussion {
  background-color: #e8f7e8;
  border: 1px solid #c3e6c3;
  margin-left: 80px;
}

.chat-fork-node-user_input {
  background-color: #fff3e0;
  border: 1px solid #ffe0b2;
  margin-left: 100px;
}

/* Selected node */
.chat-fork-node-selected {
  border: 2px solid #0066cc;
  box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.3);
}

/* Node ID */
.chat-fork-node-id {
  position: absolute;
  top: 5px;
  left: 5px;
  font-size: 12px;
  color: #666;
  font-weight: bold;
}

/* Node Content */
.chat-fork-node-content {
  margin-top: 10px;
  padding: 5px;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Input field */
.chat-fork-input {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  max-width: 800px;
  background: white;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.chat-fork-input-field {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
}

.chat-fork-input-field:focus {
  outline: none;
  border-color: #0066cc;
  box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
}

/* Context links */
.chat-fork-node-context-links {
  display: flex;
  gap: 10px;
  margin-top: 10px;
  padding-top: 5px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.context-link {
  color: #0066cc;
  text-decoration: underline;
  cursor: pointer;
  font-size: 12px;
}

.context-link:hover {
  color: #004499;
}

/* Node levels positioning */
.chat-fork-node[data-level="0"] {
  margin-left: 0;
}

.chat-fork-node[data-level="1"] {
  margin-left: 40px;
}

.chat-fork-node[data-level="2"] {
  margin-left: 80px;
}

.chat-fork-node[data-level="3"] {
  margin-left: 120px;
}

/* Connection Styles */
.chat-fork-connection {
  pointer-events: none;
  z-index: 0;
}

/* Animation for new nodes */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.chat-fork-node {
  animation: fadeIn 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .chat-fork-container-wrapper {
    width: 100%;
    height: 100vh;
    border-radius: 0;
  }

  .chat-fork-node {
    max-width: 95%;
  }

  .chat-fork-node[data-level="1"] {
    margin-left: 20px;
  }

  .chat-fork-node[data-level="2"] {
    margin-left: 40px;
  }

  .chat-fork-node[data-level="3"] {
    margin-left: 60px;
  }
}

/* GovernanceAgent styles */
.governance-agent-dialog .MuiPaper-root {
  border-radius: 10px;
  overflow: hidden;
}

.welcome-message {
  padding: 15px;
  background-color: #f0f8ff;
  border-radius: 8px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.message {
  margin-bottom: 15px;
  padding: 10px 15px;
  border-radius: 10px;
  position: relative;
  max-width: 80%;
}

.user-message {
  align-self: flex-end;
  background-color: #0066cc;
  color: white;
  margin-left: auto;
}

.assistant-message {
  align-self: flex-start;
  background-color: #f0f0f0;
  color: #333;
  margin-right: auto;
}

.message-sender {
  font-weight: bold;
  font-size: 0.8rem;
  margin-bottom: 5px;
}

.message-text {
  word-break: break-word;
}

.message-timestamp {
  font-size: 0.7rem;
  color: rgba(0, 0, 0, 0.5);
  text-align: right;
  margin-top: 5px;
}

.user-message .message-timestamp {
  color: rgba(255, 255, 255, 0.7);
}

.message-history {
  display: flex;
  flex-direction: column;
  padding: 10px;
}

.settings-panel {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 15px;
  border: 1px solid #e0e0e0;
}

.model-selector {
  margin-top: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.model-selector select {
  padding: 5px;
  border-radius: 4px;
  border: 1px solid #ccc;
}

.crew-ai-status {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
}

/* Error display for ChatFork visualization */
.chat-fork-error {
  padding: 20px;
  margin: 20px;
  background-color: #fff4f4;
  border: 1px solid #ffcaca;
  border-radius: 8px;
  color: #d32f2f;
}

.chat-fork-error p {
  margin-bottom: 10px;
  font-weight: bold;
}

.chat-fork-error pre {
  background-color: #f8f8f8;
  padding: 10px;
  border-radius: 4px;
  overflow: auto;
  font-size: 12px;
  color: #666;
  max-height: 300px;
}

/* ChatForkView - Modern component for exploratory content */
/* Positioned at top-left, distinct from GovernanceChat (bottom-right) */

.chatfork-container {
  position: fixed; /* Fixed position to ensure it stays in place */
  top: 20px;
  left: 20px;
  z-index: 1000;
  width: 400px;
  max-width: 80vw;
  max-height: 70vh;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #4caf50; /* Green accent to distinguish from GovernanceChat */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: opacity 0.3s ease, transform 0.3s ease;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.chatfork-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #c8e6c9; /* Light green border */
  background-color: #e8f5e9; /* Light green background */
}

.chatfork-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chatfork-close-btn {
  background: none;
  border: none;
  color: #666;
  font-size: 18px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.chatfork-close-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
}

.chatfork-content {
  padding: 16px;
  overflow-y: auto;
  flex: 1;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  max-height: 400px;
}

.chatfork-content p {
  margin: 0 0 16px 0;
  text-align: justify;
}

.chatfork-content p:last-child {
  margin-bottom: 0;
}

.chatfork-selection-actions {
  padding: 12px 16px;
  background-color: #f7f7f7;
  border-top: 1px solid #eaeaea;
  display: flex;
  justify-content: flex-end;
  animation: fadeIn 0.3s ease;
}

.chatfork-selection-actions button {
  background-color: #0066cc;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chatfork-selection-actions button:hover {
  background-color: #0055aa;
}

/* Canvas-based ChatFork styles */
.chatfork-canvas-container {
  position: absolute;
  top: 40px; /* Below the header */
  left: 0;
  right: 0;
  bottom: 70px; /* Above the mindsheet tabs (30px) and footer (40px) */
  background-color: white;
  overflow: auto; /* Changed from hidden to auto to allow scrolling */
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  font-family: Arial, sans-serif;
  z-index: 10;
  border: 1px solid #e0e0e0; /* Add a subtle border */
}

/* Special styling for ChatFork when inside a MindSheet */
.chatfork-canvas-container.in-mindsheet {
  position: relative;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  width: 100%;
  border: none;
  z-index: 1; /* Lower z-index when in a mindsheet */
}

/* Standalone container for ChatFork when not in a MindSheet */
.chatfork-standalone-container {
  position: fixed;
  top: 40px; /* Below the header */
  left: 0;
  right: 0;
  bottom: 70px; /* Above the footer */
  z-index: 100;
  background-color: rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;
  align-items: center;
}

.chatfork-standalone-container .chatfork-canvas-container {
  position: relative;
  width: 90%;
  max-width: 1200px;
  height: 80%;
  margin: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.chatfork-canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #000;
  color: white;
}

.chatfork-canvas-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chatfork-canvas-content {
  padding: 15px;
  overflow-y: auto;
  max-height: calc(80vh - 50px);
  font-size: 14px;
  line-height: 1.6;
  flex: 1; /* Added to ensure it takes up available space */
}

.chatfork-empty-message {
  color: #666;
  font-style: italic;
  text-align: center;
  margin-top: 40px;
}

.chatfork-canvas-content p {
  margin: 0 0 15px 0;
}

.chatfork-selection-menu {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  padding: 5px;
  z-index: 1001;
  animation: fadeIn 0.2s ease;
}

.chatfork-action-button {
  background-color: #0066cc;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chatfork-action-button:hover {
  background-color: #0055aa;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Minimal header for exploratory content without title */
.chatfork-canvas-header-minimal {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 1002;
}

.chatfork-close-btn-minimal {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.chatfork-close-btn-minimal:hover {
  background-color: rgba(0, 0, 0, 0.9);
}

/* NEW LAYOUT STYLES FOR IMPROVED CHATFORK */

/* Main layout container */
.chatfork-layout {
  display: flex;
  height: 100%;
  gap: 1px;
  background-color: #e0e0e0;
}

/* Main content area (left side) */
.chatfork-main-content {
  flex: 0 0 60%;
  background-color: white;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chatfork-main-header {
  padding: 12px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.chatfork-main-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.chatfork-main-text {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #fafafa;
  /* Explicitly enable text selection */
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  cursor: text;
}

/* Fork chats area (right side) */
.chatfork-forks-area {
  flex: 0 0 40%;
  background-color: white;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chatfork-forks-header {
  padding: 12px 20px;
  background-color: #f1f3f4;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.chatfork-forks-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.chatfork-forks-count {
  font-size: 12px;
  color: #6c757d;
  background-color: #e9ecef;
  padding: 2px 8px;
  border-radius: 12px;
}

.chatfork-forks-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #f8f9fa;
}

.chatfork-forks-placeholder {
  text-align: center;
  color: #6c757d;
  padding: 40px 20px;
}

.chatfork-forks-placeholder p {
  margin: 0 0 15px 0;
  font-size: 14px;
  line-height: 1.5;
}

.chatfork-forks-instructions {
  background-color: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 8px;
  padding: 12px;
  margin-top: 15px;
}

.chatfork-forks-instructions small {
  color: #1976d2;
  font-size: 12px;
  line-height: 1.4;
}

/* MARKDOWN STYLING */
.chatfork-markdown {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  color: #333;
  /* Explicitly enable text selection */
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

/* GitHub Flavored Markdown uses default browser styling */

/* Enhanced selection menu */
.chatfork-fork-button {
  background-color: #28a745;
}

.chatfork-fork-button:hover {
  background-color: #218838;
}

/* Responsive design */
@media (max-width: 768px) {
  .chatfork-layout {
    flex-direction: column;
  }
  
  .chatfork-main-content {
    flex: 0 0 60%;
  }
  
  .chatfork-forks-area {
    flex: 0 0 40%;
  }
}

/* Scrollbar styling for webkit browsers */
.chatfork-main-text::-webkit-scrollbar,
.chatfork-forks-content::-webkit-scrollbar {
  width: 6px;
}

.chatfork-main-text::-webkit-scrollbar-track,
.chatfork-forks-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.chatfork-main-text::-webkit-scrollbar-thumb,
.chatfork-forks-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chatfork-main-text::-webkit-scrollbar-thumb:hover,
.chatfork-forks-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Fork items styling */
.chatfork-forks-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.chatfork-fork-item {
  background-color: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

.chatfork-fork-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.chatfork-fork-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.chatfork-fork-header h5 {
  margin: 0;
  font-size: 13px;
  font-weight: 600;
  color: #495057;
  flex: 1;
  line-height: 1.3;
}

.chatfork-fork-header small {
  font-size: 11px;
  color: #6c757d;
  white-space: nowrap;
  margin-left: 8px;
}

.chatfork-fork-content {
  font-size: 12px;
  line-height: 1.4;
}

.chatfork-fork-content p {
  margin: 4px 0;
  color: #495057;
}

.chatfork-fork-content strong {
  color: #212529;
  font-weight: 600;
}

.chatfork-fork-content em {
  color: #6c757d;
  font-style: italic;
}

/* ChatFork Manager Styles */
.chatfork-manager-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.chatfork-manager-dialog {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.chatfork-manager-header {
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chatfork-manager-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.chatfork-manager-close {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.chatfork-manager-close:hover {
  background-color: #e0e0e0;
}

.chatfork-manager-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.chatfork-info-section,
.chatfork-selection-section,
.chatfork-actions-section,
.chatfork-stats-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.chatfork-info-section:last-child,
.chatfork-selection-section:last-child,
.chatfork-actions-section:last-child,
.chatfork-stats-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.chatfork-info-section h4,
.chatfork-selection-section h4,
.chatfork-actions-section h4,
.chatfork-stats-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.chatfork-info p,
.chatfork-stats p {
  margin: 8px 0;
  font-size: 14px;
  color: #666;
}

.chatfork-info strong,
.chatfork-stats strong {
  color: #333;
}

.selected-text-preview {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 12px;
  font-size: 14px;
  line-height: 1.4;
  color: #495057;
  white-space: pre-wrap;
}

.chatfork-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.chatfork-manager-button {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chatfork-manager-button:hover {
  background-color: #0056b3;
}

.chatfork-manager-button.secondary {
  background-color: #6c757d;
}

.chatfork-manager-button.secondary:hover {
  background-color: #545b62;
}

.chatfork-manager-footer {
  border-top: 1px solid #e0e0e0;
  padding: 16px 20px;
  display: flex;
  justify-content: flex-end;
}

/* Responsive styles */
@media (max-width: 768px) {
  .chatfork-manager-dialog {
    width: 95%;
    max-height: 90vh;
  }
  
  .chatfork-manager-header,
  .chatfork-manager-content,
  .chatfork-manager-footer {
    padding: 12px 16px;
  }
  
  .chatfork-actions {
    flex-direction: column;
  }
  
  .chatfork-manager-button {
    width: 100%;
  }
}

/* ENHANCED CHATFORK STYLES */

/* Text highlighting */
.chatfork-highlight {
  padding: 2px 4px;
  border-radius: 3px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.chatfork-highlight:hover {
  border-color: rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chatfork-highlighted-content {
  line-height: 1.6;
}

/* Enhanced fork layout */
.chatfork-enhanced-forks-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* MINDBACK-STYLE FORK LAYOUT */
.chatfork-mindback-forks-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chatfork-mindback-fork-item {
  background-color: #fafbfc;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(149, 157, 165, 0.15);
  transition: all 0.2s ease;
  position: relative;
}

.chatfork-mindback-fork-item:hover {
  border-color: #d1d9e0;
  box-shadow: 0 3px 10px rgba(149, 157, 165, 0.2);
}

/* Fork headline - prominent and clean */
.chatfork-mindback-headline {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eaecef;
}

.chatfork-mindback-headline h4 {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
  color: #24292e;
  line-height: 1.4;
  font-style: italic;
}

/* Primary input field - MindBack style */
.chatfork-mindback-input-section {
  margin-bottom: 12px;
}

.chatfork-mindback-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e4e8;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  transition: all 0.15s ease;
  background-color: #ffffff;
  color: #24292e;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
}

.chatfork-mindback-input:focus {
  outline: none;
  border-color: #0366d6;
  box-shadow: inset 0 1px 2px rgba(27, 31, 35, 0.075), 0 0 0 0.2em rgba(3, 102, 214, 0.3);
  background-color: #ffffff;
}

.chatfork-mindback-input.active {
  border-color: #28a745;
  box-shadow: inset 0 1px 2px rgba(27, 31, 35, 0.075), 0 0 0 0.2em rgba(40, 167, 69, 0.2);
}

.chatfork-mindback-input::placeholder {
  color: #6a737d;
  font-weight: 400;
}

/* Development area - subtle and secondary */
.chatfork-mindback-development-section {
  margin-bottom: 8px;
}

.chatfork-mindback-development-area {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
  font-size: 13px;
  line-height: 1.4;
  resize: vertical;
  min-height: 40px;
  transition: all 0.15s ease;
  background-color: #f6f8fa;
  color: #586069;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
}

.chatfork-mindback-development-area:focus {
  outline: none;
  border-color: #0366d6;
  box-shadow: inset 0 1px 2px rgba(27, 31, 35, 0.075), 0 0 0 0.2em rgba(3, 102, 214, 0.15);
  background-color: #ffffff;
  color: #24292e;
}

.chatfork-mindback-development-area::placeholder {
  color: #959da5;
  font-style: italic;
}

/* Meta information - very subtle */
.chatfork-mindback-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f1f3f4;
}

.chatfork-meta-level {
  font-size: 10px;
  font-weight: 600;
  color: #0366d6;
  background-color: #f1f8ff;
  padding: 2px 6px;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.chatfork-meta-time {
  font-size: 11px;
  color: #959da5;
  font-weight: 400;
}

/* Child forks */
.chatfork-mindback-children {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px dashed #d1d5da;
}

/* Text highlighting - more subtle */
.chatfork-highlight {
  padding: 1px 3px;
  border-radius: 3px;
  border: 1px solid rgba(3, 102, 214, 0.2);
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.chatfork-highlight:hover {
  border-color: rgba(3, 102, 214, 0.4);
  box-shadow: 0 1px 3px rgba(3, 102, 214, 0.2);
}

/* Main header improvements */
.chatfork-main-header small {
  display: block;
  font-size: 12px;
  color: #6a737d;
  font-weight: 400;
  margin-top: 4px;
}

.chatfork-main-header small strong {
  color: #24292e;
  background-color: #f6f8fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 11px;
}

/* Remove enhanced styles that are no longer needed */
/* Legacy selectors - removed empty ruleset */
/* 
.chatfork-enhanced-fork-item,
.chatfork-enhanced-fork-header,
.chatfork-fork-info,
.chatfork-fork-level,
.chatfork-fork-timestamp,
.chatfork-selected-text-reference,
.chatfork-input-section,
.chatfork-input-label,
.chatfork-fork-input,
.chatfork-development-section,
.chatfork-development-label,
.chatfork-development-area,
.chatfork-children 
*/

/* Responsive design for MindBack forks */
@media (max-width: 768px) {
  .chatfork-mindback-fork-item {
    margin-left: 0 !important;
    padding: 12px;
  }
  
  .chatfork-mindback-input,
  .chatfork-mindback-development-area {
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  .chatfork-mindback-headline h4 {
    font-size: 14px;
  }
  
  .chatfork-mindback-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* Animations */
.chatfork-mindback-fork-item {
  animation: forkFadeIn 0.3s ease-out;
}

@keyframes forkFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus states for accessibility */
.chatfork-mindback-input:focus,
.chatfork-mindback-development-area:focus {
  outline: 2px solid transparent;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .chatfork-highlight {
    border: 2px solid currentColor;
  }
  
  .chatfork-mindback-fork-item {
    border: 2px solid currentColor;
  }
  
  .chatfork-mindback-input,
  .chatfork-mindback-development-area {
    border: 2px solid currentColor;
  }
}

/* Ensure text selection is always enabled and visible */
.chatfork-main-text,
.chatfork-main-text *,
.chatfork-markdown,
.chatfork-markdown *,
.chatfork-empty-message,
.chatfork-empty-message * {
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  cursor: text !important;
  position: relative !important;
  z-index: 1 !important;
}

/* Make text selection very visible for debugging */
.chatfork-main-text ::selection,
.chatfork-markdown ::selection,
.chatfork-empty-message ::selection {
  background-color: #007acc !important;
  color: white !important;
}

/* Ensure no overlays are blocking text interaction */
.chatfork-main-text {
  pointer-events: auto !important;
  touch-action: auto !important;
}

/* Highlight for selected text - extremely important styles */
.highlight-selection {
  background-color: #ffeb3b !important;
  color: #000 !important;
  border-radius: 3px !important;
  padding: 2px 4px !important;
  margin: 0 1px !important;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1) !important;
  font-weight: bold !important;
  display: inline !important;
  position: relative !important;
  cursor: pointer !important;
  text-decoration: none !important;
  border: 1px solid #ffc107 !important;
  z-index: 100 !important;
}

/* Make hover state very obvious */
.highlight-selection:hover {
  background-color: #ffd600 !important;
  box-shadow: 0 0 0 3px rgba(255, 214, 0, 0.3) !important;
}

/* Text selection styles */
.chatfork-content ::selection {
  background-color: #ffeb3b;
  color: #000;
}

/* Make sure the selection is visible */
.chatfork-content {
  position: relative;
}

/* Add a subtle indicator that text can be selected */
.chatfork-content:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255, 235, 59, 0.5), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chatfork-content:hover:after {
  opacity: 1;
}

/* Tracked Selection styles */
.tracked-selection {
  background-color: #ffeb3b !important;
  border-radius: 2px !important;
  padding: 2px 0 !important;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05) !important;
  position: relative !important;
  cursor: pointer !important;
  display: inline !important;
  color: black !important;
  font-weight: bold !important;
}

.tracked-selection:hover {
  background-color: #ffd600 !important;
}

/* Add a subtle ID indicator using data attribute */
.tracked-selection::after {
  content: attr(data-selection-id);
  position: absolute;
  top: -16px;
  right: 0;
  font-size: 9px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 1px 3px;
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.tracked-selection:hover::after {
  opacity: 1;
}

/* Fork-specific styling (optional) */
.tracked-selection[data-fork-id="default"] {
  border-bottom: 1px dashed #333;
}

/* Ensure text is selectable */
.chatfork-content,
.chatfork-content *,
.initial-exploration,
.initial-exploration *,
.fork-chats,
.fork-chats * {
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  cursor: text !important;
}

/* ChatFork Header Controls */
.chatfork-header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.chatfork-action-btn {
  background-color: #f0f0f0;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s ease;
}

.chatfork-action-btn:hover {
  background-color: #e0e0e0;
}

/* Selections List */
.chatfork-selection-actions {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border-top: 1px solid #ddd;
}

.chatfork-selection-actions h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 14px;
  color: #333;
}

.chatfork-selections-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
}

.chatfork-selection-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.selection-text {
  font-size: 13px;
  color: #333;
  font-style: italic;
}

.selection-actions {
  display: flex;
  gap: 8px;
}

.selection-actions button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.selection-actions button:hover {
  background-color: #0069d9;
}

.selection-actions .remove-btn {
  background-color: #f44336;
  font-size: 10px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.selection-actions .remove-btn:hover {
  background-color: #d32f2f;
}

.chatfork-instructions {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px dashed #ddd;
}

.chatfork-instructions p {
  font-size: 13px;
  color: #666;
  margin: 0;
}

.chatfork-instructions kbd {
  background-color: #f7f7f7;
  border: 1px solid #ccc;
  border-radius: 3px;
  box-shadow: 0 1px 0 rgba(0,0,0,0.2);
  color: #333;
  display: inline-block;
  font-size: 11px;
  line-height: 1.4;
  margin: 0 2px;
  padding: 1px 5px;
}

/* Keyboard shortcut styling */
kbd {
  display: inline-block;
  padding: 2px 5px;
  font-family: monospace;
  font-size: 11px;
  line-height: 1;
  background-color: #f7f7f7;
  border: 1px solid #ccc;
  border-radius: 3px;
  box-shadow: 0 1px 0 rgba(0,0,0,0.2);
  margin: 0 2px;
  vertical-align: middle;
  color: #333;
}

/* Simple highlighting for selected text */
.simple-highlight {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 1px 2px;
  border-radius: 2px;
}

.chatfork-content-text {
  line-height: 1.6;
  padding: 1.5rem;
  background-color: #f9f9f9;
  border-radius: 6px;
  word-wrap: break-word;
  font-size: 0.95rem;
  color: #333;
  border-left: 2px solid #eee;
}

/* Professional markdown styling */
.chatfork-content-text h1,
.chatfork-content-text h2,
.chatfork-content-text h3 {
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
  font-family: 'Segoe UI', sans-serif;
  color: #2c3e50;
}

.chatfork-content-text h1 {
  font-size: 1.5rem;
  border-bottom: 2px solid #3498db;
  padding-bottom: 0.5rem;
}

.chatfork-content-text h2 {
  font-size: 1.3rem;
  border-top: 1px solid #eee;
  padding-top: 1rem;
}

.chatfork-content-text h3 {
  font-size: 1.1rem;
  color: #34495e;
}

.chatfork-content-text ul {
  padding-left: 1.2rem;
  margin-bottom: 1rem;
  list-style-type: disc;
}

.chatfork-content-text ol {
  padding-left: 1.2rem;
  margin-bottom: 1rem;
}

.chatfork-content-text li {
  margin-bottom: 0.4rem;
  line-height: 1.5;
}

.chatfork-content-text p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

.chatfork-content-text blockquote {
  border-left: 4px solid #3498db;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #555;
  background-color: #f8f9fa;
  padding: 0.5rem 1rem;
  border-radius: 0 4px 4px 0;
}

.chatfork-content-text code {
  background-color: #f1f2f6;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 0.9em;
}

.chatfork-content-text pre {
  background-color: #f1f2f6;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  margin: 1rem 0;
}

.chatfork-content-text strong {
  font-weight: 600;
  color: #2c3e50;
}

.chatfork-content-text em {
  font-style: italic;
  color: #555;
}

.selection-instruction {
  margin-top: 10px;
  padding: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  text-align: center;
}

.selection-instruction small {
  color: #666;
  font-size: 12px;
}

/* Fork Chat Items */
.fork-chat-item {
  margin-bottom: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: white;
}

.fork-chat-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 10px 15px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.fork-chat-title {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.fork-chat-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 10px;
}

.fork-chat-meta small {
  color: #666;
  font-size: 11px;
}

.fork-remove-btn {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  font-size: 12px;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.fork-remove-btn:hover {
  background-color: #f0f0f0;
  color: #666;
}

.fork-chat-content {
  padding: 15px;
}

.fork-user-input {
  margin-bottom: 15px;
}

.fork-input-field {
  width: 100%;
  min-height: 60px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
  font-family: inherit;
  resize: vertical;
  background-color: white;
}

.fork-input-field:focus {
  outline: none;
  border-color: #999;
}

.fork-input-field::placeholder {
  color: #999;
}

.fork-llm-response {
  min-height: 40px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.fork-response-content {
  font-size: 13px;
  line-height: 1.5;
  color: #333;
}

.fork-response-placeholder {
  font-size: 12px;
  color: #999;
  font-style: italic;
}

/* Subtle sticky highlight styling */
.sticky-highlight {
  border-radius: 2px !important;
  padding: 0 2px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
  /* Background and border colors set dynamically */
}

.sticky-highlight:hover {
  opacity: 0.8 !important;
}

.sticky-highlight.active-selection {
  /* Only outline, no bold text or shadow */
}

/* Ensure active outline is always visible and stable */
.sticky-highlight[data-selection-id] {
  transition: none !important;
}

/* Vertical tabs on left side of fork area */
.chatfork-vertical-tabs {
  width: 50px;
  background-color: #f5f6f7;
  border-left: 1px solid #e9ecef;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 12px 6px;
  overflow-y: auto;
  flex-shrink: 0;
}

.chatfork-vertical-tab {
  width: 38px;
  height: 38px;
  border: 2px solid;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  color: #333;
  transition: all 0.2s ease;
  opacity: 0.8;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chatfork-vertical-tab:hover {
  opacity: 1;
  transform: scale(1.1);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.chatfork-vertical-tab.active {
  opacity: 1;
  border-width: 3px;
  transform: scale(1.05);
  outline: 2px solid rgba(0, 0, 0, 0.3);
  outline-offset: 2px;
}

/* Selected text preview in fork chat */
.fork-selected-text {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.fork-selected-text h5 {
  margin: 0 0 8px 0;
  color: #495057;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.fork-text-preview {
  padding: 12px;
  border-radius: 6px;
  border: 2px solid;
  font-style: italic;
  color: #333;
  line-height: 1.4;
  word-break: break-word;
  font-size: 14px;
}