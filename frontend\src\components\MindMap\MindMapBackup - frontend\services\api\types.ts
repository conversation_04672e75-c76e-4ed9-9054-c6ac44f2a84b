export interface PromptData {
  background?: string;
  role?: string;
  task?: string;
  input?: Record<string, string>;
  guidelines?: string;
  result_format?: string;
  example_result?: string;
  system_role?: string;
  [key: string]: any;
}

export interface LLMConfig {
  apiBaseUrl?: string;
  useMockResponses?: boolean;
}

export interface PromptResponse {
  success: boolean;
  prompt_type: string;
  file_path: string;
  content: string;
  parsed: PromptData;
  error?: string;
} 