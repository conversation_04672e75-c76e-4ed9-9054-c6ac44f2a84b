/**
 * MindBook Component
 *
 * A container component that manages multiple MindSheets.
 * Similar to an Excel workbook with multiple sheets.
 *
 * This component uses the MindBookStore for state management
 * and serves as the main container for all content in the application.
 *
 * It follows the Excel model where a workbook contains multiple worksheets.
 */

import React, { useEffect } from 'react';
import { MindSheetContentType } from '../../core/state/StoreTypes';
import MindSheetWrapper from './MindSheetWrapper';
import { useMindBookStore } from '../../core/state/MindBookStore';
import RegistrationManager, { EventType } from '../../core/services/RegistrationManager';
import './MindBook.css';

// Props interface
interface MindBookProps {
  governanceComponent?: React.ReactNode;
}

const MindBook: React.FC<MindBookProps> = ({
  governanceComponent
}) => {
  // Use the MindBookStore for state management
  const { sheets, activeSheetId, setActiveSheet } = useMindBookStore();

  // Log when sheets or active sheet changes
  useEffect(() => {
    console.log('MindBook: Sheets updated', sheets);
    console.log('MindBook: Active sheet ID', activeSheetId);
  }, [sheets, activeSheetId]);

  // Handle sheet activation
  const handleActivateSheet = (sheetId: string) => {
    console.log('MindBook: Activating sheet', sheetId);
    setActiveSheet(sheetId);

    // Find the sheet to get its content type
    const sheet = sheets.find(s => s.id === sheetId);
    if (sheet) {
      // Register the sheet switch event
      RegistrationManager.registerEvent(EventType.SHEET_SWITCHED, {
        id: sheetId,
        type: sheet.contentType.toLowerCase()
      });
    }
  };

  return (
    <div className="mind-book">
      {/* Governance component (if provided) */}
      {governanceComponent && (
        <div className="governance-container">
          {governanceComponent}
        </div>
      )}

      {/* Sheets container */}
      <div className="sheets-container">
        {sheets.map(sheet => (
          <MindSheetWrapper
            key={sheet.id}
            id={sheet.id}
            title={sheet.title}
            contentType={sheet.contentType}
            isActive={sheet.id === activeSheetId}
            content={sheet.content}
            onActivate={() => handleActivateSheet(sheet.id)}
          />
        ))}
      </div>

      {/* We don't need sheet tabs here as they're handled by MindSheetTabs component */}
    </div>
  );
};

export default MindBook;
