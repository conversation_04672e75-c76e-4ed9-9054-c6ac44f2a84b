# Intent Types Configuration
# This is the single source of truth for all intent types in the application

intents:
  factual:
    description: "A direct question with a clear, verifiable answer"
    display_name: "Factual"
    requires_mindmap: false
    requires_chatfork: false
    requires_template: false
    # Factual responses don't need specialized prompts
    
  exploratory:
    description: "A conceptual or open-ended question exploring a topic or idea"
    display_name: "Exploratory"
    requires_mindmap: false
    requires_chatfork: true
    requires_template: false
    prompt_template: "initiate_chatfork2"
    
  teleological:
    description: "Structured planning to reach a goal, often involving mindmaps"
    display_name: "Teleological"
    requires_mindmap: true
    requires_chatfork: false
    requires_template: false
    prompt_template: "initiate_mindmap2"
    
  instantiation:
    description: "Populating a known structure (e.g., SWOT, Business Model Canvas)"
    display_name: "Instantiation"
    requires_mindmap: false
    requires_chatfork: false
    requires_template: true
    # Template routing is handled separately via instantiation_template_router
    
  situational:
    description: "Solving a real-world business challenge involving interpersonal or stakeholder conflict"
    display_name: "Situational"
    requires_mindmap: false
    requires_chatfork: true
    requires_template: false
    prompt_template: "initiate_situational2"
    
  miscellaneous:
    description: "Prompts that don't clearly fit any other category"
    display_name: "Miscellaneous"
    requires_mindmap: false
    requires_chatfork: false
    requires_template: false
    prompt_template: "initiate_miscellaneous2"
