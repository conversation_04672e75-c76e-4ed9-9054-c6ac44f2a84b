/**
 * GovernanceChat.css
 * 
 * Styles for the GovernanceChat component.
 */

.governance-chat {
  position: fixed;
  z-index: 1000;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.governance-chat.collapsed {
  height: auto !important;
}

.governance-chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.governance-chat-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

/* Model selector styling */
.model-selector-container {
  display: flex;
  padding: 8px 16px;
  background-color: #f5f5f5;
  border-top: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
}

.model-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
  cursor: pointer;
}

.model-select:focus {
  outline: none;
  border-color: #2196f3;
}

/* Resize handle styling */
.resize-handle-corner {
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  z-index: 1;
}

.resize-handle-corner:hover {
  background-color: rgba(33, 150, 243, 0.3);
}

/* Bottom right corner - main resize handle */
.governance-chat .resize-handle-corner:nth-child(1) {
  right: 0;
  bottom: 0;
  width: 15px;
  height: 15px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 0 0 8px 0;
  cursor: nwse-resize;
}

.governance-chat .resize-handle-corner:nth-child(1):hover {
  background-color: rgba(33, 150, 243, 0.2);
}

/* Edge resize handles */
.resize-handle-bottom,
.resize-handle-right,
.resize-handle-left,
.resize-handle-top {
  position: absolute;
  background-color: transparent;
  z-index: 1;
}

.resize-handle-bottom:hover,
.resize-handle-right:hover,
.resize-handle-left:hover,
.resize-handle-top:hover {
  background-color: rgba(33, 150, 243, 0.1);
}

/* Icon styling for header buttons */
.icon-restore-position,
.icon-restore-size {
  font-size: 16px;
  font-weight: bold;
}
