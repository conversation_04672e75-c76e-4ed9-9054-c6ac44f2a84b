/**
 * MindMapCanvas.tsx
 *
 * Component for rendering a mind map canvas.
 * This version has been refactored to use the new service layer.
 */

import React, { useRef, useEffect, useState } from 'react';
import { Layer, Group } from 'react-konva';
import NodeComponent from './NodeComponent';
import ConnectionComponent from './ConnectionComponent';
import StageCompatWrapper from '../../../../components/MindMap/components/Canvas/StageCompatWrapper';
import '../../../../core/services/KeyboardManager'; // Import to ensure it's initialized
import './MindMapCanvas.css';

// Import services instead of direct store access
import { mindObjectService } from '../../../../core/services/MindObjectService';
import { layoutService, LayoutType } from '../../../../core/services/LayoutService';
import { mindSheetService } from '../../../../core/services/MindSheetService';
import RegistrationManager, { EventType } from '../../../../core/services/RegistrationManager';

// Cast Konva components to any to bypass TypeScript errors
const KonvaLayer = Layer as any;
const KonvaGroup = Group as any;

interface MindMapCanvasProps {
  width: number;
  height: number;
  sheetId: string;
  store?: any; // Store prop passed from wrapper - will be removed in future versions
}

const MindMapCanvas: React.FC<MindMapCanvasProps> = ({ width, height, sheetId, store }) => {
  const stageRef = useRef<any>(null);
  const storeRef = useRef<any>(store);

  // Store the store reference in a ref to avoid re-renders
  if (store !== storeRef.current) {
    storeRef.current = store;
  }

  // Use state to track store data
  const [nodes, setNodes] = useState<any>({});
  const [connections, setConnections] = useState<any[]>([]);
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [position, setPosition] = useState<{ x: number, y: number }>({ x: 0, y: 0 });
  const [scale, setScale] = useState<number>(1);

  // Log the sheet ID and store to help with debugging
  useEffect(() => {
    if (storeRef.current) {
      console.log(`MindMapCanvas: Using sheet-specific store for sheet: ${sheetId}`);
      console.log(`MindMapCanvas: Store has ${Object.keys(storeRef.current.getState().nodes).length} nodes`);
    }
  }, [sheetId]);

  // Subscribe to store changes
  useEffect(() => {
    if (!storeRef.current) return;

    // Get initial state
    const initialState = storeRef.current.getState();
    setNodes(initialState.nodes);
    setConnections(initialState.connections);
    setSelectedNodeId(initialState.selectedNodeId);
    setPosition(initialState.position);
    setScale(initialState.scale);

    // Subscribe to store changes
    const unsubscribe = storeRef.current.subscribe(
      (state: any) => ({
        nodes: state.nodes,
        connections: state.connections,
        selectedNodeId: state.selectedNodeId,
        position: state.position,
        scale: state.scale
      }),
      (newState: any) => {
        setNodes(newState.nodes);
        setConnections(newState.connections);
        setSelectedNodeId(newState.selectedNodeId);
        setPosition(newState.position);
        setScale(newState.scale);
      }
    );

    return () => {
      if (unsubscribe) unsubscribe();
    };
  }, [sheetId]);

  // Focus the stage element
  const focusStage = () => {
    if (stageRef.current) {
      const stage = stageRef.current.getStage();
      if (stage && stage.container()) {
        stage.container().focus();
        console.log('Stage focused');
      }
    }
  };

  // Focus the stage on mount and center the view
  useEffect(() => {
    // Only proceed if this sheet is active
    const isActive = document.querySelector(`[data-sheet-id="${sheetId}"]`)?.classList.contains('active');
    if (!isActive) {
      console.log('MindMapCanvas: Not focusing or centering because sheet is not active:', sheetId);
      return;
    }

    // Focus with a delay to ensure the DOM is ready
    const timer = setTimeout(() => {
      focusStage();

      // Center the view if we have nodes
      if (storeRef.current) {
        console.log('MindMapCanvas: Centering view on mount for sheet:', sheetId);

        // Use the LayoutService to center the view
        layoutService.centerOnRoot(sheetId);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [sheetId]);

  // Listen for the mindmap_initialized event and refresh_canvas event
  useEffect(() => {
    // Handler for mindmap_initialized event
    const handleMindmapInitialized = (event: any) => {
      if (event.detail.sheetId === sheetId) {
        console.log('MindMapCanvas: Received mindmap_initialized event for sheet:', sheetId);
        
        // Layout should only be updated when explicitly requested by the user
        console.log('[MindMapCanvasRefactored] Not triggering automatic layout (governance compliance)');
      }
    };

    // Handler for refresh_canvas event
    const handleRefreshCanvas = (event: any) => {
      if (event.detail.sheetId === sheetId) {
        console.log('MindMapCanvas: Received refresh_canvas event for sheet:', sheetId);
        
        // Layout should only be updated when explicitly requested by the user
        console.log('[MindMapCanvasRefactored] Not triggering automatic layout (governance compliance)');
      }
    };

    // Add event listeners
    document.addEventListener('mindback:mindmap_initialized', handleMindmapInitialized);
    document.addEventListener('mindback:refresh_canvas', handleRefreshCanvas);

    // Clean up
    return () => {
      document.removeEventListener('mindback:mindmap_initialized', handleMindmapInitialized);
      document.removeEventListener('mindback:refresh_canvas', handleRefreshCanvas);
    };
  }, [sheetId]);

  // Handle stage drag start
  const handleDragStart = (e: any) => {
    // Only handle stage drag if no node is being dragged
    if (e.target === stageRef.current.getStage()) {
      console.log('Stage drag started');
    }
  };

  // Handle stage drag move
  const handleDragMove = (e: any) => {
    // Only handle stage drag if no node is being dragged
    if (e.target === stageRef.current.getStage()) {
      // Update position in the store
      if (storeRef.current) {
        const storeState = storeRef.current.getState();
        storeState.setPosition({
          x: e.target.x(),
          y: e.target.y()
        });
      }
    }
  };

  // Handle stage drag end
  const handleDragEnd = (e: any) => {
    // Only handle stage drag if no node is being dragged
    if (e.target === stageRef.current.getStage()) {
      console.log('Stage drag ended');
      
      // Save the state to ensure it's preserved when switching sheets
      mindSheetService.saveMindMapSheetState(sheetId);
    }
  };

  // Handle wheel event for zooming
  const handleWheel = (e: any) => {
    e.evt.preventDefault();

    // Get the stage
    const stage = stageRef.current.getStage();
    const oldScale = stage.scaleX();

    // Get pointer position
    const pointer = stage.getPointerPosition();
    const mousePointTo = {
      x: (pointer.x - stage.x()) / oldScale,
      y: (pointer.y - stage.y()) / oldScale
    };

    // Calculate new scale
    const newScale = e.evt.deltaY < 0 ? oldScale * 1.1 : oldScale / 1.1;

    // Limit scale
    const limitedScale = Math.max(0.1, Math.min(newScale, 3));

    // Update scale in the store
    if (storeRef.current) {
      const storeState = storeRef.current.getState();
      storeState.setScale(limitedScale);
      storeState.setPosition({
        x: pointer.x - mousePointTo.x * limitedScale,
        y: pointer.y - mousePointTo.y * limitedScale
      });
    }
  };

  // Handle key down events
  const handleKeyDown = (e: any) => {
    // Let the KeyboardManager handle key events
    // It will dispatch events that will be handled by the appropriate services
  };

  // Select a node using the MindObjectService
  const selectNode = (nodeId: string | null) => {
    if (nodeId) {
      mindObjectService.selectNode(sheetId, nodeId);
    } else {
      // Deselect by setting selectedNodeId to null in the store
      if (storeRef.current) {
        storeRef.current.getState().selectNode(null);
      }
    }
  };

  // Update a node using the MindObjectService
  const updateNode = (nodeId: string, updates: any) => {
    mindObjectService.updateNode(sheetId, nodeId, updates);
  };

  // Calculate initial position and scale
  const initialX = position ? position.x : 0;
  const initialY = position ? position.y : 0;

  // Render loading message if no nodes
  if (!nodes || Object.keys(nodes).length === 0) {
    console.log('MindMapCanvas: No nodes found, showing loading state for sheet:', sheetId);
    return (
      <div className="loading-canvas">
        <div className="initializing-mindmap">
          <div className="loading-spinner"></div>
          <p>Initializing canvas for sheet {sheetId}...</p>
          <button
            onClick={() => {
              console.log('Forcing sheet-specific store update for sheet:', sheetId);
              
              // Use the MindSheetService to initialize the mindmap
              mindSheetService.initializeMindMapSheet(sheetId, {
                text: 'New Mindmap',
                mindmap: {
                  root: {
                    text: 'New Mindmap',
                    children: []
                  }
                }
              });
            }}
          >
            Force Initialize
          </button>
        </div>
      </div>
    );
  }

  return (
    <StageCompatWrapper
      ref={stageRef}
      width={width}
      height={height}
      draggable
      x={initialX}
      y={initialY}
      scaleX={scale}
      scaleY={scale}
      onDragStart={handleDragStart}
      onDragMove={handleDragMove}
      onDragEnd={handleDragEnd}
      onWheel={handleWheel}
      onKeyDown={handleKeyDown}
      onClick={(e) => {
        // Only handle stage clicks, not node clicks
        if (e.target === stageRef.current.getStage()) {
          // Focus the stage on click
          if (stageRef.current) {
            stageRef.current.getStage().container().focus();
            console.log('Stage clicked and focused');

            // Deselect any selected node when clicking on empty space
            if (selectedNodeId) {
              selectNode(null);
              console.log('Deselected node on stage click');
            }
          }
        }
      }}
      tabIndex={0} // Make the stage focusable
    >
      <KonvaLayer>
        {/* Render connections */}
        <KonvaGroup>
          {connections.map((connection: any) => (
            <ConnectionComponent
              key={connection.id}
              connection={connection}
              fromNode={nodes[connection.from]}
              toNode={nodes[connection.to]}
              sheetId={sheetId}
            />
          ))}
        </KonvaGroup>

        {/* Render nodes */}
        <KonvaGroup>
          {Object.values(nodes).map((node: any) => (
            <NodeComponent
              key={node.id}
              node={node}
              isSelected={node.id === selectedNodeId}
              onClick={() => selectNode(node.id)}
              updateNode={updateNode}
              selectNode={selectNode}
              sheetId={sheetId}
            />
          ))}
        </KonvaGroup>
      </KonvaLayer>
    </StageCompatWrapper>
  );
};

export default MindMapCanvas;
