/**
 * ApplicationService.ts
 * 
 * Service layer for application-wide operations.
 * This service provides methods for interacting with the ApplicationStore
 * and handles application-level operations.
 * 
 * The service layer pattern helps break circular dependencies by providing
 * a single point of access for store operations without direct imports.
 */

import { useApplicationStore, ThemeType, UIPanelType, DialogType, DialogState } from '../state/ApplicationStore';
import { EventType } from './RegistrationManager';

/**
 * ApplicationService class
 * 
 * Singleton service for application-wide operations.
 */
class ApplicationService {
  private static instance: ApplicationService;

  private constructor() {
    // Private constructor to prevent direct instantiation
    console.log('ApplicationService: Initialized');
  }

  /**
   * Get the singleton instance of the ApplicationService
   */
  public static getInstance(): ApplicationService {
    if (!ApplicationService.instance) {
      ApplicationService.instance = new ApplicationService();
    }
    return ApplicationService.instance;
  }

  /**
   * Initialize the application
   */
  public initialize(): void {
    const store = useApplicationStore.getState();
    
    // Register window resize listener for mobile detection
    window.addEventListener('resize', this.handleResize);
    
    // Initialize the store
    store.initialize();
    
    // Log the initialization
    this.logEvent(EventType.APPLICATION_INITIALIZED, {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent
    });
    
    console.log('ApplicationService: Application initialized');
  }

  /**
   * Handle window resize events
   */
  private handleResize = (): void => {
    const isMobile = window.innerWidth < 768;
    const store = useApplicationStore.getState();
    
    // Update the mobile state in the UI
    store.setLoading(store.ui.isLoading, store.ui.loadingMessage);
    
    // If the UI state changes, log the event
    if (store.ui.isMobile !== isMobile) {
      this.logEvent(EventType.VIEWPORT_CHANGED, {
        width: window.innerWidth,
        height: window.innerHeight,
        isMobile
      });
    }
  };

  /**
   * Log an event to the application store
   */
  public logEvent(type: EventType, payload: any): void {
    useApplicationStore.getState().addEvent(type, payload);
  }

  /**
   * Set the application theme
   */
  public setTheme(theme: ThemeType): void {
    const store = useApplicationStore.getState();
    store.setTheme(theme);
    
    // Apply the theme to the document
    document.documentElement.setAttribute('data-theme', theme);
    
    // Log the theme change
    this.logEvent(EventType.THEME_CHANGED, { theme });
    
    console.log('ApplicationService: Theme set to', theme);
  }

  /**
   * Open a panel
   */
  public openPanel(panel: UIPanelType): void {
    const store = useApplicationStore.getState();
    store.openPanel(panel);
    
    // Log the panel open
    this.logEvent(EventType.PANEL_OPENED, { panel });
    
    console.log('ApplicationService: Panel opened', panel);
  }

  /**
   * Close a panel
   */
  public closePanel(panel: UIPanelType): void {
    const store = useApplicationStore.getState();
    store.closePanel(panel);
    
    // Log the panel close
    this.logEvent(EventType.PANEL_CLOSED, { panel });
    
    console.log('ApplicationService: Panel closed', panel);
  }

  /**
   * Collapse a panel
   */
  public collapsePanel(panel: UIPanelType): void {
    const store = useApplicationStore.getState();
    store.collapsePanel(panel);
    
    // Log the panel collapse
    this.logEvent(EventType.PANEL_COLLAPSED, { panel });
    
    console.log('ApplicationService: Panel collapsed', panel);
  }

  /**
   * Expand a panel
   */
  public expandPanel(panel: UIPanelType): void {
    const store = useApplicationStore.getState();
    store.expandPanel(panel);
    
    // Log the panel expand
    this.logEvent(EventType.PANEL_EXPANDED, { panel });
    
    console.log('ApplicationService: Panel expanded', panel);
  }

  /**
   * Set the position of a panel
   */
  public setPanelPosition(panel: UIPanelType, position: { x: number; y: number }): void {
    const store = useApplicationStore.getState();
    store.setPanelPosition(panel, position);
    
    // Log the panel position change
    this.logEvent(EventType.PANEL_MOVED, { panel, position });
    
    console.log('ApplicationService: Panel position set', panel, position);
  }

  /**
   * Show a dialog
   */
  public showDialog(dialog: DialogState): void {
    const store = useApplicationStore.getState();
    store.showDialog(dialog);
    
    // Log the dialog show
    this.logEvent(EventType.DIALOG_OPENED, { 
      type: dialog.type,
      title: dialog.title
    });
    
    console.log('ApplicationService: Dialog shown', dialog.type);
  }

  /**
   * Show a confirmation dialog
   */
  public showConfirmation(title: string, message: string, onConfirm: () => void, onCancel?: () => void): void {
    this.showDialog({
      type: DialogType.CONFIRMATION,
      title,
      message,
      onConfirm,
      onCancel
    });
  }

  /**
   * Show an alert dialog
   */
  public showAlert(title: string, message: string, onConfirm?: () => void): void {
    this.showDialog({
      type: DialogType.ALERT,
      title,
      message,
      onConfirm
    });
  }

  /**
   * Hide the current dialog
   */
  public hideDialog(): void {
    const store = useApplicationStore.getState();
    const dialogType = store.ui.dialog.type;
    
    store.hideDialog();
    
    // Log the dialog hide
    if (dialogType !== DialogType.NONE) {
      this.logEvent(EventType.DIALOG_CLOSED, { type: dialogType });
      console.log('ApplicationService: Dialog hidden', dialogType);
    }
  }

  /**
   * Set the loading state
   */
  public setLoading(isLoading: boolean, message: string = ''): void {
    const store = useApplicationStore.getState();
    store.setLoading(isLoading, message);
    
    // Log the loading state change
    this.logEvent(EventType.LOADING_STATE_CHANGED, { isLoading, message });
    
    console.log('ApplicationService: Loading state set', isLoading, message);
  }

  /**
   * Set the default LLM model
   */
  public setDefaultLLMModel(model: string): void {
    const store = useApplicationStore.getState();
    store.setDefaultLLMModel(model);
    
    // Log the model change
    this.logEvent(EventType.DEFAULT_MODEL_CHANGED, { model });
    
    console.log('ApplicationService: Default LLM model set to', model);
  }

  /**
   * Get the current user
   */
  public getCurrentUser() {
    return useApplicationStore.getState().user;
  }

  /**
   * Check if the user is authenticated
   */
  public isAuthenticated(): boolean {
    return useApplicationStore.getState().authenticated;
  }

  /**
   * Get the current theme
   */
  public getCurrentTheme(): ThemeType {
    return useApplicationStore.getState().preferences.theme;
  }

  /**
   * Get the default LLM model
   */
  public getDefaultLLMModel(): string {
    return useApplicationStore.getState().preferences.defaultLLMModel;
  }

  /**
   * Clean up resources when the application is unmounted
   */
  public cleanup(): void {
    // Remove event listeners
    window.removeEventListener('resize', this.handleResize);
    
    // Log the cleanup
    this.logEvent(EventType.APPLICATION_CLEANUP, {
      timestamp: new Date().toISOString()
    });
    
    console.log('ApplicationService: Resources cleaned up');
  }
}

// Export the singleton instance
export const applicationService = ApplicationService.getInstance();

// Export convenience functions
export const initializeApplication = () => applicationService.initialize();
export const setTheme = (theme: ThemeType) => applicationService.setTheme(theme);
export const openPanel = (panel: UIPanelType) => applicationService.openPanel(panel);
export const closePanel = (panel: UIPanelType) => applicationService.closePanel(panel);
export const collapsePanel = (panel: UIPanelType) => applicationService.collapsePanel(panel);
export const expandPanel = (panel: UIPanelType) => applicationService.expandPanel(panel);
export const setPanelPosition = (panel: UIPanelType, position: { x: number; y: number }) => 
  applicationService.setPanelPosition(panel, position);
export const showDialog = (dialog: DialogState) => applicationService.showDialog(dialog);
export const showConfirmation = (title: string, message: string, onConfirm: () => void, onCancel?: () => void) => 
  applicationService.showConfirmation(title, message, onConfirm, onCancel);
export const showAlert = (title: string, message: string, onConfirm?: () => void) => 
  applicationService.showAlert(title, message, onConfirm);
export const hideDialog = () => applicationService.hideDialog();
export const setLoading = (isLoading: boolean, message?: string) => 
  applicationService.setLoading(isLoading, message);
export const setDefaultLLMModel = (model: string) => applicationService.setDefaultLLMModel(model);
export const getCurrentUser = () => applicationService.getCurrentUser();
export const isAuthenticated = () => applicationService.isAuthenticated();
export const getCurrentTheme = () => applicationService.getCurrentTheme();
export const getDefaultLLMModel = () => applicationService.getDefaultLLMModel();
export const cleanupApplication = () => applicationService.cleanup();
