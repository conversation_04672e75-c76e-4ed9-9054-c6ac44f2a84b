// MindMap/Connection.tsx
import React from 'react';
import { Node } from './types';

interface ConnectionProps {
  fromNode: Node;
  toNode: Node;
}

export const Connection: React.FC<ConnectionProps> = ({ fromNode, toNode }) => {
  const fromX = fromNode.x + fromNode.width / 2;
  const fromY = fromNode.y + fromNode.height / 2;
  const toX = toNode.x + toNode.width / 2;
  const toY = toNode.y + toNode.height / 2;
  
  // Calculate line length
  const dx = toX - fromX;
  const dy = toY - fromY;
  const length = Math.sqrt(dx * dx + dy * dy);
  
  // Calculate angle
  const angle = Math.atan2(dy, dx) * 180 / Math.PI;
  
  return (
    <div
      className="connection-line"
      style={{
        position: 'absolute',
        left: `${fromX}px`,
        top: `${fromY}px`,
        width: `${length}px`,
        height: '2px',
        backgroundColor: '#adb5bd',
        transformOrigin: '0 0',
        transform: `rotate(${angle}deg)`,
        zIndex: 1
      }}
    />
  );
};