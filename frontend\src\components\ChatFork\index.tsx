import React, { useState, useEffect, useRef } from 'react';
import { ChatResponse } from '../../services/api/GovernanceLLM';
import RegistrationManager, { EventType } from '../../core/services/RegistrationManager';
import './ChatFork.css';
import ChatForkNode from './ChatForkNode';
import ChatForkConnection from './ChatForkConnection';

interface ChatForkProps {
  chatResponse: ChatResponse | null;
  isVisible: boolean;
  onNodeClick: (nodeId: string) => void;
  onNodeFork: (nodeId: string, parentId: string, type: 'question' | 'discussion') => void;
}

export interface ChatForkNodeData {
  id: string;
  type: 'title' | 'explanation' | 'section' | 'subpoint' | 'discussion' | 'user_input' | 'content';
  text: string;
  parentId?: string;
  level: number;
  index: number;
  discussionPoints?: Array<{
    question: string;
    context: string;
  }>;
  intent?: string;
  agent?: string;
}

export interface ChatForkConnectionData {
  id: string;
  fromId: string;
  toId: string;
}

const ChatFork: React.FC<ChatForkProps> = ({ 
  chatResponse, 
  isVisible, 
  onNodeClick, 
  onNodeFork 
}) => {
  const [nodes, setNodes] = useState<ChatForkNodeData[]>([]);
  const [connections, setConnections] = useState<ChatForkConnectionData[]>([]);
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [userInput, setUserInput] = useState<string>('');
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!chatResponse?.templateOutput) return;
    
    console.log('Processing chat response for ChatFork:', chatResponse);
    
    const newNodes: ChatForkNodeData[] = [];
    const newConnections: ChatForkConnectionData[] = [];
    
    const templateOutput = chatResponse.templateOutput;
    
    try {
      // Handle MBCP format (check for root node structure)
      if (templateOutput.chatfork && templateOutput.chatfork.root) {
        // Handle nested MBCP format
        const mbcpData = templateOutput.chatfork;
        const root = mbcpData.root;
        
        // Add title node
        newNodes.push({
          id: root.id || '0',
          type: 'title',
          text: root.text,
          level: 0,
          index: 0
        });
    
        // Add main explanation
        if (root.description) {
          const explanationId = `${root.id || '0'}.explanation`;
          newNodes.push({
            id: explanationId,
            type: 'explanation',
            text: root.description,
            parentId: root.id || '0',
            level: 1,
            index: 1
          });
          
          newConnections.push({
            id: `conn-${root.id || '0'}-${explanationId}`,
            fromId: root.id || '0',
            toId: explanationId
          });
        }
        
        // Process children nodes
        if (root.children && root.children.length > 0) {
          root.children.forEach((child, index) => {
            // Add section node
            const sectionId = child.id || `section_${index + 1}`;
            newNodes.push({
              id: sectionId,
              type: 'section',
              text: child.text,
              parentId: root.id || '0',
              level: 1,
              index: index + 2, // Start after explanation
              intent: child.intent,
              agent: child.agent
            });
            
            newConnections.push({
              id: `conn-${root.id || '0'}-${sectionId}`,
              fromId: root.id || '0',
              toId: sectionId
            });
            
            // Add description for the section
            if (child.description) {
              const descriptionId = `${sectionId}.description`;
              newNodes.push({
                id: descriptionId,
                type: 'content',
                text: child.description,
                parentId: sectionId,
                level: 2,
                index: index * 10 // Ensure space between sections
              });
              
              newConnections.push({
                id: `conn-${sectionId}-${descriptionId}`,
                fromId: sectionId,
                toId: descriptionId
              });
            }
          });
        }
      } else if (templateOutput.root) {
        // Direct MBCP format
        const root = templateOutput.root;
        
        // Add title node
        newNodes.push({
          id: root.id || '0',
          type: 'title',
          text: root.text,
          level: 0,
          index: 0
        });
    
        // Add main explanation
        if (root.description) {
          const explanationId = `${root.id || '0'}.explanation`;
          newNodes.push({
            id: explanationId,
            type: 'explanation',
            text: root.description,
            parentId: root.id || '0',
            level: 1,
            index: 1
          });
          
          newConnections.push({
            id: `conn-${root.id || '0'}-${explanationId}`,
            fromId: root.id || '0',
            toId: explanationId
          });
        }
        
        // Process children nodes
        if (root.children && root.children.length > 0) {
          root.children.forEach((child, index) => {
            // Add section node
            const sectionId = child.id || `section_${index + 1}`;
            newNodes.push({
              id: sectionId,
              type: 'section',
              text: child.text,
              parentId: root.id || '0',
              level: 1,
              index: index + 2, // Start after explanation
              intent: child.intent,
              agent: child.agent
            });
            
            newConnections.push({
              id: `conn-${root.id || '0'}-${sectionId}`,
              fromId: root.id || '0',
              toId: sectionId
            });
            
            // Add description for the section
            if (child.description) {
              const descriptionId = `${sectionId}.description`;
              newNodes.push({
                id: descriptionId,
                type: 'content',
                text: child.description,
                parentId: sectionId,
                level: 2,
                index: index * 10 // Ensure space between sections
              });
              
              newConnections.push({
                id: `conn-${sectionId}-${descriptionId}`,
                fromId: sectionId,
                toId: descriptionId
              });
            }
          });
        }
      } else {
        // Legacy format processing
        // Add title node
        const title = templateOutput.title || 
                    (templateOutput.content && templateOutput.content.title) || 
                    chatResponse.text || 
                    'Topic';
        
        newNodes.push({
          id: '0',
          type: 'title',
          text: title,
          level: 0,
          index: 0
        });
    
        // Add main explanation
        const mainExplanation = templateOutput.main_explanation || 
                              (templateOutput.content && templateOutput.content.main_explanation) ||
                              templateOutput.explanation;
        
        if (mainExplanation) {
          const explanationId = '0.1';
          newNodes.push({
            id: explanationId,
            type: 'explanation',
            text: mainExplanation,
            parentId: '0',
            level: 1,
            index: 1
          });
          
          newConnections.push({
            id: `conn-0-${explanationId}`,
            fromId: '0',
            toId: explanationId
          });
        }
        
        // Process sections
        const sections = templateOutput.sections || 
                        (templateOutput.content && templateOutput.content.sections) || [];
        
        sections.forEach((section, index) => {
          // Handle different section formats
          const heading = section.heading || section.title || `Section ${index+1}`;
          const content = section.content || section.description || section.text || '';
          
          // Add section node
          const sectionId = `${index + 1}`;
          newNodes.push({
            id: sectionId,
            type: 'section',
            text: `${heading}: ${content}`,
            parentId: '0',
            level: 1,
            index: index + 2,
            discussionPoints: section.discussion_points,
            intent: section.intent,
            agent: section.agent
          });
          
          newConnections.push({
            id: `conn-0-${sectionId}`,
            fromId: '0',
            toId: sectionId
          });
  
          // Add subpoints
          const subpoints = section.subpoints || section.points || [];
          if (Array.isArray(subpoints)) {
            subpoints.forEach((subpoint, j) => {
              const subpointId = `${sectionId}.${j + 1}`;
              
              // Handle different subpoint formats
              const title = subpoint.title || '';
              const subContent = subpoint.content || subpoint.text || subpoint.description || '';
              const displayText = title ? `${title}: ${subContent}` : subContent;
              
              newNodes.push({
                id: subpointId,
                type: 'subpoint',
                text: displayText,
                parentId: sectionId,
                level: 2,
                index: j + 1,
                intent: subpoint.intent,
                agent: subpoint.agent
              });
              
              newConnections.push({
                id: `conn-${sectionId}-${subpointId}`,
                fromId: sectionId,
                toId: subpointId
              });
            });
          }
  
          // Add discussion points
          const discussionPoints = section.discussion_points || [];
          if (Array.isArray(discussionPoints)) {
            discussionPoints.forEach((point, k) => {
              const discussionId = `${sectionId}.d${k + 1}`;
              
              const question = point.question || '';
              const context = point.context || point.description || '';
              
              newNodes.push({
                id: discussionId,
                type: 'discussion',
                text: `Q: ${question}\nContext: ${context}`,
                parentId: sectionId,
                level: 2,
                index: k + 1,
                intent: point.intent,
                agent: point.agent
              });
              
              newConnections.push({
                id: `conn-${sectionId}-${discussionId}`,
                fromId: sectionId,
                toId: discussionId
              });
            });
          }
        });
      }
      
      console.log('ChatFork processed nodes:', newNodes.length);
      console.log('ChatFork processed connections:', newConnections.length);
      
      setNodes(newNodes);
      setConnections(newConnections);
    } catch (error) {
      console.error('Error processing ChatFork data:', error);
    }
  }, [chatResponse]);

  const handleNodeClick = (nodeId: string) => {
    setSelectedNodeId(nodeId === selectedNodeId ? null : nodeId);
    onNodeClick(nodeId);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Tab' && selectedNodeId) {
      e.preventDefault();
      
      const selectedNode = nodes.find(node => node.id === selectedNodeId);
      if (!selectedNode) return;
      
      const parentId = selectedNodeId;
      const childCount = nodes.filter(n => n.parentId === parentId).length;
      const newId = `${parentId}.${childCount + 1}`;
      
      const newNode: ChatForkNodeData = {
        id: newId,
        type: 'user_input',
        text: userInput || 'New discussion point',
        parentId,
        level: selectedNode.level + 1,
        index: childCount + 1,
        intent: selectedNode.intent,
        agent: selectedNode.agent
      };
      
      const newConnection: ChatForkConnectionData = {
        id: `conn-${parentId}-${newId}`,
        fromId: parentId,
        toId: newId
      };
      
      setNodes([...nodes, newNode]);
      setConnections([...connections, newConnection]);
      setSelectedNodeId(newId);
      
      // Determine if this is a question or discussion based on the parent node
      const forkType = selectedNode.type === 'discussion' ? 'question' : 'discussion';
      onNodeFork(newId, parentId, forkType);
      
      // Register the fork creation event
      RegistrationManager.registerEvent(EventType.CHATFORK_FORK_CREATED, {
        topic: userInput || 'New discussion point',
        text: userInput || 'New discussion point',
        method: 'tab_key_fork',
        parentId: parentId,
        nodeId: newId,
        forkType: forkType,
        timestamp: new Date().toISOString()
      });
      
      // Clear user input after forking
      setUserInput('');
    }
  };

  if (!isVisible) return null;

  return (
    <div 
      className="chat-fork-container" 
      ref={containerRef}
      tabIndex={0}
      onKeyDown={handleKeyDown}
    >
      <div className="chat-fork-nodes">
        {nodes.map(node => (
          <ChatForkNode
            key={node.id}
            node={node}
            isSelected={node.id === selectedNodeId}
            onClick={() => handleNodeClick(node.id)}
          />
        ))}
      </div>
      
      <div className="chat-fork-connections">
        {connections.map(connection => (
          <ChatForkConnection
            key={connection.id}
            connection={connection}
            nodes={nodes}
          />
        ))}
      </div>
      
      {selectedNodeId && (
        <div className="chat-fork-input">
          <input
            type="text"
            value={userInput}
            onChange={(e) => setUserInput(e.target.value)}
            placeholder="Type your response and press Tab to fork..."
            className="chat-fork-input-field"
          />
        </div>
      )}
    </div>
  );
};

export default ChatFork; 