"""
Simple API key test script.
Tests loading from the single source of truth: root .env file.
"""
import os
import sys
from dotenv import load_dotenv
from pathlib import Path

print(f"Python version: {sys.version}")
print(f"Current working directory: {os.getcwd()}")

# Load from root .env file only (single source of truth)
root_dir = Path(__file__).resolve().parent.parent
env_path = root_dir / '.env'

print(f"\n📁 Loading .env from: {env_path.absolute()}")
print(f"📁 File exists: {env_path.exists()}")

if env_path.exists():
    try:
        load_dotenv(dotenv_path=env_path)
        print(f"✅ Successfully loaded .env from {env_path.absolute()}")
    except Exception as e:
        print(f"❌ Error loading .env: {e}")
        sys.exit(1)
else:
    print(f"❌ CRITICAL: .env file not found at {env_path.absolute()}")
    sys.exit(1)

# Check the API key
api_key = os.getenv("OPENAI_API_KEY")
if api_key:
    print(f"✅ API key found with length: {len(api_key)}")
    print(f"First 4 characters: {api_key[:4]}")
    print(f"Last 4 characters: {api_key[-4:]}")

    # Basic validation
    if len(api_key) < 20:
        print("⚠️  WARNING: API key seems too short")
    if "your_" in api_key.lower() or "placeholder" in api_key.lower():
        print("⚠️  WARNING: API key appears to be a placeholder")
else:
    print("❌ CRITICAL: No OpenAI API key found in environment")
    sys.exit(1)

# Test OpenAI client initialization
print("\n🔧 Testing OpenAI client initialization:")
try:
    import openai
    openai.api_key = api_key
    print("✅ OpenAI client initialized successfully")
except Exception as e:
    print(f"❌ Error initializing OpenAI client: {e}")

# Test our settings module
print("\n🔧 Testing MindBack settings module:")
try:
    from api.config.settings import get_settings
    settings = get_settings()
    if settings.openai_api_key:
        print(f"✅ Settings loaded API key successfully (length: {len(settings.openai_api_key)})")
    else:
        print("❌ Settings did not load API key")
except Exception as e:
    print(f"❌ Error loading settings: {e}")

print("\n🎉 API key test completed!")

print("\nTest complete.") 