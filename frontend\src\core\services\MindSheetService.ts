/**
 * MindSheetService.ts
 * 
 * Service layer for MindSheet operations.
 * This service provides methods for interacting with MindSheets
 * and handles sheet-level operations.
 * 
 * The service layer pattern helps break circular dependencies by providing
 * a single point of access for operations without direct imports.
 */

import { MindSheetContentType } from '../state/StoreTypes';
import RegistrationManager, { EventType } from './RegistrationManager';
import { mindBookService } from './MindBookService';
import { applicationService } from './ApplicationService';
import { getMindMapStore, hasMindMapStore, saveMindMapStoreState } from '../state/MindMapStoreFactory';

/**
 * MindSheetService class
 * 
 * Singleton service for MindSheet operations.
 */
class MindSheetService {
  private static instance: MindSheetService;

  private constructor() {
    // Private constructor to prevent direct instantiation
    console.log('MindSheetService: Initialized');
  }

  /**
   * Get the singleton instance of the MindSheetService
   */
  public static getInstance(): MindSheetService {
    if (!MindSheetService.instance) {
      MindSheetService.instance = new MindSheetService();
    }
    return MindSheetService.instance;
  }

  /**
   * Initialize a MindMap sheet with content
   */
  public initializeMindMapSheet(sheetId: string, mbcpData: any): boolean {
    try {
      console.log('MindSheetService: Initializing mindmap with content for sheet:', sheetId);

      // Get the sheet-specific store
      const store = getMindMapStore(sheetId);
      
      // Get the store state directly
      const storeState = store.getState();

      // Extract data from mbcpData safely
      if (!mbcpData) {
        console.error('MindSheetService: No MBCP data provided');
        return false;
      }

      // Log the MBCP data structure for debugging
      try {
        console.log('MindSheetService: MBCP data structure:', JSON.stringify(mbcpData, null, 2));
      } catch (e) {
        console.warn('MindSheetService: Could not stringify MBCP data');
      }

      // Handle different MBCP data formats
      let rootNode = null;
      let rootNodeText = 'Untitled';

      // Check for different possible MBCP data structures
      if (mbcpData.mindmap && mbcpData.mindmap.root) {
        // Format: { mindmap: { root: {...} } }
        rootNode = mbcpData.mindmap.root;
        rootNodeText = rootNode.text || mbcpData.text || 'Untitled';
      } else if (mbcpData.root) {
        // Format: { root: {...} }
        rootNode = mbcpData.root;
        rootNodeText = rootNode.text || mbcpData.text || 'Untitled';
      } else if (typeof mbcpData.text === 'string') {
        // Format: { text: "..." }
        rootNodeText = mbcpData.text;
      } else {
        // Fallback for any other format
        rootNodeText = 'Teleological Mindmap';
        console.warn('MindSheetService: Using fallback root node text for mindmap initialization');
      }

      // Create a new project with the root node text
      console.log('MindSheetService: Creating new project with root node text:', rootNodeText);
      const rootNodeId = storeState.createNewProject(rootNodeText);

      if (!rootNodeId) {
        console.error('MindSheetService: Failed to create root node');
        return false;
      }

      // If we have a root node with children, create them
      if (rootNode && rootNode.children && Array.isArray(rootNode.children)) {
        console.log('MindSheetService: Creating child nodes from MBCP data');
        this.processChildNodes(storeState, rootNodeId, rootNode.children);
      }

      // Update the layout
      setTimeout(() => {
        console.log('MindSheetService: Updating layout for sheet:', sheetId);
        storeState.updateLayout('tree');
      }, 100);

      // Save the state to the MindBookStore
      setTimeout(() => {
        this.saveMindMapSheetState(sheetId);
      }, 200);

      // Log the initialization
      RegistrationManager.registerEvent(EventType.MINDMAP_INITIALIZED, {
        id: sheetId,
        type: 'mindmap'
      });

      return true;
    } catch (error) {
      console.error('MindSheetService: Error initializing mindmap:', error);
      
      // Log the error
      RegistrationManager.registerEvent(EventType.ERROR_OCCURRED, {
        component: 'MindSheetService',
        method: 'initializeMindMapSheet',
        message: error.message,
        stack: error.stack
      });
      
      return false;
    }
  }

  /**
   * Process child nodes for a mindmap
   */
  private processChildNodes(storeState: any, parentNodeId: string, children: any[]): void {
    if (!children || !Array.isArray(children)) return;

    children.forEach((child, index) => {
      if (!child.text) return;

      // Create the child node
      const childNodeId = storeState.addNode({
        text: child.text,
        parent: parentNodeId,
        index
      });

      // Process any children of this child
      if (child.children && Array.isArray(child.children)) {
        this.processChildNodes(storeState, childNodeId, child.children);
      }
    });
  }

  /**
   * Activate a MindSheet
   */
  public activateSheet(sheetId: string): void {
    // Set the active sheet in the MindBookStore
    mindBookService.setActiveSheet(sheetId);
    
    // Get the sheet
    const sheet = mindBookService.getSheetById(sheetId);
    if (!sheet) {
      console.error('MindSheetService: Cannot activate non-existent sheet:', sheetId);
      return;
    }
    
    // Log the activation
    RegistrationManager.registerEvent(EventType.SHEET_ACTIVATED, {
      id: sheetId,
      type: sheet.contentType
    });
    
    console.log('MindSheetService: Sheet activated', sheetId, sheet.contentType);
  }

  /**
   * Deactivate a MindSheet
   */
  public deactivateSheet(sheetId: string): void {
    // Get the sheet
    const sheet = mindBookService.getSheetById(sheetId);
    if (!sheet) {
      console.error('MindSheetService: Cannot deactivate non-existent sheet:', sheetId);
      return;
    }
    
    // If this is a mindmap sheet, save its state before deactivation
    if (sheet.contentType === MindSheetContentType.MINDMAP) {
      this.saveMindMapSheetState(sheetId);
    }
    
    // Log the deactivation
    RegistrationManager.registerEvent(EventType.SHEET_DEACTIVATED, {
      id: sheetId,
      type: sheet.contentType
    });
    
    console.log('MindSheetService: Sheet deactivated', sheetId, sheet.contentType);
  }

  /**
   * Save the state of a MindMap sheet
   */
  public saveMindMapSheetState(sheetId: string): void {
    try {
      // Check if the sheet exists
      if (!hasMindMapStore(sheetId)) {
        console.warn('MindSheetService: Cannot save state for non-existent mindmap store:', sheetId);
        return;
      }
      
      // Save the state
      saveMindMapStoreState(sheetId);
      
      console.log('MindSheetService: MindMap sheet state saved', sheetId);
    } catch (error) {
      console.error('MindSheetService: Error saving mindmap sheet state:', error);
      
      // Log the error
      RegistrationManager.registerEvent(EventType.ERROR_OCCURRED, {
        component: 'MindSheetService',
        method: 'saveMindMapSheetState',
        message: error.message,
        stack: error.stack
      });
    }
  }

  /**
   * Get the MindMap store for a sheet
   */
  public getMindMapSheetStore(sheetId: string): any {
    try {
      return getMindMapStore(sheetId);
    } catch (error) {
      console.error('MindSheetService: Error getting mindmap sheet store:', error);
      
      // Log the error
      RegistrationManager.registerEvent(EventType.ERROR_OCCURRED, {
        component: 'MindSheetService',
        method: 'getMindMapSheetStore',
        message: error.message,
        stack: error.stack
      });
      
      return null;
    }
  }

  /**
   * Check if a MindMap store exists for a sheet
   */
  public hasMindMapSheetStore(sheetId: string): boolean {
    try {
      return hasMindMapStore(sheetId);
    } catch (error) {
      console.error('MindSheetService: Error checking mindmap sheet store:', error);
      
      // Log the error
      RegistrationManager.registerEvent(EventType.ERROR_OCCURRED, {
        component: 'MindSheetService',
        method: 'hasMindMapSheetStore',
        message: error.message,
        stack: error.stack
      });
      
      return false;
    }
  }

  /**
   * Create a new MindSheet
   */
  public createSheet(title: string, contentType: MindSheetContentType, content: any): string {
    // Show loading indicator
    applicationService.setLoading(true, `Creating ${contentType} sheet: ${title}`);
    
    try {
      // Create the sheet
      const sheetId = mindBookService.createSheet(title, contentType, content);
      
      // Initialize the sheet based on content type
      if (contentType === MindSheetContentType.MINDMAP) {
        // Initialize the mindmap
        this.initializeMindMapSheet(sheetId, content);
      }
      
      return sheetId;
    } finally {
      // Hide loading indicator
      applicationService.setLoading(false);
    }
  }
}

// Export the singleton instance
export const mindSheetService = MindSheetService.getInstance();

// Export convenience functions
export const initializeMindMapSheet = (sheetId: string, mbcpData: any) => 
  mindSheetService.initializeMindMapSheet(sheetId, mbcpData);
export const activateSheet = (sheetId: string) => 
  mindSheetService.activateSheet(sheetId);
export const deactivateSheet = (sheetId: string) => 
  mindSheetService.deactivateSheet(sheetId);
export const saveMindMapSheetState = (sheetId: string) => 
  mindSheetService.saveMindMapSheetState(sheetId);
export const getMindMapSheetStore = (sheetId: string) => 
  mindSheetService.getMindMapSheetStore(sheetId);
export const hasMindMapSheetStore = (sheetId: string) => 
  mindSheetService.hasMindMapSheetStore(sheetId);
export const createSheet = (title: string, contentType: MindSheetContentType, content: any) => 
  mindSheetService.createSheet(title, contentType, content);
