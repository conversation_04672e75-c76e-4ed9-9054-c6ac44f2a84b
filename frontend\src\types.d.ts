declare module 'react-router-dom' {
  import { ComponentType, ReactNode } from 'react';

  export interface RouteProps {
    path?: string;
    element?: React.ReactNode;
    children?: React.ReactNode;
  }

  export interface LinkProps {
    to: string;
    replace?: boolean;
    children?: React.ReactNode;
  }

  export const BrowserRouter: ComponentType<{ children?: ReactNode }>;
  export const Routes: ComponentType<{ children?: ReactNode }>;
  export const Route: ComponentType<RouteProps>;
  export const Link: ComponentType<LinkProps>;
} 