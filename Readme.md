# MindBack - Mind Mapping Application

A modern mind mapping application with a React frontend and optional FastAPI backend.

## Project Structure

- `frontend/` - React TypeScript frontend application
- `backend/` - FastAPI Python backend (optional)

## Frontend Setup

### Prerequisites
- Node.js 20 LTS (recommended) or Node.js 18 LTS
- npm 10+ or yarn

1. Navigate to the frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

The frontend should be available at http://localhost:5173/

## Backend Setup (Optional)

The application can work without a backend connection. If you want to use the backend:

1. Navigate to the backend directory:
```bash
cd backend
```

2. Create a virtual environment:
```bash
python -m venv venv
```

3. Activate the virtual environment:
- Windows:
```bash
venv\Scripts\activate
```
- macOS/Linux:
```bash
source venv/bin/activate
```

4. Install dependencies:
```bash
pip install -r requirements.txt
```

5. Start the backend server:
```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```
Alternatevly run the setup script run_setup.ps1: .\run_setup.ps1

The backend should be available at http://localhost:8000/

## Features

- Create and edit mind maps with a user-friendly interface
- Customize node appearance and connections
- Save and load projects
- Works offline with localStorage backup
- Keyboard shortcuts for efficient editing

## Troubleshooting

### Frontend Issues

- If you see TypeScript warnings, they can be safely ignored as they don't affect functionality
- If the server doesn't start, check if port 5173 is already in use
- The application will work with sample projects even if the backend is not available
- For best compatibility, use Node.js 20 LTS. Using newer versions (e.g., Node.js 22+) may cause unexpected issues

### Backend Issues

- If you get connection errors, make sure the backend server is running
- Check that the virtual environment is activated before starting the server

## Development

- The frontend uses React with TypeScript and Konva.js for canvas rendering
- The backend uses FastAPI with SQLAlchemy for database operations
- All changes are saved to localStorage as a backup

## Directory Structure

The MindBack application uses the following directory structure:

```
frontend/src/
├── governance/            # Governance system for the application
│   ├── chat/              # GovernanceChatDialog and related components
│   ├── agents/            # Individual specialized agents
│   ├── templates/         # Response templates
│   └── rag/               # Retrieval-augmented generation
├── components/            # React components
│   ├── MindMap/           # Mind mapping components
│   ├── ChatFork/          # Chat fork functionality
│   └── shared/            # Shared UI components
├── services/              # Application services
│   ├── api/               # API clients
│   └── transformers/      # Data transformation
└── store/                 # Global state management
```
