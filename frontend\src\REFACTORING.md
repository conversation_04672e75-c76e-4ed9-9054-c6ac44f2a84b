# MindBack Refactoring

This document outlines the refactoring of the MindBack application to eliminate circular dependencies and establish a clear architecture with independent components.

## Architecture Overview

The refactored architecture consists of three main components:

1. **Governance Box**: Handles user interaction and moderation
2. **MindMap Workflow**: Manages the mind map creation and editing
3. **AI Agents**: Specialized agents for different tasks (to be implemented)

These components are designed to be independent, with clear interfaces for communication between them.

## Directory Structure

```
frontend/
├── src/
│   ├── core/                  # Core functionality shared across features
│   │   ├── adapters/          # Adapters for communication between features
│   │   ├── state/             # Global state management
│   │   └── types/             # Shared types
│   │
│   ├── features/              # Feature modules
│   │   ├── governance/        # Governance box feature
│   │   │   ├── components/    # UI components
│   │   │   ├── hooks/         # Custom hooks
│   │   │   └── types.ts       # Feature-specific types
│   │   │
│   │   └── mindmap/           # MindMap feature
│   │       ├── components/    # UI components
│   │       ├── hooks/         # Custom hooks
│   │       └── types.ts       # Feature-specific types
│   │
│   ├── App.tsx                # Main application component
│   └── index.tsx              # Entry point
```

## Key Changes

### 1. Consolidated State Management

- Created a single source of truth for MindMap state in `core/state/MindMapStore.ts`
- Used a consistent data structure (Record-based nodes)
- Implemented proper typing for all state

### 2. Clear Component Hierarchy

- Separated components into layers:
  - **Core Layer**: Pure business logic, no UI dependencies
  - **UI Layer**: Presentation components that consume the core layer
  - **Container Layer**: Components that connect UI to state

### 3. Independent Governance Module

- Created a separate governance module in `features/governance`
- Defined clear interfaces for communication with MindMap
- Used adapters to convert between different data formats

### 4. Unidirectional Data Flow

- State flows down from store to components
- Actions flow up from components to store
- No circular dependencies between components

## How to Use the Refactored Code

To use the refactored code, you can import the new components from their respective modules:

```tsx
// Import the MindMap feature
import MindMap from './features/mindmap/MindMap';

// Import the Governance feature
import GovernanceChat from './features/governance/GovernanceChat';

// Import the adapter for communication
import { processGovernanceAction } from './core/adapters/GovernanceMindMapAdapter';
```

The `AppRefactored.tsx` component shows how to integrate these features together.

## Migration Plan

To migrate to the new architecture:

1. Start using the new components in new features
2. Gradually replace old components with new ones
3. Once all components are migrated, remove the old code

## Future Improvements

- Implement AI agents as independent modules
- Add support for nested intentions across different canvas sheets
- Improve the adapter pattern for more flexible communication between modules
