# ChatFork Component

## Features

### Sticky Text Selection

The ChatFork component now supports "sticky" text selection, which allows users to:

1. Select text by clicking and dragging
2. Have the selection remain highlighted after the mouse is released
3. Create a new fork node from the selected text using the button or by pressing Shift+Tab

## Implementation Details

The sticky text selection feature is implemented across these files:

- `ChatForkView.tsx`: The main component that handles text selection and rendering
- `ChatForkStore.ts`: The state store that maintains the selected text
- `ChatForkAdapter.ts`: The adapter that handles actions related to text selection
- `ChatFork.css`: Contains styling for the highlighted text

### Key Functionality

1. **Text Selection**
   - When text is selected, it's stored in the ChatForkStore
   - The selection is visually highlighted using a DOM manipulation approach
   - The highlight remains active until cleared or a new selection is made

2. **Keyboard Shortcuts**
   - Shift+Tab: Creates a fork from the selected text

3. **Selection Highlighting**
   - Selected text is wrapped in a `<span>` with the class `highlight-selection`
   - The DOM is scanned to find and highlight matching text nodes

## Testing

You can test the sticky text selection feature using the provided test document:
`TestContent.md`

## Troubleshooting

If text selection is not working:

1. Check browser console for any errors
2. Verify that the ChatForkView component is properly mounted
3. Ensure the ChatForkStore and ChatForkAdapter are correctly imported

## Future Improvements

- Add undo/redo functionality for text selection
- Support for selecting multiple text fragments
- Implement advanced formatting options for selected text 