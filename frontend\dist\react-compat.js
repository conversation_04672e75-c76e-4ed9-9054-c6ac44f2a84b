// React compatibility script for browser
(function() {
  try {
    console.log('Initializing React compatibility layer...');
    
    // Create a safe React reference
    const safeReact = window.React || {};
    
    // Create a safe internals object if it doesn't exist
    if (!safeReact.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) {
      safeReact.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = {};
    }
    
    const internals = safeReact.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
    
    // Create ReactCurrentDispatcher if it doesn't exist
    if (!internals.ReactCurrentDispatcher) {
      internals.ReactCurrentDispatcher = {
        current: {
          useState: function(initialState) {
            console.warn('Using polyfilled useState');
            return [
              typeof initialState === 'function' ? initialState() : initialState,
              function() { console.warn('setState called from polyfill'); }
            ];
          },
          useEffect: function(effect, deps) {
            console.warn('Using polyfilled useEffect');
            // No-op implementation
          },
          useContext: function(context) {
            console.warn('Using polyfilled useContext');
            return context._currentValue;
          },
          useReducer: function(reducer, initialArg, init) {
            console.warn('Using polyfilled useReducer');
            const initialState = init ? init(initialArg) : initialArg;
            return [initialState, function() { console.warn('dispatch called from polyfill'); }];
          },
          useRef: function(initialValue) {
            console.warn('Using polyfilled useRef');
            return { current: initialValue };
          },
          useLayoutEffect: function() {
            console.warn('Using polyfilled useLayoutEffect');
            // No-op implementation
          },
          useCallback: function(callback, deps) {
            console.warn('Using polyfilled useCallback');
            return callback;
          },
          useMemo: function(create, deps) {
            console.warn('Using polyfilled useMemo');
            return create();
          },
          useImperativeHandle: function() {
            console.warn('Using polyfilled useImperativeHandle');
            // No-op implementation
          },
          useDebugValue: function() {
            // No-op implementation
          },
          useDeferredValue: function(value) {
            return value;
          },
          useTransition: function() {
            return [false, function() {}];
          },
          useId: function() {
            return 'polyfill-id-' + Math.random().toString(36).substr(2, 9);
          },
          useSyncExternalStore: function(subscribe, getSnapshot) {
            console.warn('Using polyfilled useSyncExternalStore');
            return getSnapshot();
          },
          useInternalStore: function(subscribe, getSnapshot) {
            console.warn('Using polyfilled useInternalStore');
            return getSnapshot();
          }
        }
      };
    } else if (!internals.ReactCurrentDispatcher.current) {
      internals.ReactCurrentDispatcher.current = {};
    }
    
    // Ensure the current dispatcher has useInternalStore
    if (internals.ReactCurrentDispatcher.current && !internals.ReactCurrentDispatcher.current.useInternalStore) {
      internals.ReactCurrentDispatcher.current.useInternalStore = function(subscribe, getSnapshot) {
        console.warn('Using polyfilled useInternalStore');
        return getSnapshot();
      };
    }
    
    // Update window.React
    window.React = safeReact;
    
    console.log('React compatibility layer initialized');
  } catch (error) {
    console.error('Error initializing React compatibility layer:', error);
  }
})();
