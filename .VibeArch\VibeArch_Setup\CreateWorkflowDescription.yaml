---
# META-PROMPT: Generate Comprehensive Workflow Description
# This file contains instructions to create a complete workflowDescription.yaml

prompt_metadata:
  purpose: "Generate comprehensive workflow documentation for MindBack application"
  target_output: "workflowDescription.yaml"
  analysis_scope: "Complete codebase workflow mapping and dead code detection"
  approach: "Professional software architecture documentation standards"

# =====================================================
# ANALYSIS INSTRUCTIONS
# =====================================================
analysis_requirements:
  
  input_data_sources:
    primary: "mvcd.yaml - Complete codebase inventory with file paths, dependencies, and metadata"
    secondary: "Actual codebase structure and file organization"
    context: "Windows-based development environment with React frontend and FastAPI backend"
  
  analysis_methodology:
    1. "Parse mvcd.yaml to understand complete codebase structure"
    2. "Identify entry points and main application flows"
    3. "Trace code execution paths from user actions to system responses"
    4. "Map inter-component dependencies and data flows"
    5. "Detect unused, deprecated, and duplicate code"
    6. "Calculate workflow coverage metrics"

# =====================================================
# WORKFLOW MAPPING INSTRUCTIONS
# =====================================================
workflow_mapping_prompt:
  
  core_instruction: |
    "Create a comprehensive workflow description that maps EVERY user interaction and system process 
    in the MindBack application. Focus on actual code execution paths using specific file names 
    and function references from the codebase inventory."
  
  required_workflow_categories:
    
    1_startup_workflows:
      instruction: |
        "Document the complete application startup sequence from cold start to ready state:
        - Start with run_setup.ps1 execution
        - Map dependency installation (pip, npm)
        - Document service startup (backend FastAPI, frontend Vite)
        - Show UI initialization including governance box display
        - Include health checks and ready signals"
      
      key_files_to_map:
        - "backend/api/main.py"
        - "frontend/src/main.tsx"
        - "frontend/src/App.tsx"
        - "backend/api/config/settings.py"
        - "All polyfill and compatibility files"
    
    2_user_interaction_workflows:
      instruction: |
        "Map all user-triggered workflows with specific code execution paths:
        
        A. MindMap Creation Flow:
        - User clicks 'New MindMap' → Which component handles this?
        - How does it trigger state management?
        - What backend APIs are called?
        - How is LLM processing integrated?
        - How is the visual mindmap rendered?
        
        B. Governance Chat Flow:
        - User opens governance box → Specific component path
        - User types message → Input handling chain
        - Intent selection → Classification logic
        - LLM processing → Backend routing
        - Response display → UI update chain
        
        C. Node Manipulation Flow:
        - User interacts with nodes → Event detection
        - Position updates → State management
        - Layout recalculation → Layout engine
        - Visual updates → Canvas rendering"
      
      mapping_format: |
        "For each workflow step, specify:
        - trigger: 'User action description'
        - ui: 'specific_file.tsx::ComponentName'
        - calls: 'target_file.ts::functionName'
        - action: 'methodName()' or 'specific operation'
        - result: 'What happens next'"
    
    3_backend_processing_workflows:
      instruction: |
        "Document all backend processing pipelines:
        - LLM request processing from frontend to OpenAI and back
        - Prompt template loading and variable replacement
        - Response processing and validation
        - Error handling and logging chains
        - State persistence and retrieval"
      
      focus_areas:
        - "backend/api/routes/llm.py processing chain"
        - "Prompt library integration"
        - "OpenAI service integration"
        - "Response processing pipeline"
    
    4_state_management_workflows:
      instruction: |
        "Map all state management patterns:
        - Zustand store interactions
        - Cross-component state synchronization
        - Frontend-backend state sync
        - Error state propagation
        - UI state management"
    
    5_error_handling_workflows:
      instruction: |
        "Document error handling cascades:
        - Where errors originate
        - How they propagate through the system
        - Error boundaries and fallback UI
        - Logging and reporting mechanisms"

# =====================================================
# DEAD CODE ANALYSIS INSTRUCTIONS
# =====================================================
dead_code_analysis_prompt:
  
  core_instruction: |
    "Perform comprehensive dead code analysis by comparing the mvcd.yaml inventory 
    against the mapped workflows to identify unused, deprecated, and duplicate code."
  
  analysis_categories:
    
    deprecated_directories:
      instruction: |
        "Identify directories that are clearly backups or deprecated:
        - Look for patterns like 'backup', '_WRECKED', 'old_'
        - Check last_modified dates for staleness
        - Identify directories not referenced in any workflow"
      
      action_items:
        - "Mark for archival and removal"
        - "Verify no critical dependencies exist"
    
    duplicate_files:
      instruction: |
        "Find files with similar names or purposes:
        - Multiple main.py files
        - Duplicate store implementations
        - Similar component variations (App.tsx vs AppRefactored.tsx)
        - Compare functionality and identify the active version"
      
      analysis_method: |
        "For each duplicate set:
        1. Determine which version is referenced in active workflows
        2. Check import dependencies
        3. Recommend consolidation or removal"
    
    orphaned_components:
      instruction: |
        "Identify components/services not referenced in any workflow:
        - Large component directories (ChatFork, context features)
        - Standalone service files
        - Utility components not imported anywhere"
      
      verification_required: |
        "For each orphaned component:
        1. Search for any imports or references
        2. Check if it's part of an experimental feature
        3. Assess removal impact"
    
    utility_scripts:
      instruction: |
        "Categorize one-time use scripts:
        - Migration scripts (migrate_*, cleanup_*, setup_*)
        - Test files not part of testing framework
        - Development utilities"
      
      recommendation: "Move to appropriate /scripts/ directories"

# =====================================================
# COVERAGE ANALYSIS INSTRUCTIONS
# =====================================================
coverage_analysis_prompt:
  
  metrics_to_calculate:
    - "Total files in codebase (from mvcd.yaml count)"
    - "Files mapped to active workflows"
    - "Coverage percentage"
    - "Breakdown by category (active, deprecated, utility, unmapped)"
  
  gap_analysis:
    instruction: |
      "Identify unmapped but potentially active code:
      - Recent files not in workflows
      - Core service files without workflow mapping
      - Infrastructure files (polyfills, configs)
      - Feature directories not documented"
    
    action_items:
      - "Prioritize mapping of high-confidence active files"
      - "Investigate unclear files"
      - "Document missing workflows"

# =====================================================
# OUTPUT FORMAT REQUIREMENTS
# =====================================================
output_format_specifications:
  
  structure_requirements:
    - "Use clear YAML hierarchy with descriptive section headers"
    - "Include metadata section with version and maintainer info"
    - "Separate operational workflows from architectural analysis"
    - "Use consistent naming conventions for file references"
  
  code_reference_format:
    - "Always use full file paths from project root"
    - "Include function/component names with :: separator"
    - "Example: 'frontend/src/components/App.tsx::App'"
  
  workflow_documentation_pattern:
    example: |
      workflow_name:
        trigger: "Specific user action or system event"
        execution_path:
          - component: "file_path::ComponentName"
            calls: "target_file::functionName"
            action: "specific method or operation"
            result: "what happens next"
  
  analysis_sections_required:
    1. "Application startup workflow"
    2. "Runtime operational workflows" 
    3. "Code interaction flows"
    4. "Data flow architecture"
    5. "Critical dependency chains"
    6. "Workflow coverage analysis"
    7. "Dead code analysis recommendations"
    8. "Coverage metrics and recommendations"

# =====================================================
# QUALITY STANDARDS
# =====================================================
quality_requirements:
  
  completeness_criteria:
    - "Map 80%+ of active production code to workflows"
    - "Document all user-facing features"
    - "Include all critical backend processing"
    - "Cover error handling and edge cases"
  
  accuracy_requirements:
    - "Verify all file paths exist in mvcd.yaml"
    - "Ensure logical flow sequences"
    - "Validate component relationships"
  
  maintainability_standards:
    - "Use searchable file references"
    - "Provide clear action items for improvements"
    - "Include rationale for recommendations"
    - "Structure for easy updates"

# =====================================================
# EXECUTION CONTEXT
# =====================================================
execution_context:
  
  codebase_characteristics:
    - "React TypeScript frontend with Vite"
    - "FastAPI Python backend"
    - "LLM integration with OpenAI"
    - "Zustand for state management"
    - "Canvas-based mindmap rendering"
    - "Windows development environment"
  
  architectural_patterns:
    - "Component-based React architecture"
    - "Service layer pattern in frontend"
    - "RESTful API with structured responses"
    - "Event-driven UI updates"
    - "Modular prompt system"
  
  focus_priorities:
    1. "User experience flows (mindmap creation, governance chat)"
    2. "LLM processing pipeline"
    3. "State management patterns"
    4. "Error handling robustness"
    5. "Code maintainability"

# =====================================================
# FINAL PROMPT ASSEMBLY
# =====================================================
final_prompt_instruction: |
  "Using the mvcd.yaml codebase inventory as your primary data source, create a comprehensive 
  workflowDescription.yaml that:
  
  1. Documents every significant user interaction flow from UI trigger to system response
  2. Maps the complete application startup sequence with specific file references
  3. Traces backend processing pipelines with exact function calls
  4. Identifies deprecated, duplicate, and unused code with actionable recommendations
  5. Provides coverage metrics and improvement suggestions
  6. Serves as both operational documentation and architectural analysis tool
  
  Follow professional software architecture documentation standards while maintaining 
  practical utility for developers, onboarding, and maintenance activities."

validation_checklist:
  - "All workflows trace from user action to system completion"
  - "File references match mvcd.yaml inventory"
  - "Dead code analysis provides clear action items"
  - "Coverage metrics are realistic and useful"
  - "Documentation serves both technical and operational needs"
  - "Structure supports easy navigation and search"
