from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import json
import os
from datetime import datetime

router = APIRouter()

# Define the data directory
DATA_DIR = "data/mindmaps"

# Ensure the data directory exists
os.makedirs(DATA_DIR, exist_ok=True)

class MindMapNode(BaseModel):
    id: str
    text: str
    parent_id: Optional[str] = None
    x: Optional[float] = None
    y: Optional[float] = None
    design: Optional[Dict[str, Any]] = None

class MindMapProject(BaseModel):
    id: str
    name: str
    nodes: List[MindMapNode]

class ProjectMetadata(BaseModel):
    id: str
    name: str
    lastModified: str

@router.post("/save")
async def save_mindmap(project: MindMapProject):
    try:
        # Create a filename based on the project ID
        filename = f"{project.id}.json"
        filepath = os.path.join(DATA_DIR, filename)
        
        # Save the mind map data
        with open(filepath, "w") as f:
            json.dump({
                "id": project.id,
                "name": project.name,
                "nodes": [node.dict() for node in project.nodes],
                "lastModified": datetime.now().isoformat()
            }, f, indent=2)
        
        return {"success": True, "message": "Mind map saved successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to save mind map: {str(e)}")

@router.get("/load/{project_id}")
async def load_mindmap(project_id: str):
    try:
        # Create the filename based on the project ID
        filename = f"{project_id}.json"
        filepath = os.path.join(DATA_DIR, filename)
        
        # Check if the file exists
        if not os.path.exists(filepath):
            raise HTTPException(status_code=404, detail="Mind map not found")
        
        # Load the mind map data
        with open(filepath, "r") as f:
            data = json.load(f)
        
        return data
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to load mind map: {str(e)}")

@router.get("/projects")
async def get_projects():
    try:
        projects = []
        
        # List all JSON files in the data directory
        for filename in os.listdir(DATA_DIR):
            if filename.endswith(".json"):
                filepath = os.path.join(DATA_DIR, filename)
                
                # Load the project metadata
                with open(filepath, "r") as f:
                    data = json.load(f)
                
                # Add the project metadata to the list
                projects.append({
                    "id": data.get("id", filename.replace(".json", "")),
                    "name": data.get("name", "Untitled"),
                    "lastModified": data.get("lastModified", datetime.fromtimestamp(os.path.getmtime(filepath)).isoformat())
                })
        
        # Sort projects by last modified date (newest first)
        projects.sort(key=lambda x: x["lastModified"], reverse=True)
        
        return {"projects": projects}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get projects: {str(e)}")

@router.delete("/delete/{project_id}")
async def delete_project(project_id: str):
    try:
        # Create the filename based on the project ID
        filename = f"{project_id}.json"
        filepath = os.path.join(DATA_DIR, filename)
        
        # Check if the file exists
        if not os.path.exists(filepath):
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Delete the file
        os.remove(filepath)
        
        return {"success": True, "message": "Project deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete project: {str(e)}") 