/**
 * NodeComponent.tsx
 *
 * Component for rendering a node in the mind map.
 * This version has been refactored to use the new service layer.
 */

import React from 'react';
import { Group, Rect, Text, Ellipse, RegularPolygon } from 'react-konva';
import { Node } from '../../../../core/types/MindMapTypes';
import RegistrationManager, { EventType } from '../../../../core/services/RegistrationManager';
import { mindObjectService, getNode } from '../../../../core/services/MindObjectService';
import { mindSheetService } from '../../../../core/services/MindSheetService';

interface NodeComponentProps {
  node: Node;
  isSelected: boolean;
  onClick: () => void;
  updateNode: (id: string, updates: Partial<Node>) => void;
  selectNode: (id: string) => void;
  sheetId: string;
}

const NodeComponent: React.FC<NodeComponentProps> = ({
  node,
  isSelected,
  onClick,
  updateNode,
  selectNode,
  sheetId
}) => {
  // Helper function to check if NodeBox is open
  const checkNodeboxOpen = () => {
    const nodeboxOverlay = document.querySelector('.nodebox-overlay');
    return !!nodeboxOverlay && window.getComputedStyle(nodeboxOverlay).display !== 'none';
  };

  // Handle drag start
  const handleDragStart = (e: any) => {
    // Check if the node is being edited or if NodeBox is open
    if (node.metadata?.isEditing || checkNodeboxOpen()) {
      console.log('Node is being edited or NodeBox is open, canceling drag start');
      e.cancelBubble = true;
      if (e.evt) {
        e.evt.stopPropagation();
        e.evt.preventDefault();
      }
      return false; // Cancel the drag
    }

    // Select the node when starting to drag
    selectNode(node.id);

    // Force the node to be selected
    if (!isSelected) {
      console.log('Force selecting node on drag start:', node.id);

      // Use the MindObjectService to select the node
      mindObjectService.selectNode(sheetId, node.id);

      // Register the selection event
      RegistrationManager.registerEvent(EventType.NODE_SELECTED, {
        id: node.id,
        text: node.text,
        position: { x: node.x, y: node.y },
        sheetId
      });
    }

    // Register the drag start event
    RegistrationManager.registerEvent(EventType.NODE_DRAG_STARTED, {
      id: node.id,
      position: { x: e.target.x(), y: e.target.y() },
      sheetId
    });

    // Prevent event bubbling
    e.cancelBubble = true;
    if (e.evt) {
      e.evt.stopPropagation();
      e.evt.preventDefault();
    }
  };

  // Handle drag move
  const handleDragMove = (e: any) => {
    // Check if the node is being edited or if NodeBox is open - if so, don't allow dragging
    if (node.metadata?.isEditing || checkNodeboxOpen()) {
      console.log('Node is being edited or NodeBox is open, ignoring drag move');
      e.cancelBubble = true;
      if (e.evt) {
        e.evt.stopPropagation();
        e.evt.preventDefault();
      }
      return;
    }

    // Update node position in real-time during drag
    // This ensures smooth dragging experience
    const newX = e.target.x();
    const newY = e.target.y();

    // Use the MindObjectService to update the node position
    mindObjectService.updateNode(sheetId, node.id, {
      x: newX,
      y: newY
    });

    // Prevent event bubbling
    e.cancelBubble = true;
  };

  // Handle drag end
  const handleDragEnd = (e: any) => {
    // Check if the node is being edited or if NodeBox is open - if so, don't allow dragging
    if (node.metadata?.isEditing || checkNodeboxOpen()) {
      console.log('Node is being edited or NodeBox is open, ignoring drag end');
      e.cancelBubble = true;
      if (e.evt) {
        e.evt.stopPropagation();
        e.evt.preventDefault();
      }
      return;
    }

    // Get the final position
    const finalX = e.target.x();
    const finalY = e.target.y();

    // Use the MindObjectService to update the node position
    mindObjectService.updateNode(sheetId, node.id, {
      x: finalX,
      y: finalY
    });

    // Register the drag end event
    RegistrationManager.registerEvent(EventType.NODE_DRAG_ENDED, {
      id: node.id,
      position: { x: finalX, y: finalY },
      sheetId
    });

    // Prevent event bubbling
    e.cancelBubble = true;

    // Save the state to ensure it's preserved when switching sheets
    mindSheetService.saveMindMapSheetState(sheetId);

    // Force a refresh of the canvas to ensure connections are properly updated
    setTimeout(() => {
      // Dispatch a custom event to refresh the canvas
      const event = new CustomEvent('mindback:refresh_canvas', {
        detail: {
          sheetId,
          nodeId: node.id
        }
      });
      document.dispatchEvent(event);
    }, 10);
  };

  // Render different shapes based on node.shape
  const renderShape = () => {
    // Check both the isSelected prop and the node's own isSelected property
    const nodeIsSelected = isSelected || node.isSelected;

    // Common event handlers for all shapes
    const shapeEventHandlers = {
      onClick: (e: any) => {
        // Prevent the event from bubbling up
        e.cancelBubble = true;
        if (e.evt) {
          e.evt.stopPropagation();
          e.evt.preventDefault();
        }

        // Call the onClick handler to select the node
        onClick();

        // Force the node to be selected
        console.log('Shape clicked, selecting node:', node.id);
        selectNode(node.id);

        // Register the node selection event
        RegistrationManager.registerEvent(EventType.NODE_SELECTED, {
          id: node.id,
          text: node.text,
          position: { x: nodeX, y: nodeY },
          sheetId
        });
      },
      onTap: (e: any) => {
        // Prevent the event from bubbling up
        e.cancelBubble = true;
        if (e.evt) {
          e.evt.stopPropagation();
          e.evt.preventDefault();
        }

        // Call the onClick handler to select the node
        onClick();

        // Force the node to be selected
        console.log('Shape tapped, selecting node:', node.id);
        selectNode(node.id);

        // Register the node selection event
        RegistrationManager.registerEvent(EventType.NODE_SELECTED, {
          id: node.id,
          text: node.text,
          position: { x: nodeX, y: nodeY },
          sheetId
        });
      }
    };

    // Use blue for selected nodes (original styling)
    const selectedColor = '#3498db';
    const selectedStrokeWidth = 3;
    const selectedShadowBlur = 10;
    const selectedShadowOpacity = 0.5;

    const shapeProps = {
      width: nodeWidth,
      height: nodeHeight,
      fill: node.color || '#ffffff',
      stroke: nodeIsSelected ? selectedColor : (node.borderColor || '#2c3e50'),
      strokeWidth: nodeIsSelected ? selectedStrokeWidth : 1,
      shadowColor: nodeIsSelected ? selectedColor : 'rgba(0,0,0,0.3)',
      shadowBlur: nodeIsSelected ? selectedShadowBlur : 5,
      shadowOffset: { x: 0, y: 2 },
      shadowOpacity: nodeIsSelected ? selectedShadowOpacity : 0.5,
      cornerRadius: 5,
      // Add event handlers to the shape itself
      ...shapeEventHandlers
    };

    switch (node.shape) {
      case 'ellipse':
        return (
          <Ellipse
            radiusX={nodeWidth / 2}
            radiusY={nodeHeight / 2}
            fill={node.color || '#ffffff'}
            stroke={nodeIsSelected ? selectedColor : (node.borderColor || '#2c3e50')}
            strokeWidth={nodeIsSelected ? selectedStrokeWidth : 1}
            shadowColor={nodeIsSelected ? selectedColor : 'rgba(0,0,0,0.3)'}
            shadowBlur={nodeIsSelected ? selectedShadowBlur : 5}
            shadowOffset={{ x: 0, y: 2 }}
            shadowOpacity={nodeIsSelected ? selectedShadowOpacity : 0.5}
            // Add event handlers to the shape itself
            {...shapeEventHandlers}
          />
        );
      case 'diamond':
        return (
          <RegularPolygon
            sides={4}
            radius={Math.min(nodeWidth, nodeHeight) / 2}
            rotation={45}
            fill={node.color || '#ffffff'}
            stroke={nodeIsSelected ? selectedColor : (node.borderColor || '#2c3e50')}
            strokeWidth={nodeIsSelected ? selectedStrokeWidth : 1}
            shadowColor={nodeIsSelected ? selectedColor : 'rgba(0,0,0,0.3)'}
            shadowBlur={nodeIsSelected ? selectedShadowBlur : 5}
            shadowOffset={{ x: 0, y: 2 }}
            shadowOpacity={nodeIsSelected ? selectedShadowOpacity : 0.5}
            // Add event handlers to the shape itself
            {...shapeEventHandlers}
          />
        );
      case 'hexagon':
        return (
          <RegularPolygon
            sides={6}
            radius={Math.min(nodeWidth, nodeHeight) / 2}
            fill={node.color || '#ffffff'}
            stroke={nodeIsSelected ? selectedColor : (node.borderColor || '#2c3e50')}
            strokeWidth={nodeIsSelected ? selectedStrokeWidth : 1}
            shadowColor={nodeIsSelected ? selectedColor : 'rgba(0,0,0,0.3)'}
            shadowBlur={nodeIsSelected ? selectedShadowBlur : 5}
            shadowOffset={{ x: 0, y: 2 }}
            shadowOpacity={nodeIsSelected ? selectedShadowOpacity : 0.5}
            // Add event handlers to the shape itself
            {...shapeEventHandlers}
          />
        );
      case 'rectangle':
      default:
        return <Rect {...shapeProps} />;
    }
  };

  // Handle double-click to open the node dialog
  const handleDblClick = (e: any) => {
    console.log('NODEBOX DEBUG: Double-click detected on node:', node.id);
    console.log('NODEBOX DEBUG: Node details:', {
      id: node.id,
      text: node.text,
      sheetId,
      hasMetadata: !!node.metadata,
      isEditing: node.metadata?.isEditing
    });

    // Prevent default browser behavior (text selection)
    if (e && e.evt) {
      e.evt.preventDefault();
      e.cancelBubble = true; // Stop event propagation
      e.evt.stopPropagation(); // Additional stop propagation
    }

    // First make sure the node is selected
    console.log('NODEBOX DEBUG: Selecting node:', node.id, 'in sheet:', sheetId);
    mindObjectService.selectNode(sheetId, node.id);

    try {
      // Get the current node state to verify
      const currentNode = getNode(sheetId, node.id);
      console.log('NODEBOX DEBUG: Current node state before update:', {
        id: currentNode?.id,
        hasMetadata: !!currentNode?.metadata,
        isEditing: currentNode?.metadata?.isEditing
      });

      // Use the MindObjectService to update the node metadata
      console.log('NODEBOX DEBUG: Updating node metadata with isEditing flag');
      mindObjectService.updateNode(sheetId, node.id, {
        metadata: {
          ...(node.metadata || {}),
          isEditing: true // This flag will be used by NodeBox to determine if it should open
        }
      });

      // Verify the update was applied
      const updatedNode = getNode(sheetId, node.id);
      console.log('NODEBOX DEBUG: Node state after update:', {
        id: updatedNode?.id,
        hasMetadata: !!updatedNode?.metadata,
        isEditing: updatedNode?.metadata?.isEditing
      });

      console.log('NODEBOX DEBUG: Updated node metadata with isEditing flag');

      // Make the node non-draggable immediately
      const nodeGroup = document.querySelector(`[data-node-id="${node.id}"]`);
      if (nodeGroup) {
        (nodeGroup as HTMLElement).setAttribute('draggable', 'false');
        console.log('NODEBOX DEBUG: Set node to non-draggable');
      } else {
        console.error('NODEBOX DEBUG: Could not find node element with data-node-id:', node.id);
      }

      // Register the node selection event
      RegistrationManager.registerEvent(EventType.NODE_SELECTED, {
        id: node.id,
        text: node.text,
        position: { x: node.x, y: node.y },
        sheetId
      });

      // Register the node opening event
      RegistrationManager.registerEvent(EventType.NODE_OPENED, {
        id: node.id,
        sheetId
      });

      // Force a refresh of the canvas to ensure connections are properly updated
      setTimeout(() => {
        // Dispatch a custom event to refresh the canvas
        const event = new CustomEvent('mindback:refresh_canvas', {
          detail: {
            sheetId,
            nodeId: node.id
          }
        });
        document.dispatchEvent(event);
        console.log('NODEBOX DEBUG: Dispatched refresh_canvas event');
      }, 10);

    } catch (error) {
      console.error('NODEBOX DEBUG: Error in double-click handler:', error);
    }
  };

  // Ensure node has valid position
  const nodeX = node.x || 0;
  const nodeY = node.y || 0;
  const nodeWidth = node.width || 200;
  const nodeHeight = node.height || 100;

  // Track if we're in editing mode to prevent dragging
  const isEditing = node.metadata?.isEditing || false;

  // Determine if the node should be draggable
  const shouldBeDraggable = !isEditing && !checkNodeboxOpen();

  return (
    <Group
      x={nodeX}
      y={nodeY}
      draggable={shouldBeDraggable} // Disable dragging when editing or when NodeBox is open
      data-node-id={node.id} // Add data attribute for DOM selection
      onDragStart={handleDragStart}
      onDragMove={handleDragMove}
      onDragEnd={handleDragEnd}
      onClick={(e) => {
        // Prevent the event from bubbling up
        e.cancelBubble = true;
        if (e.evt) {
          e.evt.stopPropagation();
          e.evt.preventDefault();
        }

        // Call the onClick handler to select the node
        onClick();

        // Use the MindObjectService to select the node
        mindObjectService.selectNode(sheetId, node.id);

        // Register the node selection event
        RegistrationManager.registerEvent(EventType.NODE_SELECTED, {
          id: node.id,
          text: node.text,
          position: { x: nodeX, y: nodeY },
          sheetId
        });
      }}
      onDblClick={handleDblClick}
      onDblTap={handleDblClick}
    >
      {renderShape()}
      {/* Node Path (displayed separately from the title) */}
      <Text
        text={node.metadata?.nodePath || '1.0'}
        width={nodeWidth}
        height={20}
        y={-nodeHeight/2 + 15}
        align="center"
        verticalAlign="middle"
        fontSize={10}
        fontFamily="Arial, sans-serif"
        fill="#666666"
        listening={true} // Make text respond to mouse events
        onClick={(e) => {
          e.cancelBubble = true;
          if (e.evt) {
            e.evt.stopPropagation();
            e.evt.preventDefault();
          }
          onClick();
          selectNode(node.id);
          console.log('Path text clicked, selecting node:', node.id);
        }}
      />
      {/* Node Title (without path prefix) */}
      <Text
        text={node.text || 'Untitled Node'}
        width={nodeWidth}
        height={nodeHeight}
        align="center"
        verticalAlign="middle"
        fontSize={16}
        fontFamily="Arial, sans-serif"
        fill="#333333"
        padding={10}
        listening={true} // Make text respond to mouse events
        onClick={(e) => {
          e.cancelBubble = true;
          if (e.evt) {
            e.evt.stopPropagation();
            e.evt.preventDefault();
          }
          onClick();
          selectNode(node.id);
          console.log('Title text clicked, selecting node:', node.id);
        }}
      />
    </Group>
  );
};

export default NodeComponent;
