/**
 * GovernanceTestUtils.ts
 * 
 * Utilities for testing and debugging governance compliance
 */

import { LayoutGovernanceService } from '../services/LayoutGovernanceService';
import { UnifiedLayoutManager } from '../layout/UnifiedLayoutManager';
import { LayoutStrategyType } from '../types/LayoutTypes';

export class GovernanceTestUtils {
  
  /**
   * Test governance compliance for a specific sheet
   */
  static async testGovernanceCompliance(sheetId: string): Promise<void> {
    console.log(`\n🔍 GOVERNANCE COMPLIANCE TEST for sheet: ${sheetId}`);
    console.log('='.repeat(60));
    
    const governance = LayoutGovernanceService.getInstance();
    const layoutManager = UnifiedLayoutManager.getInstance();
    
    // Test 1: Auto-switching should be blocked
    console.log('\n1️⃣ Testing auto-switching prevention...');
    const autoRequest = {
      strategy: 'topDown' as LayoutStrategyType,
      sheetId,
      requestOrigin: 'auto' as const
    };
    
    const canAutoChange = layoutManager.canChangeLayout(autoRequest);
    console.log(`   Auto-switching allowed: ${canAutoChange} ${canAutoChange ? '❌ VIOLATION' : '✅ COMPLIANT'}`);
    
    // Test 2: User changes should be allowed
    console.log('\n2️⃣ Testing user override permission...');
    const userRequest = {
      strategy: 'radial' as LayoutStrategyType,
      sheetId,
      requestOrigin: 'user' as const
    };
    
    const canUserChange = layoutManager.canChangeLayout(userRequest);
    console.log(`   User override allowed: ${canUserChange} ${canUserChange ? '✅ COMPLIANT' : '❌ VIOLATION'}`);
    
    // Test 3: Rate limiting
    console.log('\n3️⃣ Testing rate limiting...');
    let rapidChanges = 0;
    for (let i = 0; i < 5; i++) {
      const response = await layoutManager.requestLayoutChange({
        strategy: 'leftToRight',
        sheetId,
        requestOrigin: 'user',
        reason: `Rapid test ${i}`
      });
      if (response.success) rapidChanges++;
    }
    console.log(`   Rapid changes allowed: ${rapidChanges}/5 ${rapidChanges <= 3 ? '✅ COMPLIANT' : '❌ VIOLATION'}`);
    
    // Test 4: Preference persistence
    console.log('\n4️⃣ Testing preference persistence...');
    const originalPreference = governance.getPreferredStrategy(sheetId);
    governance.setUserPreference(sheetId, 'radial');
    const newPreference = governance.getPreferredStrategy(sheetId);
    const persistent = newPreference === 'radial';
    console.log(`   Preference persistence: ${persistent ? '✅ COMPLIANT' : '❌ VIOLATION'}`);
    
    // Restore original preference
    governance.setUserPreference(sheetId, originalPreference);
    
    // Test 5: Get governance stats
    console.log('\n5️⃣ Governance statistics...');
    const stats = governance.getGovernanceStats(sheetId);
    console.log(`   Preferred strategy: ${stats.preferredStrategy}`);
    console.log(`   Has user preference: ${stats.hasUserPreference}`);
    console.log(`   Recent attempts: ${stats.recentAttempts}`);
    
    console.log('\n✅ Governance compliance test completed');
    console.log('='.repeat(60));
  }
  
  /**
   * Test for layout chaos indicators
   */
  static monitorLayoutChaos(sheetId: string, duration: number = 10000): void {
    console.log(`\n📊 MONITORING LAYOUT CHAOS for sheet: ${sheetId} (${duration}ms)`);
    
    let layoutChangeCount = 0;
    let lastLayoutTime = 0;
    let rapidChanges = 0;
    
    const startTime = Date.now();
    
    // Monitor layout events
    const originalRequestLayoutChange = UnifiedLayoutManager.getInstance().requestLayoutChange;
    UnifiedLayoutManager.getInstance().requestLayoutChange = async function(request) {
      layoutChangeCount++;
      const currentTime = Date.now();
      
      if (currentTime - lastLayoutTime < 1000) {
        rapidChanges++;
        console.warn(`⚠️ RAPID LAYOUT CHANGE detected: ${request.strategy} (${currentTime - lastLayoutTime}ms since last)`);
      }
      
      lastLayoutTime = currentTime;
      console.log(`📈 Layout change #${layoutChangeCount}: ${request.strategy} (origin: ${request.requestOrigin})`);
      
      return originalRequestLayoutChange.call(this, request);
    };
    
    // Report after duration
    setTimeout(() => {
      console.log(`\n📊 LAYOUT CHAOS REPORT for sheet: ${sheetId}`);
      console.log(`   Total layout changes: ${layoutChangeCount}`);
      console.log(`   Rapid changes (< 1s): ${rapidChanges}`);
      console.log(`   Chaos indicator: ${rapidChanges > 3 ? '❌ HIGH CHAOS' : layoutChangeCount > 10 ? '⚠️ MODERATE' : '✅ LOW CHAOS'}`);
      
      // Restore original method
      UnifiedLayoutManager.getInstance().requestLayoutChange = originalRequestLayoutChange;
    }, duration);
  }
  
  /**
   * Reset all governance state for testing
   */
  static resetGovernanceForTesting(): void {
    console.log('🔄 Resetting governance state for testing...');
    const governance = LayoutGovernanceService.getInstance();
    governance.resetAllState();
    console.log('✅ Governance state reset complete');
  }
  
  /**
   * Simulate old component behavior to test governance blocking
   */
  static async simulateOldComponentBehavior(sheetId: string): Promise<void> {
    console.log(`\n🚫 SIMULATING OLD COMPONENT BEHAVIOR for sheet: ${sheetId}`);
    
    const layoutManager = UnifiedLayoutManager.getInstance();
    
    // Simulate MindSheet automatic trigger
    console.log('Simulating MindSheet auto-trigger...');
    const mindSheetResponse = await layoutManager.requestLayoutChange({
      strategy: 'topDown',
      sheetId,
      requestOrigin: 'auto',
      reason: 'MindSheet activation'
    });
    console.log(`   MindSheet trigger: ${mindSheetResponse.success ? '❌ ALLOWED' : '✅ BLOCKED'} - ${mindSheetResponse.reason}`);
    
    // Simulate Canvas automatic trigger
    console.log('Simulating Canvas auto-trigger...');
    const canvasResponse = await layoutManager.requestLayoutChange({
      strategy: 'topDown',
      sheetId,
      requestOrigin: 'auto',
      reason: 'Canvas initialization'
    });
    console.log(`   Canvas trigger: ${canvasResponse.success ? '❌ ALLOWED' : '✅ BLOCKED'} - ${canvasResponse.reason}`);
    
    // Simulate rapid fire changes
    console.log('Simulating rapid fire changes...');
    let blockedCount = 0;
    for (let i = 0; i < 5; i++) {
      const response = await layoutManager.requestLayoutChange({
        strategy: i % 2 === 0 ? 'leftToRight' : 'topDown',
        sheetId,
        requestOrigin: 'auto',
        reason: `Rapid change ${i}`
      });
      if (!response.success) blockedCount++;
    }
    console.log(`   Rapid changes blocked: ${blockedCount}/5 ${blockedCount >= 3 ? '✅ GOOD BLOCKING' : '❌ INSUFFICIENT BLOCKING'}`);
  }
}

// Make it available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).GovernanceTestUtils = GovernanceTestUtils;
  console.log('🔧 GovernanceTestUtils available globally for debugging');
} 