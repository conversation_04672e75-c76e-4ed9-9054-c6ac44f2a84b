/**
 * Debug utilities for the GovernanceChatDialog
 */

// Enable debug mode
const DEBUG_ENABLED = true;

/**
 * Log debug information if debug mode is enabled
 */
export const debugLog = (message: string, ...args: any[]) => {
  if (DEBUG_ENABLED) {
    console.log(`[GovernanceDialog] ${message}`, ...args);
  }
};

/**
 * Inspect DOM element state for debugging
 */
export const inspectElement = (element: HTMLElement | null, label: string) => {
  if (!DEBUG_ENABLED || !element) return;
  
  const styles = window.getComputedStyle(element);
  debugLog(`${label} Inspection:`, {
    transform: styles.transform,
    position: styles.position,
    zIndex: styles.zIndex,
    pointerEvents: styles.pointerEvents,
    width: styles.width,
    height: styles.height,
    classNames: element.className,
    parentElement: element.parentElement?.className
  });
};

/**
 * Monitor drag operations
 */
export const monitorDrag = {
  start: (e: React.MouseEvent | MouseEvent, id: string) => {
    debugLog(`Drag START on ${id}`, {
      clientX: e.clientX,
      clientY: e.clientY,
      target: (e.target as HTMLElement).className,
      elementTransform: ((e.target as HTMLElement).closest('.react-draggable') as HTMLElement)?.style.transform || 'unknown'
    });
  },
  
  move: (e: React.MouseEvent | MouseEvent, id: string) => {
    debugLog(`Drag MOVE on ${id}`, {
      clientX: e.clientX,
      clientY: e.clientY
    });
  },
  
  end: (e: React.MouseEvent | MouseEvent, id: string) => {
    const draggableElement = (e.target as HTMLElement).closest('.react-draggable') as HTMLElement;
    const transform = draggableElement?.style.transform;
    const match = transform?.match(/translate\((-?\d+)px, (-?\d+)px\)/);
    
    let finalPosition = { x: 'unknown', y: 'unknown' };
    if (match && match.length === 3) {
      finalPosition = { x: match[1], y: match[2] };
    }
    
    debugLog(`Drag END on ${id}`, {
      clientX: e.clientX,
      clientY: e.clientY,
      target: (e.target as HTMLElement).className,
      finalTransform: transform,
      finalPosition
    });
  }
};

/**
 * Apply fix to make draggable work correctly
 */
export const applyDraggableFix = (dialogElement: HTMLElement | null) => {
  if (!dialogElement) return false;
  
  try {
    // Find the react-draggable element
    const draggableElement = dialogElement.closest('.react-draggable') as HTMLElement;
    if (!draggableElement) {
      debugLog('Draggable element not found');
      return false;
    }
    
    // First check if there's an existing transform we should preserve
    const existingTransform = draggableElement.style.transform;
    const match = existingTransform?.match(/translate\((-?\d+)px, (-?\d+)px\)/);
    
    if (match && match.length === 3) {
      // If there's already a transform, preserve it to avoid jumping
      debugLog('Preserving existing transform: ' + existingTransform);
    } else {
      // If no transform exists, use the default position from rules
      debugLog('No existing transform found, setting initial position');
      draggableElement.style.transform = 'translate(223px, 550px)';
    }
    
    // Ensure pointerEvents are set correctly
    draggableElement.style.pointerEvents = 'auto';
    dialogElement.style.pointerEvents = 'auto';
    
    // Log success
    debugLog('Applied draggable fix successfully');
    
    return true;
  } catch (error) {
    debugLog('Error applying draggable fix', error);
    return false;
  }
}; 