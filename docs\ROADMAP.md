# MindBack Development Roadmap

## Current Implementation Status

### Exploratory Intent

**Current Status**: Partially Implemented
- The exploratory intent detection is functional in the backend via the initiation prompt.
- The frontend correctly identifies and processes exploratory intent responses from the LLM.
- The UI displays an "Explore Topic" button for content with exploratory intent.
- The MBCP format supports exploratory intent with appropriate metadata fields.

**Missing Components**:
- Consistent handling of exploratory content across different response formats.
- Comprehensive error handling for malformed exploratory responses.
- Complete integration with the mindmap canvas for exploratory content visualization.

### ChatFork Implementation

**Current Status**: Partially Implemented
- Basic ChatFork container and components exist in the codebase.
- The ChatForkAdapter provides integration between UI components and state management.
- The backend has prompt templates for generating chatfork content (initiate_chatfork1.yaml and initiate_chatfork2.yaml).
- The UI supports text selection and node creation from selected text.

**Missing Components**:
- Full integration of ChatFork with the canvas-based approach outlined in the planning document.
- Complete implementation of the multi-level forking functionality.
- Finalized styling and positioning of the ChatFork component.
- Comprehensive testing across different content types and user interactions.

## Architecture and Components

### Exploratory Intent Architecture

1. **Backend Components**:
   - Prompt templates in `backend/Prompt_library/` for exploratory content generation.
   - MBCP models in `backend/api/models/mbcp_models.py` with support for exploratory intent.
   - Validation logic for exploratory content structure.

2. **Frontend Components**:
   - Intent detection in `useChat.ts` hook.
   - UI rendering in `MessageList.tsx` with special handling for exploratory content.
   - Action handling for exploratory intent in `Implementation.tsx`.

### ChatFork Architecture

1. **Core Components**:
   - `ChatForkContainer.tsx`: Main container component managing the ChatFork UI.
   - `ChatForkView.tsx`: Modern component for displaying exploratory content.
   - `ChatForkNode.tsx` and `ChatForkConnection.tsx`: Components for visualizing the fork structure.
   - `ChatForkAdapter.ts`: Integration layer between UI and state management.

2. **State Management**:
   - `ChatForkStore`: Zustand store for managing ChatFork state.
   - Integration with `MindMapStore` for node creation and management.

## Workflow and Data Flow

### Exploratory Intent Workflow

1. User submits a query to the governance box.
2. Backend processes the query and detects exploratory intent.
3. LLM generates a response in MBCP format with exploratory metadata.
4. Frontend receives the response and identifies the exploratory intent.
5. UI displays an "Explore Topic" button for the user.
6. When clicked, the system either:
   - Shows the ChatFork component with the exploratory content, or
   - Creates a node in the mindmap with exploratory intent metadata.

### ChatFork Data Flow

1. User clicks on an exploratory content action button.
2. `ChatForkAdapter` processes the action and updates the `ChatForkStore`.
3. `ChatForkView` or `ChatForkContainer` renders based on the store state.
4. User can select text within the ChatFork content.
5. On selection (or Tab key), the system creates a new node in the mindmap.
6. The node is connected to the original content with appropriate metadata.

## UI/UX Implementation

### Exploratory Intent UI

- Special styling for exploratory content messages.
- "Explore Topic" button with distinct visual treatment.
- Integration with the governance chat interface.

### ChatFork UI

- Positioned at the top-left of the screen, distinct from the governance chat.
- Clean, modern interface with a header and close button.
- Text selection functionality with visual feedback.
- Node creation from selected text with appropriate styling.
- Connection visualization between related nodes.

## Challenges and Issues

1. **Integration Challenges**:
   - Ensuring consistent behavior between ChatFork and MindMap components.
   - Managing state transitions between different UI modes.
   - Handling various response formats from the LLM.

2. **Technical Debt**:
   - Multiple implementations of similar functionality (original and backup versions).
   - Inconsistent handling of MBCP data across components.
   - Potential performance issues with large text selections or complex fork structures.

3. **UX Considerations**:
   - Ensuring intuitive user flow between governance chat, ChatFork, and mindmap.
   - Providing clear visual feedback for user actions.
   - Maintaining consistent styling across all components.

## Recommendations for Future Development

### Short-term Priorities

1. **Complete the ChatFork Canvas Integration**:
   - Implement the plan outlined in `0BuildingChatfork.md`.
   - Replace the modal dialog with an embedded canvas view.
   - Ensure proper text selection and forking functionality.

2. **Standardize MBCP Handling**:
   - Ensure consistent processing of exploratory content across all components.
   - Implement robust validation and error handling.
   - Document the expected format for exploratory content.

3. **Improve UI/UX**:
   - Finalize styling and positioning of ChatFork components.
   - Implement smooth transitions between different states.
   - Add keyboard shortcuts for common actions.

### Medium-term Goals

1. **Enhanced Forking Capabilities**:
   - Implement multi-level forking with proper visualization.
   - Add support for different fork types (questions, discussions, etc.).
   - Improve connection visualization between related nodes.

2. **Integration with Other Intent Types**:
   - Ensure proper interaction between exploratory and teleological content.
   - Support mixed-intent workflows where appropriate.
   - Implement transitions between different intent-based views.

3. **Performance Optimization**:
   - Optimize rendering for large text selections.
   - Implement virtualization for complex fork structures.
   - Improve state management for better performance.

### Long-term Vision

1. **Advanced Collaboration Features**:
   - Real-time collaboration on ChatFork content.
   - Shared exploration of complex topics.
   - User-specific forks and annotations.

2. **Enhanced AI Integration**:
   - Contextual suggestions for fork points.
   - AI-assisted exploration of complex topics.
   - Personalized content based on user interests.

3. **Export and Integration**:
   - Export ChatFork structures to various formats.
   - Integration with external knowledge management systems.
   - API for third-party extensions and integrations.

## Implementation Roadmap

### Phase 1: Core Functionality (Current Focus)

- [x] Basic ChatFork component implementation
- [x] Exploratory intent detection and handling
- [x] Text selection and basic forking
- [ ] Canvas integration for ChatFork content
- [ ] Standardized MBCP handling for exploratory content

### Phase 2: Enhanced User Experience

- [ ] Improved styling and positioning
- [ ] Keyboard shortcuts and navigation
- [ ] Multi-level forking with proper visualization
- [ ] Smooth transitions and animations
- [ ] Comprehensive error handling

### Phase 3: Advanced Features

- [ ] Integration with other intent types
- [ ] Advanced collaboration features
- [ ] Enhanced AI integration
- [ ] Export and third-party integration
- [ ] Performance optimization for complex structures

## Conclusion

The exploratory intent and ChatFork implementation are in a partially complete state, with core functionality in place but several key components still under development. The current architecture provides a solid foundation for future enhancements, but requires standardization and completion of the planned features to deliver a seamless user experience.

The primary focus should be on completing the canvas integration for ChatFork content, standardizing MBCP handling, and improving the overall user experience. Once these core components are in place, the system can be extended with advanced features for collaboration, AI integration, and third-party extensions.
