import React, { useState, useEffect, useRef, useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { ChatResponse } from '../../services/api/GovernanceLLM';
import { useChatForkStore } from '../MindMap/core/state/ChatForkStore';
import RegistrationManager, { EventType } from '../../core/services/RegistrationManager';
import './ChatFork.css';

interface StickySelection {
  id: string;
  text: string;
  startIndex: number;
  endIndex: number;
  created: Date;
  userInput?: string;
  llmResponse?: string;
}

interface ChatForkCanvasProps {
  content: ChatResponse;
  isVisible: boolean;
  onClose: () => void;
  sheetId?: string;
  showHeader?: boolean;
  onContentChange?: (updatedContent: ChatResponse, selections: StickySelection[]) => void;
}

/**
 * ChatForkCanvas - A canvas-based implementation of the ChatFork component
 * This renders the ChatFork content directly on the canvas without using a dialog
 * Now supports markdown rendering and has a layout optimized for fork chats
 */
const ChatForkCanvas: React.FC<ChatForkCanvasProps> = (props) => {
  const { content, isVisible, onClose, sheetId, showHeader = true, onContentChange } = props;

  const [stickySelections, setStickySelections] = useState<StickySelection[]>([]);
  const [markdownText, setMarkdownText] = useState<string>('');
  const [activeTab, setActiveTab] = useState<string>('');
  const containerRef = useRef<HTMLDivElement>(null);

  const title = content?.text || 'Exploration';
  const fullText = content?.full_text || '';
  
  // Subtle but visible color palette
  const colorPalette = [
    '#FFEBEE', '#E8F5E8', '#E3F2FD', '#FFFDE7', '#F3E5F5',
    '#FFF3E0', '#E0F2F1', '#FAFAFA', '#FCE4EC', '#F1F8E9'
  ];
  
  const getSelectionColor = (index: number) => {
    return colorPalette[index % colorPalette.length];
  };

  // Generate storage key for this content
  const getStorageKey = () => {
    // Use sheetId first (most reliable), then fallback to content hash
    if (sheetId) {
      return `chatfork_sheet_${sheetId}`;
    }
    
    // Create a more stable hash from content
    const contentHash = content?.text?.substring(0, 100)?.replace(/[^a-zA-Z0-9]/g, '') || 'default';
    return `chatfork_content_${contentHash}`;
  };

  // Save state to localStorage and parent
  const saveState = (newMarkdownText: string, newSelections: StickySelection[]) => {
    const storageKey = getStorageKey();
    const stateData = {
      markdownText: newMarkdownText,
      selections: newSelections,
      lastUpdated: new Date().toISOString()
    };
    
    try {
      localStorage.setItem(storageKey, JSON.stringify(stateData));
      
      // Also save to parent if callback provided
      if (onContentChange) {
        const updatedContent = {
          ...content,
          full_text: newMarkdownText
        };
        onContentChange(updatedContent, newSelections);
      }
      
      console.log('ChatFork state saved:', storageKey);
    } catch (error) {
      console.error('Failed to save ChatFork state:', error);
    }
  };

  // Load state from localStorage
  const loadState = () => {
    const storageKey = getStorageKey();
    
    try {
      const savedData = localStorage.getItem(storageKey);
      if (savedData) {
        const parsed = JSON.parse(savedData);
        console.log('Loading ChatFork state:', storageKey, parsed);
        
        setMarkdownText(parsed.markdownText || fullText);
        
        // Convert date strings back to Date objects when loading
        const restoredSelections = (parsed.selections || []).map((sel: any) => ({
          ...sel,
          created: new Date(sel.created) // Convert string/timestamp back to Date
        }));
        
        setStickySelections(restoredSelections);
        return true;
      }
    } catch (error) {
      console.error('Failed to load ChatFork state:', error);
    }
    
    // Fallback to original content
    setMarkdownText(fullText);
    setStickySelections([]);
    return false;
  };

  // Initialize state on mount
  useEffect(() => {
    const wasLoaded = loadState();
    if (!wasLoaded) {
      console.log('No saved state found, using original content');
    }
      }, [fullText]);

  // Set up click handlers for marked text
  useEffect(() => {
    const handleMarkClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const mark = target.closest('[data-selection]') as HTMLElement;
      
      if (mark) {
        const selectionId = mark.getAttribute('data-selection');
        if (selectionId) {
          setActiveTab(selectionId);
          console.log('Selected tab from text click:', selectionId);
          event.preventDefault();
          event.stopPropagation();
        }
      }
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('click', handleMarkClick);
      return () => container.removeEventListener('click', handleMarkClick);
    }
  }, []);

  // Manage outline styling - runs after DOM updates to apply/remove outlines
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Remove outlines from all marks
    const allMarks = container.querySelectorAll('[data-selection]');
    allMarks.forEach(mark => {
      (mark as HTMLElement).style.outline = '';
      (mark as HTMLElement).style.outlineOffset = '';
    });

    // Apply outline to active selection
    if (activeTab) {
      const activeMark = container.querySelector(`[data-selection="${activeTab}"]`);
      if (activeMark) {
        const color = activeMark.getAttribute('data-color') || '#333';
        (activeMark as HTMLElement).style.outline = `2px solid ${color}`;
        (activeMark as HTMLElement).style.outlineOffset = '1px';
      }
    }
  }, [activeTab, markdownText]); // Re-run when activeTab changes or when text re-renders

    // Handle keyboard shortcuts for creating forks directly from selection
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.shiftKey && e.key === 'Tab') {
        const sel = window.getSelection();
        if (!sel || sel.isCollapsed) return;

        const selectedText = sel.toString();
        const anchorNode = sel.anchorNode;
        if (!anchorNode || anchorNode.nodeType !== Node.TEXT_NODE) return;

        const fullNodeText = anchorNode.textContent || '';
        const anchorOffset = sel.anchorOffset;
        const focusOffset = sel.focusOffset;
        const start = Math.min(anchorOffset, focusOffset);
        const end = Math.max(anchorOffset, focusOffset);

        // Snap to word boundaries
        const leftContext = fullNodeText.slice(0, start);
        const rightContext = fullNodeText.slice(end);
        const wordLeft = leftContext.match(/\b\w+$/)?.[0] || '';
        const wordRight = rightContext.match(/^\w+\b/)?.[0] || '';
        const fullSelected = wordLeft + selectedText + wordRight;

        // Inject into markdownText - find safe match
        const id = `sel_${Date.now()}`;
        const safeIndex = markdownText.indexOf(fullSelected);
        if (safeIndex === -1) return;

        const before = markdownText.slice(0, safeIndex);
        const after = markdownText.slice(safeIndex + fullSelected.length);
        const newMarked = `${before}[[mark:id=${id}]]${fullSelected}[[/mark]]${after}`;
        const newSelection: StickySelection = {
          id,
          text: fullSelected,
          startIndex: safeIndex,
          endIndex: safeIndex + fullSelected.length,
          created: new Date(),
          userInput: '',
          llmResponse: ''
        };

        const updatedSelections = [...stickySelections, newSelection];
        
        setMarkdownText(newMarked);
        setStickySelections(updatedSelections);
        setActiveTab(id);
        
        // Save to persistence
        saveState(newMarked, updatedSelections);
        
        console.log('Created fork from selection:', fullSelected.substring(0, 50));

        // Register the text selection event for tracking
        RegistrationManager.registerEvent(EventType.CHATFORK_TEXT_SELECTED, {
          text: fullSelected.substring(0, 50) + (fullSelected.length > 50 ? '...' : ''),
          length: fullSelected.length,
          sheetId: props.sheetId || null,
          method: 'sticky'
        });

        // Register the fork creation event
        RegistrationManager.registerEvent(EventType.CHATFORK_FORK_CREATED, {
          topic: fullSelected.substring(0, 50) + (fullSelected.length > 50 ? '...' : ''),
          text: fullSelected,
          method: 'shift_tab_selection',
          sheetId: props.sheetId || null,
          forkId: id,
          timestamp: new Date().toISOString()
        });

        e.preventDefault(); // prevent focus jumping
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [markdownText, props.sheetId]);



  // Preprocess markdown to convert markers to HTML with stable colors (no outline logic here)
  const preprocessMarkdown = (text: string) => {
    return text.replace(
      /\[\[mark:id=(.*?)\]\](.*?)\[\[\/mark\]\]/gs,
      (_match, id, content) => {
        const selectionIndex = stickySelections.findIndex(sel => sel.id === id);
        const color = selectionIndex >= 0 ? getSelectionColor(selectionIndex) : '#FFF5F5';
        return `<mark data-selection="${id}" data-color="${color}" class="sticky-highlight" style="background-color: ${color}; border: 1px solid ${color}; cursor: pointer;">${content}</mark>`;
      }
    );
  };

  // Format timestamp as YYYY.MM.DD hh:mm
  const formatTimestamp = (date: Date | string | number): string => {
    let dateObj: Date;
    
    try {
      // Handle different date formats
      if (date instanceof Date) {
        dateObj = date;
      } else {
        dateObj = new Date(date);
      }
      
      // Validate the date
      if (isNaN(dateObj.getTime())) {
        console.warn('Invalid date provided to formatTimestamp:', date);
        return 'Invalid Date';
      }
      
      const year = dateObj.getFullYear();
      const month = String(dateObj.getMonth() + 1).padStart(2, '0');
      const day = String(dateObj.getDate()).padStart(2, '0');
      const hours = String(dateObj.getHours()).padStart(2, '0');
      const minutes = String(dateObj.getMinutes()).padStart(2, '0');
      
      return `${year}.${month}.${day} ${hours}:${minutes}`;
    } catch (error) {
      console.error('Error formatting timestamp:', error, date);
      return 'Error';
    }
  };

  // Remove a sticky selection
  const removeStickySelection = (id: string) => {
    // Find the selection being removed for logging
    const selectionToRemove = stickySelections.find(sel => sel.id === id);
    
    const updatedSelections = stickySelections.filter(sel => sel.id !== id);
    
    // Remove the markdown marker from the text
    const markRegex = new RegExp(`\\[\\[mark:id=${id}\\]\\](.*?)\\[\\[/mark\\]\\]`, 'g');
    const updatedMarkdown = markdownText.replace(markRegex, '$1');
    
    // If removing active tab, clear selection
    if (activeTab === id) {
      setActiveTab('');
    }
    
    setStickySelections(updatedSelections);
    setMarkdownText(updatedMarkdown);
    
    // Register the fork removal event
    if (selectionToRemove) {
      RegistrationManager.registerEvent(EventType.CHATFORK_FORK_CREATED, {
        topic: `Removed: ${selectionToRemove.text.substring(0, 50) + (selectionToRemove.text.length > 50 ? '...' : '')}`,
        text: selectionToRemove.text,
        method: 'remove_fork_button',
        action: 'removed',
        sheetId: props.sheetId || null,
        forkId: id,
        timestamp: new Date().toISOString()
      });
    }
    
    // Save to persistence
    saveState(updatedMarkdown, updatedSelections);
  };

  // Update user input for a selection
  const updateUserInput = (id: string, input: string) => {
    const updatedSelections = stickySelections.map(sel => 
      sel.id === id ? { ...sel, userInput: input } : sel
    );
    
    setStickySelections(updatedSelections);
    
    // Save to persistence
    saveState(markdownText, updatedSelections);
  };

  // Clear saved state (for debugging or reset)
  const clearSavedState = () => {
    const storageKey = getStorageKey();
    localStorage.removeItem(storageKey);
    setMarkdownText(fullText);
    setStickySelections([]);
    console.log('ChatFork state cleared:', storageKey);
  };

  const inMindSheetContext = !!props.sheetId;

  return (
    <div
      className={`chatfork-canvas-container ${inMindSheetContext ? 'in-mindsheet' : ''}`}
      ref={containerRef}
    >
      {showHeader && (
        <div className="chatfork-canvas-header">
          <h3>{title}</h3>
        </div>
      )}

      <div className="chatfork-layout">
        {/* Main content area */}
        <div className="chatfork-main-content">
          <div className="chatfork-main-header">
            <h4>Initial Exploration</h4>
          </div>
          <div
            className="chatfork-main-text"
            onClick={(e) => e.stopPropagation()}
          >
            {markdownText ? (
              <div className="chatfork-content-text">
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  rehypePlugins={[rehypeRaw]}
                >
                  {preprocessMarkdown(markdownText)}
                </ReactMarkdown>
              </div>
            ) : (
              <p className="chatfork-empty-message">
                No content available. This appears to be an empty ChatFork.
              </p>
            )}
          </div>
          
          <div className="selection-instruction">
            <small>Select text and press <kbd>Shift</kbd>+<kbd>Tab</kbd> to create a fork chat</small>
          </div>
        </div>

        {/* Vertical tabs between main text and fork area */}
        {stickySelections.length > 0 && (
          <div className="chatfork-vertical-tabs">
            {stickySelections.map((selection, index) => (
              <button
                key={selection.id}
                className={`chatfork-vertical-tab ${activeTab === selection.id ? 'active' : ''}`}
                style={{ 
                  backgroundColor: getSelectionColor(index),
                  borderColor: getSelectionColor(index)
                }}
                onClick={() => setActiveTab(selection.id)}
                title={`Fork ${index + 1}: ${selection.text.substring(0, 50)}...`}
              >
                {index + 1}
              </button>
            ))}
          </div>
        )}

        {/* Fork chats area - now contains sticky selections */}
        <div className="chatfork-forks-area">
          <div className="chatfork-forks-header">
            <h4>FORKED CHATS LEVEL 1</h4>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span className="chatfork-forks-count">{stickySelections.length} active</span>
            </div>
          </div>
                                  <div className="chatfork-forks-content">
              {stickySelections.length > 0 ? (
                activeTab ? (
                  // Show only the selected fork
                  (() => {
                    const selection = stickySelections.find(sel => sel.id === activeTab);
                    if (!selection) return <div>Fork not found</div>;
                    
                    const selectionIndex = stickySelections.findIndex(sel => sel.id === activeTab);
                    
                    return (
                      <div key={selection.id} className="fork-chat-item">
                        {/* Selected text preview with level number and metadata */}
                        <div className="fork-selected-text">
                          <div 
                            className="fork-text-preview"
                            style={{ 
                              backgroundColor: getSelectionColor(selectionIndex),
                              borderColor: getSelectionColor(selectionIndex),
                              fontFamily: 'Arial, sans-serif',
                              fontWeight: 'bold',
                              display: 'flex',
                              justifyContent: 'space-between',
                              alignItems: 'flex-start',
                              gap: '10px'
                            }}
                          >
                            <span style={{ flex: '1' }}>
                              {selection.text} <span style={{ fontWeight: 'normal' }}>[1.{selectionIndex + 1}]</span>
                            </span>
                            <div style={{ 
                              display: 'flex', 
                              alignItems: 'center', 
                              gap: '8px',
                              fontSize: '12px',
                              fontWeight: 'normal',
                              whiteSpace: 'nowrap'
                            }}>
                              <small>[{formatTimestamp(selection.created)} {selection.id}]</small>
                              <button 
                                onClick={() => removeStickySelection(selection.id)}
                                className="fork-remove-btn"
                                title="Remove fork chat"
                                style={{ marginLeft: '4px' }}
                              >
                                ✕
                              </button>
                            </div>
                          </div>
                        </div>
                        
                        <div className="fork-chat-content">
                          <div className="fork-user-input">
                            <textarea
                              placeholder="Ask about this selection..."
                              value={selection.userInput || ''}
                              onChange={(e) => updateUserInput(selection.id, e.target.value)}
                              className="fork-input-field"
                            />
                          </div>
                          
                          <div className="fork-llm-response">
                            {selection.llmResponse ? (
                              <div className="fork-response-content">
                                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                                  {selection.llmResponse}
                                </ReactMarkdown>
                              </div>
                            ) : (
                              <div className="fork-response-placeholder">
                                LLM response will appear here...
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })()
                ) : (
                  <div className="chatfork-forks-placeholder">
                    <p>Click a tab to view fork details.</p>
                  </div>
                )
            ) : (
              <div className="chatfork-forks-placeholder">
                <p>Select text and press <kbd>Shift</kbd>+<kbd>Tab</kbd> to create fork chats.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatForkCanvas;
