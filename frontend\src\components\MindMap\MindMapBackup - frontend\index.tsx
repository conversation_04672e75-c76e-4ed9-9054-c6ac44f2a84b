// MindMap/index.tsx
import React, { useState, useCallback, useEffect } from 'react';
import { MindMapProvider } from './context/MindMapContext';
// import { LLMProvider } from './contexts/LLMContext';
import { MindMapCanvas } from './components/Canvas/MindMapCanvas';
import MindMapControls from './components/Controls/MindMapControls';
// import LLMControls from './components/Controls/LLMControls';
import NodeDialogContainer from './components/Dialogs/NodeDialog/NodeDialogContainer';
import { ProjectDialog } from './components/Dialogs/ProjectDialog';
import { useMindMapStore } from './core/state/MindMapStore';
import { useAutosave } from './hooks/useAutosave';
import ManualNodeControls from './components/ControlPanel/ManualNodeControls';
import { Node } from './core/models/Node';
import './index.css';

interface MindMapProps {
  apiBaseUrl?: string;
  autosaveInterval?: number;
}

/**
 * Main MindMap component that integrates all the other components
 */
const MindMap: React.FC<MindMapProps> = ({ 
  apiBaseUrl = 'http://localhost:8000/api',
  autosaveInterval = 30000 // Default to 30 seconds
}) => {
  const [canvasSize, setCanvasSize] = useState({ width: 0, height: 0 });
  const [showProjectDialog, setShowProjectDialog] = useState(false);
  const [nodeDialogMounted, setNodeDialogMounted] = useState(false);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  
  // Enable autosave functionality
  useAutosave(autosaveInterval);
  
  // Reference to the content div for resizing
  const contentRef = React.useRef<HTMLDivElement>(null);
  
  // Update canvas size on resize
  React.useEffect(() => {
    const updateCanvasSize = () => {
      if (contentRef.current) {
        setCanvasSize({
          width: contentRef.current.offsetWidth,
          height: contentRef.current.offsetHeight
        });
      }
    };
    
    // Initial size
    updateCanvasSize();
    
    // Add resize listener
    window.addEventListener('resize', updateCanvasSize);
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', updateCanvasSize);
    };
  }, []);
  
  // Ensure NodeDialogContainer is mounted after component is rendered
  useEffect(() => {
    // Short delay to ensure component is mounted
    const timer = setTimeout(() => {
      console.log('Mounting NodeDialogContainer...');
      setNodeDialogMounted(true);
    }, 500);
    
    return () => clearTimeout(timer);
  }, []);
  
  // Watch for selected node changes from the store
  useEffect(() => {
    const unsubscribe = useMindMapStore.subscribe(
      state => {
        const { selectedNodeId, nodes } = state;
        if (selectedNodeId && nodes[selectedNodeId]) {
          setSelectedNode(nodes[selectedNodeId]);
        } else {
          setSelectedNode(null);
        }
      }
    );
    
    return () => unsubscribe();
  }, []);
  
  // Handle node selection
  const handleNodeSelect = useCallback((nodeId: string | null) => {
    console.log('MindMap: Node selected:', nodeId);
    // The MindMapCanvas component will update the store directly
  }, []);
  
  // Handle node update
  const handleNodeUpdate = useCallback((nodeId: string, updates: any) => {
    console.log('MindMap: Node updated:', nodeId, updates);
    // The NodeDialog component will update the store directly
  }, []);
  
  // Canvas control handlers
  const handleZoomIn = () => {
    console.log('Zoom in');
    // This is handled in the MindMapCanvas component
  };
  
  const handleZoomOut = () => {
    console.log('Zoom out');
    // This is handled in the MindMapCanvas component
  };
  
  const handleCenter = () => {
    console.log('Center view');
    // This is handled in the MindMapCanvas component
  };
  
  const handleRotate = () => {
    console.log('Change direction');
    // This is handled in the MindMapCanvas component
  };
  
  return (
    <MindMapProvider>
      {/* Temporarily remove LLMProvider since it doesn't exist yet */}
      {/* <LLMProvider apiBaseUrl={apiBaseUrl}> */}
        <div className="mindmap-container">
          <MindMapControls
            onZoomIn={handleZoomIn}
            onZoomOut={handleZoomOut}
            onCenter={handleCenter}
            onRotate={handleRotate}
          />
          
          <div className="mindmap-content" ref={contentRef}>
            <MindMapCanvas 
              width={canvasSize.width} 
              height={canvasSize.height}
              onNodeSelect={handleNodeSelect}
              onNodeUpdate={handleNodeUpdate}
            />
            
            <div className="mindmap-sidebar">
              {/* Manual Node Controls */}
              <ManualNodeControls selectedNode={selectedNode} />
              
              {/* Temporarily remove LLMControls since it doesn't exist yet */}
              {/* <LLMControls /> */}
            </div>
          </div>
          
          {/* Always render NodeDialogContainer */}
          <NodeDialogContainer key="node-dialog-container" />
          
          {/* Render a second instance as backup to ensure it's mounted */}
          {nodeDialogMounted && <NodeDialogContainer key="node-dialog-container-backup" />}
          
          {/* Use the ProjectDialog component's own state management */}
          {/* <ProjectDialog 
            isOpen={showProjectDialog} 
            onClose={() => setShowProjectDialog(false)} 
          /> */}
        </div>
      {/* </LLMProvider> */}
    </MindMapProvider>
  );
};

export default MindMap;

