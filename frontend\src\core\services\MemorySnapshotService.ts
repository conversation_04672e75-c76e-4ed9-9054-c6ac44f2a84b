/**
 * MemorySnapshotService.ts
 *
 * Enhanced MBCP-compatible memory snapshot service.
 * Implements Phase 1.1.3 of the Development Plan.
 */

import { useMindBookStore } from '../state/MindBookStore';
import { useChatStore } from '../../governance/chat/state/ChatStore';
import { useContextStore } from '../../features/context/store/ContextStore';
import { ChatMemoryService } from '../../services/ChatMemoryService';
import RegistrationManager from '../services/RegistrationManager';

interface MemorySnapshot {
  timestamp: string;
  mindBook: any;
  contextSettings: any;
  chatMessages: any;
  chatMemory: any;
  localStorage: Record<string, any>;
  logs: any;
}

interface MBCPMemorySnapshot {
  id: string;
  timestamp: string;
  version: string;
  format: 'MBCP';
  mindBook: {
    sheets: any[];
    activeSheetId: string | null;
    name: string | null;
  };
  contextSettings: {
    current: any;
    available: any[];
  };
  conversationThreads: {
    [threadId: string]: {
      messages: any[];
      parentThreadId?: string;
      sheetId: string;
      contextSummary?: string;
    };
  };
  memoryEntries: {
    [entryId: string]: {
      type: string;
      content: any;
      relevanceScore: number;
      sheetId?: string;
      timestamp: string;
    };
  };
  events: any[];
  localStorage: Record<string, any>;
  metadata: {
    totalSheets: number;
    totalMessages: number;
    totalMemoryEntries: number;
    compressionRatio?: number;
  };
}

class MemorySnapshotService {
  private static filterKeys = [
    'mindbook_',
    'mindmap_sheet_',
    'context_settings_',
    'context_settings_list',
    'current_context_settings',
    'mindbook_autosave'
  ];

  private static version = '1.0.0';
  private static memoryEntries: Map<string, any> = new Map();
  private static conversationThreads: Map<string, any> = new Map();

  static collectSnapshot = async (): Promise<MemorySnapshot> => {
    const mindBook = useMindBookStore.getState();
    const chatStore = useChatStore.getState();
    const contextStore = useContextStore.getState();
    const chatMemory = ChatMemoryService.getInstance().getCurrentContext();

    const storage: Record<string, any> = {};
    Object.keys(localStorage).forEach(key => {
      if (this.filterKeys.some(prefix => key.startsWith(prefix))) {
        try {
          storage[key] = JSON.parse(localStorage.getItem(key) as string);
        } catch {
          storage[key] = localStorage.getItem(key);
        }
      }
    });

    // Capture logs from backend if available
    let logs: any = null;
    try {
      const resp = await fetch('/api/logging/events');
      if (resp.ok) {
        logs = await resp.json();
      }
    } catch {
      // Ignore errors when fetching logs
    }

    return {
      timestamp: new Date().toISOString(),
      mindBook,
      contextSettings: contextStore.currentContextSettings,
      chatMessages: chatStore.messages,
      chatMemory,
      localStorage: storage,
      logs
    };
  };

  static exportSnapshot = async (): Promise<boolean> => {
    try {
      const snapshot = await this.collectSnapshot();
      const resp = await fetch('/api/memory/snapshot', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(snapshot)
      });
      return resp.ok;
    } catch {
      return false;
    }
  };

  /**
   * Collect MBCP-compatible memory snapshot
   * Implements Phase 1.1.3: MBCP-compatible snapshot format
   */
  static collectMBCPSnapshot = async (): Promise<MBCPMemorySnapshot> => {
    const mindBook = useMindBookStore.getState();
    const chatStore = useChatStore.getState();
    const contextStore = useContextStore.getState();
    const chatMemory = ChatMemoryService.getInstance().getCurrentContext();

    // Collect localStorage data
    const storage: Record<string, any> = {};
    Object.keys(localStorage).forEach(key => {
      if (this.filterKeys.some(prefix => key.startsWith(prefix))) {
        try {
          storage[key] = JSON.parse(localStorage.getItem(key) as string);
        } catch {
          storage[key] = localStorage.getItem(key);
        }
      }
    });

    // Collect events from RegistrationManager
    const events = RegistrationManager.getRecentEvents(50); // Get last 50 events

    // Build conversation threads from chat memory
    const conversationThreads: { [threadId: string]: any } = {};
    if (chatMemory.recentMessages.length > 0) {
      const currentThreadId = `thread_${Date.now()}`;
      conversationThreads[currentThreadId] = {
        messages: chatMemory.recentMessages,
        sheetId: mindBook.activeSheetId || 'unknown',
        contextSummary: this.generateContextSummary(chatMemory.recentMessages)
      };
    }

    // Build memory entries from various sources
    const memoryEntries: { [entryId: string]: any } = {};

    // Add chat messages as memory entries
    chatMemory.recentMessages.forEach((message, index) => {
      const entryId = `msg_${message.id}`;
      memoryEntries[entryId] = {
        type: 'conversation',
        content: {
          message: message.content,
          sender: message.sender,
          timestamp: message.timestamp,
          tags: message.tags || []
        },
        relevanceScore: this.calculateMessageRelevance(message, index),
        sheetId: mindBook.activeSheetId,
        timestamp: message.timestamp.toISOString()
      };
    });

    // Add context settings as memory entries
    if (contextStore.currentContextSettings) {
      const entryId = `ctx_${Date.now()}`;
      memoryEntries[entryId] = {
        type: 'context',
        content: contextStore.currentContextSettings,
        relevanceScore: 1.0,
        timestamp: new Date().toISOString()
      };
    }

    // Add sheet states as memory entries
    mindBook.sheets.forEach(sheet => {
      const entryId = `sheet_${sheet.id}`;
      memoryEntries[entryId] = {
        type: 'node_state',
        content: {
          sheetId: sheet.id,
          title: sheet.title,
          contentType: sheet.contentType,
          hasContent: !!sheet.content
        },
        relevanceScore: sheet.id === mindBook.activeSheetId ? 1.0 : 0.7,
        sheetId: sheet.id,
        timestamp: new Date().toISOString()
      };
    });

    const snapshot: MBCPMemorySnapshot = {
      id: `mbcp_snapshot_${Date.now()}`,
      timestamp: new Date().toISOString(),
      version: this.version,
      format: 'MBCP',
      mindBook: {
        sheets: mindBook.sheets,
        activeSheetId: mindBook.activeSheetId,
        name: mindBook.name
      },
      contextSettings: {
        current: contextStore.currentContextSettings,
        available: contextStore.contextSettingsList || []
      },
      conversationThreads,
      memoryEntries,
      events,
      localStorage: storage,
      metadata: {
        totalSheets: mindBook.sheets.length,
        totalMessages: chatMemory.recentMessages.length,
        totalMemoryEntries: Object.keys(memoryEntries).length
      }
    };

    return snapshot;
  };

  /**
   * Export MBCP-compatible snapshot to backend
   */
  static exportMBCPSnapshot = async (): Promise<boolean> => {
    try {
      const snapshot = await this.collectMBCPSnapshot();
      const resp = await fetch('/api/memory/mbcp-snapshot', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(snapshot)
      });
      return resp.ok;
    } catch (error) {
      console.error('Failed to export MBCP snapshot:', error);
      return false;
    }
  };

  /**
   * Generate context summary from messages
   */
  private static generateContextSummary(messages: any[]): string {
    if (messages.length === 0) return 'No conversation history';

    const userMessages = messages.filter(m => m.sender === 'user');
    const llmMessages = messages.filter(m => m.sender === 'llm');

    return `Conversation with ${userMessages.length} user messages and ${llmMessages.length} LLM responses`;
  }

  /**
   * Calculate relevance score for a message
   */
  private static calculateMessageRelevance(message: any, index: number): number {
    let score = 0.5; // Base score

    // More recent messages are more relevant
    score += (index / 10) * 0.3;

    // User messages are slightly more relevant
    if (message.sender === 'user') score += 0.1;

    // Messages with tags are more relevant
    if (message.tags && message.tags.length > 0) score += 0.2;

    // Cap at 1.0
    return Math.min(score, 1.0);
  }

  /**
   * Store memory entry for future retrieval
   */
  static storeMemoryEntry(entryId: string, entry: any): void {
    this.memoryEntries.set(entryId, entry);
  }

  /**
   * Retrieve memory entries by sheet ID
   */
  static getMemoryEntriesBySheet(sheetId: string): any[] {
    const entries: any[] = [];
    for (const [id, entry] of this.memoryEntries) {
      if (entry.sheetId === sheetId) {
        entries.push({ id, ...entry });
      }
    }

    // Sort by relevance score
    return entries.sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  /**
   * Create conversation thread
   */
  static createConversationThread(sheetId: string, parentThreadId?: string): string {
    // Generate unique thread ID with timestamp + random component to prevent collisions
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substr(2, 9);
    const threadId = `thread_${timestamp}_${randomSuffix}_${sheetId}`;

    this.conversationThreads.set(threadId, {
      threadId,
      parentThreadId,
      sheetId,
      messages: [],
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    });

    return threadId;
  }

  /**
   * Add message to conversation thread
   */
  static addMessageToThread(threadId: string, message: any): boolean {
    const thread = this.conversationThreads.get(threadId);
    if (!thread) return false;

    thread.messages.push(message);
    thread.lastUpdated = new Date().toISOString();

    return true;
  }
}

export default MemorySnapshotService;

// Legacy exports
export const collectMemorySnapshot = () => MemorySnapshotService.collectSnapshot();
export const exportMemorySnapshot = () => MemorySnapshotService.exportSnapshot();

// MBCP-compatible exports
export const collectMBCPSnapshot = () => MemorySnapshotService.collectMBCPSnapshot();
export const exportMBCPSnapshot = () => MemorySnapshotService.exportMBCPSnapshot();
export const storeMemoryEntry = (entryId: string, entry: any) => MemorySnapshotService.storeMemoryEntry(entryId, entry);
export const getMemoryEntriesBySheet = (sheetId: string) => MemorySnapshotService.getMemoryEntriesBySheet(sheetId);
export const createConversationThread = (sheetId: string, parentThreadId?: string) => MemorySnapshotService.createConversationThread(sheetId, parentThreadId);
export const addMessageToThread = (threadId: string, message: any) => MemorySnapshotService.addMessageToThread(threadId, message);

// Type exports
export type { MemorySnapshot, MBCPMemorySnapshot };
