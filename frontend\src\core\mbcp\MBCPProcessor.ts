/**
 * Unified MBCP Processor
 * 
 * This is the single source of truth for processing MBCP (MindBack Communication Protocol) data.
 * Consolidates all previous implementations and handles multiple data format variations.
 * 
 * Key Features:
 * - Supports all existing MBCP data formats
 * - Normalizes data to a consistent internal format
 * - Follows teleological mindmap workflow requirements
 * - Proper error handling and logging
 * - Type-safe implementation
 */

import { useMindMapStore } from '../state/MindMapStore';
import { useMindBookStore } from '../state/MindBookStore';
import { getMindMapStore } from '../state/MindMapStoreFactory';
import { v4 as uuidv4 } from 'uuid';

// Unified MBCP interfaces
export interface MBCPNode {
  id?: string;
  text: string;
  description?: string;
  children?: MBCPNode[];
  metadata?: {
    intent?: string;
    agent?: string;
    tags?: string[];
    nodePath?: string;
    [key: string]: any;
  };
}

export interface MBCPData {
  // Format 1: { mindmap: { root: MBCPNode } }
  mindmap?: {
    root: MBCPNode;
  };
  // Format 2: { root: MBCPNode }
  root?: MBCPNode;
  // Format 3: { structure: { root: MBCPNode } }
  structure?: {
    root: MBCPNode;
  };
  // Additional fields
  text?: string;
  description?: string;
  intent?: string;
  type?: string;
  // Nested actions
  suggestedActions?: Array<{
    type: string;
    label: string;
    data: {
      mbcpData?: MBCPData;
      [key: string]: any;
    };
  }>;
}

export interface MBCPProcessingResult {
  success: boolean;
  rootNodeId?: string | null;
  error?: string;
  dataFormat?: string;
}

/**
 * Unified MBCP processor class
 */
export class UnifiedMBCPProcessor {
  private static instance: UnifiedMBCPProcessor;
  
  public static getInstance(): UnifiedMBCPProcessor {
    if (!UnifiedMBCPProcessor.instance) {
      UnifiedMBCPProcessor.instance = new UnifiedMBCPProcessor();
    }
    return UnifiedMBCPProcessor.instance;
  }

  /**
   * Main processing function - handles all MBCP data formats
   */
  public processMBCPData(mbcpData: MBCPData): MBCPProcessingResult {
    try {
      console.log('UnifiedMBCPProcessor: Starting MBCP data processing');
      console.log('UnifiedMBCPProcessor: Input data format:', this.detectDataFormat(mbcpData));

      if (!mbcpData) {
        return {
          success: false,
          error: 'No MBCP data provided'
        };
      }

      // Check for nested suggestedActions format first
      if (mbcpData.suggestedActions && Array.isArray(mbcpData.suggestedActions)) {
        console.log('UnifiedMBCPProcessor: Processing nested suggestedActions format');
        const createAction = mbcpData.suggestedActions.find(action => action.type === 'create_mindmap');
        if (createAction?.data?.mbcpData) {
          console.log('UnifiedMBCPProcessor: Found nested MBCP data in suggestedActions');
          return this.processMBCPData(createAction.data.mbcpData);
        }
      }

      // Normalize the data format
      const normalizedRoot = this.normalizeMBCPData(mbcpData);
      if (!normalizedRoot) {
        return {
          success: false,
          error: 'Failed to normalize MBCP data'
        };
      }

      // Create mindmap using sheet-specific store
      const rootNodeId = this.createMindmapFromNormalizedData(normalizedRoot);
      
      if (!rootNodeId) {
        return {
          success: false,
          error: 'Failed to create mindmap from normalized data'
        };
      }

      console.log('UnifiedMBCPProcessor: Successfully processed MBCP data');
      return {
        success: true,
        rootNodeId,
        dataFormat: this.detectDataFormat(mbcpData)
      };

    } catch (error) {
      console.error('UnifiedMBCPProcessor: Error processing MBCP data:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Normalize MBCP data to a consistent format
   * Handles all known format variations
   */
  private normalizeMBCPData(mbcpData: MBCPData): MBCPNode | null {
    console.log('UnifiedMBCPProcessor: Normalizing MBCP data format');

    let rootNode: MBCPNode | null = null;

    // Format 1: { mindmap: { root: {...} } }
    if (mbcpData.mindmap?.root) {
      console.log('UnifiedMBCPProcessor: Detected Format 1 - mbcpData.mindmap.root');
      rootNode = mbcpData.mindmap.root;
    }
    // Format 2: { root: {...} }
    else if (mbcpData.root) {
      console.log('UnifiedMBCPProcessor: Detected Format 2 - mbcpData.root');
      rootNode = mbcpData.root;
    }
    // Format 3: { structure: { root: {...} } }
    else if (mbcpData.structure?.root) {
      console.log('UnifiedMBCPProcessor: Detected Format 3 - mbcpData.structure.root');
      rootNode = mbcpData.structure.root;
    }
    // Format 4: Direct data { text: "...", description: "..." }
    else if (mbcpData.text || mbcpData.description) {
      console.log('UnifiedMBCPProcessor: Detected Format 4 - Direct data');
      rootNode = {
        text: mbcpData.text || 'Teleological Mindmap',
        description: mbcpData.description || '',
        metadata: {
          intent: mbcpData.intent || 'teleological'
        },
        children: []
      };
    }
    // Fallback: Create minimal structure
    else {
      console.warn('UnifiedMBCPProcessor: No recognizable format, creating fallback structure');
      rootNode = {
        text: 'Teleological Mindmap',
        description: 'Generated mindmap from unstructured data',
        metadata: {
          intent: 'teleological'
        },
        children: []
      };
    }

    // Ensure the root node has required fields
    if (rootNode) {
      rootNode.id = rootNode.id || uuidv4();
      rootNode.metadata = rootNode.metadata || {};
      rootNode.metadata.intent = rootNode.metadata.intent || 'teleological';
      rootNode.children = rootNode.children || [];
    }

    console.log('UnifiedMBCPProcessor: Normalized root node:', rootNode);
    return rootNode;
  }

  /**
   * Create mindmap from normalized data using sheet-specific store
   */
  private createMindmapFromNormalizedData(rootNode: MBCPNode): string | null {
    try {
      console.log('UnifiedMBCPProcessor: Creating mindmap from normalized data');

      // Get the active sheet from MindBookStore
      const mindBookStore = useMindBookStore.getState();
      const activeSheetId = mindBookStore.activeSheetId;

      if (!activeSheetId) {
        console.error('UnifiedMBCPProcessor: No active sheet found');
        return null;
      }

      console.log('UnifiedMBCPProcessor: Using active sheet:', activeSheetId);

      // Get the sheet-specific MindMap store
      const sheetStore = getMindMapStore(activeSheetId);
      const store = sheetStore.getState();

      // Create project name from root node
      const projectName = rootNode.text || 'Teleological Mindmap';
      console.log('UnifiedMBCPProcessor: Creating project:', projectName);

      // Initialize the store with sheet ID and create new project
      store.initialize(window.innerWidth, window.innerHeight, activeSheetId);
      const rootNodeId = store.createNewProject(projectName);

      if (!rootNodeId) {
        console.error('UnifiedMBCPProcessor: Failed to create root node');
        return null;
      }

      // Update root node with MBCP data
      store.updateNode(rootNodeId, {
        text: rootNode.text,
        description: rootNode.description || rootNode.text,
        metadata: {
          ...rootNode.metadata,
          nodePath: '1.0',
          isRoot: true,
          creationSource: 'mbcp_unified'
        }
      });

      // Process children if they exist
      if (rootNode.children && rootNode.children.length > 0) {
        console.log('UnifiedMBCPProcessor: Processing children nodes');
        this.processChildrenNodes(store, rootNodeId, rootNode.children);
      }

      // Apply layout after creation - now with proper sheetId
      setTimeout(() => {
        try {
          // TEMPORARILY DISABLED: Layout update causing initialization issues
          // Will re-enable after debugging the sheetId issue
          console.log('UnifiedMBCPProcessor: Layout update temporarily disabled for sheet:', activeSheetId);
          
          // if (store.updateLayout) {
          //   console.log('UnifiedMBCPProcessor: Applying layout for sheet:', activeSheetId);
          //   store.updateLayout('topDown', 'system');
          // }
        } catch (layoutError) {
          console.warn('UnifiedMBCPProcessor: Layout update failed, but mindmap was created:', layoutError);
        }
      }, 100);

      console.log('UnifiedMBCPProcessor: Successfully created mindmap with root node ID:', rootNodeId);
      return rootNodeId;

    } catch (error) {
      console.error('UnifiedMBCPProcessor: Error creating mindmap:', error);
      return null;
    }
  }

  /**
   * Process children nodes recursively
   */
  private processChildrenNodes(
    storeState: any,
    parentId: string,
    children: MBCPNode[],
    parentPath: string = '1'
  ): void {
    children.forEach((child, index) => {
      if (!child.text) {
        console.warn('UnifiedMBCPProcessor: Child node missing text, skipping');
        return;
      }

      // Calculate position with intelligent spacing
      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;
      
      const horizontalSpacing = Math.min(250, windowWidth * 0.15);
      const verticalOffset = (index - Math.floor(children.length / 2)) * 100;
      
      const x = Math.max(100, Math.min(windowWidth / 2 + horizontalSpacing, windowWidth - 200));
      const y = Math.max(100, Math.min(windowHeight / 2 + verticalOffset, windowHeight - 100));

      // Create node path
      const nodePath = parentPath.endsWith('.0') 
        ? `${parentPath.slice(0, -2)}.${index + 1}`
        : `${parentPath}.${index + 1}`;

      console.log(`UnifiedMBCPProcessor: Creating child node ${index + 1}: "${child.text}"`);

      // Add child node
      const childId = storeState.addNode(
        parentId,
        child.text,
        x,
        y,
        {
          description: child.description || child.text,
          metadata: {
            nodePath,
            intent: child.metadata?.intent || 'teleological',
            agent: child.metadata?.agent,
            tags: child.metadata?.tags || [],
            creationSource: 'mbcp_unified',
            ...child.metadata
          }
        }
      );

      // Process grandchildren recursively
      if (childId && child.children && child.children.length > 0) {
        this.processChildrenNodes(storeState, childId, child.children, nodePath);
      }
    });
  }

  /**
   * Detect the format of MBCP data for logging
   */
  private detectDataFormat(mbcpData: MBCPData): string {
    if (mbcpData.suggestedActions?.length > 0) return 'suggestedActions_nested';
    if (mbcpData.mindmap?.root) return 'mindmap.root';
    if (mbcpData.root) return 'root';
    if (mbcpData.structure?.root) return 'structure.root';
    if (mbcpData.text || mbcpData.description) return 'direct_data';
    return 'unknown';
  }
}

// Export singleton instance and convenience functions
export const mbcpProcessor = UnifiedMBCPProcessor.getInstance();

/**
 * Main function for processing MBCP data - replaces all other processors
 */
export const processMBCPData = (mbcpData: MBCPData): MBCPProcessingResult => {
  return mbcpProcessor.processMBCPData(mbcpData);
};

/**
 * Legacy compatibility function - matches previous function signatures
 */
export const createMindmapFromMBCP = (mbcpData: MBCPData): boolean => {
  const result = mbcpProcessor.processMBCPData(mbcpData);
  return result.success;
}; 