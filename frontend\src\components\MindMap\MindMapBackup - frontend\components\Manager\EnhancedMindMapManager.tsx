import React, { useState, useRef, useCallback } from 'react';
import Dialog from '@mui/material/Dialog';
import Paper, { PaperProps } from '@mui/material/Paper';
import Draggable from 'react-draggable';
import { useMindMapStore } from '../../core/state/MindMapStore';
import { LayoutSelector } from '../../layouts/components/LayoutSelector';
import { LayoutStrategyType } from '../../layouts/types';
import './EnhancedMindMapManager.css';

// Draggable Paper component for the dialog
function DraggablePaper(props: PaperProps) {
  const nodeRef = useRef(null);

  return (
    <Draggable
      handle=".manager-dialog-header"
      bounds="body"
      nodeRef={nodeRef}
      defaultPosition={{x: 20, y: 60}}
      cancel=".manager-dialog-header-buttons"
      enableUserSelectHack={true}
      onStart={(e) => {
        // Ensure the drag start event doesn't get blocked
        e.stopPropagation();
        document.body.classList.add('react-draggable-transparent-selection');
      }}
      onStop={() => {
        document.body.classList.remove('react-draggable-transparent-selection');
      }}
    >
      <Paper {...props} ref={nodeRef} className="mindmap-manager-paper" />
    </Draggable>
  );
}

interface EnhancedMindMapManagerProps {
  open: boolean;
  onClose: () => void;
}

// Function to convert between old and new direction formats
const convertLegacyDirectionToStrategy = (direction: string): LayoutStrategyType => {
  switch (direction) {
    case '0': return 'leftToRight';  // Right
    case '90': return 'topDown';     // Down
    case '180': return 'leftToRight'; // Left (still use leftToRight since it's just mirrored)
    case '270': return 'bottomUp';   // Up
    default: return 'leftToRight';
  }
};

// Helper to get layout display name with highlight for compact layout
const getLayoutDisplayName = (strategyType: LayoutStrategyType): JSX.Element | string => {
  if (strategyType === 'compactLeftToRight') {
    return (
      <span className="compact-layout-label">
        Compact Layout
        <span className="new-badge">NEW!</span>
      </span>
    );
  }

  const strategyNames: Record<LayoutStrategyType, string> = {
    'leftToRight': 'Left to Right',
    'topDown': 'Top Down',
    'bottomUp': 'Bottom Up',
    'radial': 'Radial',
    'compactLeftToRight': 'Compact Layout' // Fallback plain text
  };

  return strategyNames[strategyType];
};

export const EnhancedMindMapManager: React.FC<EnhancedMindMapManagerProps> = ({
  open,
  onClose
}) => {
  // State
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [size, setSize] = useState({ width: 320, height: 400 });
  const [showSettings, setShowSettings] = useState(false);
  const dialogRef = useRef<HTMLDivElement>(null);
  const resizeStartPos = useRef({ x: 0, y: 0 });
  const startSize = useRef({ width: 0, height: 0 });
  const isResizing = useRef(false);

  // Get data from store using selectors
  const nodes = useMindMapStore(state => state.nodes);
  const connections = useMindMapStore(state => state.connections);
  const updateConnection = useMindMapStore(state => state.updateConnection);
  const scale = useMindMapStore(state => state.scale);
  const position = useMindMapStore(state => state.position);
  const llmModel = useMindMapStore(state => state.llmModel);
  const setLlmModel = useMindMapStore(state => state.setLlmModel);
  const setShowProjectDialog = useMindMapStore(state => state.setShowProjectDialog);
  const direction = useMindMapStore(state => state.direction);

  // Get node and connection counts
  const nodeCount = Object.keys(nodes).length;
  const connectionCount = Array.isArray(connections) ? connections.length : 0;

  // Handle minimize/expand
  const handleMinimize = useCallback(() => {
    setIsCollapsed(!isCollapsed);
  }, [isCollapsed]);

  // Apply default settings to all connections
  const applyDefaultSettings = () => {
    if (!Array.isArray(connections)) return;

    connections.forEach(connection => {
      updateConnection(connection.id, {
        lineStyle: 'angled',
        thickness: 2,
        color: '#9ca3af',
        showArrow: false,
        type: 'solid'
      });
    });
  };

  // Handle zoom functions
  const handleZoomIn = () => {
    useMindMapStore.setState(state => ({
      scale: Math.min(state.scale * 1.2, 2.0)
    }));
  };

  const handleZoomOut = () => {
    useMindMapStore.setState(state => ({
      scale: Math.max(state.scale / 1.2, 0.3)
    }));
  };

  // Center view on all nodes
  const centerView = () => {
    // Get all nodes
    const nodesList = Object.values(nodes);

    if (nodesList.length === 0) return;

    // Calculate bounding box
    let minX = Infinity, minY = Infinity;
    let maxX = -Infinity, maxY = -Infinity;

    nodesList.forEach(node => {
      minX = Math.min(minX, node.x);
      minY = Math.min(minY, node.y);
      maxX = Math.max(maxX, node.x);
      maxY = Math.max(maxY, node.y);
    });

    // Calculate center of nodes
    const centerX = (minX + maxX) / 2;
    const centerY = (minY + maxY) / 2;

    // Calculate canvas center
    const canvasWidth = window.innerWidth;
    const canvasHeight = window.innerHeight - 60; // Adjust for header

    // Set position to center nodes in canvas
    useMindMapStore.setState({
      position: {
        x: (canvasWidth / 2) - (centerX * scale),
        y: (canvasHeight / 2) - (centerY * scale)
      }
    });

    console.log('Centered view on all nodes');
  };

  // Handle layout rotation
  const handleRotate = () => {
    const state = useMindMapStore.getState();
    const direction = state.direction;
    const directions: ['0', '90', '180', '270'] = ['0', '90', '180', '270'];
    const currentIndex = directions.indexOf(direction as '0' | '90' | '180' | '270');
    const nextDirection = directions[(currentIndex + 1) % directions.length];

    state.setDirection(nextDirection);
    state.updateLayout();

    console.log(`Rotated layout to ${nextDirection} degrees`);
  };

  // Handle LLM model change
  const handleModelChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setLlmModel(e.target.value as any);
  };

  // Handle resize start
  const handleResizeStart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    isResizing.current = true;
    resizeStartPos.current = { x: e.clientX, y: e.clientY };
    startSize.current = { ...size };

    // Add the transparent selection class to the body
    document.body.classList.add('react-draggable-transparent-selection');

    const handleMouseMove = (e: MouseEvent) => {
      if (isResizing.current) {
        const deltaX = e.clientX - resizeStartPos.current.x;
        const deltaY = e.clientY - resizeStartPos.current.y;

        setSize({
          width: Math.max(280, startSize.current.width + deltaX),
          height: Math.max(200, startSize.current.height + deltaY)
        });
      }
    };

    const handleMouseUp = () => {
      isResizing.current = false;
      // Remove the transparent selection class from the body
      document.body.classList.remove('react-draggable-transparent-selection');
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleResetPosition = () => {
    // Simple reset by forcing a re-render
    if (dialogRef.current) {
      const draggableNode = dialogRef.current.closest('.react-draggable');
      if (draggableNode) {
        (draggableNode as HTMLElement).style.transform = 'translate(20px, 60px)';
      }
    }
  };

  const handleResetSize = () => {
    setSize({ width: 320, height: 400 });
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      PaperComponent={DraggablePaper}
      PaperProps={{
        style: {
          width: isCollapsed ? 'auto' : `${size.width}px`,
          height: isCollapsed ? 'auto' : `${size.height}px`,
          maxWidth: 'none',
          maxHeight: 'none',
          margin: 0,
          position: 'absolute',
          pointerEvents: 'auto'
        },
        elevation: 24,
        className: "mindmap-manager-paper"
      }}
      hideBackdrop
      disableEnforceFocus
      aria-labelledby="draggable-manager-dialog"
      className="mindmap-manager-dialog-root"
      sx={{
        zIndex: 1800,
        '& .MuiDialog-container': {
          position: 'fixed',
          top: 0,
          left: 0,
          height: '100%',
          width: '100%',
          alignItems: 'flex-start',
          justifyContent: 'flex-start',
          pointerEvents: 'none',
          transform: 'none'
        },
        '& .MuiPaper-root': {
          pointerEvents: 'auto',
          position: 'absolute',
          overflow: 'visible'
        },
        '& .MuiBackdrop-root': {
          backgroundColor: 'transparent'
        }
      }}
    >
      <div className="mindmap-manager-dialog" ref={dialogRef}>
        {/* Manager Dialog Header */}
        <div className="manager-dialog-header">
          <div className="manager-dialog-header-title">
            <img
              src="/Logo/MB_logo.jpg"
              alt="MindBack Logo"
              className="manager-dialog-header-logo"
              width="24"
              height="24"
              draggable="false"
            />
            <span className="manager-dialog-header-text">MindMap Manager</span>
          </div>
          <div className="manager-dialog-header-buttons">
            <button
              onClick={handleResetPosition}
              className="manager-dialog-header-button"
              title="Reset Position"
            >
              ↖
            </button>
            <button
              onClick={handleResetSize}
              className="manager-dialog-header-button"
              title="Reset Size"
            >
              ⊡
            </button>
            <button
              onClick={handleMinimize}
              className="manager-dialog-header-button"
              title={isCollapsed ? "Expand" : "Collapse"}
            >
              {isCollapsed ? "□" : "_"}
            </button>
            <button
              onClick={onClose}
              className="manager-dialog-close-button"
              title="Close"
            >
              ×
            </button>
          </div>
        </div>

        {!isCollapsed && (
          <div className="mindmap-manager-content">
            {/* Stats */}
            <div className="mindmap-stats-container">
              <div className="mindmap-stat-item">
                <span className="stat-label">Nodes:</span>
                <span className="stat-value">{nodeCount}</span>
              </div>
              <div className="mindmap-stat-item">
                <span className="stat-label">Connections:</span>
                <span className="stat-value">{connectionCount}</span>
              </div>
              <div className="mindmap-stat-item">
                <span className="stat-label">Scale:</span>
                <span className="stat-value">{scale.toFixed(2)}x</span>
              </div>
              <div className="mindmap-stat-item">
                <span className="stat-label">Position:</span>
                <span className="stat-value">({Math.round(position.x)}, {Math.round(position.y)})</span>
              </div>
            </div>

            {/* View Controls */}
            <div className="control-group">
              <h4 className="control-group-title">Canvas Controls</h4>
              <div className="control-section">
                <button
                  className="manager-control-button"
                  onClick={handleZoomIn}
                  title="Zoom In"
                >
                  <span className="button-icon">+</span>
                  <span className="button-text">Zoom In</span>
                </button>
                <button
                  className="manager-control-button"
                  onClick={handleZoomOut}
                  title="Zoom Out"
                >
                  <span className="button-icon">-</span>
                  <span className="button-text">Zoom Out</span>
                </button>
              </div>
              <div className="control-section">
                <button
                  className="manager-control-button"
                  onClick={centerView}
                  title="Center View"
                >
                  <span className="button-icon">⌖</span>
                  <span className="button-text">Center View</span>
                </button>
                <button
                  className="manager-control-button"
                  onClick={handleRotate}
                  title="Rotate Layout"
                >
                  <span className="button-icon">↻</span>
                  <span className="button-text">Rotate Layout</span>
                </button>
              </div>
            </div>

            {/* Advanced Layout Controls */}
            <div className="control-group">
              <h4 className="control-group-title">Advanced Layout</h4>
              <div className="control-section-full">
                <LayoutSelector
                  defaultStrategy={convertLegacyDirectionToStrategy(direction)}
                />
              </div>
            </div>

            {/* Project Controls */}
            <div className="control-group">
              <h4 className="control-group-title">Project Controls</h4>
              <div className="control-section">
                <button
                  className="manager-control-button"
                  onClick={() => setShowProjectDialog(true)}
                  title="Open Projects"
                >
                  <span className="button-icon">📂</span>
                  <span className="button-text">Open Project</span>
                </button>
                <button
                  className="manager-control-button"
                  onClick={() => setShowSettings(!showSettings)}
                  title="Settings"
                >
                  <span className="button-icon">⚙️</span>
                  <span className="button-text">Settings</span>
                </button>
              </div>
            </div>

            {showSettings && (
              <div className="settings-panel">
                <h4 className="control-group-title">Connection Settings</h4>
                <button
                  className="settings-button"
                  onClick={applyDefaultSettings}
                  title="Apply default style to all connections"
                >
                  Apply Default Style
                </button>

                <h4 className="control-group-title">LLM Settings</h4>
                <div className="llm-settings">
                  <label htmlFor="llm-model-select">Model:</label>
                  <select
                    id="llm-model-select"
                    value={llmModel}
                    onChange={handleModelChange}
                    className="llm-model-select"
                  >
                    <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                    <option value="gpt-4">GPT-4</option>
                    <option value="gpt-4-mini">GPT-4 Mini</option>
                    <option value="claude-3.5-sonnet">Claude 3.5 Sonnet</option>
                    <option value="claude-3.7-sonnet">Claude 3.7 Sonnet</option>
                  </select>
                </div>
              </div>
            )}

            {/* Resize handle */}
            <div
              className="manager-resize-handle"
              onMouseDown={handleResizeStart}
            />
          </div>
        )}
      </div>
    </Dialog>
  );
};