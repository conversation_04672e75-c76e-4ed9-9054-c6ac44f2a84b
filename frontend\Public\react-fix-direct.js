// Direct fix for React compatibility issues
(function() {
  console.log('Applying direct React fix...');
  
  // Create a global React object if it doesn't exist
  if (!window.React) {
    console.log('Creating global React object');
    window.React = {};
  }
  
  // Ensure the React object has all necessary properties
  const React = window.React;
  
  // Add basic React API methods if they don't exist
  if (!React.createElement) {
    React.createElement = function() { return {}; };
    console.log('Added createElement to React');
  }
  
  if (!React.Fragment) {
    React.Fragment = Symbol('Fragment');
    console.log('Added Fragment to React');
  }
  
  if (!React.StrictMode) {
    React.StrictMode = Symbol('StrictMode');
    console.log('Added StrictMode to React');
  }
  
  // Add React hooks if they don't exist
  if (!React.useState) {
    React.useState = function(initialState) {
      return [
        typeof initialState === 'function' ? initialState() : initialState,
        function() {}
      ];
    };
    console.log('Added useState to React');
  }
  
  if (!React.useEffect) {
    React.useEffect = function() {};
    console.log('Added useEffect to React');
  }
  
  if (!React.useContext) {
    React.useContext = function() { return {}; };
    console.log('Added useContext to React');
  }
  
  if (!React.useReducer) {
    React.useReducer = function(reducer, initialState) {
      return [initialState, function() {}];
    };
    console.log('Added useReducer to React');
  }
  
  if (!React.useCallback) {
    React.useCallback = function(callback) { return callback; };
    console.log('Added useCallback to React');
  }
  
  if (!React.useMemo) {
    React.useMemo = function(factory) { return factory(); };
    console.log('Added useMemo to React');
  }
  
  if (!React.useRef) {
    React.useRef = function(initialValue) { return { current: initialValue }; };
    console.log('Added useRef to React');
  }
  
  // Create the internal structure if it doesn't exist
  if (!React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) {
    console.log('Creating React internals');
    React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = {};
  }
  
  const internals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
  
  // Create ReactCurrentDispatcher if it doesn't exist
  if (!internals.ReactCurrentDispatcher) {
    console.log('Creating ReactCurrentDispatcher');
    internals.ReactCurrentDispatcher = {};
  }
  
  // Create current if it doesn't exist
  if (!internals.ReactCurrentDispatcher.current) {
    console.log('Creating current dispatcher');
    internals.ReactCurrentDispatcher.current = {};
  }
  
  const dispatcher = internals.ReactCurrentDispatcher.current;
  
  // Add all hooks to the dispatcher if they don't exist
  const hooks = [
    'useState',
    'useEffect',
    'useContext',
    'useReducer',
    'useCallback',
    'useMemo',
    'useRef',
    'useLayoutEffect',
    'useImperativeHandle',
    'useDebugValue',
    'useDeferredValue',
    'useTransition',
    'useId',
    'useSyncExternalStore',
    'useInternalStore'
  ];
  
  hooks.forEach(hook => {
    if (!dispatcher[hook]) {
      console.log(`Adding ${hook} to dispatcher`);
      
      // Special case for useInternalStore
      if (hook === 'useInternalStore') {
        dispatcher[hook] = function(subscribe, getSnapshot) {
          return getSnapshot();
        };
      }
      // Special case for useState
      else if (hook === 'useState') {
        dispatcher[hook] = function(initialState) {
          return [
            typeof initialState === 'function' ? initialState() : initialState,
            function() {}
          ];
        };
      }
      // Special case for useId
      else if (hook === 'useId') {
        dispatcher[hook] = function() {
          return 'id-' + Math.random().toString(36).substring(2, 9);
        };
      }
      // Special case for useDeferredValue
      else if (hook === 'useDeferredValue') {
        dispatcher[hook] = function(value) {
          return value;
        };
      }
      // Special case for useTransition
      else if (hook === 'useTransition') {
        dispatcher[hook] = function() {
          return [false, function() {}];
        };
      }
      // Default implementation for other hooks
      else {
        dispatcher[hook] = function() {};
      }
    }
  });
  
  console.log('Direct React fix applied successfully');
})();
