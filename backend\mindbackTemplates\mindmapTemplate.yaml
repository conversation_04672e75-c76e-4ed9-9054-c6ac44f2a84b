# This file describes the mindmap template, its parameters and the expected outcome structure
template_id: mindmap
name: "Mindmap"
purpose: "Break down complex goals or problems into hierarchical structures."
used_for:
  - Teleological (Goal-Oriented Strategy)
input_type: "High-level goal or problem statement"
output_type: "Hierarchical JSON structure"
llm_prompt: |
  Given the following goal statement: "{user_input}",
  generate a hierarchical breakdown of key issues that must be considered.
  Return a structured JSON mindmap with 3-5 main branches and each branch containing 2-3 sub-branches.
return_structure:
  format: "json"
  schema:
    mindmap:
      root:
        id: "root"
        text: "{goal_statement}"
        children:
          - id: "node_1"
            text: "Main Branch 1"
            children:
              - id: "node_1_1"
                text: "Sub-branch 1.1"
  validation: "Must be a valid JSON object with hierarchical nesting."
