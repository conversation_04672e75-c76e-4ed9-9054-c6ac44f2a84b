/**
 * MindMapControls Component
 * Provides zoom, center, and layout rotation controls for the mind map
 */

import React, { useState } from 'react';
import { useMindMapStore } from '../../core/state/MindMapStore';
import { GovernanceLLM } from '../../services/api/GovernanceLLM';
import '../../MindMap.css';

// Props for MindMapControls
interface MindMapControlsProps {
  onZoomIn: () => void;
  onZoomOut: () => void;
  onCenter: () => void;
  onRotate: () => void;
}

// MindMapControls Component
const MindMapControls: React.FC<MindMapControlsProps> = ({
  onZoomIn,
  onZoomOut,
  onCenter,
  onRotate,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [showSettings, setShowSettings] = useState(false);
  const {
    setShowProjectDialog, 
    connections,
    updateConnection,
    nodes,
    llmModel,
    setLlmModel
  } = useMindMapStore();

  // Get node and connection counts
  const nodeCount = Object.keys(nodes).length;
  const connectionCount = Array.isArray(connections) ? connections.length : 0;

  // Apply default settings to all connections
  const applyDefaultSettings = () => {
    Object.entries(connections).forEach(([id, connection]) => {
      updateConnection(id, {
        lineStyle: 'angled',
        thickness: 2,
        color: '#9ca3af',
        showArrow: false,
        type: 'solid'
      });
    });
  };

  // Handle LLM model change
  const handleModelChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setLlmModel(e.target.value as 'gpt-3.5-turbo' | 'gpt-4');
  };
  
  // Reset LLM instance for debugging
  const handleResetLLM = () => {
    GovernanceLLM.resetInstance();
    console.log('DEBUG: LLM instance has been reset');
  };

  return (
    <div className="mindmap-controls">
      <div className="mindmap-controls-header">
        <div className="title">MindMap Manager</div>
        <button onClick={() => setIsExpanded(!isExpanded)}>
          {isExpanded ? '▼' : '▲'}
        </button>
      </div>
      
      {isExpanded && (
        <div className="mindmap-controls-content">
          <div className="mindmap-stats-container">
            <div className="mindmap-stat-item">
              <span className="stat-label">Nodes:</span>
              <span className="stat-value">{nodeCount}</span>
            </div>
            <div className="mindmap-stat-item">
              <span className="stat-label">Connections:</span>
              <span className="stat-value">{connectionCount}</span>
            </div>
          </div>
          
          <div className="control-section">
            <button className="control-button" onClick={onZoomIn} title="Zoom In">
              +
            </button>
            <button className="control-button" onClick={onZoomOut} title="Zoom Out">
              -
            </button>
            <button className="control-button" onClick={onCenter} title="Center View">
              ⌖
            </button>
            <button className="control-button" onClick={onRotate} title="Rotate Layout">
              ↻
            </button>
          </div>
          
          <div className="control-section">
            <button 
              className="control-button" 
              onClick={() => setShowSettings(!showSettings)}
              title="Connection Settings"
            >
              ⚙️
            </button>
            <button 
              className="control-button projects-button" 
              onClick={() => setShowProjectDialog(true)}
              title="Open Projects"
            >
              📂
            </button>
          </div>

          {showSettings && (
            <div className="settings-panel">
              <h4>Default Connection Settings</h4>
              <button 
                className="settings-button"
                onClick={applyDefaultSettings}
                title="Apply angled lines without arrows to all connections"
              >
                Apply Default Style
              </button>
              
              <h4>LLM Settings</h4>
              <div className="llm-settings">
                <label htmlFor="llm-model-select">Model:</label>
                <select 
                  id="llm-model-select" 
                  value={llmModel} 
                  onChange={handleModelChange}
                  className="llm-model-select"
                >
                  <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                  <option value="gpt-4">GPT-4</option>
                </select>
              </div>
              <button 
                className="settings-button"
                onClick={handleResetLLM}
                title="Reset LLM Instance (for debugging)"
              >
                Reset LLM Instance
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MindMapControls;
