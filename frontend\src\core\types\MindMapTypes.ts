/**
 * MindMapTypes.ts
 *
 * Core types for the MindMap feature.
 * These types are used throughout the application.
 */

// Node type
export interface Node {
  id: string;
  text: string;
  x: number;
  y: number;
  width: number;
  height: number;
  color: string;
  borderColor: string;
  shape: 'rectangle' | 'ellipse' | 'diamond' | 'hexagon';
  isSelected?: boolean; // Flag to indicate if the node is selected
  description?: string;
  hatContributions?: {
    blue: boolean;
    white: boolean;
    red: boolean;
    black: boolean;
    yellow: boolean;
    green: boolean;
  };
  metadata?: {
    nodePath?: string;
    [key: string]: any;
  };
}

// Connection type
export interface Connection {
  id: string;
  from: string;
  to: string;
  color?: string;
  width?: number;
  style?: 'solid' | 'dashed' | 'dotted';
}

// Direction type
export type Direction = '0' | '90' | '180' | '270';

// LLM model type
export type LLMModel = 'gpt-3.5-turbo' | 'gpt-4' | 'gpt-4-mini' | 'claude-3.5-sonnet' | 'claude-3.7-sonnet';

// Project type
export interface Project {
  name: string;
  savedAt: number;
  nodeCount: number;
  rootNodeId: string;
}

// Default values
export const defaultNodeValues = {
  width: 150,
  height: 60,
  color: '#ffffff',
  borderColor: '#2c3e50',
  shape: 'rectangle' as const,
};
