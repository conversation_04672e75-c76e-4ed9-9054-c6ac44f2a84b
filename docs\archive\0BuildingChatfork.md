# ChatFork Canvas Integration: Implementation Plan

## Overview

This document outlines the plan to integrate ChatFork content directly into the MindBack canvas, replacing the current modal dialog implementation with an embedded text-based view. This approach will allow users to interact with exploratory content directly on the canvas, select text, and create forks based on selections.

## Apperance
- a very slic field where the verbos result out pushing the Explor... button, located top-left, no shaddow
- text:
   -headline Arial 14p, bold
   -text arial 10p, plain

## Target Implementation

The new implementation will render MBCP-formatted ChatFork content directly on the canvas without opening a separate dialog. Users will be able to select text and create forks either through a Tab key shortcut or via a hover menu that appears at the cursor position.

## Architecture Design

To ensure a professional implementation that properly integrates ChatFork functionality with the Canvas, we'll implement a centralized state management approach:

### Centralized State Store

We'll create a dedicated `ChatForkStore` to manage ChatFork state across components:

```typescript
// src/core/state/ChatForkStore.ts
import create from 'zustand';
import { ChatResponse } from '../services/api/GovernanceLLM';

interface ChatForkState {
  content: ChatResponse | null;
  isVisible: boolean;
  selectedText: string | null;
  selectionContext: string | null;
  
  showChatFork: (content: ChatResponse) => void;
  hideChatFork: () => void;
  setSelectedText: (text: string, context: string) => void;
  createForkFromSelection: (text: string, context: string) => void;
}

export const useChatForkStore = create<ChatForkState>((set, get) => ({
  // State and actions implementation
}));
```

### Component Integration

We'll implement an adapter pattern to decouple the ChatFork logic from specific components:

```typescript
// src/core/adapters/ChatForkAdapter.ts
export class ChatForkAdapter {
  static handleChatForkAction(action: any) {
    // Handle actions and update ChatForkStore
  }
  
  static createForkNode(text: string, context: string) {
    // Create nodes in MindMapStore based on selections
  }
}
```

This approach eliminates global window functions and provides a clear, maintainable architecture.

## Key Requirements

- [ ] Render ChatFork MBCP content directly on the canvas
- [ ] Use root topic as the primary header
- [ ] Maintain consistent styling with the rest of the canvas (no special colors)
- [ ] Enable text selection within rendered content
- [ ] Provide hover-based interaction menu for selected text
- [ ] Implement Tab key shortcut for quick forking
- [ ] Support multi-level forks based on text selections

## Implementation Plan

### Phase 1: State Management & Core Architecture

- [ ] Create `ChatForkStore` for centralized state management
- [ ] Implement `ChatForkAdapter` for component integration
- [ ] Update `OptimizedMindMap_Modular` to use the adapter pattern
- [ ] Ensure proper data flow between components

### Phase 2: Canvas Rendering

- [ ] Create new `CanvasTextView` component that uses ChatForkStore
- [ ] Update `MindMapCanvas` to support text view mode
- [ ] Implement basic styling for text content on canvas
- [ ] Ensure proper integration with canvas zoom and pan features

### Phase 3: Text Selection Capabilities

- [ ] Implement text selection tracking in canvas
- [ ] Create visual indication for selected text
- [ ] Store selection data in ChatForkStore
- [ ] Handle click events outside selection to clear it
- [ ] Ensure selection works correctly at different zoom levels

### Phase 4: Hover UI & Interaction

- [ ] Design compact hover menu for selected text
- [ ] Implement menu positioning at cursor tip
- [ ] Add primary actions to menu (Fork, Focus, Explore)
- [ ] Create smooth animations for menu appearance
- [ ] Handle menu item selection and triggering actions

### Phase 5: Keyboard Shortcuts & Advanced Features

- [ ] Implement Tab key for quick forking of selected text
- [ ] Add keyboard navigation within hover menu
- [ ] Create smooth transition animations when forking
- [ ] Implement focus/zoom feature for content sections
- [ ] Add history tracking for fork actions

### Phase 6: Testing & Refinement

- [ ] Test with various MBCP response formats
- [ ] Verify performance with large text selections
- [ ] Ensure accessibility compliance
- [ ] Refine UX based on user feedback
- [ ] Document new features and keyboard shortcuts

## Technical Implementation Details

### New Components

1. **ChatForkStore**
   - [ ] Manages content, visibility, and selection state
   - [ ] Provides actions for showing/hiding content and handling selections
   - [ ] Coordinates between canvas and ChatFork functionality
   - [ ] Maintains single source of truth for ChatFork state

2. **ChatForkAdapter**
   - [ ] Handles communication between different components
   - [ ] Transforms action data into appropriate state updates
   - [ ] Provides methods for fork creation and management
   - [ ] Decouples business logic from UI components

3. **CanvasTextView**
   - [ ] Renders formatted MBCP content
   - [ ] Handles text selection events
   - [ ] Subscribes to ChatForkStore for state
   - [ ] Supports different text hierarchy levels

4. **SelectionHoverMenu**
   - [ ] Appears near cursor when text is selected
   - [ ] Provides contextual actions based on selection
   - [ ] Handles clicks and keyboard navigation
   - [ ] Animates smoothly when appearing/disappearing

5. **TextForkManager**
   - [ ] Creates new nodes based on text selections
   - [ ] Handles the forking logic
   - [ ] Manages relationships between original text and forks
   - [ ] Supports multi-level forking

### Modified Existing Components

1. **MindMapCanvas**
   - [ ] Subscribe to ChatForkStore
   - [ ] Render CanvasTextView when ChatFork is visible
   - [ ] Handle text selection events
   - [ ] Manage canvas state during text interaction

2. **OptimizedMindMap_Modular**
   - [ ] Use ChatForkAdapter instead of direct ChatFork management
   - [ ] Remove global window function references
   - [ ] Manage state transitions via store

## User Experience Workflow

1. User initiates exploratory query
2. LLM responds with MBCP-formatted content
3. ChatForkAdapter processes response and updates ChatForkStore
4. Content appears directly on canvas with header
5. User selects interesting text
6. Selection state is stored in ChatForkStore
7. Hover menu appears at cursor with options
8. User selects an action or presses Tab to fork
9. ChatForkAdapter creates new node and connections
10. New fork appears connected to original content

## Benefits of This Architecture

1. **Decoupling**: Components are loosely coupled through the state store
2. **Testability**: Business logic is isolated in adapters and stores
3. **Maintainability**: Clear separation of concerns between components
4. **Flexibility**: Can switch between dialog and canvas rendering without changing core logic
5. **Type Safety**: TypeScript interfaces ensure proper data handling
6. **No Global Variables**: Eliminates global window functions that are error-prone
7. **Single Source of Truth**: All components share the same state

## Potential Challenges

- Ensuring proper state synchronization across components
- Maintaining proper text layout on canvas at different zoom levels
- Ensuring reliable text selection in a canvas environment
- Creating an intuitive hover menu that doesn't obstruct content
- Managing transitions between different viewing modes

## Next Steps

- [ ] Create detailed component specifications for ChatForkStore and adapter
- [ ] Implement proof-of-concept for state management architecture
- [ ] Design initial UI mockups for hover menu
- [ ] Develop prototype of text selection mechanics
