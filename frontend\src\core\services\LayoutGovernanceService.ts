/**
 * Layout Governance Service
 * 
 * Centralized service for managing layout change requests and enforcing governance rules.
 * This is the single point of validation for all layout changes in the system.
 */

import { 
  LayoutStrategyType, 
  LayoutRequest, 
  LayoutResponse, 
  LayoutEvent,
  LayoutEventType,
  LayoutTypeUtils 
} from '../types/LayoutTypes';
import { mindMapGovernance } from '../governance/MindMapGovernance';

interface LayoutPreference {
  strategy: LayoutStrategyType;
  timestamp: number;
  userSet: boolean;
}

interface LayoutMetrics {
  requestCount: number;
  successCount: number;
  rejectionCount: number;
  lastChangeTime: number;
  averageTimeBetweenChanges: number;
}

export class LayoutGovernanceService {
  private static instance: LayoutGovernanceService;
  
  // Sheet-specific preferences
  private sheetPreferences: Map<string, LayoutPreference> = new Map();
  
  // Rate limiting and metrics
  private lastLayoutTime: Map<string, number> = new Map();
  private layoutMetrics: Map<string, LayoutMetrics> = new Map();
  
  // Event listeners
  private eventListeners: Map<LayoutEventType, Array<(event: LayoutEvent) => void>> = new Map();
  
  // Configuration
  private readonly RATE_LIMIT_MS = 500; // Minimum time between layout changes
  private readonly MAX_AUTO_LAYOUTS_PER_MINUTE = 3;
  private readonly PREFERENCE_PERSISTENCE_KEY = 'mindmap_layout_preferences';

  static getInstance(): LayoutGovernanceService {
    if (!this.instance) {
      this.instance = new LayoutGovernanceService();
    }
    return this.instance;
  }

  private constructor() {
    this.loadPreferencesFromStorage();
    this.initializeEventListeners();
  }

  /**
   * Main validation method - single point of layout change validation
   */
  validateLayoutRequest(request: LayoutRequest): LayoutResponse {
    const timestamp = Date.now();
    
    try {
      // 1. Validate request structure
      const structureValidation = this.validateRequestStructure(request);
      if (!structureValidation.success) {
        return { ...structureValidation, timestamp };
      }

      // 2. Check governance rules
      const governanceValidation = this.validateGovernanceRules(request);
      if (!governanceValidation.success) {
        this.emitEvent('governance_rule_violated', request.sheetId, request.strategy, {
          reason: governanceValidation.reason,
          request
        });
        return { ...governanceValidation, timestamp };
      }

      // 3. Check rate limiting
      const rateLimitValidation = this.validateRateLimit(request);
      if (!rateLimitValidation.success) {
        return { ...rateLimitValidation, timestamp };
      }

      // 4. Check auto-layout frequency
      const autoFrequencyValidation = this.validateAutoLayoutFrequency(request);
      if (!autoFrequencyValidation.success) {
        return { ...autoFrequencyValidation, timestamp };
      }

      // 5. All validations passed
      this.recordLayoutAttempt(request.sheetId, true);
      this.lastLayoutTime.set(request.sheetId, timestamp);
      
      this.emitEvent('layout_request', request.sheetId, request.strategy, {
        requestOrigin: request.requestOrigin,
        reason: request.reason
      });

      return {
        success: true,
        strategy: request.strategy,
        reason: 'All governance validations passed',
        timestamp
      };

    } catch (error) {
      this.recordLayoutAttempt(request.sheetId, false);
      return {
        success: false,
        strategy: request.strategy,
        reason: `Validation error: ${error.message}`,
        timestamp
      };
    }
  }

  /**
   * Store user layout preference for a sheet
   */
  setUserPreference(sheetId: string, strategy: LayoutStrategyType): void {
    const preference: LayoutPreference = {
      strategy,
      timestamp: Date.now(),
      userSet: true
    };

    this.sheetPreferences.set(sheetId, preference);
    this.persistPreferencesToStorage();
    
    this.emitEvent('layout_preference_changed', sheetId, strategy, {
      userSet: true,
      previousStrategy: this.getPreferredStrategy(sheetId)
    });

    console.log(`[LayoutGovernance] User preference set for sheet ${sheetId}: ${strategy}`);
  }

  /**
   * Get preferred layout strategy for a sheet
   */
  getPreferredStrategy(sheetId: string): LayoutStrategyType {
    // 1. Check user preference in memory
    const memoryPref = this.sheetPreferences.get(sheetId);
    if (memoryPref && memoryPref.userSet) {
      return memoryPref.strategy;
    }

    // 2. Check localStorage for persistence
    const storedPrefs = this.getStoredPreferences();
    const storedPref = storedPrefs[sheetId];
    if (storedPref && LayoutTypeUtils.isValidStrategy(storedPref.strategy)) {
      // Update memory cache
      this.sheetPreferences.set(sheetId, storedPref);
      return storedPref.strategy;
    }

    // 3. Get governance default
    try {
      const config = mindMapGovernance.getSheetConfig(sheetId);
      return config.layout.defaultStrategy;
    } catch (error) {
      console.warn(`[LayoutGovernance] Could not get governance config for sheet ${sheetId}, using fallback`);
      return LayoutTypeUtils.getDefaultStrategy();
    }
  }

  /**
   * Record successful layout completion
   */
  recordLayoutCompletion(sheetId: string, strategy: LayoutStrategyType): void {
    // Update metrics
    this.recordLayoutAttempt(sheetId, true);
    
    // Update preference if not user-set
    const currentPref = this.sheetPreferences.get(sheetId);
    if (!currentPref || !currentPref.userSet) {
      this.sheetPreferences.set(sheetId, {
        strategy,
        timestamp: Date.now(),
        userSet: false
      });
    }

    this.emitEvent('layout_complete', sheetId, strategy);
    console.log(`[LayoutGovernance] Layout completion recorded for sheet ${sheetId}: ${strategy}`);
  }

  /**
   * Get layout metrics for a sheet
   */
  getLayoutMetrics(sheetId: string): LayoutMetrics {
    return this.layoutMetrics.get(sheetId) || {
      requestCount: 0,
      successCount: 0,
      rejectionCount: 0,
      lastChangeTime: 0,
      averageTimeBetweenChanges: 0
    };
  }

  /**
   * Add event listener
   */
  addEventListener(eventType: LayoutEventType, listener: (event: LayoutEvent) => void): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, []);
    }
    this.eventListeners.get(eventType)!.push(listener);
  }

  /**
   * Remove event listener
   */
  removeEventListener(eventType: LayoutEventType, listener: (event: LayoutEvent) => void): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * Clear all preferences (for testing/reset)
   */
  clearPreferences(): void {
    this.sheetPreferences.clear();
    this.lastLayoutTime.clear();
    this.layoutMetrics.clear();
    localStorage.removeItem(this.PREFERENCE_PERSISTENCE_KEY);
    console.log('[LayoutGovernance] All preferences cleared');
  }

  // Private validation methods

  private validateRequestStructure(request: LayoutRequest): Partial<LayoutResponse> {
    if (!request.strategy || !LayoutTypeUtils.isValidStrategy(request.strategy)) {
      return {
        success: false,
        strategy: request.strategy,
        reason: `Invalid layout strategy: ${request.strategy}`
      };
    }

    if (!request.sheetId || typeof request.sheetId !== 'string') {
      return {
        success: false,
        strategy: request.strategy,
        reason: 'Invalid or missing sheetId'
      };
    }

    if (!request.requestOrigin || !['user', 'system', 'auto'].includes(request.requestOrigin)) {
      return {
        success: false,
        strategy: request.strategy,
        reason: 'Invalid requestOrigin'
      };
    }

    return { success: true };
  }

  private validateGovernanceRules(request: LayoutRequest): Partial<LayoutResponse> {
    try {
      const config = mindMapGovernance.getSheetConfig(request.sheetId);
      
      // Check if auto-switching is prevented
      if (config.layout.preventAutoSwitching && request.requestOrigin === 'auto') {
        return {
          success: false,
          strategy: request.strategy,
          reason: 'Auto-switching prevented by governance rules',
          governanceBlocked: true
        };
      }

      // Check if user override is allowed
      if (!config.layout.allowUserOverride && request.requestOrigin === 'user') {
        const currentStrategy = this.getPreferredStrategy(request.sheetId);
        if (currentStrategy !== request.strategy) {
          return {
            success: false,
            strategy: request.strategy,
            reason: 'User layout override not allowed by governance',
            governanceBlocked: true
          };
        }
      }

      return { success: true };
    } catch (error) {
      console.warn(`[LayoutGovernance] Could not validate governance rules: ${error.message}`);
      return { success: true }; // Allow if governance check fails
    }
  }

  private validateRateLimit(request: LayoutRequest): Partial<LayoutResponse> {
    const lastTime = this.lastLayoutTime.get(request.sheetId) || 0;
    const timeSinceLastLayout = Date.now() - lastTime;
    
    if (timeSinceLastLayout < this.RATE_LIMIT_MS) {
      return {
        success: false,
        strategy: request.strategy,
        reason: `Rate limited - ${this.RATE_LIMIT_MS - timeSinceLastLayout}ms remaining`
      };
    }

    return { success: true };
  }

  private validateAutoLayoutFrequency(request: LayoutRequest): Partial<LayoutResponse> {
    if (request.requestOrigin !== 'auto') {
      return { success: true };
    }

    // Check auto-layout frequency in the last minute
    const metrics = this.getLayoutMetrics(request.sheetId);
    const oneMinuteAgo = Date.now() - 60000;
    
    if (metrics.lastChangeTime > oneMinuteAgo) {
      // Count recent auto layouts (this is simplified - in production we'd track individual requests)
      if (metrics.requestCount > this.MAX_AUTO_LAYOUTS_PER_MINUTE) {
        return {
          success: false,
          strategy: request.strategy,
          reason: 'Too many automatic layout changes in the last minute'
        };
      }
    }

    return { success: true };
  }

  private recordLayoutAttempt(sheetId: string, success: boolean): void {
    const current = this.layoutMetrics.get(sheetId) || {
      requestCount: 0,
      successCount: 0,
      rejectionCount: 0,
      lastChangeTime: 0,
      averageTimeBetweenChanges: 0
    };

    const now = Date.now();
    current.requestCount++;
    
    if (success) {
      current.successCount++;
      
      // Update timing metrics
      if (current.lastChangeTime > 0) {
        const timeBetween = now - current.lastChangeTime;
        current.averageTimeBetweenChanges = 
          (current.averageTimeBetweenChanges + timeBetween) / 2;
      }
      current.lastChangeTime = now;
    } else {
      current.rejectionCount++;
    }

    this.layoutMetrics.set(sheetId, current);
  }

  private emitEvent(
    type: LayoutEventType, 
    sheetId: string, 
    strategy?: LayoutStrategyType, 
    metadata?: Record<string, any>
  ): void {
    const event: LayoutEvent = {
      type,
      sheetId,
      strategy,
      timestamp: Date.now(),
      metadata
    };

    const listeners = this.eventListeners.get(type) || [];
    listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error(`[LayoutGovernance] Error in event listener for ${type}:`, error);
      }
    });
  }

  private loadPreferencesFromStorage(): void {
    try {
      const stored = localStorage.getItem(this.PREFERENCE_PERSISTENCE_KEY);
      if (stored) {
        const preferences = JSON.parse(stored);
        Object.entries(preferences).forEach(([sheetId, pref]: [string, any]) => {
          if (pref && LayoutTypeUtils.isValidStrategy(pref.strategy)) {
            this.sheetPreferences.set(sheetId, pref);
          }
        });
        console.log(`[LayoutGovernance] Loaded ${this.sheetPreferences.size} preferences from storage`);
      }
    } catch (error) {
      console.warn('[LayoutGovernance] Could not load preferences from storage:', error);
    }
  }

  private persistPreferencesToStorage(): void {
    try {
      const preferences: Record<string, LayoutPreference> = {};
      this.sheetPreferences.forEach((pref, sheetId) => {
        preferences[sheetId] = pref;
      });
      localStorage.setItem(this.PREFERENCE_PERSISTENCE_KEY, JSON.stringify(preferences));
    } catch (error) {
      console.warn('[LayoutGovernance] Could not persist preferences to storage:', error);
    }
  }

  private getStoredPreferences(): Record<string, LayoutPreference> {
    try {
      const stored = localStorage.getItem(this.PREFERENCE_PERSISTENCE_KEY);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.warn('[LayoutGovernance] Could not parse stored preferences:', error);
      return {};
    }
  }

  private initializeEventListeners(): void {
    // Initialize event listener maps
    Object.values(['layout_request', 'layout_complete', 'layout_rejected', 'layout_preference_changed', 'governance_rule_violated'] as LayoutEventType[])
      .forEach(eventType => {
        this.eventListeners.set(eventType, []);
      });
  }
} 