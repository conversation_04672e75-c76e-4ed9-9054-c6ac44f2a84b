# GovernanceBox Recording Filter System

## Overview

The GovernanceBox in MindBack has been enhanced with an intelligent event filtering system that reduces extensive recording to only capture important user actions and system events. This addresses the issue of overwhelming recording data by filtering out unimportant UI movements and interactions.

## What Gets Recorded (Important Events)

The system now only records these **important** events:

### 🎯 Core Functionality
- **Mindsheet Creation** (`MINDSHEET_CREATED`) - When creating new mindsheets
- **Mindmap Selection** (`MINDMAP_SELECTED`) - When selecting mindmap creation
- **Node Creation** (`NODE_CREATED`) - When adding new nodes to mindmaps
- **Node Opening** (`NODE_OPENED`) - When opening nodeboxes (double-click)

### 💬 ChatFork Interactions
- **ChatFork Selection** (`CHATFORK_SELECTED`) - When ChatFork is selected
- **ChatFork Node Creation** (`CHATFORK_NODE_CREATED`) - When creating nodes from ChatFork
- **ChatFork Fork Creation** (`CHATFORK_FORK_CREATED`) - When creating fork chats

### 🎨 Intention & Template Selection
- **Intention Selected** (`INTENTION_SELECTED`) - When selecting work intentions
- **Template Selected** (`TEMPLATE_SELECTED`) - When choosing templates

### 📄 Sheet Management
- **Sheet Activation** (`SHEET_ACTIVATED`) - When switching to a sheet
- **Sheet Switching** (`SHEET_SWITCHED`) - When changing between sheets

### 🤖 LLM Interactions
- **LLM Requests** (`LLM_REQUEST_SENT`) - When sending queries to AI
- **Intent Detection** (`INTENT_DETECTED`) - When AI detects user intent

### 👤 User Actions
- **User Input** (`USER_INPUT`) - When user enters text
- **User Actions** (`USER_ACTION`) - When user performs specific actions

### ⚠️ System Events
- **System Errors** (`SYSTEM_ERROR`) - When errors occur
- **Error Events** (`ERROR_OCCURRED`) - When components encounter errors

## What Gets Filtered Out (Unimportant Events)

These events are **excluded** from recording to reduce noise:

### 🚫 Movement & Positioning
- **Node Moving** (`NODE_MOVED`) - Moving nodes around (not important for workflow)
- **Panel Moving** (`PANEL_MOVED`) - Moving UI panels (govbox repositioning)

### 🚫 Simple UI Interactions
- **Node Selection** (`NODE_SELECTED`) - Simple node clicks (too verbose)
- **Panel Open/Close** (`PANEL_OPENED`, `PANEL_CLOSED`) - UI state changes
- **Panel Collapse/Expand** (`PANEL_COLLAPSED`, `PANEL_EXPANDED`) - UI adjustments

### 🚫 Appearance Changes
- **Theme Changes** (`THEME_CHANGED`) - Visual theme switching
- **Viewport Changes** (`VIEWPORT_CHANGED`) - Window resizing

### 🚫 Automatic Layout Events
- **Layout Applied** (`LAYOUT_APPLIED`) - Automatic layout adjustments
- **Layout Changed** (`LAYOUT_CHANGED`) - Layout algorithm changes
- **View Centering** (`VIEW_CENTERED`) - Automatic view adjustments

### 🚫 Loading States
- **Loading State Changes** (`LOADING_STATE_CHANGED`) - Too verbose for workflow tracking

## Technical Implementation

### Filter Logic
The filtering is implemented in `frontend/src/core/services/RegistrationManager.ts`:

```typescript
// Important events that should be recorded
const IMPORTANT_EVENTS = new Set([
  EventType.MINDSHEET_CREATED,
  EventType.MINDMAP_SELECTED,
  EventType.NODE_CREATED,
  EventType.NODE_OPENED,
  // ... other important events
]);

// Events that should be excluded from recording
const EXCLUDED_EVENTS = new Set([
  EventType.NODE_MOVED,
  EventType.PANEL_MOVED,
  EventType.NODE_SELECTED,
  // ... other unimportant events
]);
```

### Dual Registration Methods

1. **`registerEvent()`** - Filtered registration (most common use)
   ```typescript
   RegistrationManager.registerEvent(EventType.NODE_CREATED, { id: nodeId });
   ```

2. **`registerCriticalEvent()`** - Unfiltered registration (for system-critical events)
   ```typescript
   RegistrationManager.registerCriticalEvent(EventType.SYSTEM_ERROR, { message: error });
   ```

### Utility Methods

```typescript
// Check if an event type is considered important
RegistrationManager.isImportantEvent(EventType.NODE_CREATED); // true
RegistrationManager.isImportantEvent(EventType.NODE_MOVED); // false

// Get lists of event types
RegistrationManager.getImportantEvents(); // Set of important events
RegistrationManager.getExcludedEvents(); // Set of excluded events
```

## Benefits

### ✅ Reduced Noise
- **Before**: Every UI interaction was recorded (node moves, panel adjustments, etc.)
- **After**: Only meaningful workflow actions are captured

### ✅ Focused Recording
- **Before**: "Node moved", "Panel opened", "Layout applied", "Node selected"...
- **After**: "Mindsheet created", "Node opened", "ChatFork selected"...

### ✅ Better Performance
- Fewer messages in govbox chat
- Reduced backend logging
- Cleaner console output

### ✅ Meaningful Audit Trail
- Captures actual user intentions and actions
- Ignores cosmetic UI adjustments
- Maintains workflow continuity

## Usage Examples

### What You'll See Recorded
```
✅ Intention "Teleological" selected
✅ Mindsheet created with ID: mindmap_1647891234567
✅ Node created with ID: node_abc123
✅ Node opened with ID: node_abc123
✅ ChatFork selected
✅ User input: "How can we improve our product strategy..."
```

### What Gets Filtered Out
```
🚫 Node moved with ID: node_abc123
🚫 Panel moved: governance-box
🚫 Layout applied to sheet mindmap_1647891234567: force
🚫 Node selected with ID: node_abc123
🚫 Theme changed to dark
🚫 Loading state changed: Loading
```

## Configuration

To modify which events are recorded, edit the `IMPORTANT_EVENTS` and `EXCLUDED_EVENTS` sets in:
```
frontend/src/core/services/RegistrationManager.ts
```

Add new event types to the appropriate set based on whether they represent meaningful user actions or just UI adjustments.

## Result

Your GovernanceBox recording will now show a clean, focused timeline of important actions:
- ✅ Creating mindsheets and mindmaps
- ✅ Adding and opening nodes
- ✅ Using ChatFork functionality
- ✅ Selecting intentions and templates
- ✅ User inputs and AI interactions

While filtering out unimportant UI movements:
- 🚫 Moving the govbox around the screen
- 🚫 Moving nodes for visual arrangement
- 🚫 Opening/closing panels
- 🚫 Layout adjustments and theme changes

This creates a much more useful and manageable recording that focuses on your actual workflow and decision-making process rather than UI housekeeping. 