import { useState } from 'react';
import { useMindMap } from '../../context/MindMapContext';
import { useNodeManagement } from '../../hooks/useNodeManagement';
import { useProjectManagement } from '../../hooks/useProjectManagement';

export const MindMapToolbar: React.FC = () => {
  const { 
    selectedId, 
    direction,
    setDirection, 
    lastSaved,
    setShowProjectDialog, 
    setShowDesignControls 
  } = useMindMap();
  
  // Get project management functions
  const { saveToLocalStorage: saveProject, loadInitialData } = useProjectManagement();
  const { addChildNode, deleteNode, reorganizeNodes } = useNodeManagement();
  
  // Create a new project function
  const createNewProject = () => {
    if (window.confirm('Create a new project? Any unsaved changes will be lost.')) {
      // Clear the current project and create a new one
      window.location.reload(); // Simple approach - just reload the page
    }
  };
  
  // Temporary placeholder for functions to be implemented
  const changeDirection = () => {
    const directions: Array<'right' | 'down' | 'left' | 'up'> = ['right', 'down', 'left', 'up'];
    const currentIndex = directions.indexOf(direction);
    const nextDirection = directions[(currentIndex + 1) % directions.length];
    setDirection(nextDirection);
  };
  
  return (
    <div className="toolbar">
      <button onClick={createNewProject}>New</button>
      <button onClick={saveProject}>Save</button>
      <button onClick={() => setShowProjectDialog(true)}>Open</button>
      <button 
        onClick={() => {
          console.log("Add Node button clicked, selected node:", selectedId);
          addChildNode();
        }} 
        disabled={!selectedId}
      >
        Add Node
      </button>
      <button onClick={() => selectedId && deleteNode(selectedId)} disabled={!selectedId}>Delete</button>
      <button onClick={reorganizeNodes}>Reorganize</button>
      <button onClick={changeDirection}>
        Direction: {direction === 'right' ? '→' : 
                    direction === 'down' ? '↓' : 
                    direction === 'left' ? '←' : '↑'}
      </button>
      <button onClick={() => setShowDesignControls(true)}>Design</button>
      {lastSaved && <span className="last-saved">Last saved: {new Date(lastSaved).toLocaleTimeString()}</span>}
    </div>
  );
};

export default MindMapToolbar; 