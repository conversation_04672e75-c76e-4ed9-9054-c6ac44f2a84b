// MindMap/hooks/useNodeManagement.ts
import { useCallback } from 'react';
import { useMindMap } from '../context/MindMapContext';
import { Node } from '../types';

export const useNodeManagement = () => {
  const {
    nodes, setNodes,
    connections, setConnections,
    selectedId, setSelectedId,
    nodeColor, direction,
    setNodeText, setShowNodeDialog
  } = useMindMap();

  // Add a child node to the selected node
  const addChildNode = useCallback(() => {
    if (!selectedId) {
      console.log("No node selected for adding child");
      return;
    }
    
    const parentNode = nodes.find(node => node.id === selectedId);
    if (!parentNode) {
      console.log(`Parent node with id ${selectedId} not found`);
      return;
    }
    
    const id = `node-${Date.now()}`;
    const offset = 180;
    
    // Calculate position based on direction
    let x = parentNode.x;
    let y = parentNode.y;
    
    switch (direction) {
      case 'right':
        x = parentNode.x + offset;
        break;
      case 'down':
        y = parentNode.y + offset;
        break;
      case 'left':
        x = parentNode.x - offset;
        break;
      case 'up':
        y = parentNode.y - offset;
        break;
    }
    
    console.log(`Adding node at (${x}, ${y}) with parent ${selectedId}, direction: ${direction}`);
    
    const newNode: Node = {
      id,
      text: 'New Node',
      x,
      y,
      width: 120,
      height: 50,
      color: nodeColor || '#4299E1',
      shape: 'rectangle',
      borderColor: '#2B6CB0'
    };
    
    const newConnection = {
      from: selectedId,
      to: id,
      style: 'straight' as 'straight',
      thickness: 2,
      color: '#64748b'
    };
    
    // Update state with new node and connection
    setNodes(prev => [...prev, newNode]);
    setConnections(prev => [...prev, newConnection]);
    
    // Select the new node
    setSelectedId(id);
    
    // Open dialog to edit the new node
    setNodeText('New Node');
    setShowNodeDialog(true);
    
    return id;
  }, [selectedId, nodes, connections, direction, nodeColor, setNodes, setConnections, setSelectedId, setNodeText, setShowNodeDialog]);

  // Delete a node and its connections
  const deleteNode = useCallback((id: string | null) => {
    if (!id || id === 'root') return; // Prevent deleting the root node or if id is null
    
    setNodes(nodes.filter(node => node.id !== id));
    setConnections(connections.filter(conn => conn.from !== id && conn.to !== id));
    setSelectedId(null);
  }, [nodes, connections, setNodes, setConnections, setSelectedId]);

  // Select a node
  const selectNode = useCallback((id: string) => {
    setSelectedId(id);
  }, [setSelectedId]);

  // Edit a node
  const editNode = useCallback(() => {
    if (!selectedId) return;
    
    const node = nodes.find(n => n.id === selectedId);
    if (!node) return;
    
    setNodeText(node.text);
    setShowNodeDialog(true);
  }, [selectedId, nodes, setNodeText, setShowNodeDialog]);

  // Reorganize nodes based on direction
  const reorganizeNodes = useCallback(() => {
    const rootNode = nodes.find(node => node.id === 'root');
    if (!rootNode) return;
    
    // Create a map of node id to its level (distance from root)
    const levels: Map<string, number> = new Map();
    levels.set('root', 0);
    
    // Map to track children at each level for proper vertical positioning
    const childrenAtLevel: Map<number, string[]> = new Map();
    childrenAtLevel.set(0, ['root']);
    
    // BFS to determine levels and organize children
    const queue: string[] = ['root'];
    while (queue.length > 0) {
      const nodeId = queue.shift()!;
      const level = levels.get(nodeId)!;
      
      // Find all children
      const childConnections = connections.filter(conn => conn.from === nodeId);
      const childIds = childConnections.map(conn => conn.to);
      
      if (childIds.length > 0) {
        if (!childrenAtLevel.has(level + 1)) {
          childrenAtLevel.set(level + 1, []);
        }
        childrenAtLevel.get(level + 1)!.push(...childIds);
      }
      
      for (const childId of childIds) {
        if (!levels.has(childId)) {
          levels.set(childId, level + 1);
          queue.push(childId);
        }
      }
    }
    
    // Define spacing constants
    const horizontalSpacing = 200; // Space between levels
    const verticalSpacing = 100;   // Space between siblings
    
    // Reorganize nodes based on levels and direction
    const updatedNodes = nodes.map(node => {
      if (node.id === 'root') return node; // Keep root node at its position
      
      const level = levels.get(node.id) || 1;
      const siblings = childrenAtLevel.get(level) || [];
      const siblingIndex = siblings.indexOf(node.id);
      
      // Calculate offsets based on direction
      let xOffset = 0;
      let yOffset = 0;
      
      switch (direction) {
        case 'right':
          xOffset = level * horizontalSpacing;
          yOffset = (siblingIndex - (siblings.length - 1) / 2) * verticalSpacing;
          break;
        case 'down':
          xOffset = (siblingIndex - (siblings.length - 1) / 2) * verticalSpacing;
          yOffset = level * horizontalSpacing;
          break;
        case 'left':
          xOffset = -level * horizontalSpacing;
          yOffset = (siblingIndex - (siblings.length - 1) / 2) * verticalSpacing;
          break;
        case 'up':
          xOffset = (siblingIndex - (siblings.length - 1) / 2) * verticalSpacing;
          yOffset = -level * horizontalSpacing;
          break;
      }
      
      return { 
        ...node, 
        x: rootNode.x + xOffset,
        y: rootNode.y + yOffset
      };
    });
    
    setNodes(updatedNodes);
  }, [nodes, connections, direction, setNodes]);

  return {
    addChildNode,
    deleteNode,
    selectNode,
    editNode,
    reorganizeNodes
  };
};