/**
 * ChatHeader.css
 * 
 * Styles for the ChatHeader component.
 */

.governance-chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #2c3e50;
  color: #ffffff;
  cursor: move;
  user-select: none;
}

.header-title {
  display: flex;
  flex-direction: column;
}

.header-title span {
  font-size: 16px;
  font-weight: 500;
}

.timestamp {
  font-size: 12px;
  opacity: 0.7;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.header-button {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 18px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
}

.header-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}
