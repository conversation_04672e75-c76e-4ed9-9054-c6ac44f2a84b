.mind-sheet {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f8fafc;
  overflow: hidden;
  display: none; /* Hidden by default, shown when active */
  z-index: 1500; /* Higher z-index to ensure visibility */
}

/* Container for the mindmap canvas */
.mindmap-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.mind-sheet.active {
  display: block; /* Show when active */
  z-index: 1500; /* Higher z-index to ensure visibility */
}

/* Ensure the governance box doesn't cover the mindmap */
.governance-chat-dialog-container {
  z-index: 1400 !important; /* Lower z-index than the mindmap but higher than other elements */
}

.loading-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.empty-sheet {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #888;
  font-style: italic;
}
