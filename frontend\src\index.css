/* Import z-index variables */
@import './styles/z-index.css';

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light;
  color: rgba(0, 0, 0, 0.87);
  background-color: #f8f9fa;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  display: flex;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  width: 100%;
  min-height: 100vh;
}

a {
  font-weight: 500;
  color: #4dabf7;
  text-decoration: inherit;
}
a:hover {
  color: #339af0;
}

h1 {
  font-size: 2.2em;
  line-height: 1.1;
  margin: 0;
  padding: 1rem 0;
}

h2 {
  font-size: 1.5em;
  margin-bottom: 1rem;
}