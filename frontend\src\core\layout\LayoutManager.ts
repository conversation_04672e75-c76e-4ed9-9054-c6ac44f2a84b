/**
 * LayoutManager.ts
 *
 * Manages the layout of nodes in the mind map.
 * This is a simplified version that handles basic layout operations.
 */

import { Node, Connection, NodeShape, HatContributions, Position } from '../../components/MindMap/core/models/Node';

export type LayoutStrategyType = 'tree' | 'radial' | 'force' | 'manual' | 'topDown';

export class LayoutManager {
  private nodes: Record<string, Node>;
  private connections: Connection[];
  private rootNodeId: string | null;
  private strategy: LayoutStrategyType;

  constructor(
    nodes: Record<string, Node>,
    connections: Connection[],
    rootNodeId: string | null,
    strategy: LayoutStrategyType = 'tree'
  ) {
    this.nodes = nodes;
    this.connections = connections;
    this.rootNodeId = rootNodeId;
    this.strategy = strategy;
  }

  /**
   * Apply the current layout strategy to the nodes.
   *
   * @returns The updated nodes with new positions
   */
  public applyLayout(): Record<string, Node> {
    if (!this.rootNodeId || Object.keys(this.nodes).length === 0) {
      console.warn('[LayoutManager] Cannot apply layout: No root node or empty nodes');
      return this.nodes;
    }

    switch (this.strategy) {
      case 'tree':
        return this.applyTreeLayout();
      case 'topDown':
        return this.applyTopDownLayout();
      case 'radial':
        return this.applyRadialLayout();
      case 'force':
        return this.applyForceLayout();
      case 'manual':
      default:
        return this.nodes;
    }
  }

  /**
   * Apply a tree layout to the nodes.
   *
   * @returns The updated nodes with new positions
   */
  private applyTreeLayout(): Record<string, Node> {
    if (!this.rootNodeId) return this.nodes;

    const rootNode = this.nodes[this.rootNodeId];
    if (!rootNode) return this.nodes;

    // Create a copy of the nodes to avoid mutating the original
    const updatedNodes = { ...this.nodes };

    // Set the root node position to origin (0,0) in mindmap coordinates
    // The stage will handle viewport positioning
    const rootX = 0;
    const rootY = 0;

    updatedNodes[this.rootNodeId] = {
      ...rootNode,
      x: rootX,
      y: rootY,
      width: rootNode.width || 200,
      height: rootNode.height || 100
    };

    console.log(`LayoutManager: Positioned root node at (${rootX}, ${rootY})`);

    // Build a tree structure
    const childrenMap = this.buildChildrenMap();

    // Position children recursively - use the origin as center
    this.positionChildren(this.rootNodeId, childrenMap, updatedNodes, 0, 0, 1);

    // Apply overlap prevention after initial positioning
    return this.preventNodeOverlap(updatedNodes);
  }

  /**
   * Apply a radial layout to the nodes.
   *
   * @returns The updated nodes with new positions
   */
  private applyRadialLayout(): Record<string, Node> {
    if (!this.rootNodeId) return this.nodes;

    const rootNode = this.nodes[this.rootNodeId];
    if (!rootNode) return this.nodes;

    // Create a copy of the nodes to avoid mutating the original
    const updatedNodes = { ...this.nodes };

    // Set the root node position to origin (0,0) in mindmap coordinates
    const centerX = 0;
    const centerY = 0;

    updatedNodes[this.rootNodeId] = {
      ...rootNode,
      x: centerX,
      y: centerY,
      metadata: {
        ...rootNode.metadata,
        positioned: true
      }
    };

    // Build a tree structure
    const childrenMap = this.buildChildrenMap();

    // Get direct children of the root
    const rootChildren = childrenMap[this.rootNodeId] || [];

    // Calculate appropriate radius based on number of children
    // Use a reasonable fixed radius that works well for mindmaps
    const radius = Math.max(200, 100 + rootChildren.length * 30);

    const angleStep = (2 * Math.PI) / rootChildren.length;

    rootChildren.forEach((childId, index) => {
      const angle = index * angleStep;
      // Calculate position relative to center of mindmap
      const x = centerX + Math.cos(angle) * radius;
      const y = centerY + Math.sin(angle) * radius;

      updatedNodes[childId] = {
        ...updatedNodes[childId],
        x: x,
        y: y,
        metadata: {
          ...updatedNodes[childId].metadata,
          positioned: true
        }
      };

      // Position grandchildren recursively
      this.positionRadialChildren(childId, childrenMap, updatedNodes, x, y, radius * 0.6, angle - Math.PI / 4, angle + Math.PI / 4);
    });

    // Apply overlap prevention after initial positioning
    return this.preventNodeOverlap(updatedNodes);
  }

  /**
   * Apply a top-down (horizontal, left-to-right) layout to the nodes.
   *
   * @returns The updated nodes with new positions
   */
  private applyTopDownLayout(): Record<string, Node> {
    if (!this.rootNodeId) return this.nodes;

    const rootNode = this.nodes[this.rootNodeId];
    if (!rootNode) return this.nodes;

    // Create a copy of the nodes to avoid mutating the original
    const updatedNodes = { ...this.nodes };

    // Position root node at origin (0,0) in mindmap coordinates
    const rootX = 0;
    const rootY = 0;

    // Set the root node position
    updatedNodes[this.rootNodeId] = {
      ...rootNode,
      x: rootX,
      y: rootY,
      metadata: {
        ...rootNode.metadata,
        nodePath: '1.0', // Root node has path 1.0
        positioned: true
      }
    };

    // Clean up the root node text by removing any existing numbering
    let rootText = rootNode.text;
    // Remove existing numbering patterns
    rootText = rootText.replace(/^\d+(\.\d+)*\.?\s+/, '');
    // Set the root node text without adding any prefix
    updatedNodes[this.rootNodeId].text = rootText;

    console.log(`LayoutManager: Positioned root node at fixed position (${rootX}, ${rootY})`);

    // Build a tree structure
    const childrenMap = this.buildChildrenMap();

    // Position children recursively using horizontal layout
    this.positionChildrenHorizontally(this.rootNodeId, childrenMap, updatedNodes, rootX, rootY, 1);

    // Apply overlap prevention after initial positioning
    this.preventNodeOverlap(updatedNodes);

    return updatedNodes;
  }

  /**
   * Position children nodes recursively for horizontal (left-to-right) layout.
   * Uses a tree layout algorithm that properly positions nodes to avoid crossing lines.
   *
   * @param nodeId The ID of the current node
   * @param childrenMap A map of parent node IDs to arrays of child node IDs
   * @param updatedNodes The nodes being updated
   * @param x The x position of the parent node
   * @param y The y position of the parent node
   * @param level The current level in the tree
   */
  private positionChildrenHorizontally(
    nodeId: string,
    childrenMap: Record<string, string[]>,
    updatedNodes: Record<string, Node>,
    x: number,
    y: number,
    level: number
  ): void {
    const children = childrenMap[nodeId] || [];
    if (children.length === 0) return;

    // Use consistent spacing for horizontal layout
    const levelSpacing = 300; // Horizontal spacing between levels
    const nodeSpacing = 120; // Vertical spacing between nodes

    // Calculate fixed horizontal position for all nodes at this level
    const childX = x + levelSpacing;

    // First pass: Calculate the total height needed for the subtree of each child
    const subtreeHeights: Record<string, number> = {};
    const nodeHeights: Record<string, number> = {};

    // Calculate the height of each node (including its subtree)
    const calculateSubtreeHeight = (id: string, level: number): number => {
      if (subtreeHeights[id] !== undefined) {
        return subtreeHeights[id];
      }

      const node = updatedNodes[id];
      if (!node) return 0;

      // Base height for the node itself
      const nodeHeight = node.height || 70;
      nodeHeights[id] = nodeHeight;

      // Get children of this node
      const nodeChildren = childrenMap[id] || [];
      if (nodeChildren.length === 0) {
        // Leaf node - just return its own height plus padding
        subtreeHeights[id] = nodeHeight + 20;
        return subtreeHeights[id];
      }

      // Calculate the sum of heights of all children's subtrees
      let totalChildrenHeight = 0;
      nodeChildren.forEach(childId => {
        totalChildrenHeight += calculateSubtreeHeight(childId, level + 1);
      });

      // Add vertical spacing between children
      const verticalSpacing = nodeSpacing;
      totalChildrenHeight += (nodeChildren.length - 1) * verticalSpacing;

      // The subtree height is the max of the node's height and the total children height
      subtreeHeights[id] = Math.max(nodeHeight, totalChildrenHeight);
      return subtreeHeights[id];
    };

    // Calculate subtree heights for all children
    children.forEach(childId => {
      calculateSubtreeHeight(childId, level + 1);
    });

    // Second pass: Assign proper node paths
    children.forEach((childId, index) => {
      const childNode = updatedNodes[childId];
      if (!childNode) return;

      // Get the parent node path from metadata
      const parentPath = updatedNodes[nodeId]?.metadata?.nodePath || '1';

      // Create the child node path (e.g., 1.1, 1.2, etc.)
      let nodePath;
      if (parentPath === '1.0') {
        // Root node children get proper hierarchical paths: 1.1, 1.2, 1.3
        nodePath = `1.${index + 1}`;
      } else if (parentPath.endsWith('.0')) {
        // Handle special case
        nodePath = `${parentPath.slice(0, -2)}.${index + 1}`;
      } else {
        // Normal case: append index to parent path
        nodePath = `${parentPath}.${index + 1}`;
      }

      // Update the node metadata with the path
      updatedNodes[childId] = {
        ...childNode,
        metadata: {
          ...childNode.metadata,
          nodePath: nodePath
        }
      };
    });

    // Third pass: Position nodes and update text with proper indexes

    // Calculate total height of all children including spacing
    const totalChildrenHeight = children.reduce((sum, childId) => sum + (subtreeHeights[childId] || 0), 0) +
                               (children.length - 1) * nodeSpacing;

    // Center the children vertically around the parent
    let startY = y - (totalChildrenHeight / 2);

    children.forEach((childId, index) => {
      const childNode = updatedNodes[childId];
      if (!childNode) return;

      const subtreeHeight = subtreeHeights[childId] || childNode.height || 70;
      const nodeHeight = nodeHeights[childId] || childNode.height || 70;

      // Center the node within its subtree space
      const childY = startY + (subtreeHeight / 2);
      const nodePath = childNode.metadata?.nodePath || '';

      // Clean up node text - remove any existing numbering prefix
      let nodeText = childNode.text;
      nodeText = nodeText.replace(/^\d+(\.\d+)*\.?\s+/, '');

      // Update the node with position and text
      updatedNodes[childId] = {
        ...childNode,
        x: childX,
        y: childY,
        text: nodeText,
        metadata: {
          ...childNode.metadata,
          positioned: true
        }
      };

      // Position grandchildren recursively
      this.positionChildrenHorizontally(childId, childrenMap, updatedNodes, childX, childY, level + 1);

      // Move current Y position for the next sibling
      startY += subtreeHeight + nodeSpacing;
    });

    console.log(`LayoutManager: Positioned ${children.length} children at level ${level}`);
  }

  /**
   * Apply a force-directed layout to the nodes.
   *
   * @returns The updated nodes with new positions
   */
  private applyForceLayout(): Record<string, Node> {
    // This is a simplified force layout that just spreads nodes out
    // A real force layout would use physics simulation

    // Create a copy of the nodes to avoid mutating the original
    const updatedNodes = { ...this.nodes };

    // Set initial positions in a grid
    const nodeIds = Object.keys(this.nodes);
    const gridSize = Math.ceil(Math.sqrt(nodeIds.length));
    const spacing = 200;

    nodeIds.forEach((nodeId, index) => {
      const row = Math.floor(index / gridSize);
      const col = index % gridSize;

      updatedNodes[nodeId] = {
        ...updatedNodes[nodeId],
        x: col * spacing - (gridSize * spacing) / 2,
        y: row * spacing - (gridSize * spacing) / 2
      };
    });

    // Apply overlap prevention after initial positioning
    return this.preventNodeOverlap(updatedNodes);
  }

  /**
   * Build a map of parent nodes to their children.
   *
   * @returns A map of parent node IDs to arrays of child node IDs
   */
  private buildChildrenMap(): Record<string, string[]> {
    const childrenMap: Record<string, string[]> = {};

    this.connections.forEach(connection => {
      const { from, to } = connection;

      if (!childrenMap[from]) {
        childrenMap[from] = [];
      }

      childrenMap[from].push(to);
    });

    return childrenMap;
  }

  /**
   * Prevent node overlap by adjusting positions.
   * Uses a force-directed approach to push overlapping nodes apart.
   *
   * @param nodes The nodes to adjust
   * @returns The updated nodes with adjusted positions
   */
  private preventNodeOverlap(nodes: Record<string, Node>): Record<string, Node> {
    const nodeList = Object.values(nodes);
    const nodeCount = nodeList.length;

    // Skip if there are too few nodes
    if (nodeCount <= 1) return nodes;

    // Define minimum distance between nodes (node width + spacing)
    const minDistanceX = 250; // Horizontal minimum distance
    const minDistanceY = 150; // Vertical minimum distance

    // Number of iterations to run the algorithm
    const iterations = 10;

    // Run multiple iterations of overlap prevention
    for (let iter = 0; iter < iterations; iter++) {
      let overlapsFixed = 0;

      // Check each pair of nodes for overlap
      for (let i = 0; i < nodeCount; i++) {
        const nodeA = nodeList[i];

        // Skip the root node - don't move it
        if (nodeA.id === this.rootNodeId) continue;

        for (let j = i + 1; j < nodeCount; j++) {
          const nodeB = nodeList[j];

          // Skip the root node - don't move it
          if (nodeB.id === this.rootNodeId) continue;

          // Calculate distance between nodes
          const dx = Math.abs(nodeA.x - nodeB.x);
          const dy = Math.abs(nodeA.y - nodeB.y);

          // Check if nodes are too close
          if (dx < minDistanceX && dy < minDistanceY) {
            // Nodes are overlapping or too close
            overlapsFixed++;

            // Calculate repulsion vector
            let moveX = 0;
            let moveY = 0;

            // Determine which axis has more overlap
            if (dx / minDistanceX < dy / minDistanceY) {
              // More horizontal overlap - push apart horizontally
              moveX = (minDistanceX - dx) * (nodeA.x < nodeB.x ? -1 : 1);
            } else {
              // More vertical overlap - push apart vertically
              moveY = (minDistanceY - dy) * (nodeA.y < nodeB.y ? -1 : 1);
            }

            // Apply movement to both nodes in opposite directions
            // Move each node half the distance
            const moveAmount = 0.5;

            // Update nodeA position (no window constraints)
            nodes[nodeA.id] = {
              ...nodeA,
              x: nodeA.x - moveX * moveAmount,
              y: nodeA.y - moveY * moveAmount
            };

            // Update nodeB position (no window constraints)
            nodes[nodeB.id] = {
              ...nodeB,
              x: nodeB.x + moveX * moveAmount,
              y: nodeB.y + moveY * moveAmount
            };

            // Update the nodeList references
            nodeList[i] = nodes[nodeA.id];
            nodeList[j] = nodes[nodeB.id];
          }
        }
      }

      // If no overlaps were fixed in this iteration, we can stop
      if (overlapsFixed === 0) break;

      console.log(`LayoutManager: Fixed ${overlapsFixed} overlaps in iteration ${iter + 1}`);
    }

    return nodes;
  }

  /**
   * Position children nodes recursively for tree layout.
   *
   * @param nodeId The ID of the current node
   * @param childrenMap A map of parent node IDs to arrays of child node IDs
   * @param updatedNodes The nodes being updated
   * @param x The x position of the parent node
   * @param y The y position of the parent node
   * @param level The current level in the tree
   */
  private positionChildren(
    nodeId: string,
    childrenMap: Record<string, string[]>,
    updatedNodes: Record<string, Node>,
    x: number,
    y: number,
    level: number
  ): void {
    const children = childrenMap[nodeId] || [];
    if (children.length === 0) return;

    // Use consistent spacing for tree layout
    const levelSpacing = 200; // Vertical spacing between levels
    const siblingSpacing = 250; // Horizontal spacing between siblings

    // Calculate total width needed for all children
    const totalWidth = (children.length - 1) * siblingSpacing;

    // Center the children horizontally around the parent
    const startX = x - totalWidth / 2;

    children.forEach((childId, index) => {
      // Calculate position in mindmap coordinates
      const childX = startX + index * siblingSpacing;
      const childY = y + levelSpacing;

      updatedNodes[childId] = {
        ...updatedNodes[childId],
        x: childX,
        y: childY,
        metadata: {
          ...updatedNodes[childId].metadata,
          positioned: true
        }
      };

      console.log(`LayoutManager: Positioned node ${childId} at (${childX}, ${childY})`);

      // Position grandchildren recursively
      this.positionChildren(childId, childrenMap, updatedNodes, childX, childY, level + 1);
    });
  }

  /**
   * Position children nodes recursively for radial layout.
   *
   * @param nodeId The ID of the current node
   * @param childrenMap A map of parent node IDs to arrays of child node IDs
   * @param updatedNodes The nodes being updated
   * @param x The x position of the parent node
   * @param y The y position of the parent node
   * @param radius The radius for this level
   * @param startAngle The starting angle for children
   * @param endAngle The ending angle for children
   */
  private positionRadialChildren(
    nodeId: string,
    childrenMap: Record<string, string[]>,
    updatedNodes: Record<string, Node>,
    x: number,
    y: number,
    radius: number,
    startAngle: number,
    endAngle: number
  ): void {
    const children = childrenMap[nodeId] || [];
    if (children.length === 0) return;

    // Adjust radius based on number of children to prevent overlapping
    const adjustedRadius = Math.max(radius, 80 + children.length * 20);

    const angleStep = (endAngle - startAngle) / children.length;

    children.forEach((childId, index) => {
      const angle = startAngle + index * angleStep;
      const childX = x + Math.cos(angle) * adjustedRadius;
      const childY = y + Math.sin(angle) * adjustedRadius;

      updatedNodes[childId] = {
        ...updatedNodes[childId],
        x: childX,
        y: childY,
        metadata: {
          ...updatedNodes[childId].metadata,
          positioned: true
        }
      };

      // Position grandchildren recursively with a smaller radius
      // Reduce the angle range for deeper levels to prevent overlapping
      const childAngleRange = angleStep * 0.8;
      this.positionRadialChildren(
        childId,
        childrenMap,
        updatedNodes,
        childX,
        childY,
        adjustedRadius * 0.8,
        angle - childAngleRange / 2,
        angle + childAngleRange / 2
      );
    });
  }

  /**
   * Set the layout strategy.
   *
   * @param strategy The layout strategy to use
   */
  public setStrategy(strategy: LayoutStrategyType): void {
    this.strategy = strategy;
  }

  /**
   * Update the nodes and connections.
   *
   * @param nodes The new nodes
   * @param connections The new connections
   * @param rootNodeId The new root node ID
   */
  public updateData(
    nodes: Record<string, Node>,
    connections: Connection[],
    rootNodeId: string | null
  ): void {
    this.nodes = nodes;
    this.connections = connections;
    this.rootNodeId = rootNodeId;
  }
}

export default LayoutManager;
