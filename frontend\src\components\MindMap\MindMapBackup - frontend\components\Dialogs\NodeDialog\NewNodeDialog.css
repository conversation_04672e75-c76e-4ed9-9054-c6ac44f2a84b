/* New Node Dialog CSS - Completely unique class names */
@import '../../../../../styles/theme.css';

/* Dialog root */
.node-editor-dialog-root {
  position: absolute;
  z-index: 2000;
  pointer-events: none;
}

/* Dialog paper */
.node-editor-paper {
  position: absolute !important;
  transform-origin: center !important;
  z-index: 2000 !important;
  will-change: transform !important;
  transition: none !important;
  pointer-events: auto !important;
  margin: 0 !important;
  max-width: none !important;
  max-height: none !important;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.25) !important;
}

/* Main dialog container */
.node-editor-dialog {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
  background-color: white;
  border-radius: 4px;
  position: relative;
}

/* Dialog header */
.node-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #000;
  color: white;
  cursor: move;
  user-select: none;
  will-change: transform;
  transition: none;
  height: 40px;
  touch-action: none;
  border-bottom: 1px solid #333;
  position: relative;
}

/* Visual indicator for draggable area */
.node-editor-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: #666;
  cursor: move;
}

.node-editor-header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: move;
  flex: 1;
}

.node-editor-header-logo {
  width: 24px;
  height: 24px;
  border-radius: 4px;
}

.node-editor-header-path {
  font-family: Arial, sans-serif;
  font-size: 18px;
  font-weight: bold;
  color: #00cc99;
  margin-right: 12px;
  padding: 2px 6px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  letter-spacing: 0.5px;
}

.node-editor-header-text {
  font-family: Arial, sans-serif;
  font-size: 16px;
  font-weight: bold;
  color: white;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 500px;
}

.node-editor-header-buttons {
  display: flex;
  gap: 4px;
  cursor: default;
  align-items: center;
}

.node-editor-node-id {
  font-family: monospace;
  font-size: 12px;
  color: #aaa;
  margin-right: 8px;
  opacity: 0.8;
  background-color: rgba(0, 0, 0, 0.2);
  padding: 2px 6px;
  border-radius: 4px;
}

.node-editor-header-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px 8px;
  font-size: 16px;
  border-radius: 4px;
  z-index: 10;
}

.node-editor-header-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.node-editor-close-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px 8px;
  font-size: 20px;
  border-radius: 4px;
  z-index: 10;
}

.node-editor-close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Debug indicator */
.node-editor-debug-indicator {
  position: absolute;
  top: 5px;
  right: 50px;
  background-color: #666;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  z-index: 2050;
}

/* Content area */
.node-editor-content {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
  background-color: white;
}

/* Node info section */
.node-editor-info-section {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Title container with inline label */
.node-editor-title-container {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.node-editor-inline-label {
  font-weight: bold;
  font-size: 16px;
  font-family: Arial, sans-serif;
  color: #333;
  margin-right: 10px;
  white-space: nowrap;
  min-width: 85px;
}

.node-editor-input-label {
  display: block;
  margin-bottom: 10px;
  font-weight: bold;
  font-size: 16px;
  font-family: Arial, sans-serif;
  color: #333;
}

.node-editor-title-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #999;
  border-radius: 4px;
  font-size: 16px;
  font-family: Arial, sans-serif;
  box-sizing: border-box;
  outline: none;
  background-color: white;
}

.node-editor-description-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #999;
  border-radius: 4px;
  font-size: 16px;
  font-family: Arial, sans-serif;
  min-height: 120px;
  resize: vertical;
  box-sizing: border-box;
  outline: none;
  background-color: white;
}

/* Color Agent Tabs */
.node-editor-agent-tabs {
  margin-top: 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.color-agent-tabs {
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.color-tab {
  text-transform: none !important;
  font-weight: bold !important;
  min-width: 0 !important;
  padding: 8px 16px !important;
  font-size: 14px !important;
}

.blue-hat {
  color: #1565c0 !important;
}

.white-hat {
  color: #757575 !important;
}

.red-hat {
  color: #d32f2f !important;
}

.black-hat {
  color: #212121 !important;
}

.yellow-hat {
  color: #ffa000 !important;
}

.green-hat {
  color: #2e7d32 !important;
}

.tab-content-container {
  padding: 16px;
  min-height: 150px;
  background-color: white;
}

.agent-tab-content {
  height: 100%;
  width: 100%;
}

/* Resize handle */
.node-editor-resize-handle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 20px;
  height: 20px;
  cursor: nwse-resize !important;
  background: linear-gradient(135deg, transparent 50%, rgba(0, 0, 0, 0.3) 50%);
  z-index: 10;
}

.node-editor-resize-handle:hover {
  background: linear-gradient(135deg, transparent 50%, rgba(0, 0, 0, 0.5) 50%);
}

/* Support for dragging - unique class name */
.node-dragging-active {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

.node-dragging-active * {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

.node-dragging-active .node-editor-header-buttons {
  cursor: default !important;
}

.node-dragging-active .node-editor-close-button {
  cursor: pointer !important;
} 