# Fixing Frontend React Hooks Violations in MindMap Workflow

**Date: May 12, 2025**

## Executive Summary

This document outlines a comprehensive plan to address persistent React Hooks violations in the MindMap workflow of the MindBack application. These violations are causing instability, errors in the console, and potential rendering issues. The plan involves a complete architectural refactoring of the state management and component lifecycle to ensure compliance with React's Rules of Hooks.

## Current Issues

### Symptoms
- React Hooks violations in the console: "Do not call Hooks inside useEffect(...), useMemo(...), or other built-in Hooks"
- Changes in hook order between renders: "<PERSON><PERSON> has detected a change in the order of Hooks called by MindSheet"
- Component unmounting and remounting unnecessarily
- Duplicate initialization of mindmaps

### Root Causes
1. **Hidden Hook Calls**: Store methods indirectly call hooks through Zustand
2. **Component Remounting**: MindSheet components are unmounted and remounted when switching tabs
3. **Inconsistent Hook Order**: The order of hooks changes between renders
4. **Architectural Conflicts**: Competing implementations (MindBook/MindSheet vs. canvas-based approach)
5. **Mixed State Management**: Global state and sheet-specific state are not properly separated
6. **Dependency Conflicts**: React-Konva version compatibility issues with React 18

## Comprehensive Fix Plan

### Phase 1: Architectural Assessment and Planning

#### 1.1 Identify Architectural Conflicts
- Document the competing implementations (MindBook/MindSheet vs. canvas-based approach)
- Map out the current component hierarchy and state management flow
- Identify all places where components are being unmounted and remounted unnecessarily

#### 1.2 State Management Audit
- Document all stores and their relationships (MindMapStore, MindBookStore, MindMapStoreFactory)
- Identify where global state and sheet-specific state are being mixed
- Map out the initialization flow for both manual and automatic workflows

#### 1.3 Define Target Architecture
- Fully adopt MindBook/MindSheet architecture
- Design a clear state management strategy with proper separation of concerns
- Define a consistent component lifecycle management approach

### Phase 2: State Management Refactoring

#### 2.1 Consolidate Store Implementation ✅
```typescript
// Create a new consolidated store factory
export const createMindMapStoreFactory = () => {
  // Registry to track all sheet-specific stores
  const storeRegistry = new Map<string, ReturnType<typeof createSheetStore>>();

  // Create a sheet-specific store
  const createSheetStore = (sheetId: string) => create<SheetState>((set, get) => ({
    // Sheet-specific state and actions
    // ...
  }));

  // Factory methods
  return {
    getStore: (sheetId: string) => {
      if (!storeRegistry.has(sheetId)) {
        storeRegistry.set(sheetId, createSheetStore(sheetId));
      }
      return storeRegistry.get(sheetId)!;
    },
    // ...
  };
};

// Create a single instance of the factory
export const mindMapStoreFactory = createMindMapStoreFactory();
```

#### 2.2 Implement Pure Store Methods ✅
- Refactored `addNode` method to remove DOM event dispatching
- Refactored `selectNode` method to remove DOM event dispatching
- Refactored `updateLayout` method to use a pure inner function
- Refactored `initialize` method to avoid calling other store methods
- Moved event dispatching to the component level (MindMapCanvasWrapper)
- Added store subscriptions to detect state changes and dispatch events

#### 2.3 Create Clear Separation Between Global and Sheet-Specific State ✅
```typescript
// Global state store
export const useGlobalStore = create<GlobalState>((set, get) => ({
  // Global state properties
  activeSheetId: null,
  sheets: [],

  // Global actions
  setActiveSheet: (sheetId) => {
    set({ activeSheetId: sheetId });
  },
  // ...
}));
```

### Phase 3: Component Refactoring

#### 3.1 Refactor MindSheet Component
```typescript
const MindSheet: React.FC<MindSheetProps> = (props) => {
  // Extract props to avoid prop access in hooks
  const { id, contentType, isActive, content } = props;

  // 1. All useState hooks
  const [initialized, setInitialized] = useState(false);

  // 2. All useRef hooks
  const contentRef = useRef(content);

  // 3. All custom hooks - called unconditionally
  const sheetStore = useMemo(() => mindMapStoreFactory.getStore(id), [id]);

  // 4. Effect to update refs when props change
  useEffect(() => {
    contentRef.current = content;
  }, [content]);

  // 5. Effect for initialization - runs once
  useEffect(() => {
    // Initialize with pure functions only
    if (contentType === MindSheetContentType.MINDMAP) {
      initializeSheet(sheetStore, contentRef.current, id);
    }

    return () => {
      // Cleanup logic
    };
  }, [id, contentType, sheetStore]);

  // 6. Effect for activation/deactivation
  useEffect(() => {
    // Activation/deactivation logic
  }, [isActive, id, contentType, sheetStore]);

  // 7. Render logic
  return (
    <div className={`mind-sheet ${isActive ? 'active' : ''}`}>
      {/* Render content based on contentType */}
    </div>
  );
};
```

#### 3.2 Implement Pure Helper Functions
```typescript
// Pure initialization function
const initializeSheet = (
  store: ReturnType<typeof mindMapStoreFactory.getStore>,
  content: any,
  sheetId: string
): void => {
  // Implementation without hook calls
};

// Pure function to process child nodes
const processChildNodes = (
  storeState: any,
  parentId: string,
  children: any[]
): void => {
  // Implementation without hook calls
};
```

#### 3.3 Fix Component Lifecycle Management
```typescript
// In MindBook component
const MindBook: React.FC = () => {
  const { sheets, activeSheetId } = useMindBookStore();

  return (
    <div className="mind-book">
      {sheets.map(sheet => (
        <MindSheet
          key={sheet.id} // Stable key to maintain component instance
          id={sheet.id}
          title={sheet.title}
          contentType={sheet.contentType}
          isActive={sheet.id === activeSheetId}
          content={sheet.content}
          onActivate={() => useMindBookStore.getState().setActiveSheet(sheet.id)}
        />
      ))}
    </div>
  );
};
```

### Phase 4: Workflow Refactoring

#### 4.1 Refactor Manual Workflow (Build Mind Map Button)
```typescript
// In useChat.ts
const buildMindMap = () => {
  // Create a new mindsheet with the MBCP data
  const sheetId = mindBookStore.getState().createMindMapSheet(
    mbcpData.text || 'New Mindmap',
    mbcpData
  );

  // Set this sheet as active
  mindBookStore.getState().setActiveSheet(sheetId);
};
```

#### 4.2 Refactor Automatic Workflow (Teleological Intent)
```typescript
// In GovernanceBoxPositioned.tsx
const handleIntentionSelected = (intention: string) => {
  if (intention === 'teleological') {
    // Create a new mindsheet with default MBCP data
    const sheetId = mindBookStore.getState().createMindMapSheet(
      'Teleological Mindmap',
      defaultMbcpData
    );

    // Set this sheet as active
    mindBookStore.getState().setActiveSheet(sheetId);
  }
};
```

### Phase 5: Testing and Validation

#### 5.1 Create Test Cases
- Test manual workflow (Build Mind Map button)
- Test automatic workflow (teleological intent)
- Test switching between multiple mindsheets
- Test component lifecycle (mount/unmount)

#### 5.2 Implement Error Boundaries
```typescript
// In MindBook component
const MindBook: React.FC = () => {
  return (
    <ErrorBoundary
      fallback={<div>Something went wrong with the mindbook</div>}
      onError={(error) => {
        console.error('MindBook error:', error);
      }}
    >
      {/* MindBook content */}
    </ErrorBoundary>
  );
};
```

#### 5.3 Add Logging for Debugging
```typescript
// Add comprehensive logging
const logComponentLifecycle = (component: string, action: string, id: string) => {
  console.log(`[${component}] ${action} - ID: ${id}, Time: ${new Date().toISOString()}`);
};
```

### Phase 6: Implementation Strategy

#### 6.1 Incremental Implementation
1. Start with state management refactoring
2. Then refactor the MindSheet component
3. Then refactor the workflows
4. Finally, implement testing and validation

#### 6.2 Feature Flags
- Implement feature flags to enable/disable the new implementation
- Allow for easy rollback if issues are encountered

#### 6.3 Documentation
- Document the new architecture
- Document the state management strategy
- Document the component lifecycle management approach

### Phase 7: Monitoring and Maintenance

#### 7.1 Performance Monitoring
- Monitor component render times
- Monitor state update frequency
- Monitor memory usage

#### 7.2 Error Monitoring
- Implement error tracking
- Set up alerts for React Hooks violations
- Monitor console errors

#### 7.3 Ongoing Maintenance
- Regular code reviews
- Regular dependency updates
- Regular performance audits

## Timeline Estimate

- **Phase 1**: 1-2 days
- **Phase 2**: 2-3 days
- **Phase 3**: 3-4 days
- **Phase 4**: 1-2 days
- **Phase 5**: 2-3 days
- **Phase 6**: 1-2 days
- **Phase 7**: Ongoing

**Total**: 10-16 days for full implementation

## Conclusion

This comprehensive plan addresses the root causes of the React Hooks violations in the MindMap workflow. By implementing these changes, we will not only fix the immediate issues but also establish a more maintainable and robust architecture for future development. The plan focuses on proper separation of concerns, consistent component lifecycle management, and pure function implementation to ensure compliance with React's Rules of Hooks.
