# MindBack_V1 - Enhanced Frontend Architecture

```mermaid
graph TD
    App[MindBack_V1 App] --> Router[Routing System]
    Router --> ReactApp[React Application]
    ReactApp --> ComponentSystem[Component System]
    ReactApp --> StateManagement[State Management]
    ReactApp --> HookSystem[React Hooks]

    subgraph "Real React Components (3 found)"
        COMP0[Pg]
        COMP1[F]
        COMP2[Pe]
    end
    ComponentSystem --> COMP0

    subgraph "File Structure (340 total files)"
        TSXFiles[TSX Files: 87]
        TSFiles[TS Files: 139]
        JSFiles[JS Files: 114]
        ComponentFiles[Component Files: 3]
    end
    
    ComponentSystem --> StateManagement
    HookSystem --> StateManagement
    StateManagement --> APIServices[API Services]
    APIServices --> BackendAPI[Backend Communication]

    
    %% Enhanced Project Statistics:
    %% Total Frontend Files: 340
    %% TSX Components: 87
    %% TypeScript Files: 139
    %% JavaScript Files: 114
    %% React Components Found: 3
    %% API Calls: 0
    %% Import Relationships: 0
    
    classDef app fill:#42a5f5,stroke:#1976d2,color:white;
    classDef component fill:#7e57c2,stroke:#4527a0,color:white;
    classDef service fill:#26a69a,stroke:#00796b,color:white;
    classDef realdata fill:#ff9800,stroke:#e65100,color:white;
    classDef fileinfo fill:#607d8b,stroke:#263238,color:white;
    classDef api fill:#e91e63,stroke:#ad1457,color:white;
    
    class App,Router app;
    class ReactApp,ComponentSystem,HookSystem component;
    class RC_Pg,RC_F1,RC_Pe2 realdata;
    class StateManagement,APIServices,BackendAPI service;
    class TSXFiles,TSFiles,JSFiles,ComponentFiles fileinfo;
```