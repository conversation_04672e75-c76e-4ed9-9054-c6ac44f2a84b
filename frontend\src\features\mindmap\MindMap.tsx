/**
 * MindMap.tsx
 *
 * Main MindMap component that serves as the entry point for the MindMap feature.
 * This component orchestrates the different parts of the MindMap UI.
 */

import React, { useEffect, useState, useRef } from 'react';
import { useMindMapStore } from '../../core/state/MindMapStore';
import MindMapCanvas from './components/Canvas/MindMapCanvas';
import MindMapToolbar from './components/Toolbar/MindMapToolbar';
import ProjectDialog from './components/Dialogs/ProjectDialog';
import RegistrationManager, { EventType } from '../../core/services/RegistrationManager';
import './MindMap.css';

interface MindMapProps {
  width?: number;
  height?: number;
  autosaveInterval?: number;
}

const MindMap: React.FC<MindMapProps> = ({
  width,
  height,
  autosaveInterval = 30000 // Default to 30 seconds
}) => {
  // Get dimensions from props or use container dimensions
  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({ width: width || 0, height: height || 0 });

  // Get state and actions from store
  const {
    initialize,
    saveProject,
    setShowProjectDialog,
    showProjectDialog
  } = useMindMapStore();

  // Initialize the store when the component mounts
  useEffect(() => {
    // Get container dimensions if not provided via props
    if (!width || !height) {
      const updateDimensions = () => {
        if (containerRef.current) {
          const { clientWidth, clientHeight } = containerRef.current;
          setDimensions({ width: clientWidth, height: clientHeight });
        }
      };

      // Initial update
      updateDimensions();

      // Update on resize
      window.addEventListener('resize', updateDimensions);

      // Cleanup
      return () => {
        window.removeEventListener('resize', updateDimensions);
      };
    }
  }, [width, height]);

  // Initialize the store once dimensions are available
  useEffect(() => {
    if (dimensions.width > 0 && dimensions.height > 0) {
      initialize(dimensions.width, dimensions.height);

      // Load the last active project
      const { loadLastActiveProject, nodes } = useMindMapStore.getState();

      // Only load if there are no nodes already loaded
      if (!nodes || Object.keys(nodes).length === 0) {
        console.log('[MindMap] No nodes found, loading last active project');
        const success = loadLastActiveProject();
        console.log('[MindMap] Loaded last active project:', success);

        // If no last active project, create a new default project
        if (!success) {
          console.log('[MindMap] No last active project found, creating default project');
          const { createNewProject } = useMindMapStore.getState();
          createNewProject('Untitled Mindmap');
        }
      } else {
        console.log('[MindMap] Nodes already loaded, skipping load of last active project');
      }
    }
  }, [dimensions, initialize]);

  // Set up autosave
  useEffect(() => {
    if (autosaveInterval <= 0) return;

    const intervalId = setInterval(() => {
      saveProject();
    }, autosaveInterval);

    return () => {
      clearInterval(intervalId);
    };
  }, [autosaveInterval, saveProject]);

  // Set up keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      console.log('Key pressed:', e.key, 'Shift:', e.shiftKey);

      // Skip if focus is in an input or textarea
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement
      ) {
        console.log('Focus is in input/textarea, ignoring keyboard shortcut');
        return;
      }

      const { selectedNodeId, nodes, addNode } = useMindMapStore.getState();
      console.log('Selected node ID:', selectedNodeId);

      // Handle Tab key for adding child nodes
      if (e.key === 'Tab' && selectedNodeId) {
        // Stop the event from propagating and prevent default behavior
        e.stopPropagation();
        e.preventDefault();

        console.log('Tab key pressed with node selected:', selectedNodeId);

        // Get the selected node
        const selectedNode = nodes[selectedNodeId];
        if (!selectedNode) {
          console.log('Selected node not found in nodes object');
          return;
        }

        console.log('Selected node found:', selectedNode);

        // Calculate position for the new node (offset from parent)
        const x = selectedNode.x + 200; // Offset to the right
        const y = selectedNode.y + 50;  // Offset down

        console.log('Creating new node at position:', x, y);

        // Add the child node
        const childId = addNode(
          selectedNodeId,
          'New Node',
          x,
          y,
          { metadata: { isManuallyAdded: true } }
        );

        console.log('Added child node with ID:', childId);

        // Register the node creation event
        if (childId) {
          RegistrationManager.registerEvent(EventType.NODE_CREATED, { id: childId });
          console.log('Registered node creation event');

          // If not Shift+Tab, select the new node
          if (!e.shiftKey) {
            useMindMapStore.getState().selectNode(childId);
            console.log('Selected new node:', childId);
          } else {
            console.log('Keeping parent node selected (Shift+Tab)');
          }
        } else {
          console.log('Failed to create child node');
        }

        return false;
      }
    };

    // Use capture phase to ensure we get the event before other handlers
    window.addEventListener('keydown', handleKeyDown, true);
    return () => window.removeEventListener('keydown', handleKeyDown, true);
  }, []);

  return (
    <div className="mindmap-container" ref={containerRef}>
      {/* Toolbar */}
      <MindMapToolbar />

      {/* Canvas */}
      <div
        className="mindmap-canvas-container"
        /* Removed onClick handler to prevent governance box from disappearing */
      >
        <MindMapCanvas width={dimensions.width} height={dimensions.height} />
      </div>

      {/* Dialogs */}
      <ProjectDialog
        isOpen={showProjectDialog}
        onClose={() => setShowProjectDialog(false)}
      />
    </div>
  );
};

export default MindMap;
