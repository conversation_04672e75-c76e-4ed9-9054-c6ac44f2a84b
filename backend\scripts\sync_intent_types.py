"""
Sync Intent Types Script

This script reads the intent types from the backend configuration and generates
a TypeScript file for the frontend with the same intent types.

Run this script whenever you update the intent_types.yaml file.
"""
import os
import sys
import yaml
import json

# Add the parent directory to the path so we can import from the backend
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the intent config
from config.intent_config import load_intent_config

# Path to the frontend intent types file
FRONTEND_INTENT_TYPES_PATH = os.path.join(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
    "frontend", "src", "core", "config", "intentTypes.ts"
)

def generate_frontend_intent_types():
    """
    Generate the frontend intent types TypeScript file from the backend configuration
    """
    # Load the intent configuration
    config = load_intent_config()
    intents = config.get("intents", {})
    
    # Generate the TypeScript file content
    ts_content = """/**
 * Intent Types Configuration
 * 
 * This module provides access to the intent types configuration.
 * It loads the same intent types that are defined in the backend.
 * 
 * THIS FILE IS AUTO-GENERATED - DO NOT EDIT DIRECTLY
 * Instead, edit the backend/config/intent_types.yaml file and run the sync_intent_types.py script
 */

// Intent type interface
export interface IntentType {
  id: string;
  displayName: string;
  description: string;
  requiresMindmap: boolean;
  requiresChatFork: boolean;
  requiresTemplate: boolean;
}

// Intent types from the backend configuration
export const intentTypes: IntentType[] = [
"""
    
    # Add each intent type
    for intent_id, intent_data in intents.items():
        ts_content += f"""  {{
    id: '{intent_id}',
    displayName: '{intent_data.get("display_name", intent_id.capitalize())}',
    description: '{intent_data.get("description", "").replace("'", "\\'")}',
    requiresMindmap: {str(intent_data.get("requires_mindmap", False)).lower()},
    requiresChatFork: {str(intent_data.get("requires_chatfork", False)).lower()},
    requiresTemplate: {str(intent_data.get("requires_template", False)).lower()}
  }},
"""
    
    # Remove the trailing comma
    ts_content = ts_content.rstrip(",\n") + "\n];\n\n"
    
    # Add utility functions
    ts_content += """/**
 * Get all intent types
 */
export const getAllIntentTypes = (): IntentType[] => {
  return intentTypes;
};

/**
 * Get an intent type by ID
 */
export const getIntentTypeById = (id: string): IntentType | undefined => {
  return intentTypes.find(intent => intent.id === id);
};

/**
 * Check if an intent type is valid
 */
export const isValidIntentType = (id: string): boolean => {
  return intentTypes.some(intent => intent.id === id);
};

/**
 * Get the response type configuration for an intent
 */
export const getResponseTypeForIntent = (intent: string): any => {
  const intentType = getIntentTypeById(intent);
  
  if (!intentType) {
    return {
      type: 'miscellaneous',
      requiresMindmap: false,
      requiresChatFork: false,
      requiresTemplate: false
    };
  }
  
  return {
    type: intentType.id,
    requiresMindmap: intentType.requiresMindmap,
    requiresChatFork: intentType.requiresChatFork,
    requiresTemplate: intentType.requiresTemplate
  };
};

export default {
  getAllIntentTypes,
  getIntentTypeById,
  isValidIntentType,
  getResponseTypeForIntent
};
"""
    
    # Write the TypeScript file
    with open(FRONTEND_INTENT_TYPES_PATH, "w") as f:
        f.write(ts_content)
    
    print(f"Generated frontend intent types file at {FRONTEND_INTENT_TYPES_PATH}")
    print(f"Found {len(intents)} intent types: {', '.join(intents.keys())}")

if __name__ == "__main__":
    generate_frontend_intent_types()
