import React from 'react';
import { useMindMap } from './context/MindMapContext';
import { useProjectManagement } from './hooks/useProjectManagement';

export const ProjectDialog: React.FC = () => {
  const {
    showProjectDialog,
    setShowProjectDialog,
    projects
  } = useMindMap();

  const { loadProject, deleteProject } = useProjectManagement();

  if (!showProjectDialog) return null;

  return (
    <div className="project-list-dialog" style={{
      position: 'absolute',
      top: '80px',
      left: '50%',
      transform: 'translateX(-50%)',
      backgroundColor: '#fff',
      padding: '20px',
      borderRadius: '5px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      zIndex: 1000,
      minWidth: '300px'
    }}>
      <div className="dialog-header">
        <h3>Open Project</h3>
        <button 
          className="close-button"
          onClick={() => setShowProjectDialog(false)}
          style={{
            position: 'absolute',
            top: '10px',
            right: '10px',
            background: 'none',
            border: 'none',
            fontSize: '20px',
            cursor: 'pointer'
          }}
        >
          ×
        </button>
      </div>
      <div className="project-list">
        {projects.map(project => (
          <div 
            key={project.id} 
            className="project-item"
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '10px',
              borderBottom: '1px solid #eee'
            }}
          >
            <div className="project-info">
              <div className="project-name">{project.name}</div>
              <div className="project-date" style={{ fontSize: '12px', color: '#666' }}>
                {new Date(project.lastModified).toLocaleDateString()}
              </div>
            </div>
            <div className="project-actions">
              <button 
                className="toolbar-button"
                onClick={() => {
                  loadProject(project.id);
                  setShowProjectDialog(false);
                }}
              >
                Open
              </button>
              <button 
                className="toolbar-button delete"
                onClick={() => {
                  if (window.confirm('Are you sure you want to delete this project?')) {
                    deleteProject(project.id);
                  }
                }}
                style={{ marginLeft: '5px', backgroundColor: '#dc3545' }}
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}; 