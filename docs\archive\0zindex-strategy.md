# Z-Index Strategy for MindBack Application

This document defines the z-index hierarchy for all components in the MindBack application. All developers must follow these guidelines to ensure consistent UI layering.

## Z-Index Ranges

| Range       | Purpose                                      |
|-------------|----------------------------------------------|
| 0-999       | Base content, canvas elements                |
| 1000-1999   | Floating UI elements, toolbars               |
| 2000-2999   | Dialogs, modals (standard)                   |
| 3000-3999   | Critical UI components (always on top)       |
| 4000-4999   | Notifications, toasts                        |
| 5000-5999   | Tooltips, popovers                           |
| 9000-9999   | Fullscreen overlays, loading screens         |

## Specific Component Z-Indexes

| Component                   | Z-Index | Notes                                      |
|-----------------------------|---------|-------------------------------------------|
| MindMap Canvas              | 100     | Base layer for mindmap content             |
| MindMap Nodes               | 200     | Individual nodes in the mindmap            |
| MindMap Connections         | 150     | Lines connecting nodes                     |
| Toolbar                     | 1000    | Main application toolbar                   |
| MindMap Manager             | 2000    | Mind map management dialog                 |
| Node Dialog                 | 2100    | Dialog for editing nodes                   |
| GovernanceBox               | 3000    | Always visible above standard dialogs      |
| GovernanceBox (minimized)   | 3000    | Same z-index when minimized                |
| ChatFork                    | 2500    | Chat fork component                        |
| Notifications               | 4000    | System notifications                       |
| Tooltips                    | 5000    | Tooltips for UI elements                   |
| Loading Overlay             | 9000    | Full-screen loading indicator              |
| Context Menu                | 5500    | Right-click context menus                  |

## Guidelines for Adding New Components

1. **Choose the appropriate range** based on the component's purpose
2. **Avoid arbitrary values** - use the defined ranges
3. **Document new components** by adding them to this table
4. **Consider component relationships** - related components should have nearby z-indexes
5. **Test z-index interactions** with existing components before committing

## Implementation Notes

- Use CSS variables for z-index values to maintain consistency
- Reference this document when creating new components
- Update this document when adding new component types
