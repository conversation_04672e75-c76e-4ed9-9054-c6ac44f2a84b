/**
 * MindMapStoreFactory
 *
 * Creates and manages isolated MindMapStore instances for each mindsheet.
 * This ensures true separation of state between different mindsheets.
 *
 * IMPORTANT: This factory ensures complete isolation between mindsheets by:
 * 1. Creating a separate store instance for each sheet
 * 2. Maintaining a registry of all stores
 * 3. Providing methods to access and manage stores
 * 4. Ensuring stores are properly saved and restored when switching sheets
 *
 * This factory follows the Excel model where each worksheet has its own state.
 */

import { create } from 'zustand';
import { MindMapState, createMindMapStore } from './MindMapStore';
import { MindMapStateData } from './StoreTypes';
// Import MindBookStore to avoid dynamic imports
import { useMindBookStore } from './MindBookStore';

interface MindMapStoreRegistry {
  // Map of sheet ID to MindMapStore
  stores: Record<string, ReturnType<typeof createMindMapStore>>;

  // Currently active sheet ID
  activeSheetId: string | null;

  // Get or create a store for a specific sheet
  getStore: (sheetId: string) => ReturnType<typeof createMindMapStore>;

  // Check if a store exists for a sheet
  hasStore: (sheetId: string) => boolean;

  // Remove a store for a sheet
  removeStore: (sheetId: string) => void;

  // Clear all stores
  clearStores: () => void;

  // Set the active sheet ID
  setActiveSheetId: (sheetId: string | null) => void;

  // Save the state of a store to the MindBookStore
  saveStoreState: (sheetId: string) => void;
}

// Create the store registry
export const useMindMapStoreRegistry = create<MindMapStoreRegistry>((set, get) => ({
  // Initial state - empty map of stores
  stores: {},
  activeSheetId: null,

  // Get or create a store for a specific sheet
  getStore: (sheetId: string) => {
    const { stores } = get();

    // If the store already exists, return it
    if (stores[sheetId]) {
      console.log(`[MindMapStoreFactory] Returning existing store for sheet: ${sheetId}`);
      return stores[sheetId];
    }

    // Otherwise, create a new store
    console.log('MindMapStoreFactory: Creating new store for sheet:', sheetId);
    const newStore = createMindMapStore();

    // Initialize the store with the sheet ID
    newStore.getState().initialize(window.innerWidth, window.innerHeight - 40, sheetId);
    console.log(`[MindMapStoreFactory] Initialized new store for sheet: ${sheetId}`);

    // Add it to the registry
    set(state => ({
      stores: {
        ...state.stores,
        [sheetId]: newStore
      }
    }));

    return newStore;
  },

  // Check if a store exists for a sheet
  hasStore: (sheetId: string) => {
    const { stores } = get();
    return !!stores[sheetId];
  },

  // Remove a store for a sheet
  removeStore: (sheetId: string) => {
    console.log('MindMapStoreFactory: Removing store for sheet:', sheetId);

    // First save the state to MindBookStore
    get().saveStoreState(sheetId);

    // Then remove the store
    set(state => {
      const newStores = { ...state.stores };
      delete newStores[sheetId];
      return { stores: newStores };
    });
  },

  // Clear all stores
  clearStores: () => {
    console.log('MindMapStoreFactory: Clearing all stores');

    // Save all store states before clearing
    const { stores } = get();
    Object.keys(stores).forEach(sheetId => {
      get().saveStoreState(sheetId);
    });

    set({ stores: {} });
  },

  // Set the active sheet ID
  setActiveSheetId: (sheetId: string | null) => {
    const { activeSheetId } = get();

    // If the active sheet is changing, save the state of the previous sheet
    if (activeSheetId && activeSheetId !== sheetId) {
      get().saveStoreState(activeSheetId);
    }

    set({ activeSheetId: sheetId });

    console.log('MindMapStoreFactory: Active sheet ID set to:', sheetId);
  },

  // Save the state of a store to the MindBookStore
  saveStoreState: (sheetId: string) => {
    const { stores } = get();
    const store = stores[sheetId];

    if (!store) {
      console.warn('MindMapStoreFactory: Cannot save state for non-existent store:', sheetId);
      return;
    }

    // Get the state from the store
    const storeState = store.getState();

    // Check if there are any nodes to save
    const hasNodes = storeState.nodes && Object.keys(storeState.nodes).length > 0;
    if (!hasNodes) {
      console.log('MindMapStoreFactory: No nodes to save for sheet:', sheetId);
      return;
    }

    // Extract the relevant state to save
    const stateToSave = {
      nodes: storeState.nodes,
      connections: storeState.connections,
      rootNodeId: storeState.rootNodeId,
      position: storeState.position,
      scale: storeState.scale
    };

    try {
      // Use the imported useMindBookStore
      const mindBookStore = useMindBookStore.getState();

      // Check if the state has actually changed before saving
      const currentState = mindBookStore.getSheetState(sheetId);

      // Only save if the state has changed or there is no current state
      if (!currentState || JSON.stringify(currentState) !== JSON.stringify(stateToSave)) {
        mindBookStore.saveSheetState(sheetId, stateToSave);
        console.log('MindMapStoreFactory: Saved state for sheet:', sheetId);
      } else {
        console.log('MindMapStoreFactory: State unchanged, skipping save for sheet:', sheetId);
      }
    } catch (error) {
      console.error('MindMapStoreFactory: Error saving state to MindBookStore:', error);
    }
  }
}));

// Helper function to get a store for a sheet
export function getMindMapStore(sheetId: string): ReturnType<typeof createMindMapStore> {
  // Also set this as the active sheet ID
  useMindMapStoreRegistry.getState().setActiveSheetId(sheetId);
  return useMindMapStoreRegistry.getState().getStore(sheetId);
}

// Helper function to check if a store exists for a sheet
export function hasMindMapStore(sheetId: string): boolean {
  return useMindMapStoreRegistry.getState().hasStore(sheetId);
}

// Helper function to remove a store for a sheet
export function removeMindMapStore(sheetId: string): void {
  return useMindMapStoreRegistry.getState().removeStore(sheetId);
}

// Helper function to get the active sheet ID
export function getActiveMindMapSheetId(): string | null {
  return useMindMapStoreRegistry.getState().activeSheetId;
}

// Helper function to save the state of a store
export function saveMindMapStoreState(sheetId: string): void {
  return useMindMapStoreRegistry.getState().saveStoreState(sheetId);
}

// Helper function for UnifiedLayoutManager to access sheet stores
export function getSheetMindMapStore(sheetId: string): ReturnType<typeof createMindMapStore> | null {
  const registry = useMindMapStoreRegistry.getState();
  if (registry.hasStore(sheetId)) {
    return registry.getStore(sheetId);
  }
  console.warn(`[MindMapStoreFactory] No store found for sheet: ${sheetId}`);
  return null;
}

// Global connector for UnifiedLayoutManager
(window as any).getSheetMindMapStore = getSheetMindMapStore;
(window as any).useMindMapStoreRegistry = useMindMapStoreRegistry;

console.log('[MindMapStoreFactory] Global store connector initialized for UnifiedLayoutManager');
