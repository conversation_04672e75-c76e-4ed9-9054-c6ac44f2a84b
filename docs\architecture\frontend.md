# Frontend Architecture

## Components

- React Components
- State Management
- UI/UX Design


## Frontend Architecture

# Frontend Architecture

## Layer Structure
- /components - Reusable UI components
- /features - Feature-specific modules
- /core - Core business logic
- /shared - Shared utilities and types
- /api - API integration layer

## Dependency Rules
1. Feature modules can only depend on core and shared
2. Components should be pure and not contain business logic
3. Core modules cannot depend on UI components
4. API layer should be isolated and only accessed through core

## Module Organization
- Each feature follows Domain-Driven Design
- Shared components use atomic design principles
- State management follows flux/redux pattern

## ChatFork Architecture Design

## Architecture Design

To ensure a professional implementation that properly integrates ChatFork functionality with the Canvas, we'll implement a centralized state management approach:

## Frontend Directory Structure

## New Directory Structure

```
frontend/src/
├── governance/            # Governance system for the application
│   ├── chat/              # GovernanceChatDialog and related components
│   ├── agents/            # Individual specialized agents
│   ├── templates/         # Response templates
│   └── rag/               # Retrieval-augmented generation
├── components/            # React components
│   ├── MindMap/           # Mind mapping components
│   ├── ChatFork/          # Chat fork functionality
│   └── shared/            # Shared UI components
├── services/              # Application services
│   ├── api/               # API clients
│   └── transformers/      # Data transformation
└── store/                 # Global state management
```

## Mindmap Code Quality and Architecture

## 1. Code Quality and Architecture

## Manual Node Addition

## 2. Manual Node Addition to Automatic Mindmaps

## Mindmap Positioning and Canvas Management

## 3. Mindmap Positioning and Canvas Management

## Node Positioning and Connection Management

## 4. Node Positioning and Connection Management

## Node Positioning Rules

# mindmap
when the mindmap opens:
    the governance dialogbox shall take the default position (position: x:223, y:550, width:700px height:350px, always in the top layer)
when a node dialogbox opens, it opens at default position (position: x:223, y:100, width:2000px height:900px)

# dialog layering
- governance chat dialog shall have a z-index of 2100
- node dialog shall have a z-index of 2000
- all popover menus shall have a z-index of 2200

#Chatfork

## Z-Index Strategy

# Z-Index Strategy for MindBack Application

This document defines the z-index hierarchy for all components in the MindBack application. All developers must follow these guidelines to ensure consistent UI layering.

## Z-Index Ranges

| Range       | Purpose                                      |
|-------------|----------------------------------------------|
| 0-999       | Base content, canvas elements                |
| 1000-1999   | Floating UI elements, toolbars               |
| 2000-2999   | Dialogs, modals (standard)                   |
| 3000-3999   | Critical UI components (always on top)       |
| 4000-4999   | Notifications, toasts                        |
| 5000-5999   | Tooltips, popovers                           |
| 9000-9999   | Fullscreen overlays, loading screens         |

## Specific Component Z-Indexes

| Component                   | Z-Index | Notes                                      |
|-----------------------------|---------|-------------------------------------------|
| MindMap Canvas              | 100     | Base layer for mindmap content             |
| MindMap Nodes               | 200     | Individual nodes in the mindmap            |
| MindMap Connections         | 150     | Lines connecting nodes                     |
| Toolbar                     | 1000    | Main application toolbar                   |
| MindMap Manager             | 2000    | Mind map management dialog                 |
| Node Dialog                 | 2100    | Dialog for editing nodes                   |
| GovernanceBox               | 3000    | Always visible above standard dialogs      |
| GovernanceBox (minimized)   | 3000    | Same z-index when minimized                |
| ChatFork                    | 2500    | Chat fork component                        |
| Notifications               | 4000    | System notifications                       |
| Tooltips                    | 5000    | Tooltips for UI elements                   |
| Loading Overlay             | 9000    | Full-screen loading indicator              |
| Context Menu                | 5500    | Right-click context menus                  |

## Guidelines for Adding New Components

1. **Choose the appropriate range** based on the component's purpose
2. **Avoid arbitrary values** - use the defined ranges
3. **Document new components** by adding them to this table
4. **Consider component relationships** - related components should have nearby z-indexes
5. **Test z-index interactions** with existing components before committing

## Implementation Notes

- Use CSS variables for z-index values to maintain consistency
- Reference this document when creating new components
- Update this document when adding new component types