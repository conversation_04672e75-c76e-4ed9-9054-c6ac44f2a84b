import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  Typography, 
  Paper, 
  TextField, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  Stack, 
  Divider
} from '@mui/material';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import { Node } from '../../core/models/Node';
import { useMindMapStore } from '../../core/state/MindMapStore';

interface ManualNodeControlsProps {
  selectedNode: Node | null;
}

const ManualNodeControls: React.FC<ManualNodeControlsProps> = ({ selectedNode }) => {
  const [newNodeText, setNewNodeText] = useState('');
  const [newNodeIntent, setNewNodeIntent] = useState('miscellaneous');
  
  // Access mindmap store functions
  const { addChildNode } = useMindMapStore();
  
  const handleAddChildNode = () => {
    if (!selectedNode || !newNodeText.trim()) {
      console.log('Cannot add node: No selected node or empty text');
      return;
    }
    
    console.log('Adding manual child node to parent:', selectedNode);
    
    try {
      // Validate the selected node has a valid ID
      if (!selectedNode.id) {
        console.error('Cannot add child: Selected node has no ID');
        return;
      }
      
      // Add child node with manual creation metadata
      const childId = addChildNode(selectedNode.id, {
        text: newNodeText,
        metadata: {
          intent: newNodeIntent,
          isManuallyAdded: true,
          creationSource: 'manual',
          tags: []
        }
      });
      
      if (!childId) {
        console.error('Failed to add child node - returned empty ID');
        return;
      }
      
      console.log('Successfully added manual child node with ID:', childId);
      
      // Reset form
      setNewNodeText('');
      
    } catch (error) {
      console.error('Error adding manual child node:', error);
    }
  };
  
  return (
    <Paper
      elevation={3}
      sx={{
        p: 2,
        mb: 2,
        borderRadius: 2,
        backgroundColor: '#f9f9f9',
        border: '1px solid #e0e0e0'
      }}
    >
      <Typography variant="h6" gutterBottom>
        Manual Node Addition
      </Typography>
      
      <Divider sx={{ mb: 2 }} />
      
      <Stack spacing={2}>
        {selectedNode ? (
          <>
            <Box>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Selected node: <strong>{selectedNode.text}</strong>
              </Typography>
              <Typography variant="caption" color="text.secondary">
                ID: {selectedNode.id}
              </Typography>
            </Box>
            
            <TextField
              label="New Node Text"
              value={newNodeText}
              onChange={(e) => setNewNodeText(e.target.value)}
              fullWidth
              size="small"
              variant="outlined"
              placeholder="Enter text for the new node"
            />
            
            <FormControl fullWidth size="small">
              <InputLabel>Intent</InputLabel>
              <Select
                value={newNodeIntent}
                onChange={(e) => setNewNodeIntent(e.target.value)}
                label="Intent"
              >
                <MenuItem value="factual">Factual</MenuItem>
                <MenuItem value="exploratory">Exploratory</MenuItem>
                <MenuItem value="teleological">Teleological</MenuItem>
                <MenuItem value="instantiation">Instantiation</MenuItem>
                <MenuItem value="miscellaneous">Miscellaneous</MenuItem>
              </Select>
            </FormControl>
            
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddCircleOutlineIcon />}
              onClick={handleAddChildNode}
              disabled={!newNodeText.trim()}
              fullWidth
            >
              Add Child Node
            </Button>
          </>
        ) : (
          <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
            Select a node in the mindmap to add child nodes
          </Typography>
        )}
      </Stack>
    </Paper>
  );
};

export default ManualNodeControls; 