/**
 * polyfills.ts
 * 
 * This file provides browser-compatible polyfills for Node.js built-in modules
 * that might be used by dependencies but aren't available in the browser.
 */

// Simple EventEmitter implementation for browser
export class EventEmitter {
  private _events: Record<string, Function[]>;

  constructor() {
    this._events = {};
  }

  on(event: string, listener: Function): this {
    if (!this._events[event]) {
      this._events[event] = [];
    }
    this._events[event].push(listener);
    return this;
  }

  off(event: string, listener: Function): this {
    if (!this._events[event]) return this;
    this._events[event] = this._events[event].filter(l => l !== listener);
    return this;
  }

  emit(event: string, ...args: any[]): boolean {
    if (!this._events[event]) return false;
    this._events[event].forEach(listener => listener.apply(this, args));
    return true;
  }

  once(event: string, listener: Function): this {
    const onceWrapper = (...args: any[]) => {
      this.off(event, onceWrapper);
      listener.apply(this, args);
    };
    this.on(event, onceWrapper);
    return this;
  }
}

// Create a default instance
const eventEmitter = new EventEmitter();

// Initialize polyfills
export function initPolyfills(): void {
  // Add polyfills to window if needed
  if (typeof window !== 'undefined') {
    // Add EventEmitter to window for dependencies that might look for it
    (window as any).EventEmitter = EventEmitter;
    
    // Add Node.js process shim if needed
    if (!(window as any).process) {
      (window as any).process = {
        env: {},
        nextTick: (fn: Function) => setTimeout(fn, 0)
      };
    }
  }
  
  console.log('Browser polyfills initialized');
}

export default eventEmitter;
