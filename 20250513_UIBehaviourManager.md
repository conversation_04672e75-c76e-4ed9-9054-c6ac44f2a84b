# UI Behavior Manager

## Current State Analysis

The application currently uses several approaches for managing UI component behaviors:

### 1. Custom Events System

The application uses a custom events system with the `document.dispatchEvent` and `document.addEventListener` pattern. Events are prefixed with `mindback:` (e.g., `mindback:collapse_governance_box`, `mindback:center_view`).

This approach is used for communication between components that aren't directly connected in the component hierarchy, such as:
- MindSheet triggering governance box collapse
- Notifying components about mindmap initialization
- Coordinating view centering

**Example from MindSheet.tsx:**
```typescript
// Dispatch an event to collapse and reposition the governance box
setTimeout(() => {
  console.log('MindSheet: Dispatching collapse-governance-box event');
  const collapseEvent = new CustomEvent('mindback:collapse_governance_box', {
    detail: {
      sheetId: id,
      position: 'top-right'
    }
  });
  document.dispatchEvent(collapseEvent);
}, 300);
```

### 2. UI Positioning System

There's a dedicated positioning system in `frontend\src\core\positioning\UIPositioningManager.ts` that handles:
- Element registration
- Position management
- Collision detection
- Positioning strategies (right-side, left-side, center, avoid-overlap)

Components use the `useUIElement` hook to register with this system and get positioning functionality.

**Example from GovernanceBoxPositioned.tsx:**
```typescript
const {
  position,
  setPosition,
  size,
  setSize,
  visible,
  setVisible,
  registerCollisionHandler
} = useUIElement(
  'governance-box',
  'governance-box',
  DEFAULT_POSITION,
  DEFAULT_SIZE,
  ZIndexLayer.DIALOGS,
  'manual', // Use manual strategy to prevent automatic repositioning
  10 // High priority
);
```

### 3. Registration Manager

The `RegistrationManager` (`frontend\src\core\services\RegistrationManager.ts`) is a singleton that:
- Records user and system actions
- Provides an audit trail
- Logs events to both UI and backend
- Defines event types in an enum

**Example from NodeComponent.tsx:**
```typescript
// Register the node selection event
RegistrationManager.registerEvent(EventType.NODE_SELECTED, {
  id: node.id,
  text: node.text,
  position: { x: nodeX, y: nodeY }
});
```

### 4. Store-Based State Management

The application uses Zustand stores for state management:
- MindMapStore for mindmap state
- MindBookStore for sheet management
- Various component-specific stores

## Current Issues

1. [ ] Behavior logic is scattered across multiple components
2. [ ] No centralized configuration for UI behaviors
3. [ ] Inconsistent behavior handling between components
4. [ ] Duplicate event handling code in multiple components
5. [ ] Difficult to track and modify behaviors across the application
6. [x] Store methods contain DOM interactions and event dispatching (fixed in Pure Store Methods implementation)

## Target Structure: UIBehaviorManager

### Core Components

1. [ ] **UIBehaviorManager**: Centralized service for managing UI component behaviors
2. [ ] **UIBehaviorConfig**: Configuration file for default behaviors
3. [ ] **useUIBehavior**: Hook for components to access the behavior manager
4. [ ] **Integration with existing systems**: RegistrationManager, UIPositioningManager, etc.

### Implementation Plan

#### 1. Create UIBehaviorManager Class

[ ] Create a singleton class for managing UI behaviors
[ ] Define behavior types and interfaces
[ ] Implement methods for registering and executing behaviors
[ ] Set up default behaviors for common scenarios

```typescript
/**
 * UIBehaviorManager.ts
 *
 * A centralized service for managing UI component behaviors based on system events.
 */

// Define behavior types
export type UIBehaviorType =
  | 'governance-box-collapse'
  | 'governance-box-expand'
  | 'governance-box-reposition'
  | 'mindmap-center-view'
  | 'mindmap-manager-open'
  | 'mindmap-manager-close'
  | string; // Allow for future behaviors

// Singleton class for managing UI behaviors
class UIBehaviorManager {
  private static instance: UIBehaviorManager;
  private behaviors: Record<string, Function> = {};

  // Implementation details...
}
```

#### 2. Create UIBehaviorConfig

[ ] Define default behaviors for different components
[ ] Create configuration for different system events
[ ] Document expected behaviors for each component

```typescript
/**
 * UIBehaviorConfig.ts
 *
 * Configuration file for UI component behaviors.
 */

export const UIBehaviorConfig = {
  // Governance Box behaviors
  governanceBox: {
    // When a mindmap is created
    onMindmapCreated: {
      action: 'collapse',
      position: 'top-right'
    },
    // More configurations...
  },
  // More components...
};
```

#### 3. Create useUIBehavior Hook

[ ] Create a hook for components to access the behavior manager
[ ] Implement methods for executing and registering behaviors
[ ] Ensure proper typing and documentation

```typescript
// Create a hook for components to use
export const useUIBehavior = () => {
  return {
    executeBehavior: uiBehaviorManager.executeBehavior.bind(uiBehaviorManager),
    registerBehavior: uiBehaviorManager.registerBehavior.bind(uiBehaviorManager)
  };
};
```

#### 4. Integrate with Existing Systems

[ ] Connect with RegistrationManager for event logging
[ ] Integrate with UIPositioningManager for position management
[ ] Set up event listeners for system events

```typescript
// Example integration with RegistrationManager
private setupEventListeners(): void {
  document.addEventListener('mindback:mindmap_created', (event: any) => {
    // Log the event
    RegistrationManager.registerEvent(EventType.MINDMAP_CREATED, event.detail);

    // Execute behaviors
    this.executeBehavior('governance-box-collapse', event.detail);
    this.executeBehavior('mindmap-center-view', event.detail);
  });
}
```

#### 5. Update Components to Use UIBehaviorManager

[ ] Refactor MindSheet component to use behavior manager
[ ] Update GovernanceBoxPositioned to use behavior manager
[ ] Modify MindMapCanvasWrapper to use behavior manager
[ ] Update other components as needed

```typescript
// Example in MindSheet.tsx
const { executeBehavior } = useUIBehavior();

// When a mindmap is created
useEffect(() => {
  if (needsInitialization && contentType === MindSheetContentType.MINDMAP) {
    // Initialize the mindmap
    // ...

    // Execute behaviors
    executeBehavior('governance-box-collapse', { sheetId: id });
    executeBehavior('mindmap-center-view', { sheetId: id });
  }
}, [needsInitialization, contentType, id]);
```

## Specific Behaviors to Implement

### Governance Box Behaviors

1. [ ] **Collapse on Mindmap Creation**: Collapse and move to top-right when a mindmap is created
2. [ ] **Stay Collapsed During Node Editing**: Remain collapsed when a node is being edited
3. [ ] **Expand on Return to Chat Mode**: Expand and center when returning to chat mode
4. [ ] **Reposition on Window Resize**: Maintain position relative to window edges on resize

### Mindmap Behaviors

1. [ ] **Center View on Creation**: Center the view on the root node when a mindmap is created
2. [ ] **Select Root Node on Creation**: Automatically select the root node with blue frame
3. [ ] **Adjust View on Node Selection**: Ensure selected nodes are visible in the viewport
4. [ ] **Reposition Nodes on Window Resize**: Maintain relative positions on window resize

### Mindmap Manager Behaviors

1. [ ] **Open on Specific Actions**: Open when specific actions are triggered
2. [ ] **Close on Mindmap Creation**: Close when a new mindmap is created
3. [ ] **Position Relative to Governance Box**: Position to avoid overlap with governance box
4. [ ] **Maintain State Between Sessions**: Remember state between sessions

## Benefits of Implementation

1. [ ] Centralized management of UI component behaviors
2. [ ] Consistent behavior across the application
3. [ ] Easy configuration and customization of behaviors
4. [ ] Clear documentation of expected behaviors
5. [ ] Simplified component code
6. [ ] Improved maintainability and extensibility

## Implementation Timeline

1. [ ] Phase 1: Create core UIBehaviorManager class and configuration
2. [ ] Phase 2: Implement governance box behaviors
3. [ ] Phase 3: Implement mindmap behaviors
4. [ ] Phase 4: Implement mindmap manager behaviors
5. [ ] Phase 5: Refactor components to use the behavior manager
6. [ ] Phase 6: Testing and validation
