/**
 * StageCompatWrapper Component
 *
 * A wrapper around the Konva Stage component that provides compatibility
 * with different versions of React and Konva.
 */

import React, { forwardRef } from 'react';
import { Stage } from 'react-konva';
import './StageCompatWrapper.css';

// Cast Stage to any to bypass TypeScript errors
const KonvaStage = Stage as any;

// Define the props interface
interface StageCompatWrapperProps {
  width: number;
  height: number;
  draggable?: boolean;
  x?: number;
  y?: number;
  scaleX?: number;
  scaleY?: number;
  onDragStart?: (e: any) => void;
  onDragMove?: (e: any) => void;
  onDragEnd?: (e: any) => void;
  onWheel?: (e: any) => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  onKeyUp?: (e: React.KeyboardEvent) => void;
  onClick?: (e: any) => void;
  onMouseDown?: (e: any) => void;
  onMouseMove?: (e: any) => void;
  onMouseUp?: (e: any) => void;
  onMouseEnter?: (e: any) => void;
  onMouseLeave?: (e: any) => void;
  tabIndex?: number;
  children?: React.ReactNode;
}

/**
 * StageCompatWrapper component
 *
 * A wrapper around the Konva Stage component that provides compatibility
 * with different versions of React and Konva.
 */
const StageCompatWrapper = forwardRef<any, StageCompatWrapperProps>(
  (
    {
      width,
      height,
      draggable = false,
      x = 0,
      y = 0,
      scaleX = 1,
      scaleY = 1,
      onDragStart,
      onDragMove,
      onDragEnd,
      onWheel,
      onKeyDown,
      onKeyUp,
      onClick,
      onMouseDown,
      onMouseMove,
      onMouseUp,
      onMouseEnter,
      onMouseLeave,
      tabIndex = 0,
      children
    },
    ref
  ) => {
    return (
      <KonvaStage
        ref={ref}
        width={width}
        height={height}
        draggable={draggable}
        x={x}
        y={y}
        scaleX={scaleX}
        scaleY={scaleY}
        onDragStart={onDragStart}
        onDragMove={onDragMove}
        onDragEnd={onDragEnd}
        onWheel={onWheel}
        onKeyDown={onKeyDown}
        onKeyUp={onKeyUp}
        onClick={onClick}
        onMouseDown={onMouseDown}
        onMouseMove={onMouseMove}
        onMouseUp={onMouseUp}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        tabIndex={tabIndex}
      >
        {children}
      </KonvaStage>
    );
  }
);

// Set display name for debugging
StageCompatWrapper.displayName = 'StageCompatWrapper';

export default StageCompatWrapper;
