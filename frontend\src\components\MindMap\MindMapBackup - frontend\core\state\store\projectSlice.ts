import { StateCreator } from 'zustand';
import { MindMapStore } from './types';

export interface ProjectSlice {
  projectName: string;
  showProjectDialog: boolean;
  scale: number;
  position: { x: number; y: number };
  initialize: (windowWidth: number, windowHeight: number) => void;
  loadProject: (name?: string) => boolean;
  saveProject: () => any;
}

export const createProjectSlice: StateCreator<MindMapStore, [], [], ProjectSlice> = (set, get) => ({
  projectName: 'Untitled',
  showProjectDialog: false,
  scale: 1,
  position: { x: 0, y: 0 },

  initialize: (windowWidth: number, windowHeight: number) => {
    set({
      position: {
        x: windowWidth / 2,
        y: windowHeight / 2
      }
    });
  },

  loadProject: (name?: string) => {
    if (!name) return false;

    try {
      const savedProject = localStorage.getItem(`mindmap_${name}`);
      if (!savedProject) return false;

      const { nodes, connections, position, scale } = JSON.parse(savedProject);
      set({
        nodes,
        connections,
        position,
        scale,
        projectName: name
      });
      return true;
    } catch (error) {
      console.error('Error loading project:', error);
      return false;
    }
  },

  saveProject: () => {
    const state = get();
    const { nodes, connections, position, scale, projectName } = state;
    const projectData = { nodes, connections, position, scale };

    try {
      localStorage.setItem(`mindmap_${projectName}`, JSON.stringify(projectData));
      return true;
    } catch (error) {
      console.error('Error saving project:', error);
      return false;
    }
  }
}); 