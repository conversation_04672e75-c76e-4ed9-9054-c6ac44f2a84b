import { useMindMapStore } from '../../core/state/MindMapStore';
import './MindMapManager.css';

const MindMapManager = () => {
  // Get counts for nodes and connections
  const { nodes, connections } = useMindMapStore();
  const nodeCount = Object.keys(nodes).length;
  const connectionCount = Array.isArray(connections) ? connections.length : 0;

  return (
    <div className="mindmap-manager">
      <div className="mindmap-manager-header">
        <h3>MindMap Manager</h3>
      </div>
      <div className="mindmap-stats-container">
        <div className="mindmap-stat-item">
          <span className="stat-label">Nodes:</span>
          <span className="stat-value">{nodeCount}</span>
        </div>
        <div className="mindmap-stat-item">
          <span className="stat-label">Connections:</span>
          <span className="stat-value">{connectionCount}</span>
        </div>
      </div>
      {/* Rest of the component */}
    </div>
  );
};

export default MindMapManager; 