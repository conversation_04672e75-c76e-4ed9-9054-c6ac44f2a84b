/**
 * GovernanceChat.tsx
 *
 * DEPRECATED: This component is no longer used. Use GovernanceChatDialog from /governance/chat instead.
 * This file can be removed in future cleanup.
 *
 * Main component for the governance agent feature.
 * This component is independent of the MindMap feature.
 */

import React, { useState, useEffect } from 'react';
import { Rnd } from 'react-rnd';
import ChatHeader from './components/ChatHeader';
import MessageList from './components/MessageList';
import MessageInput from './components/MessageInput';
import { useGovernanceChat } from './hooks/useGovernanceChat';
import './GovernanceChat.css';

// Define the action type for external consumers
export interface GovernanceAction {
  type: string;
  payload: any;
}

// Props interface
interface GovernanceChatProps {
  isOpen: boolean;
  isCollapsed?: boolean;
  onClose: () => void;
  onCollapse?: () => void;
  onAction?: (action: GovernanceAction) => void;
  selectedModel?: string;
  onModelChange?: (model: string) => void;
}

// Model options
const modelOptions = [
  { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
  { value: 'gpt-4-turbo', label: 'GPT-4 Turbo' },
  { value: 'gpt-4o-mini', label: 'GPT-4o-mini' },
  { value: 'claude-3-sonnet', label: 'Claude 3 Sonnet' },
  { value: 'claude-3-opus', label: 'Claude 3 Opus' },
];

const GovernanceChat: React.FC<GovernanceChatProps> = ({
  isOpen,
  isCollapsed = false,
  onClose,
  onCollapse,
  onAction,
  selectedModel = 'gpt-4o-mini',
  onModelChange
}) => {
  // State for the draggable dialog
  const [position, setPosition] = useState({ x: 20, y: 20 });
  const [size, setSize] = useState({ width: 400, height: 500 });
  const [initialPosition, setInitialPosition] = useState({ x: 20, y: 20 });
  const [initialSize, setInitialSize] = useState({ width: 400, height: 500 });

  // Save initial position and size on first render
  useEffect(() => {
    setInitialPosition({ ...position });
    setInitialSize({ ...size });
  }, []);

  // Chat state and handlers from hook
  const {
    messages,
    inputText,
    setInputText,
    isSubmitting,
    handleSendMessage,
    handleBuildMindmap
  } = useGovernanceChat({
    onAction
  });

  // Handle action click from message
  const handleActionClick = (action: GovernanceAction) => {
    if (onAction) {
      onAction(action);
    }
  };

  // Restore position handler
  const handleRestorePosition = () => {
    setPosition({ ...initialPosition });
  };

  // Restore size handler
  const handleRestoreSize = () => {
    setSize({ ...initialSize });
  };

  // Check if the last message has teleological intent or mbcpData
  const shouldShowBuildMindmapButton = () => {
    if (messages.length === 0) return false;

    const lastMessage = messages[messages.length - 1];

    // Only show for assistant messages
    if (lastMessage.sender !== 'assistant') return false;

    // Check for teleological intent in various possible locations
    const hasTeleologicalIntent =
      // Check in responseType
      (lastMessage.responseType?.type === 'teleological') ||
      // Check in mbcpData
      (lastMessage.mbcpData?.intent === 'teleological') ||
      // Check for mbcpData with intent field
      (lastMessage.mbcpData?.intent === 'teleological') ||
      // Check in suggestedActions for create_mindmap action
      (lastMessage.suggestedActions?.some(action => action.type === 'create_mindmap'));

    return hasTeleologicalIntent;
  };

  if (!isOpen) {
    return null;
  }

  return (
    <Rnd
      position={position}
      size={size}
      onDragStop={(e, d) => setPosition({ x: d.x, y: d.y })}
      onResizeStop={(e, direction, ref, delta, position) => {
        setSize({
          width: parseInt(ref.style.width),
          height: parseInt(ref.style.height)
        });
        setPosition(position);
      }}
      minWidth={300}
      minHeight={400}
      bounds="window"
      className={`governance-chat ${isCollapsed ? 'collapsed' : ''}`}
      resizeHandleClasses={{
        bottom: 'resize-handle-bottom',
        bottomRight: 'resize-handle-corner',
        bottomLeft: 'resize-handle-corner',
        right: 'resize-handle-right',
        left: 'resize-handle-left',
        top: 'resize-handle-top',
        topRight: 'resize-handle-corner',
        topLeft: 'resize-handle-corner'
      }}
    >
      <div className="governance-chat-container">
        {/* Header */}
        <ChatHeader
          title="Governance Agent"
          onClose={onClose}
          onCollapse={onCollapse}
          isCollapsed={isCollapsed}
          onRestorePosition={handleRestorePosition}
          onRestoreSize={handleRestoreSize}
        />

        {/* Content */}
        {!isCollapsed && (
          <div className="governance-chat-content">
            {/* Message list */}
            <MessageList
              messages={messages}
              onAction={handleActionClick}
              onBuildMindmap={handleBuildMindmap}
              showBuildMindmapButton={shouldShowBuildMindmapButton()}
            />

            {/* Model selector */}
            <div className="model-selector-container">
              <select
                className="model-select"
                value={selectedModel}
                onChange={(e) => onModelChange && onModelChange(e.target.value)}
              >
                {modelOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Message input */}
            <MessageInput
              value={inputText}
              onChange={setInputText}
              onSubmit={handleSendMessage}
              disabled={isSubmitting}
              messages={messages} // Pass messages for debugging
            />
          </div>
        )}
      </div>
    </Rnd>
  );
};

export default GovernanceChat;
