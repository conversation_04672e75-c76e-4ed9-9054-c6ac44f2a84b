import React, { useState } from 'react';

interface FooterGovernanceButtonProps {
  onClick: () => void;
}

const FooterGovernanceButton: React.FC<FooterGovernanceButtonProps> = ({ onClick }) => {
  const [imageSrc, setImageSrc] = useState('/Logo/MB_logo.jpg');
  const [hasError, setHasError] = useState(false);

  const handleImageError = (error: React.SyntheticEvent<HTMLImageElement, Event>) => {
    console.error('FooterGovernanceButton: Logo failed to load:', error);
    console.log('FooterGovernanceButton: Attempted path:', imageSrc);
    
    if (!hasError) {
      // Try with cache busting
      const cacheBuster = `?v=${Date.now()}`;
      console.log('FooterGovernanceButton: Retrying with cache buster:', `/Logo/MB_logo.jpg${cacheBuster}`);
      setImageSrc(`/Logo/MB_logo.jpg${cacheBuster}`);
      setHasError(true);
    }
  };

  const handleImageLoad = () => {
    console.log('FooterGovernanceButton: Logo loaded successfully');
    setHasError(false);
  };

  return (
    <button
      className="footer-governance-button"
      onClick={onClick}
      title="Open Governance Agent"
    >
      <img
        src={imageSrc}
        alt="MindBack Logo"
        className="footer-governance-logo"
        width="16"
        height="16"
        onError={handleImageError}
        onLoad={handleImageLoad}
      />
      <span>Governance</span>
    </button>
  );
};

export default FooterGovernanceButton;
