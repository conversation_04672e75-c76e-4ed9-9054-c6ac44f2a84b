/**
 * GovernanceAPI.ts
 *
 * Service for making API calls to the governance backend.
 */

// Base URL for API calls
const API_BASE_URL = '/api';

/**
 * Interface for the response from the governance chat API
 */
export interface GovernanceChatResponse {
  text: string;
  description?: string;
  intent?: string;
  mbcpData?: any;
  suggestedActions?: Array<{
    type: string;
    label: string;
    payload?: any;
  }>;
  error?: string;
}

/**
 * Send a message to the governance chat API
 *
 * @param message The message to send
 * @param promptType The type of prompt to use (default: 'g-llm_dialogue')
 * @returns The response from the API
 */
export const sendGovernanceMessage = async (
  message: string,
  promptType: string = 'g-llm_dialogue'
): Promise<GovernanceChatResponse> => {
  try {
    console.log(`GovernanceAPI: Sending message to ${promptType} endpoint:`, message);

    // Make the API call to the LLM endpoint instead of governance with full URL
    const response = await fetch(`http://localhost:8000/api/llm/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        prompt: message,
        prompt_type: promptType,
        model: 'gpt-3.5-turbo'
      })
    });

    // Check if the response is OK
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`GovernanceAPI: Error ${response.status} - ${response.statusText}`, errorText);
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    // Parse the response
    const data = await response.json();
    console.log('GovernanceAPI: Received response:', data);

    // Pass through the LLM response without modifying the intent or content
    // Let the MainRouter handle the display logic based on the MBCP protocol
    console.log('GovernanceAPI: Intent from LLM:', data.content?.intent);

    // DIRECT FIX: For factual intents, ensure the description field is properly passed
    // Log the raw data for debugging
    console.log('GovernanceAPI: Raw data from backend:', JSON.stringify(data, null, 2));
    console.log('GovernanceAPI: Content fields:', {
      text: data.content?.text,
      description: data.content?.description,
      intent: data.content?.intent
    });

    // Format the response to match what MainRouter expects
    return {
      text: data.content?.text || 'No response text received',
      // Ensure description is always passed for factual intents
      description: data.content?.description,
      mbcpData: data.content || null,
      intent: data.content?.intent || 'miscellaneous',
      // Include content directly to match MainRouter's expectation
      content: data.content,
      suggestedActions: data.content?.suggestedActions || []
    };
  } catch (error) {
    console.error('GovernanceAPI: Error sending message:', error);
    return {
      text: `Error: ${error.message}`,
      error: error.message
    };
  }
};
