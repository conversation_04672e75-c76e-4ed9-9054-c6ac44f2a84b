// Placeholder file for: frontend\src\components\MindMap\components\Dialogs\ProjectDialog.tsx

import React from 'react';
import { useMindMap } from '../../context/MindMapContext';
import { useProjectManagement } from '../../hooks/useProjectManagement';

export const ProjectDialog: React.FC = () => {
  const { showProjectDialog, setShowProjectDialog, projects } = useMindMap();
  const { loadProject } = useProjectManagement();
  
  if (!showProjectDialog) return null;
  
  return (
    <div className="project-list-dialog">
      <div className="dialog-header">
        <h3>Load Project</h3>
        <button className="close-button" onClick={() => setShowProjectDialog(false)}>×</button>
      </div>
      <div className="dialog-content">
        <div className="project-list">
          {projects.length === 0 ? (
            <p>No projects found.</p>
          ) : (
            projects
              .sort((a, b) => new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime())
              .map(project => (
                <div
                  key={project.id}
                  className="project-item"
                  onClick={() => loadProject(project.id)}
                >
                  <div className="project-name">{project.name}</div>
                  <div className="project-date">
                    {new Date(project.lastModified).toLocaleString()}
                  </div>
                </div>
              ))
          )}
        </div>
      </div>
    </div>
  );
};

export default ProjectDialog;
