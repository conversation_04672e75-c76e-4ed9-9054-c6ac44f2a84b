# MindBack Taskbar Launcher

This folder contains files to create a Windows taskbar button for starting MindBack.

## Files Created:

1. **`start_mindback.bat`** - Main batch file that:
   - Kills any existing uvicorn/node/python processes
   - Changes to project directory
   - Runs the PowerShell setup script

2. **`start_mindback_silent.vbs`** - VBScript wrapper that runs the batch file without showing command windows

3. **`create_shortcut.ps1`** - PowerShell script that creates a desktop shortcut

## Setup Instructions:

### Quick Setup (Recommended):
Simply double-click `setup_taskbar_icon.bat` - it will:
1. Convert your JPG logo to ICO format
2. Create a desktop shortcut with the MB icon
3. Give you final instructions to pin to taskbar

### Manual Setup:
If you prefer to do it step by step:

#### Step 1: Convert Logo to ICO
```bash
python convert_logo_to_ico.py
```

#### Step 2: Create the Shortcut
```powershell
powershell -ExecutionPolicy Bypass -File "create_shortcut.ps1"
```

#### Step 3: Pin to Taskbar
1. Right-click the shortcut on Desktop
2. Select "Pin to taskbar"

## How It Works:

- Single click kills any existing servers and starts fresh
- No command windows will pop up (runs silently)
- Uses your existing `run_setup.ps1` script
- Automatically opens both backend and frontend servers
- Shows MB logo in taskbar

## Testing:

1. Close any running MindBack servers
2. Click the taskbar button
3. Wait ~10-20 seconds for servers to start
4. Navigate to http://localhost:5173/ to verify it's working 