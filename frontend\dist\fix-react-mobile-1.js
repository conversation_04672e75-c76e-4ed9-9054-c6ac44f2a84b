// Additional mobile-specific React compatibility fixes
(function() {
  try {
    console.log('Applying additional mobile-specific React compatibility fixes...');
    
    // Check if we're on a mobile device
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    if (!isMobile) {
      console.log('Not on mobile device, skipping additional mobile-specific fixes');
      return;
    }
    
    console.log('Mobile device detected, applying additional fixes');
    
    // Ensure React is available
    if (!window.React) {
      console.error('React not found, cannot apply additional mobile fixes');
      return;
    }
    
    // Fix for mobile scroll performance
    if (typeof document !== 'undefined') {
      // Add style for improved scroll performance
      const style = document.createElement('style');
      style.textContent = `
        * {
          -webkit-overflow-scrolling: touch;
        }
        body {
          overscroll-behavior: none;
        }
      `;
      document.head.appendChild(style);
      console.log('Added mobile scroll performance styles');
    }
    
    // Fix for mobile input focus
    if (typeof document !== 'undefined') {
      // Add event listeners to fix input focus issues on mobile
      document.addEventListener('touchstart', function(e) {
        if (e.target && (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA')) {
          // Allow default behavior for inputs
          return;
        }
        // Prevent unintended zooming
        if (e.touches.length > 1) {
          e.preventDefault();
        }
      }, { passive: false });
      
      console.log('Added mobile input focus fixes');
    }
    
    // Fix for mobile double-tap
    if (typeof document !== 'undefined') {
      let lastTap = 0;
      document.addEventListener('touchend', function(e) {
        const currentTime = new Date().getTime();
        const tapLength = currentTime - lastTap;
        
        // Detect double-tap and prevent zoom
        if (tapLength < 500 && tapLength > 0) {
          e.preventDefault();
        }
        
        lastTap = currentTime;
      }, { passive: false });
      
      console.log('Added mobile double-tap fix');
    }
    
    console.log('Additional mobile-specific React fixes applied successfully');
  } catch (error) {
    console.error('Error applying additional mobile-specific React fixes:', error);
  }
})();
