import { create } from 'zustand';
import { ChatResponse } from '../../../../services/api/GovernanceLLM';

// Interface for tracked selections
export interface TrackedSelection {
  id: string;          // Unique identifier
  text: string;        // Selected text content
  forkId: string;      // ID of the chat fork this belongs to
  timestamp: number;   // When the selection was created
}

interface ChatForkState {
  // State
  content: ChatResponse | null;
  isVisible: boolean;
  selections: Map<string, TrackedSelection>; // Track multiple selections by ID
  activeSheetId: string | null;

  // Actions
  showChatFork: (content: ChatResponse, sheetId?: string) => void;
  hideChatFork: () => void;
  addSelection: (selection: TrackedSelection) => void;
  removeSelection: (selectionId: string) => void;
  clearAllSelections: () => void;
  getSelectionsByForkId: (forkId: string) => TrackedSelection[];
  setActiveSheetId: (sheetId: string | null) => void;
}

/**
 * ChatForkStore - Centralized state management for ChatFork
 * This store manages the visibility, content, and selection state
 */
export const useChatForkStore = create<ChatForkState>((set, get) => ({
  // Initial state
  content: null,
  isVisible: false,
  selections: new Map<string, TrackedSelection>(),
  activeSheetId: null,

  // Actions
  showChatFork: (content, sheetId) => {
    console.log('ChatForkStore: Showing ChatFork with content:', content);

    set({
      content: content,
      isVisible: true,
      ...(sheetId ? { activeSheetId: sheetId } : {})
    });
  },

  hideChatFork: () => {
    console.log('ChatForkStore: Hiding ChatFork');
    set({
      isVisible: false,
      content: null
    });
  },

  addSelection: (selection) => {
    console.log('ChatForkStore: Adding selection:', selection);
    set((state) => {
      const newSelections = new Map(state.selections);
      newSelections.set(selection.id, selection);
      return { selections: newSelections };
    });
  },

  removeSelection: (selectionId) => {
    console.log('ChatForkStore: Removing selection:', selectionId);
    set((state) => {
      const newSelections = new Map(state.selections);
      newSelections.delete(selectionId);
      return { selections: newSelections };
    });
  },

  clearAllSelections: () => {
    console.log('ChatForkStore: Clearing all selections');
    set({ selections: new Map() });
  },

  getSelectionsByForkId: (forkId) => {
    const state = get();
    return Array.from(state.selections.values())
      .filter(selection => selection.forkId === forkId);
  },

  setActiveSheetId: (sheetId) => {
    console.log('ChatForkStore: Setting active sheet ID:', sheetId);
    set({ activeSheetId: sheetId });
  }
})); 