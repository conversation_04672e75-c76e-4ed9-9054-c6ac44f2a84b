/**
 * Intent Types Configuration
 * 
 * This module provides access to the intent types configuration.
 * It loads the same intent types that are defined in the backend.
 */

// Intent type interface
export interface IntentType {
  id: string;
  displayName: string;
  description: string;
  requiresMindmap: boolean;
  requiresChatFork: boolean;
  requiresTemplate: boolean;
}

// Intent types from the backend configuration
// This should match the backend/config/intent_types.yaml file
export const intentTypes: IntentType[] = [
  {
    id: 'factual',
    displayName: 'Factual',
    description: 'A direct question with a clear, verifiable answer',
    requiresMindmap: false,
    requiresChatFork: false,
    requiresTemplate: false
  },
  {
    id: 'exploratory',
    displayName: 'Exploratory',
    description: 'A conceptual or open-ended question exploring a topic or idea',
    requiresMindmap: false,
    requiresChatFork: true,
    requiresTemplate: false
  },
  {
    id: 'teleological',
    displayName: 'Teleological',
    description: 'Structured planning to reach a goal, often involving mindmaps',
    requiresMindmap: true,
    requiresChatFork: false,
    requiresTemplate: false
  },
  {
    id: 'instantiation',
    displayName: 'Instantiation',
    description: 'Populating a known structure (e.g., SWOT, Business Model Canvas)',
    requiresMindmap: false,
    requiresChatFork: false,
    requiresTemplate: true
  },
  {
    id: 'situational',
    displayName: 'Situational',
    description: 'Solving a real-world business challenge involving interpersonal or stakeholder conflict',
    requiresMindmap: false,
    requiresChatFork: true,
    requiresTemplate: false
  },
  {
    id: 'miscellaneous',
    displayName: 'Miscellaneous',
    description: 'Prompts that don\'t clearly fit any other category',
    requiresMindmap: false,
    requiresChatFork: false,
    requiresTemplate: false
  }
];

/**
 * Get all intent types
 */
export const getAllIntentTypes = (): IntentType[] => {
  return intentTypes;
};

/**
 * Get an intent type by ID
 */
export const getIntentTypeById = (id: string): IntentType | undefined => {
  return intentTypes.find(intent => intent.id === id);
};

/**
 * Check if an intent type is valid
 */
export const isValidIntentType = (id: string): boolean => {
  return intentTypes.some(intent => intent.id === id);
};

/**
 * Get the response type configuration for an intent
 */
export const getResponseTypeForIntent = (intent: string): any => {
  const intentType = getIntentTypeById(intent);
  
  if (!intentType) {
    return {
      type: 'miscellaneous',
      requiresMindmap: false,
      requiresChatFork: false,
      requiresTemplate: false
    };
  }
  
  return {
    type: intentType.id,
    requiresMindmap: intentType.requiresMindmap,
    requiresChatFork: intentType.requiresChatFork,
    requiresTemplate: intentType.requiresTemplate
  };
};

export default {
  getAllIntentTypes,
  getIntentTypeById,
  isValidIntentType,
  getResponseTypeForIntent
};
