/**
 * MindMapStoreManager
 *
 * This manager creates and manages separate MindMapStore instances for each mindsheet.
 * It ensures that each mindsheet has its own isolated state.
 */

import { create } from 'zustand';
import { useMindMapStore, MindMapState } from './MindMapStore';

interface MindMapStoreManager {
  // Map of sheet ID to MindMapState
  storeStates: Record<string, MindMapState>;

  // Get the state for a specific sheet
  getState: (sheetId: string) => MindMapState;

  // Save the state for a specific sheet
  saveState: (sheetId: string, state: MindMapState) => void;

  // Clear the state for a specific sheet
  clearState: (sheetId: string) => void;

  // Apply a state to the global MindMapStore
  applyState: (sheetId: string) => void;

  // Save the current global state to a specific sheet
  saveCurrentState: (sheetId: string) => void;
}

// Create the store manager
export const useMindMapStoreManager = create<MindMapStoreManager>((set, get) => ({
  // Initial state - empty map of store states
  storeStates: {},

  // Get the state for a specific sheet
  getState: (sheetId: string) => {
    const { storeStates } = get();
    return storeStates[sheetId] || createEmptyState();
  },

  // Save the state for a specific sheet
  saveState: (sheetId: string, state: MindMapState) => {
    console.log('MindMapStoreManager: Saving state for sheet:', sheetId);
    set(prevState => ({
      storeStates: {
        ...prevState.storeStates,
        [sheetId]: { ...state }
      }
    }));
  },

  // Clear the state for a specific sheet
  clearState: (sheetId: string) => {
    console.log('MindMapStoreManager: Clearing state for sheet:', sheetId);
    set(prevState => {
      const newStoreStates = { ...prevState.storeStates };
      delete newStoreStates[sheetId];
      return { storeStates: newStoreStates };
    });
  },

  // Apply a state to the global MindMapStore
  applyState: (sheetId: string) => {
    console.log('MindMapStoreManager: Applying state for sheet:', sheetId);
    const state = get().getState(sheetId);

    // Apply the state to the global MindMapStore
    const mindMapStore = useMindMapStore.getState();

    // Only apply if we have a valid state with nodes
    if (state && Object.keys(state.nodes || {}).length > 0) {
      console.log('MindMapStoreManager: Found saved state with nodes:', Object.keys(state.nodes).length);

      // First reset the MindMapStore to ensure a clean state
      mindMapStore.initialize(window.innerWidth, window.innerHeight);

      // Then apply the saved state to the global MindMapStore
      mindMapStore.setState({
        nodes: state.nodes || {},
        connections: state.connections || [],
        rootNodeId: state.rootNodeId || null,
        position: state.position || { x: window.innerWidth / 2, y: window.innerHeight / 2 },
        scale: state.scale || 1.0,
        selectedNodeId: null,
        showProjectDialog: false,
        showMindMapManager: false,
        projectName: state.projectName || 'Untitled Project'
      });

      console.log('MindMapStoreManager: Successfully applied state for sheet:', sheetId);
      return true;
    } else {
      console.log('MindMapStoreManager: No saved state found for sheet:', sheetId);

      // Reset the global MindMapStore to a clean state
      mindMapStore.initialize(window.innerWidth, window.innerHeight);

      return false;
    }
  },

  // Save the current global state to a specific sheet
  saveCurrentState: (sheetId: string) => {
    console.log('MindMapStoreManager: Saving current state for sheet:', sheetId);

    // Get the current state from the global MindMapStore
    const mindMapStore = useMindMapStore.getState();

    // Check if there are any nodes to save
    if (Object.keys(mindMapStore.nodes || {}).length === 0) {
      console.log('MindMapStoreManager: No nodes to save for sheet:', sheetId);
      return;
    }

    console.log('MindMapStoreManager: Saving state with nodes:', Object.keys(mindMapStore.nodes).length);

    // Create a deep copy of the state to ensure no references are shared
    const stateCopy: MindMapState = {
      // Deep copy nodes
      nodes: JSON.parse(JSON.stringify(mindMapStore.nodes)),

      // Deep copy connections
      connections: JSON.parse(JSON.stringify(mindMapStore.connections)),

      // Copy primitive values
      rootNodeId: mindMapStore.rootNodeId,
      position: { ...mindMapStore.position },
      scale: mindMapStore.scale,
      selectedNodeId: null, // Don't save selection state
      selectedConnectionId: null, // Don't save selection state
      projectName: mindMapStore.projectName,
      showProjectDialog: false,
      showMindMapManager: false,
      llmModel: mindMapStore.llmModel,

      // Include all functions from the original state
      // These won't be used when restoring, but are needed for the type
      initialize: mindMapStore.initialize,
      createNewProject: mindMapStore.createNewProject,
      loadProject: mindMapStore.loadProject,
      saveProject: mindMapStore.saveProject,
      loadLastActiveProject: mindMapStore.loadLastActiveProject,
      addNode: mindMapStore.addNode,
      updateNode: mindMapStore.updateNode,
      deleteNode: mindMapStore.deleteNode,
      addConnection: mindMapStore.addConnection,
      updateConnection: mindMapStore.updateConnection,
      deleteConnection: mindMapStore.deleteConnection,
      selectNode: mindMapStore.selectNode,
      selectConnection: mindMapStore.selectConnection,
      setScale: mindMapStore.setScale,
      setPosition: mindMapStore.setPosition,
      setShowProjectDialog: mindMapStore.setShowProjectDialog,
      setShowMindMapManager: mindMapStore.setShowMindMapManager,
      setProjectName: mindMapStore.setProjectName,
      updateLayout: mindMapStore.updateLayout,
      setLlmModel: mindMapStore.setLlmModel,
      setState: mindMapStore.setState
    };

    // Save the state
    get().saveState(sheetId, stateCopy);
    console.log('MindMapStoreManager: Successfully saved state for sheet:', sheetId);
  }
}));

// Helper function to create an empty state
function createEmptyState(): MindMapState {
  // Get the global MindMapStore to copy its functions
  const globalStore = useMindMapStore.getState();

  // Create an empty state with the same functions
  return {
    nodes: {},
    connections: [],
    rootNodeId: null,
    position: { x: window.innerWidth / 2, y: window.innerHeight / 2 },
    scale: 1.0,
    selectedNodeId: null,
    selectedConnectionId: null,
    projectName: 'Untitled Project',
    showProjectDialog: false,
    showMindMapManager: false,
    llmModel: 'gpt-4o-mini',

    // Copy all functions from the global store
    initialize: globalStore.initialize,
    createNewProject: globalStore.createNewProject,
    loadProject: globalStore.loadProject,
    saveProject: globalStore.saveProject,
    loadLastActiveProject: globalStore.loadLastActiveProject,
    addNode: globalStore.addNode,
    updateNode: globalStore.updateNode,
    deleteNode: globalStore.deleteNode,
    addConnection: globalStore.addConnection,
    updateConnection: globalStore.updateConnection,
    deleteConnection: globalStore.deleteConnection,
    selectNode: globalStore.selectNode,
    selectConnection: globalStore.selectConnection,
    setScale: globalStore.setScale,
    setPosition: globalStore.setPosition,
    setShowProjectDialog: globalStore.setShowProjectDialog,
    setShowMindMapManager: globalStore.setShowMindMapManager,
    setProjectName: globalStore.setProjectName,
    updateLayout: globalStore.updateLayout,
    setLlmModel: globalStore.setLlmModel,
    setState: globalStore.setState
  };
}
