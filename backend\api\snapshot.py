from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Any, Dict, Optional
import json
import os
from datetime import datetime

router = APIRouter(prefix="/api/memory", tags=["memory"])

# Store snapshots under backend/snapshot
SNAPSHOT_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "snapshot")
os.makedirs(SNAPSHOT_DIR, exist_ok=True)

class MemorySnapshot(BaseModel):
    timestamp: str
    mindBook: Optional[Dict[str, Any]] = None
    contextSettings: Optional[Dict[str, Any]] = None
    chatMessages: Any = None
    chatMemory: Optional[Dict[str, Any]] = None
    localStorage: Optional[Dict[str, Any]] = None
    logs: Any = None

class SnapshotResponse(BaseModel):
    success: bool
    file: str

@router.post("/snapshot", response_model=SnapshotResponse)
async def save_memory_snapshot(snapshot: MemorySnapshot):
    """Save a memory snapshot sent from the frontend."""
    try:
        timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
        filename = f"{timestamp}_snapshot.json"
        path = os.path.join(SNAPSHOT_DIR, filename)
        with open(path, "w", encoding="utf-8") as f:
            json.dump(snapshot.dict(), f, indent=2)
        return SnapshotResponse(success=True, file=filename)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to save snapshot: {str(e)}")