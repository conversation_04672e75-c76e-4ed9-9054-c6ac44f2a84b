# Mindmap Numbering Fix

## Issue Description

The mindmap numbering system was using a timestamp-based approach to generate mindmap numbers, which resulted in inconsistent and sometimes high numbers (like "#7" for the second mindmap). This was confusing for users who expected sequential numbering starting from 1.

The issue was in the `GovernanceBoxPositioned.tsx` file, where the mindmap number was calculated using:

```javascript
const displayNumber = Math.floor((timestamp % 1000) / 100) + 1; // 1-10 range
```

This formula takes the last 3 digits of the timestamp, divides by 100, and adds 1, resulting in a number between 1 and 10. However, this approach doesn't guarantee sequential numbering and can lead to unexpected numbers.

## Solution

The solution was to change the numbering system to be based on the actual count of existing mindmaps in the MindBookStore. This ensures that mindmaps are numbered sequentially starting from 1.

The changes were made in three places in the `GovernanceBoxPositioned.tsx` file:

1. For teleological mindmaps:
```javascript
// For display purposes, we'll use a sequential number based on the number of existing sheets
// Get the current sheets and count how many mindmaps we already have
const existingSheets = mindBookStore.sheets;
const mindmapCount = existingSheets.filter(sheet => 
  sheet.contentType === MindSheetContentType.MINDMAP
).length;
const displayNumber = mindmapCount + 1; // Start from 1
```

2. For regular mindmaps:
```javascript
// For display purposes, we'll use a sequential number based on the number of existing sheets
// Get the current sheets and count how many mindmaps we already have
const existingSheets = mindBookStore.sheets;
const mindmapCount = existingSheets.filter(sheet => 
  sheet.contentType === MindSheetContentType.MINDMAP
).length;
const displayNumber = mindmapCount + 1; // Start from 1
```

3. For chatforks:
```javascript
// For display purposes, we'll use a sequential number based on the number of existing sheets
// Get the current sheets and count how many chatforks we already have
const existingSheets = mindBookStore.sheets;
const chatforkCount = existingSheets.filter(sheet => 
  sheet.contentType === MindSheetContentType.CHATFORK
).length;
const displayNumber = chatforkCount + 1; // Start from 1
```

Additionally, we fixed a variable name issue where `uniqueTitle` was undefined in the teleological mindmap creation code.

## Benefits

1. **Sequential Numbering**: Mindmaps are now numbered sequentially (1, 2, 3, ...) based on the actual count of existing mindmaps.
2. **Separate Counters**: Each type of sheet (mindmap, teleological mindmap, chatfork) has its own counter, ensuring that each type starts from 1.
3. **Consistent User Experience**: Users will now see predictable numbering that matches their expectations.

## Implementation Details

The implementation relies on the `MindBookStore` which maintains a list of all sheets in the application. We filter this list to count only the sheets of the specific type we're creating, and then add 1 to get the next number in the sequence.

This approach ensures that even if sheets are deleted, the numbering will still be sequential based on the current state of the application.

## Testing

To test this fix:
1. Create a new mindmap using the "mindmap" intention
2. Verify that it's labeled as "Mindmap 1"
3. Create another mindmap
4. Verify that it's labeled as "Mindmap 2"
5. Repeat for teleological mindmaps and chatforks

## Future Improvements

In the future, we might want to consider:

1. **Persistent Numbering**: Store the last used number for each sheet type in localStorage to ensure numbering persists across sessions.
2. **Renumbering on Delete**: Implement a renumbering system when sheets are deleted to maintain a clean sequence.
3. **Custom Naming**: Allow users to rename their mindmaps while preserving the numbering system.

## Related Components

- `GovernanceBoxPositioned.tsx`: The main component that creates mindmaps and chatforks
- `MindBookStore.ts`: The store that manages all sheets in the application
- `MindSheetContentType`: The enum that defines the different types of sheets
