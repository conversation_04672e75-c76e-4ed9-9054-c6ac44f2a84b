/**
 * NodeBox.tsx
 *
 * Main component for the NodeBox dialog that appears when a node is selected.
 * This component displays node details and provides tabs for different agent interactions.
 */

import React, { useState, useEffect, useRef } from 'react';
import { useMindMapStore } from '../../../../core/state/MindMapStore';
import { getMindMapStore, getActiveMindMapSheetId } from '../../../../core/state/MindMapStoreFactory';
import RegistrationManager, { EventType } from '../../../../core/services/RegistrationManager';
import { Rnd } from 'react-rnd';
import './NodeBox.css';

// Tab types
type TabType = 'white' | 'red' | 'black' | 'yellow' | 'green' | 'data' | 'connections';

const NodeBox: React.FC = () => {
  console.log('NodeBox DEBUG: Component initialized');
  // Store reference to the active sheet's store
  const storeRef = useRef<any>(null);

  // Local state
  const [activeTab, setActiveTab] = useState<TabType>('white');
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [nodes, setNodes] = useState<Record<string, any>>({});
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);

  // State for position and size
  const [position, setPosition] = useState({ x: window.innerWidth / 2 - 450, y: window.innerHeight / 2 - 300 });
  const [size, setSize] = useState({ width: 900, height: 600 });

  // Get the active sheet ID and set up event listeners
  useEffect(() => {
    try {
      console.log('NodeBox DEBUG: Initializing NodeBox component');

      const activeSheetId = getActiveMindMapSheetId();
      console.log('NodeBox: Active sheet ID:', activeSheetId);

      // Add more debug logging
      console.log('NodeBox DEBUG: Checking active sheet ID from DOM');
      const activeSheetElement = document.querySelector('.mind-sheet.active');
      const domSheetId = activeSheetElement?.getAttribute('data-sheet-id');
      console.log('NodeBox DEBUG: Active sheet ID from DOM:', domSheetId);

      // If we don't have an active sheet ID from the factory, try to get it from the DOM
      const effectiveSheetId = activeSheetId || domSheetId;

      // Log all mind-sheet elements for debugging
      const allSheets = document.querySelectorAll('.mind-sheet');
      console.log('NodeBox DEBUG: Found', allSheets.length, 'mind-sheet elements');
      allSheets.forEach((sheet, index) => {
        const sheetId = sheet.getAttribute('data-sheet-id');
        const isActive = sheet.classList.contains('active');
        console.log(`NodeBox DEBUG: Sheet ${index}: ID=${sheetId}, active=${isActive}`);
      });

      if (effectiveSheetId) {
        console.log('NodeBox DEBUG: Using effective sheet ID:', effectiveSheetId);

        // Get the sheet-specific store
        storeRef.current = getMindMapStore(effectiveSheetId);
        console.log('NodeBox: Got sheet-specific store for sheet:', effectiveSheetId);

        // Get initial state from the store
        const storeState = storeRef.current.getState();
        console.log('NodeBox DEBUG: Store state:', {
          nodeCount: Object.keys(storeState.nodes || {}).length,
          selectedNodeId: storeState.selectedNodeId
        });

        setNodes(storeState.nodes || {});
        setSelectedNodeId(storeState.selectedNodeId);

        // Subscribe to store changes
        const unsubscribe = storeRef.current.subscribe(
          state => ({ nodes: state.nodes, selectedNodeId: state.selectedNodeId }),
          ({ nodes, selectedNodeId }) => {
            console.log('NodeBox DEBUG: Store updated:', {
              nodeCount: Object.keys(nodes || {}).length,
              selectedNodeId
            });
            setNodes(nodes || {});
            setSelectedNodeId(selectedNodeId);
          }
        );

        // Set up event listener for direct NodeBox opening
        const handleOpenNodeBox = (event: CustomEvent) => {
          console.log('NodeBox DEBUG: Received mindback:open_nodebox event:', event.detail);
          
          try {
            // Get the current sheet ID from DOM more reliably
            const latestDomSheetId = document.body.getAttribute('data-current-sheet-id');
            console.log('NodeBox DEBUG: Latest DOM sheet ID:', latestDomSheetId);
            console.log('NodeBox DEBUG: Event sheet ID:', event.detail.sheetId);
            console.log('NodeBox DEBUG: Effective sheet ID:', effectiveSheetId);

            // Use the latest sheet ID or fall back to the one we already have
            const currentSheetId = latestDomSheetId || effectiveSheetId;
            console.log('NodeBox DEBUG: Using current sheet ID:', currentSheetId);

            // Check if this event is for our sheet
            if (event.detail.sheetId === currentSheetId || !event.detail.sheetId) {
              const nodeId = event.detail.nodeId;
              console.log('NodeBox DEBUG: Event is for our sheet, node ID:', nodeId);

              // Get the latest store for the current sheet
              const currentStore = getMindMapStore(currentSheetId);
              storeRef.current = currentStore;
              console.log('NodeBox DEBUG: Got store for sheet:', currentSheetId);

              // Make sure the node exists in our state
              if (storeRef.current && storeRef.current.getState().nodes[nodeId]) {
                console.log('NodeBox DEBUG: Opening NodeBox for node:', nodeId);

                // Force the node to be selected
                storeRef.current.getState().selectNode(nodeId);
                console.log('NodeBox DEBUG: Selected node in store');

                // Set the isEditing flag directly
                storeRef.current.getState().updateNode(nodeId, {
                  metadata: {
                    ...storeRef.current.getState().nodes[nodeId].metadata,
                    isEditing: true
                  }
                });
                console.log('NodeBox DEBUG: Set isEditing flag on node');

                // Force the NodeBox to open
                setIsOpen(true);
                console.log('NodeBox DEBUG: Set isOpen to true');

                // Update our local state
                setSelectedNodeId(nodeId);
                setNodes(storeRef.current.getState().nodes);
                console.log('NodeBox DEBUG: Updated local state');

                // Log the node data
                console.log('NodeBox DEBUG: Node data:', storeRef.current.getState().nodes[nodeId]);

                // Force a re-render
                setTimeout(() => {
                  console.log('NodeBox DEBUG: Forcing re-render with timeout');
                  setIsOpen(true);
                }, 10);
              } else {
                console.error('NodeBox DEBUG: Node not found in store:', nodeId);
                console.log('NodeBox DEBUG: Available nodes:', Object.keys(storeRef.current?.getState().nodes || {}));
              }
            } else {
              console.log('NodeBox DEBUG: Event is not for our sheet. Event sheet:', event.detail.sheetId, 'Our sheet:', currentSheetId);
            }
          } catch (error) {
            console.error('NodeBox DEBUG: Error in handleOpenNodeBox:', error);
          }
        };

        // Add event listener
        document.addEventListener('mindback:open_nodebox', handleOpenNodeBox as EventListener);

        return () => {
          if (unsubscribe) unsubscribe();
          document.removeEventListener('mindback:open_nodebox', handleOpenNodeBox as EventListener);
        };
      } else {
        console.error('NodeBox DEBUG: No sheet ID available from any source');
      }
    } catch (error) {
      console.error('NodeBox: Error getting active sheet store:', error);
    }
  }, []);

  // Helper functions to access store methods
  const selectNode = (id: string | null) => {
    if (storeRef.current) {
      storeRef.current.getState().selectNode(id);
    }
  };

  const updateNode = (id: string, updates: any) => {
    if (storeRef.current) {
      storeRef.current.getState().updateNode(id, updates);
    }
  };

  // Get the selected node
  const selectedNode = selectedNodeId ? nodes[selectedNodeId] : null;
  console.log('NodeBox: selectedNodeId:', selectedNodeId, 'selectedNode:', selectedNode);

  // Force update title and description whenever selectedNode changes
  useEffect(() => {
    if (selectedNode) {
      console.log('NodeBox: Force updating title from node text:', selectedNode.text || '');

      // Get the latest node data from our local state
      // Remove any index prefix from the title (e.g., "1. New Mindmap" -> "New Mindmap")
      const text = selectedNode.text || '';
      const cleanTitle = text.replace(/^\d+(\.\d+)*\.?\s+/, '');
      setTitle(cleanTitle);
      setDescription(selectedNode.description || '');

      console.log('NodeBox: Updated title to:', cleanTitle);
    }
  }, [selectedNode?.id, selectedNode?.text]);

  // Removed redundant effect that runs on every render

  // Update local state when selected node changes
  useEffect(() => {
    // First check if we have a valid selectedNode
    if (!selectedNode) {
      console.log('NodeBox: No selected node, closing');
      setIsOpen(false);
      return;
    }

    try {
      // Add more debug logging
      console.log('NodeBox DEBUG: Selected node details:', {
        id: selectedNode.id,
        text: selectedNode.text,
        hasMetadata: !!selectedNode.metadata,
        isEditing: selectedNode.metadata?.isEditing
      });

      // Check the node's metadata for isEditing flag
      console.log('NodeBox: Node metadata:', selectedNode.metadata);

      // Only open the NodeBox if the node has the isEditing flag set to true
      // This ensures it only opens on double-click, not on single-click selection
      if (selectedNode.metadata?.isEditing) {
        console.log('NodeBox: Opening for node', selectedNode.id || 'unknown', 'because isEditing flag is set');

        // Use a small timeout to prevent the blinking issue
        const timer = setTimeout(() => {
          try {
            // Open the NodeBox
            console.log('NodeBox DEBUG: Setting isOpen to true');
            setIsOpen(true);

            // Register the node opened event
            RegistrationManager.registerEvent(EventType.NODE_OPENED, {
              id: selectedNode.id,
              text: selectedNode.text
            });

            // Ensure the NodeBox is visible and on top
            setTimeout(() => {
              try {
                // Force the NodeBox to be visible
                const nodeboxContainer = document.querySelector('.nodebox-container');
                if (nodeboxContainer) {
                  (nodeboxContainer as HTMLElement).style.display = 'block';
                  (nodeboxContainer as HTMLElement).style.visibility = 'visible';
                  console.log('NodeBox DEBUG: Forced container visibility');
                }

                const nodeboxOverlay = document.querySelector('.nodebox-overlay');
                if (nodeboxOverlay) {
                  console.log('NodeBox: Setting z-index to node-dialog value');
                  // Get the computed value of the CSS variable
                  const nodeDialogZIndex = getComputedStyle(document.documentElement).getPropertyValue('--z-index-node-dialog').trim();
                  (nodeboxOverlay as HTMLElement).style.zIndex = nodeDialogZIndex || '2100';
                  (nodeboxOverlay as HTMLElement).style.display = 'block';
                  (nodeboxOverlay as HTMLElement).style.visibility = 'visible';
                  console.log('NodeBox DEBUG: Set z-index to:', nodeDialogZIndex || '2100');
                } else {
                  console.error('NodeBox DEBUG: Could not find nodebox-overlay element');
                }

                // Dispatch an event to notify that the NodeBox is open
                const event = new CustomEvent('mindback:nodebox_opened', {
                  detail: {
                    nodeId: selectedNode.id
                  }
                });
                document.dispatchEvent(event);
                console.log('NodeBox DEBUG: Dispatched nodebox_opened event');
              } catch (error) {
                console.error('NodeBox: Error setting z-index:', error);
              }
            }, 10);
          } catch (error) {
            console.error('NodeBox: Error opening node:', error);
          }
        }, 50);

        return () => clearTimeout(timer);
      } else {
        console.log('NodeBox: Not opening for node', selectedNode.id, 'because isEditing flag is not set');
        // If the node doesn't have isEditing flag, ensure the NodeBox is closed
        setIsOpen(false);
      }
    } catch (error) {
      console.error('NodeBox: Error in useEffect:', error);
      setIsOpen(false);
    }
  }, [selectedNode]);

  // If no selected node OR not open, don't render anything
  if (!selectedNode || !isOpen) {
    console.log('NodeBox: Not rendering - selectedNode:', !!selectedNode, 'isOpen:', isOpen);
    return null;
  }

  // Handle close - only close when the close button is clicked or overlay is clicked
  const handleClose = (e?: React.MouseEvent) => {
    // If no event is provided, it's a programmatic close
    // If event is provided, check if it's a click on the overlay (not on the NodeBox itself)
    if (!e || (e.currentTarget === e.target)) {
      // Stop event propagation to prevent it from reaching other elements
      if (e) {
        e.stopPropagation();
        e.preventDefault();
      }

      // Clear isEditing flag before closing
      if (selectedNode) {
        console.log('NodeBox: Clearing isEditing flag for node:', selectedNode.id);

        // Clear the isEditing flag in the node's metadata
        updateNode(selectedNode.id, {
          metadata: {
            ...selectedNode.metadata,
            isEditing: false
          }
        });

        // Find the node element in the DOM and make it draggable again
        setTimeout(() => {
          const nodeGroup = document.querySelector(`[data-node-id="${selectedNode.id}"]`);
          if (nodeGroup) {
            (nodeGroup as HTMLElement).setAttribute('draggable', 'true');
            console.log('NodeBox: Re-enabled dragging for node:', selectedNode.id);
          }
        }, 100);
      }

      // Keep the node selected but close the NodeBox
      setIsOpen(false);

      // Register the node closing event
      if (selectedNode && selectedNode.id) {
        RegistrationManager.registerEvent(EventType.NODE_CLOSED, { id: selectedNode.id });
      }

      console.log('NodeBox: Closed and cleared isEditing flags');
    }
  };

  // Handle title change
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value || '';
    console.log('NodeBox: Title changed to:', newValue);

    // First update local state
    setTitle(newValue);

    // Then update the node in the store
    if (selectedNodeId && storeRef.current) {
      // Update the node text without adding any prefix
      // The prefix is now handled separately via the nodePath metadata
      updateNode(selectedNodeId, {
        text: newValue
      });

      console.log('NodeBox: Updated node title in store to:', newValue);
    }
  };

  // Handle title blur (when user finishes editing)
  const handleTitleBlur = () => {
    if (selectedNodeId) {
      // Register the node edit event when the user finishes editing
      RegistrationManager.registerEvent(EventType.NODE_EDITED, { id: selectedNodeId });
    }
  };

  // Handle description change
  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setDescription(e.target.value);

    // Save the description immediately to update the node
    if (selectedNodeId && storeRef.current) {
      updateNode(selectedNodeId, {
        description: e.target.value
      });

      console.log('NodeBox: Updated node description in store');
    }
  };

  // Handle description blur (when user finishes editing)
  const handleDescriptionBlur = () => {
    if (selectedNodeId) {
      // Register the node edit event when the user finishes editing
      RegistrationManager.registerEvent(EventType.NODE_EDITED, { id: selectedNodeId });
    }
  };

  // Handle save - now only used for explicit save actions
  const handleSave = () => {
    if (selectedNodeId && storeRef.current) {
      // Update the node text without preserving any prefix
      updateNode(selectedNodeId, {
        text: title,
        description: description
      });

      console.log('NodeBox: Saved node changes to store');

      // Register the node edit event
      RegistrationManager.registerEvent(EventType.NODE_EDITED, { id: selectedNodeId });
    }
  };

  // Handle tab change
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);

    // Register the agent selection event
    if (selectedNode) {
      // Map tab types to agent names
      const agentNames: Record<TabType, string> = {
        'white': 'White Hat Agent',
        'red': 'Red Hat Agent',
        'black': 'Black Hat Agent',
        'yellow': 'Yellow Hat Agent',
        'green': 'Green Hat Agent',
        'data': 'Data Agent',
        'connections': 'Connections Agent'
      };

      // Event registration is temporarily disabled
      // Just log the event for debugging
      console.log(`[NodeBox] Would register agent selection: ${agentNames[tab]} for node ${selectedNode.id || 'unknown'}`);
    }
  };

  console.log('NodeBox DEBUG: Rendering NodeBox for node:', selectedNode.id, 'with title:', selectedNode.text);

  // Log the DOM structure to help debug
  setTimeout(() => {
    const nodeboxContainer = document.querySelector('.nodebox-container');
    if (nodeboxContainer) {
      console.log('NodeBox DEBUG: Found nodebox-container in DOM');
    } else {
      console.error('NodeBox DEBUG: Could not find nodebox-container in DOM');
    }
  }, 0);

  // Get node path (index) from metadata
  const nodePath = selectedNode.metadata?.nodePath || '';

  return (
    <div className="nodebox-container">
      <div className="nodebox-overlay" onClick={handleClose}>
      <Rnd
        position={position}
        size={size}
        onDragStop={(e, d) => setPosition({ x: d.x, y: d.y })}
        onResizeStop={(e, direction, ref, delta, position) => {
          setSize({
            width: parseInt(ref.style.width),
            height: parseInt(ref.style.height)
          });
          setPosition(position);
        }}
        minWidth={400}
        minHeight={300}
        bounds="window"
        className="nodebox"
        style={{ border: 'none' }} // Ensure no border is applied
        dragHandleClassName="nodebox-header"
        onClick={(e) => {
          // Prevent clicks on the NodeBox from closing it
          e.stopPropagation();
        }}
        resizeHandleClasses={{
          bottom: 'resize-handle-bottom',
          bottomRight: 'resize-handle-corner',
          bottomLeft: 'resize-handle-corner',
          right: 'resize-handle-right',
          left: 'resize-handle-left',
          top: 'resize-handle-top',
          topRight: 'resize-handle-corner',
          topLeft: 'resize-handle-corner'
        }}
      >
        {/* Header - styled like governance box */}
        <div className="nodebox-header">
          <div className="nodebox-header-left">
            <img
              src="./Public/Logo/MB_logo.jpg"
              alt="MindBack Logo"
              className="nodebox-header-logo"
              width="24"
              height="24"
              draggable="false"
              onError={(e) => {
                console.error('Failed to load NodeBox logo');
                e.currentTarget.src = "./Public/Logo/mindback_logo.jpg";
              }}
            />
            <span className="nodebox-index">{nodePath}</span>
            <span className="nodebox-id">ID: {selectedNodeId.substring(0, 8)}</span>
          </div>
          <div className="nodebox-header-buttons">
            <button
              className="nodebox-reposition-button"
              onClick={(e) => {
                e.stopPropagation();
                // Center the nodebox on the screen
                const overlay = document.querySelector('.nodebox-overlay');
                if (overlay) {
                  const box = overlay.querySelector('.nodebox');
                  if (box) {
                    box.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' });
                  }
                }
              }}
              title="Center Position"
            >
              ↻
            </button>
            <button
              className="nodebox-resize-button"
              onClick={(e) => {
                e.stopPropagation();
                // Reset the nodebox size
                const overlay = document.querySelector('.nodebox-overlay');
                if (overlay) {
                  const box = overlay.querySelector('.nodebox') as HTMLElement;
                  if (box) {
                    box.style.width = '80%';
                    box.style.maxWidth = '900px';
                  }
                }
              }}
              title="Reset Size"
            >
              ⤢
            </button>
            <button className="nodebox-close-button" onClick={handleClose} title="Close">×</button>
          </div>
        </div>

        {/* Title */}
        <div className="nodebox-title-section">
          <label htmlFor="node-title">Node Title:</label>
          <input
            id="node-title"
            type="text"
            value={title}
            onChange={handleTitleChange}
            onBlur={handleTitleBlur}
            className="nodebox-title-input"
          />
        </div>

        {/* Description */}
        <div className="nodebox-description-section">
          <label htmlFor="node-description">Node Description:</label>
          <textarea
            id="node-description"
            value={description}
            onChange={handleDescriptionChange}
            onBlur={handleDescriptionBlur}
            className="nodebox-description-textarea"
            rows={4}
          />
        </div>

        {/* Tabs */}
        <div className="nodebox-tabs">
          <button
            className={`nodebox-tab ${activeTab === 'white' ? 'active' : ''}`}
            onClick={() => handleTabChange('white')}
          >
            White Agent
          </button>
          <button
            className={`nodebox-tab ${activeTab === 'red' ? 'active' : ''}`}
            onClick={() => handleTabChange('red')}
          >
            Red Agent
          </button>
          <button
            className={`nodebox-tab ${activeTab === 'black' ? 'active' : ''}`}
            onClick={() => handleTabChange('black')}
          >
            Black Agent
          </button>
          <button
            className={`nodebox-tab ${activeTab === 'yellow' ? 'active' : ''}`}
            onClick={() => handleTabChange('yellow')}
          >
            Yellow Agent
          </button>
          <button
            className={`nodebox-tab ${activeTab === 'green' ? 'active' : ''}`}
            onClick={() => handleTabChange('green')}
          >
            Green Agent
          </button>
          <button
            className={`nodebox-tab ${activeTab === 'data' ? 'active' : ''}`}
            onClick={() => handleTabChange('data')}
          >
            Data
          </button>
          <button
            className={`nodebox-tab ${activeTab === 'connections' ? 'active' : ''}`}
            onClick={() => handleTabChange('connections')}
          >
            Connections
          </button>
        </div>

        {/* Tab Content */}
        <div className="nodebox-tab-content">
          {activeTab === 'white' && (
            <div className="agent-tab-content">
              <h3>White Agent (Facts): Information and data known or needed</h3>
              <div className="agent-content">
                <p>This is where factual information about the topic will be displayed.</p>
                <textarea
                  className="agent-input"
                  placeholder="Enter factual information here..."
                />
              </div>
            </div>
          )}

          {activeTab === 'red' && (
            <div className="agent-tab-content">
              <h3>Red Agent (Emotions): Feelings and intuitions about the topic</h3>
              <div className="agent-content">
                <p>This is where emotional responses to the topic will be displayed.</p>
                <textarea
                  className="agent-input"
                  placeholder="Enter emotional responses here..."
                />
              </div>
            </div>
          )}

          {activeTab === 'black' && (
            <div className="agent-tab-content">
              <h3>Black Agent (Caution): Risks, problems, and concerns</h3>
              <div className="agent-content">
                <p>This is where risks and concerns about the topic will be displayed.</p>
                <textarea
                  className="agent-input"
                  placeholder="Enter risks and concerns here..."
                />
              </div>
            </div>
          )}

          {activeTab === 'yellow' && (
            <div className="agent-tab-content">
              <h3>Yellow Agent (Optimism): Benefits and opportunities</h3>
              <div className="agent-content">
                <p>This is where benefits and opportunities will be displayed.</p>
                <textarea
                  className="agent-input"
                  placeholder="Enter benefits and opportunities here..."
                />
              </div>
            </div>
          )}

          {activeTab === 'green' && (
            <div className="agent-tab-content">
              <h3>Green Agent (Creativity): New ideas and alternatives</h3>
              <div className="agent-content">
                <p>This is where creative ideas and alternatives will be displayed.</p>
                <textarea
                  className="agent-input"
                  placeholder="Enter creative ideas here..."
                />
              </div>
            </div>
          )}

          {activeTab === 'data' && (
            <div className="agent-tab-content">
              <h3>Data: Stored information and resources</h3>
              <div className="agent-content">
                <p>This is where data and resources will be displayed.</p>
                <div className="data-section">
                  <h4>Attached Files</h4>
                  <button className="data-button">Upload File</button>
                  <ul className="data-list">
                    <li>No files attached</li>
                  </ul>
                </div>
                <div className="data-section">
                  <h4>External Links</h4>
                  <div className="link-input-group">
                    <input type="text" placeholder="Enter URL..." />
                    <button className="data-button">Add Link</button>
                  </div>
                  <ul className="data-list">
                    <li>No links added</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'connections' && (
            <div className="agent-tab-content">
              <h3>Connections: Links to other mindsheets</h3>
              <div className="agent-content">
                <p>This is where connections to other mindsheets will be displayed.</p>
                <div className="connections-section">
                  <h4>Connected Mindsheets</h4>
                  <button className="connection-button">Create Connection</button>
                  <ul className="connections-list">
                    <li>No connections yet</li>
                  </ul>
                </div>
                <div className="connections-section">
                  <h4>Create New Mindsheet</h4>
                  <div className="new-mindsheet-group">
                    <select>
                      <option value="swot">SWOT Analysis</option>
                      <option value="pestel">PESTEL Analysis</option>
                      <option value="custom">Custom Mindsheet</option>
                    </select>
                    <button className="connection-button">Create</button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </Rnd>
      </div>
    </div>
  );
};

export default NodeBox;
