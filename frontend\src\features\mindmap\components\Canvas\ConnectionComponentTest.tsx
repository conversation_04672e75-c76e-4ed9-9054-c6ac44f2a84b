/**
 * ConnectionComponentTest.tsx
 *
 * Test file to verify that the displayInExternalStore function works.
 */

import React from 'react';
import { displayInExternalStore } from './ConnectionComponent';

const ConnectionComponentTest: React.FC = () => {
  // Test the displayInExternalStore function
  const testData = { test: 'data' };
  const result = displayInExternalStore(testData);

  return (
    <div>
      <h1>ConnectionComponent Test</h1>
      <p>Testing displayInExternalStore function</p>
      <p>Result: {JSON.stringify(result)}</p>
    </div>
  );
};

export default ConnectionComponentTest;
