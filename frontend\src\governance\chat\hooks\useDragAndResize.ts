import { useState, useEffect, useCallback } from 'react';
import { Position, Dimensions, DragOffset } from '../types';

const DEFAULT_WIDTH = 800; // Match the width from MuiPaper-root
const DEFAULT_HEIGHT = 600; // Match the height from MuiPaper-root
const MIN_WIDTH = 400;
const MIN_HEIGHT = 200;
const MIN_VISIBLE_WIDTH = 100;
const MIN_VISIBLE_HEIGHT = 40;
const STORAGE_KEY = 'governanceChatDialog';

interface StoredState {
  position: Position;
  dimensions: Dimensions;
  isCollapsed: boolean;
}

interface DragAndResizeState {
  position: Position;
  dimensions: Dimensions;
  isCollapsed: boolean;
  isMindmapMode: boolean;
}

const calculateCenterPosition = (width: number, height: number): Position => {
  return {
    x: Math.max(0, Math.floor((window.innerWidth - width) / 2)),
    y: Math.max(0, Math.floor((window.innerHeight - height) / 2))
  };
};

export function useDragAndResize(initialHeight: number = 600) {
  const [state, setState] = useState<DragAndResizeState>({
    position: { x: window.innerWidth - 400, y: 100 },
    dimensions: { width: 380, height: initialHeight },
    isCollapsed: false,
    isMindmapMode: false
  });

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    const target = e.target as HTMLElement;
    if (!target.classList.contains('resize-handle') && !target.classList.contains('dialog-header')) {
      return;
    }

    const startX = e.clientX;
    const startY = e.clientY;
    const startWidth = state.dimensions.width;
    const startHeight = state.dimensions.height;
    const startLeft = state.position.x;
    const startTop = state.position.y;
    const isResize = target.classList.contains('resize-handle');

    const handleMouseMove = (moveEvent: MouseEvent) => {
      if (isResize) {
        const newWidth = Math.max(300, startWidth + moveEvent.clientX - startX);
        const newHeight = Math.max(300, startHeight + moveEvent.clientY - startY);
        setState(prev => ({
          ...prev,
          dimensions: { width: newWidth, height: newHeight }
        }));
      } else {
        const newX = Math.max(0, Math.min(window.innerWidth - state.dimensions.width, startLeft + moveEvent.clientX - startX));
        const newY = Math.max(0, Math.min(window.innerHeight - state.dimensions.height, startTop + moveEvent.clientY - startY));
        setState(prev => ({
          ...prev,
          position: { x: newX, y: newY }
        }));
      }
    };

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [state.dimensions.width, state.dimensions.height, state.position.x, state.position.y]);

  const handleMinimize = useCallback(() => {
    setState(prev => ({ ...prev, isCollapsed: !prev.isCollapsed }));
  }, []);

  const resetPosition = useCallback(() => {
    setState(prev => ({
      ...prev,
      position: { x: window.innerWidth - 400, y: 100 }
    }));
  }, []);

  const resetSize = useCallback(() => {
    setState(prev => ({
      ...prev,
      dimensions: { width: 380, height: initialHeight }
    }));
  }, [initialHeight]);

  const enterMindmapMode = useCallback(() => {
    const windowHeight = window.innerHeight;
    setState(prev => ({
      ...prev,
      isMindmapMode: true,
      dimensions: { width: prev.dimensions.width, height: 300 },
      position: { ...prev.position, y: windowHeight - 300 }
    }));
  }, []);

  const exitMindmapMode = useCallback(() => {
    setState(prev => ({
      ...prev,
      isMindmapMode: false,
      dimensions: { width: prev.dimensions.width, height: initialHeight },
      position: { x: window.innerWidth - 400, y: 100 }
    }));
  }, [initialHeight]);

  const style = {
    width: `${state.dimensions.width}px`,
    height: state.isCollapsed ? '40px' : `${state.dimensions.height}px`,
    transform: `translate(${state.position.x}px, ${state.position.y}px)`,
    transition: 'height 0.3s ease-in-out'
  };

  return {
    position: state.position,
    dimensions: state.dimensions,
    isCollapsed: state.isCollapsed,
    isMindmapMode: state.isMindmapMode,
    handleMouseDown,
    handleMinimize,
    resetPosition,
    resetSize,
    enterMindmapMode,
    exitMindmapMode,
    style
  };
} 