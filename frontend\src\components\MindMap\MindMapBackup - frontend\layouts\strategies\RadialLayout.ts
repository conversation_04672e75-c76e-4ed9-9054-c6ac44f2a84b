import { Connection } from '../../core/models/Connection';
import { Node } from '../../core/models/Node';
import { DEFAULT_LAYOUT_CONFIG, LayoutConfig, LayoutStrategy, LayoutStrategyType } from '../types';
import { adjustOverlappingNodes, buildNodeLevels, getTreeDepth } from '../utils';

/**
 * Radial layout strategy for hierarchical trees
 * Places nodes in a circular pattern around the root node
 */
export class RadialLayout implements LayoutStrategy {
  readonly name: LayoutStrategyType = 'radial';

  calculateLayout(
    nodes: Record<string, Node>,
    connections: Connection[],
    rootId: string,
    config: LayoutConfig = DEFAULT_LAYOUT_CONFIG
  ): Record<string, Node> {
    if (!nodes[rootId]) {
      console.warn('Root node not found:', rootId);
      return nodes;
    }

    // Build levels through BFS
    const levels = buildNodeLevels(nodes, connections, rootId);
    
    // Copy nodes to avoid mutating the original
    const updatedNodes = { ...nodes };
    
    // Place root node at center
    if (updatedNodes[rootId]) {
      updatedNodes[rootId] = {
        ...updatedNodes[rootId],
        x: 0,
        y: 0
      };
    }
    
    // Calculate the total number of levels for radius calculation
    const treeDepth = getTreeDepth(levels);
    
    // Skip root level (index 0) and position nodes in each level
    for (let levelIndex = 1; levelIndex < levels.length; levelIndex++) {
      const level = levels[levelIndex];
      const levelNodes = level.nodes;
      const nodeCount = levelNodes.length;
      
      // Calculate radius for this level based on level index
      const radius = levelIndex * config.levelSpacing;
      
      // Position nodes in a circle at this level
      levelNodes.forEach((nodeId, nodeIndex) => {
        const node = updatedNodes[nodeId];
        if (!node) return;
        
        // Calculate angle for this node - distribute nodes evenly around the circle
        const angle = (2 * Math.PI * nodeIndex) / nodeCount;
        
        // Calculate position using polar coordinates
        const x = radius * Math.cos(angle);
        const y = radius * Math.sin(angle);
        
        // Update node position
        updatedNodes[nodeId] = {
          ...node,
          x,
          y
        };
      });
    }

    // Apply overlap adjustment with reduced constraints for radial layout
    return adjustOverlappingNodes(updatedNodes, connections, config, false);
  }
} 