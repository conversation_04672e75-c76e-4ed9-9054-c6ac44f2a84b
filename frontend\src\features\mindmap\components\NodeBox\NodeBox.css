/**
 * NodeBox.css
 *
 * Styles for the NodeBox component.
 */

/* Container */
.nodebox-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: var(--z-index-node-dialog); /* Using CSS variable for z-index */
}

/* Add an overlay class to prevent interaction with elements underneath */
.nodebox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.1);
  pointer-events: auto; /* This ensures clicks don't go through to elements underneath */
  z-index: var(--z-index-node-dialog);
}

/* Resize handles */
.resize-handle-bottom,
.resize-handle-corner,
.resize-handle-right,
.resize-handle-left,
.resize-handle-top {
  position: absolute;
  background-color: transparent; /* Remove the blue color */
  border-radius: 4px;
  pointer-events: auto;
}

.resize-handle-bottom {
  bottom: 0;
  left: 0;
  width: 100%;
  height: 8px;
  cursor: ns-resize;
}

.resize-handle-right {
  top: 0;
  right: 0;
  width: 8px;
  height: 100%;
  cursor: ew-resize;
}

.resize-handle-corner {
  bottom: 0;
  right: 0;
  width: 16px;
  height: 16px;
  cursor: nwse-resize;
}

/* Main NodeBox container */
.nodebox {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  pointer-events: auto;
}

/* Header - styled like governance box */
.nodebox-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #000;
  color: white;
  cursor: move;
  user-select: none;
  height: 40px;
  border-radius: 8px 8px 0 0;
}

.nodebox-header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nodebox-index {
  font-weight: bold;
  color: #ffffff;
}

.nodebox-id {
  color: #e0e0e0;
  font-size: 0.9em;
}

.nodebox-close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #ffffff;
}

.nodebox-close-button:hover {
  color: #f0f0f0;
}

/* Logo styling */
.nodebox-header-logo {
  height: 24px;
  width: 24px;
  object-fit: contain;
  border-radius: 4px;
}

/* Header buttons container */
.nodebox-header-buttons {
  display: flex;
  gap: 4px;
}

/* Add resize and reposition buttons */
.nodebox-resize-button,
.nodebox-reposition-button {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #ffffff;
  padding: 4px;
}

.nodebox-resize-button:hover,
.nodebox-reposition-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Title section */
.nodebox-title-section {
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
}

.nodebox-title-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.nodebox-title-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 16px;
}

/* Description section */
.nodebox-description-section {
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
}

.nodebox-description-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.nodebox-description-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  resize: vertical;
  font-size: 14px;
}

/* Tabs */
.nodebox-tabs {
  display: flex;
  overflow-x: auto;
  border-bottom: 1px solid #e9ecef;
}

.nodebox-tab {
  padding: 12px 16px;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  cursor: pointer;
  white-space: nowrap;
  color: #495057;
  font-weight: 500;
}

.nodebox-tab:hover {
  background-color: #f8f9fa;
}

.nodebox-tab.active {
  border-bottom-color: #333333;
  color: #333333;
}

/* Tab content */
.nodebox-tab-content {
  padding: 16px;
  overflow-y: auto;
  flex: 1;
  max-height: 40vh;
}

/* Agent tab content */
.agent-tab-content h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
  color: #212529;
}

.agent-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.agent-input {
  width: 100%;
  min-height: 150px;
  padding: 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  resize: vertical;
  font-size: 14px;
}

/* Data tab */
.data-section {
  margin-bottom: 24px;
}

.data-section h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 16px;
  color: #343a40;
}

.data-button {
  padding: 8px 16px;
  background-color: #f8f9fa;
  border: 1px solid #ced4da;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #495057;
}

.data-button:hover {
  background-color: #e9ecef;
}

.data-list {
  list-style: none;
  padding: 0;
  margin: 12px 0 0 0;
  color: #6c757d;
}

.link-input-group {
  display: flex;
  gap: 8px;
}

.link-input-group input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

/* Connections tab */
.connections-section {
  margin-bottom: 24px;
}

.connections-section h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 16px;
  color: #343a40;
}

.connection-button {
  padding: 8px 16px;
  background-color: #f8f9fa;
  border: 1px solid #ced4da;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #495057;
}

.connection-button:hover {
  background-color: #e9ecef;
}

.connections-list {
  list-style: none;
  padding: 0;
  margin: 12px 0 0 0;
  color: #6c757d;
}

.new-mindsheet-group {
  display: flex;
  gap: 8px;
}

.new-mindsheet-group select {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

/* Agent-specific tab styling */
.nodebox-tab[data-tab="white"] {
  color: #212529;
}

.nodebox-tab[data-tab="red"] {
  color: #dc3545;
}

.nodebox-tab[data-tab="black"] {
  color: #212529;
}

.nodebox-tab[data-tab="yellow"] {
  color: #ffc107;
}

.nodebox-tab[data-tab="green"] {
  color: #198754;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .nodebox {
    width: 95%;
    max-height: 90vh;
  }

  .nodebox-tabs {
    flex-wrap: wrap;
  }

  .nodebox-tab {
    flex: 1;
    min-width: 100px;
    text-align: center;
  }
}
