/**
 * MessageList.tsx
 * 
 * Component for displaying a list of messages in the governance chat.
 */

import React, { useEffect, useRef } from 'react';
import { Message } from '../types';
import { GovernanceAction } from '../GovernanceChat';
import './MessageList.css';

interface MessageListProps {
  messages: Message[];
  onAction?: (action: GovernanceAction) => void;
  onBuildMindmap?: () => void;
  showBuildMindmapButton?: boolean;
}

const MessageList: React.FC<MessageListProps> = ({
  messages,
  onAction,
  onBuildMindmap,
  showBuildMindmapButton
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Debug output for showBuildMindmapButton
  useEffect(() => {
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      console.log('MessageList: Last message:', {
        text: lastMessage.text.substring(0, 50) + '...',
        sender: lastMessage.sender,
        responseType: lastMessage.responseType,
        hasMbcpData: !!lastMessage.mbcpData,
        intent: lastMessage.mbcpData?.intent,
        suggestedActionTypes: lastMessage.suggestedActions?.map(a => a.type),
        showBuildMindmapButton
      });
    }
  }, [messages, showBuildMindmapButton]);
  
  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);
  
  // Format timestamp
  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  return (
    <div className="message-list">
      {messages.map((message) => (
        <div
          key={message.id}
          className={`message ${message.sender} ${message.isError ? 'error' : ''}`}
        >
          <div className="message-content">
            <div className="message-text">{message.text}</div>
            {message.suggestedActions && message.suggestedActions.length > 0 && (
              <div className="suggested-actions">
                {message.suggestedActions.map((action, index) => (
                  <button
                    key={index}
                    className="action-button"
                    onClick={() => onAction && onAction(action)}
                  >
                    {action.label}
                  </button>
                ))}
              </div>
            )}
          </div>
          <div className="message-timestamp">
            {formatTimestamp(message.timestamp)}
          </div>
        </div>
      ))}
      
      {/* Build mindmap button */}
      {showBuildMindmapButton && onBuildMindmap && (
        <div className="build-mindmap-container">
          <button
            className="build-mindmap-button"
            onClick={onBuildMindmap}
          >
            Build Mind Map
          </button>
        </div>
      )}
      
      {/* Debug info in DOM when not showing build button */}
      {messages.length > 0 && !showBuildMindmapButton && (
        <div className="debug-info" style={{ fontSize: '10px', color: '#999', padding: '5px', display: 'none' }}>
          Intent: {messages[messages.length - 1].mbcpData?.intent || messages[messages.length - 1].responseType?.type || 'none'}
        </div>
      )}
      
      {/* Invisible element for scrolling to bottom */}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
