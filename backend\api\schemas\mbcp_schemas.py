"""
MBCP Schemas Module
Contains JSON schemas for MBCP structures for OpenAI function calling.
"""
import logging
from typing import Dict, Any, List

# Import intent types from the central configuration
try:
    from ...config.intent_config import get_intent_types
except ImportError:
    # Fallback to absolute import if relative import fails
    from config.intent_config import get_intent_types

# Get the existing logger instead of creating a new one
logger = logging.getLogger(__name__)

def get_mbcp_function_schema() -> List[Dict[str, Any]]:
    """
    Get the MBCP function schema for OpenAI function calling
    """
    logger.info("Generating MBCP function schema")

    # Get intent types from the configuration
    intent_types = get_intent_types()

    # Define the MBCP function schema
    mbcp_schema = {
        "name": "mbcp_response",
        "description": "Generate a structured response following the MindBack Content Protocol (MBCP)",
        "parameters": {
            "type": "object",
            "required": ["text", "description", "intent"],
            "properties": {
                "text": {
                    "type": "string",
                    "description": "A concise title or summary of your response"
                },
                "description": {
                    "type": "string",
                    "description": "The detailed content of your response"
                },
                "intent": {
                    "type": "string",
                    "enum": intent_types,
                    "description": "The intent classification of this content"
                },
                "type": {
                    "type": "string",
                    "enum": intent_types,
                    "description": "The type of content (usually matches intent)"
                },
                "chatfork_id": {
                    "type": "string",
                    "description": "Unique identifier for the chatfork, starting with 'cfk_'"
                },
                "root_topic": {
                    "type": "string",
                    "description": "Clean topic title for the content"
                },
                "summary": {
                    "type": "string",
                    "description": "A short summary sentence of the content"
                },
                "full_text": {
                    "type": "string",
                    "description": "Multi-paragraph prose with structure, the main content of the response"
                },
                "ui_labels": {
                    "type": "object",
                    "properties": {
                        "chatfork_button": {
                            "type": "string",
                            "description": "Label for the chatfork button"
                        },
                        "chatfork_tooltip": {
                            "type": "string",
                            "description": "Tooltip text for the chatfork button"
                        }
                    },
                    "description": "UI labels for the chatfork interface"
                },
                "mindmap": {
                    "type": "object",
                    "description": "A mindmap node structure following MBCP format",
                    "properties": {
                        "root": {
                            "type": "object",
                            "required": ["id", "text", "description", "metadata"],
                            "properties": {
                                "id": {
                                    "type": "string",
                                    "description": "Unique identifier for the root node"
                                },
                                "text": {
                                    "type": "string",
                                    "description": "Short descriptive text for the node"
                                },
                                "description": {
                                    "type": "string",
                                    "description": "Detailed explanation or content for the node"
                                },
                                "metadata": {
                                    "type": "object",
                                    "required": ["intent"],
                                    "properties": {
                                        "intent": {
                                            "type": "string",
                                            "enum": intent_types,
                                            "description": "The intent classification of this node"
                                        },
                                        "agent": {
                                            "type": "string",
                                            "enum": ["blue_hat", "white_hat", "red_hat", "black_hat", "yellow_hat", "green_hat"],
                                            "description": "The thinking hat perspective for this node"
                                        },
                                        "tags": {
                                            "type": "array",
                                            "items": {"type": "string"},
                                            "description": "Tags or keywords for this node"
                                        }
                                    }
                                },
                                "children": {
                                    "type": "array",
                                    "description": "Child nodes of this node",
                                    "items": {
                                        "$ref": "#/properties/mindmap/properties/root"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    logger.debug("MBCP function schema generated successfully")
    return [mbcp_schema]

def get_metadata_schema() -> Dict[str, Any]:
    """
    Get the metadata schema for MBCP nodes
    """
    # Get intent types from the configuration
    intent_types = get_intent_types()

    return {
        "type": "object",
        "required": ["intent"],
        "properties": {
            "intent": {
                "type": "string",
                "enum": intent_types,
                "description": "The intent classification of this node"
            },
            "agent": {
                "type": "string",
                "enum": ["blue_hat", "white_hat", "red_hat", "black_hat", "yellow_hat", "green_hat"],
                "description": "The thinking hat perspective for this node"
            },
            "tags": {
                "type": "array",
                "items": {"type": "string"},
                "description": "Tags or keywords for this node"
            },
            "action": {
                "type": "object",
                "properties": {
                    "title": {"type": "string"},
                    "owner": {"type": "string"},
                    "due_date": {"type": "string"},
                    "status": {"type": "string", "enum": ["pending", "in_progress", "done"]}
                },
                "required": ["title", "owner", "due_date", "status"],
                "description": "Action item details"
            }
        }
    }