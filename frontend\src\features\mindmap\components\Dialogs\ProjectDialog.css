/**
 * ProjectDialog.css
 * 
 * Styles for the ProjectDialog component.
 */

.project-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.project-dialog {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 600px;
  max-width: 90%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.project-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
}

.project-dialog-header h2 {
  margin: 0;
  font-size: 18px;
  color: #2d3748;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: #a0aec0;
  cursor: pointer;
}

.close-button:hover {
  color: #4a5568;
}

.project-dialog-content {
  padding: 20px;
  overflow-y: auto;
}

.current-project,
.new-project,
.project-list {
  margin-bottom: 24px;
}

.current-project h3,
.new-project h3,
.project-list h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #2d3748;
}

.project-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
}

.project-name {
  font-weight: 500;
  color: #2d3748;
}

.save-button {
  padding: 6px 12px;
  background-color: #3182ce;
  border: 1px solid #3182ce;
  border-radius: 4px;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;
}

.save-button:hover {
  background-color: #2b6cb0;
  border-color: #2b6cb0;
}

.new-project-form {
  display: flex;
  gap: 12px;
}

.new-project-form input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 14px;
}

.create-button {
  padding: 8px 16px;
  background-color: #3182ce;
  border: 1px solid #3182ce;
  border-radius: 4px;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;
}

.create-button:hover {
  background-color: #2b6cb0;
  border-color: #2b6cb0;
}

.create-button:disabled {
  background-color: #a0aec0;
  border-color: #a0aec0;
  cursor: not-allowed;
}

.project-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.project-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.project-item:hover {
  background-color: #f7fafc;
}

.project-item.active {
  background-color: #ebf8ff;
  border-color: #90cdf4;
}

.project-item-info {
  display: flex;
  flex-direction: column;
}

.project-item-name {
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 4px;
}

.project-item-meta {
  font-size: 12px;
  color: #718096;
}

.delete-button {
  padding: 6px 12px;
  background-color: #ffffff;
  border: 1px solid #e53e3e;
  border-radius: 4px;
  color: #e53e3e;
  font-size: 14px;
  cursor: pointer;
}

.delete-button:hover {
  background-color: #e53e3e;
  color: #ffffff;
}

.no-projects {
  color: #718096;
  font-style: italic;
}
