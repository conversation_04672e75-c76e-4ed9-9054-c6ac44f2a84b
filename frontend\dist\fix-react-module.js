// Fix for React module issues
(function() {
  console.log('Applying fix for React module...');
  
  // Create a mock React module
  const mockReact = {
    createElement: function() { return {}; },
    Fragment: Symbol('Fragment'),
    StrictMode: Symbol('StrictMode'),
    
    // React hooks
    useState: function(initialState) {
      return [
        typeof initialState === 'function' ? initialState() : initialState,
        function() {}
      ];
    },
    useEffect: function() {},
    useContext: function() { return {}; },
    useReducer: function(reducer, initialState) { return [initialState, function() {}]; },
    useCallback: function(callback) { return callback; },
    useMemo: function(factory) { return factory(); },
    useRef: function(initialValue) { return { current: initialValue }; },
    
    // React internals
    __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED: {
      ReactCurrentDispatcher: {
        current: {
          // Add all hooks to the dispatcher
          useState: function(initialState) {
            return [
              typeof initialState === 'function' ? initialState() : initialState,
              function() {}
            ];
          },
          useEffect: function() {},
          useContext: function() { return {}; },
          useReducer: function(reducer, initialState) { return [initialState, function() {}]; },
          useCallback: function(callback) { return callback; },
          useMemo: function(factory) { return factory(); },
          useRef: function(initialValue) { return { current: initialValue }; },
          useLayoutEffect: function() {},
          useImperativeHandle: function() {},
          useDebugValue: function() {},
          useDeferredValue: function(value) { return value; },
          useTransition: function() { return [false, function() {}]; },
          useId: function() { return 'id-' + Math.random().toString(36).substring(2, 9); },
          useSyncExternalStore: function(subscribe, getSnapshot) { return getSnapshot(); },
          useInternalStore: function(subscribe, getSnapshot) { return getSnapshot(); }
        }
      }
    }
  };
  
  // Create a mock module system
  window.mockModules = window.mockModules || {};
  
  // Add the React module to the mock module system
  window.mockModules['react'] = mockReact;
  
  // Override require to use our mock modules
  const originalRequire = window.require || function() {};
  window.require = function(moduleName) {
    console.log('Require called for:', moduleName);
    
    // Check if we have a mock for this module
    if (window.mockModules[moduleName]) {
      console.log('Using mock module for:', moduleName);
      return window.mockModules[moduleName];
    }
    
    // If it's the React module, return our mock
    if (moduleName === 'react') {
      console.log('Using mock React module');
      return mockReact;
    }
    
    // Otherwise, use the original require
    try {
      return originalRequire(moduleName);
    } catch (error) {
      console.warn('Error requiring module:', moduleName, error);
      
      // Return an empty object for missing modules
      return {};
    }
  };
  
  // Set the mock React on window
  if (!window.React) {
    window.React = mockReact;
    console.log('Set window.React to mock React');
  }
  
  console.log('Fix for React module applied successfully');
})();
