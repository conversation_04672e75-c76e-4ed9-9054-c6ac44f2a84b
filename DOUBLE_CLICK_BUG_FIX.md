# Double-Click Bug Fix - Exploratory Intent

## Issue
When clicking the "Explore" button for exploratory content, users had to click twice:
1. **First click**: Selected the ChatFork intention but didn't create the mindsheet
2. **Second click**: Actually created the mindsheet

## Root Cause
The governance action flow had an unnecessary two-step process:
1. First click → `handleExploreTopic()` → `handleChatAction()` → stored data in `pendingChatForkData` → called `handleContextChange('chatfork')`
2. Second click → Same flow but now `pendingChatForkData` existed, so mindsheet was created

This was caused by the intention selection pattern requiring confirmation before execution.

## Solution
**Modified `GovernanceBoxPositioned.tsx`** to immediately create the mindsheet on the first click:

### Before:
```typescript
if (action.type === 'show_chatfork') {
  // Store data and trigger context selection
  setPendingChatForkData(chatForkData);
  handleContextChange('chatfork'); // Required second click
}
```

### After:
```typescript
if (action.type === 'show_chatfork') {
  // IMMEDIATE CREATION - no second click required
  try {
    // Register events
    RegistrationManager.registerEvent(EventType.INTENTION_SELECTED, { name: 'ChatFork' });
    
    // Create mindsheet immediately
    const sheetId = mindBookStore.createChatForkSheet(sheetTitle, chatForkData);
    mindBookStore.setActiveSheet(sheetId);
    
    // Show in ChatForkStore
    chatForkStore.showChatFork(chatForkData, sheetId);
    
    // Add confirmation message
    useChatStore.getState().addMessage(assistantMessage);
  } catch (error) {
    console.error('Error creating chatfork sheet:', error);
  }
  return; // Done - no context selection needed
}
```

## Changes Made
1. **Removed** `pendingChatForkData` state
2. **Moved** the entire mindsheet creation logic from `handleContextChange('chatfork')` to `handleChatAction()`
3. **Eliminated** the two-step process completely
4. **Kept** the context selection flow for manual intention changes (dropdown menu)

## Result
✅ **One-click mindsheet creation**: Exploratory intents now create mindsheets immediately on first button click
✅ **Improved UX**: No more confusion about needing to click twice
✅ **Maintains compatibility**: Manual context selection still works for other use cases

## Files Modified
- `frontend/src/governance/chat/GovernanceBoxPositioned.tsx`

## Testing
The fix should resolve the double-click requirement for exploratory content while maintaining all existing functionality. 