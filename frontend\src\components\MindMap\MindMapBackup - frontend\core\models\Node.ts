/**
 * Node model for MindMap
 * Represents a node in the mind map
 */

import { v4 as uuidv4 } from 'uuid';

/**
 * NodeShape type
 */
export type NodeShape = 'rectangle' | 'roundedRectangle' | 'circle' | 'triangle';
export type Direction = 'right' | 'down' | 'left' | 'up';

export interface Position {
  x: number;
  y: number;
}

/**
 * HatContributions type
 */
export interface HatContributions {
  blue: boolean;    // Process & Control
  white: boolean;   // Facts & Information
  red: boolean;     // Emotions & Intuition
  black: boolean;   // Critical Judgment
  yellow: boolean;  // Optimism & Benefits
  green: boolean;   // Creativity & Alternatives
}

/**
 * Node interface
 */
export interface Node {
  id: string;
  text: string;
  title?: string;
  description: string;
  parentId?: string;
  x: number;
  y: number;
  width: number;
  height: number;
  color: string;
  borderColor: string;
  shape: NodeShape;
  hatContributions: HatContributions;
  isEditing?: boolean;
  createdAt?: number;
  updatedAt?: number;
  metadata?: {
    nodePath?: string;
    intention?: string;
    content?: string;
    tags?: string[];
    color?: string;
    manuallyPositioned?: boolean;
    isManuallyAdded?: boolean;
    creationSource?: 'auto' | 'manual';
    [key: string]: any;
  };
}

/**
 * Default node values
 */
export const defaultNodeValues = {
  width: 140,
  height: 60,
  color: '#60a5fa',
  borderColor: '#3b82f6',
  shape: 'roundedRectangle' as NodeShape,
  description: '',
  hatContributions: {
    blue: false,
    white: false,
    red: false,
    black: false,
    yellow: false,
    green: false
  }
};

/**
 * Create a new node with default values
 * Supports two parameter patterns for backward compatibility:
 * 1. (text, x, y, overrides) - Original format
 * 2. (options) - New format with a single object containing all properties
 */
export const createNode = (
  textOrOptions: string | Partial<Node>,
  x?: number,
  y?: number,
  overrides: Partial<Node> = {}
): Node => {
  let nodeOptions: Partial<Node> = {};
  
  // Safeguard against undefined textOrOptions
  if (!textOrOptions) {
    console.warn('createNode called with undefined parameters, using defaults');
    textOrOptions = 'New Node';
  }
  
  // Handle the case where the first argument is an options object
  if (typeof textOrOptions === 'object') {
    nodeOptions = textOrOptions || {};
  } 
  // Handle the case where the arguments are (text, x, y, overrides)
  else if (typeof textOrOptions === 'string') {
    nodeOptions = {
      text: textOrOptions,
      x: x !== undefined ? x : 0,
      y: y !== undefined ? y : 0,
      ...(overrides || {})
    };
  }
  
  const id = nodeOptions.id || uuidv4();
  const now = Date.now();
  
  // Ensure we have text for title fallback
  const text = nodeOptions.text || 'New Node';

  // Create the node with default values
  const node: Node = {
    id,
    text,
    title: text, // Ensure title is set to text by default
    x: nodeOptions.x !== undefined ? nodeOptions.x : 0,
    y: nodeOptions.y !== undefined ? nodeOptions.y : 0,
    width: defaultNodeValues.width,
    height: defaultNodeValues.height,
    color: defaultNodeValues.color,
    borderColor: defaultNodeValues.borderColor,
    shape: defaultNodeValues.shape,
    description: nodeOptions.description || defaultNodeValues.description,
    hatContributions: nodeOptions.hatContributions || {
      blue: false,
      white: false,
      red: false,
      black: false,
      yellow: false,
      green: false
    },
    createdAt: now,
    updatedAt: now,
    // Merge in any provided options, but only if it's safe
    ...(nodeOptions || {}),
  };

  // Special handle for merging metadata
  if (nodeOptions.metadata) {
    node.metadata = {
      ...(node.metadata || {}),
      ...nodeOptions.metadata
    };
  }

  // Ensure title is always set
  if (!node.title) {
    node.title = node.text;
  }

  return node;
};

/**
 * Update a node with new properties
 */
export function updateNode(node: Node, updates: Partial<Node>): Node {
  return {
    ...node,
    ...updates,
    updatedAt: Date.now(),
    title: updates.title || (updates.text ? updates.text : node.title)
  };
}

/**
 * Adds a hat contribution to a node
 */
export function addHatContribution(node: Node, hat: 'blue' | 'white' | 'red' | 'black' | 'yellow' | 'green'): Node {
  return updateNode(node, {
    hatContributions: {
      ...node.hatContributions,
      [hat]: true
    }
  });
}

/**
 * Checks if a node has a specific hat contribution
 */
export function hasHatContribution(node: Node, hat: 'blue' | 'white' | 'red' | 'black' | 'yellow' | 'green'): boolean {
  return node.hatContributions[hat];
}

/**
 * Gets an array of all hat contributions for a node
 */
export function getNodeHatContributions(node: Node): ('blue' | 'white' | 'red' | 'black' | 'yellow' | 'green')[] {
  return Object.entries(node.hatContributions)
    .filter(([_, hasContributed]) => hasContributed)
    .map(([hat, _]) => hat as 'blue' | 'white' | 'red' | 'black' | 'yellow' | 'green');
}

export interface Connection {
  from: string;
  to: string;
  style?: 'straight' | 'curved';
  thickness?: number;
  color?: string;
}

export interface Project {
  id: string;
  name: string;
  nodes: Node[];
  connections: Connection[];
  lastModified: string;
}
