import React, { useState } from 'react';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import MinimizeIcon from '@mui/icons-material/Minimize';
import RefreshIcon from '@mui/icons-material/Refresh';
import ExitToAppIcon from '@mui/icons-material/ExitToApp';
import ToggleOnIcon from '@mui/icons-material/ToggleOn';
import ToggleOffIcon from '@mui/icons-material/ToggleOff';
import AspectRatioIcon from '@mui/icons-material/AspectRatio';
import CreateNewFolderIcon from '@mui/icons-material/CreateNewFolder';

export interface DialogHeaderProps {
  title?: string;
  onClose: () => void;
  onMouseDown?: (e: React.MouseEvent) => void;
  onMinimize?: () => void;
  onResetPosition?: () => void;
  onResetSize?: () => void;
  onExitMindmapMode?: () => void;
  onToggleLiveLLM?: () => void;
  onNewProject?: () => void;
  useLiveLLM?: boolean;
  isCollapsed?: boolean;
}

export const DialogHeader: React.FC<DialogHeaderProps> = ({
  title = "Governance Agent",
  onClose,
  onMouseDown,
  onMinimize,
  onResetPosition,
  onResetSize,
  onExitMindmapMode,
  onToggleLiveLLM,
  onNewProject,
  useLiveLLM = false,
  isCollapsed = false
}) => {
  const [logoSrc, setLogoSrc] = useState('./Public/Logo/MB_logo.jpg');

  // Prevent propagation for button clicks to avoid interfering with drag
  const handleButtonClick = (callback?: () => void) => (e: React.MouseEvent) => {
    if (!callback) return;
    e.preventDefault();
    e.stopPropagation();
    callback();
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    // Improved handling for drag events
    // Only allow dragging from the header area, not from buttons
    if (
      e.target === e.currentTarget ||
      (e.target as HTMLElement).classList.contains('dialog-header-title') ||
      (e.target as HTMLElement).classList.contains('dialog-header-text') ||
      (e.target as HTMLElement).classList.contains('dialog-header-logo')
    ) {
      // Log the drag start for debugging
      console.log('DialogHeader: Starting drag operation');

      // Prevent text selection during drag
      e.preventDefault();

      // Add transparent selection class to body
      document.body.classList.add('react-draggable-transparent-selection');

      // Let the draggable component handle this
      if (onMouseDown) onMouseDown(e);

      // Set up cleanup for mouseup event
      const handleMouseUp = () => {
        console.log('DialogHeader: Ending drag operation');
        document.body.classList.remove('react-draggable-transparent-selection');
        document.removeEventListener('mouseup', handleMouseUp);
      };

      document.addEventListener('mouseup', handleMouseUp, { once: true });
    }
  };

  return (
    <div
      className="dialog-header"
      onMouseDown={handleMouseDown}
    >
      <div className="dialog-header-title">
        <img
          src={logoSrc}
          alt="MindBack Logo"
          className="dialog-header-logo"
          width="24"
          height="24"
          draggable="false"
          onError={(e) => {
            console.error('Failed to load logo:', logoSrc);
            setLogoSrc('./Public/Logo/mindback_logo.jpg');
          }}
        />
        <span className="dialog-header-text">{title}</span>
      </div>
      <div className="dialog-header-buttons">
        {onNewProject && (
          <IconButton
            onClick={handleButtonClick(onNewProject)}
            size="small"
            className="dialog-header-button new-project-button"
            title="New Project"
          >
            <CreateNewFolderIcon />
          </IconButton>
        )}
        {onToggleLiveLLM && (
          <IconButton
            onClick={handleButtonClick(onToggleLiveLLM)}
            size="small"
            className="dialog-header-button"
            title={useLiveLLM ? "Switch to Mock LLM" : "Switch to Live LLM"}
          >
            {useLiveLLM ? <ToggleOnIcon /> : <ToggleOffIcon />}
          </IconButton>
        )}
        {onResetPosition && (
          <IconButton
            onClick={handleButtonClick(onResetPosition)}
            size="small"
            className="dialog-header-button"
            title="Reset Position"
          >
            <RefreshIcon />
          </IconButton>
        )}
        {onResetSize && (
          <IconButton
            onClick={handleButtonClick(onResetSize)}
            size="small"
            className="dialog-header-button"
            title="Reset Size"
          >
            <AspectRatioIcon />
          </IconButton>
        )}
        {onExitMindmapMode && (
          <IconButton
            onClick={handleButtonClick(onExitMindmapMode)}
            size="small"
            className="dialog-header-button"
            title="Exit Mindmap Mode"
          >
            <ExitToAppIcon />
          </IconButton>
        )}
        {onMinimize && (
          <IconButton
            onClick={handleButtonClick(onMinimize)}
            size="small"
            className="dialog-header-button minimize-button"
            title={isCollapsed ? "Expand" : "Minimize"}
          >
            <MinimizeIcon />
          </IconButton>
        )}
        <IconButton
          onClick={handleButtonClick(onClose)}
          size="small"
          className="dialog-header-button"
          title="Close"
        >
          <CloseIcon />
        </IconButton>
      </div>
    </div>
  );
};

export default DialogHeader;