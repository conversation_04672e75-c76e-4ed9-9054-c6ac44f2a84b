# Node.js Version Compatibility

## Recommended Node.js Version

MindBack is optimized for **Node.js 20 LTS**. This version provides the best balance of:
- Modern JavaScript features
- Long-term support and security updates
- Compatibility with our dependencies
- Stable performance

## Version Requirements

| Component | Minimum Version | Recommended Version | Notes |
|-----------|----------------|---------------------|-------|
| Node.js   | 18.0.0         | 20.x LTS            | Avoid Node.js 22+ for now |
| npm       | 8.0.0          | 10.x                | Comes with Node.js 20 |
| Vite      | 4.4.9          | 4.4.9               | Current project version |

## Known Issues

### Node.js 22+
- May cause unexpected behavior with some dependencies
- Not fully tested with our current Vite configuration
- Potential TypeScript compilation issues

### Node.js < 18
- Missing required features for some dependencies
- Security vulnerabilities in older versions
- End of life for Node.js 16 and earlier

### React Version Conflict
- The application uses React 18.3.1, but react-konva (17.0.2-6) requires React 17.0.2
- This causes npm warnings during installation but the application works due to the resolutions field in package.json
- Consider upgrading react-konva to a version that supports React 18 to resolve these warnings

## Security Vulnerabilities

The npm audit shows moderate severity vulnerabilities in esbuild (used by Vite):
- esbuild <=0.24.2: Enables any website to send requests to the development server
- This affects the development environment only, not production builds
- Fixing requires upgrading to Vite 6.3.5+, which is a breaking change
- Consider upgrading Vite in a future update after thorough testing

## Troubleshooting

If you encounter issues after upgrading or downgrading Node.js:

1. Clear npm cache:
   ```
   npm cache clean --force
   ```

2. Delete node_modules and reinstall:
   ```
   rm -rf node_modules
   npm install
   ```

3. Check for global npm packages that might conflict:
   ```
   npm list -g --depth=0
   ```

## Checking Your Node.js Version

To check your current Node.js version:

```bash
node -v
```

To check your npm version:

```bash
npm -v
```

## Using NVM (Node Version Manager)

For developers who need to work with multiple Node.js versions, we recommend using NVM:

### Windows
Install [NVM for Windows](https://github.com/coreybutler/nvm-windows)

```bash
# Install Node.js 20 LTS
nvm install 20.12.2

# Use Node.js 20
nvm use 20.12.2
```

### macOS/Linux
Install [NVM](https://github.com/nvm-sh/nvm)

```bash
# Install Node.js 20 LTS
nvm install --lts

# Use Node.js 20
nvm use --lts
```

## Future Plans

We plan to maintain compatibility with Node.js LTS versions. When a new LTS version is well-established and tested with our dependencies, we will update our recommendations accordingly.
