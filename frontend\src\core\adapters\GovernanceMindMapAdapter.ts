/**
 * GovernanceMindMapAdapter.ts
 * 
 * Adapter for communication between the Governance and MindMap modules.
 * This adapter ensures that the modules remain independent.
 * 
 * UPDATED: Now uses the unified MBCP processor for all data format handling.
 */

import { GovernanceAction } from '../../features/governance/GovernanceChat';
import { processMBCPData, MBCPData } from '../mbcp/MBCPProcessor';
import { useMindBookStore } from '../state/MindBookStore';
import { v4 as uuidv4 } from 'uuid';

/**
 * Process a governance action and update the MindMap store accordingly.
 * 
 * @param action The governance action to process
 * @returns True if the action was processed successfully, false otherwise
 */
export const processGovernanceAction = (action: GovernanceAction): boolean => {
  console.log('GovernanceMindMapAdapter: Processing governance action:', action.type);
  
  // Process the action based on its type
  switch (action.type) {
    case 'create_mindmap':
      return createMindmapFromGovernanceAction(action);
    
    default:
      console.warn(`GovernanceMindMapAdapter: Unknown governance action type: ${action.type}`);
      return false;
  }
};

/**
 * Create a mind map from governance action using unified MBCP processor.
 * 
 * @param action The governance action containing MBCP data
 * @returns True if the mind map was created successfully, false otherwise
 */
export const createMindmapFromGovernanceAction = (action: GovernanceAction): boolean => {
  try {
    console.log('GovernanceMindMapAdapter: Creating mindmap from governance action');

    // Extract MBCP data from various possible locations in the action
    let mbcpData: MBCPData | null = null;
    
    // Check different possible locations for MBCP data
    if (action.payload?.mbcpData) {
      mbcpData = action.payload.mbcpData;
    } else if (action.data?.mbcpData) {
      mbcpData = action.data.mbcpData;
    } else if (action.payload) {
      mbcpData = action.payload;
    } else if (action.data) {
      mbcpData = action.data;
    }

    if (!mbcpData) {
      console.error('GovernanceMindMapAdapter: No MBCP data found in governance action');
      return false;
    }

    console.log('GovernanceMindMapAdapter: Extracted MBCP data:', mbcpData);

    // Create mindsheet using MindBook store
    const mindBookStore = useMindBookStore.getState();
    
    // Extract title from MBCP data
    const title = mbcpData.text || 
                  mbcpData.mindmap?.root?.text || 
                  mbcpData.root?.text || 
                  'Generated Mind Map';

    // Create new mindsheet
    const sheetId = mindBookStore.createMindMapSheet(title, mbcpData);
    
    if (!sheetId) {
      console.error('GovernanceMindMapAdapter: Failed to create mindsheet');
      return false;
    }

    console.log('GovernanceMindMapAdapter: Created mindsheet with ID:', sheetId);

    // Process MBCP data using unified processor
    const result = processMBCPData(mbcpData);
    
    if (!result.success) {
      console.error('GovernanceMindMapAdapter: Failed to process MBCP data:', result.error);
      return false;
    }

    console.log('GovernanceMindMapAdapter: Successfully processed MBCP data, root node ID:', result.rootNodeId);
    return true;
    
  } catch (error) {
    console.error('GovernanceMindMapAdapter: Error creating mind map from governance action:', error);
    return false;
  }
};

/**
 * Legacy compatibility function - deprecated but maintained for backward compatibility
 * @deprecated Use processMBCPData from core/mbcp/MBCPProcessor instead
 */
export const createMindmapFromMBCP = (mbcpData: MBCPData): boolean => {
  console.warn('GovernanceMindMapAdapter: createMindmapFromMBCP is deprecated. Use unified MBCP processor.');
  
  const result = processMBCPData(mbcpData);
  return result.success;
};
