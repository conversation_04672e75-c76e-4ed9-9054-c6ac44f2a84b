import { ProjectSlice } from './projectSlice';
import { NodeSlice } from './nodeSlice';
import { ConnectionSlice } from './connectionSlice';
import { UndoRedoSlice } from './undoRedoSlice';

export interface MindMapStore extends
  ProjectSlice,
  NodeSlice,
  ConnectionSlice,
  UndoRedoSlice {}

export interface Position {
  x: number;
  y: number;
}

export interface Node {
  id: string;
  type: string;
  position: Position;
  data: {
    label: string;
    [key: string]: any;
  };
}

export interface Connection {
  id: string;
  source: string;
  target: string;
  type?: string;
  data?: {
    label?: string;
    [key: string]: any;
  };
}

export interface HistoryEntry {
  nodes: Record<string, Node>;
  connections: Record<string, Connection>;
  position: Position;
  scale: number;
}

export type Direction = '0' | '90' | '180' | '270';
export type LLMModel = 'gpt-3.5-turbo' | 'gpt-4' | 'gpt-4-mini' | 'claude-3.5-sonnet' | 'claude-3.7-sonnet';

export interface MindMapState {
  // Data
  nodes: Record<string, Node>;
  connections: Connection[];

  // UI state
  selectedNodeId: string | null;
  selectedConnectionId: string | null;
  projectName: string;
  scale: number;
  position: { x: number; y: number };
  rootNodeId: string | null;
  showProjectDialog: boolean;
  isEditing: boolean;

  // LLM settings
  llmModel: LLMModel;
}

export interface MindMapActions {
  // Initialize
  initialize: (windowWidth: number, windowHeight: number) => void;

  // Node operations
  addNode: (parentId: string, direction: number) => string | null;
  updateNode: (id: string, updates: Partial<Node>) => void;
  deleteNode: (id: string) => void;
  moveNode: (id: string, x: number, y: number) => void;
  selectNode: (id: string | null) => void;

  // Connection operations
  addConnection: (input: CreateConnectionInput) => void;
  updateConnection: (id: string, updates: Partial<Connection>) => void;
  deleteConnection: (id: string) => void;
  selectConnection: (id: string | null) => void;

  // Project operations
  loadProject: (name?: string) => boolean;
  saveProject: () => any;

  // LLM operations
  setLlmModel: (model: LLMModel) => void;
}

export type MindMapStore = MindMapState & MindMapActions;