// MUI compatibility fixes
(function() {
  try {
    console.log('Applying MUI compatibility fixes...');
    
    // Create a namespace for M<PERSON> if it doesn't exist
    if (!window.MUI) {
      window.MUI = {};
      console.log('Created MUI namespace');
    }
    
    // Create a mock MUI object with common components
    const mockMUI = {
      // Core components
      Button: function(props) {
        return props.children;
      },
      TextField: function(props) {
        return props.children;
      },
      Box: function(props) {
        return props.children;
      },
      Container: function(props) {
        return props.children;
      },
      Grid: function(props) {
        return props.children;
      },
      Paper: function(props) {
        return props.children;
      },
      Typography: function(props) {
        return props.children;
      },
      
      // Icons
      icons: {
        Add: function() { return null; },
        Delete: function() { return null; },
        Edit: function() { return null; },
        Menu: function() { return null; },
        Close: function() { return null; }
      },
      
      // Styles
      styles: {
        makeStyles: function(styles) {
          return function() {
            return {};
          };
        },
        createTheme: function(options) {
          return {
            palette: options?.palette || {
              primary: { main: '#3f51b5' },
              secondary: { main: '#f50057' }
            },
            typography: options?.typography || {
              fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif'
            },
            spacing: function(factor) {
              return factor * 8;
            }
          };
        },
        ThemeProvider: function(props) {
          return props.children;
        }
      },
      
      // System
      system: {
        spacing: function(factor) {
          return factor * 8;
        },
        palette: {
          primary: { main: '#3f51b5' },
          secondary: { main: '#f50057' }
        }
      }
    };
    
    // Merge with existing MUI object
    Object.assign(window.MUI, mockMUI);
    
    // Create a mock module system for MUI
    window.mockModules = window.mockModules || {};
    
    // Add MUI modules to the mock module system
    window.mockModules['@mui/material'] = window.MUI;
    window.mockModules['@mui/icons-material'] = window.MUI.icons;
    window.mockModules['@mui/styles'] = window.MUI.styles;
    window.mockModules['@mui/system'] = window.MUI.system;
    
    console.log('MUI compatibility fixes applied successfully');
  } catch (error) {
    console.error('Error applying MUI compatibility fixes:', error);
  }
})();
