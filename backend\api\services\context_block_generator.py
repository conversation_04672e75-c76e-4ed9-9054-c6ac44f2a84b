"""
ContextBlockGenerator.py

Generates MBCP-compatible context blocks for the three-stage pipeline.
Implements Phase 1.1.2 of the Development Plan.
"""

import base64
import gzip
import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from .mbcp_memory_service import get_mbcp_memory_service, MBCPMemoryEntry, ConversationThread

@dataclass
class ContextBlock:
    """MBCP-compatible context block"""
    type: str  # CTX, MEM, ZIPPED
    content: str
    metadata: Dict[str, Any]

class ContextBlockGenerator:
    """
    Generates context blocks for MBCP-compatible memory system:
    - [CTX]:: compressed contextual metadata
    - [MEM]:: structured conversation history
    - [ZIPPED]:: Base64 encoded large data blocks
    """
    
    def __init__(self):
        self.memory_service = get_mbcp_memory_service()
    
    def generate_ctx_block(self, 
                          sheet_type: str,
                          intent: str,
                          topic: Optional[str] = None,
                          context_level: Optional[str] = None,
                          sheet_id: Optional[str] = None,
                          node_id: Optional[str] = None) -> ContextBlock:
        """
        Generate [CTX]:: block with compressed contextual metadata
        Format: mm/teleo/topic=Buy&Build/context=Segmentation/sheet=mindmap_001/node=active_node_id
        """
        
        # Build context string components
        components = [sheet_type, intent]
        
        if topic:
            components.append(f"topic={topic}")
        if context_level:
            components.append(f"context={context_level}")
        if sheet_id:
            components.append(f"sheet={sheet_id}")
        if node_id:
            components.append(f"node={node_id}")
        
        content = "/".join(components)
        
        return ContextBlock(
            type="CTX",
            content=content,
            metadata={
                "sheet_type": sheet_type,
                "intent": intent,
                "topic": topic,
                "context_level": context_level,
                "sheet_id": sheet_id,
                "node_id": node_id,
                "generated_at": self._get_timestamp()
            }
        )
    
    def generate_mem_block(self,
                          thread_id: Optional[str] = None,
                          sheet_id: Optional[str] = None,
                          include_parent_context: bool = True,
                          include_events: bool = True,
                          include_nodes: bool = True) -> ContextBlock:
        """
        Generate [MEM]:: block with structured conversation history
        Format:
        THREAD: parent_conversation_summary
        CONTEXT: foundational_context_summary
        EVENTS: recent_significant_actions
        NODES: related_mindmap_nodes
        """
        
        content_parts = []
        metadata = {"generated_at": self._get_timestamp()}
        
        # THREAD: Parent conversation summary
        if thread_id and include_parent_context:
            parent_context = self.memory_service.get_parent_context(thread_id)
            if parent_context["parent_messages"]:
                thread_summary = self._summarize_messages(parent_context["parent_messages"])
                content_parts.append(f"THREAD: {thread_summary}")
                metadata["thread_id"] = thread_id
                metadata["parent_context"] = parent_context
        
        # CONTEXT: Foundational context summary
        if sheet_id:
            context_entries = self.memory_service.get_memory_entries_by_sheet(sheet_id, limit=5)
            if context_entries:
                context_summary = self._summarize_memory_entries(context_entries)
                content_parts.append(f"CONTEXT: {context_summary}")
                metadata["sheet_id"] = sheet_id
        
        # EVENTS: Recent significant actions (placeholder for RegistrationManager integration)
        if include_events:
            # TODO: Integrate with RegistrationManager for recent events
            content_parts.append("EVENTS: [Integration with RegistrationManager pending]")
        
        # NODES: Related mindmap nodes (placeholder for node relationship integration)
        if include_nodes:
            # TODO: Integrate with node relationship system
            content_parts.append("NODES: [Node relationship integration pending]")
        
        content = "\n".join(content_parts) if content_parts else "No memory context available"
        
        return ContextBlock(
            type="MEM",
            content=content,
            metadata=metadata
        )
    
    def generate_zipped_block(self, data: Any, description: str = "Large data block") -> ContextBlock:
        """
        Generate [ZIPPED]:: block with Base64 encoded large data
        Compresses and encodes large structured data like mindbook state, financial docs, etc.
        """
        
        try:
            # Convert data to JSON string
            json_str = json.dumps(data, ensure_ascii=False, separators=(',', ':'))
            
            # Compress the JSON string
            compressed_data = gzip.compress(json_str.encode('utf-8'))
            
            # Encode to Base64
            base64_data = base64.b64encode(compressed_data).decode('ascii')
            
            # Create instruction for LLM
            content = f"[ZIPPED_DATA:{description}]\n{base64_data}\n[/ZIPPED_DATA]"
            
            return ContextBlock(
                type="ZIPPED",
                content=content,
                metadata={
                    "description": description,
                    "original_size": len(json_str),
                    "compressed_size": len(compressed_data),
                    "base64_size": len(base64_data),
                    "compression_ratio": len(compressed_data) / len(json_str),
                    "generated_at": self._get_timestamp()
                }
            )
            
        except Exception as e:
            return ContextBlock(
                type="ZIPPED",
                content=f"[ERROR: Failed to compress data - {str(e)}]",
                metadata={
                    "error": str(e),
                    "generated_at": self._get_timestamp()
                }
            )
    
    def decode_zipped_block(self, base64_content: str) -> Optional[Any]:
        """Decode a ZIPPED block back to original data"""
        try:
            # Extract Base64 data from content
            lines = base64_content.split('\n')
            base64_data = ""
            in_data_block = False
            
            for line in lines:
                if line.startswith("[ZIPPED_DATA:"):
                    in_data_block = True
                    continue
                elif line == "[/ZIPPED_DATA]":
                    break
                elif in_data_block:
                    base64_data += line
            
            if not base64_data:
                return None
            
            # Decode from Base64
            compressed_data = base64.b64decode(base64_data)
            
            # Decompress
            json_str = gzip.decompress(compressed_data).decode('utf-8')
            
            # Parse JSON
            return json.loads(json_str)
            
        except Exception as e:
            print(f"Error decoding ZIPPED block: {e}")
            return None
    
    def create_context_compression_utility(self, data: Dict[str, Any], max_size: int = 1000) -> str:
        """Create compressed context for smaller data that doesn't need ZIPPED format"""
        try:
            json_str = json.dumps(data, separators=(',', ':'))
            
            if len(json_str) <= max_size:
                return json_str
            
            # Simple truncation with ellipsis for now
            # TODO: Implement smarter compression based on relevance
            truncated = json_str[:max_size-3] + "..."
            return truncated
            
        except Exception as e:
            return f"[Error compressing context: {str(e)}]"
    
    def _summarize_messages(self, messages: List[Dict[str, Any]]) -> str:
        """Summarize a list of messages for context"""
        if not messages:
            return "No previous messages"
        
        # Simple summarization - take first and last message
        if len(messages) == 1:
            return f"Previous: {messages[0].get('content', '')[:100]}..."
        
        first_msg = messages[0].get('content', '')[:50]
        last_msg = messages[-1].get('content', '')[:50]
        return f"Thread started with: {first_msg}... Latest: {last_msg}... ({len(messages)} messages)"
    
    def _summarize_memory_entries(self, entries: List[MBCPMemoryEntry]) -> str:
        """Summarize memory entries for context"""
        if not entries:
            return "No memory context"
        
        # Group by type and summarize
        type_counts = {}
        for entry in entries:
            type_name = entry.type.value
            type_counts[type_name] = type_counts.get(type_name, 0) + 1
        
        summary_parts = []
        for type_name, count in type_counts.items():
            summary_parts.append(f"{count} {type_name}")
        
        return f"Memory context: {', '.join(summary_parts)}"
    
    def _get_timestamp(self) -> str:
        """Get current timestamp in ISO format"""
        from datetime import datetime
        return datetime.now().isoformat()

# Global instance
_context_block_generator = None

def get_context_block_generator() -> ContextBlockGenerator:
    """Get the global context block generator instance"""
    global _context_block_generator
    if _context_block_generator is None:
        _context_block_generator = ContextBlockGenerator()
    return _context_block_generator
