/**
 * PositioningContext.tsx
 * 
 * React context and hooks for the UI positioning system.
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import { 
  UIPositioningManager, 
  UIElementPosition, 
  UIElementType,
  PositioningStrategy
} from './UIPositioningManager';

// Create a context for the positioning manager
const PositioningManagerContext = createContext<UIPositioningManager | null>(null);

// Create a provider component
export const PositioningManagerProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Create a positioning manager instance
  const [manager] = useState(() => new UIPositioningManager());
  
  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      manager.handleWindowResize();
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [manager]);
  
  return (
    <PositioningManagerContext.Provider value={manager}>
      {children}
    </PositioningManagerContext.Provider>
  );
};

// Create a hook to use the positioning manager
export const usePositioningManager = () => {
  const manager = useContext(PositioningManagerContext);
  if (!manager) {
    throw new Error('usePositioningManager must be used within a PositioningManagerProvider');
  }
  return manager;
};

// Create a hook for individual UI elements to use
export const useUIElement = (
  id: string,
  type: UIElementType,
  defaultPosition: { x: number, y: number },
  defaultSize: { width: number, height: number },
  zIndex: number,
  strategy: PositioningStrategy = 'default',
  priority: number = 0
) => {
  const manager = usePositioningManager();
  const [position, setPosition] = useState(defaultPosition);
  const [size, setSize] = useState(defaultSize);
  const [visible, setVisible] = useState(true);
  
  // Register the element with the manager on mount
  useEffect(() => {
    manager.registerElement({
      id,
      type,
      defaultPosition,
      currentPosition: position,
      size,
      zIndex,
      visible,
      strategy,
      priority
    });
    
    return () => {
      manager.removeElement(id);
    };
  }, []);
  
  // Update the manager when local state changes
  useEffect(() => {
    manager.updatePosition(id, position);
  }, [position.x, position.y]);
  
  useEffect(() => {
    manager.updateSize(id, size);
  }, [size.width, size.height]);
  
  useEffect(() => {
    manager.updateVisibility(id, visible);
  }, [visible]);
  
  // Handle position changes from the manager
  useEffect(() => {
    const handleGlobalUpdate = (elements: Record<string, UIElementPosition>) => {
      const element = elements[id];
      if (element) {
        if (element.currentPosition.x !== position.x || element.currentPosition.y !== position.y) {
          setPosition(element.currentPosition);
        }
      }
    };
    
    manager.registerGlobalHandler(handleGlobalUpdate);
    
    return () => {
      // No way to unregister a specific handler, but the element will be removed on unmount
    };
  }, [position]);
  
  // Register a collision handler
  const registerCollisionHandler = (handler: (collidingElements: string[]) => void) => {
    manager.registerCollisionHandler(id, handler);
  };
  
  // Update the strategy
  const updateStrategy = (newStrategy: PositioningStrategy) => {
    manager.updateStrategy(id, newStrategy);
  };
  
  return {
    position,
    setPosition,
    size,
    setSize,
    visible,
    setVisible,
    registerCollisionHandler,
    updateStrategy,
    resetPosition: () => manager.resetPosition(id)
  };
};
