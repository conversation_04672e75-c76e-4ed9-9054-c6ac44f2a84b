/**
 * useGovernanceChat.ts
 *
 * Hook for managing the governance chat state and interactions.
 */

import { useState, useCallback } from 'react';
import { GovernanceAction } from '../GovernanceChat';
import { Message } from '../types';
import { v4 as uuidv4 } from 'uuid';

import { sendGovernanceMessage } from '../../../services/api/GovernanceAPI';
import { MainRouter, RoutingContext } from '../../../core/routing/MainRouter';

// API call to backend using our service
const apiCall = async (message: string): Promise<Message> => {
  try {
    // Call the governance API service
    const response = await sendGovernanceMessage(message, 'g-llm_dialogue');

    // Check for errors
    if (response.error) {
      throw new Error(response.error);
    }

    console.log('Response from API:', response);
    console.log('mbcpData:', response.mbcpData);

    // DEBUGGING: Log the full response as JSON
    console.log('FULL API Response JSON:', JSON.stringify(response, null, 2));

    // Create routing context for governance box
    const routingContext: RoutingContext = {
      isManualSelection: false,
      sourceContext: 'govbox'
    };

    // Process the response through MainRouter
    const routingDecision = MainRouter.processResponse(response, routingContext);

    console.log('MainRouter decision:', routingDecision);

    // Create proper suggestedActions based on routing decision
    let suggestedActions = response.suggestedActions || [];

    // If this is a teleological intent and we should create a mindmap, add the action if not already present
    if (routingDecision.useMindmap && !suggestedActions.some(action => action.type === 'create_mindmap')) {
      console.log('Adding create_mindmap action based on teleological intent');
      suggestedActions.push({
        type: 'create_mindmap',
        label: 'Build Mind Map',
        payload: {
          mbcpData: response.mbcpData,
          data: { mbcpData: response.mbcpData }
        }
      });
    }

    // DEBUGGING: Log the routing decision display text
    console.log('Final displayText from routingDecision:', routingDecision.displayText);

    // DIRECT FIX: For factual intents, always use the description field if available
    let displayText = routingDecision.displayText;
    if (response.intent === 'factual' && response.description) {
      console.log('DIRECT FIX: Using description field for factual intent:', response.description);
      displayText = response.description;
    } else if (response.mbcpData?.intent === 'factual' && response.mbcpData?.description) {
      console.log('DIRECT FIX: Using mbcpData.description field for factual intent:', response.mbcpData.description);
      displayText = response.mbcpData.description;
    }

    // Create the message object
    const message = {
      id: uuidv4(),
      text: displayText, // Use the display text with our direct fix
      sender: 'assistant',
      timestamp: new Date(),
      suggestedActions: suggestedActions,
      mbcpData: response.mbcpData || null,
      responseType: {
        type: routingDecision.contentType as 'teleological' | 'educational' | 'factual',
        confidence: routingDecision.contentType === 'teleological' ? 0.95 : 0.8
      }
    };

    // DEBUGGING: Log the final message object
    console.log('Final message object being returned:', JSON.stringify(message, null, 2));

    return message;
  } catch (error) {
    console.error('Error calling API:', error);
    throw error;
  }
};

// Hook interface
interface UseGovernanceChatOptions {
  onAction?: (action: GovernanceAction) => void;
}

// Hook return type
interface UseGovernanceChatReturn {
  messages: Message[];
  inputText: string;
  setInputText: (text: string) => void;
  isSubmitting: boolean;
  handleSendMessage: () => Promise<void>;
  handleBuildMindmap: () => void;
}

// Hook implementation
export const useGovernanceChat = (
  options: UseGovernanceChatOptions = {}
): UseGovernanceChatReturn => {
  // State
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 'welcome',
      text: 'Welcome to the Governance Agent! How can I help you today?',
      sender: 'assistant',
      timestamp: new Date()
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Send message
  const handleSendMessage = useCallback(async () => {
    if (!inputText.trim() || isSubmitting) return;

    // Add user message
    const userMessage: Message = {
      id: uuidv4(),
      text: inputText,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsSubmitting(true);

    try {
      // Call real API
      const response = await apiCall(inputText);

      // Add assistant message
      setMessages(prev => [...prev, response]);
    } catch (error) {
      console.error('Error sending message:', error);

      // Add error message
      setMessages(prev => [
        ...prev,
        {
          id: uuidv4(),
          text: 'Sorry, there was an error processing your request. Please try again.',
          sender: 'assistant',
          timestamp: new Date(),
          isError: true
        }
      ]);
    } finally {
      setIsSubmitting(false);
    }
  }, [inputText, isSubmitting]);

  // Build mindmap
  const handleBuildMindmap = useCallback(() => {
    // Get the last message
    const lastMessage = messages[messages.length - 1];

    console.log('handleBuildMindmap called, last message:', lastMessage);

    // Create routing context for governance box
    const routingContext: RoutingContext = {
      isManualSelection: false,
      sourceContext: 'govbox'
    };

    // First check if the message has direct mbcpData
    if (lastMessage && lastMessage.mbcpData) {
      console.log('Using direct mbcpData from message');

      // Process through MainRouter to get proper display text and content type
      const routingDecision = MainRouter.processResponse({
        ...lastMessage.mbcpData,
        intent: lastMessage.mbcpData.intent || 'teleological'
      }, routingContext);

      // Create a proper create_mindmap action
      const createMindmapAction: GovernanceAction = {
        type: 'create_mindmap',
        payload: {
          mbcpData: lastMessage.mbcpData,
          data: {
            mbcpData: lastMessage.mbcpData,
            topic: routingDecision.displayText || 'Untitled Mindmap',
            description: lastMessage.mbcpData.description || '',
            template_type: lastMessage.mbcpData.intent || 'teleological'
          }
        }
      };

      console.log('Dispatching create_mindmap action with data:', createMindmapAction);

      if (options.onAction) {
        options.onAction(createMindmapAction);
      }
      return;
    }

    // Fallback to checking suggested actions
    if (lastMessage && lastMessage.suggestedActions) {
      console.log('Checking suggestedActions for create_mindmap action');
      // Find the create_mindmap action
      const createMindmapAction = lastMessage.suggestedActions.find(
        action => action.type === 'create_mindmap'
      );

      // If found, trigger the action
      if (createMindmapAction && options.onAction) {
        console.log('Found create_mindmap action in suggestedActions');
        options.onAction(createMindmapAction);
        return;
      }
    }

    // Last resort - check if the message has teleological intent
    if (lastMessage && lastMessage.responseType?.type === 'teleological') {
      console.log('Message has teleological intent but no mbcpData or create_mindmap action');

      // Process through MainRouter to get proper display text
      const routingDecision = MainRouter.routeIntent('teleological', {
        text: lastMessage.text,
        description: lastMessage.text
      }, routingContext);

      // Create a basic action with text from MainRouter
      const createMindmapAction: GovernanceAction = {
        type: 'create_mindmap',
        payload: {
          data: {
            topic: routingDecision.displayText,
            description: routingDecision.displayText,
            template_type: 'teleological',
            mbcpData: {
              text: routingDecision.displayText,
              description: routingDecision.displayText,
              intent: 'teleological'
            }
          }
        }
      };

      console.log('Created fallback create_mindmap action:', createMindmapAction);

      if (options.onAction) {
        options.onAction(createMindmapAction);
        return;
      }
    }

    console.error('No mbcpData or create_mindmap action found in last message');
    // Show an alert to the user
    alert('Sorry, I couldn\'t build a mindmap from this message. Please try asking for a teleological analysis.');
  }, [messages, options]);

  return {
    messages,
    inputText,
    setInputText,
    isSubmitting,
    handleSendMessage,
    handleBuildMindmap
  };
};

export default useGovernanceChat;
