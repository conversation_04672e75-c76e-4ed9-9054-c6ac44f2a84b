// This script runs before <PERSON><PERSON> is loaded to ensure compatibility
(function() {
  console.log('Running pre-React compatibility fix...');
  
  // Create a global React object if it doesn't exist
  if (!window.React) {
    window.React = {
      // Basic React API stubs
      createElement: function() { return {}; },
      Fragment: Symbol('Fragment'),
      StrictMode: Symbol('StrictMode'),
      
      // React hooks stubs
      useState: function(initialState) {
        return [
          typeof initialState === 'function' ? initialState() : initialState,
          function() {}
        ];
      },
      useEffect: function() {},
      useContext: function() { return {}; },
      useReducer: function(reducer, initialState) { return [initialState, function() {}]; },
      useCallback: function(callback) { return callback; },
      useMemo: function(factory) { return factory(); },
      useRef: function(initialValue) { return { current: initialValue }; },
      
      // Create the internal structure that <PERSON><PERSON> uses
      __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED: {
        ReactCurrentDispatcher: {
          current: {
            // Add all the hooks that might be used
            useState: function(initialState) {
              return [
                typeof initialState === 'function' ? initialState() : initialState,
                function() {}
              ];
            },
            useEffect: function() {},
            useLayoutEffect: function() {},
            useCallback: function(callback) { return callback; },
            useMemo: function(create) { return create(); },
            useRef: function(initialValue) { return { current: initialValue }; },
            useContext: function() { return {}; },
            useReducer: function(reducer, initialState) { return [initialState, function() {}]; },
            useImperativeHandle: function() {},
            useDebugValue: function() {},
            useDeferredValue: function(value) { return value; },
            useTransition: function() { return [false, function() {}]; },
            useSyncExternalStore: function(subscribe, getSnapshot) { return getSnapshot(); },
            useId: function() { return 'id-' + Math.random().toString(36).substring(2, 9); },
            useInternalStore: function(subscribe, getSnapshot) { return getSnapshot(); }
          }
        }
      }
    };
    
    console.log('Created global React object with internals');
  } else {
    // If React exists but doesn't have internals, add them
    if (!window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) {
      window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = {
        ReactCurrentDispatcher: {
          current: {
            // Add all the hooks that might be used
            useState: window.React.useState || function(initialState) {
              return [
                typeof initialState === 'function' ? initialState() : initialState,
                function() {}
              ];
            },
            useEffect: window.React.useEffect || function() {},
            useLayoutEffect: window.React.useLayoutEffect || function() {},
            useCallback: window.React.useCallback || function(callback) { return callback; },
            useMemo: window.React.useMemo || function(create) { return create(); },
            useRef: window.React.useRef || function(initialValue) { return { current: initialValue }; },
            useContext: window.React.useContext || function() { return {}; },
            useReducer: window.React.useReducer || function(reducer, initialState) { return [initialState, function() {}]; },
            useImperativeHandle: window.React.useImperativeHandle || function() {},
            useDebugValue: window.React.useDebugValue || function() {},
            useDeferredValue: window.React.useDeferredValue || function(value) { return value; },
            useTransition: window.React.useTransition || function() { return [false, function() {}]; },
            useSyncExternalStore: window.React.useSyncExternalStore || function(subscribe, getSnapshot) { return getSnapshot(); },
            useId: window.React.useId || function() { return 'id-' + Math.random().toString(36).substring(2, 9); },
            useInternalStore: function(subscribe, getSnapshot) { return getSnapshot(); }
          }
        }
      };
      
      console.log('Added internals to existing React object');
    } else if (!window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentDispatcher) {
      // If internals exist but ReactCurrentDispatcher doesn't, add it
      window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentDispatcher = {
        current: {}
      };
      
      console.log('Added ReactCurrentDispatcher to React internals');
    } else if (!window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentDispatcher.current) {
      // If ReactCurrentDispatcher exists but current doesn't, add it
      window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentDispatcher.current = {};
      
      console.log('Added current to ReactCurrentDispatcher');
    }
    
    // Ensure useInternalStore exists on the dispatcher
    const dispatcher = window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentDispatcher.current;
    if (!dispatcher.useInternalStore) {
      dispatcher.useInternalStore = function(subscribe, getSnapshot) {
        return getSnapshot();
      };
      
      console.log('Added useInternalStore to React dispatcher');
    }
  }
  
  console.log('Pre-React compatibility fix completed');
})();
