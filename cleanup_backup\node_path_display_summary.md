# Node Path Display Summary

## Problem Description

The nodes in the mindmap were only displaying the title text, but the requirement was to display both the node path (index) and the title in each node.

## Changes Made

### 1. Updated Node Rendering in MindMapCanvasSimple.tsx

Modified the node rendering code to display both the node path (index) and the title:

```typescript
{/* Node Path (Index) at the top */}
<Text
  text={node.metadata?.nodePath || '1.0'}
  width={node.width}
  height={20}
  y={-node.height/2 + 5}
  align="center"
  verticalAlign="top"
  fontSize={10}
  fontFamily="Arial, sans-serif"
  fontStyle="normal"
  fontVariant="normal"
  fill="#666666"
/>

{/* Node Title */}
<Text
  text={node.text}
  width={node.width}
  height={node.height - 20}
  y={-node.height/2 + 20}
  align="center"
  verticalAlign="middle"
  fontSize={14}
  fontFamily="Arial, sans-serif"
  fontStyle="normal"
  fontVariant="normal"
  fill="#333333"
  padding={8} // Increased padding for better readability
  lineHeight={1.3} // Better line spacing
/>
```

Key changes:
- Added a new Text component to display the node path (index) at the top of the node
- Used the `node.metadata?.nodePath` property to get the node path, with a fallback to '1.0' if not available
- Adjusted the position and size of the title text to accommodate the node path text
- Used a lighter color for the node path text to differentiate it from the title

### 2. Increased Default Node Height in MindMapStore.ts

Increased the default node height to accommodate both the node path and title text:

```typescript
// Default values
export const defaultNodeValues = {
  width: 180, // Increased width for better text display
  height: 90, // Increased height to accommodate node path and title
  color: '#ffffff',
  borderColor: '#2c3e50',
  shape: 'rectangle' as const,
};
```

Changed the height from 70 to 90 pixels to provide more space for both the node path and title text.

### 3. Fixed Node Text Prefix in MindMapStore.ts

Modified the code that adds the index prefix to node text to exclude the root node:

```typescript
// Add the index to the node text if it doesn't already have it
// But only for non-root nodes (nodes with path other than 1.0)
const indexPrefix = nodePath.split('.').pop() || '';
if (nodePath !== '1.0' && !newNode.text.startsWith(`${indexPrefix}.`) && !newNode.text.startsWith(`${indexPrefix} `)) {
  newNode.text = `${indexPrefix}. ${newNode.text}`;
}
```

Added the `nodePath !== '1.0'` condition to ensure that the prefix is only added to non-root nodes.

## Why These Changes Work

1. **Separate Text Components**: By using separate Text components for the node path and title, we can control their positioning and styling independently.

2. **Vertical Positioning**: The node path text is positioned at the top of the node, and the title text is positioned below it, creating a clear visual hierarchy.

3. **Visual Differentiation**: The node path text is smaller and lighter in color than the title text, making it clear which is which.

4. **Increased Node Height**: The increased node height provides enough space for both the node path and title text without overcrowding.

5. **Proper Root Node Handling**: By excluding the root node from having a prefix added to its text, we ensure that the root node displays correctly.

## Testing Instructions

To verify the changes:

1. Start the application using `run_setup.ps1`
2. Open the application in your browser at http://localhost:5173/
3. Select "mindmap" from the intention dropdown
4. Verify that each node now displays both the node path (index) and title
5. Verify that the root node displays "1.0" as its path and the title without a prefix
6. Create a new node and verify that it also displays both the node path and title
7. Double-click on a node to open the NodeBox and verify that the title displayed in the NodeBox matches the title displayed in the node

## Expected Results

- Each node should display the node path (index) at the top in a smaller, lighter font
- Each node should display the title below the node path in a larger, darker font
- The root node should display "1.0" as its path and the title without a prefix
- Child nodes should display their path (e.g., "1.1", "1.2", etc.) and their title with the appropriate prefix
- The nodes should be tall enough to accommodate both the node path and title text without overcrowding
