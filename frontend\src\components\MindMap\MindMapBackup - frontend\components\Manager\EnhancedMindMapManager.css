/* Enhanced MindMap Manager Styles */
/* Removing all imports and hardcoding values */

.mindmap-manager-paper {
  z-index: 1800;
  position: absolute;
  transition: none !important; /* Required for React-Draggable to work properly */
  overflow: visible !important;
}

.mindmap-manager-dialog {
  display: flex;
  flex-direction: column;
  min-width: 280px;
  height: 100%;
  width: 100%;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.mindmap-manager-dialog.collapsed {
  height: 40px;
}

/* Dialog Header */
.manager-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #000000; /* Hardcoded to black instead of using CSS variable */
  color: white;
  padding: 8px 12px;
  cursor: move;
  user-select: none;
  font-size: 14px;
  font-weight: 500;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.manager-dialog-header-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.manager-dialog-header-logo {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  user-select: none;
}

.manager-dialog-header-text {
  font-size: 14px;
  font-weight: 500;
  user-select: none;
}

.manager-dialog-header-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
}

.manager-dialog-header-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 16px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  border-radius: 3px;
}

.manager-dialog-header-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.manager-dialog-close-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 20px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  border-radius: 3px;
}

.manager-dialog-close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Dialog Content */
.mindmap-manager-content {
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
  height: calc(100% - 40px);
  position: relative;
}

/* Stats Container */
.mindmap-stats-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  background-color: #f3f4f6;
  padding: 8px;
  border-radius: 6px;
  font-size: 13px;
}

.mindmap-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-weight: 500;
  color: #4b5563;
}

.stat-value {
  font-weight: 600;
  color: #111827;
}

/* Control Groups */
.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 16px;
}

.control-group-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.control-section {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.manager-control-button {
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 13px;
  cursor: pointer;
  color: #111827;
  transition: all 0.2s;
  flex: 1;
  min-width: 110px;
}

.manager-control-button:hover {
  background-color: #e5e7eb;
}

.button-icon {
  font-size: 14px;
}

.button-text {
  font-weight: 500;
}

/* Settings Panel */
.settings-panel {
  display: flex;
  flex-direction: column;
  gap: 12px;
  background-color: #f9fafb;
  padding: 10px;
  border-radius: 6px;
  margin-top: 4px;
}

.settings-button {
  background-color: #eff6ff;
  border: 1px solid #bfdbfe;
  color: #1d4ed8;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.settings-button:hover {
  background-color: #dbeafe;
}

.llm-settings {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.llm-model-select {
  padding: 6px;
  border-radius: 4px;
  border: 1px solid #d1d5db;
  font-size: 13px;
  background-color: white;
}

/* Resize Handle */
.manager-resize-handle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 16px;
  height: 16px;
  cursor: nwse-resize;
  z-index: 100;
}

.manager-resize-handle::after {
  content: "";
  position: absolute;
  right: 3px;
  bottom: 3px;
  width: 8px;
  height: 8px;
  border-right: 2px solid #9ca3af;
  border-bottom: 2px solid #9ca3af;
  pointer-events: none;
}

/* Transparent Selection (used during dragging) */
.react-draggable-transparent-selection * {
  user-select: none !important;
  -webkit-user-select: none !important;
}

/* Add CSS for the control-section-full class */
.control-section-full {
  margin-bottom: 12px;
  width: 100%;
}

/* Update styles for better integration */
.control-group {
  margin-bottom: 16px;
}

.control-group-title {
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
}

/* Make sure our layout selector styles don't clash */
.layout-selector {
  background-color: transparent;
  padding: 0;
  margin-top: 0;
}

/* Adjust button styles for consistency */
.apply-layout-button.manager-control-button {
  height: 32px;
  padding: 0 10px;
}

/* Ensure select takes proper space */
.layout-strategy-select {
  height: 32px;
}

/* New Badge for compact layout option */
.compact-layout-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.new-badge {
  background-color: #f43f5e;
  color: white;
  font-size: 10px;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 10px;
  display: inline-block;
  line-height: 1;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
} 