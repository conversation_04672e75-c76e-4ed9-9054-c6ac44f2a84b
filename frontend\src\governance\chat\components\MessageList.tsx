import React, { useState, useEffect, useCallback, useRef } from 'react';
import { ChatMessageWithActions } from '../hooks/useChat';
import { SuggestedAction, ActionTypeEnum, ResponseTypeEnum } from '../../../services/api/GovernanceLLM.ts';
import { MessageStatusValues } from '../../../types/MessageStatus.ts';
// Import only the needed utility, removing deprecated functions
import { safeParseJson } from '../../../components/MindMap/utils/ManualJsonProcessor.ts';
import { useMindMapStore } from '../../../components/MindMap/core/state/MindMapStore.ts';
import { Tooltip, Button, Typography } from '@mui/material';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';

interface MessageProps {
  messages: any[]; // Use the appropriate type here
  onAction?: (action: any) => void;
  onBuildMindmap?: () => void;
}

const MessageList: React.FC<MessageProps> = ({
  messages,
  onAction,
  onBuildMindmap
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [showScrollButton, setShowScrollButton] = useState(false);

  // Scroll to bottom whenever messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const div = e.currentTarget;
    const isScrolledUp = div.scrollHeight - div.scrollTop - div.clientHeight > 100;
    setShowScrollButton(isScrolledUp && messages.length > 2);
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="message-list" onScroll={handleScroll}>
      {messages.map((message, index) => (
        <div 
          key={index} 
          className={`message ${message.sender === 'user' ? 'user' : 'assistant'}`}
        >
          {message.text}
          
          {message.suggestedActions && message.suggestedActions.length > 0 && (
            <div className="message-actions">
              {message.suggestedActions.map((action, actionIndex) => (
                <button 
                  key={actionIndex}
                  className="action-button"
                  onClick={() => onAction && onAction(action)}
                >
                  {action.label || 'Action'}
                </button>
              ))}
            </div>
          )}
        </div>
      ))}
      
      {onBuildMindmap && (
        <Button 
          className="build-mindmap-button"
          onClick={onBuildMindmap}
          variant="contained"
        >
          Build Mindmap
        </Button>
      )}
      
      {showScrollButton && (
        <Button
          onClick={scrollToBottom}
          variant="contained"
          size="small"
          style={{
            position: 'absolute',
            bottom: '16px',
            right: '16px',
            borderRadius: '50%',
            minWidth: '40px',
            width: '40px',
            height: '40px'
          }}
        >
          <ArrowDownwardIcon />
        </Button>
      )}
      
      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList; 