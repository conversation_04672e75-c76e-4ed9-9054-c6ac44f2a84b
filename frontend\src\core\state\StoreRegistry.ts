/**
 * StoreRegistry
 * 
 * A central registry for all stores in the application.
 * This helps avoid circular dependencies between stores and components.
 * 
 * The registry provides a way to:
 * 1. Register stores with unique IDs
 * 2. Retrieve stores by ID
 * 3. Check if a store exists
 * 4. Remove stores when they're no longer needed
 * 
 * This is a singleton that can be imported anywhere in the application.
 */

import { create } from 'zustand';

// Store registry to avoid circular dependencies
interface StoreRegistry {
  // Map of store ID to store instance
  stores: Record<string, any>;
  
  // Active store ID for each store type
  activeIds: Record<string, string | null>;
  
  // Actions
  registerStore: (type: string, id: string, store: any) => void;
  getStore: (type: string, id: string) => any;
  hasStore: (type: string, id: string) => boolean;
  removeStore: (type: string, id: string) => void;
  
  // Active store management
  setActiveStoreId: (type: string, id: string | null) => void;
  getActiveStoreId: (type: string) => string | null;
  getActiveStore: (type: string) => any | null;
}

export const useStoreRegistry = create<StoreRegistry>((set, get) => ({
  // Initial state
  stores: {},
  activeIds: {},
  
  // Register a store with a type and ID
  registerStore: (type, id, store) => set(state => {
    // Create the type key if it doesn't exist
    const typeStores = state.stores[type] || {};
    
    // Add the store to the type
    return {
      stores: {
        ...state.stores,
        [type]: {
          ...typeStores,
          [id]: store
        }
      }
    };
  }),
  
  // Get a store by type and ID
  getStore: (type, id) => {
    const state = get();
    return state.stores[type]?.[id] || null;
  },
  
  // Check if a store exists
  hasStore: (type, id) => {
    const state = get();
    return !!state.stores[type]?.[id];
  },
  
  // Remove a store
  removeStore: (type, id) => set(state => {
    // If the type doesn't exist, return the current state
    if (!state.stores[type]) return state;
    
    // Create a copy of the type stores without the removed store
    const { [id]: _, ...restTypeStores } = state.stores[type];
    
    // Update the active ID if it's the one being removed
    let newActiveIds = state.activeIds;
    if (state.activeIds[type] === id) {
      newActiveIds = {
        ...state.activeIds,
        [type]: null
      };
    }
    
    return {
      stores: {
        ...state.stores,
        [type]: restTypeStores
      },
      activeIds: newActiveIds
    };
  }),
  
  // Set the active store ID for a type
  setActiveStoreId: (type, id) => set(state => ({
    activeIds: {
      ...state.activeIds,
      [type]: id
    }
  })),
  
  // Get the active store ID for a type
  getActiveStoreId: (type) => {
    const state = get();
    return state.activeIds[type] || null;
  },
  
  // Get the active store for a type
  getActiveStore: (type) => {
    const state = get();
    const activeId = state.activeIds[type];
    if (!activeId) return null;
    return state.stores[type]?.[activeId] || null;
  }
}));

// Store types
export enum StoreType {
  MIND_MAP = 'mindMap',
  CHAT_FORK = 'chatFork',
  MIND_SHEET = 'mindSheet'
}

// Helper functions for common operations
export const registerStore = (type: string, id: string, store: any) => {
  useStoreRegistry.getState().registerStore(type, id, store);
};

export const getStore = (type: string, id: string) => {
  return useStoreRegistry.getState().getStore(type, id);
};

export const hasStore = (type: string, id: string) => {
  return useStoreRegistry.getState().hasStore(type, id);
};

export const removeStore = (type: string, id: string) => {
  useStoreRegistry.getState().removeStore(type, id);
};

export const setActiveStoreId = (type: string, id: string | null) => {
  useStoreRegistry.getState().setActiveStoreId(type, id);
};

export const getActiveStoreId = (type: string) => {
  return useStoreRegistry.getState().getActiveStoreId(type);
};

export const getActiveStore = (type: string) => {
  return useStoreRegistry.getState().getActiveStore(type);
};
