/**
 * ChatHeader.tsx
 * 
 * Header component for the governance agent.
 */

import React from 'react';
import './ChatHeader.css';

interface ChatHeaderProps {
  title: string;
  onClose: () => void;
  onCollapse?: () => void;
  isCollapsed?: boolean;
  onRestorePosition?: () => void;
  onRestoreSize?: () => void;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
  title,
  onClose,
  onCollapse,
  isCollapsed,
  onRestorePosition,
  onRestoreSize
}) => {
  return (
    <div className="governance-chat-header">
      <div className="header-title">
        <span>{title}</span>
      </div>
      <div className="header-actions">
        {onRestorePosition && (
          <button
            className="header-button"
            onClick={onRestorePosition}
            title="Restore Position"
          >
            <span className="icon-restore-position">⊕</span>
          </button>
        )}
        {onRestoreSize && (
          <button
            className="header-button"
            onClick={onRestoreSize}
            title="Restore Size"
          >
            <span className="icon-restore-size">⊡</span>
          </button>
        )}
        {onCollapse && (
          <button
            className="header-button"
            onClick={onCollapse}
            title={isCollapsed ? 'Expand' : 'Collapse'}
          >
            {isCollapsed ? '▲' : '▼'}
          </button>
        )}
        <button
          className="header-button"
          onClick={onClose}
          title="Close"
        >
          ×
        </button>
      </div>
    </div>
  );
};

export default ChatHeader;
