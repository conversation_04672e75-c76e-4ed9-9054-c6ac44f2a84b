// Fix for initReactCompatibility issue
(function() {
  console.log('Applying fix for initReactCompatibility...');
  
  // Create a global object to store our compatibility functions
  window.ReactCompatibilityUtils = window.ReactCompatibilityUtils || {};
  
  // Add the initReactCompatibility function to the global object
  window.ReactCompatibilityUtils.initReactCompatibility = function() {
    console.log('Global initReactCompatibility called');
    
    try {
      // Ensure React is available
      if (!window.React) {
        console.warn('React not found, creating mock React');
        window.React = {
          createElement: function() { return {}; },
          Fragment: Symbol('Fragment'),
          StrictMode: Symbol('StrictMode'),
          useState: function(initialState) {
            return [
              typeof initialState === 'function' ? initialState() : initialState,
              function() {}
            ];
          },
          useEffect: function() {},
          useContext: function() { return {}; },
          useReducer: function(reducer, initialState) { return [initialState, function() {}]; },
          useCallback: function(callback) { return callback; },
          useMemo: function(factory) { return factory(); },
          useRef: function(initialValue) { return { current: initialValue }; }
        };
      }
      
      // Ensure React internals exist
      if (!window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) {
        console.warn('React internals not found, creating mock internals');
        window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = {
          ReactCurrentDispatcher: {
            current: {}
          }
        };
      }
      
      // Ensure ReactCurrentDispatcher exists
      const internals = window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
      if (!internals.ReactCurrentDispatcher) {
        console.warn('ReactCurrentDispatcher not found, creating mock dispatcher');
        internals.ReactCurrentDispatcher = {
          current: {}
        };
      }
      
      // Ensure current exists
      if (!internals.ReactCurrentDispatcher.current) {
        console.warn('Current dispatcher not found, creating mock current');
        internals.ReactCurrentDispatcher.current = {};
      }
      
      // Add useInternalStore to the dispatcher
      const dispatcher = internals.ReactCurrentDispatcher.current;
      if (!dispatcher.useInternalStore) {
        console.log('Adding useInternalStore to dispatcher');
        dispatcher.useInternalStore = function(subscribe, getSnapshot) {
          return getSnapshot();
        };
      }
      
      console.log('React compatibility initialized successfully');
      return true;
    } catch (error) {
      console.error('Error initializing React compatibility:', error);
      return false;
    }
  };
  
  // Create a mock module system
  window.mockModules = window.mockModules || {};
  
  // Add the reactCompatibility module to the mock module system
  window.mockModules['./utils/reactCompatibility'] = {
    initReactCompatibility: window.ReactCompatibilityUtils.initReactCompatibility,
    createInternalStoreCompat: function() {
      console.log('Mock createInternalStoreCompat called');
      return {};
    }
  };
  
  // Override require to use our mock modules
  const originalRequire = window.require || function() {};
  window.require = function(moduleName) {
    console.log('Require called for:', moduleName);
    
    // Check if we have a mock for this module
    if (window.mockModules[moduleName]) {
      console.log('Using mock module for:', moduleName);
      return window.mockModules[moduleName];
    }
    
    // If it's the reactCompatibility module, return our mock
    if (moduleName === './utils/reactCompatibility') {
      console.log('Using mock reactCompatibility module');
      return window.mockModules['./utils/reactCompatibility'];
    }
    
    // Otherwise, use the original require
    try {
      return originalRequire(moduleName);
    } catch (error) {
      console.warn('Error requiring module:', moduleName, error);
      return {};
    }
  };
  
  console.log('Fix for initReactCompatibility applied successfully');
})();
