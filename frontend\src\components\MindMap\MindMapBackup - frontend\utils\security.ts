/**
 * Utility functions for input sanitization and security
 */

/**
 * Sanitizes user input to prevent XSS and other injection attacks
 * @param input The raw user input to sanitize
 * @returns The sanitized input string
 */
export function sanitizeInput(input: string): string {
  if (!input) return '';
  
  // Remove any HTML tags
  let sanitized = input.replace(/<[^>]*>/g, '');
  
  // Remove potentially dangerous characters
  sanitized = sanitized.replace(/[<>]/g, '');
  
  // Trim whitespace
  sanitized = sanitized.trim();
  
  return sanitized;
}

/**
 * Validates that a string contains only safe characters
 * @param input The string to validate
 * @returns True if the string is safe, false otherwise
 */
export function isValidInput(input: string): boolean {
  if (!input) return false;
  
  // Check for potentially dangerous patterns
  const dangerousPatterns = [
    /<script/i,
    /javascript:/i,
    /data:/i,
    /vbscript:/i,
    /on\w+=/i
  ];
  
  return !dangerousPatterns.some(pattern => pattern.test(input));
} 