import React, { useState } from 'react';
import { createMindsheet } from '../../core/mindsheet/createMindsheet';
import { useMindBookStore } from '../../core/state/MindBookStore';
import { getMindMapStore } from '../../core/state/MindMapStoreFactory';

interface IntentSelectorProps {
  onIntentChange?: (intent: string) => void;
}

const IntentSelector: React.FC<IntentSelectorProps> = ({ onIntentChange }) => {
  const [selectedIntent, setSelectedIntent] = useState<string>('exploratory');

  // Handle intent change
  const handleIntentChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const intent = event.target.value;
    console.log('IntentSelector: Intent changed to:', intent);

    // Update the selected intent
    setSelectedIntent(intent);

    // Call the callback if provided
    if (onIntentChange) {
      onIntentChange(intent);
    }

    // If teleological is selected, create a mindsheet and mindmap
    if (intent === 'teleological') {
      console.log('IntentSelector: Creating mindsheet for teleological intent');

      try {
        // Get the MindBookStore
        const mindBookStore = useMindBookStore.getState();

        // Check if a teleological mindmap already exists
        const sheets = mindBookStore.sheets;

        // Check if there's already a teleological mindmap sheet
        const existingTeleologicalSheet = Object.values(sheets).find(
          sheet => sheet.type === 'mindmap' && sheet.content?.intent === 'teleological'
        );

        if (existingTeleologicalSheet) {
          console.log('IntentSelector: Teleological mindmap already exists, activating it:', existingTeleologicalSheet.id);
          mindBookStore.setActiveSheet(existingTeleologicalSheet.id);
          return;
        }

        // Create a simple MBCP structure with just a root node
        // Make sure to use the format expected by initializeMindMapForSheet
        const mbcpData = {
          intent: 'teleological',
          text: 'Teleological Mindmap',
          description: 'A mindmap for teleological intent',
          created: new Date().toISOString(),
          mindmap: {
            root: {
              text: 'Teleological Mindmap',
              description: 'Click to edit this mindmap',
              metadata: {
                intent: 'teleological',
                tags: ['mindmap']
              },
              children: []
            }
          }
        };

        // 1. Create the mindsheet
        console.log('IntentSelector: Creating mindsheet in MindBookStore');
        const sheetId = mindBookStore.createMindMapSheet('Teleological Mindmap', mbcpData);

        if (!sheetId) {
          console.error('IntentSelector: Failed to create mindsheet');
          return;
        }

        console.log('IntentSelector: Successfully created mindsheet with ID:', sheetId);

        // 2. Set it as the active sheet
        console.log('IntentSelector: Setting as active sheet');
        mindBookStore.setActiveSheet(sheetId);

        // 3. Initialize the mindmap using the sheet-specific store
        console.log('IntentSelector: Initializing mindmap with sheet-specific store');

        // Get the sheet-specific store
        const sheetStore = getMindMapStore(sheetId);

        // Initialize the store with window dimensions
        sheetStore.getState().initialize(window.innerWidth, window.innerHeight);

        // Create a new project with the root node
        const rootNodeId = sheetStore.getState().createNewProject('Teleological Mindmap');

        if (rootNodeId) {
          console.log('IntentSelector: Created root node with ID:', rootNodeId);

          // Apply layout THROUGH GOVERNANCE - user-initiated request
          const success = await sheetStore.getState().updateLayout('leftToRight', 'user');
          if (success) {
            console.log('[IntentSelector] Layout applied successfully through governance');
          } else {
            console.warn('[IntentSelector] Layout request was rejected by governance');
          }
        }

        // 4. Layout is complete, mindsheet is active
        console.log('IntentSelector: Teleological mindmap creation complete');

        // Note: Both automatic (AI-generated) and manual (teleological) workflows now
        // converge to use the same MindSheet framework post-creation. The MindSheet 
        // handles all mindmap operations including node interaction, editing, and display.
        // This eliminates dual rendering and ensures consistent behavior.

      } catch (error) {
        console.error('IntentSelector: Error creating mindsheet:', error);
      }
    }
  };

  return (
    <div className="intent-selector">
      <label htmlFor="intent-select">Intent:</label>
      <select
        id="intent-select"
        value={selectedIntent}
        onChange={handleIntentChange}
      >
        <option value="exploratory">Exploratory</option>
        <option value="factual">Factual</option>
        <option value="teleological">Teleological</option>
        <option value="situational">Situational</option>
      </select>
    </div>
  );
};

export default IntentSelector;
