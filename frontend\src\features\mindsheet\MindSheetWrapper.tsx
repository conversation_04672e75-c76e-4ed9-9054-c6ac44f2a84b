/**
 * MindSheetWrapper Component
 *
 * A wrapper component that handles all store access for MindSheet.
 * This component ensures that Zustand store hooks are called at the top level
 * and passes down store state and actions as props to the MindSheet component.
 *
 * This follows the container/presentational component pattern where:
 * - MindSheetWrapper (container) handles store access and data fetching
 * - MindSheet (presentational) focuses on rendering based on props
 */

import React, { useEffect } from 'react';
import MindSheet, { MindSheetProps } from './MindSheet';
import { MindSheetContentType } from '../../core/state/StoreTypes';
import RegistrationManager, { EventType } from '../../core/services/RegistrationManager';
import {
  getMindMapStore,
  getMindBookStore,
  getChatForkStore,
  getSheetMindMapStore,
  hasSheetMindMapStore
} from '../../core/services/StoreService';

interface MindSheetWrapperProps {
  id: string;
  title: string;
  contentType: MindSheetContentType;
  isActive: boolean;
  content: any;
  onActivate?: () => void;
}

const MindSheetWrapper: React.FC<MindSheetWrapperProps> = (props) => {
  // Get all stores using the StoreService
  const mindMapStore = getMindMapStore();
  const mindBookStore = getMindBookStore();
  const chatForkStore = getChatForkStore();

  // Get the sheet-specific store
  const sheetStore = getSheetMindMapStore(props.id);

  // Log when the wrapper mounts and unmounts
  useEffect(() => {
    console.log('MindSheetWrapper: Mounted for sheet:', props.id, 'contentType:', props.contentType);

    // Ensure the store exists for this sheet
    if (props.contentType === MindSheetContentType.MINDMAP) {
      if (!hasSheetMindMapStore(props.id)) {
        console.log('MindSheetWrapper: Creating new store for sheet:', props.id);
      } else {
        console.log('MindSheetWrapper: Using existing store for sheet:', props.id);
      }

      // Register the sheet creation event if it's active
      if (props.isActive) {
        RegistrationManager.registerEvent(EventType.SHEET_ACTIVATED, {
          id: props.id,
          type: props.contentType.toLowerCase()
        });
      }
    }

    return () => {
      console.log('MindSheetWrapper: Unmounted for sheet:', props.id);
    };
  }, [props.id, props.contentType, props.isActive]);

  // Pass down all props and store references to the MindSheet component
  return (
    <MindSheet
      {...props}
      mindMapStore={mindMapStore}
      mindBookStore={mindBookStore}
      chatForkStore={chatForkStore}
      sheetStore={sheetStore}
    />
  );
};

export default MindSheetWrapper;
