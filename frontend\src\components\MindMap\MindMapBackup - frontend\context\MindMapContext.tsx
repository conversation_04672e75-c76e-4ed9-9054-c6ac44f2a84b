// MindMap/context/MindMapContext.tsx
import React, { createContext, useContext, useState, ReactNode, useEffect, useCallback } from 'react';
import { Node, Connection, Direction, Project } from '../types';

// Define the context shape
interface MindMapContextType {
  // Nodes and connections
  nodes: Node[];
  setNodes: React.Dispatch<React.SetStateAction<Node[]>>;
  connections: Connection[];
  setConnections: React.Dispatch<React.SetStateAction<Connection[]>>;
  
  // Node selection and editing
  selectedId: string | null;
  setSelectedId: React.Dispatch<React.SetStateAction<string | null>>;
  nodeText: string;
  setNodeText: React.Dispatch<React.SetStateAction<string>>;
  nodeColor: string;
  setNodeColor: React.Dispatch<React.SetStateAction<string>>;
  
  // UI state
  showNodeDialog: boolean;
  setShowNodeDialog: React.Dispatch<React.SetStateAction<boolean>>;
  showProjectDialog: boolean;
  setShowProjectDialog: React.Dispatch<React.SetStateAction<boolean>>;
  showDesignControls: boolean;
  setShowDesignControls: React.Dispatch<React.SetStateAction<boolean>>;
  
  // Canvas state
  scale: number;
  setScale: React.Dispatch<React.SetStateAction<number>>;
  position: { x: number; y: number };
  setPosition: React.Dispatch<React.SetStateAction<{ x: number; y: number }>>;
  direction: Direction;
  setDirection: React.Dispatch<React.SetStateAction<Direction>>;
  
  // Project state
  projectName: string;
  setProjectName: React.Dispatch<React.SetStateAction<string>>;
  projects: Project[];
  setProjects: React.Dispatch<React.SetStateAction<Project[]>>;
  lastSaved: string | null;
  setLastSaved: React.Dispatch<React.SetStateAction<string | null>>;
  
  // Node management functions
  addNode: (parentId: string) => string | undefined;
  deleteNode: (id: string) => void;
  updateNodePosition: (id: string, x: number, y: number) => void;
  updateNode: (nodeId: string, updates: Partial<Node>) => void;
  createIndependentNode: (x: number, y: number) => string;
}

// Create the context with a default undefined value
const MindMapContext = createContext<MindMapContextType | undefined>(undefined);

// Provider component
export const MindMapProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Nodes and connections - start with empty arrays
  const [nodes, setNodes] = useState<Node[]>([]);
  const [connections, setConnections] = useState<Connection[]>([]);
  
  // Node selection and editing
  const [selectedId, setSelectedId] = useState<string | null>(null); // No default selection
  const [nodeText, setNodeText] = useState<string>('');
  const [nodeColor, setNodeColor] = useState<string>('#2563eb');
  
  // UI state
  const [showNodeDialog, setShowNodeDialog] = useState<boolean>(false);
  const [showProjectDialog, setShowProjectDialog] = useState<boolean>(false);
  const [showDesignControls, setShowDesignControls] = useState<boolean>(false);
  
  // Canvas state - initial position will be set by the Canvas component
  const [scale, setScale] = useState<number>(1);
  // Don't set any initial position here, let the Canvas component handle it
  const [position, setPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const [direction, setDirection] = useState<Direction>('right');
  
  // Project state
  const [projectName, setProjectName] = useState<string>('New Mind Map');
  const [projects, setProjects] = useState<Project[]>([]);
  const [lastSaved, setLastSaved] = useState<string | null>(null);
  
  // Initialize canvas position when component mounts
  useEffect(() => {
    // Start with center position, will be adjusted by MindMapCanvas
    setPosition({
      x: 0,
      y: 0
    });
  }, []);
  
  // Load selected node text when selectedId changes
  useEffect(() => {
    if (selectedId) {
      const selectedNode = nodes.find(node => node.id === selectedId);
      if (selectedNode) {
        setNodeText(selectedNode.text);
        setNodeColor(selectedNode.color || '#2563eb'); // Add default fallback color
      }
    }
  }, [selectedId, nodes]);
  
  // Update node position
  const updateNodePosition = (id: string, x: number, y: number) => {
    console.log(`Updating node ${id} position to (${x}, ${y})`);
    
    // Create a clone of the nodes array to ensure state update is triggered
    const updatedNodes = [...nodes];
    const nodeIndex = updatedNodes.findIndex(node => node.id === id);
    
    if (nodeIndex !== -1) {
      // Create a new object for the updated node to ensure state change is detected
      updatedNodes[nodeIndex] = {
        ...updatedNodes[nodeIndex],
        x,
        y
      };
      setNodes(updatedNodes);
    } else {
      console.warn(`Node with id ${id} not found`);
    }
  };

  // Add node
  const addNode = (parentId: string) => {
    const parentNode = nodes.find(node => node.id === parentId);
    if (!parentNode) return;

    const newId = `node_${Date.now()}`;
    const childCount = connections.filter(conn => conn.from === parentId).length;
    const spacing = 200; // Horizontal spacing between parent and child
    const verticalSpacing = 80; // Vertical spacing between siblings

    let newX = parentNode.x;
    let newY = parentNode.y;

    // Calculate position based on direction and number of existing children
    // This positions children at appropriate angles around their parent
    switch (direction) {
      case 'right':
        newX = parentNode.x + spacing;
        // If multiple children, stagger them vertically
        if (childCount > 0) {
          const offset = childCount * verticalSpacing;
          newY = parentNode.y + (offset / 2) - ((childCount - 1) * verticalSpacing / 2);
        }
        break;
      case 'down':
        newY = parentNode.y + spacing;
        // If multiple children, stagger them horizontally
        if (childCount > 0) {
          const offset = childCount * verticalSpacing;
          newX = parentNode.x + (offset / 2) - ((childCount - 1) * verticalSpacing / 2);
        }
        break;
      case 'left':
        newX = parentNode.x - spacing;
        // If multiple children, stagger them vertically
        if (childCount > 0) {
          const offset = childCount * verticalSpacing;
          newY = parentNode.y + (offset / 2) - ((childCount - 1) * verticalSpacing / 2);
        }
        break;
      case 'up':
        newY = parentNode.y - spacing;
        // If multiple children, stagger them horizontally
        if (childCount > 0) {
          const offset = childCount * verticalSpacing;
          newX = parentNode.x + (offset / 2) - ((childCount - 1) * verticalSpacing / 2);
        }
        break;
    }

    console.log(`Adding node at position (${newX}, ${newY}), direction: ${direction}`);

    const newNode: Node = {
      id: newId,
      text: 'New Node',
      x: newX,
      y: newY,
      width: 120,
      height: 50,
      color: nodeColor,
      shape: 'rectangle',
      borderColor: '#1e40af'
    };

    setNodes(prev => [...prev, newNode]);
    setConnections(prev => [...prev, {
      from: parentId,
      to: newId,
      style: 'straight',
      thickness: 2,
      color: '#64748b'
    }]);

    return newId;
  };
  
  const deleteNode = (id: string) => {
    // Don't delete if it's a connection ID
    if (id.startsWith('conn-')) {
      const [_, fromId, toId] = id.split('-');
      setConnections(prev => prev.filter(conn => !(conn.from === fromId && conn.to === toId)));
      setSelectedId(null);
      return;
    }
    
    if (id === 'root') return; // Prevent deleting the root node
    
    // Get all child nodes recursively
    const getChildIds = (nodeId: string): string[] => {
      const directChildren = connections
        .filter(conn => conn.from === nodeId)
        .map(conn => conn.to);
      
      const allChildren = [...directChildren];
      
      directChildren.forEach(childId => {
        allChildren.push(...getChildIds(childId));
      });
      
      return allChildren;
    };
    
    const childIds = getChildIds(id);
    const allIdsToDelete = [id, ...childIds];
    
    // Remove nodes and connections
    setNodes(prev => prev.filter(node => !allIdsToDelete.includes(node.id)));
    setConnections(prev => prev.filter(conn => 
      !allIdsToDelete.includes(conn.from) && !allIdsToDelete.includes(conn.to)
    ));
    
    if (selectedId && allIdsToDelete.includes(selectedId)) {
      setSelectedId(null);
    }
  };
  
  // Create independent node
  const createIndependentNode = (x: number, y: number) => {
    const newId = `node_${Date.now()}`;
    
    const newNode: Node = {
      id: newId,
      text: 'New Node',
      x,
      y,
      width: 120,
      height: 50,
      color: nodeColor,
      shape: 'rectangle',
      borderColor: '#1e40af'
    };

    setNodes(prev => [...prev, newNode]);
    setSelectedId(newId);
    setShowNodeDialog(true);
    
    return newId;
  };
  
  const updateNode = useCallback((nodeId: string, updates: Partial<Node>) => {
    setNodes(prevNodes => 
      prevNodes.map(node => 
        node.id === nodeId ? { ...node, ...updates } : node
      )
    );
  }, []);
  
  // Context value
  const contextValue: MindMapContextType = {
    nodes,
    setNodes,
    connections,
    setConnections,
    selectedId,
    setSelectedId,
    nodeText,
    setNodeText,
    nodeColor,
    setNodeColor,
    showNodeDialog,
    setShowNodeDialog,
    showProjectDialog,
    setShowProjectDialog,
    showDesignControls,
    setShowDesignControls,
    scale,
    setScale,
    position,
    setPosition,
    direction,
    setDirection,
    projectName,
    setProjectName,
    projects,
    setProjects,
    lastSaved,
    setLastSaved,
    addNode,
    deleteNode,
    updateNodePosition,
    updateNode,
    createIndependentNode
  };
  
  return (
    <MindMapContext.Provider value={contextValue}>
      {children}
    </MindMapContext.Provider>
  );
};

// Custom hook to use the context
export const useMindMap = () => {
  const context = useContext(MindMapContext);
  if (context === undefined) {
    throw new Error('useMindMap must be used within a MindMapProvider');
  }
  return context;
};
