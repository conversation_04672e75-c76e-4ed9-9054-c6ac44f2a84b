/**
 * StoreTypes.ts
 * 
 * This file contains shared types and interfaces used across different stores.
 * By centralizing these types, we avoid circular dependencies between store files.
 */

// Sheet content types
export enum MindSheetContentType {
  MINDMAP = 'mindmap',
  CHATFORK = 'chatfork',
  INSTANTIATION = 'instantiation',
  EMPTY = 'empty'
}

// Sheet data interface
export interface SheetData {
  id: string;
  title: string;
  contentType: MindSheetContentType;
  content: any;
  contextSettingsId?: string; // Reference to Context Settings ID
  createdAt?: number;
  updatedAt?: number;
  state?: {
    nodes?: any;
    connections?: any[];
    rootNodeId?: string | null;
    position?: { x: number; y: number };
    scale?: number;
  };
}

// MindMap node interface
export interface MindMapNode {
  id: string;
  text: string;
  x: number;
  y: number;
  width: number;
  height: number;
  color?: string;
  textColor?: string;
  fontSize?: number;
  fontFamily?: string;
  borderColor?: string;
  borderWidth?: number;
  selected?: boolean;
  expanded?: boolean;
  [key: string]: any;
}

// MindMap connection interface
export interface MindMapConnection {
  id: string;
  from: string;
  to: string;
  color?: string;
  width?: number;
  style?: string;
  [key: string]: any;
}

// MindMap state interface
export interface MindMapStateData {
  nodes: Record<string, MindMapNode>;
  connections: MindMapConnection[];
  rootNodeId: string | null;
  position: { x: number; y: number };
  scale: number;
}
