# UI Navigation & User Experience Requirements

## 🏠 **Startup/Welcome Screen**

### Layout
- **Center**: `mindback_logo2.jpg` 
- **Below logo**: "Welcome to MindBack" + "Intelligence Moderation" subtitle
- **Main area**: 
  - **Recent MindBooks** (5 most recent with thumbnails/previews)
  - **Browse All** button → opens full MindBook library
  - **New MindBook** button → creates new MindBook

### Visual Layout
```
┌─────────────────────────────────────────┐
│  [MB Logo]           [Settings] [?]     │
├─────────────────────────────────────────┤
│                                         │
│            [mindback_logo2.jpg]         │
│                                         │
│          Welcome to MindBack            │
│       Intelligence Moderation           │
│                                         │
│  ┌─────────────────────────────────────┐│
│  │        Recent MindBooks             ││
│  │  [Strategy 2025] [Market Analysis]  ││
│  │  [Product Dev]   [Team Planning]    ││
│  │               [Browse All]          ││
│  └─────────────────────────────────────┘│
│                                         │
│              [New MindBook]             │
│                                         │
├─────────────────────────────────────────┤
│ Welcome to MindBack      mindback.ai    │
└─────────────────────────────────────────┘
```

## 📐 **Header Navigation**

### Left Corner: MindBack Logo
- **Function**: Click → Returns to startup/welcome screen
- **Behavior**: Always visible, consistent home button

### Right Corner: Three-Line Menu (☰)
**Current Session Actions:**
- "Save Current MindBook as..." → name dialog
- "Save MindBook" (if already named)

**Open Other MindBooks:**
- **Recent MindBooks** (5 most recent)
- **Browse All MindBooks** → full library view

**Context & Settings:**
- "Context Manager" → manage context settings profiles
- "App Settings" → general preferences

## 🦶 **Enhanced Footer Design**

### Left Side: MindBook Tabs (Excel-style)
```
[Strategic Planning 2025 ×] [Market Analysis ×] [+ New MindBook]
```
- **Active MindBook** highlighted
- **Close buttons (×)** for each MindBook
- **New MindBook** button always available
- **Horizontal scroll** if too many MindBooks open

### Right Side
- Keep "mindback.ai" branding

## 📑 **MindSheet Management**

### MindSheet Tabs (under MindBook tabs)
```
[Overview ×] [Strategy Map ×] [Market Data ×] [+ New Sheet]
```
- **Close buttons (×)** for each MindSheet
- **Color coding** by intention (see below)
- **New Sheet** button always available

### MindSheet Color Coding by Intention
**Very light/subtle tones for minimal visual distinction:**
- 🟦 **MindMap Sheets**: Very light blue (`#F8FBFF`) - Strategic thinking
- 🟩 **ChatFork Sheets**: Very light green (`#F9FDF9`) - Exploratory analysis  
- 🟨 **Instantiation Sheets**: Very light yellow (`#FFFEF8`) - Implementation/action
- 🟪 **Template Sheets**: Very light purple (`#FDFAFD`) - Frameworks/tools
- ⚪ **Empty/Draft Sheets**: Light gray (`#F5F5F5`) - Uninitialized

**Visual Indicators:**
- **Tab background color** matches intention (very subtle)
- **Border left** with slightly stronger color accent (still subtle)
- **Sheet icon** (optional) for quick recognition

## 🗑️ **Deletion Workflows**

### MindBook Deletion
- **Access**: Through ☰ menu → "Manage MindBooks"
- **Protection**: Confirmation dialog with "This cannot be undone"
- **Behavior**: Closes tab and removes from recent list

### MindSheet Deletion
- **Access**: Right-click on sheet tab → Context menu OR close button (×)
- **Confirmation dialog**: 
  - "Delete '[Sheet Name]'? This cannot be undone."
  - "Move to Trash" vs "Cancel"
- **Protection Rules**:
  - Cannot delete last sheet in MindBook
  - Warning for sheets with content vs empty sheets
  - Bulk deletion protection (max 3 at once)

### Optional: Trash System
- **Recovery period**: 30-day retention
- **Access**: Through app settings
- **Auto-cleanup**: After 30 days

## 🆕 **New MindBook Creation Flow**

### Dialog Components
1. **MindBook name field** (required)
2. **Context Settings dropdown** (existing ones + "Create New")
3. **Description field** (optional)

### Context Integration
- **If "Create New" context**: Opens context settings creator
- **Default naming**: Suggest context settings name for MindBook
- **Association**: MindBook links to chosen/created context settings

## 📊 **Event Registration (Reduced Scope)**

### Critical Events to Track ✅
- **Creation**: New MindBook, MindSheet, Node, ChatFork
- **Deletion**: Remove MindBook, MindSheet, Node
- **Save Actions**: Named saves, exports
- **Navigation**: Switch MindBook, Sheet transitions
- **Agentic Interactions**: LLM requests, responses, implementations

### Events to STOP Tracking ❌
- **Governance box moves** (too noisy)
- **Mouse movements, hover states**
- **Auto-saves** (unless failure)
- **UI panel opens/closes**
- **Selection changes** (unless leading to action)

## 🔄 **User Flow Examples**

### First Time User
1. Opens app → Welcome screen
2. Clicks "New MindBook" 
3. Names it "Strategic Planning 2025"
4. Selects "Create New" context settings
5. Sets up foundational/strategic/operational context
6. Starts working on sheets
7. Footer shows MindBook tab with name

### Returning User
1. Opens app → Welcome screen with recent MindBooks
2. Clicks on "Strategic Planning 2025" from recent
3. App loads that MindBook + associated context settings
4. Footer shows current MindBook tab
5. Can use ☰ → "Save MindBook" to update or "Save As" to create copy

### Multi-MindBook Session
1. User has 3 MindBooks open (tabs in footer)
2. Working on "Strategic Planning 2025" (active tab)
3. Creates new MindMap sheet → light blue color
4. ☰ menu → "Save MindBook" (already named)
5. Switches to "Market Analysis" tab → different context loads
6. Creates ChatFork sheet → light green color

## 🎯 **Future-Proofing Considerations**

### Agentic Data Management
- **Compressed storage** for large agentic conversations
- **Summary nodes** instead of full conversation trees
- **Lazy loading** for historical data
- **Archive old sessions** after 90 days

### MindBook Organization
- **Agentic sessions** as separate sheet type
- **Data correlation** between manual and AI-generated content
- **Version control** for AI-modified content
- **Audit trails** for agentic decision chains

### Multi-MindBook Workflows
- **Compare data** across MindBooks
- **Cross-reference** between projects
- **Template reuse** from one MindBook to another
- **Context switching** preserves per-MindBook settings

## 🚀 **Implementation Priority Order**

1. **Welcome/Startup Screen** component
2. **MindBook creation dialog** with context selection
3. **Header navigation** updates (logo home, ☰ menu)
4. **Footer MindBook tabs** with close buttons
5. **MindSheet color coding** and close buttons
6. **Save workflow** integration through ☰ menu
7. **Deletion workflows** with confirmations
8. **App routing** to handle startup vs working state
9. **Event registration** cleanup and optimization 