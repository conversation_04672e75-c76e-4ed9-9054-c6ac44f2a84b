.hamburger-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.hamburger-menu {
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  width: 350px;
  background: white;
  box-shadow: -4px 0 16px rgba(0, 0, 0, 0.2);
  z-index: 1001;
  display: flex;
  flex-direction: column;
  animation: slideInRight 0.2s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.hamburger-menu-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
  background-color: #f8fafc;
}

.hamburger-menu-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a1a1a;
}

.hamburger-menu-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #64748b;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.hamburger-menu-close:hover {
  background-color: #e2e8f0;
  color: #1a1a1a;
}

.hamburger-menu-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.menu-section {
  border-bottom: 1px solid #f1f5f9;
  padding: 20px;
}

.menu-section:last-child {
  border-bottom: none;
}

.menu-section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0 0 12px 0;
}

.menu-item {
  display: block;
  width: 100%;
  padding: 12px 0;
  background: none;
  border: none;
  text-align: left;
  font-size: 0.875rem;
  color: #374151;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
  margin-bottom: 4px;
}

.menu-item:hover {
  background-color: #f8fafc;
  color: #1a1a1a;
}

.menu-item.primary {
  font-weight: 600;
  color: #3b82f6;
}

.menu-item.primary:hover {
  background-color: #eff6ff;
  color: #2563eb;
}

.menu-item.secondary {
  font-weight: 500;
  color: #6b7280;
  border-top: 1px solid #f1f5f9;
  margin-top: 8px;
  padding-top: 16px;
}

.menu-item:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.menu-item-disabled {
  color: #9ca3af;
  font-size: 0.875rem;
  font-style: italic;
  margin: 8px 0;
}

.recent-mindbooks-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.recent-mindbook-item {
  padding: 8px 0;
}

.recent-mindbook-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.recent-mindbook-name {
  font-weight: 500;
  color: #1a1a1a;
}

.recent-mindbook-date {
  font-size: 0.75rem;
  color: #64748b;
}

/* Save Dialog Styles */
.save-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1002;
}

.save-dialog {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 480px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  animation: fadeInScale 0.2s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.save-dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.save-dialog-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
}

.save-dialog-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #64748b;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.save-dialog-close:hover {
  background-color: #f1f5f9;
  color: #1a1a1a;
}

.save-dialog-content {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.save-dialog-field {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.save-dialog-field label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.save-dialog-field input,
.save-dialog-field textarea {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #1a1a1a;
  transition: border-color 0.2s ease;
}

.save-dialog-field input:focus,
.save-dialog-field textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.save-dialog-field textarea {
  resize: vertical;
  min-height: 80px;
}

.save-dialog-actions {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e2e8f0;
  justify-content: flex-end;
}

.save-dialog-button {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.save-dialog-button.secondary {
  background: white;
  color: #6b7280;
  border-color: #d1d5db;
}

.save-dialog-button.secondary:hover {
  background: #f9fafb;
  color: #374151;
}

.save-dialog-button.primary {
  background: #3b82f6;
  color: white;
}

.save-dialog-button.primary:hover {
  background: #2563eb;
}

.save-dialog-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hamburger-menu {
    width: 100%;
  }
  
  .save-dialog {
    width: 95%;
    margin: 20px;
  }
  
  .save-dialog-actions {
    flex-direction: column-reverse;
  }
  
  .save-dialog-button {
    width: 100%;
    padding: 12px;
  }
} 