# python -m venv .venv
# .venv\Scripts\Activate
# pip install -r requirements.txt

# Core dependencies
fastapi==0.110.0
uvicorn==0.27.1
pydantic>=2.7.0
python-dotenv==1.0.0

# LLM and AI
openai==1.13.3
crewai==0.22.5
tavily-python==0.3.1

# Data processing
numpy==1.26.3
pandas==2.2.0
networkx==3.2.1
python-dateutil==2.8.2
pyyaml==6.0.1

# Web and API
httpx==0.26.0
websockets==12.0
# CORS middleware is part of FastAPI

# Testing
pytest==7.4.4
pytest-asyncio==0.23.4

# Utilities
tqdm==4.66.2
requests==2.31.0

# Visualization
matplotlib==3.8.2
seaborn==0.13.1

# Database
sqlalchemy==2.0.25
alembic==1.13.1

# Document processing
beautifulsoup4==4.12.2
markdown==3.5.2
python-multipart==0.0.9

# Background processing
celery==5.3.6
redis==5.0.1

# For local development
pytest-cov==4.1.0
black==24.1.1
isort==5.13.2
flake8==7.0.0

# For documentation
mkdocs==1.5.3
mkdocs-material==9.5.3

# Note: The following packages have been temporarily removed and can be added back as needed:
# - langchain and related packages
# - sentence-transformers
# - chromadb and vector storage components
# - opentelemetry monitoring tools
# - advanced security (passlib, python-jose, bcrypt)
