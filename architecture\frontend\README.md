# Frontend Architecture Overview

This folder documents the internal structure of the **MindBack frontend**  
(built with Vite + React + TypeScript) using [Dependency Cruiser](https://github.com/sverweij/dependency-cruiser).

---

## 📊 Contents

| File                            | Description                                                  |
|---------------------------------|--------------------------------------------------------------|
| `grouped-deps.svg`              | Full dependency graph grouped by folder                      |
| `tsconfig.depcruise.json`       | Minimal TypeScript config used for analysis                  |
| `dependency-cruiser.config.js`  | Dependency Cruiser config file (rules, options, format)      |
| `README.md`                     | This documentation file                                      |

---

## 🛠 How to regenerate the graph

```bash
npx depcruise --ts-config architecture/tsconfig.depcruise.json \
              --config architecture/dependency-cruiser.config.js \
              --output-type dot frontend/src | dot -Tsvg -o architecture/grouped-deps.svg
