---
# META-PROMPT: Update Existing Workflow Description
# This file contains instructions to incrementally update workflowDescription.yaml

prompt_metadata:
  purpose: "Incrementally update existing workflow documentation for MindBack application"
  target_output: "Updated workflowDescription.yaml"
  analysis_scope: "Delta analysis and incremental workflow updates"
  approach: "Efficient maintenance of existing documentation"

# =====================================================
# UPDATE ANALYSIS REQUIREMENTS
# =====================================================
update_analysis_requirements:
  
  input_data_sources:
    primary: "Current workflowDescription.yaml - Existing workflow documentation"
    secondary: "Latest mvcd.yaml - Current codebase inventory"
    comparison_base: "Previous mvcd.yaml snapshot (if available)"
    context: "Change logs, recent commits, development activity"
  
  delta_analysis_methodology:
    1. "Compare current mvcd.yaml with previous version to identify changes"
    2. "Identify new files, modified files, and deleted files"
    3. "Analyze impact of changes on existing workflows"
    4. "Detect new workflow patterns or user interactions"
    5. "Update dead code analysis with recent changes"
    6. "Recalculate coverage metrics incrementally"

# =====================================================
# CHANGE DETECTION INSTRUCTIONS
# =====================================================
change_detection_prompt:
  
  core_instruction: |
    "Analyze the differences between the current codebase state and the existing 
    workflow documentation to identify what needs to be updated, added, or removed."
  
  change_categories_to_detect:
    
    new_files_analysis:
      instruction: |
        "Identify files added since last workflow documentation update:
        - New components or services
        - New feature directories
        - Additional configuration files
        - New utility or helper files"
      
      workflow_impact: |
        "For each new file, determine:
        - Does it introduce a new user workflow?
        - Does it modify an existing workflow?
        - Is it infrastructure/support code?
        - Should it be mapped to existing workflow sections?"
    
    modified_files_analysis:
      instruction: |
        "Identify files with significant changes:
        - Check last_modified timestamps against documentation date
        - Look for structural changes in components
        - Identify new dependencies or imports
        - Detect function/method additions or removals"
      
      update_requirements: |
        "For modified files:
        - Update workflow paths if component structure changed
        - Add new interaction patterns
        - Remove obsolete workflow steps
        - Update dependency chains"
    
    deleted_files_analysis:
      instruction: |
        "Identify files removed from codebase:
        - Components no longer in mvcd.yaml
        - Deprecated features that were removed
        - Refactored code that was consolidated"
      
      cleanup_actions: |
        "For deleted files:
        - Remove from workflow documentation
        - Update dead code analysis to reflect cleanup
        - Remove dependencies from workflow chains
        - Update coverage metrics"

# =====================================================
# INCREMENTAL WORKFLOW UPDATES
# =====================================================
incremental_workflow_updates:
  
  workflow_modification_patterns:
    
    extend_existing_workflows:
      instruction: |
        "When new functionality extends existing workflows:
        - Add new steps to existing workflow chains
        - Update execution paths with new components
        - Maintain existing workflow structure
        - Add new branches for optional features"
      
      example_scenario: |
        "If new node editing features are added:
        - Extend node_manipulation_flow with new edit modes
        - Add new UI components to the execution path
        - Update state management workflows"
    
    create_new_workflows:
      instruction: |
        "When entirely new user features are introduced:
        - Create new workflow sections
        - Define new trigger conditions
        - Map complete execution paths
        - Integrate with existing state management"
      
      example_scenario: |
        "If MindBook feature is fully implemented:
        - Add mindbook_creation_workflow
        - Document MindBook ↔ MindMap interactions
        - Update navigation workflows"
    
    deprecate_obsolete_workflows:
      instruction: |
        "When features are removed or replaced:
        - Mark workflows as deprecated
        - Document replacement workflows
        - Update coverage analysis
        - Clean up dead code references"

# =====================================================
# TARGETED DEAD CODE UPDATES
# =====================================================
targeted_dead_code_updates:
  
  focus_areas_for_incremental_analysis:
    
    recently_inactive_files:
      instruction: |
        "Identify files that haven't been modified recently:
        - Check last_modified timestamps
        - Compare against development activity patterns
        - Look for files not touched in 6+ months"
      
      verification_steps: |
        "For potentially stale files:
        1. Verify they're still imported/referenced
        2. Check if functionality was moved elsewhere
        3. Assess impact of removal"
    
    new_duplicate_detection:
      instruction: |
        "Look for new instances of code duplication:
        - Similar functionality in new files
        - Refactored code that wasn't fully cleaned up
        - Multiple implementations of same feature"
    
    orphaned_by_refactoring:
      instruction: |
        "Identify code orphaned by recent refactoring:
        - Old implementations after new ones were created
        - Helper functions no longer used
        - Components replaced but not removed"

# =====================================================
# COVERAGE METRICS UPDATE
# =====================================================
coverage_metrics_update:
  
  incremental_calculation:
    instruction: |
      "Update coverage metrics based on changes:
      - Add new files to appropriate categories
      - Recalculate percentages
      - Update trend analysis
      - Identify coverage improvements or regressions"
    
    trending_analysis: |
      "Track coverage trends:
      - Is workflow coverage improving or declining?
      - Are new features being properly documented?
      - Is dead code cleanup keeping pace with development?"

# =====================================================
# UPDATE STRATEGIES
# =====================================================
update_strategies:
  
  minimal_disruption_approach:
    instruction: |
      "Preserve existing workflow structure while adding updates:
      - Maintain existing section organization
      - Add new content in logical locations
      - Update cross-references and links
      - Preserve established naming conventions"
  
  version_control_considerations:
    instruction: |
      "Document changes for version control:
      - Add update metadata (date, version, changes)
      - Maintain changelog of documentation updates
      - Preserve previous analysis for comparison"
  
  validation_against_existing:
    instruction: |
      "Ensure updates maintain consistency:
      - Verify new workflows integrate with existing ones
      - Check that file references are still accurate
      - Validate dependency chains are complete"

# =====================================================
# EFFICIENT UPDATE PROCESS
# =====================================================
efficient_update_process:
  
  prioritized_update_order:
    1. "Critical workflow changes (user-facing features)"
    2. "New component integrations"
    3. "Dead code cleanup updates"
    4. "Coverage metrics recalculation"
    5. "Documentation cleanup and organization"
  
  automation_opportunities:
    instruction: |
      "Identify update tasks that could be automated:
      - File timestamp comparison
      - Basic coverage metric calculation
      - Dead code detection patterns
      - Workflow validation checks"

# =====================================================
# UPDATE OUTPUT REQUIREMENTS
# =====================================================
update_output_requirements:
  
  change_documentation:
    required_sections:
      - "Summary of changes made"
      - "New workflows added"
      - "Modified workflows updated"
      - "Deprecated workflows removed"
      - "Coverage impact analysis"
  
  preservation_requirements:
    - "Maintain all existing accurate documentation"
    - "Preserve workflow reference numbers/IDs"
    - "Keep established section organization"
    - "Maintain consistent formatting"
  
  update_metadata:
    - "Document update date and version"
    - "List major changes made"
    - "Note any breaking changes in workflow structure"
    - "Record coverage metric changes"

# =====================================================
# QUALITY ASSURANCE FOR UPDATES
# =====================================================
update_quality_assurance:
  
  validation_checklist:
    - "All new file references exist in latest mvcd.yaml"
    - "Modified workflows maintain logical flow"
    - "Removed content corresponds to deleted files"
    - "Coverage metrics add up correctly"
    - "Cross-references are still valid"
  
  consistency_checks:
    - "New content matches existing documentation style"
    - "File path formats are consistent"
    - "Workflow naming conventions maintained"
    - "Section organization preserved"

# =====================================================
# FINAL UPDATE INSTRUCTION
# =====================================================
final_update_instruction: |
  "Using the existing workflowDescription.yaml as the base and the latest mvcd.yaml 
  for change detection, efficiently update the workflow documentation to:
  
  1. Incorporate new files and features into appropriate workflows
  2. Update existing workflows with modified components
  3. Remove documentation for deleted/obsolete code
  4. Update dead code analysis with recent changes
  5. Recalculate coverage metrics
  6. Maintain overall documentation structure and quality
  
  Focus on incremental improvements while preserving the value of existing documentation.
  Prioritize accuracy and maintainability over comprehensive recreation."

update_success_criteria:
  - "Documentation accurately reflects current codebase state"
  - "New functionality is properly mapped to workflows"
  - "Obsolete content is removed or marked deprecated"
  - "Coverage metrics are current and accurate"
  - "Update can be completed efficiently (< 30 minutes for typical changes)"
