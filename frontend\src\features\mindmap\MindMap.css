/**
 * MindMap.css
 *
 * Styles for the main MindMap component.
 */

.mindmap-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #f8fafc;
  /* Prevent text selection globally */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.mindmap-canvas-container {
  position: absolute;
  top: 50px; /* Leave space for toolbar */
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

/* Loading state */
.loading-canvas {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f8fafc;
}

.initializing-mindmap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3498db;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
