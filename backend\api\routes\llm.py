"""
LLM API Router Module
Handles routes for LLM-based functionality including chat and mindmap generation.
"""

from fastapi import APIRouter, HTTPException, Depends
import yaml
import os
import logging
import json
import sys

# Import our new modular components
from ..models.mbcp_models import LLMChatRequest, LLMChatResponse
from ..services.openai_service import get_openai_client
from ..services.prompt_service import load_yaml_prompt, format_prompt_with_values, prepare_messages, PROMPT_LIBRARY_PATH
from ..services.response_processor import process_llm_response
from ..schemas.mbcp_schemas import get_mbcp_function_schema

# Add import for the instantiation_template_router
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from Prompt_library.instantiation_template_router import instantiation_template_router

# FIXED IMPORT: Use sys.path approach instead of relative import
backend_path = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
if backend_path not in sys.path:
    sys.path.append(backend_path)
from config.intent_config import get_intent_metadata

# Get the existing logger instead of creating a new one
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/llm", tags=["llm"])

# ============================================================================
# GENERIC INTENT ROUTING FUNCTIONS (Must be defined before endpoints use them)
# ============================================================================

async def route_intent_to_specialized_prompt(
    processed_response: LLMChatResponse,
    request: LLMChatRequest,
    openai_client
) -> LLMChatResponse:
    """
    Generic router that handles second-stage prompts based on intent metadata.
    Routes intents to their specialized prompts based on configuration.
    """
    if not (processed_response.success and processed_response.content):
        logger.info("No valid response to route - returning original response")
        return processed_response
        
    intent = processed_response.content.get('intent')
    if not intent:
        logger.info("No intent found in response - returning original response")
        return processed_response
    
    # Get intent metadata from configuration
    intent_metadata = get_intent_metadata(intent)
    if not intent_metadata:
        logger.warning(f"No metadata found for intent: {intent}")
        return processed_response
    
    # Check if this intent requires specialized processing
    prompt_template = intent_metadata.get('prompt_template')
    if not prompt_template:
        logger.info(f"Intent '{intent}' has no specialized prompt template - returning classification response")
        return processed_response
    
    # Check if any of the requirements are met
    requires_any = (
        intent_metadata.get('requires_mindmap', False) or
        intent_metadata.get('requires_chatfork', False) or
        intent_metadata.get('requires_template', False)
    )
    
    if not requires_any:
        logger.info(f"Intent '{intent}' requires no specialized processing - returning classification response")
        return processed_response
    
    logger.info(f"🎯 Routing intent '{intent}' to specialized prompt: {prompt_template}")
    
    # Process with the specialized prompt
    return await process_specialized_prompt(
        prompt_template, 
        processed_response, 
        request, 
        openai_client
    )

async def process_specialized_prompt(
    prompt_type: str,
    classification_response: LLMChatResponse, 
    original_request: LLMChatRequest,
    openai_client
) -> LLMChatResponse:
    """
    Generic processor for specialized prompts (mindmap, chatfork, situational, etc.).
    This replaces the individual hardcoded logic for each intent type.
    """
    try:
        logger.info(f"🚀 === PROCESSING SPECIALIZED PROMPT: {prompt_type} ===")
        
        # Load the specialized prompt
        prompt_data = load_yaml_prompt(prompt_type)
        if not prompt_data:
            logger.error(f"❌ Failed to load prompt template: {prompt_type}")
            return classification_response
            
        logger.info(f"✅ Loaded {prompt_type} prompt data keys: {list(prompt_data.keys())}")

        # Prepare values for prompt formatting
        values_to_replace = {
            "g-llm_dialogue": original_request.prompt,
            "topic": classification_response.content.get('text', 'Topic'),
            "intent": classification_response.content.get('intent', 'miscellaneous')
        }
        
        logger.info(f"🔄 Replacing placeholders with values: {values_to_replace}")

        # Format all parts of the prompt
        formatted_prompt = format_prompt_with_values(prompt_data, values_to_replace)

        # Prepare messages (this will use our enhanced processing for complex prompts)
        prepared = prepare_messages("", original_request.prompt, formatted_prompt)
        content_prompt = prepared.get("content_prompt", original_request.prompt)
        system_prompt = prepared.get("system_prompt", "")

        logger.info(f"📝 === FORMATTED PROMPTS FOR {prompt_type} ===")
        logger.info(f"System prompt length: {len(system_prompt)}")
        logger.info(f"System prompt preview: {system_prompt[:200]}..." if len(system_prompt) > 200 else system_prompt)
        logger.info(f"Content prompt preview: {content_prompt[:200]}..." if len(content_prompt) > 200 else content_prompt)
        logger.info("==========================================")

        # Prepare messages for the OpenAI API
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": content_prompt}
        ]

        # Get the MBCP function schema
        functions = get_mbcp_function_schema()

        # Call the OpenAI chat completion API
        logger.info(f"🤖 Making OpenAI API call for {prompt_type}")
        response = await openai_client.chat.completions.create(
            model=original_request.model,
            messages=messages,
            functions=functions,
            function_call={"name": "mbcp_response"},
            temperature=original_request.temperature
        )

        # Log what was sent to the LLM
        logger.info(f"📤 === {prompt_type.upper()} CONTENT SENT TO LLM ===")
        for msg in messages:
            if msg.get('role') == 'user':
                logger.info(f"User content: {msg.get('content')[:200]}...")
            elif msg.get('role') == 'system':
                logger.info(f"System content: {msg.get('content')[:200]}...")
        logger.info("=======================================")

        # Log the function call arguments
        if hasattr(response.choices[0].message, 'function_call') and response.choices[0].message.function_call:
            logger.info(f"📥 === {prompt_type.upper()} FUNCTION CALL ARGUMENTS ===")
            logger.info(response.choices[0].message.function_call.arguments)
            logger.info("===========================================")

        # Process the specialized response
        specialized_processed = process_llm_response(response, original_request.model_dump())

        if specialized_processed.success and specialized_processed.content:
            logger.info(f"✅ Successfully generated {prompt_type} content")
            logger.info(f"📋 {prompt_type.upper()} CONTENT: " + json.dumps(specialized_processed.content, indent=2))
            return specialized_processed
        else:
            logger.error(f"❌ Failed to process {prompt_type} response")
            logger.error(f"Error: {specialized_processed.error if hasattr(specialized_processed, 'error') else 'Unknown error'}")
            return classification_response  # Fallback to classification

    except Exception as e:
        logger.error(f"💥 Error in {prompt_type} processing: {str(e)}")
        logger.error(f"Falling back to classification response")
        return classification_response  # Fallback to classification

# ============================================================================
# API ENDPOINTS
# ============================================================================

@router.post("/chat", response_model=LLMChatResponse)
async def llm_chat(
    request: LLMChatRequest,
    openai_client = Depends(get_openai_client)
):
    """
    Generate a response from the LLM using YAML prompts with function calling to ensure MBCP structure
    """
    try:
        logger.info(f"Received chat request with prompt_type: {request.prompt_type}")
        logger.info(f"Intent: {request.intent}")
        logger.info(f"Prompt: {request.prompt[:100]}..." if request.prompt and len(request.prompt) > 100 else f"Prompt: {request.prompt}")

        # If no prompt_type is provided, use initiation_prompt2 for intent detection
        if not request.prompt_type:
            prompt_type_to_use = "initiation_prompt2"
            logger.info(f"No prompt_type provided, using default intent detection: {prompt_type_to_use}")
        else:
            prompt_type_to_use = request.prompt_type
            logger.info(f"Using prompt type from request: {prompt_type_to_use}")

        # Load the appropriate YAML prompt based on the determined prompt_type
        prompt_data = load_yaml_prompt(prompt_type_to_use)
        logger.info(f"Loaded prompt data keys: {list(prompt_data.keys())}")

        # Extract the system_role from the prompt data if not provided directly
        system_prompt = request.system_prompt
        if not system_prompt and 'system_role' in prompt_data:
            system_prompt = prompt_data.get('system_role', '')

        # Format the prompt with values from the request - no fallbacks
        values_to_replace = {
            "g-llm_dialogue": request.prompt,
            "topic": request.topic or request.prompt,  # Use the prompt as the topic if no specific topic provided
            "intent": request.intent or "miscellaneous"  # Default intent if not provided
        }

        # Format all parts of the prompt
        formatted_prompt = format_prompt_with_values(prompt_data, values_to_replace)

        # Prepare messages
        prepared = prepare_messages(system_prompt, request.prompt, formatted_prompt)
        content_prompt = prepared.get("content_prompt", request.prompt)
        system_prompt = prepared.get("system_prompt", system_prompt)

        logger.info(f"Using system prompt: {system_prompt[:100]}..." if system_prompt else "No system prompt provided")
        logger.info(f"Using content prompt: {content_prompt[:100]}...")

        # Get MBCP function schema
        functions = get_mbcp_function_schema()

        # Prepare messages for API request
        messages = []

        # Add system message if provided
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})

        # Add any previous messages from history if provided
        if request.messages:
            messages.extend(request.messages)
        else:
            # If no history, add user message with content prompt
            messages.append({"role": "user", "content": content_prompt})

        # Call the OpenAI chat completion API with function call
        response = await openai_client.chat.completions.create(
            model=request.model,
            messages=messages,
            functions=functions,
            function_call={"name": "mbcp_response"},
            temperature=request.temperature
        )

        # Log the full OpenAI response for debugging
        logger.info("=== FULL OPENAI RAW RESPONSE ===")
        logger.info(json.dumps(response.model_dump() if hasattr(response, 'model_dump') else response, indent=2))
        logger.info("================================")

        # Log what was sent to the LLM
        logger.info("=== CONTENT SENT TO LLM ===")
        for msg in messages:
            if msg.get('role') == 'user':
                logger.info(f"User content: {msg.get('content')}")
            elif msg.get('role') == 'system':
                logger.info(f"System content: {msg.get('content')}")
        logger.info("================================")

        # Log the function call arguments
        if hasattr(response.choices[0].message, 'function_call') and response.choices[0].message.function_call:
            logger.info("=== FUNCTION CALL ARGUMENTS ===")
            logger.info(response.choices[0].message.function_call.arguments)
            logger.info("================================")

        # Process the response using our new processor
        processed_response = process_llm_response(response, request.model_dump())

        # Use generic intent routing instead of hardcoded logic
        logger.info("🎯 Checking if intent requires specialized routing...")
        specialized_response = await route_intent_to_specialized_prompt(
            processed_response, 
            request, 
            openai_client
        )
        
        return specialized_response

    except Exception as e:
        logger.error(f"Error processing request: {str(e)}")
        return LLMChatResponse(
            success=False,
            error=str(e),
            content={},
            model=request.model if hasattr(request, 'model') else None,
            validation_info={
                "validated": False,
                "validation_errors": str(e)
            }
        )

@router.post("/initiate_mindmap", response_model=LLMChatResponse)
async def initiate_mindmap(
    request: LLMChatRequest,
    openai_client = Depends(get_openai_client)
):
    """
    Generate a mindmap structure for a given topic, using specific templates when available
    """
    try:
        # Check if we have a template_type in the request
        template_type = None

        # Convert from request body to dictionary to check for attribute
        request_dict = request.model_dump()

        if 'template_type' in request_dict and request_dict['template_type']:
            template_type = request_dict['template_type']
            logger.info(f"Using template_type from request body: {template_type}")
        elif hasattr(request, 'template_type') and request.template_type:
            template_type = request.template_type
            logger.info(f"Using template_type from request attribute: {template_type}")

        # Check if this template exists in the router
        # Log the available templates
        logger.info(f"Available templates: {list(instantiation_template_router.keys())}")

        # Default to initiate_mindmap2 if no specific template provided
        prompt_type = None
        if template_type and template_type in instantiation_template_router:
            template_config = instantiation_template_router[template_type]
            prompt_type = template_config.get('prompt_template', '').replace('.yaml', '')
            logger.info(f"Found template in router: {template_type} → {prompt_type}")

        if not prompt_type:
            # Use the prompt_type from the request without any hardcoded fallbacks
            prompt_type = request.prompt_type
            logger.info(f"Using prompt type from request: {prompt_type}")

        request.prompt_type = prompt_type
        logger.info(f"Final prompt_type for LLM: {prompt_type}")

        # Log the topic and prompt
        logger.info(f"Topic: {request.topic}")
        logger.info(f"Prompt: {request.prompt[:100]}..." if request.prompt and len(request.prompt) > 100 else f"Prompt: {request.prompt}")

        # Load the appropriate YAML prompt based on prompt_type
        prompt_data = load_yaml_prompt(request.prompt_type)

        # Extract the system_role from the prompt data if not provided directly
        system_prompt = request.system_prompt
        if not system_prompt and 'system_role' in prompt_data:
            system_prompt = prompt_data.get('system_role', '')

        # Format the prompt with values from the request
        formatted_prompt = format_prompt_with_values(prompt_data, {
            "g-llm_dialogue": request.prompt,
            "topic": request.topic or request.prompt,  # Use the prompt as the topic if no specific topic provided
            "intent": request.intent or "instantiation"  # Default to instantiation for mindmaps
        })

        # Prepare messages
        prepared = prepare_messages(system_prompt, request.prompt, formatted_prompt)
        content_prompt = prepared.get("content_prompt", request.prompt)
        system_prompt = prepared.get("system_prompt", system_prompt)

        logger.info(f"Using system prompt: {system_prompt[:100]}..." if system_prompt else "No system prompt provided")
        logger.info(f"Using content prompt: {content_prompt[:100]}...")

        # Use the same MBCP function schema as the chat endpoint
        functions = get_mbcp_function_schema()

        # Generate the completion with function calling
        response = await openai_client.chat.completions.create(
            model=request.model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": content_prompt}
            ],
            functions=functions,
            function_call={"name": "mbcp_response"},
            temperature=request.temperature
        )

        # Add template_type to request data for processing
        request_data = request.model_dump()
        request_data["template_type"] = template_type

        # Process the response using our new processor
        return process_llm_response(response, request_data)

    except Exception as e:
        logger.error(f"Error processing mindmap request: {str(e)}")
        return LLMChatResponse(
            success=False,
            error=str(e),
            content={},  # Empty content on error
            model=request.model if hasattr(request, 'model') else None
        )

@router.get("/status")
async def get_status():
    """
    Check if LLM service is properly configured
    """
    from ..config.settings import get_settings
    settings = get_settings()
    return {
        "configured": bool(settings.openai_api_key),
        "api_key_available": bool(settings.openai_api_key),
        "note": "Use this endpoint to check if the LLM service is properly configured"
    }

@router.get("/health")
async def health_check():
    """
    Check if the LLM router is healthy and properly configured
    """
    try:
        from ..config.settings import get_settings
        settings = get_settings()
        return {
            "status": "healthy",
            "api_key_configured": bool(settings.openai_api_key),
            "prompt_library_path": str(PROMPT_LIBRARY_PATH),
            "prompt_library_exists": os.path.exists(PROMPT_LIBRARY_PATH)
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }

@router.get("/test-prompt/{prompt_type}")
async def test_prompt(prompt_type: str):
    """
    Test a specific prompt template
    """
    try:
        # Try all possible extensions and prefixes
        for ext in [".yaml", ".yml"]:
            file_path = os.path.join(PROMPT_LIBRARY_PATH, f"{prompt_type}{ext}")
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()
                    data = yaml.safe_load(content)
                    return {
                        "success": True,
                        "prompt_type": prompt_type,
                        "file_path": file_path,
                        "content": content,
                        "parsed": data
                    }

            system_path = os.path.join(PROMPT_LIBRARY_PATH, f"system_{prompt_type}{ext}")
            if os.path.exists(system_path):
                with open(system_path, 'r', encoding='utf-8') as file:
                    content = file.read()
                    data = yaml.safe_load(content)
                    return {
                        "success": True,
                        "prompt_type": prompt_type,
                        "file_path": system_path,
                        "content": content,
                        "parsed": data
                    }

        return {
            "success": False,
            "error": f"Prompt template not found: {prompt_type}"
        }
    except Exception as e:
        return {
            "success": False,
            "error": f"Error loading prompt: {str(e)}"
        }

@router.get("/prompt/{prompt_type}")
async def get_prompt(prompt_type: str):
    """
    Get a prompt from the Prompt_library
    """
    try:
        # Try all possible extensions and prefixes
        for ext in [".yaml", ".yml"]:
            file_path = os.path.join(PROMPT_LIBRARY_PATH, f"{prompt_type}{ext}")
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()
                    parsed = yaml.safe_load(content)
                    return {
                        "success": True,
                        "prompt_type": prompt_type,
                        "file_path": file_path,
                        "content": content,
                        "parsed": parsed
                    }

            system_path = os.path.join(PROMPT_LIBRARY_PATH, f"system_{prompt_type}{ext}")
            if os.path.exists(system_path):
                with open(system_path, 'r', encoding='utf-8') as file:
                    content = file.read()
                    parsed = yaml.safe_load(content)
                    return {
                        "success": True,
                        "prompt_type": prompt_type,
                        "file_path": system_path,
                        "content": content,
                        "parsed": parsed
                    }

        raise HTTPException(status_code=404, detail=f"Prompt file not found: {prompt_type}")
    except HTTPException as e:
        raise e
    except Exception as e:
        return {
            "success": False,
            "prompt_type": prompt_type,
            "error": str(e)
        }