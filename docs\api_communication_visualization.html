<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindBack API Communication Visualization</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #2980b9;
            margin-top: 30px;
        }
        .section {
            margin-bottom: 40px;
            padding: 20px;
            border-radius: 5px;
            background-color: #f8f9fa;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .note {
            background-color: #fff8dc;
            border-left: 4px solid #ffd700;
            padding: 10px 15px;
            margin: 20px 0;
        }
        .section-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .dot {
            height: 12px;
            width: 12px;
            background-color: #3498db;
            border-radius: 50%;
            display: inline-block;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
        });
    </script>
</head>
<body>
    <h1>MindBack API Communication Visualization</h1>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>Frontend-Backend Communication Flow</h2>
        </div>
        <div class="mermaid">
        sequenceDiagram
            participant User
            participant FE_UI as Frontend UI
            participant FE_API as Frontend API Service
            participant Router as MainRouter
            participant BE_API as Backend API
            participant BE_LLM as Backend LLM Service
            participant Ext_LLM as External LLM Provider

            User->>FE_UI: Enter message in GovernanceChat
            FE_UI->>FE_API: Call GovernanceAPI.sendGovernanceMessage()
            FE_API->>BE_API: POST /api/llm/chat (prompt, model, prompt_type="initiation_prompt2")
            Note right of BE_API: First LLM call for intent detection
            BE_API->>BE_API: Load initiation YAML prompt template
            BE_API->>BE_API: Format prompt with user message
            BE_API->>BE_LLM: Prepare LLM request
            BE_LLM->>Ext_LLM: Make API call to LLM provider
            Ext_LLM-->>BE_LLM: Return raw LLM response
            BE_LLM-->>BE_API: Return LLM response
            BE_API->>BE_API: Process response to MBCP format
            BE_API-->>FE_API: Return MBCP response with intent
            FE_API-->>FE_UI: Return MBCP response

            FE_UI->>Router: Pass response to MainRouter.routeIntent()

            alt Factual Intent
                Router-->>FE_UI: Display in GovernanceChat
            else Non-Factual Intent (Exploratory/Teleological/Instantiation)
                Note right of FE_UI: Second API call needed for content generation
                FE_UI->>FE_API: Call API with detected intent
                FE_API->>BE_API: POST /api/llm/chat (prompt, model, prompt_type=intent-specific)
                BE_API->>BE_API: Load intent-specific YAML prompt
                BE_API->>BE_API: Format prompt with user message
                BE_API->>BE_LLM: Prepare LLM request
                BE_LLM->>Ext_LLM: Make API call to LLM provider
                Ext_LLM-->>BE_LLM: Return raw LLM response
                BE_LLM-->>BE_API: Return LLM response
                BE_API->>BE_API: Process response to MBCP format
                BE_API-->>FE_API: Return complete MBCP response
                FE_API-->>FE_UI: Return MBCP response
                FE_UI->>Router: Pass response to MainRouter

                alt Exploratory Intent
                    Router->>FE_UI: Create ChatFork sheet
                    FE_UI-->>User: Display ChatFork visualization
                else Teleological Intent
                    Router->>FE_UI: Create MindMap sheet
                    FE_UI-->>User: Display MindMap visualization
                else Instantiation Intent
                    Router->>FE_UI: Create Template sheet
                    FE_UI-->>User: Display Template visualization
                end
            end

            Note over FE_UI,BE_API: All communication uses the MBCP format
            Note over BE_API: Backend treats each API call as completely independent
            Note over FE_UI: Frontend is responsible for making both API calls and coordinating the workflow
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>MBCP Protocol Structure</h2>
        </div>
        <div class="mermaid">
        classDiagram
            class MBCPResponse {
                +String text
                +String description
                +String intent
                +Object metadata
                +Object mindmap
                +Object chatfork
                +Object template
            }

            class Intent {
                factual
                exploratory
                teleological
                instantiation
                miscellaneous
            }

            class Metadata {
                +String intent
                +String agent
                +String[] tags
                +Action action
            }

            class MindMap {
                +Node root
            }

            class Node {
                +String id
                +String text
                +String description
                +Metadata metadata
                +Node[] children
            }

            class ChatFork {
                +String chatfork_id
                +ChatNode[] nodes
                +ChatConnection[] connections
            }

            class ChatNode {
                +String id
                +String text
                +String description
                +Metadata metadata
            }

            class ChatConnection {
                +String source
                +String target
                +String label
            }

            MBCPResponse --> Intent
            MBCPResponse --> Metadata
            MBCPResponse --> MindMap
            MBCPResponse --> ChatFork
            MindMap --> Node
            Node --> Metadata
            ChatFork --> ChatNode
            ChatFork --> ChatConnection
            ChatNode --> Metadata
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>API Request-Response Flow</h2>
        </div>
        <div class="mermaid">
        flowchart TD
            subgraph Frontend
                FEInput["User Input"] --> GovernanceAPI["GovernanceAPI.sendGovernanceMessage()"]
                GovernanceAPI --> FetchAPI["Fetch API Call"]
                FetchAPI --> ProcessResponse["Process MBCP Response"]
                ProcessResponse --> MainRouter["MainRouter.routeIntent()"]
            end

            subgraph Backend
                APIEndpoint["POST /api/llm/chat"] --> ValidateRequest["Validate Request"]
                ValidateRequest --> LoadPrompt["Load YAML Prompt"]
                LoadPrompt --> FormatPrompt["Format Prompt with Values"]
                FormatPrompt --> PrepareMessages["Prepare Messages"]
                PrepareMessages --> ModelCheck{"Check Model Type"}

                ModelCheck -- "OpenAI" --> OpenAICall["Call OpenAI API"]
                ModelCheck -- "Gemini" --> GeminiCall["Call Gemini API"]
                ModelCheck -- "Claude" --> ClaudeCall["Call Claude API"]

                OpenAICall --> ProcessResponse["Process LLM Response"]
                GeminiCall --> ProcessResponse
                ClaudeCall --> ProcessResponse

                ProcessResponse --> ValidateMBCP["Validate MBCP Structure"]
                ValidateMBCP --> ReturnResponse["Return MBCP Response"]
            end

            FetchAPI -- "HTTP Request" --> APIEndpoint
            ReturnResponse -- "HTTP Response" --> ProcessResponse

            MainRouter --> DisplayFactual["Display in GovernanceChat"]
            MainRouter --> CreateChatFork["Create ChatFork Sheet"]
            MainRouter --> CreateMindMap["Create MindMap Sheet"]
            MainRouter --> CreateTemplate["Create Template Sheet"]

            classDef frontend fill:#bbdefb,stroke:#64b5f6,stroke-width:1px
            classDef backend fill:#c8e6c9,stroke:#66bb6a,stroke-width:1px
            classDef router fill:#ffcc80,stroke:#ff9800,stroke-width:1px
            classDef action fill:#ce93d8,stroke:#ab47bc,stroke-width:1px

            class FEInput,GovernanceAPI,FetchAPI,ProcessResponse frontend
            class APIEndpoint,ValidateRequest,LoadPrompt,FormatPrompt,PrepareMessages,ModelCheck,OpenAICall,GeminiCall,ClaudeCall,ProcessResponse,ValidateMBCP,ReturnResponse backend
            class MainRouter router
            class DisplayFactual,CreateChatFork,CreateMindMap,CreateTemplate action
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>MainRouter Decision Flow</h2>
        </div>
        <div class="mermaid">
        flowchart TD
            LLMResponse["MBCP Response"] --> MainRouter["MainRouter.routeIntent()"]

            MainRouter --> ContextCheck{"What is the context?"}

            ContextCheck -- "govbox" --> IntentCheck{{"What is the intent?"}}
            ContextCheck -- "mindsheet" --> SheetCheck{"Existing sheet?"}

            SheetCheck -- "Yes" --> ExtendSheet["Extend existing sheet"]
            SheetCheck -- "No" --> IntentCheck

            IntentCheck -- "factual" --> FactualAction["Display in GovernanceChat"]
            IntentCheck -- "exploratory" --> ExploratoryAction["Create ChatFork Sheet"]
            IntentCheck -- "teleological" --> TeleologicalAction["Create MindMap Sheet"]
            IntentCheck -- "instantiation" --> InstantiationAction["Create Template Sheet"]
            IntentCheck -- "miscellaneous" --> MiscAction["Display in GovernanceChat"]

            ExploratoryAction --> ChatForkSheet["ChatFork Sheet"]
            TeleologicalAction --> MindMapSheet["MindMap Sheet"]
            InstantiationAction --> TemplateSheet["Template Sheet"]
            ExtendSheet --> UpdateSheet["Update existing sheet"]

            classDef input fill:#f9a825,stroke:#f57f17,stroke-width:2px
            classDef router fill:#ffcc80,stroke:#ff9800,stroke-width:1px
            classDef decision fill:#fff59d,stroke:#ffee58,stroke-width:1px
            classDef action fill:#ce93d8,stroke:#ab47bc,stroke-width:1px
            classDef output fill:#90caf9,stroke:#42a5f5,stroke-width:1px

            class LLMResponse input
            class MainRouter router
            class IntentCheck,ContextCheck,SheetCheck decision
            class FactualAction,ExploratoryAction,TeleologicalAction,InstantiationAction,MiscAction,ExtendSheet action
            class ChatForkSheet,MindMapSheet,TemplateSheet,UpdateSheet output
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>Manual vs. Automated Workflow</h2>
        </div>
        <div class="mermaid">
        flowchart TD
            subgraph ManualWorkflow["Manual Workflow"]
                UserSelectsIntent["User selects intent"]
                UserSelectsIntent --> ManualRouter["MainRouter (isManualSelection=true)"]
                ManualRouter --> CreateSheet["Create appropriate sheet"]
            end

            subgraph AutomatedWorkflow["Automated Workflow"]
                UserSendsMessage["User sends message"]
                UserSendsMessage --> LLMDetectsIntent["LLM detects intent"]
                LLMDetectsIntent --> AutoRouter["MainRouter (isManualSelection=false)"]
                AutoRouter --> RouteByIntent["Route by detected intent"]
            end

            subgraph MindmapWorkflow["Mindmap Workflow"]
                CreateMindmap["Create mindmap"]
                CreateMindmap --> AddNodes["Add nodes"]
                AddNodes --> UpdateLayout["Update layout"]
            end

            subgraph ChatForkWorkflow["ChatFork Workflow"]
                CreateChatFork["Create chatfork"]
                CreateChatFork --> AddChatNodes["Add chat nodes"]
                AddChatNodes --> AddConnections["Add connections"]
            end

            ManualRouter --> MindmapWorkflow
            ManualRouter --> ChatForkWorkflow
            AutoRouter --> MindmapWorkflow
            AutoRouter --> ChatForkWorkflow
            AutoRouter --> DisplayInGovBox["Display in GovernanceChat"]

            classDef manual fill:#bbdefb,stroke:#64b5f6,stroke-width:1px
            classDef auto fill:#c8e6c9,stroke:#66bb6a,stroke-width:1px
            classDef mindmap fill:#ffcc80,stroke:#ff9800,stroke-width:1px
            classDef chatfork fill:#ce93d8,stroke:#ab47bc,stroke-width:1px
            classDef action fill:#90caf9,stroke:#42a5f5,stroke-width:1px

            class UserSelectsIntent,ManualRouter,CreateSheet manual
            class UserSendsMessage,LLMDetectsIntent,AutoRouter,RouteByIntent,DisplayInGovBox auto
            class CreateMindmap,AddNodes,UpdateLayout mindmap
            class CreateChatFork,AddChatNodes,AddConnections chatfork
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>Multi-LLM Provider Support</h2>
        </div>
        <div class="mermaid">
        flowchart TD
            FrontendRequest["Frontend Request"] --> BackendRouter["Backend LLM Router"]

            BackendRouter --> ModelCheck{"Which model?"}

            ModelCheck -- "gpt-*" --> OpenAIService["OpenAI Service"]
            ModelCheck -- "gemini-*" --> GeminiService["Gemini Service"]
            ModelCheck -- "claude-*" --> ClaudeService["Claude Service"]

            OpenAIService --> OpenAIAPI["OpenAI API"]
            GeminiService --> GeminiAPI["Gemini API"]
            ClaudeService --> ClaudeAPI["Claude API"]

            OpenAIAPI --> ResponseProcessor["Response Processor"]
            GeminiAPI --> ResponseProcessor
            ClaudeAPI --> ResponseProcessor

            ResponseProcessor --> MBCPValidator["MBCP Validator"]
            MBCPValidator --> FrontendResponse["Frontend Response"]

            classDef frontend fill:#bbdefb,stroke:#64b5f6,stroke-width:1px
            classDef backend fill:#c8e6c9,stroke:#66bb6a,stroke-width:1px
            classDef service fill:#ffcc80,stroke:#ff9800,stroke-width:1px
            classDef api fill:#ce93d8,stroke:#ab47bc,stroke-width:1px
            classDef processor fill:#90caf9,stroke:#42a5f5,stroke-width:1px

            class FrontendRequest,FrontendResponse frontend
            class BackendRouter,ModelCheck backend
            class OpenAIService,GeminiService,ClaudeService service
            class OpenAIAPI,GeminiAPI,ClaudeAPI api
            class ResponseProcessor,MBCPValidator processor
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>Proposed Improvements for API Communication</h2>
        </div>
        <div class="mermaid">
        flowchart TD
            subgraph CurrentFlow["Current Flow"]
                C_UserInput["User Input"] --> C_FrontendAPI1["First API Call (Intent Detection)"]
                C_FrontendAPI1 --> C_BackendAPI1["Backend API"]
                C_BackendAPI1 --> C_LLMCall1["LLM Call with initiation_prompt2"]
                C_LLMCall1 --> C_ProcessResponse1["Process Response"]
                C_ProcessResponse1 --> C_FrontendRouter1["Frontend Router"]

                C_FrontendRouter1 -- "Non-Factual Intent" --> C_FrontendAPI2["Second API Call (Content Generation)"]
                C_FrontendAPI2 --> C_BackendAPI2["Backend API"]
                C_BackendAPI2 --> C_LLMCall2["LLM Call with intent-specific prompt"]
                C_LLMCall2 --> C_ProcessResponse2["Process Response"]
                C_ProcessResponse2 --> C_FrontendRouter2["Frontend Router"]

                C_FrontendRouter1 -- "Factual Intent" --> C_Display["Display in GovernanceChat"]
                C_FrontendRouter2 --> C_CreateSheet["Create appropriate sheet"]
            end

            subgraph ProposedFlow["Proposed Flow"]
                P_UserInput["User Input"] --> P_FrontendAPI["Frontend API Call"]
                P_FrontendAPI --> P_BackendAPI["Backend API"]
                P_BackendAPI --> P_IntentDetection["Intent Detection Call"]
                P_IntentDetection --> P_BackendRouter["Backend Router"]

                P_BackendRouter -- "factual" --> P_FactualCall["Factual LLM Call"]
                P_BackendRouter -- "exploratory" --> P_ExploratoryCall["Exploratory LLM Call"]
                P_BackendRouter -- "teleological" --> P_TeleologicalCall["Teleological LLM Call"]
                P_BackendRouter -- "instantiation" --> P_TemplateCall["Template LLM Call"]

                P_FactualCall --> P_ProcessResponse["Process Response"]
                P_ExploratoryCall --> P_ProcessResponse
                P_TeleologicalCall --> P_ProcessResponse
                P_TemplateCall --> P_ProcessResponse

                P_ProcessResponse --> P_FrontendRouter["Frontend Router"]
            end

            classDef current fill:#bbdefb,stroke:#64b5f6,stroke-width:1px
            classDef proposed fill:#c8e6c9,stroke:#66bb6a,stroke-width:1px
            classDef router fill:#ffcc80,stroke:#ff9800,stroke-width:1px

            class C_UserInput,C_FrontendAPI1,C_FrontendAPI2,C_BackendAPI1,C_BackendAPI2,C_LLMCall1,C_LLMCall2,C_ProcessResponse1,C_ProcessResponse2,C_FrontendRouter1,C_FrontendRouter2,C_Display,C_CreateSheet current
            class P_UserInput,P_FrontendAPI,P_BackendAPI,P_IntentDetection,P_FactualCall,P_ExploratoryCall,P_TeleologicalCall,P_TemplateCall,P_ProcessResponse proposed
            class P_FrontendRouter,P_BackendRouter router
        </div>
    </div>

    <div class="section">
        <div class="section-title">
            <span class="dot"></span>
            <h2>Explanation of Diagrams</h2>
        </div>

        <h3>Sequence Diagram Notation</h3>
        <ul>
            <li><strong>Participants:</strong> Represented as boxes at the top (User, Frontend, Router, etc.)</li>
            <li><strong>Messages:</strong> Arrows between participants showing the flow of communication</li>
            <li><strong>alt:</strong> Represents alternative paths in the sequence (like an if/else statement). The diagram shows different execution paths based on conditions.</li>
            <li><strong>Note:</strong> Additional information or explanation about a specific step</li>
        </ul>

        <h3>Current Implementation (As-Is)</h3>
        <p>The current implementation uses a two-step API call process that is entirely coordinated by the frontend:</p>
        <ol>
            <li><strong>First API Call:</strong> The frontend calls <code>GovernanceAPI.sendGovernanceMessage()</code> which makes a POST request to <code>/api/llm/chat</code> with <code>prompt_type="g-llm_dialogue"</code> or <code>"initiation_prompt2"</code>. The backend loads the appropriate YAML prompt template, calls the LLM, and returns an MBCP response with the detected intent.</li>
            <li><strong>Frontend Routing:</strong> The MainRouter in the frontend (<code>MainRouter.routeIntent()</code>) decides what to do based on the detected intent. For factual intents, it simply displays the content in the GovernanceChat.</li>
            <li><strong>Second API Call (for non-factual intents):</strong> If the intent is non-factual (exploratory, teleological, instantiation), the frontend makes a completely separate second API call to <code>/api/llm/chat</code> with an intent-specific prompt type (e.g., "chatfork", "mindmap", "template"). The backend has no knowledge that this is related to the previous call - it treats it as a brand new request.</li>
            <li><strong>Final Display:</strong> The frontend receives the second MBCP response and passes it to the MainRouter again, which then creates the appropriate visualization (ChatFork, MindMap, or Template) and displays it to the user.</li>
        </ol>

        <h3>Key Issues with Current Implementation</h3>
        <ul>
            <li><strong>Frontend Coordination:</strong> The frontend is responsible for making two separate API calls and coordinating the entire workflow</li>
            <li><strong>Stateless Backend:</strong> The backend treats each API call as completely independent and maintains no state between calls</li>
            <li><strong>Duplicate Processing:</strong> The same user message is sent twice to the backend, and the LLM is called twice for non-factual intents</li>
            <li><strong>Potential Inconsistency:</strong> If the frontend doesn't correctly handle the intent from the first call, it might use the wrong prompt type for the second call</li>
            <li><strong>Manual Intent Mapping:</strong> The frontend has to manually map intents to the appropriate prompt types and visualization components</li>
            <li><strong>No Backend Routing:</strong> The backend doesn't perform any routing based on intent - all routing decisions are made in the frontend</li>
        </ul>
    </div>

    <div class="note">
        <p><strong>Note:</strong> This visualization represents the current and proposed communication flow between the frontend and backend over the API. The current implementation relies on the frontend router to manage both manual and automated LLM workflows, while the proposed improvements suggest moving some of the routing logic to the backend to streamline the process and ensure consistent handling of different intents.</p>
    </div>

</body>
</html>
