<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>MindBack - AI-Powered Meeting Facilitator</title>
  <style>
    /* Global CSS fixes */
    #root {
      width: 100%;
      height: 100vh;
      overflow: hidden;
    }
    
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #f0f0f0;
    }
    
    * {
      box-sizing: border-box;
    }
    
    /* Debug classes to help diagnose invisible elements */
    .react-debug {
      border: 2px solid red !important;
      background-color: rgba(255, 0, 0, 0.1) !important;
    }
    
    .react-debug * {
      outline: 1px dashed blue !important;
    }
  </style>
  <script>
    // Add error logging
    window.addEventListener('error', function(event) {
      console.error('Global error caught:', event.error);
    });
    
    // Debug helper functions
    window.debugElements = function() {
      document.body.classList.toggle('react-debug');
    };
    
    // Log render timings
    window.renderTimes = [];
    window.logRender = function(componentName) {
      const time = new Date().toISOString();
      window.renderTimes.push({ component: componentName, time });
      console.log(`[RENDER] ${componentName} at ${time}`);
    };
  </script>
</head>
<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>
</html> 