// events-shim.js - Direct replacement for Node.js events module
(function() {
  // Define the EventEmitter class
  class EventEmitter {
    constructor() {
      this._events = {};
      this._eventsCount = 0;
      this._maxListeners = undefined;
    }

    setMaxListeners(n) {
      this._maxListeners = n;
      return this;
    }

    getMaxListeners() {
      return this._maxListeners === undefined ? 10 : this._maxListeners;
    }

    emit(type, ...args) {
      if (!this._events[type]) return false;
      
      const handlers = this._events[type];
      if (Array.isArray(handlers)) {
        for (let i = 0; i < handlers.length; i++) {
          handlers[i].apply(this, args);
        }
      } else {
        handlers.apply(this, args);
      }
      
      return true;
    }

    addListener(type, listener) {
      return this.on(type, listener);
    }

    on(type, listener) {
      if (this._events[type]) {
        if (Array.isArray(this._events[type])) {
          this._events[type].push(listener);
        } else {
          this._events[type] = [this._events[type], listener];
        }
      } else {
        this._events[type] = listener;
        this._eventsCount++;
      }
      
      return this;
    }

    once(type, listener) {
      const onceWrapper = (...args) => {
        this.removeListener(type, onceWrapper);
        listener.apply(this, args);
      };
      
      onceWrapper.listener = listener;
      this.on(type, onceWrapper);
      
      return this;
    }

    removeListener(type, listener) {
      if (!this._events[type]) return this;
      
      if (Array.isArray(this._events[type])) {
        const idx = this._events[type].indexOf(listener);
        if (idx !== -1) {
          this._events[type].splice(idx, 1);
          if (this._events[type].length === 1) {
            this._events[type] = this._events[type][0];
          }
        }
        if (this._events[type].length === 0) {
          delete this._events[type];
          this._eventsCount--;
        }
      } else if (this._events[type] === listener) {
        delete this._events[type];
        this._eventsCount--;
      }
      
      return this;
    }

    off(type, listener) {
      return this.removeListener(type, listener);
    }

    removeAllListeners(type) {
      if (type) {
        if (this._events[type]) {
          delete this._events[type];
          this._eventsCount--;
        }
      } else {
        this._events = {};
        this._eventsCount = 0;
      }
      
      return this;
    }

    listeners(type) {
      if (!this._events[type]) return [];
      
      if (Array.isArray(this._events[type])) {
        return this._events[type].slice();
      } else {
        return [this._events[type]];
      }
    }
  }

  // Create the module
  const eventsModule = {
    EventEmitter: EventEmitter
  };

  // Make it globally available
  window.EventEmitter = EventEmitter;
  window.eventsModule = eventsModule;

  // Handle CommonJS require
  if (typeof window.require !== 'function') {
    window.require = function(moduleName) {
      if (moduleName === 'events') {
        return eventsModule;
      }
      throw new Error(`Cannot find module '${moduleName}'`);
    };
  } else {
    const originalRequire = window.require;
    window.require = function(moduleName) {
      if (moduleName === 'events') {
        return eventsModule;
      }
      return originalRequire(moduleName);
    };
  }

  console.log('Events shim loaded successfully');
})();
