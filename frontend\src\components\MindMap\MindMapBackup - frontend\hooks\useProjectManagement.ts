import { useCallback } from 'react';
import { useMindMap } from '../context/MindMapContext';
import { Project, StorageData, MindMapError, Node, Connection } from '../types';

export const useProjectManagement = () => {
  const {
    nodes,
    setNodes,
    connections,
    setConnections,
    projectName,
    setProjectName,
    projects,
    setProjects,
    setLastSaved,
    direction
  } = useMindMap();

  // Error handling
  const handleError = (error: unknown, context: string): MindMapError => {
    console.error(`Error in ${context}:`, error);
    return {
      code: 'PROJECT_MANAGEMENT_ERROR',
      message: `Failed to ${context}`,
      details: error
    };
  };

  // Load initial data from localStorage
  const loadInitialData = useCallback(() => {
    try {
      // Load saved mind map
      const savedData = localStorage.getItem('mindmap');
      if (savedData) {
        const parsed = JSON.parse(savedData) as StorageData;
        setNodes(parsed.nodes || []);
        setConnections(parsed.connections || []);
        setProjectName(parsed.projectName || 'New Mind Map');
        setLastSaved(parsed.lastSaved || null);
      } else {
        // Initialize with a root node if no data exists
        const rootNode: Node = {
          id: 'root',
          text: 'Main Idea',
          x: 400,
          y: 300,
          width: 100,
          height: 50,
          color: '#4dabf7'
        };
        setNodes([rootNode]);
      }
      
      // Load project list
      const savedProjects = localStorage.getItem('mindmap_projects');
      if (savedProjects) {
        setProjects(JSON.parse(savedProjects));
      } else {
        // Create sample projects if none exist
        const sampleProjects: Project[] = [
          {
            id: 'sample1',
            name: 'Project Planning',
            nodes: [{
              id: 'root',
              text: 'Project Planning',
              x: 400,
              y: 300,
              width: 120,
              height: 50,
              color: '#4dabf7'
            }],
            connections: [],
            lastModified: new Date().toISOString()
          }
        ];
        setProjects(sampleProjects);
        localStorage.setItem('mindmap_projects', JSON.stringify(sampleProjects));
      }
    } catch (error) {
      throw handleError(error, 'load initial data');
    }
  }, [setNodes, setConnections, setProjectName, setProjects, setLastSaved]);

  // Save current mind map to localStorage
  const saveToLocalStorage = useCallback(() => {
    try {
      const data: StorageData = {
        nodes,
        connections,
        projectName,
        direction,
        lastSaved: new Date().toISOString()
      };
      
      localStorage.setItem('mindmap', JSON.stringify(data));
      setLastSaved(data.lastSaved);
      return true;
    } catch (error) {
      throw handleError(error, 'save to localStorage');
    }
  }, [nodes, connections, projectName, direction, setLastSaved]);

  // Save as a named project
  const saveProject = useCallback(() => {
    try {
      let name = projectName;
      if (name === 'New Mind Map') {
        const newName = prompt('Enter a name for your mind map:', 'My Mind Map');
        if (!newName) return false; // User cancelled
        name = newName;
        setProjectName(name);
      }
      
      const project: Project = {
        id: `project_${Date.now()}`,
        name,
        nodes,
        connections,
        lastModified: new Date().toISOString()
      };
      
      // Update or add to projects list
      const existingIndex = projects.findIndex(p => p.name === name);
      let updatedProjects: Project[];
      
      if (existingIndex >= 0) {
        updatedProjects = [...projects];
        updatedProjects[existingIndex] = project;
      } else {
        updatedProjects = [...projects, project];
      }
      
      // Save to localStorage
      localStorage.setItem('mindmap_projects', JSON.stringify(updatedProjects));
      setProjects(updatedProjects);
      saveToLocalStorage();
      return true;
    } catch (error) {
      throw handleError(error, 'save project');
    }
  }, [projectName, nodes, connections, projects, setProjectName, setProjects, saveToLocalStorage]);

  // Load a project
  const loadProject = useCallback((id: string) => {
    try {
      const project = projects.find(p => p.id === id);
      if (!project) {
        throw new Error('Project not found');
      }
      
      setNodes(project.nodes);
      setConnections(project.connections);
      setProjectName(project.name);
      setLastSaved(project.lastModified);
      
      // Save current state to localStorage
      setTimeout(saveToLocalStorage, 0);
      return true;
    } catch (error) {
      throw handleError(error, 'load project');
    }
  }, [projects, setNodes, setConnections, setProjectName, setLastSaved, saveToLocalStorage]);

  // Delete a project
  const deleteProject = useCallback((id: string) => {
    try {
      const updatedProjects = projects.filter(p => p.id !== id);
      localStorage.setItem('mindmap_projects', JSON.stringify(updatedProjects));
      setProjects(updatedProjects);
      return true;
    } catch (error) {
      throw handleError(error, 'delete project');
    }
  }, [projects, setProjects]);

  return {
    loadInitialData,
    saveToLocalStorage,
    saveProject,
    loadProject,
    deleteProject
  };
};
