/* Project Management Dialog Styles */
.project-management-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.project-management-dialog {
  width: 500px;
  max-width: 90vw;
  max-height: 80vh;
  background-color: #f8fafc;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Header styles */
.project-management-dialog .dialog-header {
  background-color: #000000;
  color: white;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-family: Arial, sans-serif;
}

.project-management-dialog .dialog-header-logo {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}

.project-management-dialog .dialog-title {
  font-size: 16px;
  font-weight: normal;
  flex-grow: 1;
}

.project-management-dialog .close-button {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  line-height: 1;
}

/* Tabs styles */
.project-management-dialog .dialog-tabs {
  display: flex;
  border-bottom: 1px solid #e2e8f0;
}

.project-management-dialog .tab-button {
  flex: 1;
  padding: 12px;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s;
}

.project-management-dialog .tab-button.active {
  color: #000;
  border-bottom-color: #000;
}

.project-management-dialog .tab-button:hover:not(.active) {
  background-color: #f1f5f9;
}

/* Content styles */
.project-management-dialog .dialog-content {
  padding: 16px;
  overflow-y: auto;
  flex-grow: 1;
}

.project-management-dialog .tab-panel {
  display: flex;
  flex-direction: column;
}

.project-management-dialog h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #334155;
}

.project-management-dialog p {
  margin: 0 0 16px 0;
  color: #64748b;
  font-size: 14px;
}

/* Error message */
.project-management-dialog .error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 14px;
}

/* Project list styles */
.project-management-dialog .project-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.project-management-dialog .project-item {
  padding: 10px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-management-dialog .project-item:last-child {
  border-bottom: none;
}

.project-management-dialog .project-name {
  cursor: pointer;
  flex-grow: 1;
  font-size: 14px;
  display: flex;
  flex-direction: column;
}

.project-management-dialog .project-date {
  font-size: 12px;
  color: #94a3b8;
  margin-top: 4px;
}

.project-management-dialog .project-actions {
  display: flex;
  gap: 4px;
}

.project-management-dialog .project-actions button {
  background: none;
  border: 1px solid #cbd5e1;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  color: #64748b;
}

.project-management-dialog .project-actions button:hover {
  background-color: #f1f5f9;
}

/* Input styles */
.project-management-dialog .input-group {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.project-management-dialog input[type="text"] {
  flex-grow: 1;
  padding: 8px 12px;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  font-size: 14px;
  font-family: inherit;
}

.project-management-dialog input[type="text"]:focus {
  outline: none;
  border-color: #000;
}

/* Rename container */
.project-management-dialog .rename-container {
  display: flex;
  gap: 8px;
  width: 100%;
}

.project-management-dialog .rename-container input {
  flex-grow: 1;
}

.project-management-dialog .rename-container button {
  background-color: #000;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

/* Button styles */
.project-management-dialog .action-button {
  background-color: #000;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.project-management-dialog .action-button:hover {
  background-color: #262626;
}

.project-management-dialog .action-button:disabled {
  background-color: #94a3b8;
  cursor: not-allowed;
}

/* Footer styles */
.project-management-dialog .dialog-footer {
  padding: 12px 16px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #e2e8f0;
}

.project-management-dialog .cancel-button {
  background: none;
  border: 1px solid #cbd5e1;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  color: #64748b;
  transition: all 0.2s;
}

.project-management-dialog .cancel-button:hover {
  background-color: #f1f5f9;
  color: #334155;
}

/* Project Dialog Overlay */
.project-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* Dialog Container */
.project-dialog-container {
  width: 600px;
  max-width: 90%;
  background-color: #f5f5f5;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  max-height: 80vh;
  overflow: hidden;
}

/* Dialog Header */
.project-dialog-header {
  background-color: #2c3e50;
  color: white;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.project-dialog-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0 8px;
  line-height: 1;
}

.close-button:hover {
  color: #ddd;
}

/* Tabs */
.project-dialog-tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  background-color: #e9e9e9;
}

.tab-button {
  flex: 1;
  padding: 12px;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  color: #666;
}

.tab-button:hover {
  background-color: #ddd;
}

.tab-button.active {
  border-bottom: 3px solid #2c3e50;
  color: #2c3e50;
  background-color: #f5f5f5;
}

/* Dialog Content */
.project-dialog-content {
  padding: 20px;
  overflow-y: auto;
  max-height: calc(80vh - 180px);
}

/* Error Message */
.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
  border: 1px solid #f5c6cb;
}

/* Projects List */
.projects-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.project-item {
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 8px;
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
}

.project-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.project-name {
  font-weight: 500;
  color: #333;
}

.project-date {
  font-size: 0.8rem;
  color: #777;
  margin-top: 4px;
}

.project-actions {
  display: flex;
  gap: 5px;
}

/* Rename Controls */
.rename-controls {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rename-controls input {
  flex: 1;
  margin-right: 10px;
}

.rename-controls div {
  display: flex;
  gap: 5px;
}

/* Input Fields */
input[type="text"] {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  width: 100%;
}

.input-group {
  display: flex;
  margin-top: 10px;
  gap: 10px;
}

/* Buttons */
.action-button {
  background-color: #2c3e50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 15px;
  cursor: pointer;
  font-weight: 500;
}

.action-button:hover {
  background-color: #3d5876;
}

.action-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.small-button {
  background-color: #e9e9e9;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 0.8rem;
  cursor: pointer;
  color: #333;
}

.small-button:hover {
  background-color: #ddd;
}

.small-button.delete {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.small-button.delete:hover {
  background-color: #f5c6cb;
}

.small-button.cancel {
  background-color: #e9e9e9;
  color: #666;
}

/* Current Project */
.current-project {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ddd;
}

.save-as, .info-text {
  margin-bottom: 15px;
}

/* Dialog Footer */
.project-dialog-footer {
  padding: 15px;
  border-top: 1px solid #ddd;
  display: flex;
  justify-content: flex-end;
}

.cancel-button {
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 15px;
  cursor: pointer;
  font-weight: 500;
}

.cancel-button:hover {
  background-color: #5a6268;
}

.success-message {
  background-color: #dff0d8;
  color: #3c763d;
  padding: 10px;
  margin-bottom: 15px;
  border-radius: 4px;
  border: 1px solid #d6e9c6;
}

.export-section, .import-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.export-section:last-child, .import-section:last-child {
  border-bottom: none;
}

.export-project-list {
  max-height: 200px;
  overflow-y: auto;
}

.import-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
}

.file-input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* Hide the default file input but keep it accessible */
input[type="file"] {
  display: none;
} 