.layout-selector {
  width: 100%;
  margin-bottom: 10px;
  margin-top: 10px;
  padding: 10px;
  border-radius: 6px;
  background-color: #f8f9fa;
}

.layout-selector-header {
  margin-bottom: 5px;
}

.layout-selector-header h4 {
  margin: 0 0 10px 0;
  padding: 0;
  font-size: 14px;
  color: #333;
}

.layout-selector-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.layout-selector-row {
  display: flex;
  gap: 8px;
  align-items: center;
}

.layout-strategy-select {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
}

.apply-layout-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.apply-layout-button:hover {
  background-color: #e9ecef;
}

.button-icon {
  margin-right: 8px;
  font-size: 14px;
}

.button-text {
  font-size: 14px;
}

/* Compact layout specific styles */
.compact-layout-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.compact-badge {
  background-color: #f43f5e;
  color: white;
  font-size: 10px;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 10px;
  display: inline-block;
  line-height: 1;
  animation: pulseBadge 2s infinite;
}

.layout-description {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  margin-bottom: 6px;
  font-style: italic;
  padding-left: 4px;
  border-left: 2px solid #f43f5e;
}

@keyframes pulseBadge {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}
