# MindBack_V1 - Enhanced System Architecture (Detailed Level)

```mermaid
graph TD
    User([User]) -->|Interacts with| Frontend[MindBack_V1 Frontend]
    
    subgraph "Frontend Layer (340 files)"
        Frontend --> UI[User Interface]
        UI --> ReactComponents[React Components]
        
        subgraph "Real React Components"
            RC_Pg[Pg]
            RC_F1[F]
            RC_Pe2[Pe]
            RC_T3[T]
            RC_I4[I]
            RC_ZO5[ZO]
        end
        ReactComponents --> RC_Pg
        UI --> Pages[React Pages]
        UI --> Hooks[Custom Hooks]
        UI --> FrontendServices[Frontend Services]
    end
    
    Frontend -->|HTTP Requests| APIGateway[API Gateway]
    
    subgraph "Backend Layer (10917 files)"
        APIGateway --> Endpoints[API Endpoints]
        
        subgraph "Real API Endpoints"
            API_POST_mindmap_save[POST /mindmap/save]
            API_GET_mindmap_load1[GET /mindmap/load]
            API_GET2[GET /]
            API_GET_api_health3[GET /api/health]
            API_GET4[GET /]
            API_GET_health5[GET /health]
        end
        Endpoints --> API_POST_mindmap_save
        
        subgraph "Backend Classes & Services"
            CLS_ConnectionManager[ConnectionManager]
            CLS_ConnectionManager -->|Methods: __init__, disconnect| CLS_ConnectionManagerMethods[Business Logic]
            CLS_MindMapNode1[MindMapNode]
            CLS_Settings2[Settings]
            CLS_Config3[Config]
        end
        API_0 --> CLS_ConnectionManager
        Endpoints --> FastAPIApp[FastAPI Application]
        FastAPIApp --> BackendServices[Business Services]
        BackendServices --> DataStorage[Data Storage]
    end
    
    %% Enhanced Project Statistics:
    %% Frontend Files: 340
    %% Backend Files: 10917
    %% React Components Found: 8
    %% API Endpoints Found: 15
    %% Functions Analyzed: 12
    %% Classes Found: 15
    %% Frameworks: Python, Python, Python, FastAPI, React, Vite
    
    classDef frontend fill:#42a5f5,stroke:#1976d2,color:white;
    classDef backend fill:#26a69a,stroke:#00796b,color:white;
    classDef storage fill:#ef5350,stroke:#c62828,color:white;
    classDef user fill:#78909c,stroke:#455a64,color:white;
    classDef realdata fill:#ff9800,stroke:#e65100,color:white;
    classDef apiendpoint fill:#9c27b0,stroke:#4a148c,color:white;
    
    class User user;
    class Frontend,UI,Pages,Hooks,FrontendServices frontend;
    class ReactComponents,RC0,RC1,RC2,RC3,RC4,RC5 realdata;
    class APIGateway,Endpoints,FastAPIApp,BackendServices backend;
    class EP0,EP1,EP2,EP3,EP4,EP5 apiendpoint;
    class CLS0,CLS1,CLS2,CLS3 realdata;
    class DataStorage storage;
```    class FastAPIApp framework;
```