import { Connection } from '../../core/models/Connection';
import { Node } from '../../core/models/Node';
import { DEFAULT_LAYOUT_CONFIG, LayoutConfig, LayoutStrategy, LayoutStrategyType } from '../types';
import { adjustOverlappingNodes, buildNodeLevels } from '../utils';

/**
 * Compact left-to-right layout strategy for hierarchical trees
 * Places nodes in a left-to-right tree structure with optimized spacing
 * to reduce whitespace and create a more compact visualization
 */
export class CompactLeftToRightLayout implements LayoutStrategy {
  readonly name: LayoutStrategyType = 'compactLeftToRight';

  calculateLayout(
    nodes: Record<string, Node>,
    connections: Connection[],
    rootId: string,
    config: LayoutConfig = DEFAULT_LAYOUT_CONFIG
  ): Record<string, Node> {
    if (!nodes[rootId]) {
      console.warn('Root node not found:', rootId);
      return nodes;
    }

    // Build levels through BFS
    const levels = buildNodeLevels(nodes, connections, rootId);
    
    // Copy nodes to avoid mutating the original
    const updatedNodes = { ...nodes };
    
    // Prepare adaptive spacing if enabled
    const adaptiveSpacing = config.adaptiveSpacing || false;
    const siblingFactor = config.siblingCompactionFactor || 0.6;
    const levelFactor = config.levelCompactionFactor || 0.7;
    const minHSpacing = config.minimumHorizontalSpacing || 30;
    const minVSpacing = config.minimumVerticalSpacing || 25;
    
    // Calculate level content size for adaptive spacing
    const levelContentSizes = levels.map(level => {
      return level.nodes.reduce((acc, nodeId) => {
        const node = nodes[nodeId];
        const contentSize = adaptiveSpacing 
          ? Math.min(node.metadata?.content?.length || 0, 300) / 10
          : 0;
        return Math.max(acc, contentSize);
      }, 0);
    });
    
    // Calculate positions level by level with compaction
    levels.forEach((level, levelIndex) => {
      const levelNodes = level.nodes;
      
      // Calculate vertical spacing with compaction
      const verticalSpacing = Math.max(
        minVSpacing,
        config.verticalSpacing * (1 - (levelIndex * 0.05)) * siblingFactor
      );
      
      // Apply content-based spacing adjustment if enabled
      const contentSizeFactor = adaptiveSpacing 
        ? (1 + (levelContentSizes[levelIndex] * 0.02)) 
        : 1;
        
      const nodeHeight = config.nodeHeight * contentSizeFactor;
      
      // Create more compact vertical arrangement
      const totalHeight = levelNodes.length * (nodeHeight + verticalSpacing);
      const startY = -totalHeight / 2;
      
      // Calculate level spacing with progressive compaction
      // Deeper levels have more compaction
      const levelSpacing = Math.max(
        config.levelSpacing * levelFactor,
        config.levelSpacing * (1 - (levelIndex * 0.1))
      );
      
      // Position nodes in this level with compaction
      levelNodes.forEach((nodeId, nodeIndex) => {
        const node = updatedNodes[nodeId];
        if (!node) return;
        
        // Calculate compacted position
        const x = levelIndex * levelSpacing;
        const y = startY + nodeIndex * (nodeHeight + verticalSpacing);
        
        // Update node position
        updatedNodes[nodeId] = {
          ...node,
          x,
          y
        };
      });
    });

    // Group sibling nodes closer together
    Object.keys(updatedNodes).forEach(nodeId => {
      const childIds = connections
        .filter(conn => conn.from === nodeId)
        .map(conn => conn.to);
        
      if (childIds.length > 1) {
        // Calculate average Y position of children
        const childYPositions = childIds.map(id => updatedNodes[id].y);
        const avgY = childYPositions.reduce((sum, y) => sum + y, 0) / childIds.length;
        
        // Move children closer to average Y, preserving order
        childIds.forEach((childId, index) => {
          const node = updatedNodes[childId];
          const direction = node.y < avgY ? 1 : -1;
          const moveCloser = Math.abs(node.y - avgY) * 0.2 * direction;
          
          updatedNodes[childId] = {
            ...node,
            y: node.y + moveCloser
          };
        });
      }
    });

    // Apply final overlap prevention with reduced spacing requirements
    const compactConfig = {
      ...config,
      horizontalSpacing: Math.max(minHSpacing, config.horizontalSpacing * siblingFactor),
      verticalSpacing: Math.max(minVSpacing, config.verticalSpacing * siblingFactor)
    };
    
    return adjustOverlappingNodes(updatedNodes, connections, compactConfig, true);
  }
} 