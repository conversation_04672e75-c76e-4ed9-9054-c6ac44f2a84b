import React from 'react';
import { ChatForkNodeData } from './index.tsx';
import './ChatFork.css';

interface ChatForkNodeProps {
  node: ChatForkNodeData;
  isSelected: boolean;
  onClick: () => void;
}

const ChatForkNode: React.FC<ChatForkNodeProps> = ({ node, isSelected, onClick }) => {
  // Determine class based on node type and selection state
  const getNodeClass = () => {
    const baseClass = 'chat-fork-node';
    const typeClass = `chat-fork-node-${node.type}`;
    const selectedClass = isSelected ? 'chat-fork-node-selected' : '';
    
    return `${baseClass} ${typeClass} ${selectedClass}`.trim();
  };

  const formatNodeId = (id: string) => {
    return id.replace(/\./g, '.'); // Replace dots with dots (for display formatting)
  };

  return (
    <div 
      className={getNodeClass()}
      data-id={node.id}
      data-level={node.level}
      data-index={node.index}
      onClick={onClick}
    >
      <div className="chat-fork-node-id">{formatNodeId(node.id)}</div>
      <div className="chat-fork-node-content">{node.text}</div>
      
      {node.type === 'answer' && (
        <div className="chat-fork-node-context-links">
          <span className="context-link">Context</span>
          <span className="context-link">Context</span>
        </div>
      )}
    </div>
  );
};

export default ChatForkNode; 