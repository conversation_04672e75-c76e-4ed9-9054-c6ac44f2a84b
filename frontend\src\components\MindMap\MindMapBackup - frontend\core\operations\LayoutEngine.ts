// Placeholder file for: frontend\src\components\MindMap\core\operations\LayoutEngine.ts

import { Node } from '../models/Node';
import { Connection } from '../models/Connection';

interface LayoutConfig {
  nodeWidth: number;
  nodeHeight: number;
  horizontalSpacing: number;
  verticalSpacing: number;
  levelSpacing: number;
  initialDirection: number;
}

export const DEFAULT_CONFIG: LayoutConfig = {
  nodeWidth: 200,
  nodeHeight: 100,
  horizontalSpacing: 100,
  verticalSpacing: 80,
  levelSpacing: 250,
  initialDirection: 90, // Start expanding right
};

/**
 * Calculates the tree layout for nodes with directional support
 */
export function calculateTreeLayout(
  nodes: Record<string, Node>,
  connections: Connection[],
  rootId: string,
  config: LayoutConfig = DEFAULT_CONFIG
): Record<string, Node> {
  console.log('Calculating tree layout:', { rootId, nodesCount: Object.keys(nodes).length });
  
  if (!nodes[rootId]) {
    console.warn('Root node not found:', rootId);
    return nodes;
  }

  // Build levels through BFS
  const levels = buildNodeLevels(nodes, connections, rootId);
  console.log('Built node levels:', levels.map(level => level.length));

  // Calculate initial positions
  const angle = (config.initialDirection * Math.PI) / 180;
  const directionVector = { x: Math.cos(angle), y: Math.sin(angle) };
  
  const updatedNodes = { ...nodes };
  let maxY = 0;
  
  levels.forEach((level, levelIndex) => {
    const levelOffset = levelIndex * config.levelSpacing;
    
    level.forEach((nodeId, nodeIndex) => {
      const node = updatedNodes[nodeId];
      if (!node) return;

      // Calculate base position
      const x = directionVector.x * levelOffset;
      const y = (nodeIndex - (level.length - 1) / 2) * (config.nodeHeight + config.verticalSpacing);
      
      // Update node position
      node.x = x;
      node.y = y;
      maxY = Math.max(maxY, Math.abs(y));
      
      console.log('Positioned node:', { id: nodeId, x, y, level: levelIndex, index: nodeIndex });
    });
  });

  // Adjust for overlapping
  return adjustOverlappingNodes(updatedNodes, config);
}

/**
 * Checks if two nodes overlap
 */
export function checkNodeOverlap(nodeA: Node, nodeB: Node, config: LayoutConfig): boolean {
  const xOverlap = Math.abs(nodeA.x - nodeB.x) < config.nodeWidth + config.horizontalSpacing;
  const yOverlap = Math.abs(nodeA.y - nodeB.y) < config.nodeHeight + config.verticalSpacing;
  return xOverlap && yOverlap;
}

/**
 * Adjusts node positions to prevent overlap
 */
export function adjustOverlappingNodes(
  nodes: Record<string, Node>,
  config: LayoutConfig
): Record<string, Node> {
  const nodeList = Object.values(nodes);
  const updatedNodes = { ...nodes };
  let iterations = 0;
  const maxIterations = 10;
  let hasOverlap = true;

  while (hasOverlap && iterations < maxIterations) {
    hasOverlap = false;
    iterations++;

    for (let i = 0; i < nodeList.length; i++) {
      for (let j = i + 1; j < nodeList.length; j++) {
        const nodeA = nodeList[i];
        const nodeB = nodeList[j];
        
        if (checkNodeOverlap(nodeA, nodeB, config)) {
          hasOverlap = true;
          
          // Calculate repulsion force
          const dx = nodeB.x - nodeA.x;
          const dy = nodeB.y - nodeA.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          const minDistance = Math.sqrt(
            Math.pow(config.nodeWidth + config.horizontalSpacing, 2) +
            Math.pow(config.nodeHeight + config.verticalSpacing, 2)
          );
          
          if (distance < minDistance) {
            const force = (minDistance - distance) / distance;
            const moveX = (dx * force) / 2;
            const moveY = (dy * force) / 2;
            
            updatedNodes[nodeA.id].x -= moveX;
            updatedNodes[nodeA.id].y -= moveY;
            updatedNodes[nodeB.id].x += moveX;
            updatedNodes[nodeB.id].y += moveY;
            
            console.log('Adjusted overlapping nodes:', {
              nodeA: nodeA.id,
              nodeB: nodeB.id,
              moveX,
              moveY
            });
          }
        }
      }
    }
  }

  if (iterations === maxIterations) {
    console.warn('Max iterations reached while adjusting overlapping nodes');
  }

  return updatedNodes;
}

function buildNodeLevels(
  nodes: Record<string, Node>,
  connections: Connection[],
  rootId: string
): string[][] {
  const levels: string[][] = [];
  const processed = new Set<string>();

  // Queue for BFS
  const queue: { nodeId: string; level: number }[] = [{ nodeId: rootId, level: 0 }];
  processed.add(rootId);

  while (queue.length > 0) {
    const { nodeId, level } = queue.shift()!;

    // Ensure level array exists
    if (!levels[level]) {
      levels[level] = [];
    }
    levels[level].push(nodeId);

    // Find children
    const children = connections
      .filter(conn => conn.from === nodeId && !processed.has(conn.to))
      .map(conn => conn.to);

    // Add children to queue
    children.forEach(childId => {
      if (!processed.has(childId)) {
        processed.add(childId);
        queue.push({ nodeId: childId, level: level + 1 });
      }
    });
  }

  return levels;
}

export default {
  calculateTreeLayout,
  checkNodeOverlap,
  adjustOverlappingNodes
};
