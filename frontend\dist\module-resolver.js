// Module resolver for handling Node.js built-in modules in the browser
(function() {
  // Create a map of Node.js built-in modules to browser implementations
  const nodeModules = {
    'events': {
      EventEmitter: window.EventEmitter
    },
    'util': {
      inherits: function(ctor, superCtor) {
        ctor.super_ = superCtor;
        ctor.prototype = Object.create(superCtor.prototype, {
          constructor: {
            value: ctor,
            enumerable: false,
            writable: true,
            configurable: true
          }
        });
      }
    }
  };

  // Override import.meta.resolve for ES modules
  if (window.import && window.import.meta) {
    const originalResolve = window.import.meta.resolve;
    window.import.meta.resolve = function(specifier) {
      if (nodeModules[specifier]) {
        return `data:text/javascript,export default ${JSON.stringify(nodeModules[specifier])}`;
      }
      return originalResolve ? originalResolve(specifier) : specifier;
    };
  }

  // Add a global module resolver
  window.resolveNodeModule = function(moduleName) {
    return nodeModules[moduleName] || null;
  };

  console.log('Module resolver initialized');
})();
