/**
 * Test file for MindSheet Tabs functionality
 * 
 * This file contains test code to verify the mindsheet tabs implementation.
 * It's not meant to be imported into the application, but rather to be used
 * as a reference for testing the functionality.
 */

// Test creating multiple sheets and switching between them
const testMultipleSheets = () => {
  // Get the MindBookStore
  const mindBookStore = window.useMindBookStore.getState();
  
  // Create a mindmap sheet
  const mindmapSheetId = mindBookStore.createMindMapSheet('Test Mindmap', {
    root: {
      id: 'root',
      text: 'Test Mindmap',
      description: 'Test mindmap description',
      children: []
    }
  });
  
  // Create a chatfork sheet
  const chatforkSheetId = mindBookStore.createChatForkSheet('Test ChatFork', {
    text: 'Test ChatFork',
    full_text: 'This is a test ChatFork content',
    responseType: {
      type: 'exploratory',
      requiresMindmap: false,
      requiresChatFork: true
    }
  });
  
  // Log the sheets
  console.log('Sheets:', mindBookStore.sheets);
  
  // Switch between sheets
  console.log('Switching to mindmap sheet');
  mindBookStore.setActiveSheet(mindmapSheetId);
  
  // After a delay, switch to the chatfork sheet
  setTimeout(() => {
    console.log('Switching to chatfork sheet');
    mindBookStore.setActiveSheet(chatforkSheetId);
  }, 2000);
};

// Test creating sheets with the same content type
const testSameTypeSheets = () => {
  // Get the MindBookStore
  const mindBookStore = window.useMindBookStore.getState();
  
  // Create multiple chatfork sheets
  const chatfork1SheetId = mindBookStore.createChatForkSheet('ChatFork 1', {
    text: 'ChatFork 1',
    full_text: 'This is ChatFork 1 content',
    responseType: {
      type: 'exploratory',
      requiresMindmap: false,
      requiresChatFork: true
    }
  });
  
  const chatfork2SheetId = mindBookStore.createChatForkSheet('ChatFork 2', {
    text: 'ChatFork 2',
    full_text: 'This is ChatFork 2 content',
    responseType: {
      type: 'exploratory',
      requiresMindmap: false,
      requiresChatFork: true
    }
  });
  
  // Log the sheets
  console.log('Sheets:', mindBookStore.sheets);
  
  // Switch between sheets
  console.log('Switching to ChatFork 1');
  mindBookStore.setActiveSheet(chatfork1SheetId);
  
  // After a delay, switch to ChatFork 2
  setTimeout(() => {
    console.log('Switching to ChatFork 2');
    mindBookStore.setActiveSheet(chatfork2SheetId);
  }, 2000);
};

// Export the test functions
window.testMindSheetTabs = {
  testMultipleSheets,
  testSameTypeSheets
};
