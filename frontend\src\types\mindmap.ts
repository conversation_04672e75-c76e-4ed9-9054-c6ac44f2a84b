export interface NodeDesign {
  shape: 'circle' | 'rectangle' | 'roundedRect';
  strokeWidth: number;
  strokeColor: string;
  fillColor: string;
  width: number;
  height: number;
  cornerRadius?: number;
}

export interface MindMapNode {
  id: string;
  text: string;
  parent_id: string | null;
  x?: number;
  y?: number;
  design?: NodeDesign;
}

export interface ConnectionDesign {
  strokeWidth: number;
  strokeColor: string;
  style: 'straight' | 'curved';
}

export interface DesignSettings {
  nodeElement: NodeDesign;
  connectionElement: ConnectionDesign;
  connectionText: {
    fontSize: number;
    fontColor: string;
    verticalAlign: 'top' | 'middle' | 'bottom';
    horizontalAlign: 'left' | 'center' | 'right';
  };
}

export interface ProjectMetadata {
  id: string;
  name: string;
  lastModified: string;
}

export enum Direction {
  RIGHT = 0,
  DOWN = 90,
  LEFT = 180,
  UP = 270
} 