# this file describe the SWAT tool, its parameters and the expected outcome structure
tool_id: swat
name: "SWAT Analysis"
purpose: "Evaluate strengths, weaknesses, opportunities, and threats for a decision or strategy."
used_for:
  - Teleological (Goal-Oriented Strategy)
input_type: "Situation or decision context"
output_type: "SWOT table"
return_structure:
  format: "json"
  schema:
    swot:
      strengths: ["{strength_1}", "{strength_2}"]
      weaknesses: ["{weakness_1}", "{weakness_2}"]
      opportunities: ["{opportunity_1}", "{opportunity_2}"]
      threats: ["{threat_1}", "{threat_2}"]
  validation: "Must be a valid JSON object with four categories."
