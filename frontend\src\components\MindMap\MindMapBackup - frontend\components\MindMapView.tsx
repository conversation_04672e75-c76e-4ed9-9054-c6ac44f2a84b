import React, { useCallback, useEffect } from 'react';
import { Node, Connection, Direction } from '../types';
import { useMindMapCore } from '../hooks/useMindMapCore';
import { useZoomAndPan } from '../hooks/useZoomAndPan';
import { Node<PERSON>enderer } from './NodeRenderer';
import { ConnectionRenderer } from './ConnectionRenderer';
import '../MindMap.css';

interface MindMapViewProps {
  onNodeSelect?: (nodeId: string | null) => void;
  onNodeUpdate?: (node: Node) => void;
  onConnectionUpdate?: (connection: Connection) => void;
}

export const MindMapView: React.FC<MindMapViewProps> = ({
  onNodeSelect,
  onNodeUpdate,
  onConnectionUpdate,
}) => {
  const {
    nodes,
    connections,
    selectedNodeId,
    addNode,
    updateNode,
    deleteNode,
    addConnection,
    updateConnection,
    handleNodeSelect,
    changeDirection,
    autoLayout,
  } = useMindMapCore({
    onNodeSelect,
    onNodeUpdate,
    onConnectionUpdate,
  });

  const {
    zoom,
    position,
    handleZoomIn,
    handleZoomOut,
    centerView,
    startDrag,
    drag,
    endDrag,
    canvasRef,
  } = useZoomAndPan();

  const addChildNode = useCallback((parentId: string) => {
    const parentNode = nodes.find(n => n.id === parentId);
    if (parentNode) {
      // Create the new node using the store's addNode method
      // Direction 1 is right (you may need to adjust this based on your application)
      const newNodeId = addNode(parentId, 1);
      
      if (newNodeId) {
        // Node was created, now update its properties
        const childNodePath = calculateNodePath(parentNode);
        
        // Update the node with additional properties
        updateNode(newNodeId, {
          x: parentNode.x + 200,
          y: parentNode.y,
          color: parentNode.color,
          borderColor: parentNode.borderColor,
          metadata: {
            nodePath: childNodePath
          }
        });
        
        // Add connection using the correct property names from CreateConnectionInput
        addConnection({
          from: parentNode.id,
          to: newNodeId,
          lineStyle: 'angled', // Using lineStyle instead of style
          thickness: 2,
          color: '#9ca3af',
        });
      }
    }
  }, [nodes, addNode, updateNode, addConnection]);
  
  // Helper function to calculate node path
  const calculateNodePath = (parentNode: Node): string => {
    // Get parent path or default to '1.0' if not available
    const parentPath = parentNode.metadata?.nodePath || '1.0';
    
    // Find existing children of this parent
    const siblings = nodes.filter(n => n.parentId === parentNode.id);
    
    // Calculate next index for the new child
    const nextIndex = siblings.length + 1;
    
    // If parent is root, children should be 1.1, 1.2, etc.
    if (parentPath === '1.0') {
      return `1.${nextIndex}`;
    }
    
    // Otherwise, append index to parent path
    return `${parentPath}.${nextIndex}`;
  };

  // This function is problematic and needs to be rewritten
  // Let's comment it out for now until we can properly fix it
  /*
  const addSiblingNode = useCallback((nodeId: string) => {
    // Find the current node and its parent connection
    const currentNode = nodes.find(n => n.id === nodeId);
    const parentConnection = connections.find(conn => conn.to === nodeId);
    
    if (currentNode && parentConnection) {
      // Get the parent ID from the connection
      const parentId = parentConnection.from;
      
      // Add a new node with the same parent
      if (parentId) {
        // Direction 1 is right in this example (use the appropriate direction for your app)
        const newNodeId = addNode(parentId, 1);
        
        if (newNodeId) {
          // Position it near the current node but below
          updateNode(newNodeId, {
            x: currentNode.x,
            y: currentNode.y + 100
          });
        }
      }
    }
  }, [nodes, connections, addNode, updateNode]);
  */

  // Implement a proper addSiblingNode function
  const addSiblingNode = useCallback((nodeId: string) => {
    // Find the current node
    const currentNode = nodes.find(n => n.id === nodeId);
    if (!currentNode || !currentNode.parentId) return; // Only nodes with parents can have siblings
    
    // Get the parent ID
    const parentId = currentNode.parentId;
    
    // Create new node using the add node function
    const newNodeId = addNode(parentId, 1); // Direction 1 is right
    
    if (newNodeId) {
      // Calculate the sibling path
      const parentNode = nodes.find(n => n.id === parentId);
      if (!parentNode) return;
      
      const siblingPath = calculateNodePath(parentNode);
      
      // Update the node properties
      updateNode(newNodeId, {
        x: currentNode.x,
        y: currentNode.y + 100, // Position below the current node
        color: currentNode.color,
        borderColor: currentNode.borderColor,
        metadata: {
          nodePath: siblingPath
        }
      });
      
      // Add connection
      addConnection({
        from: parentId,
        to: newNodeId,
        lineStyle: 'angled',
        thickness: 2,
        color: '#9ca3af',
      });
    }
  }, [nodes, addNode, updateNode, addConnection, calculateNodePath]);

  const updateNodePosition = useCallback((nodeId: string, x: number, y: number) => {
    updateNode(nodeId, { x, y });
  }, [updateNode]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (e.key) {
        case 'Tab':
          e.preventDefault();
          if (selectedNodeId) {
            if (e.shiftKey) {
              addSiblingNode(selectedNodeId);
            } else {
              addChildNode(selectedNodeId);
            }
          }
          break;
        case 'Delete':
          if (selectedNodeId) {
            deleteNode(selectedNodeId);
          }
          break;
        case '+':
          handleZoomIn();
          break;
        case '-':
          handleZoomOut();
          break;
        case 'c':
          if (e.ctrlKey) {
            centerView();
          }
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedNodeId, addChildNode, addSiblingNode, deleteNode, handleZoomIn, handleZoomOut, centerView]);

  // Handle mouse events for panning
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.button === 1 || (e.button === 0 && e.altKey)) { // Middle click or Alt + Left click
      e.preventDefault();
      startDrag(e.clientX, e.clientY);
    }
  }, [startDrag]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    drag(e.clientX, e.clientY);
  }, [drag]);

  const handleMouseUp = useCallback(() => {
    endDrag();
  }, [endDrag]);

  // Convert nodes array to record for ConnectionRenderer
  const nodesRecord = nodes.reduce<Record<string, Node>>((acc, node) => {
    acc[node.id] = node;
    return acc;
  }, {});

  // Render nodes and connections
  const renderNodes = () => {
    return nodes.map(node => (
      <NodeRenderer
        key={node.id}
        node={node}
        isSelected={node.id === selectedNodeId}
        zoom={zoom}
        position={position}
        onSelect={(nodeId) => onNodeSelect?.(nodeId)}
        onUpdate={onNodeUpdate}
        onPositionUpdate={updateNodePosition}
      />
    ));
  };

  const renderConnections = () => {
    return connections.map(connection => (
      <ConnectionRenderer
        key={`${connection.from}-${connection.to}`}
        connection={connection}
        nodes={nodesRecord}
        zoom={zoom}
        position={position}
        onUpdate={onConnectionUpdate}
      />
    ));
  };

  // Handle changing the layout direction
  const handleDirectionChange = useCallback((direction: Direction) => {
    changeDirection(direction);
  }, [changeDirection]);

  return (
    <div
      ref={canvasRef}
      className="mindmap-canvas"
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
      style={{
        position: 'relative',
        width: '100%',
        height: '100%',
        overflow: 'hidden',
        cursor: 'default',
      }}
    >
      <div
        className="mindmap-content"
        style={{
          transform: `translate(${position.x}px, ${position.y}px) scale(${zoom})`,
          transformOrigin: '0 0',
          position: 'absolute',
          top: 0,
          left: 0,
        }}
      >
        {renderConnections()}
        {renderNodes()}
      </div>
    </div>
  );
}; 