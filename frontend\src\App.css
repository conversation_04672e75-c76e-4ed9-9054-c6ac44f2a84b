* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  zoom: 1;
  transform: scale(1);
  transform-origin: 0 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  color: #1a1a1a;
  background-color: #f8fafc;
  zoom: 1;
  transform: scale(1);
  transform-origin: 0 0;
  overflow-x: hidden;
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: relative;
}

/* Main content area that contains the MindBook */
.app-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 80px); /* Subtract header and footer heights */
}

/* Header styles - restored to original */
.app-header {
  background-color: #000000;
  color: white;
  padding: 0 20px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 20;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo-slogan {
  color: #ffffff;
  font-family: Arial, sans-serif;
  font-size: 18px;
  font-weight: normal;
  margin-left: 5px;
}

.app-header-logo {
  height: 32px;
  width: auto;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.menu-button {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.5);
  color: white;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.menu-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: white;
}

.help-button {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.5);
  color: white;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: bold;
}

.help-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: white;
}

/* Footer - black with white text, centered brand */
.app-footer {
  display: flex;
  align-items: center;
  padding: 8px 20px;
  background-color: #000000;
  border-top: 1px solid #333333;
  height: 40px;
  font-size: 14px;
  position: relative;
}

.session-info {
  color: #ffffff;
  font-weight: 500;
  position: absolute;
  left: 20px;
}

.session-arrow {
  color: #ffffff;
  font-weight: normal;
  margin-right: 5px;
}

.session-debug {
  color: #888888;
  font-size: 12px;
  margin-left: 10px;
  font-weight: normal;
}

.footer-brand {
  color: #ffffff;
  font-weight: 500;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.footer-right-controls {
  position: absolute;
  right: 20px;
  display: flex;
  align-items: center;
}

.footer-governance-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.footer-governance-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.footer-governance-logo {
  border-radius: 2px;
}

/* Legacy styles for backward compatibility */
.logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-header {
    padding: 8px 16px;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .logo-slogan {
    font-size: 16px;
  }
  
  .header-controls {
    gap: 8px;
  }
  
  .app-footer {
    padding: 8px 16px;
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}

/* Rest of existing styles... */
.initial-view-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background-color: #f8fafc;
}

.initial-view {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

.initial-view .control-buttons-bar {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 10px;
}

.initial-view .control-button {
  padding: 8px 16px;
  background-color: #000;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.initial-view .control-button:hover {
  background-color: #333;
}

.optimized-mind-map {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  background-color: #f8fafc;
}

.mind-map-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  background-color: #f8fafc;
  z-index: 1;
}

.mind-map {
  position: absolute;
  transform-origin: 0 0;
  width: 100%;
  height: 100%;
}

.content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* Panel styles */
.panel {
  position: absolute;
  width: 240px;
  background-color: #ffffff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  left: 10px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f1f5f9;
  border-bottom: 1px solid #e2e8f0;
  font-size: 13px;
  font-weight: 500;
  color: #334155;
}

.panel-toggle {
  background: none;
  border: none;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #64748b;
  font-size: 16px;
  border-radius: 3px;
}

.panel-toggle:hover {
  background-color: #e2e8f0;
}

.panel-content {
  padding: 12px;
}

.control-group {
  margin-bottom: 12px;
}

.control-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.direction-control {
  display: flex;
  align-items: center;
}

.direction-button {
  width: 28px;
  height: 28px;
  background-color: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  color: #334155;
}

.direction-button:hover {
  background-color: #e2e8f0;
}

.zoom-controls {
  display: flex;
  gap: 8px;
}

.control-button {
  flex: 1;
  height: 28px;
  background-color: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  cursor: pointer;
  color: #334155;
  font-size: 14px;
}

.control-button:hover {
  background-color: #e2e8f0;
}

/* Project list */
.project-list {
  max-height: 300px;
  overflow-y: auto;
}

.project-item {
  padding: 8px 10px;
  border-bottom: 1px solid #f1f5f9;
  cursor: pointer;
  font-size: 13px;
  color: #334155;
}

.project-item:hover {
  background-color: #f8fafc;
}

/* Context button and panel */
.context-toggle-button {
  position: absolute;
  top: 45px; /* Position it just under the MB logo */
  left: 20px;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  background-color: #ffffff;
  border: none; /* Remove the border */
  color: #000000;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1001; /* Ensure it's above other elements */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
  transition: all 0.2s ease;
}

.context-toggle-button:hover {
  background-color: #f0f0f0;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.context-panel {
  position: fixed;
  top: 40px;
  left: 0;
  bottom: 40px; /* Adjust for footer height */
  width: 50%;
  max-width: 800px;
  max-height: calc(100vh - 80px); /* Account for header and footer */
  background-color: #ffffff;
  color: #000000;
  z-index: 1000;
  transform: translateX(-100%);
  transition: transform 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
  font-family: Arial, sans-serif;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.context-panel.open {
  transform: translateX(0);
}

.context-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.context-panel-title {
  font-size: 18px;
  font-weight: bold;
}

.context-panel-close {
  background: none;
  border: none;
  color: #000000;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.context-panel-close:hover {
  background-color: #f0f0f0;
}

.context-panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.context-panel-footer {
  padding: 15px 20px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
}

.context-level-section {
  margin-bottom: 30px;
}

.context-level-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  padding-bottom: 5px;
  border-bottom: 1px solid #e0e0e0;
}

.context-input-tabs {
  display: flex;
  margin-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.context-input-tab {
  padding: 8px 15px;
  background: none;
  border: none;
  color: #888888;
  cursor: pointer;
  font-size: 14px;
  transition: color 0.2s;
}

.context-input-tab.active {
  color: #000000;
  border-bottom: 2px solid #3498db;
}

.context-text-input {
  width: 100%;
  background-color: #f5f5f5;
  color: #000000;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 10px;
  font-family: Arial, sans-serif;
  resize: vertical;
  min-height: 100px;
  margin-bottom: 10px;
}

.context-input-actions {
  display: flex;
  justify-content: flex-end;
}

.context-file-upload-button {
  display: inline-block;
  padding: 8px 15px;
  background-color: #f5f5f5;
  color: #000000;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  margin-bottom: 10px;
}

.context-file-upload-info {
  font-size: 12px;
  color: #888888;
  margin-bottom: 10px;
}

.context-reference-selector {
  width: 100%;
  background-color: #f5f5f5;
  color: #000000;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px;
  font-family: Arial, sans-serif;
  margin-bottom: 10px;
}

.context-action-button {
  padding: 8px 15px;
  background-color: #f0f0f0;
  color: #000000;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-family: Arial, sans-serif;
  transition: background-color 0.2s;
}

.context-action-button:hover {
  background-color: #e0e0e0;
}

.context-action-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Loading state styles */
.loading-canvas {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #f8fafc;
}

.initializing-mindmap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.initializing-mindmap p {
  font-size: 16px;
  color: #64748b;
  font-weight: 500;
}

.loading-spinner {
  width: 36px;
  height: 36px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
}