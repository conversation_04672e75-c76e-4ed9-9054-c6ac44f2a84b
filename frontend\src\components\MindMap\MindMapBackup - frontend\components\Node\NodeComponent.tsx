import React, { useCallback } from 'react';
import { Group, Rect, Text } from 'react-konva';
import Konva from 'konva';
import { Node } from '../../types';
import { useMindMap } from '../../context/MindMapContext';

// Cast Konva components to any to bypass TypeScript errors
const KonvaGroup = Group as any;
const KonvaRect = Rect as any;
const KonvaText = Text as any;

interface NodeComponentProps {
  node: Node;
  isSelected: boolean;
}

export const NodeComponent: React.FC<NodeComponentProps> = ({ node, isSelected }) => {
  const { 
    setSelectedId, 
    setNodeText, 
    setNodeColor, 
    setShowNodeDialog,
    updateNodePosition
  } = useMindMap();

  // Handlers from the original OptimizedMindMap
  const handleNodeClick = useCallback(() => {
    setSelectedId(node.id);
  }, [node.id, setSelectedId]);

  const handleDblClick = useCallback(() => {
    setSelectedId(node.id);
    setNodeText(node.text);
    setNodeColor(node.color || '#4dabf7');
    setShowNodeDialog(true);
  }, [node, setSelectedId, setNodeText, setNodeColor, setShowNodeDialog]);

  const handleDragMove = useCallback((e: Konva.KonvaEventObject<DragEvent>) => {
    updateNodePosition(node.id, e.target.x(), e.target.y());
  }, [node.id, updateNodePosition]);

  const handleDragEnd = useCallback((e: Konva.KonvaEventObject<DragEvent>) => {
    // Final update of node position after drag completes
    updateNodePosition(node.id, e.target.x(), e.target.y());
  }, [node.id, updateNodePosition]);

  return (
    <KonvaGroup
      key={node.id}
      x={node.x}
      y={node.y}
      draggable
      onDragMove={handleDragMove}
      onDragEnd={handleDragEnd}
      onClick={handleNodeClick}
      onTap={handleNodeClick}
      onDblClick={handleDblClick}
      onDblTap={handleDblClick}
    >
      {/* Main node rectangle */}
      <KonvaRect
        width={node.width}
        height={node.height}
        fill="#f0f0f0" // Light grey fill
        cornerRadius={8} // Soft edges
        shadowColor="black"
        shadowBlur={isSelected ? 10 : 5}
        shadowOpacity={isSelected ? 0.6 : 0.3}
        shadowOffset={{ x: 2, y: 2 }}
        stroke="#696969" // Dark grey outline
        strokeWidth={isSelected ? 3 : 2}
        x={-node.width / 2}
        y={-node.height / 2}
      />
      
      {/* Node index/path background */}
      <KonvaRect
        width={node.metadata?.nodePath ? node.metadata.nodePath.length * 6 + 8 : 40}
        height={16}
        fill="#696969" // Dark grey
        cornerRadius={3}
        x={-node.width / 2 + 8}
        y={-node.height / 2 + 8}
      />
      
      {/* Node index/path text */}
      <KonvaText
        text={node.metadata?.nodePath || '1.0'}
        fontSize={8}
        fontFamily="Arial"
        fill="#ffffff" // White text
        x={-node.width / 2 + 12}
        y={-node.height / 2 + 10}
      />
      
      {/* Node title */}
      <KonvaText
        text={node.text}
        width={node.width - 16}
        fontSize={10}
        fontFamily="Arial"
        fill="#000000" // Black text
        align="left"
        x={-node.width / 2 + 8}
        y={-node.height / 2 + 30}
      />
      
      {/* Five boxes at the bottom */}
      {[0, 1, 2, 3, 4].map((i) => (
        <KonvaRect
          key={i}
          width={14}
          height={14}
          fill="#f9f9f9" // 10% lighter than the node background
          stroke="#696969" // Dark grey outline
          strokeWidth={1}
          cornerRadius={2}
          x={-node.width / 2 + 8 + i * (14 + 8)} // Position boxes with spacing
          y={node.height / 2 - 22} // Position at bottom with margin
        />
      ))}
    </KonvaGroup>
  );
};

// Export named component instead of default
// export default NodeComponent; 