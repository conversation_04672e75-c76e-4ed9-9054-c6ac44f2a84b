// Export Node Components
export { NodeComponent } from './Node/NodeComponent';
export { ConnectionComponent } from './Node/ConnectionComponent';

// Export Dialog Components
export { NodeDialog } from './Dialogs/NodeDialog';
export { ProjectDialog } from './Dialogs/ProjectDialog';
export { DesignControlsDialog } from './Dialogs/DesignControlsDialog';
export { GovernanceChatDialog } from '../../../governance/chat';

// Export Control Components
export { MindMapToolbar } from './Controls/MindMapToolbar';

// Export Canvas Component
// IMPORTANT: We are exclusively using MindMapCanvasSimple as the implementation
// The original MindMapCanvas.tsx file is kept for reference but is NOT used
export { default as MindMapCanvas } from './Canvas/MindMapCanvasSimple';

// Note: We had persistent TypeScript errors with react-konva components in MindMapCanvas.tsx.
// The MindMapCanvasSimple implementation uses type assertions to work around these issues.
// All new development should be done in MindMapCanvasSimple.tsx, not the legacy file.

// These will be added as we create them
// export { MindMapToolbar } from './Controls/MindMapToolbar';
// export { NodeDialog } from './Dialogs/NodeDialog';
// export { ProjectDialog } from './Dialogs/ProjectDialog';
// export { DesignControlsDialog } from './Dialogs/DesignControlsDialog'; 