/**
 * ApplicationStore.ts
 * 
 * Global application state management for the MindBack application.
 * This is the top-level store in the hierarchy, following the Excel model.
 * 
 * The ApplicationStore manages:
 * 1. Global application settings
 * 2. User preferences
 * 3. UI state (dialogs, panels, etc.)
 * 4. Authentication state
 * 5. Application-wide events
 * 
 * This store should NOT contain MindBook-specific state, which belongs in MindBookStore.
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { EventType } from '../services/RegistrationManager';

// Define the theme types
export enum ThemeType {
  LIGHT = 'light',
  DARK = 'dark',
  SYSTEM = 'system'
}

// Define the UI panel types
export enum UIPanelType {
  GOVERNANCE = 'governance',
  CONTEXT = 'context',
  SETTINGS = 'settings',
  HELP = 'help'
}

// Define the dialog types
export enum DialogType {
  NONE = 'none',
  CONFIRMATION = 'confirmation',
  ALERT = 'alert',
  FORM = 'form',
  CUSTOM = 'custom'
}

// Define the application events
export interface ApplicationEvent {
  type: EventType;
  payload: any;
  timestamp: number;
}

// Define the dialog state
export interface DialogState {
  type: DialogType;
  title: string;
  message: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  customContent?: React.ReactNode;
}

// Define the UI state
export interface UIState {
  panels: {
    [key in UIPanelType]: {
      isOpen: boolean;
      isCollapsed: boolean;
      position?: { x: number; y: number };
    };
  };
  dialog: DialogState;
  isMobile: boolean;
  isMenuOpen: boolean;
  isLoading: boolean;
  loadingMessage: string;
}

// Define the user preferences
export interface UserPreferences {
  theme: ThemeType;
  fontSize: number;
  autoSave: boolean;
  autoSaveInterval: number;
  showTips: boolean;
  defaultLLMModel: string;
}

// Define the application state
export interface ApplicationState {
  // State
  initialized: boolean;
  authenticated: boolean;
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
  } | null;
  ui: UIState;
  preferences: UserPreferences;
  recentEvents: ApplicationEvent[];
  
  // Actions
  initialize: () => void;
  login: (id: string, name: string, email: string, role: string) => void;
  logout: () => void;
  
  // UI Actions
  openPanel: (panel: UIPanelType) => void;
  closePanel: (panel: UIPanelType) => void;
  collapsePanel: (panel: UIPanelType) => void;
  expandPanel: (panel: UIPanelType) => void;
  setPanelPosition: (panel: UIPanelType, position: { x: number; y: number }) => void;
  
  showDialog: (dialog: DialogState) => void;
  hideDialog: () => void;
  
  setLoading: (isLoading: boolean, message?: string) => void;
  
  // Preferences Actions
  setTheme: (theme: ThemeType) => void;
  setFontSize: (fontSize: number) => void;
  setAutoSave: (autoSave: boolean) => void;
  setAutoSaveInterval: (interval: number) => void;
  setShowTips: (showTips: boolean) => void;
  setDefaultLLMModel: (model: string) => void;
  
  // Event Actions
  addEvent: (type: EventType, payload: any) => void;
  clearEvents: () => void;
}

// Create the store with middleware
export const useApplicationStore = create<ApplicationState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        initialized: false,
        authenticated: false,
        user: null,
        ui: {
          panels: {
            [UIPanelType.GOVERNANCE]: {
              isOpen: true,
              isCollapsed: false,
              position: { x: 0, y: 0 }
            },
            [UIPanelType.CONTEXT]: {
              isOpen: false,
              isCollapsed: false,
              position: { x: 0, y: 0 }
            },
            [UIPanelType.SETTINGS]: {
              isOpen: false,
              isCollapsed: false,
              position: { x: 0, y: 0 }
            },
            [UIPanelType.HELP]: {
              isOpen: false,
              isCollapsed: false,
              position: { x: 0, y: 0 }
            }
          },
          dialog: {
            type: DialogType.NONE,
            title: '',
            message: ''
          },
          isMobile: window.innerWidth < 768,
          isMenuOpen: false,
          isLoading: false,
          loadingMessage: ''
        },
        preferences: {
          theme: ThemeType.LIGHT,
          fontSize: 16,
          autoSave: true,
          autoSaveInterval: 30000, // 30 seconds
          showTips: true,
          defaultLLMModel: 'gpt-4o-mini'
        },
        recentEvents: [],
        
        // Actions
        initialize: () => set({ initialized: true }),
        
        login: (id, name, email, role) => set({
          authenticated: true,
          user: { id, name, email, role }
        }),
        
        logout: () => set({
          authenticated: false,
          user: null
        }),
        
        // UI Actions
        openPanel: (panel) => set((state) => ({
          ui: {
            ...state.ui,
            panels: {
              ...state.ui.panels,
              [panel]: {
                ...state.ui.panels[panel],
                isOpen: true
              }
            }
          }
        })),
        
        closePanel: (panel) => set((state) => ({
          ui: {
            ...state.ui,
            panels: {
              ...state.ui.panels,
              [panel]: {
                ...state.ui.panels[panel],
                isOpen: false
              }
            }
          }
        })),
        
        collapsePanel: (panel) => set((state) => ({
          ui: {
            ...state.ui,
            panels: {
              ...state.ui.panels,
              [panel]: {
                ...state.ui.panels[panel],
                isCollapsed: true
              }
            }
          }
        })),
        
        expandPanel: (panel) => set((state) => ({
          ui: {
            ...state.ui,
            panels: {
              ...state.ui.panels,
              [panel]: {
                ...state.ui.panels[panel],
                isCollapsed: false
              }
            }
          }
        })),
        
        setPanelPosition: (panel, position) => set((state) => ({
          ui: {
            ...state.ui,
            panels: {
              ...state.ui.panels,
              [panel]: {
                ...state.ui.panels[panel],
                position
              }
            }
          }
        })),
        
        showDialog: (dialog) => set((state) => ({
          ui: {
            ...state.ui,
            dialog
          }
        })),
        
        hideDialog: () => set((state) => ({
          ui: {
            ...state.ui,
            dialog: {
              type: DialogType.NONE,
              title: '',
              message: ''
            }
          }
        })),
        
        setLoading: (isLoading, message = '') => set((state) => ({
          ui: {
            ...state.ui,
            isLoading,
            loadingMessage: message
          }
        })),
        
        // Preferences Actions
        setTheme: (theme) => set((state) => ({
          preferences: {
            ...state.preferences,
            theme
          }
        })),
        
        setFontSize: (fontSize) => set((state) => ({
          preferences: {
            ...state.preferences,
            fontSize
          }
        })),
        
        setAutoSave: (autoSave) => set((state) => ({
          preferences: {
            ...state.preferences,
            autoSave
          }
        })),
        
        setAutoSaveInterval: (interval) => set((state) => ({
          preferences: {
            ...state.preferences,
            autoSaveInterval: interval
          }
        })),
        
        setShowTips: (showTips) => set((state) => ({
          preferences: {
            ...state.preferences,
            showTips
          }
        })),
        
        setDefaultLLMModel: (model) => set((state) => ({
          preferences: {
            ...state.preferences,
            defaultLLMModel: model
          }
        })),
        
        // Event Actions
        addEvent: (type, payload) => set((state) => {
          const newEvent = {
            type,
            payload,
            timestamp: Date.now()
          };
          
          // Keep only the last 100 events
          const recentEvents = [newEvent, ...state.recentEvents].slice(0, 100);
          
          return { recentEvents };
        }),
        
        clearEvents: () => set({ recentEvents: [] })
      }),
      {
        name: 'mindback-application-store',
        partialize: (state) => ({
          preferences: state.preferences
        })
      }
    )
  )
);

// Export selectors for common state access patterns
export const selectUser = (state: ApplicationState) => state.user;
export const selectIsAuthenticated = (state: ApplicationState) => state.authenticated;
export const selectTheme = (state: ApplicationState) => state.preferences.theme;
export const selectPanelState = (state: ApplicationState, panel: UIPanelType) => state.ui.panels[panel];
export const selectIsLoading = (state: ApplicationState) => state.ui.isLoading;
export const selectLoadingMessage = (state: ApplicationState) => state.ui.loadingMessage;
export const selectDialog = (state: ApplicationState) => state.ui.dialog;
export const selectDefaultLLMModel = (state: ApplicationState) => state.preferences.defaultLLMModel;
