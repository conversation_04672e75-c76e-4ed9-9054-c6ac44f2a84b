import React, { useEffect, useRef, useState } from 'react';
import Konva from 'konva';
import { Stage, Layer, Group, Text, Line } from 'react-konva';
import { useMindMapStore } from '../../core/state/MindMapStore';
import { Node } from '../../core/models/Node';
import { Connection } from '../../core/models/Connection';
import NodeRenderer from '../Node/NodeRenderer';
import { NodeDialog } from '../Dialogs/NodeDialog/NodeDialog';
// Temporarily comment out the ConnectionRenderer import
// import { ConnectionRenderer } from '../Connection/ConnectionRenderer';

// Cast Konva components to any to bypass TypeScript errors
const KonvaStage = Stage as any;
const KonvaLayer = Layer as any;
const KonvaGroup = Group as any;
const KonvaText = Text as any;
const KonvaLine = Line as any;

// Import the NodeDialogContainer component
import NodeDialogContainer from '../Dialogs/NodeDialog/NodeDialogContainer';

const MindMapCanvasSimple: React.FC = () => {
  console.log('DEBUG: MindMapCanvasSimple rendering');

  const store = useMindMapStore();
  const {
    nodes,
    connections,
    position,
    scale,
    selectedNodeId,
    rootNodeId
  } = store;

  // No local state needed for dialog - NodeDialogContainer handles it

  console.log('DEBUG: Current store state:', {
    selectedNodeId,
    nodeCount: Object.keys(nodes).length,
    rootNodeId
  });

  const [dimensions, setDimensions] = useState({
    width: window.innerWidth,
    height: window.innerHeight - 60 // Subtract header height
  });

  const stageRef = useRef<any>(null);
  // Track last click time for double-click detection
  const lastClickTimeRef = useRef<{ nodeId: string; time: number } | null>(null);
  const DOUBLE_CLICK_THRESHOLD = 300; // ms

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setDimensions({
        width: window.innerWidth,
        height: window.innerHeight - 60
      });
    };

    window.addEventListener('resize', handleResize);

    // Call handleResize once to set initial dimensions
    handleResize();

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Center stage and make root node visible
  useEffect(() => {
    if (rootNodeId && nodes[rootNodeId]) {
      const centerX = dimensions.width / 2;
      const centerY = dimensions.height / 2;

      // Reset position and scale to ensure visibility
      useMindMapStore.setState({
        position: { x: centerX, y: centerY },
        scale: 1.0
      });

      // Update layout to properly position all nodes
      store.updateLayout('leftToRight');

      console.log('Stage centered:', {
        dimensions,
        center: { x: centerX, y: centerY },
        currentNodes: Object.keys(nodes).length,
        connections: connections.length,
        scale,
        position
      });
    }
  }, [rootNodeId, dimensions]);

  // Auto-select root node if no node is selected
  useEffect(() => {
    if (!selectedNodeId && rootNodeId) {
      useMindMapStore.setState({ selectedNodeId: rootNodeId });
    }
  }, [selectedNodeId, rootNodeId]);

  // No need to watch for selectedNodeId changes - NodeDialogContainer handles it

  // Handle node click with double-click detection
  const handleNodeClick = (nodeId: string) => {
    console.log('DEBUG: handleNodeClick called for node:', nodeId);

    // Single click - just select the node
    console.log('DEBUG: Single click on node:', nodeId);
    console.log('DEBUG: Calling store.selectNode with isEditing=false');
    store.selectNode(nodeId, false);
  };

  // Handle dialog close - now handled by NodeDialogContainer

  // Handle zoom and center functions
  const handleZoomIn = () => {
    const newScale = Math.min(scale * 1.2, 2);
    useMindMapStore.setState({ scale: newScale });
  };

  const handleZoomOut = () => {
    const newScale = Math.max(scale / 1.2, 0.1);
    useMindMapStore.setState({ scale: newScale });
  };

  const centerView = () => {
    const centerX = dimensions.width / 2;
    const centerY = dimensions.height / 2;
    useMindMapStore.setState({
      position: { x: centerX, y: centerY },
      scale: 1.0
    });
  };

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (e.key) {
        case 'Tab':
          e.preventDefault();
          if (selectedNodeId) {
            // Use the store's addChildNode function directly
            store.addChildNode(selectedNodeId, e.shiftKey);
          }
          break;
        case 'Delete':
          if (selectedNodeId) {
            store.deleteNode(selectedNodeId);
          }
          break;
        case '+':
          handleZoomIn();
          break;
        case '-':
          handleZoomOut();
          break;
        case 'c':
          if (e.ctrlKey) {
            centerView();
          }
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedNodeId, store]);

  // Render loading message if no nodes
  if (!nodes || Object.keys(nodes).length === 0) {
    return (
      <div className="loading-canvas">
        <div className="initializing-mindmap">
          <div className="loading-spinner"></div>
          <p>Initializing canvas...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <KonvaStage
        ref={stageRef}
        width={dimensions.width}
        height={dimensions.height}
        draggable={true}
        x={position.x}
        y={position.y}
        scaleX={scale}
        scaleY={scale}
        className="mindmap-canvas"
        onDragMove={(e) => {
          const newPos = e.target.position();
          useMindMapStore.setState({ position: newPos });
        }}
        onDragEnd={(e) => {
          const finalPos = e.target.position();
          useMindMapStore.setState({ position: finalPos });
        }}
      >
        <KonvaLayer>
          {/* Debug info */}
          <KonvaText
            x={10}
            y={10}
            text={`Nodes: ${Object.keys(nodes).length} | Scale: ${scale.toFixed(2)} | Position: (${position.x.toFixed(0)}, ${position.y.toFixed(0)})`}
            fontSize={12}
            fill="black"
          />

          {/* Connections */}
          <KonvaGroup>
            {connections.map(connection => {
              const sourceNode = nodes[connection.from];
              const targetNode = nodes[connection.to];

              // Check if the connection has a valid source and target node before rendering
              if (!sourceNode || !targetNode) {
                console.warn('Missing node for connection:', {
                  connection,
                  sourceExists: !!sourceNode,
                  targetExists: !!targetNode
                });
                return null;
              }

              // Skip rendering if any node has invalid position
              if (isNaN(sourceNode.x) || isNaN(sourceNode.y) || isNaN(targetNode.x) || isNaN(targetNode.y)) {
                console.warn(`Skipping connection render due to invalid node positions`);
                return null;
              }

              // Create a very simple line between nodes
              const points = [sourceNode.x, sourceNode.y, targetNode.x, targetNode.y];

              return (
                <KonvaLine
                  key={connection.id}
                  points={points}
                  stroke={connection.color || '#3b82f6'}
                  strokeWidth={connection.thickness || 2}
                />
              );
            })}
          </KonvaGroup>

          {/* Nodes */}
          <KonvaGroup>
            {Object.values(nodes).map(node => {
              // Skip rendering nodes with invalid positions
              if (isNaN(node.x) || isNaN(node.y)) {
                console.error('Node has invalid position:', node);
                return null;
              }

              // For debugging
              console.log('Rendering node:', {
                id: node.id,
                text: node.text,
                position: { x: node.x, y: node.y },
                dimensions: { width: node.width, height: node.height },
                stabilized: !!node.metadata?.positionsStabilized
              });

              return (
                <NodeRenderer
                  key={node.id}
                  node={node}
                  selected={node.id === selectedNodeId}
                  onDragMove={(e) => {
                    // Only update position if dragging
                    const pos = e.target.position();
                    store.updateNodePosition(node.id, pos.x, pos.y);

                    // Mark node as manually positioned
                    if (!node.metadata?.positionsStabilized) {
                      store.updateNode(node.id, {
                        metadata: {
                          ...node.metadata,
                          positionsStabilized: true
                        }
                      });
                    }
                  }}
                  onDragEnd={(e) => {
                    // Update final position
                    const pos = e.target.position();
                    store.updateNodePosition(node.id, pos.x, pos.y);

                    // We're still marking it as positionsStabilized, but not manuallyPositioned,
                    // to allow layout algorithms to move it when needed
                    store.updateNode(node.id, {
                      metadata: {
                        ...node.metadata,
                        positionsStabilized: true
                      }
                    });
                  }}
                  onClick={() => {
                    console.log('DEBUG: onClick handler called for node:', node.id);
                    handleNodeClick(node.id);
                  }}
                  onDblClick={() => {
                    console.log('DEBUG: Native onDblClick event on node:', node.id);

                    // Check if the node exists in the store
                    const nodeExists = store.nodes[node.id] ? true : false;
                    console.log('DEBUG: Node exists in store:', nodeExists);

                    if (!nodeExists) {
                      console.error('DEBUG: Cannot double-click on non-existent node:', node.id);
                      return;
                    }

                    // Set selectedNodeId in the store
                    // The NodeDialogContainer will handle showing the dialog
                    console.log('DEBUG: Setting selectedNodeId in store');
                    useMindMapStore.setState({
                      selectedNodeId: node.id
                    });
                  }}
                />
              );
            })}
          </KonvaGroup>
        </KonvaLayer>
      </KonvaStage>

      {/* Use the NodeDialogContainer component */}
      <NodeDialogContainer />
    </>
  );
};

export default MindMapCanvasSimple;