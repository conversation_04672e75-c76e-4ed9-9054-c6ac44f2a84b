import { useState, useCallback, useEffect } from 'react';
import { useMindMapStore } from '../../../core/state/MindMapStore';
import { CanvasState } from '../types/canvas.types';
import { Node } from '../../../core/models/Node';

export const useCanvasState = () => {
  console.log('DEBUG: useCanvasState hook called');
  
  const {
    nodes: nodesObj,
    connections,
    selectedNodeId: selectedId,
    rootNodeId,
    addChildNode,
    deleteNode,
    selectNode,
    updateNode,
    setDirection,
    autoLayout,
    clearSelection
  } = useMindMapStore(state => {
    console.log('DEBUG: MindMapStore state in useCanvasState:', {
      selectedNodeId: state.selectedNodeId,
      nodesCount: Object.keys(state.nodes).length
    });
    return state;
  });

  // Convert nodes object to array for rendering
  const nodesArray = Object.values(nodesObj);
  
  // Log nodes for debugging
  useEffect(() => {
    console.log('DEBUG: Nodes in useCanvasState:', {
      objectCount: Object.keys(nodesObj).length,
      arrayCount: nodesArray.length
    });
  }, [nodesObj, nodesArray]);

  const [canvasState, setCanvasState] = useState<CanvasState>({
    zoom: 1,
    position: { x: 0, y: 0 },
    isDragging: false,
    dragStart: null,
    dragOffset: { x: 0, y: 0 }
  });

  const [draggedNodeId, setDraggedNodeId] = useState<string | null>(null);

  const updateZoom = useCallback((newZoom: number) => {
    setCanvasState(prev => ({ ...prev, zoom: newZoom }));
  }, []);

  const updatePosition = useCallback((x: number, y: number) => {
    setCanvasState(prev => ({ ...prev, position: { x, y } }));
  }, []);

  const startDragging = useCallback((x: number, y: number, nodeId?: string) => {
    setCanvasState(prev => ({
      ...prev,
      isDragging: true,
      dragStart: { x, y }
    }));
    if (nodeId) {
      setDraggedNodeId(nodeId);
    }
  }, []);

  const stopDragging = useCallback(() => {
    setCanvasState(prev => ({
      ...prev,
      isDragging: false,
      dragStart: null
    }));
    setDraggedNodeId(null);
  }, []);

  const updateDragOffset = useCallback((x: number, y: number) => {
    setCanvasState(prev => ({
      ...prev,
      dragOffset: { x, y }
    }));
  }, []);

  const resetView = useCallback(() => {
    setCanvasState({
      zoom: 1,
      position: { x: 0, y: 0 },
      isDragging: false,
      dragStart: null,
      dragOffset: { x: 0, y: 0 }
    });
    setDraggedNodeId(null);
  }, []);

  return {
    ...canvasState,
    nodes: nodesArray,
    nodesObj, // Return the original nodes object as well
    connections,
    selectedId,
    draggedNodeId,
    rootNodeId,
    addChildNode,
    deleteNode,
    selectNode,
    updateNode,
    setDirection,
    autoLayout,
    clearSelection,
    updateZoom,
    updatePosition,
    startDragging,
    stopDragging,
    updateDragOffset,
    resetView
  };
}; 