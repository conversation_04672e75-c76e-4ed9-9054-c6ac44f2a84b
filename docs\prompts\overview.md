# Prompt System Overview

## Prompt Types

- Governance Prompts
- Mindmap Generation Prompts


## MBCP Overview

## Overview
The **MindBack Content Protocol (MBCP)** defines the standard data structure for nodes within the MindBack system. It ensures consistency across LLM outputs, agent workflows, RAG memory, and integration with external systems like HubSpot or Notion.

MBCP is designed to be:
- **Structured** for memory and action extraction
- **Semantic** to capture user intention and agent contribution
- **Composable** for rendering as mindmaps, chatforks, or tables
- **Translatable** to other protocols like MCP (Modular Content Protocol)

---

## Prompt Engineering Guidelines

# Prompt Engineering
- Do NOT hardcode LLM prompts in source code.
- Store all prompts as YAML files in:
  C:\Users\<USER>\Documents\VSCode\MindBack_Backup\MindBack_V1\frontend\src\components\MindMap\components\Agents\Prompt_library
- YAML is for human-defined prompt content only. All LLM communication uses JSON.
- LLM calls must ONLY be made from the backend (never frontend).
- Follow the structure defined in `MBCP Specification.md`.
- Do not change prompts unless clearly instructed by the user.

## MBCP Overview

## Overview
The **MindBack Content Protocol (MBCP)** defines the standard data structure for nodes within the MindBack system. It ensures consistency across LLM outputs, agent workflows, RAG memory, and integration with external systems like HubSpot or Notion.

MBCP is designed to be:
- **Structured** for memory and action extraction
- **Semantic** to capture user intention and agent contribution
- **Composable** for rendering as mindmaps, chatforks, or tables
- **Translatable** to other protocols like MCP (Modular Content Protocol)

---