// MindMap/Node.tsx
import React from 'react';
import { Node as NodeType } from './types';
import { useMindMap } from './context/MindMapContext';
import { useNodeManagement } from './hooks/useNodeManagement';

// Simple function to calculate node path based on connections
const getSimpleNodePath = (nodeId: string, connections: Record<string, any>): string => {
  // If there are no connections, this must be the root node
  if (!connections || Object.keys(connections).length === 0) {
    return "1.0";
  }
  
  // Check if this node is a target in any connection
  const isChildNode = Object.values(connections).some(conn => conn.to === nodeId);
  if (!isChildNode) {
    // This is a root node
    return "1.0";
  }
  
  // Simple numbering - just append the last few characters of the ID for uniqueness
  return `1.${nodeId.slice(-2)}`;
};

interface NodeProps {
  node: NodeType;
  isSelected: boolean;
}

export const Node: React.FC<NodeProps> = ({ node, isSelected }) => {
  const { scale } = useMindMap();
  const { selectNode, editNode } = useNodeManagement();
  
  // Use the node's metadata.nodePath property directly instead of calculating it
  const nodePath = node.metadata?.nodePath || "1.0";

  return (
    <div
      className={`node ${isSelected ? 'selected' : ''}`}
      style={{
        position: 'absolute',
        left: `${node.x}px`,
        top: `${node.y}px`,
        width: `${node.width}px`,
        height: `${node.height}px`,
        backgroundColor: node.color,
        borderRadius: '5px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        boxShadow: isSelected ? '0 0 0 2px #000' : '0 2px 5px rgba(0,0,0,0.2)',
        cursor: 'pointer',
        userSelect: 'none',
        overflow: 'hidden',
        padding: '5px',
        textAlign: 'center',
        fontSize: '14px',
        color: '#fff',
        fontWeight: 'bold',
        zIndex: 2
      }}
      onClick={() => selectNode(node.id)}
      onDoubleClick={editNode}
    >
      {/* Add node path display */}
      <div 
        className="node-path-number"
        style={{
          position: 'absolute',
          top: '4px',
          left: '4px',
          fontSize: '12px',
          fontFamily: 'inherit',
          fontWeight: 'bold',
          color: 'inherit',
          padding: '1px 4px',
          zIndex: 10,
          opacity: 0.85
        }}
      >
        {nodePath}
      </div>
      {node.text}
    </div>
  );
};