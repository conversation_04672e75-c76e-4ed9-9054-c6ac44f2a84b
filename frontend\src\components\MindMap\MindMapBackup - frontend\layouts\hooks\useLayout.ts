import { useCallback } from 'react';
import { DEFAULT_LAYOUT_CONFIG, LayoutConfig, LayoutStrategyType } from '../types';
import { layoutAdapter } from '..';

/**
 * Hook for using the layout system in React components
 */
export function useLayout() {
  /**
   * Apply a layout strategy to the current MindMap
   */
  const applyLayout = useCallback((
    strategyType: LayoutStrategyType = 'leftToRight',
    config: LayoutConfig = DEFAULT_LAYOUT_CONFIG
  ) => {
    layoutAdapter.applyLayoutToStore(strategyType, config);
  }, []);
  
  /**
   * Get available layout strategies
   */
  const getAvailableStrategies = useCallback((): LayoutStrategyType[] => {
    return layoutAdapter.getAvailableStrategies();
  }, []);
  
  return {
    applyLayout,
    getAvailableStrategies
  };
} 