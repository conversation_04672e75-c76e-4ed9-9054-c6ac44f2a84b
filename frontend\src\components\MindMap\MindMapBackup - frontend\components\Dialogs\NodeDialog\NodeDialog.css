@import '../../../../../styles/theme.css';

.node-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* Node Dialog Paper */
.node-dialog-paper {
  position: absolute !important;
  transform-origin: center !important;
  z-index: 2000 !important;
  will-change: transform !important;
  transition: none !important;
  pointer-events: auto !important;
  margin: 0 !important;
  max-width: none !important;
  max-height: none !important;
}

/* Main dialog container */
.node-dialog {
  position: fixed;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 300px;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

/* Dialog header */
.node-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #eee;
  cursor: move;
}

/* Add visual indicator for draggable header */
.node-dialog-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: #3b82f6;
  cursor: move;
}

.node-dialog-header-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.node-dialog-header-logo {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.node-dialog-header-text {
  font-weight: 500;
  font-size: 14px;
}

.node-dialog-header-buttons {
  display: flex;
  gap: 4px;
  cursor: default;
}

.node-dialog-close-button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  color: #666;
}

.node-dialog-close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Debug indicator */
.node-debug-indicator {
  position: absolute;
  top: 5px;
  right: 50px;
  background-color: #10b981;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  z-index: 2050;
}

/* Content area */
.node-dialog-content {
  padding: 15px;
}

/* Node info section */
.node-info-section {
  margin-bottom: 20px;
}

.input-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.node-title-input,
.node-description-textarea,
.node-metadata-select,
.node-action-input,
.node-action-select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 12px;
}

.node-description-textarea {
  min-height: 80px;
  resize: vertical;
}

.node-tags-container {
  margin-bottom: 16px;
}

.node-tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.node-tag {
  display: inline-flex;
  align-items: center;
  background: #e3f2fd;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.node-tag-remove {
  background: none;
  border: none;
  margin-left: 4px;
  cursor: pointer;
  color: #666;
  font-size: 14px;
  padding: 0 4px;
}

.node-tag-input {
  display: flex;
  gap: 8px;
}

.node-tag-input-field {
  flex: 1;
}

.node-tag-add {
  background: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
}

.node-action-section {
  display: grid;
  gap: 8px;
}

.node-action-input,
.node-action-select {
  width: 100%;
}

/* Resize handle - Enhanced visibility */
.resize-handle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 25px;
  height: 25px;
  cursor: nwse-resize !important;
  background: linear-gradient(135deg, transparent 40%, rgba(59, 130, 246, 0.8) 60%);
  z-index: 100;
  border-top-left-radius: 4px;
  transition: background 0.2s;
}

.resize-handle:hover {
  background: linear-gradient(135deg, transparent 30%, rgba(59, 130, 246, 1) 70%);
}

.resize-handle::after {
  content: "";
  position: absolute;
  bottom: 3px;
  right: 3px;
  width: 8px;
  height: 8px;
  border-right: 2px solid white;
  border-bottom: 2px solid white;
}

/* Support custom dialog */
.custom-node-dialog {
  pointer-events: none !important;
}

.custom-node-dialog .MuiBackdrop-root {
  background-color: transparent !important;
}

/* Override MUI Dialog root styles */
.MuiDialog-root.custom-node-dialog {
  position: absolute !important;
  pointer-events: auto !important;
  z-index: 2000 !important;
}

/* Override MUI Dialog container styles */
.custom-node-dialog .MuiDialog-container {
  position: fixed !important;
  transform: none !important;
  pointer-events: none !important;
}

/* Override MUI Dialog paper styles */
.custom-node-dialog .MuiPaper-root {
  overflow: visible !important; /* Allow resize handle to be visible */
}

/* Ensure the dialog is draggable */
.react-draggable {
  position: absolute !important;
  z-index: 2000 !important;
  will-change: transform !important;
  transition: none !important;
  pointer-events: auto !important;
  transform-origin: center !important;
}

/* Add transparent selection class for dragging/resizing */
.react-draggable-transparent-selection * {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

.react-draggable-transparent-selection .node-dialog-header-buttons {
  cursor: default !important;
}

.react-draggable-transparent-selection .node-dialog-close-button {
  cursor: pointer !important;
}

/* Node dialog title */
.node-dialog-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.title-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.node-dialog-title h2 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #334155;
}

.path-separator {
  height: 1px;
  width: 100%;
  background-color: #e2e8f0;
  margin: 6px 0;
}

.node-path {
  font-family: monospace;
  font-size: 1.1rem;
  font-weight: 600;
  color: #334155; /* Same color as the title */
  margin-top: 2px;
}

.node-id {
  font-size: 0.75rem;
  color: #64748b;
  font-family: monospace;
  text-align: right;
}

.hat-selection-tabs {
  display: flex;
  padding: 10px 20px;
  background-color: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  flex-wrap: wrap;
}

.hat-button {
  padding: 8px 16px;
  margin: 5px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: #fff;
  font-weight: normal;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  transition: all 0.2s ease;
}

.hat-button.selected {
  font-weight: bold;
  box-shadow: 0 0 10px rgba(0,0,0,0.5);
  transform: scale(1.05);
}

.chat-section {
  margin-top: 20px;
  border-top: 1px solid #e5e7eb;
  padding-top: 20px;
}

.section-title {
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
  font-weight: bold;
}

.chat-form {
  margin-bottom: 20px;
}

.chat-input {
  width: 100%;
  padding: 12px;
  border: 2px solid #3b82f6;
  border-radius: 4px;
  font-size: 16px;
  min-height: 100px;
  resize: vertical;
  margin-bottom: 15px;
  box-sizing: border-box;
  outline: none;
}

.chat-input.disabled {
  opacity: 0.7;
}

.send-button {
  padding: 12px 24px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  transition: all 0.2s ease;
}

.send-button.disabled {
  background-color: #94a3b8;
  cursor: not-allowed;
}

.suggestions-container {
  background-color: #f0f9ff;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 1px solid #bfdbfe;
}

.suggestions-title {
  margin-bottom: 15px;
  font-size: 18px;
  color: #1e40af;
  font-weight: bold;
}

.suggestions-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.suggestion-item {
  margin-bottom: 15px;
  padding: 15px;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  border: 1px solid #e5e7eb;
}

.suggestion-text {
  display: block;
  margin-bottom: 8px;
  font-size: 16px;
  color: #111827;
}

.suggestion-description {
  margin: 0;
  color: #4b5563;
  font-size: 14px;
  line-height: 1.5;
}

.accept-suggestions-button {
  padding: 12px 24px;
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  margin-top: 15px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  transition: all 0.2s ease;
}

.debug-info-container {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f8f8;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
  white-space: pre-wrap;
  overflow-x: auto;
}

.debug-info-title {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: bold;
}

.debug-info-content {
  margin: 0;
  padding: 0;
  font-size: 12px;
  font-family: monospace;
}

.node-dialog-footer {
  padding: 15px 20px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  background-color: #f8fafc;
  gap: 10px;
}

.cancel-button {
  padding: 12px 24px;
  background-color: #f1f5f9;
  color: #64748b;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
}

.save-button {
  padding: 12px 24px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  transition: all 0.2s ease;
}

/* G-LLM Chat Section (Fixed Height with Internal Scrolling) */
.gllm-chat-section {
  flex-shrink: 0;
  border-top: 1px solid #e2e8f0;
  padding: 16px 20px;
  background-color: #f8fafc;
  height: 40vh;
  max-height: 400px;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.gllm-chat-section h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  flex-shrink: 0;
}

/* Chat Messages Area (Scrollable) - Ensure scrollbar appears */
.gllm-chat-messages {
  flex: 1;
  overflow-y: scroll !important; /* Force scrollbar to always show */
  background-color: white;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 12px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-height: 180px; /* Ensure minimum height for scrollability */
}

.gllm-message {
  padding: 10px 12px;
  border-radius: 8px;
  line-height: 1.5;
  max-width: 85%;
  word-wrap: break-word;
}

.gllm-message.assistant {
  align-self: flex-start;
  background-color: #f1f5f9;
  border-bottom-left-radius: 2px;
}

.gllm-message.user {
  align-self: flex-end;
  background-color: #e0f2fe;
  border-bottom-right-radius: 2px;
}

/* Styling for structure within chat messages */
.gllm-message strong {
  font-weight: 600;
}

.gllm-message ul {
  padding-left: 20px;
  margin: 8px 0;
}

.gllm-message li {
  margin-bottom: 6px;
  line-height: 1.4;
}

.gllm-message ul ul {
  padding-left: 16px;
  margin: 4px 0;
}

.gllm-chat-input {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.gllm-input-field {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
  transition: border-color 0.3s;
}

.gllm-input-field:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.gllm-send-button {
  padding: 8px 16px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.gllm-send-button:hover {
  background-color: #2563eb;
}

.gllm-send-button:disabled {
  background-color: #cbd5e1;
  cursor: not-allowed;
}

/* Action buttons within chat messages */
.action-buttons {
  margin-top: 15px;
  display: flex;
  justify-content: center;
}

.accept-structure-button {
  padding: 10px 16px;
  background-color: #4ade80; /* Green color for positive action */
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s, transform 0.2s;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  animation: pulse 2s infinite;
}

.accept-structure-button:hover {
  background-color: #22c55e;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(74, 222, 128, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(74, 222, 128, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(74, 222, 128, 0);
  }
}

/* Node Creation Toggle */
.node-creation-toggle {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 22px;
  margin-right: 10px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .3s;
  border-radius: 22px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 2px;
  background-color: white;
  transition: .3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #2ecc71;
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

.toggle-label {
  font-size: 14px;
  color: #4b5563;
}

/* Tags */
.node-tags-container {
  margin-bottom: 15px;
}

.node-tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 10px;
}

.node-tag {
  display: inline-flex;
  align-items: center;
  background-color: #e5e7eb;
  border-radius: 16px;
  padding: 3px 8px;
  font-size: 12px;
  color: #4b5563;
}

.node-tag-remove {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  color: #6b7280;
  margin-left: 5px;
  padding: 0 2px;
}

.node-tag-input {
  display: flex;
  gap: 5px;
}

.node-tag-input-field {
  flex: 1;
  padding: 6px 10px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 13px;
}

.node-tag-add {
  background-color: #e5e7eb;
  border: none;
  border-radius: 4px;
  width: 28px;
  cursor: pointer;
  font-size: 16px;
  color: #4b5563;
}

.node-tag-add:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Dialog actions */
.node-dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.node-dialog-button {
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-button {
  background-color: #e5e7eb;
  color: #4b5563;
}

.save-button {
  background-color: #3b82f6;
  color: white;
}

.cancel-button:hover {
  background-color: #d1d5db;
}

.save-button:hover {
  background-color: #2563eb;
} 