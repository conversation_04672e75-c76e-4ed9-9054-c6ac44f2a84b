// Placeholder file for: frontend\src\components\MindMap\components\Dialogs\NodeDialog\NodeLLMTab.tsx

import React, { useState, useEffect } from 'react';
import { NodeType } from '../../../core/models/Node';
import { useMindMap } from '../../../contexts/MindMapContext';
import { useLLM } from '../../../contexts/LLMContext';

interface NodeLLMTabProps {
  node: NodeType;
}

const NodeLLMTab: React.FC<NodeLLMTabProps> = ({ node }) => {
  const { updateNode } = useMindMap();
  const { 
    llmState,
    setHatType,
    generateSuggestions,
    applyHatToNode 
  } = useLLM();
  
  const [selectedHat, setSelectedHat] = useState<string | null>(node.hat || null);
  
  // Update local state when node changes
  useEffect(() => {
    setSelectedHat(node.hat || null);
  }, [node]);
  
  // Update LLM state when tab is opened
  useEffect(() => {
    if (selectedHat) {
      setHatType(selectedHat);
    }
  }, [selectedHat, setHatType]);
  
  // Available hat options
  const hatOptions = [
    { id: 'white', name: 'White Hat', description: 'Facts & Information' },
    { id: 'red', name: 'Red Hat', description: 'Emotions & Feelings' },
    { id: 'black', name: 'Black Hat', description: 'Critical Thinking' },
    { id: 'yellow', name: 'Yellow Hat', description: 'Positive Thinking' },
    { id: 'green', name: 'Green Hat', description: 'Creative Thinking' },
    { id: 'blue', name: 'Blue Hat', description: 'Process Control' }
  ];
  
  // Handle hat selection
  const handleHatChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newHat = e.target.value === '' ? null : e.target.value;
    setSelectedHat(newHat);
    
    // Update the LLM hat type
    setHatType(newHat);
    
    // Update the node in the store
    updateNode({
      ...node,
      hat: newHat || undefined
    });
  };
  
  // Handle generate suggestions
  const handleGenerateSuggestions = async () => {
    if (selectedHat) {
      await generateSuggestions(node.id);
    }
  };
  
  // Handle apply hat to node
  const handleApplyHatToNode = async () => {
    if (selectedHat) {
      await applyHatToNode(node.id, selectedHat);
    }
  };
  
  return (
    <div className="node-llm-tab">
      <div className="form-group">
        <label htmlFor="node-hat">Thinking Hat</label>
        <select
          id="node-hat"
          value={selectedHat || ''}
          onChange={handleHatChange}
        >
          <option value="">No Hat</option>
          {hatOptions.map(hat => (
            <option key={hat.id} value={hat.id}>
              {hat.name} - {hat.description}
            </option>
          ))}
        </select>
      </div>
      
      <div className="action-buttons">
        <button 
          onClick={handleGenerateSuggestions}
          disabled={!selectedHat || llmState.isLoading}
        >
          {llmState.isLoading ? 'Generating...' : 'Generate Ideas'}
        </button>
        
        <button
          onClick={handleApplyHatToNode}
          disabled={!selectedHat || llmState.isLoading}
        >
          Apply Thinking Method
        </button>
      </div>
      
      {llmState.isLoading && (
        <div className="loading-indicator">
          AI is thinking...
        </div>
      )}
      
      {llmState.error && (
        <div className="error-message">
          {llmState.error}
        </div>
      )}
      
      {node.suggestions && node.suggestions.length > 0 && (
        <div className="node-suggestions">
          <h4>Previous AI Suggestions</h4>
          <ul>
            {node.suggestions.map((suggestion, index) => (
              <li key={index}>{suggestion}</li>
            ))}
          </ul>
        </div>
      )}
      
      {llmState.suggestions.length > 0 && (
        <div className="suggestions">
          <h4>New AI Suggestions</h4>
          <ul>
            {llmState.suggestions.map((suggestion, index) => (
              <li key={index}>{suggestion}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default NodeLLMTab;
