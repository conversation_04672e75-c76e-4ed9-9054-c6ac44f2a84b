import React from 'react';

interface ContextToggleButtonProps {
  isOpen: boolean;
  onClick: () => void;
}

const ContextToggleButton: React.FC<ContextToggleButtonProps> = ({ isOpen, onClick }) => {
  // Only render the button when the panel is closed
  if (isOpen) return null;

  return (
    <button
      className="context-toggle-button"
      onClick={onClick}
      title="Context Settings"
      style={{
        position: 'absolute',
        top: '45px', // Position it just under the MB logo
        left: '20px',
        width: '28px',
        height: '28px',
        borderRadius: '4px',
        backgroundColor: '#ffffff',
        border: 'none', // Remove the border
        color: '#000000',
        fontSize: '16px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: 'pointer',
        zIndex: 1001, // Ensure it's above other elements
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)' // Add subtle shadow for depth without border
      }}
    >
      {'>'}
    </button>
  );
};

export default ContextToggleButton;
