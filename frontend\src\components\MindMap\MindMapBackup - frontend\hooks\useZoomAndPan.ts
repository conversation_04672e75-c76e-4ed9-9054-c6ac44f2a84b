/**
 * useZoomAndPan Hook
 * Provides zoom and pan functionality for the mind map canvas
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { useMindMapStore } from '../core/state/MindMapStore';

interface UseZoomAndPanProps {
  minZoom?: number;
  maxZoom?: number;
  initialZoom?: number;
}

export const useZoomAndPan = ({
  minZoom = 0.3,
  maxZoom = 2,
  initialZoom = 1,
}: UseZoomAndPanProps = {}) => {
  const [zoom, setZoom] = useState(initialZoom);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const isDragging = useRef(false);
  const lastPosition = useRef({ x: 0, y: 0 });
  const canvasRef = useRef<HTMLDivElement | null>(null);
  const { nodes } = useMindMapStore();
  
  // Store the canvas dimensions and dialog height
  const canvasDimensions = useRef({ width: 0, height: 0 });
  const dialogHeight = useRef(0); // Height of the dialog box at the bottom
  const headerHeight = useRef(0); // Height of the header

  // Update dimensions on resize
  useEffect(() => {
    const updateDimensions = () => {
      if (!canvasRef.current) return;
      
      // Get canvas dimensions
      const rect = canvasRef.current.getBoundingClientRect();
      canvasDimensions.current = {
        width: rect.width,
        height: rect.height
      };
      
      // Get dialog height
      const governanceSection = document.querySelector('.governance-agent-section');
      if (governanceSection) {
        const govRect = governanceSection.getBoundingClientRect();
        dialogHeight.current = govRect.height;
      }
      
      // Get header height
      const header = document.querySelector('.mindback-header, .project-bar');
      if (header) {
        const headerRect = header.getBoundingClientRect();
        headerHeight.current = headerRect.height;
      }
    };
    
    // Initial update
    updateDimensions();
    
    // Update on resize
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  // Calculate the effective canvas area (excluding dialog and header)
  const getEffectiveCanvasArea = useCallback(() => {
      return {
      width: canvasDimensions.current.width,
      height: canvasDimensions.current.height - dialogHeight.current,
      top: headerHeight.current,
      left: 0,
      centerX: canvasDimensions.current.width / 2,
      centerY: (canvasDimensions.current.height - dialogHeight.current) / 2 + headerHeight.current
    };
  }, []);

  const handleZoomIn = useCallback(() => {
    setZoom(prev => Math.min(prev * 1.2, maxZoom));
  }, [maxZoom]);

  const handleZoomOut = useCallback(() => {
    setZoom(prev => Math.max(prev / 1.2, minZoom));
  }, [minZoom]);

  // Center the view on all nodes or a specific node
  const centerView = useCallback(() => {
    const effectiveCanvas = getEffectiveCanvasArea();
    const nodesList = Object.values(nodes);
    
    if (nodesList.length === 0) {
      // No nodes, center at the middle of the effective canvas
      setPosition({
        x: effectiveCanvas.centerX,
        y: effectiveCanvas.centerY
      });
      return;
    }
    
    // Calculate the bounding box of all nodes
    let minX = Infinity;
    let minY = Infinity;
    let maxX = -Infinity;
    let maxY = -Infinity;
    
    nodesList.forEach(node => {
      minX = Math.min(minX, node.x);
      minY = Math.min(minY, node.y);
      maxX = Math.max(maxX, node.x);
      maxY = Math.max(maxY, node.y);
    });
    
    // Calculate center of nodes
    const centerX = (minX + maxX) / 2;
    const centerY = (minY + maxY) / 2;
    
    // Center the view on nodes, adjusting for the effective canvas area
    setPosition({
      x: effectiveCanvas.centerX - (centerX * zoom),
      y: effectiveCanvas.centerY - (centerY * zoom)
    });
    
    console.log('Centered view on nodes', {
      effectiveCanvas,
      nodesBounds: { minX, minY, maxX, maxY },
      centerX,
      centerY,
      newPosition: {
        x: effectiveCanvas.centerX - (centerX * zoom),
        y: effectiveCanvas.centerY - (centerY * zoom)
      }
    });
  }, [nodes, zoom, getEffectiveCanvasArea]);

  // Initialize center position when nodes change
  useEffect(() => {
    if (Object.keys(nodes).length > 0 && position.x === 0 && position.y === 0) {
      centerView();
    }
  }, [nodes, position, centerView]);

  const startDrag = useCallback((clientX: number, clientY: number) => {
    isDragging.current = true;
    lastPosition.current = { x: clientX, y: clientY };
  }, []);

  const drag = useCallback((clientX: number, clientY: number) => {
    if (!isDragging.current) return;
    
    const dx = clientX - lastPosition.current.x;
    const dy = clientY - lastPosition.current.y;
    
    setPosition(prev => ({
      x: prev.x + dx,
      y: prev.y + dy,
    }));
    
    lastPosition.current = { x: clientX, y: clientY };
  }, []);
  
  const endDrag = useCallback(() => {
    isDragging.current = false;
  }, []);

  return {
    zoom,
    position,
    setPosition,
    handleZoomIn,
    handleZoomOut,
    centerView,
    startDrag,
    drag,
    endDrag,
    canvasRef,
  };
};
