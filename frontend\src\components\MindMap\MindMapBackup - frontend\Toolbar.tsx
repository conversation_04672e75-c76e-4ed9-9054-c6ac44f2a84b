import React from 'react';
import { useMindMap } from './context/MindMapContext';
import { useProjectManagement } from './hooks/useProjectManagement';

export const Toolbar: React.FC = () => {
  const {
    lastSaved
  } = useMindMap();

  const formatLastSaved = () => {
    if (!lastSaved) return 'Not saved yet';
    
    const date = new Date(lastSaved);
    return `Last saved: ${date.toLocaleTimeString()}`;
  };

  return (
    <div className="toolbar">
      <div className="last-saved">
        {formatLastSaved()}
      </div>
    </div>
  );
}; 