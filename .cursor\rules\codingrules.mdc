---
description: 
globs: 
alwaysApply: false
---
```rules
# MindBack Development Rules

## Core Principles
1. IF IT AIN'T BROKE, DON'T FIX IT
2. Minimal changes only
3. No overengineering
4. No mockdata, no workaround
5. Fail fast and hard

## Mandatory Rules for AI Assistant

### Before Making ANY Changes
- MUST verify the specific issue or requirement
- MUST check if the feature/code already works
- MUST propose the smallest possible change
- MUST NOT suggest rewrites of working code
- MUST NOT "improve" or "clean up" working code
- MUST NOT change architecture or file structure

### Code Changes
- Add only what's necessary
- No premature optimization
- No "nice to have" features
- Keep changes localized
- Preserve existing patterns
- if codde is already available for a specific topic, before adding new code, fix existing code
- remove the content of a depreciated code snippet and mark as depreciated, keep the code name so that it can be found
- No dependency changes without explicit approval

### Testing Requirements
- Every change MUST be immediately testable
- Changes MUST NOT break existing functionality
- All errors MUST be handled explicitly
- No silent failures allowed

### Forbidden Actions
- No component rewrites
- No store architecture changes
- No "refactoring" of working code
- No moving of files
- No deletion of working components
- No architectural "improvements"
- No dependency updates without approval

### Communication Requirements
- MUST explain why each change is necessary
- MUST provide specific test steps
- MUST highlight potential risks
- MUST ask for confirmation before major changes
- MUST stop and ask when uncertain

### Windows-Specific Rules
- All commands MUST be Windows-compatible
- No Linux/Mac terminal commands
- Never attempt to start/restart apps
- Use Windows path separators (\\)

### Error Handling
- Fail immediately and visibly
- No silent error suppression
- Report all errors to console
- Preserve error stack traces

### State Management
- No store architecture changes
- Preserve existing state patterns
- No new global state without approval
- Keep state changes minimal

# Dependency Management
- Always manage Python dependencies via `pip freeze > requirements.txt` and use a virtual environment.
- Remove unused packages to maintain lean requirements.
- Check for outdated packages using `pip list --outdated`, and validate installs with `pip check`.
- Use version pinning (e.g., `fastapi==0.100.1`) to avoid breaking changes.
- Automate updates using Dependabot or `pip-tools` (`pip-compile`).
- Before modifying `package.json`, review `dependency-notes.md`.

# Prompt Engineering
- Do NOT hardcode LLM prompts in source code.
- Store all prompts as YAML files in: C:\\Users\\<USER>\\Documents\\VSCode\\MindBack_Backup\\MindBack_V1\\backend\\Prompt_library
- LLM calls must ONLY be made from the backend (never frontend).
- Follow the structure defined in `MBCP Specification.md`.
- Do not change prompts unless clearly instructed by the user.

# Terminal Commands
- We are on Windows
- Do not suggest commands for Mac or Linux

# Coding Patterns and Structure
- Prioritize simple, maintainable solutions.
- Avoid hardcoded defaults and duplicated logic.
- Avoid files longer than 300 lines—modularize when expanding.
- Never mock or stub data in dev/prod—mocking is test-only.
- Never mask real error messages—report root causes directly.
- Do not overwrite `.env` files without confirmation from the user.
- Before adding new functionality, check for existing files with similar responsibilities.
- Reflect when the user reflects, and always ask if suggestions should be implemented.

# LLM Integration
- The LLM selection box is the single source of truth.
- All LLM communication and backend/frontend exchange is in JSON only.
- Ensure backend logic reflects the frontend selection consistently.

# Project Documentation
- Keep the following files up-to-date with the significant architectual, functional and design changes made (not fixes):
  - C:\\Users\\<USER>\\Documents\\VSCode\\MindBack_Backup\\MindBack_V1\\0StageOfDev.md
  - C:\\Users\\<USER>\\Documents\\VSCode\\MindBack_Backup\\MindBack_V1\\WORKFLOW.md
    - start with a timestamp
    - explain what was done and what code snippet(s) was address

# Code Style and Communication
- Do not use emojis unless explicitly asked.
- Use TypeScript interfaces (not types), avoid enums, prefer RORO pattern.
- Always communicate decisions and ask for confirmation on implementation.



```