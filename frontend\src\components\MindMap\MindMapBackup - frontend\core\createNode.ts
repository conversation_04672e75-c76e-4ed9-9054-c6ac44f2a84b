import { Node } from './models/Node';

interface NodeOptions {
  description?: string;
  parentId?: string;
  color?: string;
  metadata?: {
    nodePath?: string;
    [key: string]: any;
  };
}

export function createNode(
  text: string,
  x: number,
  y: number,
  options: NodeOptions = {}
): Node {
  const now = Date.now();
  return {
    id: Math.random().toString(36).substring(2, 11),
    text,
    x,
    y,
    width: 120,
    height: 40,
    color: options.color || '#ffffff',
    description: options.description || '',
    parentId: options.parentId,
    createdAt: now,
    updatedAt: now,
    metadata: options.metadata || {}
  };
} 