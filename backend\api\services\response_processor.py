"""
Response Processor Module
Processes and validates LLM API responses, extracting and validating MBCP structures.
"""
import json
import logging
from typing import Dict, Any, Optional

from ..models.mbcp_models import LLMChatResponse

# Import intent config functions
try:
    from ...config.intent_config import is_valid_intent, get_intent_metadata
except ImportError:
    # Fallback to absolute import if relative import fails
    from config.intent_config import is_valid_intent, get_intent_metadata

# Get the existing logger instead of creating a new one
logger = logging.getLogger(__name__)

def process_llm_response(response: Any, request_data: Dict[str, Any]) -> LLMChatResponse:
    """
    Process the raw LLM response and extract MBCP structure
    """
    try:
        logger.info("Processing LLM response")

        # Convert response to dict if it's an object with model_dump method
        if hasattr(response, 'model_dump'):
            response_dict = response.model_dump()
        else:
            response_dict = response

        logger.debug(f"Response keys: {list(response_dict.keys()) if response_dict else 'None'}")

        # Check if we have valid choices
        if not response_dict.get('choices') or not response_dict['choices']:
            logger.error("No choices in LLM response")
            return LLMChatResponse(
                success=False,
                error="No response content from LLM",
                content={},
                model=request_data.get('model', None)
            )

        # Get the first choice
        choice = response_dict['choices'][0]

        # Extract message and check for function call
        message = choice.get('message', {})

        # Try to extract the function call arguments if present
        if 'function_call' in message:
            try:
                # Get the function call arguments as a string
                args_str = message['function_call']['arguments']

                # Parse the JSON directly - no fallbacks or workarounds
                function_args = json.loads(args_str)

                logger.info("Found function call in response")
                logger.debug(f"Function args keys: {list(function_args.keys()) if function_args else 'None'}")

                # Always log the structure for debugging
                validate_mbcp_structure(function_args)

                # Always return the function args as valid content
                logger.info("Returning function call content without validation")
                return LLMChatResponse(
                    success=True,
                    content=function_args,
                    model=request_data.get('model', None),
                    validation_info={
                        "validated": True,
                        "validation_method": "function_call"
                    }
                )
            except json.JSONDecodeError as jde:
                logger.error(f"JSON decode error in function call: {str(jde)}")

                # Try to extract raw text from the function call
                args_str = message['function_call']['arguments']
                if args_str:
                    # Check if this is a mindmap response with direct JSON instead of function call
                    if 'prompt_type' in request_data and request_data['prompt_type'] == 'initiate_mindmap2':
                        logger.error("JSON decode error in mindmap function call - this is likely due to the model returning direct JSON instead of using the function call format")
                        logger.error("The prompt template may need to be updated to instruct the model to use the function call format")

                        # Try to extract JSON from the content field if available
                        if 'content' in message and message['content']:
                            try:
                                # Look for JSON in the content
                                content_json = extract_json_from_content(message['content'])
                                if content_json and 'mindmap' in content_json:
                                    logger.info("Found mindmap JSON in content field")

                                    # Always log the structure for debugging
                                    validate_mbcp_structure(content_json)

                                    # Always return the content as valid
                                    logger.info("Returning content JSON without validation")
                                    return LLMChatResponse(
                                        success=True,
                                        content=content_json,
                                        model=request_data.get('model', None),
                                        validation_info={
                                            "validated": True,
                                            "validation_method": "content_json_mindmap"
                                        }
                                    )
                            except Exception as e:
                                logger.error(f"Error extracting JSON from content: {str(e)}")

                    # If we can't parse JSON, return an error - no fallbacks
                    logger.error("Failed to parse JSON in function call")
                    return LLMChatResponse(
                        success=False,
                        error="Failed to parse JSON in LLM response",
                        content={},
                        model=request_data.get('model', None)
                    )
            except Exception as e:
                logger.error(f"Error processing function call: {str(e)}")

        # If no function call or invalid function call, try direct content
        if 'content' in message and message['content']:
            try:
                # Try to extract JSON from the content
                content = extract_json_from_content(message['content'])
                if content:
                    # Always log the structure for debugging
                    validate_mbcp_structure(content)

                    logger.info("Returning content without validation")
                    return LLMChatResponse(
                        success=True,
                        content=content,
                        model=request_data.get('model', None),
                        validation_info={
                            "validated": True,
                            "validation_method": "content_json"
                        }
                    )
                else:
                    # If we couldn't extract JSON, return the raw content
                    logger.info("No JSON found in content, returning raw content")
                    return LLMChatResponse(
                        success=True,
                        content={
                            "text": "Raw LLM Response",
                            "description": "Raw content from LLM",
                            "intent": "miscellaneous",
                            "raw_content": message['content']
                        },
                        model=request_data.get('model', None),
                        validation_info={
                            "validated": True,
                            "validation_method": "raw_content"
                        }
                    )
            except Exception as e:
                logger.error(f"Error processing content: {str(e)}")

                # Log the error but still try to return something useful
                logger.error(f"Error processing content, but continuing: {str(e)}")
                return LLMChatResponse(
                    success=True,
                    content={
                        "text": "Error Processing Response",
                        "description": f"Error occurred but continuing: {str(e)}",
                        "intent": "miscellaneous",
                        "error": str(e)
                    },
                    model=request_data.get('model', None),
                    validation_info={
                        "validated": True,
                        "validation_method": "error_bypass"
                    }
                )

        # If we get here, no valid content was found, but we'll still try to return something useful
        logger.error("No valid content found in LLM response, but continuing")

        # Return a generic response that won't cause errors
        return LLMChatResponse(
            success=True,
            content={
                "text": "Raw LLM Response",
                "description": "Could not extract structured content from LLM response",
                "intent": "miscellaneous",
                "raw_response": str(response_dict)
            },
            model=request_data.get('model', None),
            validation_info={
                "validated": True,
                "validation_method": "fallback_bypass"
            }
        )
    except Exception as e:
        logger.error(f"Error processing LLM response: {str(e)}")
        # Return a valid response even in case of exception
        return LLMChatResponse(
            success=True,
            content={
                "text": "Exception in Response Processing",
                "description": f"An exception occurred but we're continuing: {str(e)}",
                "intent": "miscellaneous",
                "error": str(e),
                "exception_type": str(type(e).__name__)
            },
            model=request_data.get('model', None),
            validation_info={
                "validated": True,
                "validation_method": "exception_bypass"
            }
        )

def validate_mbcp_structure(data: Dict[str, Any]) -> bool:
    """
    Validate that the data follows MBCP structure and add responseType if needed

    Note: All validation has been removed as requested by the user.
    This function now always returns True to allow any structure to pass through.
    """
    # Log the structure for debugging purposes
    if isinstance(data, dict):
        if 'mindmap' in data and isinstance(data['mindmap'], dict) and 'root' in data['mindmap']:
            root = data['mindmap']['root']
            if isinstance(root, dict):
                # Log information about the structure
                logger.info("MBCP structure: Found mindmap with root node")

                # Check for children nodes and log information
                if 'children' in root and isinstance(root['children'], list):
                    children_count = len(root['children'])
                    logger.info(f"Root node has {children_count} children (main branches)")

                    # Log information about sub-branches
                    for i, child in enumerate(root['children']):
                        branch_id = child.get('id', f'branch_{i+1}')
                        if 'children' in child and isinstance(child['children'], list):
                            sub_branch_count = len(child['children'])
                            logger.info(f"Main branch {branch_id} has {sub_branch_count} sub-branches")
                        else:
                            logger.info(f"Main branch {branch_id} has no sub-branches")
                else:
                    logger.info("Root node has no children (main branches)")

        # Log basic MBCP fields
        basic_fields = ['text', 'intent', 'description']
        present_fields = [field for field in basic_fields if field in data]
        logger.info(f"Basic MBCP fields present: {present_fields}")

        # Add responseType if not present but intent is present
        if 'intent' in data and 'responseType' not in data:
            intent = data['intent']
            # Check if the intent is valid
            if is_valid_intent(intent):
                # Get metadata for this intent type
                intent_metadata = get_intent_metadata(intent)

                # Set the responseType based on the intent metadata
                data['responseType'] = {
                    'type': intent,
                    'requiresMindmap': intent_metadata.get('requires_mindmap', False),
                    'requiresChatFork': intent_metadata.get('requires_chatfork', False),
                    'requiresTemplate': intent_metadata.get('requires_template', False)
                }
            else:
                # Fallback for unknown intent types
                data['responseType'] = {
                    'type': intent,
                    'requiresMindmap': intent == 'teleological',
                    'requiresChatFork': intent == 'exploratory',
                    'requiresTemplate': False
                }
            logger.info(f"Added responseType to match LLM intent: {intent}")

    # Always return True to allow any structure to pass through
    return True

def extract_json_from_content(content: str) -> Optional[Dict[str, Any]]:
    """
    Extract JSON objects from text content
    """
    try:
        # Look for JSON-like structure in the content
        start_idx = content.find('{')
        end_idx = content.rfind('}')

        if start_idx >= 0 and end_idx > start_idx:
            json_str = content[start_idx:end_idx+1]
            return json.loads(json_str)

        return None
    except json.JSONDecodeError:
        return None
    except Exception as e:
        logger.error(f"Error extracting JSON from content: {str(e)}")
        return None

# Removed generate_text_response function as it was a workaround