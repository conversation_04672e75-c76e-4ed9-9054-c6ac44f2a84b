"""
Template <PERSON><PERSON> Module
Handles template variable substitution in MBCP data.
"""
import logging
from typing import Dict, Any, Optional

# Get the existing logger instead of creating a new one
logger = logging.getLogger(__name__)

def replace_template_variables(data: Dict[str, Any], variables: Dict[str, str]) -> Dict[str, Any]:
    """
    Replace template variables in MBCP data.
    
    Args:
        data: The MBCP data to process
        variables: A dictionary of variable names and values
        
    Returns:
        The processed MBCP data with template variables replaced
    """
    if not isinstance(data, dict):
        return data
    
    # Create a copy of the data to avoid modifying the original
    result = {}
    
    # Process each key-value pair
    for key, value in data.items():
        if isinstance(value, str):
            # Replace template variables in strings
            result[key] = replace_variables_in_string(value, variables)
        elif isinstance(value, dict):
            # Recursively process nested dictionaries
            result[key] = replace_template_variables(value, variables)
        elif isinstance(value, list):
            # Process lists
            result[key] = [
                replace_template_variables(item, variables) if isinstance(item, dict)
                else replace_variables_in_string(item, variables) if isinstance(item, str)
                else item
                for item in value
            ]
        else:
            # Keep other values as is
            result[key] = value
    
    return result

def replace_variables_in_string(text: str, variables: Dict[str, str]) -> str:
    """
    Replace template variables in a string.
    
    Args:
        text: The string to process
        variables: A dictionary of variable names and values
        
    Returns:
        The processed string with template variables replaced
    """
    if not isinstance(text, str):
        return text
    
    result = text
    
    # Replace each variable
    for name, value in variables.items():
        placeholder = f"{{{name}}}"
        if placeholder in result:
            result = result.replace(placeholder, value)
    
    return result

def process_mindmap_data(mindmap_data: Dict[str, Any], variables: Dict[str, str]) -> Dict[str, Any]:
    """
    Process mindmap data, replacing template variables and ensuring proper structure.
    
    Args:
        mindmap_data: The mindmap data to process
        variables: A dictionary of variable names and values
        
    Returns:
        The processed mindmap data
    """
    logger.info("Processing mindmap data with variables")
    
    # Replace template variables
    processed_data = replace_template_variables(mindmap_data, variables)
    
    # Ensure the mindmap has the correct intent
    if 'intent' in processed_data:
        # Set the top-level intent to teleological for mindmaps
        processed_data['intent'] = 'teleological'
    
    # Ensure the mindmap structure has the correct intent
    if 'mindmap' in processed_data and 'root' in processed_data['mindmap']:
        root = processed_data['mindmap']['root']
        
        # Set the root node intent to teleological
        if 'metadata' in root:
            root['metadata']['intent'] = 'teleological'
        else:
            root['metadata'] = {'intent': 'teleological'}
    
    logger.info("Mindmap data processed successfully")
    return processed_data
